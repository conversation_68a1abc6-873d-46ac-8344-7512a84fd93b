package com.nsy.wms.elasticjob.stockout;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.domain.stockout.StockoutShipmentModel;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.response.stockout.GetSecondaryNumResponse;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutShipmentErpPickingBoxService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;

import cn.hutool.core.util.StrUtil;

@Component
public class SyncTransferLogisticJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(SyncTransferLogisticJob.class);

    @Autowired
    StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    StockoutShipmentService stockoutShipmentService;
    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    TmsCacheService tmsCacheService;
    @Autowired
    StockoutShipmentErpPickingBoxService stockoutShipmentErpPickingBoxService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("SyncTransferLogisticJob start");
        LoginInfoService.setName("SyncTransferLogisticJob");
        List<StockoutShipmentModel> stockoutShipmentModelList = new LinkedList<>();
        List<StockoutShipmentModel> edlShipmentModels = stockoutShipmentMapper.getShipmentListToEdlSync(LogisticsCompanyConstant.EDL_LOGISTICS_COMPANY, -30);
        if (!edlShipmentModels.isEmpty()) {
            LOGGER.info("EDL 装箱清单数 {}", edlShipmentModels.size());
            stockoutShipmentModelList.addAll(edlShipmentModels);
        }
        List<String> logisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList().stream()
                .filter(company -> LogisticsCompanyConstant.YUNTU_METHOD.equals(company.getLogisticsMethod())
                        && StrUtil.equalsAnyIgnoreCase(company.getLocation(), LocationEnum.QUANZHOU.getLocation(), LocationEnum.XIAMEN.getLocation()))
                .map(TmsLogisticsCompany::getLogisticsCompany).collect(Collectors.toList());
        List<StockoutShipmentModel> yuntuShipmentModels = stockoutShipmentMapper.getShipmentListToYUNTUSync(logisticsCompanyList, -30);
        if (!yuntuShipmentModels.isEmpty()) {
            LOGGER.info("YUNTU 装箱清单数 {}", yuntuShipmentModels.size());
            stockoutShipmentModelList.addAll(yuntuShipmentModels);
        }
        List<StockoutShipmentModel> zhongChengHangShipmentModels = stockoutShipmentMapper.getShipmentListToZHONGCHENGHANGSync(LogisticsCompanyConstant.ZHONGCHEGNHANG_LOGISTICS_COMPANY, -30);
        if (!zhongChengHangShipmentModels.isEmpty()) {
            LOGGER.info("ZHONGCHENGHANG 装箱清单数 {}", zhongChengHangShipmentModels.size());
            stockoutShipmentModelList.addAll(zhongChengHangShipmentModels);
        }

        for (StockoutShipmentModel shipmentModel : stockoutShipmentModelList) {
            processShipment(shipmentModel);
        }

        LOGGER.info("SyncTransferLogisticJob end");
        LoginInfoService.removeName();
    }

    /**
     * 处理装箱清单
     */
    private void processShipment(StockoutShipmentModel shipmentModel) {
        if (!StringUtils.hasText(shipmentModel.getLogisticsNo())) {
            LOGGER.info("装箱清单【 {} 】物流单号为空，跳过", shipmentModel.getShipmentBoxCode());
            return;

        }
        try {
            GetSecondaryNumResponse response = tmsApiService.getSecondaryNum(shipmentModel.getLogisticsNo());
            if (StringUtils.hasText(response.getSecondaryNumber()) && !response.getSecondaryNumber().equals(shipmentModel.getLogisticsNo())) {
                LOGGER.info("装箱清单【 {} 】将原物流单号： {} 更新为： {} ", shipmentModel.getShipmentBoxCode(), shipmentModel.getLogisticsNo(), response.getSecondaryNumber());
                StockoutShipmentEntity shipmentEntity = stockoutShipmentService.getById(shipmentModel.getShipmentId());
                shipmentEntity.setTransferLogisticsNo(shipmentEntity.getLogisticsNo());
                shipmentEntity.setLogisticsNo(response.getSecondaryNumber());
                shipmentEntity.setUpdateBy("SyncTransferLogisticJob");
                stockoutShipmentService.updateById(shipmentEntity);
                stockoutShipmentErpPickingBoxService.sendErpPickingBoxShippedSync(shipmentEntity);
            }
        } catch (Exception ex) {
            LOGGER.error("装箱清单【 {} 】处理失败： {}", shipmentModel.getShipmentBoxCode(), ex.getMessage());
        }
    }
}
