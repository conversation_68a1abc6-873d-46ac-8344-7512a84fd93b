package com.nsy.wms.business.service.stockout;

import com.nsy.api.core.apicore.response.CustomExcelResponse;
import com.nsy.api.core.apicore.util.NsyExcelUtil;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentItemExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.base.PageRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentItemRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StockoutCustomsDeclareDocumentItemDownService implements IDownloadService {

    @Autowired
    StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;


    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_CUSTOMS_DECLARE_DOCUMENT_ITEM;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockoutCustomsDeclareDocumentItemRequest idRequest = JsonMapper.fromJson(request.getRequestContent(), StockoutCustomsDeclareDocumentItemRequest.class);
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageIndex(request.getPageIndex());
        pageRequest.setPageSize(request.getPageSize());
        PageResponse<StockoutCustomsDeclareDocumentAggregatedItemResult> resultResponse = declareDocumentAggregatedItemService.getAggregatedItemResultPage(idRequest.getDeclareDocumentId(), pageRequest);
        CustomExcelResponse excelResponse = new CustomExcelResponse();
        excelResponse.setHeaders(NsyExcelUtil.getCommonHeads(StockoutCustomsDeclareDocumentItemExport.class));
        List<List<Object>> data = resultResponse.getContent().stream().map(content -> {
            StockoutCustomsDeclareDocumentItemExport export = new StockoutCustomsDeclareDocumentItemExport();
            BeanUtils.copyProperties(content, export);
            return NsyExcelUtil.getData(StockoutCustomsDeclareDocumentItemExport.class, export);
        }).collect(Collectors.toList());
        excelResponse.setData(data);
        response.setTotalCount(resultResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(excelResponse));
        return response;
    }
}
