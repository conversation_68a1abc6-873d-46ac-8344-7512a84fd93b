<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.bd.BdAqlRuleMapper">
    <select id="getRuleList" resultType="com.nsy.api.wms.response.bd.BdAqlRuleListResponse">
        SELECT
        t.aql_rule_id,
        t.space_id as spaceId,
        s.space_name as spaceName,
        t.range_from as rangeFrom,
        t.range_to as rangeTo,
        t.min_accept_qty as minAcceptQty,
        t.description as description,
        t.create_date as createDate,
        t.create_by as createBy,
        t.update_date as updateDate,
        t.update_by as updateBy
        FROM
        bd_aql_rule t
        left join bd_space s on t.space_id = s.space_id
        <where>
            t.is_deleted = 0
            <if test="request.spaceIdList != null and request.spaceIdList.size() > 0 ">
                and t.space_id in
                <foreach collection="request.spaceIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spaceId != null">
                AND t.space_id = #{request.spaceId}
            </if>
            <if test="request.rangeFrom != null">
                AND t.range_from = #{request.rangeFrom}
            </if>
            <if test="request.rangeTo != null">
                AND t.range_to = #{request.rangeTo}
            </if>
        </where>
        ORDER BY t.create_date DESC        

    </select>
</mapper> 