package com.nsy.wms.elasticjob.stockin;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.NoneDataException;
import com.nsy.api.wms.constants.ReturnProductConstant;
import com.nsy.api.wms.enumeration.PurchaseOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductStatusEnum;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.service.internal.scm.ReturnOrderService;
import com.nsy.wms.business.service.stockin.StockinReturnProductLogService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskItemService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductLog;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 从发货时间开始超过10天后，还没完成到货确认自动更新为退货完成
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Component
public class StockinReturnTaskAutoFinishedJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReturnTaskAutoFinishedJob.class);

    @Autowired
    private StockinReturnProductTaskService returnProductTaskService;
    @Autowired
    private StockinReturnProductTaskItemService returnProductTaskItemService;
    @Autowired
    private StockinReturnProductLogService returnProductLogService;
    @Autowired
    private GcApiService gcApiService;
    @Autowired
    private ReturnOrderService returnOrderService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockinReturnTaskAutoFinishedJob start");
        List<StockinReturnProductTaskEntity> taskEntityList = returnProductTaskService.list(new LambdaQueryWrapper<StockinReturnProductTaskEntity>()
            .lt(StockinReturnProductTaskEntity::getDeliveryDate, DateUtils.addDays(new Date(), -10))
            .eq(StockinReturnProductTaskEntity::getStatus, ReturnProductStatusEnum.IN_TRANSIT.name()));
        if (CollectionUtils.isEmpty(taskEntityList)) {
            throw new NoneDataException();
        }
        List<StockinReturnProductLog> logList = Lists.newArrayListWithExpectedSize(taskEntityList.size());
        taskEntityList.forEach(entity -> {
            entity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.name());
            entity.setUpdateBy(getJobName(jobDataMap));
            logList.add(returnProductLogService.getLog(entity.getReturnProductTaskId(), ReturnProductConstant.FINISH_TASK, "发货超10天，自动退货完成", getJobName(jobDataMap), ""));
        });
        returnProductTaskService.updateBatchById(taskEntityList);
        returnProductLogService.saveLogs(logList);
        List<Integer> returnProductTaskIdList = taskEntityList.stream().map(StockinReturnProductTaskEntity::getReturnProductTaskId).collect(Collectors.toList());
        List<StockinReturnProductTaskItemEntity> returnProductTaskItemEntityList = returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>()
            .in(StockinReturnProductTaskItemEntity::getReturnProductTaskId, returnProductTaskIdList)
            .eq(StockinReturnProductTaskItemEntity::getStatus, ReturnProductStatusEnum.IN_TRANSIT.name()));
        returnProductTaskItemEntityList.forEach(itemEntity -> {
            itemEntity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.name());
            itemEntity.setUpdateBy(getJobName(jobDataMap));
        });
        returnProductTaskItemService.updateBatchById(returnProductTaskItemEntityList);
        gcApiService.reworkConfirmReceipt(returnProductTaskItemEntityList.stream().map(StockinReturnProductTaskItemEntity::getId).collect(Collectors.toList()));
        returnOrderService.addPurchaseOrderLogToScm(returnProductTaskItemEntityList, PurchaseOrderLogTypeEnum.SUPPLIER_REWORK_RETURN_RECEIPT);
        LOGGER.info("StockinReturnTaskAutoFinishedJob end");
    }
}
