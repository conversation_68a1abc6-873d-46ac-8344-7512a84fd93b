package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutLogisticsBatchInternalCreateRequest;
import com.nsy.api.wms.request.stockout.StockoutLogisticsBatchPageRequest;
import com.nsy.api.wms.request.stockout.StockoutLogisticsBatchUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchPageResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchSumResponse;
import com.nsy.wms.business.service.stockout.StockoutLogisticsBatchService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * FBA出库批次(StockoutLogisticsBatch)接口
 *
 * <AUTHOR>
 * @since 2023-03-27 09:50:20
 */
@Api(tags = "FBA出库批次(StockoutLogisticsBatch)相关接口")
@RestController
public class StockoutLogisticsBatchController extends BaseController {

    @Resource
    private StockoutLogisticsBatchService stockoutLogisticsBatchService;

    /**
     * 分页查询
     */
    @ApiOperation("首页page列表")
    @PostMapping("/stockout-logistics-batch/page")
    public PageResponse<StockoutLogisticsBatchPageResponse> queryByPage(@RequestBody StockoutLogisticsBatchPageRequest pageRequest) {
        return this.stockoutLogisticsBatchService.queryByPage(pageRequest);
    }

    @ApiOperation("首页page列表-统计")
    @PostMapping("/stockout-logistics-batch/statistics")
    public StockoutLogisticsBatchSumResponse statistics(@RequestBody StockoutLogisticsBatchPageRequest pageRequest) {
        return this.stockoutLogisticsBatchService.statistics(pageRequest);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/stockout-logistics-batch/{id}")
    public StockoutLogisticsBatchResponse queryById(@PathVariable("id") Integer id) {
        return this.stockoutLogisticsBatchService.getOneById(id);
    }

    /**
     * 编辑数据
     */
    @ApiOperation("修改")
    @PostMapping("/stockout-logistics-batch/update")
    public void edit(@RequestBody @Valid StockoutLogisticsBatchUpdateRequest stockoutLogisticsBatch) {
        this.stockoutLogisticsBatchService.update(stockoutLogisticsBatch);
    }

    /**
     * 重新获取价格
     */
    @ApiOperation("重新获取价格")
    @PostMapping("/stockout-logistics-batch/fresh-price")
    public void freshPrice(@RequestBody @Valid IdListRequest idListRequest) {
        this.stockoutLogisticsBatchService.freshPrice(idListRequest.getIdList());
    }

    @ApiOperation("内部修复数据-生成物流批次费用")
    @RequestMapping(value = "/stockout-logistics-batch/internal/generate-batch", method = RequestMethod.POST)
    public void internalGenerateBatch(@RequestBody @Valid StockoutLogisticsBatchInternalCreateRequest request) {
        stockoutLogisticsBatchService.internalGenerateBatch(request);
    }

    // 推送财务
    @ApiOperation("推送财务")
    @PostMapping("/stockout-logistics-batch/push-finance")
    public String pushFinance(@RequestBody @Valid IdListRequest idListRequest) {
        return this.stockoutLogisticsBatchService.pushFinance(idListRequest.getIdList());
    }
}

