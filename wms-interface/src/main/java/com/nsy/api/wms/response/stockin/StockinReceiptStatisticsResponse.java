package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 入库收货列表统计响应
 */
@ApiModel(value = "StockinReceiptStatisticsResponse", description = "入库收货列表统计响应")
public class StockinReceiptStatisticsResponse {

    @ApiModelProperty(value = "发货总件数", name = "shipmentQtyTotal")
    private Integer shipmentQtyTotal;

    @ApiModelProperty(value = "收货总件数", name = "receiveQtyTotal")
    private Integer receiveQtyTotal;

    @ApiModelProperty(value = "需退货总件数", name = "waitReturnQtyTotal")
    private Integer waitReturnQtyTotal;

    @ApiModelProperty(value = "已退货总件数", name = "returnedQtyTotal")
    private Integer returnedQtyTotal;

    @ApiModelProperty(value = "上架总件数", name = "shelvedQtyTotal")
    private Integer shelvedQtyTotal;

    @ApiModelProperty(value = "多收总件数", name = "overchargeQtyTotal")
    private Integer overchargeQtyTotal;

    @ApiModelProperty(value = "少收总件数", name = "lessQtyTotal")
    private Integer lessQtyTotal;

    @ApiModelProperty(value = "结算总件数", name = "settlementQtyTotal")
    private Integer settlementQtyTotal;

    public Integer getShipmentQtyTotal() {
        return shipmentQtyTotal;
    }

    public void setShipmentQtyTotal(Integer shipmentQtyTotal) {
        this.shipmentQtyTotal = shipmentQtyTotal;
    }

    public Integer getReceiveQtyTotal() {
        return receiveQtyTotal;
    }

    public void setReceiveQtyTotal(Integer receiveQtyTotal) {
        this.receiveQtyTotal = receiveQtyTotal;
    }

    public Integer getWaitReturnQtyTotal() {
        return waitReturnQtyTotal;
    }

    public void setWaitReturnQtyTotal(Integer waitReturnQtyTotal) {
        this.waitReturnQtyTotal = waitReturnQtyTotal;
    }

    public Integer getShelvedQtyTotal() {
        return shelvedQtyTotal;
    }

    public void setShelvedQtyTotal(Integer shelvedQtyTotal) {
        this.shelvedQtyTotal = shelvedQtyTotal;
    }

    public Integer getOverchargeQtyTotal() {
        return overchargeQtyTotal;
    }

    public void setOverchargeQtyTotal(Integer overchargeQtyTotal) {
        this.overchargeQtyTotal = overchargeQtyTotal;
    }

    public Integer getLessQtyTotal() {
        return lessQtyTotal;
    }

    public void setLessQtyTotal(Integer lessQtyTotal) {
        this.lessQtyTotal = lessQtyTotal;
    }

    public Integer getSettlementQtyTotal() {
        return settlementQtyTotal;
    }

    public void setSettlementQtyTotal(Integer settlementQtyTotal) {
        this.settlementQtyTotal = settlementQtyTotal;
    }

    public Integer getReturnedQtyTotal() {
        return returnedQtyTotal;
    }

    public void setReturnedQtyTotal(Integer returnedQtyTotal) {
        this.returnedQtyTotal = returnedQtyTotal;
    }
}
