package com.nsy.api.wms.response.stockin;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "StockinPlatformScheduleListResponse", description = "月台调度列表")
public class StockinPlatformScheduleListResponse {

    @ApiModelProperty(value = "月台审核ID", name = "platformScheduleId")
    private Integer platformScheduleId;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;

    @ApiModelProperty(value = "工厂", name = "supplierName")
    private String supplierName;


    @ApiModelProperty(value = "仓库", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态描述", name = "statusStr")
    private String statusStr;

    @ApiModelProperty(value = "审核时间", name = "auditDate")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date auditDate;

    @ApiModelProperty(value = "审核人", name = "audit_by")
    private String auditBy;

    @ApiModelProperty(value = "总箱数", name = "boxNum")
    private Integer boxNum;

    @ApiModelProperty(value = "计划入库总件数", name = "expectedQty")
    private Integer expectedQty;

    @ApiModelProperty(value = "创建时间", name = "auditDate")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "审核原因", name = "auditDescription")
    private String auditDescription;

    @ApiModelProperty(value = "直发备注", name = "remarks")
    private String remarks;

    @ApiModelProperty(value = "品类", name = "categoryNames")
    private String categoryNames;

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getPlatformScheduleId() {
        return platformScheduleId;
    }

    public void setPlatformScheduleId(Integer platformScheduleId) {
        this.platformScheduleId = platformScheduleId;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getAuditBy() {
        return auditBy;
    }

    public void setAuditBy(String auditBy) {
        this.auditBy = auditBy;
    }

    public Integer getBoxNum() {
        return boxNum;
    }

    public void setBoxNum(Integer boxNum) {
        this.boxNum = boxNum;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public String getAuditDescription() {
        return auditDescription;
    }

    public void setAuditDescription(String auditDescription) {
        this.auditDescription = auditDescription;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCategoryNames() {
        return categoryNames;
    }

    public void setCategoryNames(String categoryNames) {
        this.categoryNames = categoryNames;
    }
}
