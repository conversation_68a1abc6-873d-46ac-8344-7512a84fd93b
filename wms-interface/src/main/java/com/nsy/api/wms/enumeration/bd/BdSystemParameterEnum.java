package com.nsy.api.wms.enumeration.bd;

/**
 * 数据字典名称
 */
public enum BdSystemParameterEnum {

    DOMESTIC_SMALL_BATCH_UPPER_LIMIT("domestic_small_batch_upper_limit", "内贸小波次数量上限"),
    DOMESTIC_BIG_BATCH_LOWER_LIMIT("domestic_big_batch_lower_limit", "内贸大波次数量下限"),
    B2B_SMALL_BATCH_UPPER_LIMIT("b2b_small_batch_upper_limit", "B2B小波次数量上限"),
    B2B_BIG_BATCH_LOWER_LIMIT("b2b_big_batch_lower_limit", "B2B大波次数量下限"),
    WMS_STOCKS_LIGHT_SEEDING_NUM("wms_stocks_light_seeding_num", "亮灯播种墙数"),
    WMS_STOCKS_ARTIFICIAL_SEEDING_NUM("wms_stocks_artificial_seeding_num", "人工播种墙数"),
    WMS_ALLOCATION_RULES_SKU_SET_LIMIT("allocation_rules_sku_set_limit", "分配规则SKU阈值"),
    WMS_WHOLE_PICK_STORE("wms_whole_pick_store", "按单拣货的店铺"),
    WMS_STORE_SPACE_MEMO("wms_store_space_memo", "店铺仓库备注"),
    WMS_STOCKOUT_SHIPMENT_INIT("wms_stockout_shipment_init", "装箱初始化出库类型"),
    WMS_STOCKOUT_DECLARE_FORM_INPUT_RATE("wms_stockout_declare_form_input_rate", "关单进项比例"),
    WMS_EXCHANGE_RATE("wms_exchange_rate", "当月汇率"),
    WMS_STOCKOUT_DECLARE_FORM_FETCH_RANGE("wms_stockout_declare_form_fetch_range", "关单获取时间长度"),
    WMS_SIZE_SORTED("wms_size_sorted", "商品排序(从小到大,英文逗号分隔)"),
    WMS_STOCKS_MACHINE_NUM("wms_stocks_machine_num", "窄带分拣口数"),
    WMS_ACTIVITY_SPACE_NOTICE_USER("wms_activity_space_notice_user", "活动仓到货钉钉提示用户"),
    WMS_PICKING_LACK_NOTICE_USER_JSON("wms_picking_lack_notice_user_json", "拣货任务缺货钉钉提示备单人员"),
    WMS_STOCK_PLATFORM_NOTICE_USER_JSON("wms_stock_platform_notice_user_json", "生成月台失败钉钉提示人员"),
    WMS_STOCKIN_NOTICE_USER("wms_stockin_notice_user", "入库上架任务钉钉提示用户"),

    WMS_ASYNC_FAIL_NOTICE_USER("wms_async_fail_notice_user", "异步调用失败提示用户"),
    WMS_ASYNC_BACK_TRADE_FAIL_NOTICE_USER("wms_async_back_trade_fail_notice_user", "异步调用回退订单失败提示用户"),
    WMS_STOCKIN_LAST_UPDATE_DATE("wms_stockin_last_update_date", "入库单上次更新时间"),
    WMS_RETURN_TASK_LAST_UPDATE_DATE("wms_return_task_last_update_date", "退货任务上次更新时间"),

    WMS_STOCKOUT_CHECK_NOTICE_USER("wms_stockout_check_notice_user", "出库核对异常钉钉提示人员"),

    WMS_UPS_EDS_EMAIL_RECEIVER("wms_ups_eds_email_receiver", "UpsEds邮件接收人"),
    WMS_FAIRE_SHIPMENT_STORE_LIST("wms_faire_shipment_store_list", "Faire装箱清单店铺列表"),
    WMS_FAIRE_SHIPMENT_LOGISTIC_COMPANY_LIST("wms_faire_shipment_logistic_company_list", "Faire装箱清单物流公司列表"),
    WMS_AIR_TRANSPORT("wms_air_transport", "空运运输方式"),
    WMS_SEA_TRANSPORT("wms_sea_transport", "海运运输方式"),
    TEMU_EXPRESS_COMPANY_URGENCY("temu_express_company_urgency", "拼多多快递 - 急单"),
    TEMU_EXPRESS_COMPANY_NORMAL_SMALL("temu_express_company_normal_small", "拼多多快递 - 普单 - 小包"),
    TEMU_EXPRESS_COMPANY_NORMAL_BIG("temu_express_company_normal_big", "拼多多快递 - 普单 - 大包"),
    TEMU_EXPRESS_COMPANY_BLACKLIST("temu_express_company_blacklist", "拼多多快递黑名单"),
    WMS_LA_SOCIETY_EMAIL_RECEIVER("WMS_LA_SOCIETY_EMAIL_RECEIVER", "LaSociety仓接收人"),
    WMS_CANCEL_ORDER_PROCESS_NOTICE_USER("wms_cancel_order_process_notice_user", "取消加工出库单通知业务"),
    WMS_INVENTORY_SYNC_MARK("wms_inventory_sync_mark", "wms盘点同步开关"),
    WMS_SHEIN_EXPRESS_ID("wms_shein_express_id", "shein快递公司"),
    WMS_CROSS_BELT_EXCEPTION_OUTLET("wms_cross_belt_exception_outlet", "交叉带异常口"),
    WMS_TEMU_AUTO_CREATE_SHIPORDER_INTERVAL("wms_temu_auto_create_shiporder_interval", "temu自动创建发货单间隔"),
    WMS_TEMU_AUTO_CREATE_SHIPORDER_SYNC_NUM("wms_temu_auto_create_shiporder_sync_num", "temu自动创建发货单 - 同步数量"),
    WMS_TEMU_AUTO_CREATE_SHIPORDER_MERGE_NUM("wms_temu_auto_create_shiporder_merge_num", "temu自动创建发货单 - 合并发货数量"),
    WMS_SHEIN_LABEL_SPLIT_PDF_KEY("wms_shein_label_split_pdf_key", "shein条码分割"),
    WMS_VALUE_ADD_PRICE_LIST("wms_value_add_price_list", "增值服务价目表"),
    WMS_TEMU_PREV_CREATE_SHIPORDER_QTY_LIMIT("wms_temu_prev_create_shiporder_qty_limit", "temu前置抢仓qty限制"),
    WMS_TEMU_AUTO_CREATE_SHIPORDER_SWITCH("wms_temu_auto_create_shiporder_switch", "temu自动抢仓开关"),
    WMS_STOCKOUT_WEIGHT_ALLOWABLE_RANGE("wms_stockout_weight_allowable_range", "小包称重允许误差范围"),
    WMS_STOCKOUT_WEIGHT_OF_PACKAGING_BAG("wms_stockout_weight_of_packaging_bag", "小包称重包装袋重量"),
    WMS_OTTO_PUSH_STORE_ID_LIST("wms_otto_push_store_id_list", "推送金蝶otto店铺id集合"),
    WMS_LA_SOCIETY_EMAIL_BAN_STORE("wms_la_society_email_ban_store", "LaSociety仓禁止发邮件店铺"),
    WMS_TEMU_APPOINTMENT_TIME("wms_temu_appointment_time", "temu预约时间"),
    WMS_TEMU_CREATE_SHIPORDER_NUM_LIMIT("wms_temu_create_shiporder_num_limit", "temu每日创建发货单限制"),
    WMS_TEMU_APPOINTMENT_CHANGE_TIME("wms_temu_appointment_change_time", "temu预约改变时间，该时间后则预约次日"),
    WMS_STORE_DELIVERY_COMPANY("wms_store_delivery_company", "店铺发货抬头公司"),
    // true or false
    WMS_STOCKIN_IGNORE_CHECK("wms_stockin_ignore_check", "入库单是否跳过核对和采购确认"),
    WMS_STOCKIN_IS_NEGATIVE_UP_SHELF("wms_stockin_is_negative_up_shelf", "工厂出库单是否允许明细编辑，上架数回退"),
    WMS_STOCKIN_DELIVERY_CONFIRM("wms_stockin_delivery_confirm", "入库单是否需要工厂核对"),

    WMS_STOCKIN_QA_CONCESSION_RECEIVE_DISCOUNT("wms_stockin_qa_concession_receive_discount", "入库质检单让步接收折扣"),

    // 通知缺货是否自动归还拣货箱库存  true or false  未配置默认为false
    WMS_STOCKOUT_NOTICE_LACK_CLEAR_BOX("wms_stockout_notice_lack_clear_box", "通知缺货是否自动归还拣货箱库存"),
    WMS_TIKTOK_LOGISTICS_BLACKLIST("wms_tiktok_logistics_blacklist", "tk全托管物流黑名单"),
    WMS_STORE_WORKSPACE("wms_store_workspace", "店铺工作区域"),
    WMS_OVERSEA_SPACE_CHECK("wms_oversea_space_check", "海外仓库存核对"),
    WMS_OVERSEA_SPACE_LIST("wms_oversea_space_list", "海外仓仓库选项"),
    WMS_TIKTOK_LOGISTICS_APPOINTMENT_TIME("wms_tiktok_logistics_appointment_time", "tk全托管快递预约时间"),
    WMS_TIKTOK_DESIGNATED_LOGISTICS("wms_tiktok_designated_logistics", "tk全托管指定的物流(默认20kg以下)"),
    WMS_TIKTOK_DESIGNATED_LARGE_WEIGHT_LOGISTICS("wms_tiktok_designated_large_weight_logistics", "tk全托管指定的物流(20kg以上)"),
    WMS_AMAZON_FIRST_ORDER_ADD_VALUE("wms_amazon_first_order_add_value", "亚马逊首单增值服务开关"),
    WMS_STOCKOUT_DECLARE_FORM_FETCH_TOTAL("wms_stockout_declare_form_fetch_total", "关单手动获取时间 - 总天数"),
    WMS_STOCKOUT_DECLARE_CI_ORIGIN_PLACE("wms_stockout_declare_ci_origin_place", "报关 - CI单证产地"),

    WMS_STOCKOUT_B2B_SELF_LOGISTICS("wms_stockout_b2b_self_logistics", "B2B自发货物流公司"),

    WMS_TRY_ON_STYLE_LIST("wms_try_on_style_list", "需要试穿的标签集合");


    BdSystemParameterEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    private final String key;
    private final String name;

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }
}
