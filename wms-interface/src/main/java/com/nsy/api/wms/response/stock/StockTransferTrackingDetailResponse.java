package com.nsy.api.wms.response.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询仓库在途明细响应类
 */
@ApiModel("StockTransferTrackingDetailResponse")
public class StockTransferTrackingDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("SKU编码")
    private String sku;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("店铺")
    private String storeName;

    @ApiModelProperty("发货数量")
    private Integer shipQty;

    @ApiModelProperty("目的仓库")
    private String targetSpaceName;

    @ApiModelProperty("物流公司")
    private String logisticsCompany;

    @ApiModelProperty("仓库发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date shipDate;

    @ApiModelProperty("运输方式")
    private String shippingType;

    @ApiModelProperty("仓库ID")
    private Integer spaceId;

    @ApiModelProperty("ERP仓库ID")
    private Integer erpSpaceId;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getShipQty() {
        return shipQty;
    }

    public void setShipQty(Integer shipQty) {
        this.shipQty = shipQty;
    }

    public String getTargetSpaceName() {
        return targetSpaceName;
    }

    public void setTargetSpaceName(String targetSpaceName) {
        this.targetSpaceName = targetSpaceName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Date getShipDate() {
        return shipDate;
    }

    public void setShipDate(Date shipDate) {
        this.shipDate = shipDate;
    }

    public String getShippingType() {
        return shippingType;
    }

    public void setShippingType(String shippingType) {
        this.shippingType = shippingType;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getErpSpaceId() {
        return erpSpaceId;
    }

    public void setErpSpaceId(Integer erpSpaceId) {
        this.erpSpaceId = erpSpaceId;
    }
}
