package com.nsy.wms.business.service.qa;

import com.google.common.collect.Lists;
import com.nsy.api.activiti.dto.request.AssignTaskRequest;
import com.nsy.api.activiti.dto.request.ClaimTaskRequest;
import com.nsy.api.activiti.dto.request.CompleteTaskRequest;
import com.nsy.api.activiti.dto.request.DeleteTaskRequest;
import com.nsy.api.activiti.dto.request.StartProcessRequest;
import com.nsy.api.activiti.dto.request.UpdateTaskAssigneeRequest;
import com.nsy.api.activiti.dto.response.TaskResponse;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.enumeration.ActivitiBusinessKeyPrefixEnum;
import com.nsy.api.wms.enumeration.ActivitiProcessDefinitionKeyEnum;
import com.nsy.api.wms.enumeration.FlowTaskDefinitionEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.qa.FlowTaskQaOperationEnum;
import com.nsy.api.wms.enumeration.qa.QaLogTypeEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.request.qa.StockinQaOrderAssignRequest;
import com.nsy.wms.business.manage.activiti.FlowOperateService;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.manage.user.response.SysUserInfoListResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/27 16:48
 */
@Service
public class StockinQaOrderAuditService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaOrderAuditService.class);

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private LoginInfoService loginInfoService;

    @Autowired
    private FlowOperateService flowOperateService;

    @Autowired
    private ProductSpecInfoService specInfoService;

    @Autowired
    private StockinQaOrderService qaOrderService;

    @Autowired
    private StockinQaOrderLogService logService;

    /**
     * 开启质检工作流
     * 如果能找到用户的上级则指派任务给对应的上级
     *
     * @param qaOrderId
     */
    public void startQaOrderWorkFlow(Integer qaOrderId, String userAccount, String content) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(qaOrderId);
        ProductSpecInfo specInfo = specInfoService.getBySku(qaOrderEntity.getSku());
        //开启工作流
        StartProcessRequest startProcessRequest = new StartProcessRequest();
        startProcessRequest.setBusinessKey(String.format("%s:%s", ActivitiBusinessKeyPrefixEnum.QAORDER.name(), qaOrderEntity.getStockinQaOrderId()));
        startProcessRequest.setSearchCode(qaOrderEntity.getInternalBoxCode());
        startProcessRequest.setDescription(String.format("内部箱【%s】,sku【%s】发起质检审核", qaOrderEntity.getInternalBoxCode(), qaOrderEntity.getSku()));
        startProcessRequest.setImageUrl(specInfo.getImageUrl());
        startProcessRequest.setCreateBy(loginInfoService.getName());
        startProcessRequest.setCreateName(loginInfoService.getName());

        startProcessRequest.setCompanyId(String.valueOf(LocationEnum.getCompanyIdByLocation(TenantContext.getTenant())));
        startProcessRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.QA_ORDER.getName());
        flowOperateService.startProcessInstanceByKey(startProcessRequest);
        logService.addLog(qaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.AUDIT, String.format("%s发起质检单审核%s", loginInfoService.getName(), content));
        //指派给对应的上级
        this.autoAssign(userAccount, startProcessRequest.getBusinessKey());
    }

    /**
     * 初审到复审
     *
     * @param qaOrderId
     */
    public void submitToAudit(Integer qaOrderId, boolean isAdmin, String userAccount) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(qaOrderId);
        TaskResponse workFlowInfo = this.getAndValidWorkFlowInfo(qaOrderEntity, isAdmin);
        // 完成工作流
        flowOperateService.completeTask(this.buildCompleteTaskRequest(workFlowInfo, FlowTaskQaOperationEnum.SUBMIT_TO_AUDIT));
        logService.addLog(qaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.AUDIT, String.format("%s发起质检单复核", loginInfoService.getName()));
        //指派给对应的上级
        this.autoAssign(userAccount, workFlowInfo.getBusinessKey());
    }

    /**
     * 指派给对应的阿上级
     *
     * @param userAccount
     * @param businessKey
     */
    public void autoAssign(String userAccount, String businessKey) {
        //获取当前用户的上级
        SysUserInfoListResponse superiorUserInfo = userApiService.getSuperiorByUserAccount(userAccount);
        if (Objects.isNull(superiorUserInfo) || CollectionUtils.isEmpty(superiorUserInfo.getContent())) {
            return;
        }
        try {
            //指派对对应上级
            TaskResponse taskResponse = flowOperateService.queryTaskByBusinessKey(businessKey);
            AssignTaskRequest request = new AssignTaskRequest();
            request.setTaskId(taskResponse.getTaskId());
            request.setAssignee(superiorUserInfo.getContent().get(0).getUserAccount());
            flowOperateService.assignTask(request);
        } catch (Exception e) {
            LOGGER.error("指派质检任务失败", e);
        }
    }

    /**
     * 初审到结束
     *
     * @param qaOrderId
     */
    public void submitToEnd(Integer qaOrderId, boolean isAdmin) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(qaOrderId);
        TaskResponse workFlowInfo = this.getAndValidWorkFlowInfo(qaOrderEntity, isAdmin);
        // 完成工作流
        flowOperateService.completeTask(this.buildCompleteTaskRequest(workFlowInfo, FlowTaskQaOperationEnum.SUBMIT_TO_END));
    }

    /**
     * 复审到结束
     *
     * @param qaOrderId
     */
    public void auditToEnd(Integer qaOrderId, boolean isAdmin) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(qaOrderId);
        TaskResponse workFlowInfo = this.getAndValidWorkFlowInfo(qaOrderEntity, isAdmin);
        // 完成工作流
        flowOperateService.completeTask(this.buildCompleteTaskRequest(workFlowInfo, FlowTaskQaOperationEnum.AUDIT_TO_END));
        logService.addLog(qaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.TWICE_AUDIT, String.format("%s完成质检单复核", loginInfoService.getName()));
    }

    /**
     * 复审回退到初审
     *
     * @param qaOrderId
     */
    public void auditToSubmit(Integer qaOrderId, boolean isAdmin) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(qaOrderId);
        TaskResponse workFlowInfo = this.getAndValidWorkFlowInfo(qaOrderEntity, isAdmin);
        // 完成工作流
        flowOperateService.completeTask(this.buildCompleteTaskRequest(workFlowInfo, FlowTaskQaOperationEnum.AUDIT_TO_SUBMIT));
        logService.addLog(qaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.AUDIT_REJECT, String.format("%s将质检单回退到初审", loginInfoService.getName()));
    }

    /**
     * 复审回退到初审
     *
     * @param qaOrderId
     */
    public void deleteTask(Integer qaOrderId) {
        StockinQaOrderEntity qaOrderEntity = qaOrderService.getById(qaOrderId);
        TaskResponse workFlowInfo = flowOperateService.queryTaskByBusinessKey(String.format("%s:%s", ActivitiBusinessKeyPrefixEnum.QAORDER.name(), qaOrderEntity.getStockinQaOrderId()));
        if (Objects.isNull(workFlowInfo)) {
            throw new BusinessServiceException("查询不到当前的工作流信息!");
        }
        DeleteTaskRequest deleteTaskRequest = new DeleteTaskRequest();
        deleteTaskRequest.setBusinessKey(workFlowInfo.getBusinessKey());
        deleteTaskRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.QA_ORDER.getName());
        deleteTaskRequest.setTaskName(workFlowInfo.getTaskName());
        deleteTaskRequest.setOperateBy(loginInfoService.getUserName());
        // 完成工作流
        flowOperateService.deleteTask(deleteTaskRequest);
    }

    /**
     * 获取并校验工作流信息
     *
     * @param qaOrderEntity
     * @return
     */
    public TaskResponse getAndValidWorkFlowInfo(StockinQaOrderEntity qaOrderEntity, boolean isAdmin) {
        TaskResponse taskResponse = flowOperateService.queryTaskByBusinessKey(String.format("%s:%s", ActivitiBusinessKeyPrefixEnum.QAORDER.name(), qaOrderEntity.getStockinQaOrderId()));
        if (Objects.isNull(taskResponse)) {
            throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】对应质检单查询不到当前的工作流信息!", qaOrderEntity.getInternalBoxCode(), qaOrderEntity.getSku()));
        }
        //非管理员或或者未领取
        if (!isAdmin && !StringUtils.hasText(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】对应质检单未领取,请领取后处理!", qaOrderEntity.getInternalBoxCode(), qaOrderEntity.getSku()));
        }
        if (!isAdmin && !loginInfoService.getName().equalsIgnoreCase(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】对应质检单属于【%s】无法处理!", qaOrderEntity.getInternalBoxCode(), qaOrderEntity.getSku(), taskResponse.getAssigneeName()));
        }
        return taskResponse;
    }

    /**
     * 工作流转派
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignTask(StockinQaOrderAssignRequest request) {
        List<StockinQaOrderEntity> orderEntityList = qaOrderService.listByIds(request.getStockinQaOrderIdList());
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("查询不到质检单信息!");
        }
        List<String> businessKeyList = new ArrayList<>();
        UpdateTaskAssigneeRequest assignTaskRequest = new UpdateTaskAssigneeRequest();
        orderEntityList.forEach(detail -> {
            if (!Lists.newArrayList(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name(), StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name()).contains(detail.getProcessStatus())) {
                throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】不在流程中，无法指派!", detail.getInternalBoxCode(), detail.getSku()));
            }
            TaskResponse taskInfo = this.getAndValidWorkFlowInfo(detail, loginInfoService.isAdmin() || request.getIgnoreNotice());
            businessKeyList.add(taskInfo.getBusinessKey());
            if (StringUtils.hasText(taskInfo.getAssigneeName())) {
                logService.addLog(detail.getStockinQaOrderId(), QaLogTypeEnum.ASSIGN, String.format("负责人由%s转派给%s", taskInfo.getAssigneeName(), request.getAssignName()));
            } else {
                logService.addLog(detail.getStockinQaOrderId(), QaLogTypeEnum.ASSIGN, String.format("负责人转派给%s", request.getAssignName()));
            }
            if (!StringUtils.hasText(assignTaskRequest.getTaskName())) {
                assignTaskRequest.setTaskName(taskInfo.getTaskName());
            }
            if (!StringUtils.hasText(assignTaskRequest.getTaskDefinitionKey())) {
                assignTaskRequest.setTaskDefinitionKey(taskInfo.getTaskDefinitionKey());
            }
            detail.setAppointor(request.getAssignName());
        });
        //更新指派人
        qaOrderService.updateBatchById(orderEntityList);
        assignTaskRequest.setBusinessKey(businessKeyList);
        assignTaskRequest.setAssignee(request.getAssignAccount());
        //批量转派
        flowOperateService.updateTaskAssign(assignTaskRequest);
    }

    @Transactional(rollbackFor = Exception.class)
    public void claimTask(StockinQaOrderAssignRequest request) {
        List<StockinQaOrderEntity> orderEntityList = qaOrderService.listByIds(request.getStockinQaOrderIdList());
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("查询不到质检单信息!");
        }
        List<SysUserInfo> userInfoList = new ArrayList<>();
        if (StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name().equalsIgnoreCase(orderEntityList.get(0).getProcessStatus())) {
            userInfoList = this.getQaAuditCandidateUser();
        } else if (StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name().equalsIgnoreCase(orderEntityList.get(0).getProcessStatus())) {
            userInfoList = this.getQaTwiceAuditCandidateUser();
        }
        if (!CollectionUtils.isEmpty(userInfoList) && !userInfoList.stream().filter(detail -> loginInfoService.getName().equalsIgnoreCase(detail.getUserName())).findAny().isPresent()) {
            throw new BusinessServiceException("当前角色无领取权限!");
        }
        List<String> businessKeyList = new ArrayList<>();
        ClaimTaskRequest claimTaskRequest = new ClaimTaskRequest();
        orderEntityList.forEach(detail -> {
            if (!Lists.newArrayList(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name(), StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name()).contains(detail.getProcessStatus())) {
                throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】不在流程中，无法领取!", detail.getInternalBoxCode(), detail.getSku()));
            }
            TaskResponse taskResponse = flowOperateService.queryTaskByBusinessKey(String.format("%s:%s", ActivitiBusinessKeyPrefixEnum.QAORDER.name(), detail.getStockinQaOrderId()));
            if (Objects.isNull(taskResponse)) {
                throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】对应质检单查询不到当前的工作流信息!", detail.getInternalBoxCode(), detail.getSku()));
            }
            if (StringUtils.hasText(taskResponse.getAssigneeName()) && !loginInfoService.getName().equalsIgnoreCase(taskResponse.getAssigneeName())) {
                throw new BusinessServiceException(String.format("内部箱号【%s】,sku【%s】对应质检单属于【%s】无法处理!", detail.getInternalBoxCode(), detail.getSku(), taskResponse.getAssigneeName()));
            }
            businessKeyList.add(taskResponse.getBusinessKey());
            logService.addLog(detail.getStockinQaOrderId(), QaLogTypeEnum.CLAIM, String.format("%s领取任务", loginInfoService.getName()));
            if (!StringUtils.hasText(claimTaskRequest.getTaskName())) {
                claimTaskRequest.setTaskName(taskResponse.getTaskName());
            }
            if (!StringUtils.hasText(claimTaskRequest.getTaskDefinitionKey())) {
                claimTaskRequest.setTaskDefinitionKey(taskResponse.getTaskDefinitionKey());
            }
            detail.setAppointor(loginInfoService.getName());
        });
        //更新指派人
        qaOrderService.updateBatchById(orderEntityList);
        claimTaskRequest.setBusinessKey(businessKeyList);
        claimTaskRequest.setClaimUser(loginInfoService.getUserName());
        //批量领取
        flowOperateService.claimTask(claimTaskRequest);
    }

    /**
     * 组装完成工作流参数
     *
     * @param workFlowInfo
     * @param operationEnum
     * @param assignee
     * @return
     */
    private CompleteTaskRequest buildCompleteTaskRequest(TaskResponse workFlowInfo, FlowTaskQaOperationEnum operationEnum) {
        CompleteTaskRequest completeTaskRequest = new CompleteTaskRequest();
        completeTaskRequest.setVariables(operationEnum.getVariables());
        completeTaskRequest.setBusinessKey(workFlowInfo.getBusinessKey());
        completeTaskRequest.setTaskId(workFlowInfo.getTaskId());
        completeTaskRequest.setTaskName(operationEnum.getTaskName());
        completeTaskRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.QA_ORDER.getName());
        completeTaskRequest.setLocalVariable(true);
        completeTaskRequest.setOperateBy(loginInfoService.getName());
        completeTaskRequest.setComment(String.format("%s进行%s", loginInfoService.getName(), operationEnum.getTaskName()));
        return completeTaskRequest;
    }

    /**
     * 获取初审任务候选人
     *
     * @return
     */
    public List<SysUserInfo> getQaAuditCandidateUser() {
        return flowOperateService.getConfigCandidateUser(FlowTaskDefinitionEnum.QA_AUDIT.getTaskDefinitionKey(), LocationEnum.getCompanyIdByLocation(TenantContext.getTenant()));
    }

    /**
     * 获取复审任务候选人
     *
     * @return
     */
    public List<SysUserInfo> getQaTwiceAuditCandidateUser() {
        return flowOperateService.getConfigCandidateUser(FlowTaskDefinitionEnum.QA_TWICE_AUDIT.getTaskDefinitionKey(), LocationEnum.getCompanyIdByLocation(TenantContext.getTenant()));
    }
}
