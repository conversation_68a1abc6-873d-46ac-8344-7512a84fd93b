package com.nsy.wms.business.service.esign;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.wms.business.domain.bo.esign.ESignCallbackResponse;
import com.nsy.wms.business.domain.bo.esign.ESignCheckFileStatusResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFileUploadResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFileUploadUrlRequest;
import com.nsy.wms.business.domain.bo.esign.ESignFileUploadUrlResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowCreateByFileResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowInitiateRescissionResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowKeywordPosResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowSignUrlResponse;
import com.nsy.wms.business.domain.bo.esign.ESignGetFilesResponse;
import com.nsy.wms.business.domain.bo.esign.ESignGetSignFlowDetailResponse;
import com.nsy.wms.business.domain.bo.esign.ESignGetSignedFilesResponse;
import com.nsy.wms.business.domain.bo.esign.ESignGetSigningFilesResponse;
import com.nsy.wms.business.domain.bo.esign.ESignOrganizationsIdentityInfoResponse;
import com.nsy.wms.business.domain.bo.esign.ESignPersonsIdentityInfoResponse;
import com.nsy.wms.business.domain.bo.esign.ESignResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractCancelService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractService;
import com.nsy.wms.config.ESignProperties;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.nsy.api.wms.domain.esign.ESignSignFlowListRequest;
import com.nsy.api.wms.domain.esign.ESignSignFlowListResponse;

/**
 * e签宝
 */
@Service
public class ESignService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ESignService.class);

    @Resource
    ESignProperties eSignProperties;
    @Resource
    RestTemplate restTemplate;
    @Resource
    StockoutCustomsDeclareContractService contractService;
    @Resource
    ExternalApiLogService externalApiLogService;
    @Resource
    StockoutCustomsDeclareContractCancelService contractCancelService;

    /**
     * 获取文件上传地址
     */
    public ESignFileUploadUrlResponse getFileUploadUrl(String fileName, String contentMd5, String contentType, Long fileSize) {
        String uri = "/v3/files/file-upload-url";
        String url = eSignProperties.getUrl() + uri;

        try {
            ESignFileUploadUrlRequest requestBody = new ESignFileUploadUrlRequest();
            requestBody.setContentMd5(contentMd5);
            requestBody.setContentType(contentType);
            requestBody.setFileName(fileName);
            requestBody.setFileSize(fileSize);
            requestBody.setConvertToPDF(Boolean.TRUE);
            String bodyJson = JsonMapper.toJson(requestBody);

            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, bodyJson);
            HttpEntity<String> httpEntity = new HttpEntity<>(bodyJson, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            return validAndParse(response, ESignFileUploadUrlResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】获取文件上传地址失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 查询文件上传状态
     */
    public ESignCheckFileStatusResponse checkFileStatus(String fileId) {
        String uri = "/v3/files/" + fileId;
        String url = eSignProperties.getUrl() + uri;

        try {

            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignCheckFileStatusResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】查询文件上传状态：%s", e.getMessage()), e);
        }
    }


    /**
     * 文件上传
     */
    public void uploadFile(ESignFileUploadUrlResponse uploadUrlResponse, InputStream is, String contentMd5, String contentType) {
        CloseableHttpResponse response;

        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpRequestBase request = new HttpPut(uploadUrlResponse.getFileUploadUrl());
            ((HttpEntityEnclosingRequest) request).setEntity(new ByteArrayEntity(readInputstream(is)));
            request.setHeader("Content-MD5", contentMd5);
            request.setHeader("Content-Type", contentType);
            response = httpclient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            if (200 != statusCode) {
                throw new BusinessServiceException("请求上传文件失败");
            }
            //获取请求响应对象和响应entity
            org.apache.http.HttpEntity httpEntity = response.getEntity();
            if (Objects.isNull(httpEntity)) {
                throw new BusinessServiceException("返回数据为空");
            }
            String json = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            LOGGER.info("上传文件返回 {} ", json);
            ESignFileUploadResponse fileUploadResponse = JsonMapper.fromJson(json, ESignFileUploadResponse.class);
            if (0 != fileUploadResponse.getErrCode()) {
                throw new BusinessServiceException(fileUploadResponse.getMsg());
            }
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】文件上传失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 开启签署流程
     */
    public void startSignFlow(String signFlowId) {
        String uri = StrUtil.format("/v3/sign-flow/{}/start", signFlowId);
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            validAndParse(response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】开启签署流程：%s", e.getMessage()), e);
        }
    }


    /**
     * 查询签署流程详情
     */
    public ESignGetSignFlowDetailResponse getSignFlowDetail(String signFlowId) {
        String uri = StrUtil.format("/v3/sign-flow/{}/detail", signFlowId);
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignGetSignFlowDetailResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】查询签署流程详情败：%s", e.getMessage()), e);
        }
    }

    /**
     * 合同初始化
     */
    public ESignFlowCreateByFileResponse initContract(String body) {
        String uri = "/v3/sign-flow/create-by-file";
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, body);
            HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
            LOGGER.info("合同初始化 {} ", body);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            return validAndParse(response, ESignFlowCreateByFileResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】合同初始化失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 检索文件关键字坐标
     */
    public Map<String, ESignFlowKeywordPosResponse> getKeywordPos(String fileId, String keyword) {
        String uri = String.format("/v3/files/%s/keyword-positions?keywords=%s", fileId, keyword);
        String url = eSignProperties.getUrl() + uri;

        String[] keywordArr = keyword.split(",");

        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>("", headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            ESignResponse eSignResponse = valid(response);
            LOGGER.info("检索文件关键字坐标 {} ", JsonMapper.toJson(eSignResponse));
            JSONObject jobj = JSONUtil.parseObj(JsonMapper.toJson(eSignResponse.getData()));
            if (!jobj.containsKey("keywordPositions")) throw new BusinessServiceException("keywordPositions 不存在");
            JSONArray keywordPositions = jobj.getJSONArray("keywordPositions");
            if (keywordPositions.size() != keywordArr.length)
                throw new BusinessServiceException("keywordPositions 关键词数量不一致");
            Map<String, ESignFlowKeywordPosResponse> resultMap = new HashMap<>(keywordArr.length);
            keywordPositions.forEach(obj -> {
                JSONObject positionInfoJobj = (JSONObject) obj;
                String kw = positionInfoJobj.getStr("keyword");
                if (!positionInfoJobj.getBool("searchResult"))
                    throw new BusinessServiceException(String.format("未找到关键词 【%s】", kw));
                ESignFlowKeywordPosResponse result = new ESignFlowKeywordPosResponse();
                JSONArray positions = positionInfoJobj.getJSONArray("positions");
                if (positions.size() < 1) throw new BusinessServiceException("positions 为空");
                JSONObject position = (JSONObject) positions.get(0);
                result.setPageNum(position.getInt("pageNum"));
                JSONArray coordinates = position.getJSONArray("coordinates");
                if (coordinates.size() < 1) throw new BusinessServiceException("coordinates 为空");
                JSONObject coordinate = (JSONObject) coordinates.get(0);
                result.setPositionX(coordinate.getBigDecimal("positionX"));
                result.setPositionY(coordinate.getBigDecimal("positionY"));
                resultMap.put(kw, result);
            });

            return resultMap;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】检索文件关键字坐标失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 获取合同路径
     */
    public ESignFlowSignUrlResponse getContractUrl(ContractUrlTypeEnum contractUrlType, String orgName, String phone, String signFlowId) {
        String uri = String.format("/v3/sign-flow/%s/sign-url", signFlowId);
        String url = eSignProperties.getUrl() + uri;

        String body = String.format("{\"needLogin\":true,\"operator\":{\"psnAccount\":\"%s\"},\"organization\":{\"orgName\":\"%s\"},\"urlType\":%s}", phone, orgName, contractUrlType.getValue());

        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, body);
            HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            return validAndParse(response, ESignFlowSignUrlResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取合同下载地址
     */
    public ESignGetFilesResponse getFiles(String fileId) {
        String uri = StrUtil.format("/v3/files/{}", fileId);
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignGetFilesResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】获取文件下载地址失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 获取签署中合同
     */
    public ESignGetSigningFilesResponse getSigningFiles(String signFlowId, String fileId) {
        String uri = StrUtil.format("/v3/sign-flow/{}/preview-file-download-url?docFileId={}", signFlowId, fileId);
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignGetSigningFilesResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】获取签署中文件地址失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 获取合同下载地址
     */
    public ESignGetSignedFilesResponse getSignedFiles(String signFlowId) {
        String uri = StrUtil.format("/v3/sign-flow/{}/file-download-url", signFlowId);
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignGetSignedFilesResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】获取已签署文件地址失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 撤销合同
     */
    public void revoke(String signFlowId) {
        ESignGetSignFlowDetailResponse signFlowDetail = getSignFlowDetail(signFlowId);
        //草稿中要先开启签署
        if (signFlowDetail.getSignFlowStatus() == 0) {
            startSignFlow(signFlowId);
        }

        String uri = String.format("/v3/sign-flow/%s/revoke", signFlowId);
        String url = eSignProperties.getUrl() + uri;

        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>("", headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            valid(response);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】合同撤销失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 发起合同解约
     */
    public ESignFlowInitiateRescissionResponse initiateRescission(String signFlowId, String body) {
        String uri = String.format("/v3/sign-flow/%s/initiate-rescission", signFlowId);
        String url = eSignProperties.getUrl() + uri;
        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, body);
            HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            return validAndParse(response, ESignFlowInitiateRescissionResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】发起合同解约失败：%s", e.getMessage()), e);
        }
    }

    private <T> T validAndParse(ResponseEntity<String> response, Class<T> clazz) {
        ESignResponse eSignResponse = valid(response);
        if (Objects.isNull(eSignResponse.getData())) {
            throw new BusinessServiceException("返回数据为空");
        }
        return JsonMapper.fromJson(JsonMapper.toJson(eSignResponse.getData()), clazz);
    }

    private void validAndParse(ResponseEntity<String> response) {
        valid(response);
    }

    private ESignResponse valid(ResponseEntity<String> response) {
        if (!StringUtils.hasText(response.getBody())) {
            throw new BusinessServiceException("返回数据为空");
        }
        ESignResponse eSignResponse = JsonMapper.fromJson(response.getBody(), ESignResponse.class);
        if (0 != eSignResponse.getCode()) {
            throw new BusinessServiceException(eSignResponse.getMessage());
        }
        return eSignResponse;
    }

    /**
     * 易签宝回调
     *
     * @param body
     * @return
     */
    @Transactional
    public ESignCallbackResponse callback(String body) {
        TenantContext.setTenant("QUANZHOU");
        JSONObject jObj = JSONUtil.parseObj(body);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.NOTIFY_ESIGN_CALLBACK, "esign/callback",
                body, jObj.getStr("signFlowId"), "e签宝回调");
        LOGGER.info(body);
        LoginInfoService.setName("e签宝");
        try {
            String action = jObj.getStr("action");
            if ("SIGN_MISSON_COMPLETE".equals(action)) {
                contractService.signFinish(jObj);
            } else if ("SIGN_FILE_RESCINDED".equals(action)) {
                contractCancelService.rescinded(jObj);
            } else if ("SIGN_FLOW_COMPLETE".equals(action)) {
                contractService.flowComplete(jObj);
            }
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(ESignCallbackResponse.ok()), ExternalApiLogStatusEnum.SUCCESS);
            return ESignCallbackResponse.ok();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            return ESignCallbackResponse.fail();
        } finally {
            LoginInfoService.removeName();
        }
    }


    /**
     * 查询机构认证信息
     */
    public ESignOrganizationsIdentityInfoResponse organizationsIdentityInfo(String orgName) {

        String uri = String.format("/v3/organizations/identity-info?orgName=%s", orgName);
        String url = eSignProperties.getUrl() + uri;

        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>("", headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignOrganizationsIdentityInfoResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】合同撤销失败：%s", e.getMessage()), e);
        }
    }


    /**
     * 查询个人认证信息
     */
    public ESignPersonsIdentityInfoResponse personsIdentityInfo(String psnAccount) {

        String uri = String.format("/v3/persons/identity-info?psnAccount=%s", psnAccount);
        String url = eSignProperties.getUrl() + uri;

        try {
            HttpHeaders headers = buildHttpHeaders(HttpMethod.GET, uri, "");
            HttpEntity<String> httpEntity = new HttpEntity<>("", headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            return validAndParse(response, ESignPersonsIdentityInfoResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】合同撤销失败：%s", e.getMessage()), e);
        }
    }

    /**
     * 读取inputstream
     *
     * @param is
     * @return
     * @throws IOException
     */
    private byte[] readInputstream(InputStream is) throws IOException {
        byte[] buffer = new byte[1024];
        int length;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while (true) {
            length = is.read(buffer, 0, 1024);
            if (length == -1) break;
            bos.write(buffer, 0, length);
        }
        bos.close();
        return bos.toByteArray();
    }

    /***
     * 计算请求Body体的Content-MD5
     * @param bodyData 请求Body体数据
     * @return
     */
    private String getBodyContentMD5(String bodyData) throws NoSuchAlgorithmException {
        // 获取Body体的MD5的二进制数组（128位）
        byte[] bytes = getBodyMD5Bytes128(bodyData);
        // 对Body体MD5的二进制数组进行Base64编码
        return new String(Base64.encodeBase64(bytes), Charset.defaultCharset());
    }

    /***
     * 获取MD5-二进制数组（128位）
     *
     * @param bodyData 请求Body体数据
     * @return
     */
    private byte[] getBodyMD5Bytes128(String bodyData) throws NoSuchAlgorithmException {
        byte[] md5Bytes;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(bodyData.getBytes(StandardCharsets.UTF_8));
        md5Bytes = md5.digest();
        return md5Bytes;
    }

    private HttpHeaders buildHttpHeaders(HttpMethod method, String pathAndParameters, String body) throws Exception {
        String accept = "*/*";
        String contentMD5 = StringUtils.hasText(body) ? getBodyContentMD5(body) : "";
        String contentType = "application/json; charset=UTF-8";

        HttpHeaders headers = new HttpHeaders();
        headers.add("X-Tsign-Open-App-Id", eSignProperties.getAppId());
        headers.add("X-Tsign-Open-Auth-Mode", "Signature");
        headers.add("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        headers.add(HttpHeaders.ACCEPT, accept);
        headers.add(HttpHeaders.CONTENT_TYPE, contentType);
        headers.add("Content-MD5", contentMD5);
        headers.add("X-Tsign-Open-Ca-Signature", getSignature(method, accept, contentMD5, contentType, pathAndParameters));
        return headers;
    }

    /**
     * 获取签名值
     *
     * @return
     */
    private String getSignature(HttpMethod method, String accept, String contentMD5, String contentType, String pathAndParameters) throws NoSuchAlgorithmException, InvalidKeyException {
        String httpMethod = method.name();
        String date = "";
//        String headers = "";
        // 组合拼接待签名字符串
        String strBuff = httpMethod + "\n" + accept + "\n" + contentMD5 + "\n"
                + contentType + "\n" + date + "\n" + pathAndParameters;
//        strBuff.append(headers).append(pathAndParameters);
        return doSignatureBase64(strBuff, eSignProperties.getAppSecret());
    }

    /**
     * 计算文件的Content-MD5
     *
     * @param is
     * @return
     * @throws NoSuchAlgorithmException
     * @throws IOException
     */
    public String getFileContentMD5(InputStream is) throws NoSuchAlgorithmException, IOException {
        // 获取文件MD5的二进制数组（128位）
        byte[] bytes = getFileMD5Bytes128(is);
        // 对文件MD5的二进制数组进行base64编码
        return new String(Base64.encodeBase64(bytes), Charset.defaultCharset());
    }

    /***
     * 获取文件MD5-二进制数组（128位）
     *
     * @return
     * @throws IOException
     */
    private byte[] getFileMD5Bytes128(InputStream is) throws NoSuchAlgorithmException, IOException {
        byte[] md5Bytes;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] buffer = new byte[1024];
        int length;
        while (true) {
            length = is.read(buffer, 0, 1024);
            if (length == -1) break;
            md5.update(buffer, 0, length);
        }
        md5Bytes = md5.digest();
        return md5Bytes;
    }

    /***
     * 计算请求签名值
     *
     * @param message 待签名字符串
     * @param secret  密钥APP KEY
     * @return HmacSHA256计算后摘要值的Base64编码
     * @throws Exception 加密过程中的异常信息
     */
    private String doSignatureBase64(String message, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        String algorithm = "HmacSHA256";
        Mac hmacSha256;
        hmacSha256 = Mac.getInstance(algorithm);
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));
        // 使用HmacSHA256对二进制数据消息Bytes计算摘要
        byte[] digestBytes = hmacSha256.doFinal(messageBytes);
        // 把摘要后的结果digestBytes使用Base64进行编码
        return new String(Base64.encodeBase64(digestBytes), StandardCharsets.UTF_8);
    }

    /**
     * 获取回调链接
     *
     * @return
     */
    public String getNotifyUrl() {
        String notifyUrl = eSignProperties.getNotifyUrl();
        if (!StringUtils.hasText(notifyUrl)) {
            throw new BusinessServiceException("未配置通知回调");
        }
        return notifyUrl;
    }

    /**
     * 合同地址类型
     */
    public enum ContractUrlTypeEnum {
        PREVIEW(1, "预览链接（仅限查看，不能签署）"),
        SIGN(2, "签署链接");

        ContractUrlTypeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private String name;

        private Integer value;

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

    }

    /**
     * 查询集成方企业流程列表
     */
    public ESignSignFlowListResponse getSignFlowList(ESignSignFlowListRequest request) {
        String uri = "/v3/organizations/sign-flow-list";
        String url = eSignProperties.getUrl() + uri;
        try {
            String bodyJson = JsonMapper.toJson(request);
            HttpHeaders headers = buildHttpHeaders(HttpMethod.POST, uri, bodyJson);
            HttpEntity<String> httpEntity = new HttpEntity<>(bodyJson, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            return validAndParse(response, ESignSignFlowListResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(String.format("【esign】查询签署流程列表失败：%s", e.getMessage()), e);
        }
    }

}
