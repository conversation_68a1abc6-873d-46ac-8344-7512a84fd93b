package com.nsy.api.wms.domain.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutPickingTaskConfirmSku", description = "拣货任务SKU信息实体")
public class StockoutPickingTaskConfirmSku {

    @ApiModelProperty(value = "主键id", name = "taskItemId")
    private String taskItemId;

    @ApiModelProperty(value = "原图地址", name = "imageUrl")
    private String imageUrl;

    @ApiModelProperty(value = "缩略图", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "条形码", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "拣货库区", name = "spaceAreaName")
    private String spaceAreaName;

    @ApiModelProperty(value = "拣货库位", name = "positionCode")
    private String positionCode;

    @ApiModelProperty(value = "待拣件数", name = "expectedQty")
    private Integer expectedQty;

    @ApiModelProperty(value = "扫描数", name = "pickedQty")
    private Integer pickedQty;

    @ApiModelProperty(value = "缺货数", name = "lackQty")
    private Integer lackQty;

    @ApiModelProperty(value = "装箱明细", name = "stockoutPickingTaskItemDetailList")
    private List<StockoutOrderTaskItemDetail> stockoutPickingTaskItemDetailList;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "出库单备注", name = "description")
    private String description;

    @ApiModelProperty(value = "拣货库区排序", name = "spaceAreaName")
    private Integer spaceAreaSort;

    @ApiModelProperty(value = "包装方式", name = "packageVacuum")
    private String packageVacuum;

    /**
     * 库区id
     */
    private Integer spaceAreaId;

    @ApiModelProperty(value = "真空cn", name = "vacuumCn")
    private String vacuumCn;

    @ApiModelProperty(value = "真空", name = "vacuum")
    private String vacuum;

    @ApiModelProperty(value = "是否店铺首单", name = "isFirstOrderByStore")
    private Integer isFirstOrderByStore;

    @ApiModelProperty(value = "商品标签", name = "productTag")
    private List<String> productTag;

    /**
     * 是否引流款
     */
    @ApiModelProperty(value = "是否引流款", name = "isLeadGeneration") 
    private Boolean isLeadGeneration;

    public Boolean getIsLeadGeneration() {
        return isLeadGeneration;
    }

    public void setIsLeadGeneration(Boolean isLeadGeneration) {
        this.isLeadGeneration = isLeadGeneration;
    }

    public List<String> getProductTag() {
        return productTag;
    }

    public void setProductTag(List<String> productTag) {
        this.productTag = productTag;
    }

    public Integer getIsFirstOrderByStore() {
        return isFirstOrderByStore;
    }

    public void setIsFirstOrderByStore(Integer isFirstOrderByStore) {
        this.isFirstOrderByStore = isFirstOrderByStore;
    }

    public String getVacuumCn() {
        return vacuumCn;
    }

    public void setVacuumCn(String vacuumCn) {
        this.vacuumCn = vacuumCn;
    }

    public String getVacuum() {
        return vacuum;
    }

    public void setVacuum(String vacuum) {
        this.vacuum = vacuum;
    }

    public Integer getSpaceAreaId() {
        return spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public Integer getSpaceAreaSort() {
        return spaceAreaSort;
    }

    public void setSpaceAreaSort(Integer spaceAreaSort) {
        this.spaceAreaSort = spaceAreaSort;
    }

    public String getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(String taskItemId) {
        this.taskItemId = taskItemId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Integer getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(Integer pickedQty) {
        this.pickedQty = pickedQty;
    }

    public Integer getLackQty() {
        return lackQty;
    }

    public void setLackQty(Integer lackQty) {
        this.lackQty = lackQty;
    }

    public List<StockoutOrderTaskItemDetail> getStockoutPickingTaskItemDetailList() {
        return stockoutPickingTaskItemDetailList;
    }

    public void setStockoutPickingTaskItemDetailList(List<StockoutOrderTaskItemDetail> stockoutPickingTaskItemDetailList) {
        this.stockoutPickingTaskItemDetailList = stockoutPickingTaskItemDetailList;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPackageVacuum() {
        return packageVacuum;
    }

    public void setPackageVacuum(String packageVacuum) {
        this.packageVacuum = packageVacuum;
    }
}
