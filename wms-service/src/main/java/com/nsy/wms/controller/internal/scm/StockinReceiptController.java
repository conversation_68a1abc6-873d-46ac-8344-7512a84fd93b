package com.nsy.wms.controller.internal.scm;

import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.QtyRecordRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptItemListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptRecordListRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.QueryReceiptAndDeliveryRecordResponse;
import com.nsy.api.wms.response.stockin.QueryShelvedRecordResponse;
import com.nsy.api.wms.response.stockin.StockReceiptItemListResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptListResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptRecordListResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptStatisticsResponse;
import com.nsy.wms.business.service.internal.scm.StockinReceiptService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "提供给scm,入库收货列表相关接口")
public class StockinReceiptController extends BaseController {
    @Autowired
    private StockinReceiptService stockinReceiptService;
    @Autowired
    private StockinShelveTaskItemService shelveTaskItemService;

    @ApiOperation(value = "收货记录列表-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-order/receipt-record-list", method = RequestMethod.POST)
    public PageResponse<StockinReceiptRecordListResponse> getReceiptRecordList(@RequestBody @Valid StockinReceiptRecordListRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        return stockinReceiptService.getReceiptRecordList(request);
    }

    @ApiOperation(value = "入库收货列表-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-order/receipt-list", method = RequestMethod.POST)
    public PageResponse<StockinReceiptListResponse> getReceiptList(@RequestBody @Valid StockinReceiptListRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockinReceiptService.getReceiptList(request);
    }

    @ApiOperation(value = "入库收货列表统计-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-order/receipt-list/statistics", method = RequestMethod.POST)
    public StockinReceiptStatisticsResponse getReceiptListStatistics(@RequestBody @Valid StockinReceiptListRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockinReceiptService.getReceiptListStatistics(request);
    }

    @ApiOperation(value = "入库收货列表核对按钮-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-order/receipt-list/check", method = RequestMethod.PUT)
    public void check(@Valid @RequestBody IdListRequest request) {
        stockinReceiptService.check(request.getIdList());
    }

    @ApiOperation(value = "入库收货列表差异确认按钮", produces = "application/json")
    @RequestMapping(value = "/stockin-order/receipt-list/difference-confirm", method = RequestMethod.PUT)
    public void differenceConfirm(@Valid @RequestBody StringListRequest request) {
        stockinReceiptService.differenceConfirm(request);
    }

    @ApiOperation(value = "入库收货日志类型-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-order-log/type-select", method = RequestMethod.GET)
    public List<SelectModel> getStocinOrderLogTypeSelect() {
        return stockinReceiptService.getStocinOrderLogTypeSelect();
    }

    @ApiOperation(value = "入库收货明细列表-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-order/receipt-item-list", method = RequestMethod.POST)
    public StockReceiptItemListResponse getReceiptItemList(@RequestBody @Valid StockinReceiptItemListRequest request) {
        return stockinReceiptService.getReceiptItemListResponse(request);
    }

    @ApiOperation(value = "已上架记录-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/shelved-record", method = RequestMethod.POST)
    public PageResponse<QueryShelvedRecordResponse> shelvedRecord(@Valid @RequestBody QtyRecordRequest request) {
        return shelveTaskItemService.shelvedRecord(request);
    }

    @ApiOperation(value = "收发货记录-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/receipt-delivery-record", method = RequestMethod.POST)
    public PageResponse<QueryReceiptAndDeliveryRecordResponse> receiptAndDeliveryRecord(@Valid @RequestBody QtyRecordRequest request) {
        return stockinReceiptService.receiptAndDeliveryRecord(request);
    }
}
