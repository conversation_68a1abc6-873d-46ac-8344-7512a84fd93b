package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.ReturnProductConstant;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItem;
import com.nsy.api.wms.domain.stockin.StockinReturnProductItemModel;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskMessage;
import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.PurchaseOrderTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformSchedulePurchaseModelEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleStockinStatusEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.request.stock.StockPlatformScheduleSupplierAddRequest;
import com.nsy.api.wms.request.stock.StockReturnShelveRequest;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.SyncPlatformScheduleToErpRequest;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.supplier.request.CreateSupplierDeliveryDto;
import com.nsy.wms.business.manage.supplier.request.CreateSupplierDeliveryRequest;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleCreateService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleLogService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductLog;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 退货库位商品拿回上架Service
 *
 * <AUTHOR>
 * @since 2022-07-14
 */
@Service
public class StockinReturnShelveService {
    @Autowired
    private GcApiService gcApiService;
    @Autowired
    private StockPlatformScheduleService stockPlatformScheduleService;
    @Autowired
    private StockPlatformScheduleCreateService stockPlatformScheduleCreateService;
    @Autowired
    private StockPlatformScheduleLogService platformSchedulerLogService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    private StockinScanLogService stockinScanLogService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    private StockinOrderLogService stockinOrderLogService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private StockinReturnProductTaskService returnProductTaskService;
    @Autowired
    private StockinReturnProductTaskItemService returnProductTaskItemService;
    @Autowired
    private StockinReturnProductLogService returnProductLogService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    UserApiService userApiService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;

    /**
     * 退货上架补充单据
     * step 1: 生成退货任务
     * step 2: 生成供应商系统的工厂出库单
     * step 3: 生成月台、入库单、入库任务、绑定内部箱
     * step 4: 同步erp生成工厂出库单、接收单主表
     */
    @Transactional
    public void returnShelveSupplementaryDocuments(String internalBoxCode, List<StockReturnShelveRequest> returnShelveRequestList) {
        returnShelveRequestList.stream().collect(Collectors.groupingBy(StockReturnShelveRequest::getSupplierId)).forEach((supplierId, itemList) -> {
            // step 1: 生成退货任务
            createReturnTask(itemList);
            // step 2: 生成供应商系统的工厂出库单
            StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier platformScheduleSupplier = createVirtualSupplierDelivery(supplierId, itemList);
            // step 3:生成月台、入库单、入库任务、绑定内部箱
            SyncPlatformScheduleToErpRequest erpRequest = saveStockPlatformSchedule(internalBoxCode, platformScheduleSupplier, itemList.get(0));
            try {
                // step 4:同步erp生成工厂出库单、接收单
                erpApiService.returnShelveAddDocuments(erpRequest);
            } catch (Exception e) {
                gcApiService.deleteVirtualSupplierDelivery(platformScheduleSupplier.getSupplierDeliveryNo());
                throw new BusinessServiceException(e.getMessage(), e);
            }
        });
    }

    /**
     * 生成供应商系统的工厂出库单
     *
     * @param supplierId
     * @param returnShelveRequestList
     * @return
     */
    private StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier createVirtualSupplierDelivery(Integer supplierId, List<StockReturnShelveRequest> returnShelveRequestList) {
        List<CreateSupplierDeliveryDto> deliveryDtoList = Lists.newArrayList();
        returnShelveRequestList.forEach(m -> {
            StockinReturnProductItemModel returnProductItemModel = m.getReturnProductItemModel();
            CreateSupplierDeliveryDto skuItem = deliveryDtoList.stream().filter(o -> o.getSku().equals(returnProductItemModel.getSku())
                && Objects.equals(o.getOrderNo(), returnProductItemModel.getPurchasePlanNo())).findFirst().orElse(null);
            if (skuItem != null) {
                skuItem.setQty(skuItem.getQty() + m.getReturnShelveQty());
            } else {
                CreateSupplierDeliveryDto deliveryDto = new CreateSupplierDeliveryDto();
                deliveryDto.setQty(m.getReturnShelveQty());
                deliveryDto.setSku(returnProductItemModel.getSku());
                deliveryDto.setOrderNo(returnProductItemModel.getPurchasePlanNo());
                deliveryDto.setOriginalSupplierDeliveryBoxNo(m.getSupplierDeliveryBoxCode());
                StockinOrderTaskItem stockinOrderTaskItem = m.getStockinOrderTaskItem();
                if (Objects.nonNull(stockinOrderTaskItem)) {
                    deliveryDto.setBrandName(stockinOrderTaskItem.getBrandName());
                    deliveryDto.setStoreId(stockinOrderTaskItem.getStoreId());
                    deliveryDto.setStoreName(stockinOrderTaskItem.getStoreName());
                }
                deliveryDtoList.add(deliveryDto);
            }
        });
        CreateSupplierDeliveryRequest createSupplierDeliveryRequest = new CreateSupplierDeliveryRequest();
        createSupplierDeliveryRequest.setSupplierId(supplierId);
        createSupplierDeliveryRequest.setCreateSupplierDeliveryDtoList(deliveryDtoList);
        createSupplierDeliveryRequest.setOperator(loginInfoService.getName());
        return gcApiService.createVirtualSupplierDelivery(createSupplierDeliveryRequest);
    }

    private void createReturnTask(List<StockReturnShelveRequest> returnShelveRequestList) {
        // 保存主任务
        StockinReturnProductTaskEntity returnProductTaskEntity = buildRefundReturnEntity(returnShelveRequestList);
        returnProductTaskService.save(returnProductTaskEntity);
        // 保存任务明细
        returnProductTaskEntity.getItemList().forEach(it -> it.setReturnProductTaskId(returnProductTaskEntity.getReturnProductTaskId()));
        returnProductTaskItemService.saveBatch(returnProductTaskEntity.getItemList());
        // 保存任务日志
        StockinReturnProductLog log = returnProductLogService.getLog(returnProductTaskEntity.getReturnProductTaskId(), ReturnProductConstant.CREATE_TASK, String.format("生成退货任务%s", returnProductTaskEntity.getReturnProductTaskId()), loginInfoService.getName(), loginInfoService.getIpAddress());
        returnProductLogService.saveLog(log);
        returnProductTaskService.generateReturnOrderToErp(returnProductTaskEntity);
    }

    private StockinReturnProductTaskEntity buildRefundReturnEntity(List<StockReturnShelveRequest> returnShelveRequestList) {
        StockinReturnProductItemModel stockinReturnProductItemModel = returnShelveRequestList.get(BigDecimal.ZERO.intValue()).getReturnProductItemModel();
        BdPositionEntity positionEntity = bdPositionService.getPositionByCode(stockinReturnProductItemModel.getPositionCode());
        StockinReturnProductTaskEntity entity = new StockinReturnProductTaskEntity();
        BeanUtils.copyProperties(stockinReturnProductItemModel, entity);
        entity.setCreateBy(loginInfoService.getName());
        entity.setLocation(TenantContext.getTenant());
        entity.setStatus(ReturnProductStatusEnum.RETURN_SUCCESS.getCode());
        entity.setReturnNature(StockinReturnNatureEnum.VIRTUAL_REWORK.name());
        entity.setPositionCode(positionEntity.getPositionCode());
        Date date = new Date();
        entity.setOperateStartDate(date);
        entity.setOperateStartDate(date);
        entity.setPositionId(positionEntity.getPositionId());
        entity.setSpaceAreaId(positionEntity.getSpaceAreaId());
        entity.setSpaceAreaName(positionEntity.getSpaceAreaName());
        // 设置任务明细
        List<StockinReturnProductTaskItemEntity> taskItemList = Lists.newArrayList();
        returnShelveRequestList.forEach(m -> {
            StockinReturnProductItemModel returnProductItemModel = m.getReturnProductItemModel();
            StockinReturnProductTaskItemEntity skuItem = taskItemList.stream().filter(o -> o.getSku().equals(returnProductItemModel.getSku())
                && Objects.equals(o.getSupplierDeliveryNo(), returnProductItemModel.getSupplierDeliveryNo()) && Objects.equals(o.getPurchasePlanNo(), returnProductItemModel.getPurchasePlanNo())).findFirst().orElse(null);
            if (skuItem != null) {
                skuItem.setWaitReturnQty(skuItem.getWaitReturnQty() + m.getReturnShelveQty());
                skuItem.setActualReturnQty(skuItem.getActualReturnQty() + m.getReturnShelveQty());
            } else {
                StockinReturnProductTaskItemEntity itemEntity = new StockinReturnProductTaskItemEntity();
                BeanUtils.copyProperties(entity, itemEntity);
                BeanUtils.copyProperties(returnProductItemModel, itemEntity);
                itemEntity.setPositionCode(positionEntity.getPositionCode());
                itemEntity.setPositionId(positionEntity.getPositionId());
                itemEntity.setWaitReturnQty(m.getReturnShelveQty());
                itemEntity.setActualReturnQty(m.getReturnShelveQty());
                taskItemList.add(itemEntity);
            }
        });
        entity.setItemList(taskItemList);
        if (StringUtils.hasText(entity.getPurchaserUserName())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserAccount(entity.getPurchaserUserName());
            entity.setPurchaseUserId(Objects.nonNull(userInfo) ? userInfo.getUserId() : null);
        }
        return entity;
    }

    /**
     * 生成已入库的月台
     *
     * @param stockPlatformScheduleSupplier
     */
    private SyncPlatformScheduleToErpRequest saveStockPlatformSchedule(String internalBoxCode, StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier stockPlatformScheduleSupplier,
                                                                       StockReturnShelveRequest stockReturnShelveRequest) {
        // 主表信息保存
        StockPlatformScheduleEntity platformScheduleEntity = new StockPlatformScheduleEntity();
        BeanUtils.copyProperties(stockPlatformScheduleSupplier, platformScheduleEntity);
        platformScheduleEntity.setStatus(StockPlatformScheduleStatusEnum.AUDITED.name());
        platformScheduleEntity.setStockinStatus(StockPlatformScheduleStockinStatusEnum.INBOUNDED.name());
        platformScheduleEntity.setPurchaseModel(StockPlatformSchedulePurchaseModelEnum.BULK.name());
        platformScheduleEntity.setPlatformScheduleType(stockPlatformScheduleSupplier.getStockinType().name());
        platformScheduleEntity.setAuditBy(stockReturnShelveRequest.getAuditBy());
        platformScheduleEntity.setAuditDate(stockReturnShelveRequest.getAuditTime());
        platformScheduleEntity.setLocation(TenantContext.getTenant());
        platformScheduleEntity.setOrderType(PurchaseOrderTypeEnum.BRANCH_COMPANY_APPLY.getIntValue().equals(stockPlatformScheduleSupplier.getOrderType()) ? 1 : 0);
        if (PurchaseOrderTypeEnum.RETURN_APPLY.getIntValue().equals(stockPlatformScheduleSupplier.getOrderType()) && !TenantContext.getTenant().equals(stockPlatformScheduleSupplier.getSupplierLocation())) {
            platformScheduleEntity.setOrderType(1);
        }
        SysUserInfo userInfo = userApiService.getUserInfoByUserAccount(platformScheduleEntity.getPurchaseUserName());
        platformScheduleEntity.setPurchaseUserId(Objects.nonNull(userInfo) ? userInfo.getUserId() : null);
        stockPlatformScheduleService.save(platformScheduleEntity);
        // 新增明细
        List<StockPlatformScheduleItemEntity> platformScheduleItemEntityList = stockPlatformScheduleCreateService.savePlatformScheduleItem(platformScheduleEntity.getPlatformScheduleId(), stockPlatformScheduleSupplier);
        platformSchedulerLogService.addLog(platformScheduleEntity.getPlatformScheduleId(), StockPlatformScheduleLogTypeEnum.GENERATE.getLogType(), "退货上架，生成已入库的月台");
        this.saveStockinOrderTask(internalBoxCode, platformScheduleEntity, platformScheduleItemEntityList);
        return stockPlatformScheduleService.buildSyncPlatformScheduleToErpRequest(platformScheduleEntity, platformScheduleItemEntityList);
    }

    /**
     * 生成收货完成的入库任务
     *
     * @param platformScheduleEntity
     * @param platformScheduleItemEntityList
     */
    private void saveStockinOrderTask(String internalBoxCode, StockPlatformScheduleEntity platformScheduleEntity, List<StockPlatformScheduleItemEntity> platformScheduleItemEntityList) {
        Map<String, List<StockPlatformScheduleItemEntity>> map = platformScheduleItemEntityList.stream().collect(Collectors.groupingBy(StockPlatformScheduleItemEntity::getSupplierDeliveryBoxCode));
        map.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(entry -> {
            List<StockPlatformScheduleItemEntity> platformScheduleItemList = entry.getValue();
            StockinOrderTaskEntity orderTaskEntity = new StockinOrderTaskEntity();
            orderTaskEntity.setSupplierDeliveryNo(platformScheduleEntity.getSupplierDeliveryNo());
            orderTaskEntity.setLocation(platformScheduleEntity.getLocation());
            orderTaskEntity.setStockinType(platformScheduleEntity.getPlatformScheduleType());
            orderTaskEntity.setPlatformScheduleId(platformScheduleEntity.getPlatformScheduleId());
            orderTaskEntity.setPurchaseUserId(platformScheduleEntity.getPurchaseUserId());
            orderTaskEntity.setExpectedQty(platformScheduleItemList.stream().mapToInt(StockPlatformScheduleItemEntity::getQty).sum());
            orderTaskEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVED.name());
            orderTaskEntity.setBoxIndex(platformScheduleItemList.get(0).getBoxIndex());
            orderTaskEntity.setSupplierDeliveryBoxCode(entry.getKey());
            orderTaskEntity.setSupplierDeliveryBarcode(platformScheduleItemList.get(0).getSupplierDeliveryBarcode());
            orderTaskEntity.setCreateBy(loginInfoService.getName());
            orderTaskEntity.setOperateStartDate(new Date());
            orderTaskEntity.setOperateEndDate(new Date());
            stockinOrderTaskService.save(orderTaskEntity);

            stockinScanLogService.addScanLog("退货上架，生成已收货完成的入库任务", orderTaskEntity, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_CREATE);
            List<StockinOrderTaskItemEntity> orderTaskItemEntityList = platformScheduleItemList.stream().map(entity -> {
                StockinOrderTaskItemEntity orderTaskItemEntity = new StockinOrderTaskItemEntity();
                BeanUtilsEx.copyProperties(entity, orderTaskItemEntity, "id");
                orderTaskItemEntity.setTaskId(orderTaskEntity.getTaskId());
                orderTaskItemEntity.setStoreId(entity.getStoreId());
                orderTaskItemEntity.setBusinessType(entity.getBusinessType());
                orderTaskItemEntity.setSpaceId(entity.getSpaceId());
                orderTaskItemEntity.setExpectedQty(entity.getQty());
                orderTaskItemEntity.setIsNeedQa(0);
                orderTaskItemEntity.setQaQty(0);
                orderTaskItemEntity.setStockinQty(entity.getQty());
                stockinOrderTaskItemService.recommendShelveSpaceArea(orderTaskEntity, entity.getSpaceId(), orderTaskItemEntity);
                orderTaskItemEntity.setCreateBy(loginInfoService.getName());
                return orderTaskItemEntity;
            }).collect(Collectors.toList());
            stockinOrderTaskItemService.saveBatch(orderTaskItemEntityList);
            this.saveStockinOrder(internalBoxCode, platformScheduleEntity, orderTaskEntity, orderTaskItemEntityList);
        });
    }

    /**
     * 生成已完成的入库单
     *
     * @param platformScheduleEntity
     * @param orderTaskEntity
     * @param orderTaskItemEntityList
     */
    private void saveStockinOrder(String internalBoxCode, StockPlatformScheduleEntity platformScheduleEntity, StockinOrderTaskEntity orderTaskEntity, List<StockinOrderTaskItemEntity> orderTaskItemEntityList) {
        StockinOrderEntity stockinOrderEntity = new StockinOrderEntity();
        stockinOrderEntity.setCreateBy(loginInfoService.getName());
        stockinOrderEntity.setStockinType(orderTaskEntity.getStockinType());
        stockinOrderEntity.setStatus(StockinOrderStatusEnum.RECEIVING.name());
        stockinOrderEntity.setStockinOrderNo(FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKIN_ORDER_NO_BFRK));
        stockinOrderEntity.setSupplierDeliveryBoxCode(orderTaskEntity.getSupplierDeliveryBoxCode());
        stockinOrderEntity.setSupplierDeliveryBarcode(orderTaskEntity.getSupplierDeliveryBarcode());
        stockinOrderEntity.setTaskId(orderTaskEntity.getTaskId());
        stockinOrderEntity.setPurchaseUserId(orderTaskEntity.getPurchaseUserId());
        stockinOrderEntity.setSupplierId(platformScheduleEntity.getSupplierId());
        stockinOrderEntity.setSupplierName(platformScheduleEntity.getSupplierName());
        stockinOrderEntity.setOrderType(platformScheduleEntity.getOrderType());
        stockinOrderService.save(stockinOrderEntity);
        stcokinOrderTimeService.addTime(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.CREATE_STOCKIN_ORDER);
        stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.CREATE_STOCKIN_ORDER.getName(), "退货上架,生成收货中的入库单");
        List<StockinOrderItemEntity> orderItemEntityList = orderTaskItemEntityList.stream().map(taskItemEntity -> {
            StockinOrderItemEntity entity = new StockinOrderItemEntity();
            BeanUtilsEx.copyProperties(taskItemEntity, entity);
            entity.setCreateBy(loginInfoService.getName());
            entity.setInternalBoxCode(internalBoxCode);
            entity.setQty(taskItemEntity.getExpectedQty());
            entity.setStockinOrderId(stockinOrderEntity.getStockinOrderId());
            entity.setPushedQty(0);
            entity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
            return entity;
        }).collect(Collectors.toList());
        stockinOrderItemService.saveBatch(orderItemEntityList);
        // 绑定内部箱
        stockInternalBoxService.skuToBox(orderItemEntityList, stockinOrderEntity.getStockinOrderNo());
        //发送kafka消息
        String businessMark = KafkaConstant.BUSINESS_MARK_WMS_STOCK_IN_SHELVE;
        StockinShelveTaskMessage message = new StockinShelveTaskMessage();
        message.setInternalBoxCode(internalBoxCode);
        message.setStockinOrderId(stockinOrderEntity.getStockinOrderId());
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
            .eq(StockInternalBoxItemEntity::getStockInOrderNo, stockinOrderEntity.getStockinOrderNo()));
        message.setInternalBoxItemIdList(boxItemEntityList.stream().map(StockInternalBoxItemEntity::getInternalBoxItemId).collect(Collectors.toList()));
        message.setLocation(TenantContext.getTenant());
        messageProducer.sendMessage(businessMark, KafkaConstant.TOPIC_NAME_STOCKIN_SHELVE_TASK, message);
    }

}
