package com.nsy.wms.business.service.stockin;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.wms.domain.bd.BdPosition;
import com.nsy.api.wms.domain.stockin.ShelveTaskItemInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskExport;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.StockinShelveEventEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.external.ExternalOrderCheckModuleEnum;
import com.nsy.api.wms.enumeration.external.ExternalSystemTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferInternalBoxLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferInternalBoxTaskStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskGenerateRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskListRequest;
import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.ShelveInternalBoxCodeInfo;
import com.nsy.api.wms.response.stockin.ShelveTaskListResponse;
import com.nsy.api.wms.response.stockin.StockinShelveTaskCountResponse;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdStockFifoService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.BdTagService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.external.ExternalOrderCheckQueueService;
import com.nsy.wms.business.service.internal.scm.StockinReceiptService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stock.StockTransferCrossSpaceService;
import com.nsy.wms.business.service.stock.StockTransferInternalBoxLogService;
import com.nsy.wms.business.service.stock.StockTransferInternalBoxTaskService;
import com.nsy.wms.business.service.stockin.query.StockinShelveTaskItemQueryWrapper;
import com.nsy.wms.elasticjob.stockin.StockinAutoCompleteShelveTaskJob;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stock.StockTransferInternalBoxTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskMapper;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinShelveTaskService extends ServiceImpl<StockinShelveTaskMapper, StockinShelveTaskEntity> implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinShelveTaskService.class);
    @Inject
    StockinShelveTaskMapper stockinShelveTaskMapper;
    @Inject
    StockinShelveTaskItemMapper shelveTaskItemMapper;
    @Inject
    StockInternalBoxItemMapper internalBoxItemMapper;
    @Autowired
    StockinOrderTaskService taskService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinShelveLogService stockinShelveLogService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService internalBoxItemService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    StockTransferInternalBoxTaskService transferInternalBoxTaskService;
    @Autowired
    StockTransferInternalBoxLogService transferInternalBoxLogService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    StockPlatformScheduleService platformScheduleService;
    @Autowired
    StockPlatformScheduleItemService platformScheduleItemService;
    @Autowired
    ExternalOrderCheckQueueService checkQueueService;
    @Autowired
    StockinShelveTaskCreateService shelveTaskCreateService;
    @Autowired
    StockTransferCrossSpaceService stockTransferCrossSpaceService;
    @Autowired
    StockinShelveTaskListService stockinShelveTaskListService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;
    @Autowired
    StockinShelveTaskOpService stockinShelveTaskOpService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    StockinReceiptService stockinReceiptService;
    @Autowired
    BdStockFifoService bdStockFifoService;
    @Autowired
    BdTagService bdTagService;

    /**
     * 内部箱调整更新上架任务信息
     * 1.修改调出箱对应上架任务及明细
     * 调出箱为空(全部调出)，删除箱号对应上架任务及明细
     * 调出箱不为空(部分调出),存在上架任务则更新上架任务明细（删除已调出的sku及数量更新），不存在上架任务则新增
     * 2.修改调入箱
     * 存在上架任务则更新上架任务明细（根据规格id，采购计划单号比对是否存在）
     * 不存在上架任务则新增
     */
    @Transactional
    public void updateShelveTaskByInternalBoxCode(String internalBoxCodeOut, String internalBoxCodeIn, String sku) {
        // 修改调出箱对应上架任务及明细
        stockinShelveTaskOpService.editInternalBoxOutItem(internalBoxCodeOut, sku);
        // 修改调入箱对应上架任务及明细
        stockinShelveTaskOpService.editInternalBoxInItem(internalBoxCodeIn, sku);
    }


    /**
     * 根据上架分拣任务生成上架任务信息
     */
    @Transactional
    public void createShelveTaskBySplitShelveTask(String internalBoxCode, List<StockinShelveSplitTaskItemEntity> shelveSplitTaskItemEntityList) {
        List<String> statusList = new ArrayList<>();
        statusList.add(StockinShelveTaskStatusEnum.PENDING.name());
        StockinShelveTaskEntity stockinShelveTaskEntity = this.getOne(buildWrapperTaskByInternalBoxCode(internalBoxCode, statusList));
        // 根据内部箱号找待上架的上架任务, 找到则更新，找不到上架任务,新增上架任务
        if (Objects.isNull(stockinShelveTaskEntity)) {
            stockinShelveTaskEntity = new StockinShelveTaskEntity();
            StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getStockInternalBoxByInternalBoxCode(internalBoxCode);
            BeanUtilsEx.copyProperties(stockInternalBoxEntity, stockinShelveTaskEntity, "createDate", "updateDate", "updateBy");
            stockinShelveTaskEntity.setUpdateBy(loginInfoService.getName());
            stockinShelveTaskEntity.setTaskType(StockinShelveTaskTypeEnum.TRANSFER_SHELVE.name());
            stockinShelveTaskEntity.setStatus(StockinShelveTaskStatusEnum.PENDING.name());
            this.save(stockinShelveTaskEntity);
            String content = String.format("内部箱号:%s 装箱完成，生成上架任务", internalBoxCode);
            stockinShelveLogService.addShelveLog(StockinShelveEventEnum.CREATE_SHELVE_TASK, stockinShelveTaskEntity.getShelveTaskId(), content);
        } else {
            String content = String.format("内部箱号:%s 重新装箱，重新生成上架任务", internalBoxCode);
            stockinShelveLogService.addShelveLog(StockinShelveEventEnum.SHELVE_TASK_CHANGE, stockinShelveTaskEntity.getShelveTaskId(), content);
        }
        Integer shelveTaskId = stockinShelveTaskEntity.getShelveTaskId();
        // 新增或更新上架明细
        shelveSplitTaskItemEntityList.forEach(item -> {
            LambdaQueryWrapper<StockinShelveTaskItemEntity> queryWrapper = StockinShelveTaskItemQueryWrapper.buildWrapperByTaskIdAndSourceIdAndSpecId(shelveTaskId, item.getTransferTaskId(), item.getSpecId());
            StockinShelveTaskItemEntity stockinShelveTaskItemEntity = stockinShelveTaskItemService.getOne(queryWrapper);
            if (Objects.isNull(stockinShelveTaskItemEntity)) {
                stockinShelveTaskItemEntity = new StockinShelveTaskItemEntity();
                BeanUtilsEx.copyProperties(item, stockinShelveTaskItemEntity);
                stockinShelveTaskItemEntity.setShelveTaskId(shelveTaskId);
                stockinShelveTaskItemEntity.setCreateBy(loginInfoService.getName());
                stockinShelveTaskItemEntity.setSourceId(item.getTransferTaskId());
                stockinShelveTaskItemEntity.setUpdateBy(loginInfoService.getName());
                stockinShelveTaskItemEntity.setStatus(StockinShelveTaskStatusEnum.PENDING.name());
                stockinShelveTaskItemEntity.setStockinQty(item.getScanQty());
                stockinShelveTaskItemService.save(stockinShelveTaskItemEntity);
            } else {
                Integer oldStockinQty = stockinShelveTaskItemEntity.getStockinQty();
                stockinShelveTaskItemEntity.setStockinQty(oldStockinQty + item.getScanQty());
                stockinShelveTaskItemEntity.setUpdateBy(loginInfoService.getName());
                stockinShelveTaskItemService.updateById(stockinShelveTaskItemEntity);
            }
        });
    }

    /*
     * 根据内部箱号查找上架任务内部箱信息
     * */
    @Transactional
    public ShelveInternalBoxCodeInfo shelveCheckInternalBoxCode(String internalBoxCode) {
        List<String> statusList = new ArrayList<>();
        statusList.add(StockinShelveTaskStatusEnum.PENDING.name());
        statusList.add(StockinShelveTaskStatusEnum.SHELVING.name());
        List<StockinShelveTaskEntity> entityList = stockinShelveTaskMapper.selectList(buildWrapperTaskByInternalBoxCode(internalBoxCode, statusList));
        if (CollectionUtils.isEmpty(entityList)) {
            LOGGER.error("找不到内部箱号:{} 的待上架任务信息", internalBoxCode);
            throw new BusinessServiceException("找不到内部箱号: " + internalBoxCode + "的上架任务记录");
        }
        checkInternalBoxStatus(internalBoxCode);
        List<StockinShelveTaskItemEntity> shelveTaskItemEntityList = shelveTaskItemMapper.selectList(buildWrapperShelveTaskItemByTaskId(entityList.get(0).getShelveTaskId()));
        if (CollectionUtils.isEmpty(shelveTaskItemEntityList)) {
            throw new BusinessServiceException("找不到内部箱号: " + internalBoxCode + "的上架任务明细记录");
        }
        ShelveInternalBoxCodeInfo shelveInternalBoxCodeInfo = new ShelveInternalBoxCodeInfo();
        shelveInternalBoxCodeInfo.setSkuQty(shelveTaskItemEntityList.size());
        shelveInternalBoxCodeInfo.setInternalBoxCode(internalBoxCode);
        shelveInternalBoxCodeInfo.setAreaName(stockInternalBoxService.getAreaNameByInternalBoxCode(internalBoxCode));
        if (!CollectionUtils.isEmpty(shelveTaskItemEntityList)) {
            int allStock = shelveTaskInternalBoxAllNum(shelveTaskItemEntityList);
            shelveInternalBoxCodeInfo.setAllStock(allStock);
            shelveInternalBoxCodeInfo.setPendingQty(allStock);
        }
        if (!entityList.get(0).getStatus().equals(StockinShelveTaskStatusEnum.SHELVING.name())) {
            changeStockinShelveTaskStatus(entityList.get(0), StockinShelveTaskStatusEnum.SHELVING.name());
        }
        shelveInternalBoxCodeInfo.setStatus(StockinShelveTaskStatusEnum.SHELVING.getName());

        // 检查是否配置了先进先出
        Boolean isFifo = Boolean.FALSE;
        if (!CollectionUtils.isEmpty(shelveTaskItemEntityList)) {
            // 取第一个SKU检查是否配置了FIFO
            String sku = shelveTaskItemEntityList.get(0).getSku();
            isFifo = bdTagService.checkStockFifo(sku);
        }
        shelveInternalBoxCodeInfo.setIsFifo(isFifo);

        // 更新内部箱状态为(上架中)
        stockInternalBoxService.changeStockInternalBoxStatus(internalBoxCode, StockInternalBoxStatusEnum.SHELVING.name());
        // 更新入库单状态
        shelveChangeStockinOrderStatus(internalBoxCode);
        return shelveInternalBoxCodeInfo;
    }

    public int shelveTaskInternalBoxAllNum(List<StockinShelveTaskItemEntity> shelveTaskItemEntityList) {
        Map<String, List<StockinShelveTaskItemEntity>> mapGroup = shelveTaskItemEntityList.stream().collect(Collectors.groupingBy(StockinShelveTaskItemEntity::getSku));
        List<Integer> list = new ArrayList<>();
        mapGroup.forEach((sku, groupList) -> {
            Integer shelvedTotalQty = groupList.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
            Integer returnedQty = groupList.stream().mapToInt(StockinShelveTaskItemEntity::getReturnedQty).sum();
            Integer shelvedQty = groupList.stream().mapToInt(StockinShelveTaskItemEntity::getShelvedQty).sum();
            int skuTotal = Math.max(shelvedTotalQty - returnedQty - shelvedQty, 0);
            list.add(skuTotal);
        });
        int qty = 0;
        for (Integer integer : list) {
            qty = qty + integer;
        }
        return qty;
    }


    private void shelveChangeStockinOrderStatus(String internalBoxCode) {
        // 根据内部箱号，状态待上架查询所有内部箱明细，得到多个出库单
        List<StockInternalBoxItemEntity> boxItemList = internalBoxItemMapper.searchByInternalBoxCodeAndStatus(internalBoxCode, StockInternalBoxStatusEnum.SHELVING.name())
                .stream().filter(item -> item.getQty() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(boxItemList)) {
            return;
        }
        List<String> stockinOrderNoList = boxItemList.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stockinOrderNoList)) {
            List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockinOrderNoList);
            List<StockinOrderEntity> filterStockinOrderEntityList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(stockinOrderEntityList)) {
                filterStockinOrderEntityList = stockinOrderEntityList.stream().filter(s -> !StockinOrderStatusEnum.SHELVING.name().equals(s.getStatus())
                        && !StockinOrderStatusEnum.CHECKING.name().equals(s.getStatus())
                        && !StockinOrderStatusEnum.COMPLETED.name().equals(s.getStatus())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(filterStockinOrderEntityList)) {
                String contentStr = String.format("扫描内部箱【%s】开始上架,", internalBoxCode);
                stockinOrderService.batchChangeStatus(filterStockinOrderEntityList, StockinOrderStatusEnum.SHELVING.name(), contentStr, StockinOrderLogTypeEnum.BEGIN_SHELVE.getName());
                stcokinOrderTimeService.addTimeBatch(filterStockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList()), StockinOrderLogTypeEnum.BEGIN_SHELVE);
            }
        }
    }


    public List<StockinShelveTaskItemInfo> buildShelveTaskItemInfo(List<StockinShelveTaskItemEntity> shelveTaskItemEntityList) {
        List<StockinShelveTaskItemInfo> shelveTaskItemInfoList = new ArrayList<>();
        shelveTaskItemEntityList.forEach(item -> {
            StockinShelveTaskItemInfo stockinShelveTaskItemInfo = new StockinShelveTaskItemInfo();
            BeanUtilsEx.copyProperties(item, stockinShelveTaskItemInfo);
            shelveTaskItemInfoList.add(stockinShelveTaskItemInfo);
        });
        return shelveTaskItemInfoList;
    }

    public List<StockinShelveTaskCountResponse> getStockinShelveTaskCount(TabCountRequest request) {
        Map<String, List<StockinShelveTaskCountResponse>> collect = this.getBaseMapper().countByStatus(request).stream().collect(Collectors.groupingBy(StockinShelveTaskCountResponse::getStatus));
        List<StockinShelveTaskCountResponse> list = Arrays.stream(StockinShelveTaskStatusEnum.values()).map(statusEnum -> {
            StockinShelveTaskCountResponse response = new StockinShelveTaskCountResponse();
            List<StockinShelveTaskCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getName());
            return response;
        }).collect(Collectors.toList());

        List<StockinShelveTaskCountResponse> countResponses = collect.get(StockInternalBoxStatusEnum.WAIT_QC.name());
        StockinShelveTaskCountResponse countResponse = new StockinShelveTaskCountResponse();
        countResponse.setQty(CollectionUtils.isEmpty(countResponses) ? Integer.valueOf(0) : countResponses.get(0).getQty());
        countResponse.setStatus(StockInternalBoxStatusEnum.WAIT_QC.name());
        countResponse.setValue(StockInternalBoxStatusEnum.WAIT_QC.name());
        countResponse.setLabel(StockInternalBoxStatusEnum.WAIT_QC.getStatus());

        List<StockinShelveTaskCountResponse> sortList = new ArrayList<>();
        sortList.add(countResponse);
        sortList.addAll(list);

        StockinShelveTaskCountResponse response = new StockinShelveTaskCountResponse();
        response.setQty(list.stream().mapToInt(StockinShelveTaskCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        sortList.add(response);
        return sortList;
    }

    /**
     * 更新上架任务状态
     */
    @Transactional
    public void changeStockinShelveTaskStatus(StockinShelveTaskEntity stockinShelveTaskEntity, String status) {
        if (stockinShelveTaskEntity.getStatus().equals(status))
            return;
        if (StockinShelveTaskStatusEnum.SHELVING.name().equals(status) && Objects.isNull(stockinShelveTaskEntity.getOperateStartDate()))
            stockinShelveTaskEntity.setOperateStartDate(new Date());
        if (StockinShelveTaskStatusEnum.SHELVED.name().equals(status))
            stockinShelveTaskEntity.setOperateEndDate(new Date());
        stockinShelveTaskEntity.setUpdateBy(loginInfoService.getName());
        if (!StockinAutoCompleteShelveTaskJob.STOCKIN_AUTO_COMPLETE_SHELVE_TASK_JOB_NAME.equals(loginInfoService.getName())) {
            stockinShelveTaskEntity.setOperator(loginInfoService.getName());
        }

        stockinShelveTaskEntity.setStatus(status);
        stockinShelveTaskMapper.updateById(stockinShelveTaskEntity);
    }

    /**
     * 自动上架,如果没有上架开始时间则赋值自动上架开始时间
     *
     * @param shelveTaskId
     */
    public void updateShelveDate(Integer shelveTaskId) {
        StockinShelveTaskEntity shelveTaskEntity = this.getById(shelveTaskId);
        if (Objects.isNull(shelveTaskEntity) || Objects.nonNull(shelveTaskEntity.getOperateStartDate())) {
            return;
        }
        shelveTaskEntity.setOperateStartDate(new Date());
        shelveTaskEntity.setUpdateBy(loginInfoService.getName());
        stockinShelveTaskMapper.updateById(shelveTaskEntity);
    }

    // 上架完成更新内部箱调拨任务状态及记录日志
    public void shelveCompleteChangeTransferBoxTask(List<StockinShelveTaskItemEntity> list) {
        List<Integer> transferList = list.stream().map(StockinShelveTaskItemEntity::getSourceId).distinct().collect(Collectors.toList());
        List<ShelveTaskItemInfo> shelveItemList = stockinShelveTaskItemService.getBySourceIdListAndStockinType(transferList, StockinShelveTaskTypeEnum.TRANSFER_SHELVE.name());

        Map<Integer, List<ShelveTaskItemInfo>> mapGroup = shelveItemList.stream().collect(Collectors.groupingBy(ShelveTaskItemInfo::getSourceId));
        List<Integer> filterTransferIdList = new ArrayList<>();
        mapGroup.forEach((transferId, itemList) -> {
            List<ShelveTaskItemInfo> filterShelveItemList = itemList.stream().filter(m -> m.getStockinQty().compareTo(m.getShelvedQty() + m.getReturnedQty()) > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterShelveItemList)) {
                filterTransferIdList.add(transferId);
            }
        });
        if (!CollectionUtils.isEmpty(filterTransferIdList)) {
            List<StockTransferInternalBoxTaskEntity> transferInternalBoxTaskList = transferInternalBoxTaskService.listByIds(filterTransferIdList);
            if (CollectionUtils.isEmpty(transferInternalBoxTaskList)) {
                throw new BusinessServiceException("找不到内部箱调拨任务信息");
            }
            transferInternalBoxTaskList.forEach(item -> {
                item.setStatus(StockTransferInternalBoxTaskStatusEnum.COMPLETED.name());
                item.setUpdateBy(loginInfoService.getName());
                item.setOperateEndDate(new Date());
                item.setOperator(loginInfoService.getName());
            });
            transferInternalBoxTaskService.updateBatchById(transferInternalBoxTaskList);
            // 内部箱调拨任务日志
            transferInternalBoxLogService.addLogBatch(filterTransferIdList, transferInternalBoxTaskList.get(0).getLocation(), StockTransferInternalBoxLogTypeEnum.COMPLETED.name(), "完成调拨");
        }
    }

    // 校验上架库位类型
    public void checkShelvePosition(String positionCode) {
        BdPosition bdPosition = bdPositionService.bdPositionByPositionCode(positionCode);
        if (Objects.isNull(bdPosition)) {
            throw new BusinessServiceException(String.format("找不到库位: %s 的信息", positionCode));
        }
        if (!BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPosition.getPositionType()) && !BdPositionTypeEnum.SPARE_POSITION.name().equals(bdPosition.getPositionType())
                && !BdPositionTypeEnum.STOCK_POSITION.name().equals(bdPosition.getPositionType())
                && !BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(bdPosition.getPositionType())
                && !BdPositionTypeEnum.OEM_POSITION.name().equals(bdPosition.getPositionType())
                && !BdPositionTypeEnum.STORE_POSITION.name().equals(bdPosition.getPositionType())) {
            throw new BusinessServiceException("该库位不允许操作上架");
        }
    }

    // 上架完成回写入库单状态
    public void shelveCompleteChangStockinOrderStatus(List<StockinShelveTaskItemEntity> shelveTaskItemList) {
        List<Integer> stockinOrderIdList = shelveTaskItemList.stream().map(StockinShelveTaskItemEntity::getSourceId).distinct().collect(Collectors.toList());
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.listByIds(stockinOrderIdList);
        List<ShelveTaskItemInfo> list = shelveTaskItemMapper.searchShelveTaskItemList(stockinOrderIdList, StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name());
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(stockinOrderEntityList)) {
            LOGGER.error("上架完成回写入库单状态找不到入库单信息");
            throw new BusinessServiceException("上架完成回写入库单状态找不到入库单信息", null);
        }
        Map<Integer, String> stockinOrderNoMap = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderId, StockinOrderEntity::getStockinOrderNo));
        Map<Integer, List<ShelveTaskItemInfo>> mapGroup = list.stream().collect(Collectors.groupingBy(ShelveTaskItemInfo::getSourceId));
        List<Integer> idList = new ArrayList<>();
        mapGroup.forEach((stockinOrderId, itemList) -> {
            String stockinOrderNo = stockinOrderNoMap.get(stockinOrderId);
            if (StringUtils.hasText(stockinOrderNo)) {
                Integer count = internalBoxItemMapper.selectCount(new LambdaQueryWrapper<StockInternalBoxItemEntity>().eq(StockInternalBoxItemEntity::getStockInOrderNo, stockinOrderNo).ne(StockInternalBoxItemEntity::getQty, 0));
                LOGGER.info("上架完成回写入库单状态:{},{}", stockinOrderNo, count);
                if (count > 0) return;
            }
            List<ShelveTaskItemInfo> filterItemList = itemList.stream().filter(m -> !StockinShelveTaskStatusEnum.SHELVED.name().equals(m.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterItemList)) {
                idList.add(stockinOrderId);
            }
        });
        if (!CollectionUtils.isEmpty(idList)) {
            updateStockinOrder(idList, false);
        }
    }

    @Transactional
    public void updateStockinOrder(List<Integer> idList, boolean isConfirmComplete) {
        LOGGER.info("上架完成回写入库单状态,idList:{}", idList);
        // 泰利是虚拟上架，不自动完结入库单，
        String value = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKIN_IS_NEGATIVE_UP_SHELF.getKey());
        if (!isConfirmComplete && com.nsy.api.core.apicore.util.StringUtils.hasText(value) && "true".equals(value)) {
            LOGGER.info("上架完成回写入库单状态,允许上架数回退，不自动完结入库单");
            return;
        }

        List<StockinOrderEntity> stockinOrderList = stockinOrderService.listByIds(idList);
        LOGGER.info("上架完成回写入库单状态,status:{}", stockinOrderList.stream().map(StockinOrderEntity::getStatus).collect(Collectors.joining(",")));
        List<StockinOrderEntity> filterStockinOrderList = stockinOrderList.stream().filter(m -> !(StockinOrderStatusEnum.CHECKING.name().equals(m.getStatus())
                || StockinOrderStatusEnum.COMPLETED.name().equals(m.getStatus()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterStockinOrderList)) {
            List<StockinOrderLogEntity> stockinOrderLogEntityList = Lists.newArrayList();
            filterStockinOrderList.forEach(item -> {
                item.setStatus(StockinOrderStatusEnum.CHECKING.name());
                item.setCompleteShelvedDate(new Date());
                item.setUpdateBy(loginInfoService.getName());
                StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(item.getStockinOrderId(), StockinOrderLogTypeEnum.COMPLETE_SHELVE.getName(), "该入库单上架完成,进入待核对状态");
                stockinOrderLogEntityList.add(logEntity);
            });
            stcokinOrderTimeService.addTimeBatch(filterStockinOrderList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList()), StockinOrderLogTypeEnum.COMPLETE_SHELVE);
            stockinOrderService.updateBatchById(filterStockinOrderList);
            if (!CollectionUtils.isEmpty(stockinOrderLogEntityList))
                stockinOrderLogService.saveBatch(stockinOrderLogEntityList);
            stockinOrderService.autoCheckConfirm(filterStockinOrderList);
            // 上架完成反馈erp
            stockinOrderService.feedbackShelvedToErp(filterStockinOrderList, loginInfoService.getName());

            //伟跃自动核对和采购确认
            autoShip(filterStockinOrderList);
        }
        // 是否加入核对
        List<String> supplierDeliveryBoxCodeList = stockinOrderList.stream().map(StockinOrderEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.toList());
        createCheckQueue(supplierDeliveryBoxCodeList, loginInfoService.getName());
    }

    private void autoShip(List<StockinOrderEntity> filterStockinOrderList) {
        String value = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKIN_IGNORE_CHECK.getKey());
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(value) && "true".equals(value)) {
            List<Integer> stockinOrderIds = filterStockinOrderList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());
            List<StockinOrderEntity> list = stockinOrderService.list(new LambdaQueryWrapper<StockinOrderEntity>()
                    .select(StockinOrderEntity::getStockinOrderId)
                    .in(StockinOrderEntity::getStockinOrderId, stockinOrderIds)
                    .eq(StockinOrderEntity::getStatus, StockinOrderStatusEnum.CHECKING.name()));
            if (!CollectionUtils.isEmpty(list)) {
                List<Integer> integerList = list.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());
                stockinReceiptService.check(integerList);
                //stockinReceiptService.differenceConfirm(new IdListRequest(integerList));
            }
        }
    }

    /**
     * 判断所属月台预收单下的入库单是否全部上架完成
     *
     * @param supplierDeliveryBoxCodeList 出库箱码
     */
    private void createCheckQueue(List<String> supplierDeliveryBoxCodeList, String operator) {
        List<Integer> scheduleIds = platformScheduleItemService.list(new QueryWrapper<StockPlatformScheduleItemEntity>().lambda()
                .in(StockPlatformScheduleItemEntity::getSupplierDeliveryBoxCode, supplierDeliveryBoxCodeList)).stream().map(StockPlatformScheduleItemEntity::getPlatformScheduleId).collect(Collectors.toList());
        if (scheduleIds.isEmpty()) {
            LOGGER.error("未生成月台任务: {}", supplierDeliveryBoxCodeList.get(0));
            return;
        }
        List<StockPlatformScheduleEntity> scheduleEntities = platformScheduleService.list(new QueryWrapper<StockPlatformScheduleEntity>().lambda()
                .in(StockPlatformScheduleEntity::getPlatformScheduleId, scheduleIds));
        List<StockinOrderTaskEntity> taskEntities = taskService.list(new QueryWrapper<StockinOrderTaskEntity>().lambda().in(StockinOrderTaskEntity::getPlatformScheduleId, scheduleIds));
        if (taskEntities.isEmpty()) {
            LOGGER.error("入库任务未赋值月台任务id: {}", supplierDeliveryBoxCodeList.get(0));
            return;
        }
        List<Integer> taskIds = taskEntities.stream().map(StockinOrderTaskEntity::getTaskId).collect(Collectors.toList());
        List<StockinOrderEntity> stockinOrderEntities = stockinOrderService.list(new QueryWrapper<StockinOrderEntity>().lambda().in(StockinOrderEntity::getTaskId, taskIds));

        List<String> supplierDeliveryNoList = new LinkedList<>();
        for (StockPlatformScheduleEntity scheduleEntity : scheduleEntities) {
            List<Integer> currentTaskIds = taskEntities.stream().filter(o -> o.getPlatformScheduleId().equals(scheduleEntity.getPlatformScheduleId())).map(StockinOrderTaskEntity::getTaskId).collect(Collectors.toList());
            int unShelvedCount = (int) stockinOrderEntities.stream().filter(o -> !StockinOrderStatusEnum.CHECKING.name().equals(o.getStatus())
                    || !StockinOrderStatusEnum.COMPLETED.name().equals(o.getStatus())
                    && currentTaskIds.contains(o.getTaskId())).count();
            if (unShelvedCount <= 0)
                supplierDeliveryNoList.add(scheduleEntity.getSupplierDeliveryNo());
        }
        if (supplierDeliveryNoList.isEmpty()) return;
        if (supplierDeliveryNoList.stream().anyMatch(supplierDeliveryNo -> supplierDeliveryNo.startsWith("DBRK"))) {
            stockTransferCrossSpaceService.updateTransferInStatus(supplierDeliveryNoList, StockTransferStatusEnum.INBOUNDED);
        } else
            checkQueueService.createQueueList(ExternalOrderCheckModuleEnum.STOCKIN, ExternalSystemTypeEnum.ERP, supplierDeliveryNoList, operator);
    }

    private void checkInternalBoxStatus(String internalBoxCode) {
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.getByInternalBoxCode(internalBoxCode);
        if (CollectionUtils.isEmpty(boxItemEntityList))
            throw new BusinessServiceException(String.format("内部箱【%s】没有商品，无法上架", internalBoxCode));
        if (!boxItemEntityList.stream().anyMatch(item -> StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())))
            throw new BusinessServiceException(String.format("内部箱【%s】没有待上架的商品，无法上架", internalBoxCode));
    }

    public StockinShelveTaskEntity getByInternalBoxCode(String internalBoxCode) {
        List<StockinShelveTaskEntity> entityList = stockinShelveTaskMapper.selectList(buildWrapperTaskByInternalBoxCode(internalBoxCode, null));
        if (CollectionUtils.isEmpty(entityList)) {
            LOGGER.error("找不到内部箱号:{} 的上架任务信息", internalBoxCode);
            throw new BusinessServiceException("找不到内部箱号: " + internalBoxCode + "的上架任务记录");
        }
        return entityList.get(0);
    }

    public StockinShelveTaskEntity getByInternalBoxCodeNoValid(List<String> internalBoxCodes) {
        LambdaQueryWrapper<StockinShelveTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockinShelveTaskEntity::getInternalBoxCode, internalBoxCodes);
        queryWrapper.orderByDesc(StockinShelveTaskEntity::getCreateDate);
        List<StockinShelveTaskEntity> entityList = list(queryWrapper);
        if (CollectionUtils.isEmpty(entityList)) {
            LOGGER.error("找不到内部箱号的上架任务信息");
            return new StockinShelveTaskEntity();
        }
        return entityList.get(0);
    }

    public LambdaQueryWrapper<StockinShelveTaskItemEntity> buildWrapperShelveTaskItemByTaskId(Integer shelveTaskId) {
        LambdaQueryWrapper<StockinShelveTaskItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(shelveTaskId))
            queryWrapper.eq(StockinShelveTaskItemEntity::getShelveTaskId, shelveTaskId);
        return queryWrapper;
    }

    public static LambdaQueryWrapper<StockinShelveTaskEntity> buildWrapperTaskByInternalBoxCode(String internalBoxCode, List<String> statusList) {
        LambdaQueryWrapper<StockinShelveTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(internalBoxCode))
            queryWrapper.eq(StockinShelveTaskEntity::getInternalBoxCode, internalBoxCode);
        if (!CollectionUtils.isEmpty(statusList))
            queryWrapper.in(StockinShelveTaskEntity::getStatus, statusList);
        queryWrapper.orderByDesc(StockinShelveTaskEntity::getShelveTaskId);
        return queryWrapper;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_SHELVE_TASK;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinShelveTaskListRequest downloadRequest = JSONObject.parseObject(request.getRequestContent(), StockinShelveTaskListRequest.class);
        // 设置每次的查询数量
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        PageResponse<ShelveTaskListResponse> pageResponse = stockinShelveTaskListService.getStockinShelveTaskList(downloadRequest);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMAT_DATE4);
        List<StockinShelveTaskExport> resultList = pageResponse.getContent().stream().map(item -> {
            StockinShelveTaskExport export = new StockinShelveTaskExport();
            BeanUtilsEx.copyProperties(item, export, "operateStartDate", "operateEndDate");
            if (item.getOperateStartDate() != null) {
                export.setOperateStartDate(dateFormat.format(item.getOperateStartDate()));
            }
            if (item.getOperateEndDate() != null)
                export.setOperateEndDate(dateFormat.format(item.getOperateEndDate()));
            return export;
        }).collect(Collectors.toList());
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }

    public LambdaQueryWrapper<StockinShelveTaskEntity> wrapperTaskByInternalBoxCodeAndStatus(String internalBoxCode, String status) {
        LambdaQueryWrapper<StockinShelveTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(internalBoxCode))
            queryWrapper.eq(StockinShelveTaskEntity::getInternalBoxCode, internalBoxCode);
        if (StringUtils.hasText(status)) queryWrapper.eq(StockinShelveTaskEntity::getStatus, status);
        return queryWrapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public void generateShelveTask(StockinShelveTaskGenerateRequest request) {
        if (StringUtils.hasText(request.getInternalBoxCode())) {
            List<StockInternalBoxItemEntity> internalBoxItemList = internalBoxItemService.listByIds(request.getInternalBoxItemIdList());
            if (CollectionUtils.isEmpty(internalBoxItemList)) {
                LOGGER.error("内部箱：【 {}】生成上架任务时，找不到内部箱明细", request.getInternalBoxCode());
                return;
            }
            Map<String, List<StockInternalBoxItemEntity>> stockinOrderNoGroup = internalBoxItemList.stream().collect(Collectors.groupingBy(StockInternalBoxItemEntity::getStockInOrderNo));
            List<String> stockinOrderNoList = new ArrayList<>();
            stockinOrderNoGroup.forEach((stockinOrderNo, list) -> stockinOrderNoList.add(stockinOrderNo));
            List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockinOrderNoList);
            if (CollectionUtils.isEmpty(stockinOrderEntityList)) {
                LOGGER.error("内部箱：【 {}】生成上架任务时，根据内部箱明细找不到入库单信息", request.getInternalBoxCode());
                return;
            }
            for (StockinOrderEntity s : stockinOrderEntityList) {
                List<StockInternalBoxItemEntity> filterStockinOrderIdItemList = internalBoxItemList.stream().filter(m -> s.getStockinOrderNo().equals(m.getStockInOrderNo())).collect(Collectors.toList());
                List<Integer> itemIdList = filterStockinOrderIdItemList.stream().map(StockInternalBoxItemEntity::getInternalBoxItemId).collect(Collectors.toList());
                shelveTaskCreateService.syncGenerateStockinShelveTask(StockinTypeEnum.ALLOT.name().equals(s.getStockinType()) ? StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name() : StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name(), s.getStockinOrderId(), request.getInternalBoxCode(), itemIdList);
            }
        }
    }


    // 上架完成回写入库单状态(仓间调拨)
    public void shelveCompleteChangSpaceCrossStockinOrderStatus(List<StockinShelveTaskItemEntity> shelveTaskItemList) {
        List<Integer> stockinOrderIdList = shelveTaskItemList.stream().map(StockinShelveTaskItemEntity::getSourceId).distinct().collect(Collectors.toList());
        List<ShelveTaskItemInfo> list = shelveTaskItemMapper.searchShelveTaskItemList(stockinOrderIdList, StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name());
        if (CollectionUtils.isEmpty(list)) {
            LOGGER.error("上架完成回写入库单状态找不到入库单信息");
            throw new BusinessServiceException("上架完成回写入库单状态找不到入库单信息", null);
        }
        Map<Integer, List<ShelveTaskItemInfo>> mapGroup = list.stream().collect(Collectors.groupingBy(ShelveTaskItemInfo::getSourceId));
        List<Integer> idList = new ArrayList<>();
        mapGroup.forEach((stockinOrderId, itemList) -> {
            List<ShelveTaskItemInfo> filterItemList = itemList.stream().filter(m -> !StockinShelveTaskStatusEnum.SHELVED.name().equals(m.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterItemList)) {
                idList.add(stockinOrderId);
            }
        });
        if (!CollectionUtils.isEmpty(idList)) {
            List<StockinOrderEntity> stockinOrderList = stockinOrderService.listByIds(idList);
            List<StockinOrderEntity> filterStockinOrderList = stockinOrderList.stream().filter(m -> !StockinOrderStatusEnum.CHECKING.name().equals(m.getStatus())
                    || !StockinOrderStatusEnum.COMPLETED.name().equals(m.getStatus())).collect(Collectors.toList());
            List<StockinOrderLogEntity> stockinOrderLogEntityList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(filterStockinOrderList)) {
                filterStockinOrderList.forEach(item -> {
                    item.setStatus(StockinOrderStatusEnum.CHECKING.name());
                    item.setUpdateBy(loginInfoService.getName());
                    StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(item.getStockinOrderId(), StockinOrderLogTypeEnum.COMPLETE_SHELVE.getName(), "该入库单上架完成,进入待核对状态");
                    stockinOrderLogEntityList.add(logEntity);
                });
                stockinOrderService.updateBatchById(filterStockinOrderList);
                stockinOrderService.autoCheckConfirm(filterStockinOrderList);
            }
            if (!CollectionUtils.isEmpty(stockinOrderLogEntityList))
                stockinOrderLogService.saveBatch(stockinOrderLogEntityList);
            // 是否加入核对
            List<String> supplierDeliveryBoxCodeList = stockinOrderList.stream().map(StockinOrderEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.toList());
            createCheckQueue(supplierDeliveryBoxCodeList, loginInfoService.getName());
        }
    }
}
