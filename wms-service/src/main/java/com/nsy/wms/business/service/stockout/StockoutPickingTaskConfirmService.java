package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockout.StockoutBatchPickingTask;
import com.nsy.api.wms.domain.stockout.StockoutOrderSkuDescription;
import com.nsy.api.wms.domain.stockout.StockoutOrderTaskItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskConfirmSpaceArea;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskConfirmSpaceAreaSku;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutPickingTaskLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskCreateEnum;
import com.nsy.api.wms.request.stockout.StockoutPickingRecordSetRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingTaskBatchRequest;
import com.nsy.api.wms.response.stockout.StockoutAllBatchPickingTaskResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskConfirmResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskConfirmSpaceResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskItemRecordResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.dto.stockout.StockoutVasTaskCreateDTO;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.ProcessPickingTaskCompleteItemRequest;
import com.nsy.wms.business.manage.scm.request.ProcessPickingTaskCompleteRequest;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdSpaceAreaService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockLockService;
import com.nsy.wms.business.service.stock.StockPrematchRemoveService;
import com.nsy.wms.business.service.stock.StockPrematchService;
import com.nsy.wms.business.service.stock.StockPrintService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.business.service.stockout.valid.StockoutPickingTaskValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.elasticjob.stockout.StockoutPickingTaskStartJob;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceAreaEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingItemRecordEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StockoutPickingTaskConfirmService {
    @Resource
    StockoutPickingTaskService stockoutPickingTaskService;
    @Resource
    StockInternalBoxService stockInternalBoxService;
    @Resource
    ProductSpecInfoService productSpecInfoService;
    @Resource
    BdAreaService areaService;
    @Resource
    BdSpaceAreaService spaceAreaService;
    @Resource
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Resource
    StockoutPickingItemRecordService stockoutPickingItemRecordService;
    @Resource
    StockInternalBoxItemService stockInternalBoxItemService;
    @Resource
    StockoutPickingLogService logService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutBatchService stockoutBatchService;
    @Resource
    MessageProducer messageProducer;
    @Resource
    StockoutPickingTaskConfirmBuildService stockoutPickingTaskConfirmBuildService;
    @Resource
    StockoutBatchLogService stockoutBatchLogService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Resource
    ScmApiService scmApiService;
    @Resource
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    StockoutOrderLackItemService stockoutOrderLackItemService;
    @Resource
    StockPrintService stockPrintService;
    @Resource
    BdTagMappingService tagMappingService;
    @Resource
    StockoutPickingTaskValid stockoutPickingTaskValid;
    @Resource
    StockLockService stockLockService;
    @Resource
    StockPrematchService stockPrematchService;
    @Resource
    StockPrematchRemoveService stockPrematchRemoveService;
    @Resource
    ProductInfoService productInfoService;

    @Transactional
    public StockoutPickingTaskConfirmResponse getByPickingTaskId(Integer taskId) {
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        if (StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus()))
            throw new BusinessServiceException("该任务已经拣货完成");
        List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList = stockoutPickingTaskItemService.findAllByTaskId(taskId);
        List<StockoutPickingItemRecordEntity> pickingItemRecordEntityList = stockoutPickingItemRecordService.findAllByTaskId(taskId);
        Map<Integer, List<StockoutPickingItemRecordEntity>> recordMap = pickingItemRecordEntityList.stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getTaskItemId));

        StockoutPickingTaskConfirmResponse stockoutPickingTaskConfirm = new StockoutPickingTaskConfirmResponse();
        stockoutPickingTaskConfirm.setPickingInternalBoxCode(taskEntity.getPickingBoxCode());
        BeanUtilsEx.copyProperties(taskEntity, stockoutPickingTaskConfirm);
        stockoutPickingTaskConfirm.setExpectedQty(pickingTaskItemEntityList.stream().mapToInt(StockoutPickingTaskItemEntity::getExpectedQty).sum());
        stockoutPickingTaskConfirm.setPickedQty(pickingTaskItemEntityList.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum());
        stockoutPickingTaskConfirm.setWaitPickQty(stockoutPickingTaskConfirm.getExpectedQty() - stockoutPickingTaskConfirm.getPickedQty());

        stockoutPickingTaskConfirm.setStockoutPickingTaskConfirmSkuList(stockoutPickingTaskConfirmBuildService.buildPickingTaskConfirmSkuList(taskEntity.getBatchId(), pickingTaskItemEntityList, recordMap));
        List<StockoutPickingTaskConfirmSpaceArea> stockoutPickingTaskConfirmSpaceAreaList = stockoutPickingTaskConfirmBuildService.buildPickingTaskConfirmSpaceAreaList(taskEntity, pickingTaskItemEntityList, pickingItemRecordEntityList);
        stockoutPickingTaskConfirm.setStockoutPickingTaskConfirmSpaceAreaList(stockoutPickingTaskConfirmSpaceAreaList);

        stockoutPickingTaskConfirm.setSpaceAreaNameQty(stockoutPickingTaskConfirmSpaceAreaList.size());
        Long internalBoxQty = pickingItemRecordEntityList.stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().count();
        stockoutPickingTaskConfirm.setInternalBoxQty(internalBoxQty.intValue());
        if (StockoutPickingTaskStatusEnum.SUSPEND_PICK.name().equals(taskEntity.getStatus())
                && StringUtils.hasText(taskEntity.getOperator())
                && taskEntity.getOperator().equals(loginInfoService.getName())) {
            stockoutPickingTaskConfirm.setShowNotice(Boolean.TRUE);
            stockoutPickingTaskConfirm.setNotice("当前任务是暂停拣货中，是否继续拣货");
        } else {
            stockoutPickingTaskConfirm.setShowNotice(Boolean.FALSE);
        }
        StockoutBatchEntity stockoutBatchById = stockoutBatchService.getStockoutBatchById(taskEntity.getBatchId());
        stockoutPickingTaskConfirm.setShowSpaceArea(!StockoutPickingTypeEnum.WHOLE_PICK.name().equals(stockoutBatchById.getPickingType()));
        return stockoutPickingTaskConfirm;
    }


    @Transactional
    public void changePickingTaskStatus(Integer taskId, String status) {
        StockoutPickingTaskValid.validStatus(status);
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        if (StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())) {
            throw new BusinessServiceException("任务已完成，不允许改变状态");
        }
        taskEntity.setUpdateBy(loginInfoService.getName());
        if (StockoutPickingTaskStatusEnum.PICKING.name().equals(status)) {
            taskEntity.setOperator(loginInfoService.getName());
            taskEntity.setOperateStartDate(new Date());
            if (StockoutPickingTaskStatusEnum.SUSPEND_PICK.name().equals(taskEntity.getStatus())) {
                taskEntity.setPauseEndDate(new Date());
            }
        } else if (StockoutPickingTaskStatusEnum.SUSPEND_PICK.name().equals(status)) {
            taskEntity.setPauseStartDate(new Date());
            logService.addLog(taskEntity.getTaskId(), StockoutPickingTaskLogTypeEnum.SUSPEND_PICK.getChangeLogType(),
                    String.format("【%s】暂停拣货", loginInfoService.getName()));
        }
        taskEntity.setStatus(status);
        stockoutPickingTaskService.updateById(taskEntity);
    }

    @Transactional
    @JLock(keyConstant = "stockoutPickingRecordSet", lockKey = "#taskId")
    public void stockoutPickingRecordSet(Integer taskId, StockoutPickingRecordSetRequest request) {
        StockoutPickingTaskValid.validateStockoutPickingRecordSetRequest(request);
        Validator.isValid(request.getQty(), qty -> qty > 0, "扫描数量需大于0");
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        StockoutPickingTaskItemEntity taskItemEntity = stockoutPickingTaskItemService.getById(request.getTaskItemId());
        if (Objects.isNull(taskItemEntity)) {
            throw new BusinessServiceException("未找到对应的拣货任务明细，请确认商品条形码!");
        }
        StockInternalBoxEntity boxEntity = stockInternalBoxService.findByInternalBoxCode(request.getInternalBoxCode());
        if (!StockInternalBoxTypeEnum.PICKING_BOX.name().equals(boxEntity.getInternalBoxType()))
            throw new BusinessServiceException("该箱子不是拣货箱，请扫描拣货箱！");
        //1.查询库位库存是否足够，不足则提示盘点  2.查询库存是否被锁定，锁定则抛出异常
        stockLockService.validLockStock(taskItemEntity.getSku(), taskItemEntity.getPositionCode(), request.getQty());
        //如果是加工发货库位，校验可用库存
        stockoutPickingTaskValid.validProcessPositionStock(taskItemEntity, request.getQty());
        if (StringUtils.hasText(taskItemEntity.getOperator()) && !taskItemEntity.getOperator().equals(loginInfoService.getName()))
            throw new BusinessServiceException(String.format("该SKU【%s】,已经由%s拣货", taskItemEntity.getSku(), taskItemEntity.getOperator()));
        //如果为null 设置为0，防止报错
        if (Objects.isNull(taskItemEntity.getPickedQty()))
            taskItemEntity.setPickedQty(0);
        int pickedQty = taskItemEntity.getPickedQty() + request.getQty();
        if (pickedQty > taskItemEntity.getExpectedQty()) {
            throw new BusinessServiceException(String.format("已有%s件，最多还可再扫描%s件，请确认！",
                    taskItemEntity.getPickedQty(), taskItemEntity.getExpectedQty() - taskItemEntity.getPickedQty()));
        }
        taskItemEntity.setPickedQty(pickedQty);
        if (taskItemEntity.getExpectedQty().equals(taskItemEntity.getPickedQty())) {
            taskItemEntity.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
        } else {
            taskItemEntity.setStatus(StockoutPickingTaskStatusEnum.PICKING.name());
        }
        taskItemEntity.setOperator(loginInfoService.getName());
        taskItemEntity.setOperatorDate(new Date());
        taskItemEntity.setUpdateBy(loginInfoService.getName());
        stockoutPickingTaskItemService.updateById(taskItemEntity);
        StockoutPickingItemRecordEntity itemRecordEntity = stockoutPickingItemRecordService.findTopByTaskItemIdAndInternalBoxCode(request.getTaskItemId(), request.getInternalBoxCode());
        if (Objects.nonNull(itemRecordEntity)) {
            itemRecordEntity.setPickedQty(itemRecordEntity.getPickedQty() + request.getQty());
            itemRecordEntity.setUpdateBy(loginInfoService.getName());
            stockoutPickingItemRecordService.updateById(itemRecordEntity);
        } else {
            itemRecordEntity = stockoutPickingItemRecordService.createItem(taskItemEntity, request);
        }
        //商品入箱
        stockoutPickingItemRecordService.skuToBox(taskEntity.getBatchId(), taskItemEntity, itemRecordEntity, request.getQty());
        //删除预配库存
        stockPrematchRemoveService.pickRemovePrematch(taskEntity, taskItemEntity, request.getQty());
        logService.addLog(taskEntity.getTaskId(), loginInfoService.isPda() ? StockoutPickingTaskLogTypeEnum.PDA_PICKING.getChangeLogType() : StockoutPickingTaskLogTypeEnum.PICKING.getChangeLogType(),
                String.format("SKU【%s】、【%s】待拣%s件，从分配库位【%s】已拣%s件，拣货箱号【%s】", taskItemEntity.getSku(), taskItemEntity.getPositionCode(),
                        taskItemEntity.getExpectedQty(), taskItemEntity.getPositionCode(), itemRecordEntity.getPickedQty(), itemRecordEntity.getInternalBoxCode()));
    }

    //isSync 是否同步更新波次状态
    @Transactional
    public StockInternalBox getInternalBox(Integer taskId, String internalBoxCode, boolean isSync) {
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        StockInternalBox stockInternalBox = stockoutPickingTaskValid.validateInternalBox(internalBoxCode, boxEntity);
        StockInternalBoxItemEntity boxItemEntity = stockInternalBoxItemService.getOne(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode).isNotNull(StockInternalBoxItemEntity::getBatchId).last("limit 1").orderByDesc(StockInternalBoxItemEntity::getCreateDate).last("limit 1"));
        if (StockInternalBoxStatusEnum.PACKING.name().equals(boxEntity.getStatus()) && Objects.nonNull(boxItemEntity) && !boxItemEntity.getBatchId().equals(taskEntity.getBatchId()))
            throw new BusinessServiceException("当前拣货箱已被其他拣货波次绑定，不允许混波次共用拣货箱");
        if (StockInternalBoxStatusEnum.SORTING.name().equals(boxEntity.getStatus())) {
            taskEntity.setPickingBoxCode(stockPrintService.getPickBoxAndUpdatePrint(taskEntity));
            stockoutPickingTaskService.updateById(taskEntity);
            boxEntity = stockInternalBoxService.getOne(StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(taskEntity.getPickingBoxCode()));
        }
        if (StockInternalBoxStatusEnum.WAIT_SORT.name().equals(boxEntity.getStatus())
                || StockInternalBoxStatusEnum.WAIT_REVIEW.name().equals(boxEntity.getStatus())) {
            if (Objects.nonNull(boxItemEntity) && boxItemEntity.getBatchId().equals(taskEntity.getBatchId())) {
                stockInternalBox.setShowNotice(Boolean.TRUE);
                stockInternalBox.setNotice("当前拣货箱已经在装箱完成，是否继续装箱");
            } else {
                throw new BusinessServiceException("当前拣货箱已被其他拣货波次绑定，不允许混波次共用拣货箱");
            }
        }
        // 更新内部箱状态
        stockInternalBoxService.changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.PACKING.name());

        logService.addLog(taskEntity.getTaskId(), StockoutPickingTaskLogTypeEnum.START_PICKING.getChangeLogType(),
                String.format("开始拣货，拣货工具%s拣货", loginInfoService.isPda() ? "PDA" : "纸质单"));
        stockoutBatchLogService.addLog(taskEntity.getBatchId(), StockoutBatchLogTypeEnum.START_PICKING.getStockoutBatchLogType(),
                String.format("出库单拣货任务【%S】开始拣货", taskEntity.getTaskId()));
        BeanUtilsEx.copyProperties(boxEntity, stockInternalBox);
        if (StockoutPickingTaskStatusEnum.WAIT_PICK.name().equals(taskEntity.getStatus())) {
            stockoutPickingTaskConfirmBuildService.startPickingUpdateStatus(taskEntity, stockInternalBox, isSync);
        }
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(taskEntity.getBatchId());
        if (Objects.nonNull(batchEntity.getMergeBatchId())) {
            StockoutBatchEntity byId = stockoutBatchService.getById(batchEntity.getMergeBatchId());
            if (byId.getIsNeedProcess().equals(1) && byId.getIsMergeBatch().equals(1))
                return stockInternalBox;
        }
        //去生成分拣任务和复核任务
        generateSplitTaskAndScanTask(taskEntity);
        return stockInternalBox;
    }

    /**
     * 开始拣货生成分拣任务和复核任务
     */
    private void generateSplitTaskAndScanTask(StockoutPickingTaskEntity taskEntity) {
        messageProducer.sendMessage(StockoutPickingTaskStartJob.JOB_NAME, StockoutPickingTaskStartJob.TOPIC, taskEntity.getBatchId(), TenantContext.getTenant());
    }

    @Transactional
    public void fullInternalBox(Integer taskId, String internalBoxCode) {
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        if (Objects.isNull(boxEntity)) {
            return;
        }
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(taskEntity.getBatchId());
        if (Objects.isNull(batchEntity)) {
            throw new BusinessServiceException("错误!未找到对应的波次");
        }
        String changeStatus;
        if (StockoutPickingTypeEnum.WHOLE_PICK.name().equals(batchEntity.getPickingType())) {
            changeStatus = StockInternalBoxStatusEnum.WAIT_REVIEW.name();
        } else {
            changeStatus = StockInternalBoxStatusEnum.WAIT_SORT.name();
        }
        stockInternalBoxService.changeStockInternalBoxStatus(boxEntity, changeStatus);
    }

    /**
     * 拣货缺货
     * 更新库位-拣货库位减去缺货数，异常库位增加缺货数
     *
     * @param taskItemId
     */
    @Transactional
    public void lack(Integer taskItemId) {
        StockoutPickingTaskItemEntity taskItemEntity = stockoutPickingTaskItemService.getById(taskItemId);
        if (Objects.isNull(taskItemEntity)) {
            throw new BusinessServiceException("未找到对应的拣货任务明细，请确认!");
        }
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskItemEntity.getTaskId());
        if (StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())) {
            throw new BusinessServiceException("任务已完成，不允许改变状态");
        }
        if (Objects.isNull(taskItemEntity.getOperatorDate())) {
            taskItemEntity.setOperatorDate(new Date());
            taskItemEntity.setOperator(loginInfoService.getName());
        }
        taskItemEntity.setIsLack(1);
        taskItemEntity.setUpdateBy(loginInfoService.getName());
        taskItemEntity.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
        taskItemEntity.setLackQty(taskItemEntity.getExpectedQty() - taskItemEntity.getPickedQty());
        if (taskItemEntity.getLackQty() == 0) {
            throw new BusinessServiceException("已拣货数等于待拣货数，无法设置为缺货");
        }
        //查询库位上是否还有库存，如果有，锁定库存
        stockLockService.pickLackLockStock(taskItemEntity.getSku(), taskItemEntity.getPositionCode());
        stockoutPickingTaskItemService.updateById(taskItemEntity);
        //删除预配库存
        stockPrematchRemoveService.pickRemovePrematch(taskEntity, taskItemEntity, taskItemEntity.getLackQty());
    }

    @Transactional
    public void pickingComplete(List<Integer> taskIdList) {
        for (Integer taskId : taskIdList) {
            StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
            if (!StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus()))
                messageProducer.sendMessage(KafkaConstant.SYNC_STOCKOUT_PICKING_TASK_COMPLETE_TOPIC_NAME, KafkaConstant.SYNC_STOCKOUT_PICKING_TASK_COMPLETE_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), taskId));
        }
    }

    @Transactional
    @JLock(keyConstant = "pickingComplete", lockKey = "#taskId")
    // isAbnormal 是否异常完结
    public void pickingComplete(Integer taskId, Boolean isAbnormal) {
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        if (StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus()))
            return;
        //校验数据
        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.findAllByTaskId(taskId);
        taskItemEntityList.forEach(taskItemEntity -> {
            if (!taskItemEntity.getExpectedQty().equals(taskItemEntity.getPickedQty()) && taskItemEntity.getIsLack().equals(0))
                //自动设置成缺货
                lack(taskItemEntity.getTaskItemId());
        });
        StockoutPickingTaskEntity stockoutPickingTaskEntity = new StockoutPickingTaskEntity();
        stockoutPickingTaskEntity.setTaskId(taskEntity.getTaskId());
        stockoutPickingTaskEntity.setUpdateBy(loginInfoService.getName());
        stockoutPickingTaskEntity.setOperator(loginInfoService.getName());
        stockoutPickingTaskEntity.setOperateEndDate(new Date());
        stockoutPickingTaskEntity.setStatus(isAbnormal ? StockoutPickingTaskStatusEnum.ABNORMAL_TERMINATE.name() : StockoutPickingTaskStatusEnum.PICKED.name());
        stockoutPickingTaskService.updateById(stockoutPickingTaskEntity);

        List<StockoutPickingItemRecordEntity> recordEntityList = stockoutPickingItemRecordService.findAllByTaskId(taskId);
        recordEntityList.stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getInternalBoxCode))
                .entrySet().stream().forEach(entry -> this.fullInternalBox(taskId, entry.getKey()));

        // 加工拣货任务完成 (缺货)
        if (taskEntity.getIsNeedProcess() != null && taskEntity.getIsNeedProcess().equals(1) && taskEntity.getTaskType().equals(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name()))
            processLackPickingComplete(taskEntity);
        else {
            //若当前波次下所有拣货任务都已完成拣货，需要回写波次状态、分拣任务、复核任务的拣货箱数和件数
            sendComoleteMessage(taskEntity.getBatchId(), taskId);
        }
        List<String> boxCodes = recordEntityList.stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(boxCodes) && !Objects.equals(boxCodes.get(0), taskEntity.getPickingBoxCode())) {
            StockoutPickingTaskEntity stockoutPickingTaskEntity1 = new StockoutPickingTaskEntity();
            stockoutPickingTaskEntity1.setTaskId(taskEntity.getTaskId());
            stockoutPickingTaskEntity1.setPickingBoxCode(boxCodes.get(0));
            stockoutPickingTaskService.updateById(stockoutPickingTaskEntity1);
        }
        List<StockoutPickingTaskItemEntity> taskItemList = stockoutPickingTaskItemService.findAllByTaskId(taskId);
        logService.addLog(taskEntity.getTaskId(), StockoutPickingTaskLogTypeEnum.PICKED.getChangeLogType(), String.format("拣货完成，需拣%s件，共拣%s件，缺货%s件，共拣货【%s】箱",
                taskItemList.stream().mapToInt(StockoutPickingTaskItemEntity::getExpectedQty).sum(),
                taskItemList.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum(),
                taskItemList.stream().mapToInt(itemEntity -> Objects.isNull(itemEntity.getLackQty()) ? 0 : itemEntity.getLackQty()).sum(),
                recordEntityList.stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().count()));
        stockoutBatchLogService.addLog(taskEntity.getBatchId(), StockoutBatchLogTypeEnum.PICKED.getStockoutBatchLogType(), String.format("出库单拣货任务【%s】拣货完成", taskEntity.getTaskId()));

        //缺货拣货任务完成后，对应的缺货出库单明细OUT_STOCK CHECKED
        if (StockoutPickingTaskTypeEnum.OUT_STOCK_PICKING.name().equals(taskEntity.getTaskType()))
            stockoutOrderLackItemService.completeByBatchId(taskEntity.getBatchId());
    }

    private void sendComoleteMessage(Integer batchId, Integer taskId) {
        List<StockoutPickingTaskEntity> list = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .eq(StockoutPickingTaskEntity::getBatchId, batchId)
                .notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(),
                        StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
        StockoutBatchEntity stockoutBatchEntity = stockoutBatchService.getById(batchId);
        //存在未完成、未加工完成的拣货任务则return
        if (Objects.isNull(stockoutBatchEntity) || CollectionUtils.isEmpty(list) || list.stream().anyMatch(taskEntity -> !StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())))
            return;
        messageProducer.sendMessage(KafkaConstant.STOCKOUT_BATCH_PICKING_TASK_COMPLETE_TOPIC_NAME, KafkaConstant.STOCKOUT_BATCH_PICKING_TASK_COMPLETE_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), batchId));
        // 异步是否生成增值任务
        List<Integer> stockoutOrderIdList = stockoutPickingTaskService.validAddValue(taskId);
        if (!CollectionUtils.isEmpty(stockoutOrderIdList)) {
            stockoutOrderIdList.forEach(detail -> {
                StockoutVasTaskCreateDTO stockoutVasTaskCreateDTO = new StockoutVasTaskCreateDTO();
                stockoutVasTaskCreateDTO.setStockoutOrderId(detail);
                stockoutVasTaskCreateDTO.setVasTaskCreateEnum(StockoutVasTaskCreateEnum.PICKING_END);
                messageProducer.sendMessage(KafkaConstant.SYNC_VAS_TASK_CREATE_TOPIC_NAME, KafkaConstant.SYNC_VAS_TASK_CREATE_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutVasTaskCreateDTO));
            });
        }
    }

    /**
     * 加工拣货完成
     *
     * @param taskEntity
     */
    private void processLackPickingComplete(StockoutPickingTaskEntity taskEntity) {
        // 加工缺货拣货任务
        //清楚预配
        stockPrematchRemoveService.removeProcessLackByBatchId(taskEntity.getBatchId(), taskEntity.getTaskId());
        ProcessPickingTaskCompleteRequest request = new ProcessPickingTaskCompleteRequest();
        request.setLackPickingTaskId(taskEntity.getTaskId());
        List<StockoutPickingTaskItemEntity> taskItemList = stockoutPickingTaskItemService.findAllByTaskId(taskEntity.getTaskId());
        List<ProcessPickingTaskCompleteItemRequest> requestItemList = taskItemList.stream().map(item -> {
            ProcessPickingTaskCompleteItemRequest requestItem = new ProcessPickingTaskCompleteItemRequest();
            requestItem.setBaseSku(item.getSku());
            requestItem.setPickedQty(item.getPickedQty());
            requestItem.setLack(1 == item.getIsLack());
            requestItem.setLackQty(item.getLackQty());
            return requestItem;
        }).collect(Collectors.toList());
        request.setItemList(requestItemList);
        scmApiService.processLackPickingComplete(request);
    }

    @Transactional
    public void editQty(Integer taskId, StockoutPickingRecordSetRequest request) {
        StockoutPickingTaskValid.validateStockoutPickingRecordSetRequest(request);
        Validator.isValid(request.getQty(), qty -> qty >= 0, "扫描数量需大于等于0");
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        StockoutPickingTaskItemEntity taskItemEntity = stockoutPickingTaskItemService.getById(request.getTaskItemId());
        if (Objects.isNull(taskItemEntity)) {
            throw new BusinessServiceException("未找到对应的拣货任务明细，请确认商品条码!");
        }
        StockoutPickingItemRecordEntity itemRecordEntity = stockoutPickingItemRecordService.findTopByTaskItemIdAndInternalBoxCode(request.getTaskItemId(), request.getInternalBoxCode());
        if (Objects.isNull(itemRecordEntity)) {
            throw new BusinessServiceException("商品未录入，请先录入该sku数据");
        }
        Integer recordPickedQty = itemRecordEntity.getPickedQty();
        int qty = request.getQty() - recordPickedQty;
        if (qty == 0) {
            throw new BusinessServiceException("修改的数量与当前数量相同");
        }
        //如果是加工发货库位，校验可用库存
        if (qty > 0) {
            stockoutPickingTaskValid.validProcessPositionStock(taskItemEntity, qty);
            //1.查询库位库存是否足够，不足则提示盘点  2.查询库存是否被锁定，锁定则抛出异常
            stockLockService.validLockStock(taskItemEntity.getSku(), taskItemEntity.getPositionCode(), qty);

        }
        Integer pickedQty = taskItemEntity.getPickedQty();
        taskItemEntity.setPickedQty(pickedQty + qty);
        if (taskItemEntity.getPickedQty() > taskItemEntity.getExpectedQty()) {
            throw new BusinessServiceException(String.format("已拣货%s件，该箱子中有%s件，其他箱子中有%s件，该箱子最多还可增加%s件，请确认！",
                    pickedQty, recordPickedQty, pickedQty - recordPickedQty, taskItemEntity.getExpectedQty() - pickedQty));
        }
        if (taskItemEntity.getExpectedQty().equals(taskItemEntity.getPickedQty())) {
            taskItemEntity.setIsLack(0);
        } else {
            taskItemEntity.setIsLack(1);
        }
        taskItemEntity.setLackQty(taskItemEntity.getExpectedQty() - taskItemEntity.getPickedQty());
        taskItemEntity.setUpdateBy(loginInfoService.getName());
        stockoutPickingTaskItemService.updateById(taskItemEntity);
        itemRecordEntity.setPickedQty(request.getQty());
        itemRecordEntity.setUpdateBy(loginInfoService.getName());
        stockoutPickingItemRecordService.updateById(itemRecordEntity);

        //更新内部箱和库位库存
        stockoutPickingItemRecordService.editSku(taskEntity.getBatchId(), taskItemEntity, itemRecordEntity, qty);
        //查询库位上是否还有库存，如果有，锁定库存
        if (taskItemEntity.getIsLack().equals(1))
            stockLockService.pickLackLockStock(taskItemEntity.getSku(), taskItemEntity.getPositionCode());
        else
            stockLockService.unlockStock(taskItemEntity.getSku(), taskItemEntity.getPositionCode());
        logService.addLog(taskEntity.getTaskId(), StockoutPickingTaskLogTypeEnum.PICKING_EDIT.getChangeLogType(),
                String.format("箱号【%s】，SKU【%s】预拣货%s件，原拣货%s件，修改拣货为%s件", itemRecordEntity.getInternalBoxCode(),
                        taskItemEntity.getSku(), taskItemEntity.getExpectedQty(), recordPickedQty, itemRecordEntity.getPickedQty()));
    }

    public StockoutPickingTaskEntity getStockoutPickingTaskEntity(Integer taskId) {
        StockoutPickingTaskEntity taskEntity = stockoutPickingTaskService.getById(taskId);
        if (Objects.isNull(taskEntity)) {
            throw new BusinessServiceException("未找到对应的拣货任务!");
        }
        return taskEntity;
    }

    public StockoutPickingTaskConfirmSpaceResponse scanSpaceAreaName(Integer taskId, String spaceAreaName) {
        StockoutPickingTaskEntity taskEntity = getStockoutPickingTaskEntity(taskId);
        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.findAllByTaskIdAndSpaceAreaName(taskId, spaceAreaName);
        if (CollectionUtils.isEmpty(taskItemEntityList))
            throw new BusinessServiceException("未找到对应的拣货任务明细，请确认库区!");
        StockoutBatchEntity stockoutBatch = stockoutBatchService.getStockoutBatchById(taskEntity.getBatchId());
        List<String> skuList1 = taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getSku).collect(Collectors.toList());
        Map<String, List<String>> skuTagMap = stockoutBatch.getWorkspace().contains("FBA") ? tagMappingService.getProductTagBySkus(skuList1) : Collections.emptyMap();
        List<ProductInfoEntity> productInfoList = productInfoService.findByProductIds(taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getProductId).collect(Collectors.toList()));
        Map<Integer, String> packageNameMap = productInfoList.stream().collect(Collectors.toMap(ProductInfoEntity::getProductId, ProductInfoEntity::getPackageVacuum));
        StockoutPickingTaskConfirmSpaceResponse response = new StockoutPickingTaskConfirmSpaceResponse();
        response.setPickingInternalBoxCode(taskEntity.getPickingBoxCode());
        Map<String, StockoutOrderSkuDescription> skuDescription = stockoutOrderItemService.getBaseMapper().getPickingDescription(taskEntity.getBatchId()).stream().collect(Collectors.toMap(StockoutOrderSkuDescription::getSku, stockoutOrderSkuDescription -> stockoutOrderSkuDescription));
        List<StockoutPickingTaskConfirmSpaceAreaSku> skuList = taskItemEntityList.stream().map(taskItemEntity -> {
            StockoutPickingTaskConfirmSpaceAreaSku sku = new StockoutPickingTaskConfirmSpaceAreaSku();
            BeanUtilsEx.copyProperties(taskItemEntity, sku, "taskItemId");
            sku.setTaskItemId(taskItemEntity.getTaskItemId().toString());
            if (taskItemEntity.getExpectedQty().equals(taskItemEntity.getPickedQty())) {
                sku.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
            } else {
                if (taskItemEntity.getIsLack().equals(1)) {
                    sku.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
                } else if (sku.getPickedQty().equals(0)) {
                    sku.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
                } else {
                    sku.setStatus(StockoutPickingTaskStatusEnum.PICKING.name());
                }
            }
            StockoutOrderSkuDescription description = skuDescription.get(taskItemEntity.getSku());
            sku.setDescription(Objects.nonNull(description) ? description.getDescription() : "");
            sku.setIsFirstOrderByStore(Objects.nonNull(description) ? description.getIsFirstOrderByStore() : 0);
            sku.setIsLeadGeneration(Objects.nonNull(description) ? description.getIsLeadGeneration() : null);
            sku.setProductTag(skuTagMap.get(taskItemEntity.getSku()));
            sku.setPackageVacuum(packageNameMap.get(taskItemEntity.getProductId()));
            return sku;
        }).sorted(Comparator.comparing(StockoutPickingTaskConfirmSpaceAreaSku::getSku)).collect(Collectors.toList());

        response.setStockoutPickingTaskConfirmSpaceAreaSkuList(stockoutPickingTaskConfirmBuildService.mergeItemListArea(skuList));
        response.setExpectedQty(taskItemEntityList.stream().mapToInt(StockoutPickingTaskItemEntity::getExpectedQty).sum());
        response.setPickedQty(taskItemEntityList.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum());
        response.setWaitPickQty(response.getExpectedQty() - response.getPickedQty());
        Map<String, List<StockoutPickingItemRecordEntity>> collect = stockoutPickingItemRecordService.list(new QueryWrapper<StockoutPickingItemRecordEntity>().lambda()
                .eq(StockoutPickingItemRecordEntity::getTaskId, taskId)).stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getInternalBoxCode));
        int count = collect.size();
        response.setInternalBoxQty(count);
        response.setTaskId(taskId);
        response.setBatchId(taskEntity.getBatchId());
        return response;
    }

    public List<StockoutPickingTaskItemRecordResponse> spaceAreaNameRecord(Integer taskId, String spaceAreaName) {
        getStockoutPickingTaskEntity(taskId);
        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.findAllByTaskIdAndSpaceAreaName(taskId, spaceAreaName);
        Map<Integer, List<StockoutPickingItemRecordEntity>> collect = stockoutPickingItemRecordService.findAllByTaskItemIdIn(taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getTaskItemId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getTaskItemId));
        List<ProductSpecInfoEntity> specInfoEntityList = productSpecInfoService.findAllBySpecIdIn(taskItemEntityList.stream()
                .map(StockoutPickingTaskItemEntity::getSpecId).collect(Collectors.toList()));
        HashMap<Integer, ProductSpecInfoEntity> specMap = new HashMap<>();
        specInfoEntityList.stream().forEach(productSpecInfoEntity -> specMap.put(productSpecInfoEntity.getSpecId(), productSpecInfoEntity));
        return taskItemEntityList.stream().map(taskItemEntity -> {
            StockoutPickingTaskItemRecordResponse response = new StockoutPickingTaskItemRecordResponse();
            BeanUtilsEx.copyProperties(taskItemEntity, response);
            List<StockoutPickingItemRecordEntity> recordEntityList = collect.get(taskItemEntity.getTaskItemId());
            if (!CollectionUtils.isEmpty(recordEntityList)) {
                List<StockoutOrderTaskItemDetail> detailList = recordEntityList.stream().map(recordEntity -> {
                    StockoutOrderTaskItemDetail detail = new StockoutOrderTaskItemDetail();
                    detail.setBarcode(recordEntity.getBarcode());
                    detail.setInternalBoxCode(recordEntity.getInternalBoxCode());
                    detail.setQty(recordEntity.getPickedQty());
                    detail.setSku(recordEntity.getSku());
                    return detail;
                }).collect(Collectors.toList());
                response.setStockoutPickingTaskItemDetailList(detailList);
            } else {
                response.setStockoutPickingTaskItemDetailList(new ArrayList<>());
            }
            ProductSpecInfoEntity specInfoEntity = specMap.get(taskItemEntity.getSpecId());
            if (Objects.nonNull(specInfoEntity)) {
                response.setImageUrl(specInfoEntity.getImageUrl());
                response.setPreviewImageUrl(specInfoEntity.getPreviewImageUrl());
                response.setThumbnailImageUrl(specInfoEntity.getThumbnailImageUrl());
            }
            if (taskItemEntity.getExpectedQty().equals(taskItemEntity.getPickedQty())) {
                response.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
            } else {
                if (taskItemEntity.getIsLack().equals(1)) {
                    response.setStatus(StockoutPickingTaskStatusEnum.PICKED.name());
                } else if (response.getPickedQty().equals(0)) {
                    response.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
                } else {
                    response.setStatus(StockoutPickingTaskStatusEnum.PICKING.name());
                }
            }
            return response;
        }).collect(Collectors.toList());
    }

    @Transactional
    public void spaceAreaNameConfirm(Integer taskId, String spaceAreaName) {
        StockoutPickingTaskEntity task = getStockoutPickingTaskEntity(taskId);
        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.findAllByTaskIdAndSpaceAreaName(taskId, spaceAreaName);
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            throw new BusinessServiceException("未找到对应的拣货任务明细，请确认库区!");
        }
        taskItemEntityList.stream().forEach(taskItemEntity -> {
            if (!taskItemEntity.getExpectedQty().equals(taskItemEntity.getPickedQty()) && taskItemEntity.getIsLack().equals(0)) {
                //自动设置成缺货
                lack(taskItemEntity.getTaskItemId());
            }
        });
        List<StockoutPickingItemRecordEntity> recordEntityList = stockoutPickingItemRecordService.findAllByTaskIdAndSpaceAreaName(taskId, spaceAreaName);
        List<StockoutPickingTaskItemEntity> taskItemEntityListNew = stockoutPickingTaskItemService.findAllByTaskIdAndSpaceAreaName(taskId, spaceAreaName);
        List<String> boxCodes = recordEntityList.stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(boxCodes) && !Objects.equals(boxCodes.get(0), task.getPickingBoxCode())) {
            task.setPickingBoxCode(boxCodes.get(0));
            stockoutPickingTaskService.updateById(task);
        }
        logService.addLog(taskId, StockoutPickingTaskLogTypeEnum.PICKED.getChangeLogType(),
                String.format("【%s】库区拣货完成，需拣%s件，共拣%s件，缺货%s件，共拣货【%s】箱",
                        spaceAreaName,
                        taskItemEntityListNew.stream().mapToInt(StockoutPickingTaskItemEntity::getExpectedQty).sum(),
                        taskItemEntityListNew.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum(),
                        taskItemEntityListNew.stream().mapToInt(itemEntity -> Objects.isNull(itemEntity.getLackQty()) ? 0 : itemEntity.getLackQty()).sum(),
                        CollectionUtils.isEmpty(recordEntityList) ? 0 : boxCodes.size()));
    }

    public StockoutPickingTaskResponse getPickingTaskByBatchId(Integer batchId, String spaceAreaName, StockoutPickingTaskBatchRequest request) {
        List<StockoutPickingTaskEntity> list = stockoutPickingTaskService.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().eq(StockoutPickingTaskEntity::getBatchId, batchId)
                .orderByDesc(StockoutPickingTaskEntity::getIsNeedProcess));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("该波次号未找到拣货任务!");
        }
        StockoutPickingTaskResponse response = new StockoutPickingTaskResponse();
        List<Integer> taskIds = list.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        Map<Integer, List<StockoutPickingTaskItemEntity>> collect = stockoutPickingTaskItemService.findAllByTaskIdList(taskIds)
                .stream().collect(Collectors.groupingBy(StockoutPickingTaskItemEntity::getTaskId));
        Map<Integer, List<StockoutPickingItemRecordEntity>> recodeMap = stockoutPickingItemRecordService.findAllByTaskIdIn(taskIds).stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getTaskId));
        Map<String, String> stockoutPickingTaskStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TASK_STATUS.getName());
        List<StockoutBatchPickingTask> taskList = list.stream().map(entity -> {
            List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList = collect.get(entity.getTaskId());
            List<StockoutPickingItemRecordEntity> recordEntityList = recodeMap.get(entity.getTaskId());
            StockoutBatchPickingTask task = new StockoutBatchPickingTask();
            BeanUtilsEx.copyProperties(entity, task);
            if (!CollectionUtils.isEmpty(recordEntityList)) {
                task.setInternalBoxCode(recordEntityList.stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().collect(Collectors.joining(",")));
                task.setPickedQty(recordEntityList.stream().mapToInt(StockoutPickingItemRecordEntity::getPickedQty).sum());
            } else {
                task.setPickedQty(0);
            }
            task.setSpaceAreaNameList(pickingTaskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getSpaceAreaName).distinct().collect(Collectors.toList()));
            task.setSpaceAreaNameIdList(pickingTaskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getSpaceAreaId).distinct().collect(Collectors.toList()));
            task.setSpaceAreaName(String.join(",", task.getSpaceAreaNameList()));
            task.setStatusStr(stockoutPickingTaskStatusEnumMap.get(entity.getStatus()));
            task.setIsLack(pickingTaskItemEntityList.stream().anyMatch(item -> item.getIsLack().equals(1)) ? 1 : 0);
            return task;
        }).collect(Collectors.toList());
        taskList.stream().sorted(Comparator.comparing(task -> {
            String status = task.getStatus();
            return StockoutPickingTaskStatusEnum.PICKING.name().equals(status) ? 0 : StockoutPickingTaskStatusEnum.WAIT_PICK.name().equals(status) ? 1 : 2;
        }));
        StockoutBatchEntity stockoutBatchEntity = stockoutBatchService.getById(batchId);
        response.setShowSpaceArea(!StockoutPickingTypeEnum.WHOLE_PICK.name().equals(stockoutBatchEntity.getPickingType()));
        buildResponseTask(response, taskList, spaceAreaName);
        response.setBatchId(batchId);
        response.setTaskQty(response.getTaskList().size());
        if (Objects.nonNull(request) && StringUtils.hasText(request.getTaskType())) {
            response.setTaskList(response.getTaskList().stream().filter(item -> item.getTaskType().equals(request.getTaskType())).collect(Collectors.toList()));
            response.setPickedQty(response.getTaskList().stream().mapToInt(StockoutBatchPickingTask::getPickedQty).sum());
            response.setExpectedQty(response.getTaskList().stream().mapToInt(StockoutBatchPickingTask::getExpectedQty).sum());
        } else {
            response.setPickedQty(response.getTaskList().stream().mapToInt(StockoutBatchPickingTask::getPickedQty).sum());
            response.setExpectedQty(response.getTaskList().stream().mapToInt(StockoutBatchPickingTask::getExpectedQty).sum());
        }
        response.setWaitPickQty(response.getExpectedQty() - response.getPickedQty());
        return response;
    }

    private void buildResponseTask(StockoutPickingTaskResponse response, List<StockoutBatchPickingTask> taskList, String spaceAreaName) {
        if (!StringUtils.hasText(spaceAreaName) || "ALL".equalsIgnoreCase(spaceAreaName)) {
            response.setTaskList(taskList);
            return;
        }
        if ("ZS".equalsIgnoreCase(spaceAreaName)) {
            response.setTaskList(taskList.stream().filter(task -> task.getSpaceAreaNameList().stream().anyMatch(it -> it.startsWith("ZS"))).collect(Collectors.toList()));
            return;
        }
        if ("ERP".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("erp");
            response.setTaskList(taskList.stream().filter(task -> task.getSpaceAreaNameIdList().stream().anyMatch(sets::contains)).collect(Collectors.toList()));
            return;
        }
        if ("ACTIVITY".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("活动");
            response.setTaskList(taskList.stream().filter(task -> task.getSpaceAreaNameIdList().stream().anyMatch(sets::contains)).collect(Collectors.toList()));
            return;
        }
        if ("OEM".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("OEM");
            response.setTaskList(taskList.stream().filter(task -> task.getSpaceAreaNameIdList().stream().anyMatch(sets::contains)).collect(Collectors.toList()));
            return;
        }
        if ("OTHER".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("erp");
            sets.addAll(buildErpSpaceAreaName("OEM"));
            sets.addAll(buildErpSpaceAreaName("活动"));
            response.setTaskList(taskList.stream().filter(task -> task.getSpaceAreaNameList().stream().anyMatch(re ->
                    !re.startsWith("ZS") && !"G".equalsIgnoreCase(re)) && task.getSpaceAreaNameIdList().stream().anyMatch(it -> !sets.contains(it))).collect(Collectors.toList()));
            return;
        }
        // 如果都匹配不上，直接过滤,例如G区
        response.setTaskList(taskList.stream().filter(task -> task.getSpaceAreaNameList().stream()
                .anyMatch(spaceAreaName::equalsIgnoreCase)).collect(Collectors.toList()));
    }

    // 根据区域名称 获取对应的库区(过滤ZS自建仓)
    private Set<Integer> buildErpSpaceAreaName(String areaNameLike) {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<BdAreaEntity>()
                .like(BdAreaEntity::getAreaName, "%" + areaNameLike + "%").eq(BdAreaEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<BdAreaEntity> list1 = areaService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list1))
            throw new BusinessServiceException("未找到该区域：" + areaNameLike);
        LambdaQueryWrapper<BdSpaceAreaEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BdSpaceAreaEntity::getAreaId, list1.stream().map(BdAreaEntity::getAreaId).collect(Collectors.toList()));
        return spaceAreaService.list(wrapper).stream().filter(it -> !it.getSpaceAreaName().startsWith("ZS")).map(BdSpaceAreaEntity::getSpaceAreaId).collect(Collectors.toSet());
    }

    /**
     * 查看拣货中、待拣货、待分拣的全部批次
     *
     * @return
     */
    public List<StockoutAllBatchPickingTaskResponse> getPickingAllBatch(String spaceAreaName) {
        List<StockoutAllBatchPickingTaskResponse> responses = stockoutBatchService.getBaseMapper().getPickingAllBatch();
        if (CollectionUtils.isEmpty(responses))
            return responses;
        Map<String, String> stockoutWaveTaskStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WAVE_TASK_STATUS.getName());
        responses.forEach(response -> {
            response.setStatusStr(stockoutWaveTaskStatusEnumMap.get(response.getStatus()));
            if (StringUtils.hasText(response.getSpaceAreaNameString()))
                response.setSpaceAreaNameList(Arrays.asList(response.getSpaceAreaNameString().split(",")));
            if (StringUtils.hasText(response.getSpaceAreaIdString()))
                response.setSpaceAreaIdList(Arrays.stream(response.getSpaceAreaIdString().split(",")).filter(StringUtils::hasText).map(Integer::valueOf).collect(Collectors.toList()));
        });
        if (!StringUtils.hasText(spaceAreaName) || "ALL".equalsIgnoreCase(spaceAreaName))
            return responses;
        if ("ZS".equalsIgnoreCase(spaceAreaName))
            return responses.stream().filter(resp -> resp.getSpaceAreaNameList().stream().anyMatch(re -> re.startsWith("ZS"))).collect(Collectors.toList());
        if ("ERP".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("erp");
            return responses.stream().filter(resp -> resp.getSpaceAreaIdList().stream().anyMatch(sets::contains)).collect(Collectors.toList());
        }
        if ("ACTIVITY".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("活动");
            return responses.stream().filter(resp -> resp.getSpaceAreaIdList().stream().anyMatch(sets::contains)).collect(Collectors.toList());
        }
        if ("OEM".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("OEM");
            return responses.stream().filter(resp -> resp.getSpaceAreaIdList().stream().anyMatch(sets::contains)).collect(Collectors.toList());
        }
        if ("OTHER".equalsIgnoreCase(spaceAreaName)) {
            Set<Integer> sets = buildErpSpaceAreaName("erp");
            sets.addAll(buildErpSpaceAreaName("OEM"));
            sets.addAll(buildErpSpaceAreaName("活动"));
            List<StockoutAllBatchPickingTaskResponse> responseList = new ArrayList<>();
            responses.forEach(resp -> {
                resp.getSpaceAreaIdList().removeAll(sets);
                if (CollectionUtils.isEmpty(resp.getSpaceAreaIdList()))
                    return;
                List<String> collect = spaceAreaService.listByIds(resp.getSpaceAreaIdList()).stream().map(BdSpaceAreaEntity::getSpaceAreaName).collect(Collectors.toList());
                if (collect.stream().anyMatch(re -> !re.startsWith("ZS") && !"G".equalsIgnoreCase(re)))
                    responseList.add(resp);
            });
            return responseList;
        }
        return responses.stream().filter(resp -> resp.getSpaceAreaNameList().stream().anyMatch(spaceAreaName::equalsIgnoreCase)).collect(Collectors.toList());
    }
}
