package com.nsy.api.wms.domain.stockout;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

@ApiModel(value = "StockoutCustomsDeclareDocumentAggregatedItemResult", description = "报关单据明细")
public class StockoutCustomsDeclareDocumentAggregatedItemResult {

    @ApiModelProperty(value = "item主键", name = "declareDocumentAggregatedItemId")
    private Integer declareDocumentAggregatedItemId;

    @ApiModelProperty(value = "项号", name = "gNo")
    private Integer gNo;

    @ApiModelProperty(value = "成分", name = "fabricType")
    private String fabricType;

    @ApiModelProperty(value = "制作方式", name = "spinType")
    private String spinType;

    @ApiModelProperty(value = "报关名称", name = "customsDeclareCn")
    private String customsDeclareCn;

    @ApiModelProperty(value = "报关英文名称", name = "customsDeclareEn")
    private String customsDeclareEn;

    @ApiModelProperty(value = "海关编码", name = "hsCode")
    private String hsCode;

    @ApiModelProperty(value = "FOB单价", name = "FobPrice")
    private BigDecimal fobPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "数量", name = "qty")
    private Integer qty = 0;

    @ApiModelProperty(value = "分摊运费", name = "apportionFreight")
    private BigDecimal apportionFreight;

    @ApiModelProperty(value = "单位（中文）", name = "customsDeclareUnitCn")
    private String customsDeclareUnitCn;

    @ApiModelProperty(value = "单位", name = "customsDeclareUnit")
    private String customsDeclareUnit;

    @ApiModelProperty(value = "实际箱数", name = "actualBoxQty")
    private Integer actualBoxQty = 0;

    @ApiModelProperty(value = "净重", name = "netWeight")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "毛重", name = "roughWeight")
    private BigDecimal roughWeight;

    @ApiModelProperty(value = "单件重量", name = "weight")
    private BigDecimal weight = BigDecimal.ZERO;

    @ApiModelProperty(value = "体积", name = "volume")
    private BigDecimal volume;

    @ApiModelProperty(value = "实际毛重", name = "actualRoughWeight")
    private BigDecimal actualRoughWeight = BigDecimal.ZERO;

    @ApiModelProperty(value = "规格类型", name = "specType")
    private String specType;

    @ApiModelProperty(value = "实际净重", name = "actualNetWeight")
    private BigDecimal actualNetWeight = BigDecimal.ZERO;

    @JsonIgnore
    private BigDecimal boxWeight;

    @JsonIgnore
    private Integer customsDeclareQty = 1;

    @JsonIgnore
    private Integer categoryCustomsDeclareId;

    @JsonIgnore
    private String polymerizedDeclareElement;

    //货源省份
    @ApiModelProperty(value = "货源省份", name = "originProvince")
    private String originProvince;

    //货源城市
    @ApiModelProperty(value = "货源城市", name = "originCity")
    private String originCity;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    private Integer declareDocumentId;

    private Date signingDate;

    public Integer getDeclareDocumentId() {
        return declareDocumentId;
    }

    public void setDeclareDocumentId(Integer declareDocumentId) {
        this.declareDocumentId = declareDocumentId;
    }

    @ApiModelProperty(value = "计划箱数", name = "planBoxQty")
    public Integer getPlanBoxQty() {
        return weight.multiply(BigDecimal.valueOf(qty)).divide(BigDecimal.valueOf(21), 0, RoundingMode.HALF_UP).intValue();
    }

    @ApiModelProperty(value = "fob总价", name = "fobTotalPrice")
    public BigDecimal getFobTotalPrice() {
        return fobPrice.multiply(BigDecimal.valueOf(qty)).setScale(2, RoundingMode.HALF_UP);
    }

    @ApiModelProperty(value = "C&F价格, 精度为2位小数", name = "CAndFPrice")
    public BigDecimal getCAndFPrice() {
        return getFobTotalPrice().add(apportionFreight).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * freemarker使用，cAndFPrice识别不了
     */
    @JsonIgnore
    public BigDecimal getCnfPrice() {
        return getFobTotalPrice().add(apportionFreight).setScale(2, RoundingMode.HALF_UP);
    }

    @ApiModelProperty(value = "计划重", name = "planWeight")
    public BigDecimal getPlanWeight() {
        return weight.multiply(BigDecimal.valueOf(qty));
    }


    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public String getSpinType() {
        return spinType;
    }

    public void setSpinType(String spinType) {
        this.spinType = spinType;
    }

    public String getCustomsDeclareCn() {
        return customsDeclareCn;
    }

    public void setCustomsDeclareCn(String customsDeclareCn) {
        this.customsDeclareCn = customsDeclareCn;
    }

    public String getCustomsDeclareEn() {
        return customsDeclareEn;
    }

    public void setCustomsDeclareEn(String customsDeclareEn) {
        this.customsDeclareEn = customsDeclareEn;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public BigDecimal getFobPrice() {
        return fobPrice;
    }

    public void setFobPrice(BigDecimal fobPrice) {
        this.fobPrice = fobPrice;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }


    public BigDecimal getApportionFreight() {
        return apportionFreight;
    }

    public void setApportionFreight(BigDecimal apportionFreight) {
        this.apportionFreight = apportionFreight;
    }


    public String getCustomsDeclareUnitCn() {
        return customsDeclareUnitCn;
    }

    public void setCustomsDeclareUnitCn(String customsDeclareUnitCn) {
        this.customsDeclareUnitCn = customsDeclareUnitCn;
    }

    public String getCustomsDeclareUnit() {
        return customsDeclareUnit;
    }

    public void setCustomsDeclareUnit(String customsDeclareUnit) {
        this.customsDeclareUnit = customsDeclareUnit;
    }

    public Integer getActualBoxQty() {
        return actualBoxQty;
    }

    public void setActualBoxQty(Integer actualBoxQty) {
        this.actualBoxQty = actualBoxQty;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public String getSpecType() {
        return specType;
    }

    public void setSpecType(String specType) {
        this.specType = specType;
    }

    public BigDecimal getActualRoughWeight() {
        return actualRoughWeight;
    }

    public void setActualRoughWeight(BigDecimal actualRoughWeight) {
        this.actualRoughWeight = actualRoughWeight;
    }

    public BigDecimal getActualNetWeight() {
        return actualNetWeight;
    }

    public void setActualNetWeight(BigDecimal actualNetWeight) {
        this.actualNetWeight = actualNetWeight;
    }

    public void setBoxWeight(BigDecimal boxWeight) {
        this.boxWeight = boxWeight;
    }

    public BigDecimal getBoxWeight() {
        return boxWeight;
    }

    public Integer getCustomsDeclareQty() {
        return customsDeclareQty;
    }

    public void setCustomsDeclareQty(Integer customsDeclareQty) {
        this.customsDeclareQty = customsDeclareQty;
    }

    public Integer getCategoryCustomsDeclareId() {
        return categoryCustomsDeclareId;
    }

    public void setCategoryCustomsDeclareId(Integer categoryCustomsDeclareId) {
        this.categoryCustomsDeclareId = categoryCustomsDeclareId;
    }

    public BigDecimal getRoughWeight() {
        return roughWeight;
    }

    public void setRoughWeight(BigDecimal roughWeight) {
        this.roughWeight = roughWeight;
    }

    public String getPolymerizedDeclareElement() {
        return polymerizedDeclareElement;
    }

    public void setPolymerizedDeclareElement(String polymerizedDeclareElement) {
        this.polymerizedDeclareElement = polymerizedDeclareElement;
    }

    public String getOriginProvince() {
        return originProvince;
    }

    public void setOriginProvince(String originProvince) {
        this.originProvince = originProvince;
    }

    public String getOriginCity() {
        return originCity;
    }

    public void setOriginCity(String originCity) {
        this.originCity = originCity;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }


    public Integer getgNo() {
        return gNo;
    }

    public void setgNo(Integer gNo) {
        this.gNo = gNo;
    }

    public Integer getDeclareDocumentAggregatedItemId() {
        return declareDocumentAggregatedItemId;
    }

    public void setDeclareDocumentAggregatedItemId(Integer declareDocumentAggregatedItemId) {
        this.declareDocumentAggregatedItemId = declareDocumentAggregatedItemId;
    }

    public Date getSigningDate() {
        return signingDate;
    }

    public void setSigningDate(Date signingDate) {
        this.signingDate = signingDate;
    }
}
