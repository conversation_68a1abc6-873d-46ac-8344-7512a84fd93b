package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ExceptionConstants;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.DeliverySkuCheckView;
import com.nsy.api.wms.domain.stockin.ReceiveSkuBoxView;
import com.nsy.api.wms.domain.stockin.ReceiveSkuView;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItemDetail;
import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleStockinStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.enumeration.stockout.VacuumEnum;
import com.nsy.api.wms.request.stock.StockinInternalBoxFullRequest;
import com.nsy.api.wms.request.stockin.AddReceiveSkuQtyRequest;
import com.nsy.api.wms.request.stockin.ModifySkuReceiveQtyRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSetRequest;
import com.nsy.api.wms.response.stockin.AddReceiveResponse;
import com.nsy.api.wms.response.stockin.AddReceiveSkuQtyResponse;
import com.nsy.api.wms.response.stockin.DeliverySkuCheckListResponse;
import com.nsy.api.wms.response.stockin.GetReceiveSkuInfoBoxListResponse;
import com.nsy.api.wms.response.stockin.GetReceiveSkuListResponse;
import com.nsy.api.wms.response.stockin.ReceiveResponse;
import com.nsy.api.wms.response.stockin.ScanInSkuAreaResponse;
import com.nsy.api.wms.response.stockin.ScanInStorageBarcodeResponse;
import com.nsy.api.wms.response.stockin.StockinReceiveSkuListResponse;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.qa.BdQaSopRuleService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleLogService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinSpotInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinOrderTaskPDAService {
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockPlatformScheduleService platformScheduleService;
    @Autowired
    StockPlatformScheduleItemService platformScheduleItemService;
    @Autowired
    ErpApiService erpApiService;
    @Inject
    ProductSpecInfoService productSpecInfoService;
    @Inject
    ProductInfoService productInfoService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinScanLogService stockinScanLogService;
    @Inject
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinReturnProductService stockinReturnProductService;
    @Autowired
    StockInternalBoxMapper stockInternalBoxMapper;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinSpotInfoService stockinSpotInfoService;
    @Autowired
    StockPlatformScheduleLogService platformSchedulerLogService;
    @Autowired
    StockinOrderTaskBuildService stockinOrderTaskBuildService;
    @Autowired
    StockinOrderTaskReceiveService stockinOrderTaskReceiveService;
    @Autowired
    OmsApiService omsApiService;
    @Resource
    BdTagMappingService tagMappingService;
    @Autowired
    StockinDeliveryPdaService stockinDeliveryPdaService;
    @Resource
    StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    private BdQaSopRuleService bdQaSopRuleService;

    /**
     * 扫描箱号生成接收单
     *
     * @param supplierDeliveryBoxCode 支持出库箱码或工厂出库单
     * @return
     */
    @JLock(keyConstant = "getBySupplierDeliveryBoxCode", lockKey = "#supplierDeliveryBoxCode")
    public AddReceiveResponse addReceiveBySupplierDeliveryBoxNo(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null)
            //支持工厂出库单号
            return stockinDeliveryPdaService.addReceiveBySupplierDeliveryNo(supplierDeliveryBoxCode);
        if (StockinTypeEnum.SPOT.name().equals(entity.getStockinType()))
            throw new BusinessServiceException("现货商品请使用电脑操作！");
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId());
        if (CollectionUtils.isEmpty(itemEntityList)) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK_ITEM);
        }
        AddReceiveResponse addReceiveResponse = new AddReceiveResponse();
        String noticeMessage = stockinOrderTaskService.getSupplierNameTotice(entity);
        Long isFbaQuick = itemEntityList.stream().filter(item -> item.getIsFbaQuick().equals(1)).count();
        if (isFbaQuick > 0) {
            noticeMessage = noticeMessage + "，属于快进快出";
        }
        addReceiveResponse.setCheckInfo(noticeMessage);
        addReceiveResponse.setDeliveryLocation(entity.getLocation());
        addReceiveResponse.setStatus(StockinOrderTaskStatusEnum.valueOf(entity.getStatus()).getStatus());
        addReceiveResponse.setTaskId(entity.getTaskId());

        //已经收获的允许扫描
        if (!StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus())) {
            //生成入库单
            StockinOrderEntity orderEntity = stockinOrderService.create(entity, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);
            //生成扫描日志
            stockinScanLogService.addScanLog(StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_CONTENT_RECEIVE, entity, orderEntity, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_START_RECEIVE);
            //更新出库任务状态
            entity.setOperator(loginInfoService.getName());
            entity.setOperateStartDate(new Date());
            entity.setStatus(StockinOrderTaskStatusEnum.RECEIVING.name());
            entity.setUpdateBy(loginInfoService.getName());
            stockinOrderTaskService.updateById(entity);
            platformScheduleService.updateStockinStatus(entity.getPlatformScheduleId(), StockPlatformScheduleStockinStatusEnum.INBOUNDING);
            platformSchedulerLogService.addLog(entity.getPlatformScheduleId(), StockPlatformScheduleLogTypeEnum.START_RECEIVING.getLogType(), String.format("月台下的出库箱码【%s】开始收货，生成入库单【%s】", entity.getSupplierDeliveryBoxCode(), orderEntity.getStockinOrderNo()));
        }
        stockinOrderTaskBuildService.buildAddReceiveResponse(addReceiveResponse, itemEntityList);
        return addReceiveResponse;
    }

    public DeliverySkuCheckListResponse getDeliverySkuCheckList(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        }
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId())
            .stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku)).entrySet().stream().map(entry -> {
                StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
                List<StockinOrderTaskItemEntity> value = entry.getValue();
                BeanUtilsEx.copyProperties(value.get(0), taskItemEntity);
                taskItemEntity.setIsNeedQa(bdQaSopRuleService.existSpotSopRule() ? 1 : 0);
                taskItemEntity.setQaQty(value.stream().mapToInt(StockinOrderTaskItemEntity::getQaQty).sum());
                taskItemEntity.setExpectedQty(value.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
                return taskItemEntity;
            }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemEntityList)) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK_ITEM);
        }
        DeliverySkuCheckListResponse deliverySkuCheckListResponse = new DeliverySkuCheckListResponse();
        List<DeliverySkuCheckView> deliverySkuCheckViewList = itemEntityList.stream().map(itemEntity -> {
            DeliverySkuCheckView deliverySkuCheckView = new DeliverySkuCheckView();
            deliverySkuCheckView.setQty(itemEntity.getExpectedQty());
            deliverySkuCheckView.setCheck(itemEntity.getIsNeedQa().equals(1));
            deliverySkuCheckView.setSku(String.format("%s%s", deliverySkuCheckView.getCheck() ? "(检)" : "", itemEntity.getSku()));
            return deliverySkuCheckView;
        }).collect(Collectors.toList());
        deliverySkuCheckListResponse.setDeliverySkuCheckViewList(deliverySkuCheckViewList);
        deliverySkuCheckListResponse.setCheckInfo(getCheckInfo(entity, itemEntityList));
        deliverySkuCheckListResponse.setSupplierDeliveryBoxNo(supplierDeliveryBoxCode);
        return deliverySkuCheckListResponse;
    }

    private String getCheckInfo(StockinOrderTaskEntity entity, List<StockinOrderTaskItemEntity> itemEntityList) {
        StockPlatformScheduleEntity platformScheduleEntity = platformScheduleService.getById(entity.getPlatformScheduleId());
        List<ProductSpecInfoEntity> specInfoEntityList = productSpecInfoService.findAllBySpecIdIn(itemEntityList.stream()
            .map(StockinOrderTaskItemEntity::getSpecId).collect(Collectors.toList()));
        HashMap<Integer, ProductSpecInfoEntity> specMap = new HashMap<>();
        specInfoEntityList.stream().forEach(productSpecInfoEntity -> specMap.put(productSpecInfoEntity.getSpecId(), productSpecInfoEntity));
        int checkSkuQty = 0;
        int checkQty = 0;
        int unCheckSkuQty = 0;
        int uncheckQty = 0;
        int weightSkuQty = 0;
        for (StockinOrderTaskItemEntity itemEntity : itemEntityList) {
            if (itemEntity.getIsNeedQa().equals(1)) {
                checkSkuQty++;
            }
            checkQty += itemEntity.getQaQty();
            uncheckQty += itemEntity.getExpectedQty() - itemEntity.getQaQty();

            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(itemEntity.getSpecId());
            if (Objects.nonNull(productSpecInfoEntity) && Objects.isNull(productSpecInfoEntity.getActualWeight()) || productSpecInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0) {
                weightSkuQty++;
            }
        }
        StringBuffer stringBuffer = new StringBuffer(String.format("供应商属于%s", platformScheduleEntity.getSupplierName()));
        if (unCheckSkuQty == 0) {
            stringBuffer.append(",且该箱Sku全部需要质检！");
        } else if (checkSkuQty != 0) {
            stringBuffer.append(String.format(",质检%s款，%s件；盘点%s款，%s件！", checkSkuQty, checkQty, unCheckSkuQty, uncheckQty));
        }
        if (weightSkuQty > 0) {
            stringBuffer.append(String.format(",称重%s款", weightSkuQty));
        }
        return stringBuffer.toString();
    }

    //支持出库箱码或工厂出库单
    public ScanInStorageBarcodeResponse getReceiveSkuInfo(String supplierDeliveryBoxCode, String barcode) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null)
            //支持工厂出库单号
            return stockinDeliveryPdaService.getReceiveSkuInfo(supplierDeliveryBoxCode, barcode);
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(barcode, Collections.singletonList(entity.getTaskId()));
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSpecId(entity.getTaskId(), productSpecInfoEntity.getSpecId());
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            return buildScanInStorageBarcodeResponse(entity, productSpecInfoEntity);
        }
        return getScanInStorageBarcodeResponse(entity, productSpecInfoEntity, taskItemEntityList);
    }

    @NotNull
    public ScanInStorageBarcodeResponse getScanInStorageBarcodeResponse(StockinOrderTaskEntity entity, ProductSpecInfoEntity productSpecInfoEntity, List<StockinOrderTaskItemEntity> taskItemEntityList) {
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            throw new BusinessServiceException("该入库任务明细不存在");
        }

        ScanInStorageBarcodeResponse response = new ScanInStorageBarcodeResponse();
        response.setShowNotice(Boolean.FALSE);
        response.setSupplierDeliveryBoxNo(entity.getSupplierDeliveryBoxCode());
        response.setDeliveryQty(taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
        response.setIsPreQaReturn(taskItemEntityList.get(0).getIsPreQaReturn());
        response.setPreStatus(stockinOrderTaskItemService.getPreStatus(taskItemEntityList.get(0)));
        response.setImage(productSpecInfoEntity.getImageUrl(), productSpecInfoEntity.getThumbnailImageUrl(), productSpecInfoEntity.getPreviewImageUrl());
        List<String> promptInformation = getPromptInformation(taskItemEntityList.get(0).getTaskId(), productSpecInfoEntity.getSku(), response);
        response.setNoticeMessage(StringUtils.join(promptInformation, '\n'));
        response.setCheck(taskItemEntityList.stream().anyMatch(taskItemEntity -> taskItemEntity.getIsNeedQa().equals(1)));
        response.setSku(taskItemEntityList.get(0).getSku());
        response.setReceiveQty(taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getStockinQty).sum());
        response.setBarcode(taskItemEntityList.get(0).getBarcode());
        response.setPackageName(taskItemEntityList.get(0).getPackageName());
        String labelAttributeNames = getLabelAttributeNames(taskItemEntityList);
/*        if (StringConstant.FIRST_ORDER_LABEL.equals(labelAttributeNames)) {
            response.setShelveSpaceAreaName(null);
        }*/
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames())
            && item.getLabelAttributeNames().contains(StringConstant.FIRST_ORDER_LABEL_OEM)))
            response.setFirstLabel(StringConstant.FIRST_ORDER_LABEL_OEM);
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames())
            && item.getLabelAttributeNames().contains(StringConstant.FIRST_ORDER_LABEL_ODM)))
            response.setFirstLabel(StringConstant.FIRST_ORDER_LABEL_ODM);
        // 品牌
        if (taskItemEntityList.get(0).getStoreId() != null && taskItemEntityList.get(0).getIsFbaQuick() != null && taskItemEntityList.get(0).getIsFbaQuick().equals(1)) {
            List<SaStorePageInfoResponse> storeItems = omsApiService.getAllStoreInfo();
            storeItems.stream().filter(s -> s.getId().equals(taskItemEntityList.get(0).getStoreId())).findFirst().ifPresent(curItem -> {
                response.setStoreName(curItem.getStoreName());
                response.setStorePreFix(curItem.getExtendValue());
            });
        }
        response.setBrandName(taskItemEntityList.get(0).getBrandName());
        response.setLabelAttributeNames(labelAttributeNames);
        response.setProductTag(tagMappingService.getProductTagBySkus(Collections.singletonList(productSpecInfoEntity.getSku())).get(productSpecInfoEntity.getSku()));
        return response;
    }

    public List<String> getPromptInformation(Integer taskId, String sku, ScanInStorageBarcodeResponse response) {
        List<String> strings = new LinkedList<>();
        StockinOrderTaskEntity taskEntity = stockinOrderTaskService.getById(taskId);
        if (Objects.isNull(taskEntity))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(sku);
        ProductInfoEntity productInfoEntity = productInfoService.findTopByProductId(productSpecInfoEntity.getProductId());
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSpecId(taskId, productSpecInfoEntity.getSpecId());
        if (CollectionUtils.isEmpty(itemEntityList)) {
            strings.add("该商品不属于该工厂出库单");
            return strings;
        }
        StockinOrderTaskItemEntity itemEntity = itemEntityList.get(0);
        StockPlatformScheduleEntity platformScheduleEntity = platformScheduleService.getById(taskEntity.getPlatformScheduleId());
        boolean vacuumFlag = itemEntityList.stream().anyMatch(taskItem -> StockConstant.ENABLE.equals(taskItem.getVacuumFlag()));
        response.setPackageVacuum(vacuumFlag ? productInfoEntity.getPackageVacuum() : "");
        response.setVacuumCn(vacuumFlag ? VacuumEnum.getPcNoticeByKey(productInfoEntity.getPackageVacuum()) : "");
        String message1 = itemEntityList.stream().anyMatch(taskItemEntity -> taskItemEntity.getIsNeedQa().equals(1)) ? "商品需要质检" : "商品不需要质检";
        strings.add(message1.concat(String.format(",到货属于%s到货", platformScheduleEntity.getSupplierName())));
        stockinOrderTaskItemService.recommendShelveSpaceArea(taskEntity, itemEntity.getSpaceId(), itemEntity);

        setAreaName(response, itemEntityList);
        //OEM及活动仓才需要显示
        List<String> collect = itemEntityList.stream().filter(item -> SpaceAreaMapConstant.WmsArea.ACTIVITY_AREA.equals(item.getAreaName())
                || SpaceAreaMapConstant.WmsArea.OEM_AREA.equals(item.getAreaName()))
            .map(StockinOrderTaskItemEntity::getPurchaseApplyInfo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<String> orderList = itemEntityList.stream().map(StockinOrderTaskItemEntity::getOrderNo).distinct().filter(item -> StringUtils.hasText(item)).collect(Collectors.toList());
            response.setOrderNotice(!CollectionUtils.isEmpty(orderList) ? "订单号：" + StringUtils.join(orderList, ',') : "");
            response.setPurchaseApplyInfo(StringUtils.join(collect, ';'));
            response.setInfo(platformScheduleEntity.getSupplierName() + ";" + response.getOrderNotice() + ";" + response.getPurchaseApplyInfo());
        }
        for (int i = 1; i <= itemEntityList.size(); i++) {
            StockinOrderTaskItemEntity itemEntity1 = itemEntityList.get(i - 1);
            response.setPurchaserName(platformScheduleEntity.getPurchaseUserRealName());
            strings.add(String.format("申请部门%s：%s,%s|申请计划单号：%s", i, itemEntity1.getPurchaseApplyInfo(),
                platformScheduleEntity.getPurchaseUserRealName(), itemEntity1.getPurchasePlanNo()));
        }
        if (productSpecInfoEntity.getActualWeight() == null || productSpecInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0) {
            List<StockinSpotInfoEntity> infoEntityList = stockinSpotInfoService.listBySupplierDeliveryNo(taskEntity.getSupplierDeliveryNo());
            if (!CollectionUtils.isEmpty(infoEntityList)) {
                strings.add("入库时需要称重");
            }
        }
        return strings;
    }

    private void setAreaName(ScanInStorageBarcodeResponse response, List<StockinOrderTaskItemEntity> itemEntityList) {
        Map<String, List<StockinOrderTaskItemEntity>> collect = itemEntityList.stream()
            .collect(Collectors.groupingBy(item -> item.getAreaName() + "_" + item.getBusinessType() + "_" + item.getShelveSpaceAreaName()));
        List<ScanInSkuAreaResponse> collect1 = collect.entrySet().stream().map(entry -> {
            ScanInSkuAreaResponse scanInSkuAreaResponse = new ScanInSkuAreaResponse();
            int sum = entry.getValue().stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
            scanInSkuAreaResponse.setAreaName(String.format("%s(%s)", entry.getValue().get(0).getAreaName(), sum));
            scanInSkuAreaResponse.setBusinessType(entry.getValue().get(0).getBusinessType());
            scanInSkuAreaResponse.setQty(sum);
            scanInSkuAreaResponse.setShelveSpaceAreaName(entry.getValue().get(0).getShelveSpaceAreaName());
            return scanInSkuAreaResponse;
        }).collect(Collectors.toList());
        response.setScanInSkuAreaResponseList(collect1);
    }

    public String getLabelAttributeNames(List<StockinOrderTaskItemEntity> taskItemEntityList) {
        //优先级： 新>返>S >A > 改版 > 新品，其余标签不用显示
        String str = "";
        if (taskItemEntityList.stream().anyMatch(item -> StringConstant.FIRST_ORDER_LABEL.equals(item.getFirstOrderLabel()))) {
            str = StringConstant.FIRST_ORDER_LABEL + " ";
        }

        //返工
        /** 采购申请类型，1:缺货订单申请，2:定制申请，3:FBA发货申请，4:采购申请,5:正常采购,6:退货返工,7:开发申请,8:分公司申请,9:现货补单,10:市场补单 */
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getPurchasingApplyType())
            && item.getPurchasingApplyType().equals(6))
            || stockinOrderTaskItemService.getBaseMapper().isReturnDeliveryType(
            taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskId).distinct().collect(Collectors.toList()))) {
            str += "返";
        }
        if (str.length() > 0)
            return str.trim();
        //S
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames())
            && item.getLabelAttributeNames().contains("S"))) {
            return "S";
        }
        //A
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames())
            && item.getLabelAttributeNames().contains("A"))) {
            return "A";
        }
        //改版
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames())
            && item.getLabelAttributeNames().contains("改版"))) {
            return "改版";
        }
        //新品
        if (taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames())
            && item.getLabelAttributeNames().contains("新品"))) {
            return "新品";
        }
        return "";
    }

    private ScanInStorageBarcodeResponse buildScanInStorageBarcodeResponse(StockinOrderTaskEntity entity, ProductSpecInfoEntity productSpecInfoEntity) {
        StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(entity.getTaskId());
        List<StockinOrderItemEntity> itemEntityList = stockinOrderItemService.findAllByStockinOrderIdAndSku(stockinOrderEntity.getStockinOrderId(), productSpecInfoEntity.getSku());
        ScanInStorageBarcodeResponse response = new ScanInStorageBarcodeResponse();
        response.setCheck(Boolean.FALSE);
        response.setSku(productSpecInfoEntity.getSku());
        response.setShowNotice(Boolean.TRUE);
        response.setNotice("商品条码不在当前工厂出库箱码中，请核实。");
        response.setSupplierDeliveryBoxNo(entity.getSupplierDeliveryBoxCode());
        List<String> promptInformation = stockinOrderTaskService.getPromptInformation(entity.getTaskId(), productSpecInfoEntity.getBarcode());
        response.setNoticeMessage(StringUtils.join(promptInformation, '\n'));
        response.setReceiveQty(CollectionUtils.isEmpty(itemEntityList) ? 0 : itemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum());
        return response;
    }

    /**
     * 查询内部箱
     *
     * @param internalBoxCode
     * @return
     */
    public StockInternalBox getInternalBox(String internalBoxCode) {
        StockInternalBoxEntity boxEntity = stockInternalBoxMapper.selectOne(StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode));
        if (boxEntity == null) {
            throw new BusinessServiceException(String.format("未找到内部箱号为【%s】的内部箱!", internalBoxCode));
        }
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted())) {
            throw new BusinessServiceException("箱子已被删除，请确认！");
        }
        if (!StockInternalBoxTypeEnum.RECEIVE_BOX.name().equals(boxEntity.getInternalBoxType())) {
            throw new BusinessServiceException("该箱子不是收货箱，请扫描收货箱！");
        }
        String status = boxEntity.getStatus();
        List<String> statusList = Arrays.asList(StockInternalBoxStatusEnum.EMPTY.name(), StockInternalBoxStatusEnum.PACKING.name(), StockInternalBoxStatusEnum.WAIT_QC.name(),
            StockInternalBoxStatusEnum.WAIT_SHELVE.name(), StockInternalBoxStatusEnum.QC_COMPLETED.name(),
            StockInternalBoxStatusEnum.WAIT_RETURN.name());
        if (!statusList.contains(status)) {
            throw new BusinessServiceException(String.format("该类型【%s】的内部箱，不允许接收", StockInternalBoxStatusEnum.valueOf(status).getStatus()));
        }
        StockInternalBox stockInternalBox = new StockInternalBox();
        stockInternalBox.setShowNotice(Boolean.FALSE);
    /*    if (StockInternalBoxStatusEnum.WAIT_QC.name().equals(status)
                || StockInternalBoxStatusEnum.WAIT_SHELVE.name().equals(status)) {
            stockInternalBox.setShowNotice(Boolean.TRUE);
            stockInternalBox.setNotice("内部箱已装箱完成，是否继续装箱？");
        } else {
        }*/
        //        stockInternalBoxService.changeStockInternalBoxStatus(boxEntity, StockInternalBoxStatusEnum.PACKING.name());
        BeanUtilsEx.copyProperties(boxEntity, stockInternalBox);
        return stockInternalBox;
    }

    @Transactional
    @JLock(keyConstant = "addReceiveSkuQtyPda", lockKey = "#addReceiveSkuQtyRequest.supplierDeliveryBoxNo + '-' + #addReceiveSkuQtyRequest.barcode")
    public AddReceiveSkuQtyResponse addReceiveSkuQty(AddReceiveSkuQtyRequest addReceiveSkuQtyRequest) {
        validateReceiveSkuQtyRequest(addReceiveSkuQtyRequest);
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(addReceiveSkuQtyRequest.getSupplierDeliveryBoxNo());
        if (Objects.isNull(entity))
            //支持工厂出库单号
            return stockinDeliveryPdaService.addReceiveSkuQty(addReceiveSkuQtyRequest);
        if (StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus()))
            throw new BusinessServiceException("入库任务已经完成收货，请勿操作");
        StockinOrderItemSetRequest request = new StockinOrderItemSetRequest();
        request.setBarcode(addReceiveSkuQtyRequest.getBarcode());
        request.setInternalBoxCode(addReceiveSkuQtyRequest.getInternalBoxCode());
        request.setQty(addReceiveSkuQtyRequest.getQty());
        stockinOrderService.stockinOrderItemSet(entity.getTaskId(), request, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);

        StockinOrderEntity stockinOrderEntity = stockinOrderService.create(entity, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);
        AddReceiveSkuQtyResponse addReceiveSkuQtyResponse = new AddReceiveSkuQtyResponse();
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndBarcode(entity.getTaskId(), addReceiveSkuQtyRequest.getBarcode());
        Integer zero = Integer.valueOf(0);
        addReceiveSkuQtyResponse.setDeliveryQty(CollectionUtils.isEmpty(taskItemEntityList) ? zero : taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(addReceiveSkuQtyRequest.getBarcode(), Collections.singletonList(entity.getTaskId()));

        List<StockinOrderItemEntity> orderItemEntityList = stockinOrderItemService.findAllByStockinOrderIdAndSku(stockinOrderEntity.getStockinOrderId(), productSpecInfoEntity.getSku());
        addReceiveSkuQtyResponse.setReceiveQty(CollectionUtils.isEmpty(orderItemEntityList) ? zero : orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum());
        return addReceiveSkuQtyResponse;
    }

    // 新增修改，基本数据校验
    public void validateReceiveSkuQtyRequest(AddReceiveSkuQtyRequest request) {
        Validator.isValid(request.getSupplierDeliveryBoxNo(), StringUtils::hasText, "出库箱码不允许为空");
        Validator.isValid(request.getBarcode(), StringUtils::hasText, "商品编码不允许为空");
        Validator.isValid(request.getInternalBoxCode(), StringUtils::hasText, "内部箱号不允许为空");
        Validator.isValid(request.getQty(), Objects::nonNull, "数量不允许为空");
    }

    /**
     * 1.满箱确认 发送kafka消息通知生成上架任务
     * 2.回写更新质检数量 - 生成质检任务
     * 3.更新入库单和入库任务状态
     *
     * @param supplierDeliveryBoxCode
     */
    @Transactional
    public ReceiveResponse modifyReceiveInfo(String supplierDeliveryBoxCode, Boolean isConfirm) {
        StockinOrderTaskEntity taskEntity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (Objects.isNull(taskEntity)) {
            //支持工厂出库单号
            return stockinDeliveryPdaService.modifyReceiveInfo(supplierDeliveryBoxCode, isConfirm);
        }
        return getReceiveResponse(isConfirm, taskEntity);
    }

    @NotNull
    public ReceiveResponse getReceiveResponse(Boolean isConfirm, StockinOrderTaskEntity taskEntity) {
        ReceiveResponse receiveResponse = new ReceiveResponse();
        StockinOrderEntity orderEntity = stockinOrderService.create(taskEntity, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);
        List<StockinOrderItemEntity> orderItemEntityList = stockinOrderItemService.findAllByStockinOrderId(orderEntity.getStockinOrderId());
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskId(taskEntity.getTaskId());
        if (!isConfirm && itemEntityList.stream().anyMatch(item -> Objects.isNull(item.getStockinQty()) || item.getStockinQty().equals(0))) {
            receiveResponse.setIsNotice(Boolean.TRUE);
            receiveResponse.setNotice("存在未扫描的SKU，是否确认收货？");
            return receiveResponse;
        }

        Set<String> internalBoxCodes = new HashSet<>();
        orderItemEntityList.stream().forEach(orderItemEntity -> {
            internalBoxCodes.add(orderItemEntity.getInternalBoxCode());
        });
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
            .eq(StockInternalBoxItemEntity::getStockInOrderNo, orderEntity.getStockinOrderNo()));

        //更新出库任务状态
        taskEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVED.name());
        taskEntity.setOperateEndDate(new Date());
        taskEntity.setUpdateBy(loginInfoService.getName());
        stockinOrderTaskService.updateById(taskEntity);
        //未生成入库单明细的入库任务明细需要去生成
        stockinOrderTaskBuildService.buildOrderItem(itemEntityList, orderItemEntityList, orderEntity);
        if (StockinOrderStatusEnum.RECEIVING.name().equals(orderEntity.getStatus())
            || StockinOrderStatusEnum.PENDING_QC.name().equals(orderEntity.getStatus())) {
            List<StockinOrderItemEntity> collect1 = orderItemEntityList.stream().filter(item -> item.getQty() > 0).collect(Collectors.toList());
            orderEntity.setStatus(CollectionUtils.isEmpty(collect1) ? StockinOrderStatusEnum.CHECKING.name() : StockinOrderStatusEnum.PENDING_QC.name());
            stockinOrderService.updateById(orderEntity);
        }

        internalBoxCodes.stream().forEach(internalBoxCode -> {
            StockinInternalBoxFullRequest request = new StockinInternalBoxFullRequest();
            request.setTaskId(taskEntity.getTaskId());
            //满箱确认
            stockInternalBoxService.fullInternalBox(internalBoxCode, request);
        });

        //生成日志
        int qty = orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
        String content = String.format("完成入库任务收货，预收%s件，实际收货%s件", taskEntity.getExpectedQty(), qty);
        stockinScanLogService.addScanLog(content, taskEntity, orderEntity, qty, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_RECEIVING_COMPLETE);
        // 出库单日志
        List<StockinOrderTaskItemEntity> allByTaskId = stockinOrderTaskItemService.findAllByTaskId(taskEntity.getTaskId());
        Map<String, List<StockInternalBoxItemEntity>> collect = list.stream().collect(Collectors.groupingBy(StockInternalBoxItemEntity::getInternalBoxCode));
        stockinOrderTaskReceiveService.receiveCompleteAddStockinOrderLog(orderEntity.getStockinOrderId(), allByTaskId, collect);
        platformScheduleService.receivedUpdatePlatformStatus(taskEntity.getPlatformScheduleId());
        receiveResponse.setIsNotice(Boolean.FALSE);
        //箱子里没货的情况 自动完结入库单
        int boxQty = list.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
        if (boxQty <= 0)
            stockinShelveTaskService.updateStockinOrder(Collections.singletonList(orderEntity.getStockinOrderId()), true);
        return receiveResponse;
    }

    @Transactional
    public void modifyReceiveSkuQty(ModifySkuReceiveQtyRequest request) {
        validateModifySkuReceiveQtyRequest(request);
        StockinOrderTaskEntity taskEntity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(request.getSupplierDeliveryBoxNo());
        if (Objects.isNull(taskEntity)) {
            //支持工厂出库单号
            stockinDeliveryPdaService.modifyReceiveSkuQty(request);
            return;
        }
        if (StockinOrderTaskStatusEnum.RECEIVED.name().equals(taskEntity.getStatus()))
            throw new BusinessServiceException("入库任务已经完成收货，请勿操作");
        request.getModifySkuReceiveQtyInfoList().forEach(info -> {
            StockinOrderTaskItemDetail detail = new StockinOrderTaskItemDetail();
            detail.setInternalBoxCode(info.getInternalBoxCode());
            detail.setQty(info.getReceiveQty());
            detail.setSku(request.getSku());
            stockinOrderTaskService.editSkuQty(taskEntity.getTaskId(), detail);
        });
    }

    public void validateModifySkuReceiveQtyRequest(ModifySkuReceiveQtyRequest request) {
        Validator.isValid(request.getSupplierDeliveryBoxNo(), StringUtils::hasText, "出库箱码不允许为空");
        Validator.isValid(request.getModifySkuReceiveQtyInfoList(), Objects::nonNull, "商品编码不允许为空");
        Validator.isValid(request.getSku(), StringUtils::hasText, "SKU不允许为空");
    }

    @Transactional
    public void modifyReceiveStatus(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity taskEntity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (Objects.isNull(taskEntity)) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        }
        StockinOrderEntity orderEntity = stockinOrderService.findTopByTaskId(taskEntity.getTaskId());
        //更新出库任务状态
        taskEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVED.name());
        taskEntity.setOperateEndDate(new Date());
        taskEntity.setUpdateBy(loginInfoService.getName());
        stockinOrderTaskService.updateById(taskEntity);
        platformScheduleService.receivedUpdatePlatformStatus(taskEntity.getPlatformScheduleId());
        orderEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVED.name());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockinOrderService.updateById(orderEntity);
    }

    //支持出库箱码或工厂出库单
    public GetReceiveSkuListResponse getReceiveSkuList(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null)
            //支持工厂出库单号
            return stockinDeliveryPdaService.getReceiveSkuList(supplierDeliveryBoxCode);
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId());
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK_ITEM);
        }
        StockinOrderEntity orderEntity = stockinOrderService.create(entity, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);
        List<StockinOrderItemEntity> orderItemEntityList = stockinOrderItemService.findAllByStockinOrderId(orderEntity.getStockinOrderId());

        return getGetReceiveSkuListResponse(entity, taskItemEntityList, orderItemEntityList);
    }

    @NotNull
    public GetReceiveSkuListResponse getGetReceiveSkuListResponse(StockinOrderTaskEntity entity, List<StockinOrderTaskItemEntity> taskItemEntityList, List<StockinOrderItemEntity> orderItemEntityList) {
        GetReceiveSkuListResponse response = new GetReceiveSkuListResponse();
        Map<String, List<StockinOrderItemEntity>> collect = orderItemEntityList.stream().collect(Collectors.groupingBy(orderItemEntity ->
            orderItemEntity.getSku() + "-" + orderItemEntity.getInternalBoxCode()));

        List<ReceiveSkuView> list = collect.entrySet().stream().map(entry -> {
            List<StockinOrderItemEntity> value = entry.getValue();
            StockinOrderItemEntity orderItemEntity = value.get(0);
            ReceiveSkuView receiveSkuView = new ReceiveSkuView();
            receiveSkuView.setSku(orderItemEntity.getSku());
            receiveSkuView.setSpecId(orderItemEntity.getSpecId());
            receiveSkuView.setReceiveQty(value.stream().mapToInt(StockinOrderItemEntity::getQty).sum());
            receiveSkuView.setInternalBoxCode(orderItemEntity.getInternalBoxCode());
            if (Objects.isNull(orderItemEntity.getTaskItemId())) {
                receiveSkuView.setDeliveryQty(0);
            } else {
                List<StockinOrderTaskItemEntity> collect1 = taskItemEntityList.stream().filter(stockinOrderTaskItemEntity -> stockinOrderTaskItemEntity.getSku().equals(orderItemEntity.getSku())).collect(Collectors.toList());
                receiveSkuView.setDeliveryQty(collect1.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
            }
            return receiveSkuView;
        }).collect(Collectors.toList());
        //list.addAll(addItemList(orderEntity));
        response.setStatus(StockinOrderTaskStatusEnum.valueOf(entity.getStatus()).getStatus());
        response.setReceiveSkuViewList(list);
        response.setSupplierDeliveryBoxNo(entity.getSupplierDeliveryBoxCode());
        return response;
    }

    public GetReceiveSkuInfoBoxListResponse getReceiveSkuInfoBoxList(String supplierDeliveryBoxCode, Integer specId) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null) {
            //支持工厂出库单号
            return stockinDeliveryPdaService.getReceiveSkuInfoBoxList(supplierDeliveryBoxCode, specId);
        }
        StockinOrderEntity stockinOrderEntity = stockinOrderService.create(entity, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);

        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSpecId(entity.getTaskId(), specId);
        List<StockinOrderItemEntity> stockinOrderItemEntities = stockinOrderItemService.findAllByStockinOrderIdAndSpecId(stockinOrderEntity.getStockinOrderId(), specId);

        GetReceiveSkuInfoBoxListResponse response = getGetReceiveSkuInfoBoxListResponse(specId, taskItemEntityList, stockinOrderItemEntities);
        response.setSupplierDeliveryBoxNo(supplierDeliveryBoxCode);
        return response;
    }

    @NotNull
    public GetReceiveSkuInfoBoxListResponse getGetReceiveSkuInfoBoxListResponse(Integer specId, List<StockinOrderTaskItemEntity> taskItemEntityList, List<StockinOrderItemEntity> stockinOrderItemEntities) {
        Map<String, List<StockinOrderItemEntity>> collect = stockinOrderItemEntities.stream().collect(Collectors.groupingBy(StockinOrderItemEntity::getInternalBoxCode));

        GetReceiveSkuInfoBoxListResponse response = new GetReceiveSkuInfoBoxListResponse();
        List<ReceiveSkuBoxView> list = collect.entrySet().stream().map(entry -> {
            List<StockinOrderItemEntity> value = entry.getValue();
            ReceiveSkuBoxView receiveSkuBoxView = new ReceiveSkuBoxView();
            receiveSkuBoxView.setInternalBoxCode(entry.getKey());
            receiveSkuBoxView.setReceiveQty(value.stream().mapToInt(StockinOrderItemEntity::getQty).sum());
            return receiveSkuBoxView;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySpecId(specId);
            if (productSpecInfoEntity == null) {
                throw new BusinessServiceException("商品条码不存在");
            }
            response.setDeliveryQty(0);
            response.setSku(productSpecInfoEntity.getSku());
            response.setReceiveQty(list.stream().mapToInt(ReceiveSkuBoxView::getReceiveQty).sum());
        } else {
            response.setDeliveryQty(taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
            response.setSku(taskItemEntityList.get(0).getSku());
            response.setReceiveQty(list.stream().mapToInt(ReceiveSkuBoxView::getReceiveQty).sum());
            response.setIsPreQaReturn(taskItemEntityList.get(0).getIsPreQaReturn());
        }
        response.setReceiveSkuViewList(list);
        response.setSpecId(specId);
        return response;
    }

    // 支持出库箱码或工厂出库单
    public StockinReceiveSkuListResponse getStockinReceiveSkuList(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null)
            //支持工厂出库单号
            return stockinDeliveryPdaService.getStockinReceiveSkuList(supplierDeliveryBoxCode);
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId());
        if (CollectionUtils.isEmpty(itemEntityList))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK_ITEM);
        return getStockinReceiveSkuListResponse(entity, itemEntityList);
    }

    @NotNull
    public StockinReceiveSkuListResponse getStockinReceiveSkuListResponse(StockinOrderTaskEntity entity, List<StockinOrderTaskItemEntity> itemEntityList) {
        List<ReceiveSkuView> receiveSkuViewList = new LinkedList<>();
        StockinReceiveSkuListResponse receiveSkuListResponse = new StockinReceiveSkuListResponse();
        if (StockinOrderTaskStatusEnum.PENDING.name().equals(entity.getStatus())) {
            itemEntityList.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku)).values().forEach(value -> {
                ReceiveSkuView receiveSkuView = new ReceiveSkuView();
                int waitReceiveQty = value.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
                BeanUtilsEx.copyProperties(value.get(0), receiveSkuView);
                receiveSkuView.setReceiveQty(0);
                receiveSkuView.setDeliveryQty(waitReceiveQty);
                receiveSkuViewList.add(receiveSkuView);
            });
        } else {
            List<Integer> itemIdList = itemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).distinct().collect(Collectors.toList());
            Map<String, List<StockinOrderItemEntity>> collect = stockinOrderItemService.findAllByTaskItemIdIn(itemIdList).stream().collect(Collectors.groupingBy(StockinOrderItemEntity::getSku));
            itemEntityList.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku)).values().forEach(value -> {
                List<StockinOrderItemEntity> stockinOrderItemList = collect.get(value.get(0).getSku());
                int waitReceiveQty = value.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
                if (CollectionUtils.isEmpty(stockinOrderItemList)) {
                    ReceiveSkuView receiveSkuView = new ReceiveSkuView();
                    BeanUtilsEx.copyProperties(value.get(0), receiveSkuView);
                    receiveSkuView.setReceiveQty(0);
                    receiveSkuView.setDeliveryQty(waitReceiveQty);
                    receiveSkuViewList.add(receiveSkuView);
                    return;
                }
                Map<String, List<StockinOrderItemEntity>> internalBoxGroup = stockinOrderItemList.stream()
                    .filter(item -> Objects.nonNull(item.getInternalBoxCode()))
                    .collect(Collectors.groupingBy(StockinOrderItemEntity::getInternalBoxCode));
                internalBoxGroup.forEach((internalBoxCode, orderItemList) -> {
                    ReceiveSkuView receiveSkuView = new ReceiveSkuView();
                    BeanUtilsEx.copyProperties(orderItemList.get(0), receiveSkuView);
                    receiveSkuView.setDeliveryQty(waitReceiveQty);
                    receiveSkuView.setReceiveQty(orderItemList.stream().mapToInt(StockinOrderItemEntity::getQty).sum());
                    receiveSkuView.setInternalBoxCode(internalBoxCode);
                    receiveSkuViewList.add(receiveSkuView);
                });
                int receiveQty = stockinOrderItemList.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
                if (waitReceiveQty != receiveQty)
                    receiveSkuListResponse.setNotEqual(1);
            });
        }
        receiveSkuListResponse.setQty(itemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
        receiveSkuListResponse.setBoxQty((int) itemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskId).distinct().count());
        receiveSkuListResponse.setSupplierDeliveryBoxNo(entity.getSupplierDeliveryBoxCode());
        receiveSkuListResponse.setStatus(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TASK_TYPE.getName(), entity.getStatus()));
        receiveSkuListResponse.setReceiveSkuViewList(receiveSkuViewList);
        return receiveSkuListResponse;
    }
}
