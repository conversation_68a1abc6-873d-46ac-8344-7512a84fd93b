package com.nsy.wms.business.service.stockin;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stockin.StockinReturnProduct;
import com.nsy.api.wms.domain.stockin.StockinReturnProductQcInfo;
import com.nsy.api.wms.domain.stockin.StockinReturnProductVo;
import com.nsy.api.wms.enumeration.bd.SupplierTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnProductStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskStatusEnum;
import com.nsy.api.wms.request.external.ExternalQcReturnBackRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.QueryStockinReturnProductQtyRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSpotSetRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductAddRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductListRequest;
import com.nsy.api.wms.request.stockin.UpdateSupplierBuyerRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.QueryStockinReturnProductQtyResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductPDAListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductPDATotalResponse;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpBuyerInfoBySupplierIdRequest;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductItemSyncRequest;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductSyncRequest;
import com.nsy.wms.business.manage.erp.response.ErpBuyerInfoBySupplierIdResponse;
import com.nsy.wms.business.manage.erp.response.ErpCheckMsg;
import com.nsy.wms.business.manage.erp.response.ErpSupplierEmpNameInfo;
import com.nsy.wms.business.manage.erp.response.ErpSupplierEmpNameInfoResponse;
import com.nsy.wms.business.manage.erp.response.ErpSupplierInfo;
import com.nsy.wms.business.manage.erp.response.ErpSupplierInfoResponse;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.PurchaseRefundTaskAddRequest;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.supplier.request.SyncWaitReturnQtyToSupplierRequest;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSupplierPositionMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSupplierPositionMappingEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductMapper;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class StockinReturnProductService extends ServiceImpl<StockinReturnProductMapper, StockinReturnProductEntity> {

    @Autowired
    StockinReturnProductMapper returnProductMapper;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    BdPositionMapper positionMapper;
    @Autowired
    StockInternalBoxMapper internalBoxMapper;
    @Autowired
    StockService stockService;
    @Autowired
    StockinShelveTaskItemService shelveTaskItemService;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockinShelveTaskService shelveTaskService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    BdSupplierPositionMappingService supplierPositionMappingService;
    @Autowired
    private GcApiService gcApiService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    UserApiService userApiService;
    @Autowired
    BrandCommonService brandCommonService;
    @Autowired
    private StockinShelveTaskOpService stockinShelveTaskOpService;
    @Autowired
    private BdAreaService bdAreaService;
    @Autowired
    private BdErpSpaceMappingService bdErpSpaceMappingService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;


    /**
     * 供应商退货明细
     *
     * @param request
     * @return
     */
    public PageResponse<StockinReturnProductListResponse> getListByRequest(StockinReturnProductListRequest request) {
        PageResponse<StockinReturnProductListResponse> pageResponse = new PageResponse<>();
        ErpSupplierInfoResponse erpSupplierInfoResponse = erpApiService.getSupplierInfo();
        List<ErpSupplierInfo> supplierInfoList = erpSupplierInfoResponse.getList();
        ErpSupplierEmpNameInfoResponse erpSupplierEmpNameInfoResponse = erpApiService.getSupplierEmpNameInfo();
        List<ErpSupplierEmpNameInfo> supplierEmpInfoList = erpSupplierEmpNameInfoResponse.getList();

        List<Integer> supplierIds = new ArrayList<>();
        if (request.getBuyerCode() != null && !request.getBuyerCode().isEmpty()) {
            List<Integer> empSupplierIds = supplierInfoList.stream().filter(info -> info.getEmpCode() != null && info.getEmpCode().equals(request.getBuyerCode())).map(ErpSupplierInfo::getSupplierId).collect(Collectors.toList());
            if (!empSupplierIds.isEmpty()) {
                supplierIds.addAll(empSupplierIds);
            }
        }
        if (supplierIds.isEmpty()) {
            supplierIds.add(-1);
        } else {
            supplierIds = supplierIds.stream().distinct().collect(Collectors.toList());
        }
        request.setSupplierIds(supplierIds);
        Page<StockinReturnProduct> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinReturnProduct> pageResult = returnProductMapper.pageSearchReturnProduct(page, request);
        Map<Integer, String> qcUserNameMap = this.getQcUserInfoByReturnProductId(pageResult.getRecords().stream().map(StockinReturnProduct::getReturnProductId).collect(Collectors.toList()));
        List<StockinReturnProductListResponse> returnProductList = pageResult.getRecords().stream().map(projection -> {
            StockinReturnProductListResponse response = new StockinReturnProductListResponse();
            BeanUtils.copyProperties(projection, response);
            if (StringUtils.hasText(qcUserNameMap.get(projection.getReturnProductId()))) {
                response.setQcUserName(qcUserNameMap.get(projection.getReturnProductId()));
            }
            if (supplierInfoList != null && !supplierInfoList.isEmpty()) {
                Optional<String> empCodeOptional = supplierInfoList.stream().filter(t -> projection.getSupplierId().equals(t.getSupplierId())).map(ErpSupplierInfo::getEmpCode).findFirst();
                if (empCodeOptional.isPresent()) {
                    Optional<String> empNameOptional = supplierEmpInfoList.stream().filter(t -> empCodeOptional.get().equals(t.getEmpCode())).map(ErpSupplierEmpNameInfo::getEmpName).findFirst();
                    empNameOptional.ifPresent(response::setBuyerName);
                }
            }
            return response;
        }).collect(Collectors.toList());
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(returnProductList);
        return pageResponse;
    }


    /**
     * 扣减供应商退货明细
     * step1：扣减供应商退货明细数量
     * step2：库存变动
     * step3：如果有上架任务需更新上架任务
     *
     * @param request
     */
    public void backReturnProduct(ExternalQcReturnBackRequest request) {
        Validator.isValid(request.getOperator(), Objects::nonNull, "请指定操作人");

        QueryWrapper<StockinReturnProductEntity> queryWrapper = buildWrapper(request);
        List<StockinReturnProductEntity> returnProductEntityList = this.list(queryWrapper);
        if (returnProductEntityList.isEmpty())
            throw new BusinessServiceException("未找到退货商品数据，请确认");

        // step1：扣减供应商退货明细数量
        Integer leftQty = request.getDelQty();
        // 用于判断出库单号是否符合
        List<String> originList = new LinkedList<>();
        for (StockinReturnProductEntity returnProductEntity : returnProductEntityList) {
            if (leftQty <= 0)
                break;
            if (!StringUtils.hasText(returnProductEntity.getSupplierDeliveryNo()))
                continue;
            originList.addAll(Arrays.asList(returnProductEntity.getSupplierDeliveryNo().split(",")));
            originList.retainAll(request.getSupplierDeliverNos());
            if (!originList.isEmpty()) {
                if (leftQty >= returnProductEntity.getReturnQty()) {
                    leftQty = leftQty - returnProductEntity.getReturnQty();
                    this.removeById(returnProductEntity.getReturnProductId());
                } else {
                    returnProductEntity.setReturnQty(returnProductEntity.getReturnQty() - leftQty);
                    this.updateById(returnProductEntity);
                    leftQty = 0;
                }
            }
            originList.clear();
        }
        // step2：库存变动
        // step3：如果有上架任务需更新上架任务
        if (leftQty <= 0) {
            StockinReturnProductAddRequest returnProductAddRequest = new StockinReturnProductAddRequest();
            BeanUtils.copyProperties(request, returnProductAddRequest);
            if (!CollectionUtils.isEmpty(request.getSupplierDeliverNos()))
                returnProductAddRequest.setSupplierDeliveryNo(request.getSupplierDeliverNos().get(0));
            if (!CollectionUtils.isEmpty(request.getReceiveOrderNos()))
                returnProductAddRequest.setReceiveOrderNo(request.getReceiveOrderNos().get(0));
            returnProductAddRequest.setInternalBoxCode(request.getInternalBoxNo());
            returnProductAddRequest.setReturnQty(-request.getDelQty());
            alterReturnProduct(returnProductAddRequest, Boolean.FALSE);
        }
    }

    private QueryWrapper<StockinReturnProductEntity> buildWrapper(ExternalQcReturnBackRequest request) {
        QueryWrapper<StockinReturnProductEntity> wrapper = new QueryWrapper<>();
        if (!StringUtils.hasText(request.getSku()))
            throw new BusinessServiceException("请填写规格编码");
        wrapper.lambda().eq(StockinReturnProductEntity::getSku, request.getSku());
        if (!StringUtils.hasText(request.getInternalBoxNo()))
            throw new BusinessServiceException("请填写内部箱号");
        wrapper.lambda().eq(StockinReturnProductEntity::getInternalBoxCode, StringUtils.hasText(request.getBoxBarcode()) ? request.getBoxBarcode() : request.getInternalBoxNo());
        if (request.getSupplierId() == null)
            throw new BusinessServiceException("请填写供应商ID");
        if (StringUtils.hasText(request.getPurchasePlanNo()))
            wrapper.lambda().eq(StockinReturnProductEntity::getPurchasePlanNo, request.getPurchasePlanNo());
        return wrapper;
    }

    /**
     * 新增供应商退货明细
     *
     * @param request
     */
    public void alterReturnProduct(StockinReturnProductAddRequest request, Boolean checkSupplier) {
        Validator.isValid(request.getSku(), Objects::nonNull, "请填写规格编码");
        if (request.getReturnQty() == 0)
            return;

        //获取采购员
        ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo buyerInfo = fetchBuyerInfoBySupplierId(request.getSupplierId());
        //获取退货性质
        String returnNature = getReturnNatureByBuyerInfo(buyerInfo, request.getSupplierDeliveryNo());
        // 获取 供应商库位关系
        BdSupplierPositionMappingEntity mappingEntity = supplierPositionMappingService.getSupplierPositionMappingEntity(request.getSupplierId(), returnNature, this.getReturnSpaceId(request));
        // 获取 sku
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopBySku(request.getSku());
        if (Objects.isNull(specInfoEntity))
            throw new BusinessServiceException(String.format("规格编码【%s】未找到！", request.getSku()));
        // 获取 库位
        BdPositionEntity positionEntity = positionMapper.selectOne(new QueryWrapper<BdPositionEntity>().lambda().eq(BdPositionEntity::getPositionId, mappingEntity.getPositionId())
            .eq(BdPositionEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED).last("limit 1"));
        if (Objects.isNull(positionEntity))
            throw new BusinessServiceException(String.format("库位编码【%s】未找到！", mappingEntity.getPositionCode()));
        request.setSpaceId(positionEntity.getSpaceId());
        // 1、新增退货明细
        if (request.getReturnQty() > 0) {
            StockinReturnProductEntity returnProductEntity = buildReturnProductEntity(specInfoEntity, positionEntity, request, mappingEntity, buyerInfo);
            save(returnProductEntity);
        }
        if (checkSupplier && !StringConstant.UNCHECK_POSITION_CODE.equals(positionEntity.getPositionCode()) && !scmApiService.checkSupplierExistsSku(request.getSupplierId(), specInfoEntity.getSku()))
            throw new BusinessServiceException("当前工厂没有生产该商品，请确认调入工厂");
        // 2、库存变化 + 库存变动日志记录
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(specInfoEntity.getSku());
        stockUpdateRequest.setPositionCode(positionEntity.getPositionCode());
        StockChangeLogTypeEnum qaType = request.getQaType();
        stockUpdateRequest.setChangeLogType(qaType);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.QC);
        stockUpdateRequest.setQty(request.getReturnQty());
        stockUpdateRequest.setPurchasePlanNo(request.getPurchasePlanNo());
        stockUpdateRequest.setStockInOrderNo(request.getStockinOrderNo());
        stockService.updateStock(stockUpdateRequest);
        if (!StringUtils.hasText(request.getInternalBoxCode())) {
            returnProductSync(specInfoEntity.getSku(), request, request.getPurchasePlanNo(), request.getReceiveOrderNo(), request.getReturnQty());
            return;
        }

        // 获取 内部箱
        StockInternalBoxEntity internalBoxEntity = internalBoxMapper.selectOne(new QueryWrapper<StockInternalBoxEntity>().eq("internal_box_code", request.getInternalBoxCode()));
        if (Objects.isNull(internalBoxEntity))
            throw new BusinessServiceException(String.format("内部箱编码【%s】未找到！", request.getInternalBoxCode()));
        // 是否有上架任务
        StockinShelveTaskEntity taskEntity = shelveTaskService.getOne(StockinShelveTaskService.buildWrapperTaskByInternalBoxCode(internalBoxEntity.getInternalBoxCode(),
            Arrays.asList(StockinShelveTaskStatusEnum.PENDING.name(), StockinShelveTaskStatusEnum.SHELVING.name())).last(MybatisQueryConstant.QUERY_FIRST));
        if (taskEntity == null) {
            // 更新收货箱上架任务
            if (request.getCreateShelveTask())
                stockinShelveTaskOpService.editInternalBoxInItem(internalBoxEntity.getInternalBoxCode(), request.getSku());
            returnProductSync(specInfoEntity.getSku(), request, request.getPurchasePlanNo(), request.getReceiveOrderNo(), request.getReturnQty());
        } else {
            // 3、上架任务退货数量变化
            if (request.getReturnQty() > 0) {
                shelveTaskItemService.updateAddReturnedQtyBy(internalBoxEntity, specInfoEntity, request, taskEntity.getShelveTaskId(), mappingEntity.getPositionCode());
            } else {
                shelveTaskItemService.updateSubReturnedQtyBy(internalBoxEntity, specInfoEntity, request, taskEntity.getShelveTaskId(), mappingEntity.getPositionCode());
            }
        }
    }

    private Integer getReturnSpaceId(StockinReturnProductAddRequest request) {
        if (Objects.nonNull(request.getSpaceId())) {
            return request.getSpaceId();
        }
        if (StringUtils.hasText(request.getStockinOrderNo()) && StringUtils.hasText(request.getPurchasePlanNo()) && StringUtils.hasText(request.getSku())) {
            return stockinOrderItemService.getSpaceIdByCondition(request.getStockinOrderNo(), request.getPurchasePlanNo(), request.getSku());
        }
        return null;
    }

    public void returnProductSync(String sku, StockinReturnProductAddRequest returnProductAddRequest, String purchasePlanNo, String supplierDeliveryBoxCode, Integer returnQty) {
        ErpReturnProductItemSyncRequest itemSyncRequest = new ErpReturnProductItemSyncRequest(sku, returnQty);
        itemSyncRequest.setPurchasePlanNo(purchasePlanNo);
        itemSyncRequest.setSupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        itemSyncRequest.setIsSpot(returnProductAddRequest.getIsSpot());
        if (StringUtils.hasText(supplierDeliveryBoxCode) && StringUtils.hasText(purchasePlanNo)) {
            StockinOrderEntity orderEntity = stockinOrderService.findByStockinOrderNo(returnProductAddRequest.getStockinOrderNo());
            if (orderEntity != null) {
                StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemService.list(new LambdaQueryWrapper<StockinOrderItemEntity>().eq(StockinOrderItemEntity::getStockinOrderId, orderEntity.getStockinOrderId())
                        .eq(StockinOrderItemEntity::getPurchasePlanNo, purchasePlanNo)
                        .eq(StockinOrderItemEntity::getSku, sku))
                    .stream().min(Comparator.comparing(StockinOrderItemEntity::getCreateDate)).orElse(new StockinOrderItemEntity());
                itemSyncRequest.setReceiveDate(stockinOrderItemEntity.getCreateDate());
            }
        }

        ErpReturnProductSyncRequest request = new ErpReturnProductSyncRequest();
        request.setItems(Stream.of(itemSyncRequest).collect(Collectors.toList()));
        request.setLocation(TenantContext.getTenant());
        request.setEmpName(returnProductAddRequest.getOperator());
        BdErpSpaceMappingEntity defaultEntityBySpaceId = bdErpSpaceMappingService.getDefaultEntityBySpaceId(returnProductAddRequest.getSpaceId());
        request.setReturnSpaceId(defaultEntityBySpaceId.getErpReturnSpaceId());
        //默认异步队列
        if (StringUtils.hasText(supplierDeliveryBoxCode)) {
            erpApiService.returnProductAsync(request, supplierDeliveryBoxCode);
        } else {
            ErpCheckMsg checkMsg = erpApiService.returnProductSync(request);
            if (checkMsg != null && checkMsg.getIsError())
                throw new BusinessServiceException(String.format("erp退货仓库存同步失败：%s", checkMsg.getMessage()));
        }
    }

    /**
     * 构造退货商品明细
     */
    private StockinReturnProductEntity buildReturnProductEntity(ProductSpecInfoEntity specInfoEntity, BdPositionEntity positionEntity, StockinReturnProductAddRequest request, BdSupplierPositionMappingEntity mappingEntity, ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo buyerInfo) {
        StockinReturnProductEntity returnProductEntity = new StockinReturnProductEntity();
        returnProductEntity.setProductId(specInfoEntity.getProductId());
        returnProductEntity.setSpecId(specInfoEntity.getSpecId());
        returnProductEntity.setSku(specInfoEntity.getSku());
        returnProductEntity.setSpaceId(positionEntity.getSpaceId());
        returnProductEntity.setSpaceName(positionEntity.getSpaceName());
        returnProductEntity.setSupplierId(mappingEntity.getSupplierId());
        returnProductEntity.setSupplierName(mappingEntity.getSupplierName());
        returnProductEntity.setSupplierDeliveryNo(request.getSupplierDeliveryNo());
        returnProductEntity.setInternalBoxCode(request.getInternalBoxCode());
        returnProductEntity.setReturnQty(request.getReturnQty());
        returnProductEntity.setPurchasePlanNo(request.getPurchasePlanNo());
        returnProductEntity.setStatus(StockinReturnProductStatusEnum.PENDING.name());
        returnProductEntity.setLocation(positionEntity.getLocation());
        returnProductEntity.setCreateBy(loginInfoService.getName());
        returnProductEntity.setStockinOrderNo(request.getStockinOrderNo());
        returnProductEntity.setUnqualifiedReason(request.getUnqualifiedReason());
        returnProductEntity.setUnqualifiedCategory(request.getUnqualifiedCategory());

        if (StringUtils.hasText(request.getStockinOrderNo())) {
            StockinOrderEntity stockinOrder = stockinOrderService.findByStockinOrderNo(request.getStockinOrderNo());
            if (!Objects.isNull(stockinOrder))
                returnProductEntity.setSupplierDeliveryBoxCode(stockinOrder.getSupplierDeliveryBoxCode());
            String areaName = stockinOrderItemService.getBaseMapper().getAreaName(request.getStockinOrderNo(), request.getPurchasePlanNo(), request.getSku());
            returnProductEntity.setBrandName(brandCommonService.getBrandNameByAreaName(areaName));
            if (StringUtils.hasText(areaName)) {
                BdAreaEntity areaEntity = bdAreaService.findByAreaNameAndSpaceId(areaName, positionEntity.getSpaceId());
                returnProductEntity.setSourceAreaId(Objects.nonNull(areaEntity) ? areaEntity.getAreaId() : null);
            }

            // 尝试从任务明细中获取包装方式和版本号信息
            if (StrUtil.isNotEmpty(request.getPurchasePlanNo())
                && StrUtil.isNotEmpty(request.getSku())
                && Objects.nonNull(stockinOrder)) {
                StockinOrderTaskItemEntity taskItemEntity = stockinOrderTaskItemService.findTopByTaskIdAndSkuAndPurchasePlanNo(
                    stockinOrder.getTaskId(),
                    request.getSku(),
                    request.getPurchasePlanNo());

                returnProductEntity.setPackingMethod(Objects.nonNull(taskItemEntity) ? taskItemEntity.getPackageName() : null);
                returnProductEntity.setVersionNo(Objects.nonNull(taskItemEntity) ? taskItemEntity.getWorkmanshipVersion() : null);
            }
        }

        // 如果请求中有这些信息，则以请求为准
        if (StrUtil.isNotEmpty(request.getPackingMethod())) {
            returnProductEntity.setPackingMethod(request.getPackingMethod());
        }
        if (StrUtil.isNotEmpty(request.getVersionNo())) {
            returnProductEntity.setVersionNo(request.getVersionNo());
        }
        buildSupplierInfo(returnProductEntity, buyerInfo);
        return returnProductEntity;
    }

    // 构造退货商品信息 退货类型、采购员信息
    public void buildSupplierInfo(StockinReturnProductEntity returnProductEntity, ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo buyerInfo) {
        returnProductEntity.setPurchaserRealName(buyerInfo.getRealName());
        returnProductEntity.setPurchaserUserName(buyerInfo.getUserName());
        if (SupplierTypeEnum.MARKETPLAYERS.name().equals(buyerInfo.getSupplierType())) {
            returnProductEntity.setReturnType(StockinReturnTypeEnum.SPOT_RETURN.name());
            returnProductEntity.setReturnNature(StockinReturnNatureEnum.REFUND_RETURN.name());
        } else {
            returnProductEntity.setReturnType(StockinReturnTypeEnum.FACTORY_RETURN.name());
            returnProductEntity.setReturnNature(StringUtils.hasText(returnProductEntity.getSupplierDeliveryNo()) ? StockinReturnNatureEnum.REWORK_RETURN.name() : StockinReturnNatureEnum.REFUND_RETURN.name());
        }
        if (StringUtils.hasText(returnProductEntity.getPurchaserUserName())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserAccount(returnProductEntity.getPurchaserUserName());
            returnProductEntity.setPurchaseUserId(Objects.nonNull(userInfo) ? userInfo.getUserId() : null);
        }
    }

    /**
     * 通过供应商ID去erp查找采购员信息
     *
     * @param supplierId
     * @return
     */
    public ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo fetchBuyerInfoBySupplierId(Integer supplierId) {
        ErpBuyerInfoBySupplierIdRequest erpRequest = new ErpBuyerInfoBySupplierIdRequest();
        erpRequest.setSupplierIdList(Collections.singletonList(supplierId));
        List<ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo> buyerInfoList = erpApiService.getBuyerInfoBySupplierIdList(erpRequest).getBuyerInfoList();
        if (CollectionUtils.isEmpty(buyerInfoList)) {
            throw new BusinessServiceException("获取erp供应商信息失败");
        }
        return buyerInfoList.stream().filter(t -> supplierId.equals(t.getSupplierId())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("【erp】未找到供应商ID【%s】", supplierId)));
    }

    /**
     * 通过供应商ID去erp查找退货性质
     *
     * @param supplierDeliveryNo
     * @return
     */
    public String getReturnNatureByBuyerInfo(ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo buyerInfo, String supplierDeliveryNo) {
        if (SupplierTypeEnum.MARKETPLAYERS.name().equals(buyerInfo.getSupplierType())) {
            return StockinReturnNatureEnum.REFUND_RETURN.name();
        } else {
            return StringUtils.hasText(supplierDeliveryNo) ? StockinReturnNatureEnum.REWORK_RETURN.name() : StockinReturnNatureEnum.REFUND_RETURN.name();
        }
    }

    /**
     * 通过供应商ID去erp查找退货性质
     *
     * @param supplierId
     * @param hasOriginOrder 是否有原单
     * @return
     */
    public String getReturnNatureBySupplierId(Integer supplierId, Boolean hasOriginOrder) {
        ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo buyerInfo = fetchBuyerInfoBySupplierId(supplierId);
        if (SupplierTypeEnum.MARKETPLAYERS.name().equals(buyerInfo.getSupplierType())) {
            return StockinReturnNatureEnum.REFUND_RETURN.name();
        } else {
            return hasOriginOrder ? StockinReturnNatureEnum.REWORK_RETURN.name() : StockinReturnNatureEnum.REFUND_RETURN.name();
        }
    }

    /**
     * 现货质检退货
     */
    public void spotReturnProduct(Integer returnQty, String supplierDeliveryNo, StockinOrderItemSpotSetRequest request, StockinOrderEntity orderEntity, StockinOrderItemEntity itemEntity) {
        StockinReturnProductEntity entity = this.getOne(new LambdaQueryWrapper<StockinReturnProductEntity>()
            .eq(StockinReturnProductEntity::getSupplierDeliveryNo, supplierDeliveryNo)
            .eq(StockinReturnProductEntity::getSku, itemEntity.getSku())
            .eq(StockinReturnProductEntity::getStatus, StockinReturnProductStatusEnum.PENDING.name())
            .isNull(StockinReturnProductEntity::getInternalBoxCode).last("limit 1"));
        if (Objects.isNull(entity)) {
            entity = new StockinReturnProductEntity();
            BeanUtilsEx.copyProperties(itemEntity, entity);
            entity.setSupplierDeliveryNo(supplierDeliveryNo);
            entity.setReturnQty(returnQty);
            entity.setStatus(StockinReturnProductStatusEnum.PENDING.toString());
            entity.setDescription(request.getQaProblem());
            entity.setCreateBy(loginInfoService.getName());
            entity.setUpdateBy(loginInfoService.getName());
            entity.setSpaceName(spaceService.getById(itemEntity.getSpaceId()).getSpaceName());
            entity.setSupplierId(orderEntity.getSupplierId());
            entity.setSupplierName(orderEntity.getSupplierName());
            entity.setStockinOrderNo(orderEntity.getStockinOrderNo());
            entity.setUnqualifiedReason(request.getQaProblem());
            //获取采购员信息
            ErpBuyerInfoBySupplierIdResponse.ErpBuyerInfo erpBuyerInfo = fetchBuyerInfoBySupplierId(request.getSupplierId());
            buildSupplierInfo(entity, erpBuyerInfo);
            save(entity);
        } else {
            if (StringUtils.hasText(request.getQaProblem()))
                entity.setUnqualifiedReason(request.getQaProblem());
            entity.setReturnQty(entity.getReturnQty() + returnQty);
            entity.setUpdateBy(loginInfoService.getName());
            entity.setLastStockinTime(new Date());
            this.updateById(entity);
        }

        // 新增供应商退货库位库存
        addReturnPositionStock(orderEntity.getSupplierId(), orderEntity.getSupplierName(), itemEntity.getSku(), returnQty, entity.getSpaceId());
        // erp 退货仓同步
        StockinReturnProductAddRequest returnProductAddRequest = new StockinReturnProductAddRequest();
        returnProductAddRequest.setSupplierId(orderEntity.getSupplierId());
        returnProductAddRequest.setSupplierName(orderEntity.getSupplierName());
        returnProductAddRequest.setReturnQty(returnQty);
        returnProductAddRequest.setOperator(loginInfoService.getName());
        returnProductAddRequest.setSpaceId(itemEntity.getSpaceId());
        returnProductSync(itemEntity.getSku(), returnProductAddRequest, null, null, returnQty);
    }

    /**
     * 新增供应商退货库位库存
     */
    private void addReturnPositionStock(Integer supplierId, String supplierName, String sku, Integer returnQty, Integer spaceId) {
        // 获取 供应商库位关系  现货 退款退货
        BdSupplierPositionMappingEntity mappingEntity = supplierPositionMappingService.getSupplierPositionMappingEntity(supplierId, StockinReturnNatureEnum.REFUND_RETURN.name(), spaceId);
        if (Objects.isNull(mappingEntity))
            throw new BusinessServiceException(String.format("供应商【%s】未找到绑定的库位！", supplierName));
        // 获取 库位
        BdPositionEntity positionEntity = positionMapper.selectOne(new QueryWrapper<BdPositionEntity>().eq("position_id", mappingEntity.getPositionId()));
        if (Objects.isNull(positionEntity))
            throw new BusinessServiceException(String.format("库位编码【%s】未找到！", mappingEntity.getPositionCode()));
        // 库存变化 + 库存变动日志记录
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(sku);
        stockUpdateRequest.setPositionCode(positionEntity.getPositionCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.RETURN_QC);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.QC);
        stockUpdateRequest.setQty(returnQty);
        stockService.updateStock(stockUpdateRequest);
    }

    /**
     * 通过供应商ID, 统计库位sku总数量
     */
    public StockinReturnProductPDATotalResponse total(Integer supplierId) {
        return baseMapper.positionTotal(supplierId);
    }

    public List<StockinReturnProductPDAListResponse> listBySupplierId(Integer supplierId) {
        return baseMapper.listBySupplierId(supplierId);
    }

    /**
     * 退货库位数量变化 其他系统的处理
     *
     * @param returnProductMessageList
     */
    public void processWaitReturnQtyChangeOtherSystem(List<StockinReturnProductVo> returnProductMessageList) {
        if (CollectionUtils.isEmpty(returnProductMessageList)) {
            return;
        }
        // 现货退货=》生成or修改scm退货任务
        generateOrUpdateSpotReturnTask(returnProductMessageList);
        // 工厂退货=》同步待退货数到supplier
        syncWaitReturnQtyToSupplier(returnProductMessageList);
    }

    public void generateOrUpdateSpotReturnTask(List<StockinReturnProductVo> returnProductMessageList) {
        List<StockinReturnProductVo> tempReturnProductMessageList = returnProductMessageList.stream()
            .filter(t -> StockinReturnTypeEnum.SPOT_RETURN.name().equals(t.getReturnType()) && StringUtils.hasText(t.getPurchasePlanNo())).collect(Collectors.toList());
        List<PurchaseRefundTaskAddRequest.Item> itemList = tempReturnProductMessageList.stream().map(returnProductEntity -> {
            PurchaseRefundTaskAddRequest.Item item = new PurchaseRefundTaskAddRequest.Item();
            item.setPurchaseOrderNo(returnProductEntity.getPurchasePlanNo());
            item.setProblemQty(returnProductEntity.getReturnQty());
            item.setWmsReturnProductId(returnProductEntity.getReturnProductId());
            item.setSku(returnProductEntity.getSku());
            return item;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        PurchaseRefundTaskAddRequest request = new PurchaseRefundTaskAddRequest();
        request.setItemList(itemList);
        request.setOperator(loginInfoService.getName());
        scmApiService.generateOrUpdateSpotReturnTask(request);
    }

    // 工厂退货=》同步待退货数到supplier(异步)
    public void syncWaitReturnQtyToSupplier(List<StockinReturnProductVo> returnProductEntityList) {
        List<StockinReturnProductVo> voList = returnProductEntityList.stream()
            .filter(t -> StockinReturnTypeEnum.FACTORY_RETURN.name().equals(t.getReturnType()) && StringUtils.hasText(t.getPurchasePlanNo())).collect(Collectors.toList());
        List<Integer> supplierIdList = voList.stream().map(StockinReturnProductVo::getSupplierId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<SupplierDto> supplierInfoList = scmApiService.getSupplierInfoList(supplierIdList);
        List<Integer> stockSupplierIdList = supplierInfoList.stream().filter(t -> SupplierService.isStockSupplier(t)).map(SupplierDto::getSupplierId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(stockSupplierIdList)) {
            voList = voList.stream().filter(t -> !stockSupplierIdList.contains(t.getSupplierId())).collect(Collectors.toList());
        }
        List<String> purchasePlanNoList = voList.stream().map(StockinReturnProductVo::getPurchasePlanNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(purchasePlanNoList)) {
            return;
        }
        Map<String, List<StockinReturnProductEntity>> allReturnProductMap = this.list(new LambdaQueryWrapper<StockinReturnProductEntity>()
            .in(StockinReturnProductEntity::getPurchasePlanNo, purchasePlanNoList)).stream().collect(Collectors.groupingBy(StockinReturnProductEntity::getPurchasePlanNo));
        purchasePlanNoList.forEach(purchasePlanNo -> {
            SyncWaitReturnQtyToSupplierRequest supplierRequest = new SyncWaitReturnQtyToSupplierRequest();
            supplierRequest.setPurchasePlanNo(purchasePlanNo);
            supplierRequest.setOperator(loginInfoService.getName());
            Map<String, List<StockinReturnProductEntity>> returnProductMap = allReturnProductMap.getOrDefault(purchasePlanNo, Lists.newArrayList()).stream()
                .collect(Collectors.groupingBy(StockinReturnProductEntity::getSku));
            List<SyncWaitReturnQtyToSupplierRequest.Item> itemList = Lists.newArrayListWithExpectedSize(returnProductMap.size());
            returnProductMap.forEach((sku, returnProductList) -> {
                SyncWaitReturnQtyToSupplierRequest.Item item = new SyncWaitReturnQtyToSupplierRequest.Item();
                item.setWaitReturnQty(returnProductList.stream().filter(t -> Objects.nonNull(t.getReturnQty())).mapToInt(StockinReturnProductEntity::getReturnQty).sum());
                item.setSku(sku);
                itemList.add(item);
            });
            supplierRequest.setItemList(itemList);
            gcApiService.syncWaitReturnQty(supplierRequest);
        });
    }

    public List<QueryStockinReturnProductQtyResponse> queryReturnQtyByPurchasePlanNo(QueryStockinReturnProductQtyRequest request) {
        return baseMapper.queryReturnQtyByPurchasePlanNo(request);
    }

    /**
     * 更新退货商品明细的采购员
     *
     * @param request
     */
    @Transactional
    public void updateSupplierBuyer(UpdateSupplierBuyerRequest request) {
        this.update(new LambdaUpdateWrapper<StockinReturnProductEntity>()
            .set(StockinReturnProductEntity::getPurchaserUserName, request.getContactPurchaserEmpCode())
            .set(StockinReturnProductEntity::getPurchaserRealName, request.getContactPurchaserEmpName())
            .set(StockinReturnProductEntity::getPurchaseUserId, request.getContactPurchaserEmpId())
            .eq(StockinReturnProductEntity::getSupplierId, request.getSupplierId()));


        List<BdSupplierPositionMappingEntity> supplierList = supplierPositionMappingService.getBySupplierId(request.getSupplierId());
        if (!supplierList.isEmpty()) {
            supplierList.forEach(supplier -> {
                String supplierName = supplier.getSupplierName().split("-")[0];
                supplier.setSupplierName(supplierName + "-" + request.getContactPurchaserEmpName());
            });
            supplierPositionMappingService.updateBatchById(supplierList);
        }
    }

    /**
     * 根据退货id获取质检信息
     *
     * @param returnProductIdList
     * @return
     */
    public Map<Integer, String> getQcUserInfoByReturnProductId(List<Integer> returnProductIdList) {
        if (CollectionUtils.isEmpty(returnProductIdList)) {
            return new HashMap<>();
        }
        List<StockinReturnProductQcInfo> qcInfoList = baseMapper.findQcInfoByReturnProductId(returnProductIdList);
        return qcInfoList.stream().collect(Collectors.toMap(StockinReturnProductQcInfo::getReturnProductId, StockinReturnProductQcInfo::getQcUserName));
    }
}
