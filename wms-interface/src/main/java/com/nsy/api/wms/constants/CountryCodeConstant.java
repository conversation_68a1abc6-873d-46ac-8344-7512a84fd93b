package com.nsy.api.wms.constants;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CountryCodeConstant {
    // 欧盟国家二字码
    public static final List<String> EUROPEAN_UNION_COUNTRY_CODE = Collections.unmodifiableList(Arrays.asList("AT", "BE", "HR", "LU", "BG", "CY", "CZ", "DK",
            "EE", "FI", "FR", "DE", "GR", "HU", "IS", "IT", "LV", "LT", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE"));
    // 加拿大
    public static final String CANADA = "CA";

    public static final String USA = "US";

    public static final String PR = "PR";

    private static final Map<String, String> COUNTRY_CODE_MAP = new HashMap<>();

    static {
        COUNTRY_CODE_MAP.put("HK", "香港");
        COUNTRY_CODE_MAP.put("TW", "台湾");
        COUNTRY_CODE_MAP.put("MO", "澳门");
        COUNTRY_CODE_MAP.put("US", "美国");
        COUNTRY_CODE_MAP.put("AR", "阿根廷");
        COUNTRY_CODE_MAP.put("AD", "安道尔");
        COUNTRY_CODE_MAP.put("AE", "阿联酋");
        COUNTRY_CODE_MAP.put("AF", "阿富汗");
        COUNTRY_CODE_MAP.put("AG", "安提瓜和巴布达");
        COUNTRY_CODE_MAP.put("AI", "安圭拉");
        COUNTRY_CODE_MAP.put("AL", "阿尔巴尼亚");
        COUNTRY_CODE_MAP.put("AM", "亚美尼亚");
        COUNTRY_CODE_MAP.put("AO", "安哥拉");
        COUNTRY_CODE_MAP.put("AQ", "南极洲");
        COUNTRY_CODE_MAP.put("AS", "美属萨摩亚");
        COUNTRY_CODE_MAP.put("AT", "奥地利");
        COUNTRY_CODE_MAP.put("AU", "澳大利亚");
        COUNTRY_CODE_MAP.put("AW", "阿鲁巴");
        COUNTRY_CODE_MAP.put("AX", "奥兰群岛");
        COUNTRY_CODE_MAP.put("AZ", "阿塞拜疆");
        COUNTRY_CODE_MAP.put("BA", "波黑");
        COUNTRY_CODE_MAP.put("BB", "巴巴多斯");
        COUNTRY_CODE_MAP.put("BD", "孟加拉");
        COUNTRY_CODE_MAP.put("BE", "比利时");
        COUNTRY_CODE_MAP.put("BF", "布基纳法索");
        COUNTRY_CODE_MAP.put("BG", "保加利亚");
        COUNTRY_CODE_MAP.put("BH", "巴林");
        COUNTRY_CODE_MAP.put("BI", "布隆迪");
        COUNTRY_CODE_MAP.put("BJ", "贝宁");
        COUNTRY_CODE_MAP.put("BL", "圣巴泰勒米岛");
        COUNTRY_CODE_MAP.put("BM", "百慕大");
        COUNTRY_CODE_MAP.put("BN", "文莱");
        COUNTRY_CODE_MAP.put("BO", "玻利维亚");
        COUNTRY_CODE_MAP.put("BQ", "荷兰加勒比区");
        COUNTRY_CODE_MAP.put("BR", "巴西");
        COUNTRY_CODE_MAP.put("BS", "巴哈马");
        COUNTRY_CODE_MAP.put("BT", "不丹");
        COUNTRY_CODE_MAP.put("BV", "布韦岛");
        COUNTRY_CODE_MAP.put("BW", "博茨瓦纳");
        COUNTRY_CODE_MAP.put("BY", "白俄罗斯");
    }
    static {

        COUNTRY_CODE_MAP.put("CC", "科科斯群岛");
        COUNTRY_CODE_MAP.put("CD", "刚果（金）");
        COUNTRY_CODE_MAP.put("CF", "中非");
        COUNTRY_CODE_MAP.put("CG", "刚果（布）");
        COUNTRY_CODE_MAP.put("CH", "瑞士");
        COUNTRY_CODE_MAP.put("CI", "科特迪瓦");
        COUNTRY_CODE_MAP.put("CK", "库克群岛");
        COUNTRY_CODE_MAP.put("CL", "智利");
        COUNTRY_CODE_MAP.put("CM", "喀麦隆");
        COUNTRY_CODE_MAP.put("CN", "中国");
        COUNTRY_CODE_MAP.put("CO", "哥伦比亚");
        COUNTRY_CODE_MAP.put("CR", "哥斯达黎加");
        COUNTRY_CODE_MAP.put("CU", "古巴");
        COUNTRY_CODE_MAP.put("CV", "佛得角");
        COUNTRY_CODE_MAP.put("CW", "库拉索");
        COUNTRY_CODE_MAP.put("CX", "圣诞岛");
        COUNTRY_CODE_MAP.put("CY", "塞浦路斯");
        COUNTRY_CODE_MAP.put("CZ", "捷克");
        COUNTRY_CODE_MAP.put("DE", "德国");
        COUNTRY_CODE_MAP.put("DJ", "吉布提");
        COUNTRY_CODE_MAP.put("DK", "丹麦");
        COUNTRY_CODE_MAP.put("DM", "多米尼克");
        COUNTRY_CODE_MAP.put("DO", "多米尼加");
        COUNTRY_CODE_MAP.put("DZ", "阿尔及利亚");
        COUNTRY_CODE_MAP.put("EC", "厄瓜多尔");
        COUNTRY_CODE_MAP.put("EE", "爱沙尼亚");
        COUNTRY_CODE_MAP.put("EG", "埃及");
        COUNTRY_CODE_MAP.put("EH", "西撒哈拉");
        COUNTRY_CODE_MAP.put("ER", "厄立特里亚");
        COUNTRY_CODE_MAP.put("ES", "西班牙");
        COUNTRY_CODE_MAP.put("ET", "埃塞俄比亚");
        COUNTRY_CODE_MAP.put("FI", "芬兰");
        COUNTRY_CODE_MAP.put("FJ", "斐济群岛");
        COUNTRY_CODE_MAP.put("FK", "马尔维纳斯群岛（福克兰）");
        COUNTRY_CODE_MAP.put("FM", "密克罗尼西亚联邦");
        COUNTRY_CODE_MAP.put("FO", "法罗群岛");
        COUNTRY_CODE_MAP.put("FR", "法国");
        COUNTRY_CODE_MAP.put("GA", "加蓬");
        COUNTRY_CODE_MAP.put("GB", "英国");
        COUNTRY_CODE_MAP.put("GD", "格林纳达");
    }
    static {
        COUNTRY_CODE_MAP.put("GF", "法属圭亚那");
        COUNTRY_CODE_MAP.put("GG", "根西岛");
        COUNTRY_CODE_MAP.put("GH", "加纳");
        COUNTRY_CODE_MAP.put("GI", "直布罗陀");
        COUNTRY_CODE_MAP.put("GL", "格陵兰");
        COUNTRY_CODE_MAP.put("GM", "冈比亚");
        COUNTRY_CODE_MAP.put("GN", "几内亚");
        COUNTRY_CODE_MAP.put("GP", "瓜德罗普");
        COUNTRY_CODE_MAP.put("GQ", "赤道几内亚");
        COUNTRY_CODE_MAP.put("GR", "希腊");
        COUNTRY_CODE_MAP.put("GS", "南乔治亚岛和南桑威奇群岛");
        COUNTRY_CODE_MAP.put("GT", "危地马拉");
        COUNTRY_CODE_MAP.put("GU", "关岛");
        COUNTRY_CODE_MAP.put("GW", "几内亚比绍");
        COUNTRY_CODE_MAP.put("GY", "圭亚那");
        COUNTRY_CODE_MAP.put("HM", "赫德岛和麦克唐纳群岛");
        COUNTRY_CODE_MAP.put("HN", "洪都拉斯");
        COUNTRY_CODE_MAP.put("HR", "克罗地亚");
        COUNTRY_CODE_MAP.put("HT", "海地");
        COUNTRY_CODE_MAP.put("HU", "匈牙利");
        COUNTRY_CODE_MAP.put("ID", "印尼");
        COUNTRY_CODE_MAP.put("IE", "爱尔兰");
        COUNTRY_CODE_MAP.put("IL", "以色列");
        COUNTRY_CODE_MAP.put("IM", "马恩岛");
        COUNTRY_CODE_MAP.put("IN", "印度");
        COUNTRY_CODE_MAP.put("IO", "英属印度洋领地");
        COUNTRY_CODE_MAP.put("IQ", "伊拉克");
        COUNTRY_CODE_MAP.put("IR", "伊朗");
        COUNTRY_CODE_MAP.put("IS", "冰岛");
        COUNTRY_CODE_MAP.put("IT", "意大利");
        COUNTRY_CODE_MAP.put("JE", "泽西岛");
        COUNTRY_CODE_MAP.put("JM", "牙买加");
        COUNTRY_CODE_MAP.put("JO", "约旦");
        COUNTRY_CODE_MAP.put("JP", "日本");
        COUNTRY_CODE_MAP.put("KE", "肯尼亚");
        COUNTRY_CODE_MAP.put("KG", "吉尔吉斯斯坦");
        COUNTRY_CODE_MAP.put("KH", "柬埔寨");
        COUNTRY_CODE_MAP.put("KI", "基里巴斯");
        COUNTRY_CODE_MAP.put("KM", "科摩罗");
        COUNTRY_CODE_MAP.put("KN", "圣基茨和尼维斯");
    }
    static {
        COUNTRY_CODE_MAP.put("KR", "韩国");
        COUNTRY_CODE_MAP.put("KW", "科威特");
        COUNTRY_CODE_MAP.put("KY", "开曼群岛");
        COUNTRY_CODE_MAP.put("KZ", "哈萨克斯坦");
        COUNTRY_CODE_MAP.put("LA", "老挝");
        COUNTRY_CODE_MAP.put("LB", "黎巴嫩");
        COUNTRY_CODE_MAP.put("LC", "圣卢西亚");
        COUNTRY_CODE_MAP.put("LI", "列支敦士登");
        COUNTRY_CODE_MAP.put("LK", "斯里兰卡");
        COUNTRY_CODE_MAP.put("LR", "利比里亚");
        COUNTRY_CODE_MAP.put("LS", "莱索托");
        COUNTRY_CODE_MAP.put("LT", "立陶宛");
        COUNTRY_CODE_MAP.put("LU", "卢森堡");
        COUNTRY_CODE_MAP.put("LV", "拉脱维亚");
        COUNTRY_CODE_MAP.put("LY", "利比亚");
        COUNTRY_CODE_MAP.put("MA", "摩洛哥");
        COUNTRY_CODE_MAP.put("MC", "摩纳哥");
        COUNTRY_CODE_MAP.put("MD", "摩尔多瓦");
        COUNTRY_CODE_MAP.put("ME", "黑山");
        COUNTRY_CODE_MAP.put("MF", "法属圣马丁");
        COUNTRY_CODE_MAP.put("MG", "马达加斯加");
        COUNTRY_CODE_MAP.put("MH", "马绍尔群岛");
        COUNTRY_CODE_MAP.put("MK", "马其顿");
        COUNTRY_CODE_MAP.put("ML", "马里");
        COUNTRY_CODE_MAP.put("MM", "缅甸");
        COUNTRY_CODE_MAP.put("MN", "蒙古国");
        COUNTRY_CODE_MAP.put("MP", "北马里亚纳群岛");
        COUNTRY_CODE_MAP.put("MQ", "马提尼克");
        COUNTRY_CODE_MAP.put("MR", "毛里塔尼亚");
        COUNTRY_CODE_MAP.put("MS", "蒙塞拉特岛");
        COUNTRY_CODE_MAP.put("MT", "马耳他");
        COUNTRY_CODE_MAP.put("MU", "毛里求斯");
        COUNTRY_CODE_MAP.put("MV", "马尔代夫");
        COUNTRY_CODE_MAP.put("MW", "马拉维");
        COUNTRY_CODE_MAP.put("MX", "墨西哥");
        COUNTRY_CODE_MAP.put("MY", "马来西亚");
        COUNTRY_CODE_MAP.put("MZ", "莫桑比克");
        COUNTRY_CODE_MAP.put("NA", "纳米比亚");
        COUNTRY_CODE_MAP.put("NC", "新喀里多尼亚");
        COUNTRY_CODE_MAP.put("NE", "尼日尔");
    }
    static {
        COUNTRY_CODE_MAP.put("NF", "诺福克岛");
        COUNTRY_CODE_MAP.put("NG", "尼日利亚");
        COUNTRY_CODE_MAP.put("NI", "尼加拉瓜");
        COUNTRY_CODE_MAP.put("NL", "荷兰");
        COUNTRY_CODE_MAP.put("NO", "挪威");
        COUNTRY_CODE_MAP.put("NP", "尼泊尔");
        COUNTRY_CODE_MAP.put("NR", "瑙鲁");
        COUNTRY_CODE_MAP.put("NU", "纽埃");
        COUNTRY_CODE_MAP.put("NZ", "新西兰");
        COUNTRY_CODE_MAP.put("OM", "阿曼");
        COUNTRY_CODE_MAP.put("PA", "巴拿马");
        COUNTRY_CODE_MAP.put("PE", "秘鲁");
        COUNTRY_CODE_MAP.put("PF", "法属波利尼西亚");
        COUNTRY_CODE_MAP.put("PG", "巴布亚新几内亚");
        COUNTRY_CODE_MAP.put("PH", "菲律宾");
        COUNTRY_CODE_MAP.put("PK", "巴基斯坦");
        COUNTRY_CODE_MAP.put("PL", "波兰");
        COUNTRY_CODE_MAP.put("PM", "圣皮埃尔和密克隆");
        COUNTRY_CODE_MAP.put("PN", "皮特凯恩群岛");
        COUNTRY_CODE_MAP.put("PR", "波多黎各");
        COUNTRY_CODE_MAP.put("PS", "巴勒斯坦");
        COUNTRY_CODE_MAP.put("PT", "葡萄牙");
        COUNTRY_CODE_MAP.put("PW", "帕劳");
        COUNTRY_CODE_MAP.put("PY", "巴拉圭");
        COUNTRY_CODE_MAP.put("QA", "卡塔尔");
        COUNTRY_CODE_MAP.put("RE", "留尼汪");
        COUNTRY_CODE_MAP.put("RO", "罗马尼亚");
        COUNTRY_CODE_MAP.put("RS", "塞尔维亚");
        COUNTRY_CODE_MAP.put("RU", "俄罗斯");
        COUNTRY_CODE_MAP.put("RW", "卢旺达");
        COUNTRY_CODE_MAP.put("SA", "沙特阿拉伯");
        COUNTRY_CODE_MAP.put("SB", "所罗门群岛");
        COUNTRY_CODE_MAP.put("SC", "塞舌尔");
        COUNTRY_CODE_MAP.put("SD", "苏丹");
        COUNTRY_CODE_MAP.put("SE", "瑞典");
        COUNTRY_CODE_MAP.put("SG", "新加坡");
        COUNTRY_CODE_MAP.put("SH", "圣赫勒拿");
        COUNTRY_CODE_MAP.put("SI", "斯洛文尼亚");
        COUNTRY_CODE_MAP.put("SJ", "斯瓦尔巴群岛和扬马延岛");
        COUNTRY_CODE_MAP.put("SK", "斯洛伐克");
    }
    static {
        COUNTRY_CODE_MAP.put("SN", "塞内加尔");
        COUNTRY_CODE_MAP.put("SO", "索马里");
        COUNTRY_CODE_MAP.put("SR", "苏里南");
        COUNTRY_CODE_MAP.put("SS", "南苏丹");
        COUNTRY_CODE_MAP.put("ST", "圣多美和普林西比");
        COUNTRY_CODE_MAP.put("SV", "萨尔瓦多");
        COUNTRY_CODE_MAP.put("SX", "荷属圣马丁");
        COUNTRY_CODE_MAP.put("SY", "叙利亚");
        COUNTRY_CODE_MAP.put("SZ", "斯威士兰");
        COUNTRY_CODE_MAP.put("TC", "特克斯和凯科斯群岛");
        COUNTRY_CODE_MAP.put("TD", "乍得");
        COUNTRY_CODE_MAP.put("TF", "法属南部领地");
        COUNTRY_CODE_MAP.put("TG", "多哥");
        COUNTRY_CODE_MAP.put("TH", "泰国");
        COUNTRY_CODE_MAP.put("TJ", "塔吉克斯坦");
        COUNTRY_CODE_MAP.put("TK", "托克劳");
        COUNTRY_CODE_MAP.put("TL", "东帝汶");
        COUNTRY_CODE_MAP.put("TM", "土库曼斯坦");
        COUNTRY_CODE_MAP.put("TN", "突尼斯");
        COUNTRY_CODE_MAP.put("TO", "汤加");
        COUNTRY_CODE_MAP.put("TR", "土耳其");
        COUNTRY_CODE_MAP.put("TT", "特立尼达和多巴哥");
        COUNTRY_CODE_MAP.put("TV", "图瓦卢");
        COUNTRY_CODE_MAP.put("TZ", "坦桑尼亚");
        COUNTRY_CODE_MAP.put("UA", "乌克兰");
        COUNTRY_CODE_MAP.put("UG", "乌干达");
        COUNTRY_CODE_MAP.put("UM", "美国本土外小岛屿");
        COUNTRY_CODE_MAP.put("UY", "乌拉圭");
        COUNTRY_CODE_MAP.put("UZ", "乌兹别克斯坦");
        COUNTRY_CODE_MAP.put("VA", "梵蒂冈");
        COUNTRY_CODE_MAP.put("VC", "圣文森特和格林纳丁斯");
        COUNTRY_CODE_MAP.put("VE", "委内瑞拉");
        COUNTRY_CODE_MAP.put("VG", "英属维尔京群岛");
        COUNTRY_CODE_MAP.put("VI", "美属维尔京群岛");
        COUNTRY_CODE_MAP.put("VN", "越南");
        COUNTRY_CODE_MAP.put("VU", "瓦努阿图");
        COUNTRY_CODE_MAP.put("WF", "瓦利斯和富图纳");
        COUNTRY_CODE_MAP.put("WS", "萨摩亚");
        COUNTRY_CODE_MAP.put("YE", "也门");
    }
    static {
        COUNTRY_CODE_MAP.put("YT", "马约特");
        COUNTRY_CODE_MAP.put("ZA", "南非");
        COUNTRY_CODE_MAP.put("ZM", "赞比亚");
        COUNTRY_CODE_MAP.put("ZW", "津巴布韦");
        COUNTRY_CODE_MAP.put("BZ", "伯利兹");
        COUNTRY_CODE_MAP.put("CA", "加拿大");
        COUNTRY_CODE_MAP.put("GE", "格鲁吉亚");
        COUNTRY_CODE_MAP.put("KP", "朝鲜");
        COUNTRY_CODE_MAP.put("SL", "塞拉利昂");
        COUNTRY_CODE_MAP.put("SM", "圣马力诺");
        COUNTRY_CODE_MAP.put("UK", "英国");
    }

    public static Map<String, String> getCountryCodeMap() {
        return Collections.unmodifiableMap(COUNTRY_CODE_MAP);
    }


}
