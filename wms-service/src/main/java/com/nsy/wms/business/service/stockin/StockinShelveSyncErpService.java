package com.nsy.wms.business.service.stockin;

import com.nsy.api.wms.domain.stockin.StockinOrderItem;
import com.nsy.api.wms.domain.stockin.StockinPackageQty;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.FeedbackConcessionReceiveItem;
import com.nsy.wms.business.manage.erp.request.FeedbackConcessionReceiveRequest;
import com.nsy.wms.business.manage.erp.request.FeedbackPackageQtyRequest;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderRequest;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderRequestItem;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockin.StockinDeliveryBoxCodeDetailEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinShelveSyncErpService {

    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinDeliveryBoxCodeDetailService stockinDeliveryBoxCodeDetailService;
    @Autowired
    private StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    private ScmApiService scmApiService;

    /**
     * 反馈包装数
     * <p>
     * 1.全部合格上架，合格包装数=上架数；
     * 2.包装问题让步接收上架，包装合格数=上架数-让步接收数；
     * 3.包装问题退货的，包装合格数=上架数
     * 4.其他问题的让步接收上架，包装合格数=上架数；
     * 5.其他问题导致的退货，包装合格数=上架数；
     *
     * @param stockinOrderTaskEntities
     */
    @Transactional
    public void processFeedbackPackageQty(List<StockinOrderTaskEntity> stockinOrderTaskEntities) {
        List<FeedbackPackageQtyRequest> packageQtyRequestList = new LinkedList<>();
        for (StockinOrderTaskEntity orderTaskEntity : stockinOrderTaskEntities) {
            List<StockinPackageQty> result = stockinOrderTaskItemService.getBaseMapper().getPackageQty(orderTaskEntity.getTaskId());
            if (CollectionUtils.isEmpty(result))
                continue;
            Map<String, List<StockinPackageQty>> collect = result.stream().collect(Collectors.groupingBy(item -> item.getPurchasePlanNo() + "_" + item.getSku()));
            List<FeedbackPackageQtyRequest.FeedbackPackageQtyItem> packageQtyRequestItemList = new LinkedList<>();
            collect.entrySet().stream().forEach(entry -> {
                List<StockinPackageQty> value = entry.getValue();
                StockinPackageQty stockinPackageQty = value.get(0);
                int sum = value.stream().mapToInt(item -> "包装问题".equals(item.getUnqualifiedCategory()) ? item.getShelvedQty() - item.getConcessionsCount() : item.getShelvedQty()).sum();
                if (sum <= 0)
                    return;
                packageQtyRequestItemList.add(new FeedbackPackageQtyRequest.FeedbackPackageQtyItem(stockinPackageQty.getSku(), stockinPackageQty.getPurchasePlanNo(), sum));
            });
            if (CollectionUtils.isEmpty(packageQtyRequestItemList))
                continue;
            FeedbackPackageQtyRequest feedbackPackageQtyRequest = new FeedbackPackageQtyRequest();
            feedbackPackageQtyRequest.setOperator(loginInfoService.getName());
            feedbackPackageQtyRequest.setSupplierDeliveryBoxCode(orderTaskEntity.getSupplierDeliveryBoxCode());
            feedbackPackageQtyRequest.setPackageQtyItemList(packageQtyRequestItemList);
            packageQtyRequestList.add(feedbackPackageQtyRequest);
        }
        if (!CollectionUtils.isEmpty(packageQtyRequestList)) {
            //保存包装数记录
            this.saveRecord(packageQtyRequestList);
            packageQtyRequestList.forEach(item -> {
                erpApiService.feedbackPackageQty(item);
            });
        }
    }

    private void saveRecord(List<FeedbackPackageQtyRequest> packageQtyRequestList) {
        LinkedList<StockinDeliveryBoxCodeDetailEntity> linkedList = new LinkedList<>();
        packageQtyRequestList.stream().forEach(request -> {
            linkedList.addAll(request.getPackageQtyItemList().stream().map(item -> {
                StockinDeliveryBoxCodeDetailEntity entity = new StockinDeliveryBoxCodeDetailEntity();
                entity.setLocation(TenantContext.getTenant());
                entity.setPackageQty(item.getPackageQty());
                entity.setPurchasePlanNo(item.getPurchasePlanNo());
                entity.setSku(item.getSku());
                entity.setSupplierDeliveryBoxCode(request.getSupplierDeliveryBoxCode());
                return entity;
            }).collect(Collectors.toList()));
        });
        if (!CollectionUtils.isEmpty(linkedList))
            stockinDeliveryBoxCodeDetailService.saveBatch(linkedList);
    }

    /**
     * 反馈让步接收数
     */
    public void processConcessionSyncErp(List<StockinOrderEntity> stockinOrderEntityList) {
        List<Integer> stockinOrderIds = stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());
        Map<Integer, StockinOrderEntity> entityMap = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderId, Function.identity()));
        List<StockinOrderItem> stockinOrderItems = stockinOrderItemService.getBaseMapper().getConcessionItem(stockinOrderIds);
        if (CollectionUtils.isEmpty(stockinOrderItems))
            return;
        stockinOrderItems.stream().collect(Collectors.groupingBy(StockinOrderItem::getStockinOrderId)).forEach((key, value) -> {
            StockinOrderEntity stockinOrderEntity = entityMap.get(key);
            FeedbackConcessionReceiveRequest request = new FeedbackConcessionReceiveRequest();
            request.setOperator(loginInfoService.getName());
            request.setSupplierDeliveryBoxCode(stockinOrderEntity.getSupplierDeliveryBoxCode());
            List<FeedbackConcessionReceiveItem> itemList = value.stream().map(stockinOrderItem -> {
                FeedbackConcessionReceiveItem feedbackConcessionReceiveItem = new FeedbackConcessionReceiveItem();
                feedbackConcessionReceiveItem.setConcessionPrice(stockinOrderItem.getConcessionPrice());
                feedbackConcessionReceiveItem.setConcessionQty(Math.min(stockinOrderItem.getConcessionsCount(), stockinOrderItem.getShelvedQty()));
                feedbackConcessionReceiveItem.setPurchasePlanNo(stockinOrderItem.getPurchasePlanNo());
                feedbackConcessionReceiveItem.setSku(stockinOrderItem.getSku());
                feedbackConcessionReceiveItem.setReceiveDate(stockinOrderItem.getCreateDate());
                return feedbackConcessionReceiveItem;
            }).collect(Collectors.toList());
            request.setConcessionReceiveItemList(itemList);
            erpApiService.feedbackConcessionReceive(request);
        });
    }

    public void syncPurchaseOrderToScm(List<StockinOrderEntity> stockinOrderEntityList) {
        stockinOrderEntityList.stream().forEach(stockinOrderEntity -> {
            PurchaseOrderRequest purchaseOrderRequest = new PurchaseOrderRequest();
            List<PurchaseOrderRequestItem> result = stockinOrderItemService.getBaseMapper().getPurchaseOrderRequest(stockinOrderEntity.getStockinOrderId());
            result.stream().forEach(item -> {
                if (Objects.isNull(item.getReceivingItemId()))
                    return;
                int packageQty = "包装问题".equals(item.getUnqualifiedCategory()) ? item.getReceiveQty() - item.getConcessionQty() : item.getReceiveQty();
                item.setPackageQty(Integer.valueOf(packageQty));
                //查询上架时间和erp仓库Id
                PurchaseOrderRequestItem shelveItem = stockinShelveTaskItemService.getBaseMapper().getPurchaseOrderRequest(stockinOrderEntity.getStockinOrderId(), item.getInternalBoxCode(), item.getPurchaseOrderPlanNo(), item.getSku());
                if (Objects.nonNull(shelveItem)) {
                    item.setShelfTime(shelveItem.getShelfTime());
                    item.setSpaceId(shelveItem.getSpaceId());
                }
            });
            purchaseOrderRequest.setItemList(result);
            purchaseOrderRequest.setSupplierId(stockinOrderEntity.getSupplierId());
            purchaseOrderRequest.setReceivingNo(stockinOrderEntity.getSupplierDeliveryBoxCode());

            if (StockinTypeEnum.OVER.name().equals(stockinOrderEntity.getStockinType())) {
                //工厂多发入库
                scmApiService.syncReceivingByOtherStockIn(purchaseOrderRequest);
            } else {
                scmApiService.syncPurchaseOrder(purchaseOrderRequest);
            }
        });
    }
}
