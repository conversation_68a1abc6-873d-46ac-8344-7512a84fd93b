
package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/18 11:42
 */
@ApiModel(value = "StockinWaitQaTaskPageResponse", description = "待质检任务Response")
public class StockinWaitQaTaskPageResponse {

    /**
     * TaskId
     */
    @ApiModelProperty("任务Id")
    private Integer taskId;
    /**
     * 内部箱号
     */
    @ApiModelProperty("内部箱号")
    private String internalBoxCode;
    /**
     * spu
     */
    @ApiModelProperty("商品编码")
    private String spu;
    /**
     * skc
     */
    @ApiModelProperty("颜色编码")
    private String skc;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String sku;
    /**
     * 入库数量
     */
    @ApiModelProperty("到货数量")
    private Integer arrivalCount;
    /**
     * 推荐质检数量
     */
    @ApiModelProperty("要求质检数量")
    private Integer qaQty;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 是否需要做产前样
     */
    @ApiModelProperty("是否需要做产前样 1是0否")
    private Integer needProductSample;
    /**
     * 是否新款
     */
    @ApiModelProperty("是否新款 1是0否")
    private Integer isNew;

    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stockinDate;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;

    @ApiModelProperty(value = "首单标签", name = "firstLabel")
    private String firstLabel;

    /**
     * 工艺版本号
     */
    @ApiModelProperty(value = "工艺版本号", name = "workmanshipVersion")
    private String workmanshipVersion;

    /**
     * 商品图片地址
     */
    @ApiModelProperty(value = "商品图片地址", name = "imageUrl")
    private String imageUrl;
    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    /**
     * 预览图地址
     */
    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    @ApiModelProperty(value = "标签属性", name = "labelAttributeNames")
    private String labelAttributeNames;

    /** 采购申请类型，1:缺货订单申请，2:定制申请，3:FBA发货申请，4:采购申请,5:正常采购,6:退货返工,7:开发申请,8:分公司申请,9:现货补单,10:市场补单 */
    @ApiModelProperty(value = "采购申请类型", name = "purchasingApplyType")
    private Integer purchasingApplyType;

    @ApiModelProperty("是否返工退货 1是0否")
    private Integer isReturnOrder;

    @ApiModelProperty(value = "QA：相同商品最近一次的质检员", name = "previousQaUserRealName")
    private String previousQaUserRealName;

    @ApiModelProperty(value = "WMS标签", name = "productTag")
    private List<String> productTag;

    @ApiModelProperty(value = "是否需要测量高度", name = "needHeight")
    private Integer needHeight;

    @ApiModelProperty(value = "是否需要测量重量", name = "needWeight")
    private Integer needWeight;

    @ApiModelProperty(value = "采购单号", name = "purchasePlanNos")
    private String purchasePlanNos;

    @ApiModelProperty(value = "商品id", name = "productId")
    private Integer productId;

    @ApiModelProperty(value = "尺码", name = "size")
    private String size;

    @ApiModelProperty(value = "箱内数", name = "boxQty")
    private Integer boxQty;

    public Integer getPurchasingApplyType() {
        return purchasingApplyType;
    }

    public void setPurchasingApplyType(Integer purchasingApplyType) {
        this.purchasingApplyType = purchasingApplyType;
    }

    public Integer getIsReturnOrder() {
        return isReturnOrder;
    }

    public void setIsReturnOrder(Integer isReturnOrder) {
        this.isReturnOrder = isReturnOrder;
    }

    public String getWorkmanshipVersion() {
        return workmanshipVersion;
    }

    public void setWorkmanshipVersion(String workmanshipVersion) {
        this.workmanshipVersion = workmanshipVersion;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getLabelAttributeNames() {
        return labelAttributeNames;
    }

    public void setLabelAttributeNames(String labelAttributeNames) {
        this.labelAttributeNames = labelAttributeNames;
    }

    public String getPreviousQaUserRealName() {
        return previousQaUserRealName;
    }

    public void setPreviousQaUserRealName(String previousQaUserRealName) {
        this.previousQaUserRealName = previousQaUserRealName;
    }

    public List<String> getProductTag() {
        return productTag;
    }

    public void setProductTag(List<String> productTag) {
        this.productTag = productTag;
    }

    public Integer getNeedHeight() {
        return needHeight;
    }

    public void setNeedHeight(Integer needHeight) {
        this.needHeight = needHeight;
    }

    public Integer getNeedWeight() {
        return needWeight;
    }

    public void setNeedWeight(Integer needWeight) {
        this.needWeight = needWeight;
    }

    public String getFirstLabel() {
        return firstLabel;
    }

    public void setFirstLabel(String firstLabel) {
        this.firstLabel = firstLabel;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getQaQty() {
        return qaQty;
    }

    public void setQaQty(Integer qaQty) {
        this.qaQty = qaQty;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getNeedProductSample() {
        return needProductSample;
    }

    public void setNeedProductSample(Integer needProductSample) {
        this.needProductSample = needProductSample;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPurchasePlanNos() {
        return purchasePlanNos;
    }

    public void setPurchasePlanNos(String purchasePlanNos) {
        this.purchasePlanNos = purchasePlanNos;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }
}
