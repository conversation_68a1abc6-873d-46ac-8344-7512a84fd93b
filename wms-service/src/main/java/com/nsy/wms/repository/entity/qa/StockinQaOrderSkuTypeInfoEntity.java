
package com.nsy.wms.repository.entity.qa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 质检任务标签内容
 *
 * <AUTHOR>
 * @Date 2024-11-18 15:58
 */
@Entity
@TableName("stockin_qa_order_sku_type_info")
@Table(name = "stockin_qa_order_sku_type_info")
public class StockinQaOrderSkuTypeInfoEntity extends BaseMpEntity {

    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 区域
     */
    private String location;
    /**
     * 质检单id
     */
    private Integer stockinQaOrderId;

    /**
     * 质检任务id
     */
    private Integer taskId;
    /**
     * 标签内容
     */
    private String skuType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getSkuType() {
        return skuType;
    }

    public void setSkuType(String skuType) {
        this.skuType = skuType;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }
}
