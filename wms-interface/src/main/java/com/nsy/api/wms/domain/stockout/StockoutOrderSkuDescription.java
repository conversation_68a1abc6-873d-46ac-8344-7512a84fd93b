package com.nsy.api.wms.domain.stockout;

public class StockoutOrderSkuDescription {

    private String sku;

    private String description;

    private Integer isFirstOrderByStore;

    private Integer isTransparency;

    //是否引流款
    private Boolean isLeadGeneration;

    /**
     * 增值任务
     */
    private String vasType;

    public Integer getIsTransparency() {
        return isTransparency;
    }

    public void setIsTransparency(Integer isTransparency) {
        this.isTransparency = isTransparency;
    }

    public Integer getIsFirstOrderByStore() {
        return isFirstOrderByStore;
    }

    public void setIsFirstOrderByStore(Integer isFirstOrderByStore) {
        this.isFirstOrderByStore = isFirstOrderByStore;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsLeadGeneration() {
        return isLeadGeneration;
    }

    public void setIsLeadGeneration(Boolean isLeadGeneration) {
        this.isLeadGeneration = isLeadGeneration;
    }

    public String getVasType() {
        return vasType;
    }

    public void setVasType(String vasType) {
        this.vasType = vasType;
    }
}
