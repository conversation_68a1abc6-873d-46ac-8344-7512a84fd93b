package com.nsy.wms.controller.stock;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stockin.StockinOrderItem;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipment;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockReturnSkuRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.InternalBoxExceptionRequest;
import com.nsy.api.wms.request.stockin.StockOrderStatusExceptionRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.wms.business.service.QRetryService;
import com.nsy.wms.business.service.StockoutOrderDeliverDateService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockExceptionService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stockout.StockoutPickingExceptionService;
import com.nsy.wms.business.service.stockout.StockoutShipmentAmazonRelationService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 仅技术人员调用，不对外开放接口
 * @author: caishaohui
 * @time: 2023/8/11 14:11
 */
@RestController
public class StockExceptionController {
    @Autowired
    StockoutPickingExceptionService stockoutPickingExceptionService;
    @Autowired
    StockService stockService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockExceptionService stockExceptionService;
    @Autowired
    StockoutOrderDeliverDateService stockoutOrderDeliverDateService;
    @Autowired
    StockoutShipmentAmazonRelationService stockoutShipmentAmazonRelationService;
    @Autowired
    private QRetryService qRetryService;

    // 清除内部箱库存
    @RequestMapping(value = "/stock-exception/clear-box", method = RequestMethod.POST)
    @ApiOperation(value = "清除内部箱库存", produces = "application/json")
    public void clearBox(@Valid @RequestBody StringListRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockoutPickingExceptionService.clearBox(request.getStringList());
    }

    // 修复库存
    @RequestMapping(value = "/stock-exception/update-stock", method = RequestMethod.POST)
    @ApiOperation(value = "修复库存", produces = "application/json")
    public void updateStock(@Valid @RequestBody List<StockUpdateRequest> request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockService.updateStockBatch(request);
    }

    // 清除拣货箱，返还原库位
    @RequestMapping(value = "/stockout-picking-task/clear-box", method = RequestMethod.POST)
    @ApiOperation(value = "清除拣货箱，返还原库位", produces = "application/json")
    public void clearPickingBox(@Valid @RequestBody StringListRequest request) {
        stockoutPickingExceptionService.clearPickingBox(request.getStringList(), "人工按钮清理");
    }

    // 拣货箱异常人工处理
    @RequestMapping(value = "/stockout-picking-task/deal-exception", method = RequestMethod.POST)
    @ApiOperation(value = "拣货箱异常人工处理", produces = "application/json")
    public void dealException(@Valid @RequestBody InternalBoxExceptionRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockoutPickingExceptionService.dealException(request);
    }

    // 归还内部箱数据（箱号+sku）
    @RequestMapping(value = "/stock-exception/return-sku", method = RequestMethod.POST)
    @ApiOperation(value = "归还内部箱数据", produces = "application/json")
    public void returnSku(@Valid @RequestBody StockReturnSkuRequest request) {
        stockExceptionService.returnSku(request);
    }

    // 修复入库单状态
    @RequestMapping(value = "/stock-exception/stockin-order/status", method = RequestMethod.POST)
    @ApiOperation(value = "修复入库单状态", produces = "application/json")
    public void updateStockinOrderStatus(@Valid @RequestBody StockOrderStatusExceptionRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockinOrderStatus(request);
    }

    // 修复出库单状态
    @RequestMapping(value = "/stock-exception/stockout-order/status", method = RequestMethod.POST)
    @ApiOperation(value = "修复出库单状态", produces = "application/json")
    public void updateStockoutOrderStatus(@Valid @RequestBody StockOrderStatusExceptionRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockoutOrderStatus(request);
    }


    // 清除发货库位记录表
    @RequestMapping(value = "/stock-exception/stock-shipping-position/clear", method = RequestMethod.POST)
    @ApiOperation(value = "清除发货库位记录表", produces = "application/json")
    public void clearStockShippingPosition(@Valid @RequestBody StringListRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.clearStockShippingPosition(request);
    }

    @ApiOperation(value = "发送kafka消息", notes = "发送kafka消息", produces = "application/json")
    @RequestMapping(value = "/stock-exception/kafka-send", method = RequestMethod.POST)
    public void sendToKafka(@Valid @RequestBody QMessage qMessage) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.sendMessage(qMessage);
    }

    // 修复上架任务
    @RequestMapping(value = "/stock-exception/stockin-shelve-task/status", method = RequestMethod.POST)
    @ApiOperation(value = "修复上架任务状态", produces = "application/json")
    public void updateStockinShelveTaskStatus(@Valid @RequestBody StockOrderStatusExceptionRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockinShelveTaskStatus(request);
    }


    // 修复上架任务明细
    @RequestMapping(value = "/stock-exception/stockin-shelve-task/item", method = RequestMethod.POST)
    @ApiOperation(value = "修复上架任务明细", produces = "application/json")
    public void updateStockinShelveTaskItem(@Valid @RequestBody StockinShelveTaskItemInfo request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockinShelveTaskItem(request);
    }

    // 修复入库单明细
    @RequestMapping(value = "/stock-exception/stockin-order-item/item", method = RequestMethod.POST)
    @ApiOperation(value = "修复入库单明细", produces = "application/json")
    public void updateStockinOrderItem(@Valid @RequestBody StockinOrderItem request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockinOrderItem(request);
    }

    // 修复入库任务明细
    @RequestMapping(value = "/stock-exception/stockin-order-task-item/item", method = RequestMethod.POST)
    @ApiOperation(value = "修复入库任务明细", produces = "application/json")
    public void updateStockinOrderTaskItem(@Valid @RequestBody StockinOrderTaskItemEntity request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockinOrderTaskItem(request);
    }


    // 初始化发货时间
    @RequestMapping(value = "/stock-exception/stockout-order/deliver-date/{fromId}/{lastId}", method = RequestMethod.PUT)
    @ApiOperation(value = "初始化发货时间", produces = "application/json")
    public void updatedeliverDate(@PathVariable("fromId") Integer fromId, @PathVariable("lastId") Integer lastId) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockoutOrderDeliverDateService.run(fromId, lastId);
    }

    //kafka消费失败重试
    @ApiOperation(value = "kafka消费失败重试", notes = "kafka消费失败重试", produces = "application/json")
    @RequestMapping(value = "/stock-exception/kafka-fail-retry", method = RequestMethod.POST)
    public void kafkaFailRetry(@Valid @RequestBody IdListRequest request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        qRetryService.kafkaFailRetry(request);
    }

    // 修复装箱清单
    @RequestMapping(value = "/stock-exception/stockout_shipment/fba_replenish_type", method = RequestMethod.POST)
    @ApiOperation(value = "修复装箱清单", produces = "application/json")
    public void updateStockoutShipment(@Valid @RequestBody StockoutShipment request) {
        if (!loginInfoService.isAdmin())
            throw new BusinessServiceException("仅管理员可调用");
        stockExceptionService.updateStockoutShipment(request);
    }
}
