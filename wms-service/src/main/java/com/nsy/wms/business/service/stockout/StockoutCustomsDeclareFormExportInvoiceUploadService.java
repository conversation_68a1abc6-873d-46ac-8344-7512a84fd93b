package com.nsy.wms.business.service.stockout;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.user.upload.StockoutCustomsDeclareFormExportInvoiceImport;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareDocumentMapper;
import com.nsy.wms.utils.JsonMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutCustomsDeclareFormExportInvoiceUploadService extends ServiceImpl<StockoutCustomsDeclareDocumentMapper, StockoutCustomsDeclareDocumentEntity> implements IProcessUploadDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareFormExportInvoiceUploadService.class);

    @Resource
    private StockoutCustomsDeclareFormService stockoutCustomsDeclareFormService;

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_CUSTOMS_DECLARE_FORM_EXPORT_INVOICE;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (StringUtils.isBlank(request.getDataJsonStr())) {
            throw new BusinessServiceException("上传数据为空");
        }
        List<StockoutCustomsDeclareFormExportInvoiceImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StockoutCustomsDeclareFormExportInvoiceImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            throw new BusinessServiceException("上传列表为空");
        }
        importList.forEach(importRow -> {
            try {
                importEachData(importRow);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                importRow.setErrorMsg(e.getMessage());
            }
        });

        response.setDataJsonStr(JsonMapper.toJson(importList.stream().filter(temp -> StrUtil.isNotEmpty(temp.getErrorMsg())).collect(Collectors.toList())));
        return response;
    }

    public void importEachData(StockoutCustomsDeclareFormExportInvoiceImport importRow) {
        LOGGER.info("出口发票导入 {} ", JSONUtil.toJsonStr(importRow));
        if (StrUtil.isEmpty(importRow.getProtocolNo()))
            throw new BusinessServiceException("【协议号】不能为空");
        if (StrUtil.isEmpty(importRow.getgNo()))
            throw new BusinessServiceException("【项号】不能为空");
        StockoutCustomsDeclareFormEntity formEntity = stockoutCustomsDeclareFormService.getByProtocolNoAndGNo(importRow.getProtocolNo(), importRow.getgNo());
        if (Objects.isNull(formEntity))
            throw new BusinessServiceException(String.format("关单不存在 %s，%s", importRow.getProtocolNo(), importRow.getgNo()));

        stockoutCustomsDeclareFormService.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormEntity>()
                .set(StockoutCustomsDeclareFormEntity::getExportInvoiceNo, importRow.getExportInvoiceNo())
                .set(StockoutCustomsDeclareFormEntity::getExportInvoiceDate, StrUtil.isEmpty(importRow.getExportInvoiceDate()) ? null : DateUtil.parse(importRow.getExportInvoiceDate(), "yyyy-MM-dd"))
                .eq(StockoutCustomsDeclareFormEntity::getgNo, importRow.getgNo())
                .eq(StockoutCustomsDeclareFormEntity::getDeclareFormId, formEntity.getDeclareFormId()));
    }

}
