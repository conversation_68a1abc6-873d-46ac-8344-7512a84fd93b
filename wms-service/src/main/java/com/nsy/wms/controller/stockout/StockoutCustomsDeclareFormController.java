package com.nsy.wms.controller.stockout;


import cn.hutool.core.date.DateUtil;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormItemResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormLogResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormResult;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormItemConfirmRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormItemUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormLogRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormMatchSupplierRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.common.CommonResponse;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.external.request.DeclareFormFetchRequest;
import com.nsy.wms.business.manage.external.response.DecMessage;
import com.nsy.wms.business.service.async.AsyncProcessFlowService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareAEOService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormFetchService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormItemService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormMQService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormMatchService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.utils.mp.TenantContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 关单(StockoutCustomsDeclareForm)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
@RestController
@Api(tags = "关单列表相关接口")
public class StockoutCustomsDeclareFormController extends BaseController {

    @Autowired
    StockoutCustomsDeclareFormService stockoutCustomsDeclareFormService;
    @Autowired
    StockoutCustomsDeclareFormItemService stockoutCustomsDeclareFormItemService;
    @Autowired
    StockoutCustomsDeclareFormMQService declareFormMQService;
    @Autowired
    StockoutCustomsDeclareFormFetchService declareFormFetchService;
    @Autowired
    StockoutCustomsDeclareFormMatchService formMatchService;
    @Resource
    StockoutCustomsDeclareAEOService declareAEOService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    AsyncProcessFlowService asyncProcessFlowService;

    @ApiOperation(value = "查询关单状态数量", notes = "查询关单状态数量", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/tab-count", method = RequestMethod.GET)
    public List<StatusCountResponse> tabCount() {
        return stockoutCustomsDeclareFormService.tabCount();
    }

    @ApiOperation(value = "查询关单列表", notes = "查询关单列表", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/list", method = RequestMethod.POST)
    public PageResponse<StockoutCustomsDeclareFormResult> pageList(@Valid @RequestBody StockoutCustomsDeclareFormSearchRequest request) {
        return stockoutCustomsDeclareFormService.pageSearchList(request);
    }

    @ApiOperation(value = "查询关单详细信息", notes = "查询关单详细信息", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/detail/{declareFormId}", method = RequestMethod.GET)
    public StockoutCustomsDeclareFormResult detailInfo(@PathVariable Integer declareFormId) {
        return stockoutCustomsDeclareFormService.detailInfo(declareFormId);
    }

    @ApiOperation(value = "关单详细信息更新", notes = "关单详细信息更新", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/detail/update", method = RequestMethod.POST)
    public void updateInfo(@Valid @RequestBody StockoutCustomsDeclareFormUpdateRequest request) {
        stockoutCustomsDeclareFormService.updateInfo(request);
    }

    @ApiOperation(value = "查询关单进项审核", notes = "查询关单进项审核", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/item/confirm", method = RequestMethod.POST)
    public void confirmItem(@RequestBody StockoutCustomsDeclareFormItemConfirmRequest request) {
        stockoutCustomsDeclareFormService.confirmItem(request);
    }

    @ApiOperation(value = "查询进项明细列表", notes = "查询进项明细列表", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/item/{declareFormId}", method = RequestMethod.GET)
    public List<StockoutCustomsDeclareFormItemResult> itemList(@PathVariable Integer declareFormId) {
        return stockoutCustomsDeclareFormItemService.itemResultList(declareFormId);
    }

    @ApiOperation(value = "更新进项明细", notes = "更新进项明细", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/item/update", method = RequestMethod.POST)
    public void updateItemList(@Valid @RequestBody StockoutCustomsDeclareFormItemUpdateRequest request) {
        stockoutCustomsDeclareFormItemService.updateItemList(request);
    }

    @ApiOperation(value = "更新税额", notes = "更新税额", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/item/update-tax", method = RequestMethod.POST)
    public void updateTax(@Valid @RequestBody StockoutCustomsDeclareFormItemUpdateRequest request) {
        stockoutCustomsDeclareFormItemService.updateTax(request);
    }

    @ApiOperation(value = "查询关单日志列表", notes = "查询关单日志列表", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/log/list", method = RequestMethod.POST)
    public PageResponse<StockoutCustomsDeclareFormLogResult> pageLogList(@Valid @RequestBody StockoutCustomsDeclareFormLogRequest request) {
        return stockoutCustomsDeclareFormService.pageSearchLogList(request);
    }

    @ApiOperation(value = "新增关单(内部)", notes = "新增关单(内部)", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/internal/add", method = RequestMethod.POST)
    public void addOrUpdateForm(@Valid @RequestBody DecMessage request) {
        declareFormMQService.addOrUpdateForm(request);
    }

    @ApiOperation(value = "拉取关单列表", notes = "拉取关单列表", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/fetch", method = RequestMethod.POST)
    public AsyncProcessFlowResult fetchData(@Valid @RequestBody DeclareFormFetchRequest request) {
        String time = DateUtil.formatDateTime(new Date());
        LocationWrapperMessage<DeclareFormFetchRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request);
        return asyncProcessFlowService.createFlow(KafkaConstant.DECLARE_FORM_FETCH_TOPIC, KafkaConstant.DECLARE_FORM_FETCH_MARK, message, time);
    }

    @ApiOperation(value = "手动匹配供应商", notes = "手动匹配供应商", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-form/match-supplier", method = RequestMethod.POST)
    public CommonResponse matchSupplier(@Valid @RequestBody StockoutCustomsDeclareFormMatchSupplierRequest supplierRequest) {
        return formMatchService.matchSupplierManualWithResp(supplierRequest);
    }

    @ApiOperation(value = "生成AEO", produces = "application/json")
    @PostMapping("/stockout-customs-declare-form/generate-aeo")
    public void generateAEO(@RequestBody IdListRequest request) {
        declareAEOService.generateFromForm(request.getIdList());
    }

    @ApiOperation(value = "批量自动匹配供应商", produces = "application/json")
    @PostMapping("/stockout-customs-declare-form/batch-match-supplier")
    public void batchMatchSupplier(@RequestBody IdListRequest request) {
        formMatchService.batchMatchSupplier(request.getIdList());
    }


    @ApiOperation(value = "删除关单", notes = "删除关单", produces = "application/json")
    @DeleteMapping("/stockout-customs-declare-form")
    public void delete(@RequestBody IdListRequest request) {
        stockoutCustomsDeclareFormService.delete(request);
    }

    @ApiOperation(value = "自动生成合同", produces = "application/json")
    @PostMapping("/stockout-customs-declare-form/auto-generate-contract")
    public AsyncProcessFlowResult autoGenerateContract() {
        String time = DateUtil.formatDateTime(new Date());
        LocationWrapperMessage<String> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), time);
        return asyncProcessFlowService.createFlow(KafkaConstant.DECLARE_CONTRACT_AUTO_GENERATE_TOPIC, KafkaConstant.DECLARE_CONTRACT_AUTO_GENERATE_MARK, message, time);
    }

    @ApiOperation(value = "关单审核", notes = "关单审核", produces = "application/json")
    @DeleteMapping("/stockout-customs-declare-form/audit")
    public void audit(@RequestBody IdListRequest request) {
        stockoutCustomsDeclareFormService.audit(request);
    }
}

