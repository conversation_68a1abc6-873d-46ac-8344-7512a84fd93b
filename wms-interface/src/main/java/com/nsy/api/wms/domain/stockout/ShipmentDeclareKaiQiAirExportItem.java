package com.nsy.api.wms.domain.stockout;

public class ShipmentDeclareKaiQiAirExportItem {

    String boxIndex;

    String fbaShipmentCode;

    String fbaShipmentId;

    String referenceId;

    String customsDeclareCn;

    String customsDeclareEn;

    String hsCode;

    String unitPrice;

    String qty;

    String qtyPrice;

    String brandName;

    String sku;

    String fabricType;

    String imageUrl;

    String boxWeight;

    String length;

    String width;

    String height;

    public String getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(String boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getFbaShipmentCode() {
        return fbaShipmentCode;
    }

    public void setFbaShipmentCode(String fbaShipmentCode) {
        this.fbaShipmentCode = fbaShipmentCode;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public String getCustomsDeclareCn() {
        return customsDeclareCn;
    }

    public void setCustomsDeclareCn(String customsDeclareCn) {
        this.customsDeclareCn = customsDeclareCn;
    }

    public String getCustomsDeclareEn() {
        return customsDeclareEn;
    }

    public void setCustomsDeclareEn(String customsDeclareEn) {
        this.customsDeclareEn = customsDeclareEn;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public String getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getQty() {
        return qty;
    }

    public void setQty(String qty) {
        this.qty = qty;
    }

    public String getQtyPrice() {
        return qtyPrice;
    }

    public void setQtyPrice(String qtyPrice) {
        this.qtyPrice = qtyPrice;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getBoxWeight() {
        return boxWeight;
    }

    public void setBoxWeight(String boxWeight) {
        this.boxWeight = boxWeight;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }
}
