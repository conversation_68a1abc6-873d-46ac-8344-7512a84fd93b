package com.nsy.wms.business.manage.gc;

import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.supplier.DeliveryBoxStickerDto;
import com.nsy.api.wms.domain.supplier.DeliveryCheckInfoDto;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.qa.ImportWalMartReplenishmentCheckRequest;
import com.nsy.api.wms.request.stock.StockPlatformScheduleSupplierAddRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.stockout.ImportWalMartReplenishmentCheckResponse;
import com.nsy.wms.business.manage.supplier.request.CreateSupplierDeliveryRequest;
import com.nsy.wms.business.manage.supplier.request.DeliveryOverShipmentRequest;
import com.nsy.wms.business.manage.supplier.request.PurchaseOrderItemQueryRequest;
import com.nsy.wms.business.manage.supplier.request.ReworkConfirmReceiptRequest;
import com.nsy.wms.business.manage.supplier.request.SyncReturnDeliveryRequest;
import com.nsy.wms.business.manage.supplier.request.SyncWaitReturnQtyToSupplierRequest;
import com.nsy.wms.business.manage.supplier.response.DeliveryOverShipmentResponse;
import com.nsy.wms.business.manage.supplier.response.DeliverySkuListResponse;
import com.nsy.wms.business.manage.supplier.response.PurchaseOrderItemResponse;
import com.nsy.wms.business.manage.supplier.response.PurchaseReturnItemNewOrderNoResponse;
import com.nsy.wms.business.service.external.ExternalApiAsyncQueueService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class GcApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GcApiService.class);

    @Autowired
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.gc}")
    private String apiUrl;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ExternalApiAsyncQueueService externalApiAsyncQueueService;


    /**
     * 沃尔玛补货前置校验
     *
     * @param request
     * @return
     */
    public ImportWalMartReplenishmentCheckResponse validImportWalMartReplenishment(ImportWalMartReplenishmentCheckRequest request) {
        try {

            String uri = String.format("%s/valid-import-wal-mart-replenishment", apiUrl);
            LOGGER.info(JsonMapper.toJson(request));
            ImportWalMartReplenishmentCheckResponse checkResponse = this.restTemplate.postForObject(uri, request, ImportWalMartReplenishmentCheckResponse.class);
            LOGGER.info(JsonMapper.toJson(checkResponse));
            return checkResponse;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 创建工厂出库单，并返回生成月台request
     *
     * @param request
     * @return
     */
    public StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier createVirtualSupplierDelivery(CreateSupplierDeliveryRequest request) {
        String uri = String.format("%s/create_virtual_supplier_delivery", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.CREATE_SUPPLIER_DELIVERY, uri,
            JsonMapper.toJson(request), request.getSupplierId().toString(), "wms创建虚拟工厂出库单");
        try {
            ResponseEntity<StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier> responseEntity = this.restTemplate.postForEntity(uri, request, StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(responseEntity), ExternalApiLogStatusEnum.SUCCESS);
            return responseEntity.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 删除工厂出库单
     *
     * @param supplierDeliveryNo
     * @return
     */
    public void deleteVirtualSupplierDelivery(String supplierDeliveryNo) {
        String uri = String.format("%s/delete_virtual_supplier_delivery/%s", apiUrl, supplierDeliveryNo);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.DELETE_SUPPLIER_DELIVERY, uri,
            "", supplierDeliveryNo, "wms删除虚拟工厂出库单");
        try {
            this.restTemplate.delete(uri);
            externalApiLogService.updateLog(apiLogEntity, "", ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 编辑已上架数，工厂多发处理
     *
     * @param request
     * @return
     */
    public DeliveryOverShipmentResponse deliveryOverShipment(DeliveryOverShipmentRequest request) {
        String uri = String.format("%s/rapid-production-delivery/update-or-add-supplier-delivery-item", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.DELIVERY_OVER_SHIPMENT, uri,
            JsonMapper.toJson(request), request.getSupplierDeliveryNo() + "-" + request.getSku(),
            String.format("编辑已上架数，工厂多发处理，出库箱码【%s】，sku【%s】", request.getSupplierDeliveryNo(), request.getSku()));
        try {
            ResponseEntity<DeliveryOverShipmentResponse> result = this.restTemplate.postForEntity(uri, request, DeliveryOverShipmentResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(result), ExternalApiLogStatusEnum.SUCCESS);
            return result.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    public List<DeliveryCheckInfoDto> getReceiveCheckInfo(List<String> supplierDeliveryNoList) {
        String uri = String.format("%s/delivery/get-receive-check-info", apiUrl);
        StringListRequest stringListRequest = new StringListRequest();
        stringListRequest.setStringList(supplierDeliveryNoList);
        ResponseEntity<List> responseEntity = this.restTemplate.postForEntity(uri, stringListRequest, List.class);
        List body = responseEntity.getBody();
        return JsonMapper.jsonStringToObjectArray(JsonMapper.toJson(body), DeliveryCheckInfoDto.class);
    }


    /**
     * 根据采购单号 返回待发货数大于0的sku列表
     *
     * @param purchasePlanNo
     * @return
     */
    public List<String> getDeliverySkuList(String purchasePlanNo) {
        String url = String.format("%s/rapid-production-delivery/get-sku-list-by-order-no", apiUrl);
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("orderNo", purchasePlanNo);
        DeliverySkuListResponse responses = this.restTemplate.postForObject(url, map, DeliverySkuListResponse.class);
        return Objects.isNull(responses) ? Collections.emptyList() : responses.getSkuList();
    }

    /**
     * 同步待退货数
     *
     * @param request
     */
    public void syncWaitReturnQty(SyncWaitReturnQtyToSupplierRequest request) {
        String uri = String.format("%s/purchase-order-item/supplier/sync_wait_return_qty", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_WAIT_RETURN_QTY, uri,
            JsonMapper.toJson(request), request.getPurchasePlanNo(), "同步待退货数到vms");
        //保存到异步调用队列
        externalApiAsyncQueueService.add(apiLogEntity, request.getPurchasePlanNo());
    }

    // 根据新采购单号获取WmsReturnTaskItemId
    public List<Integer> getWmsReturnTaskItemIdList(String newOrderNo) {
        if (!StringUtils.hasText(newOrderNo)) {
            return Lists.newArrayList();
        }
        String url = String.format("%s/purchase-return/WmsReturnTaskItemIdList/%s", apiUrl, newOrderNo);
        try {
            Integer[] responses = this.restTemplate.getForObject(url, Integer[].class);
            return Objects.isNull(responses) ? Lists.newArrayList() : Arrays.asList(responses);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    // 根据WmsReturnTaskId获取新采购单号
    public List<PurchaseReturnItemNewOrderNoResponse.Item> getNewPurchaseNo(List<Integer> returnTaskIdList) {
        if (CollectionUtils.isEmpty(returnTaskIdList)) {
            return Lists.newArrayList();
        }
        IdListRequest idListRequest = new IdListRequest();
        idListRequest.setIdList(returnTaskIdList);
        String url = String.format("%s/purchase-return/get-new-order-no", apiUrl);
        try {
            ResponseEntity<PurchaseReturnItemNewOrderNoResponse> responseEntity = this.restTemplate.postForEntity(url, idListRequest, PurchaseReturnItemNewOrderNoResponse.class);
            PurchaseReturnItemNewOrderNoResponse response = responseEntity.getBody();
            return Objects.nonNull(response) && response.getItemList() != null ? response.getItemList() : Lists.newArrayList();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return Lists.newArrayList();
    }

    // 返工退货确认收货
    public void reworkConfirmReceipt(List<Integer> returnTaskItemIdList) {
        ReworkConfirmReceiptRequest request = new ReworkConfirmReceiptRequest();
        request.setIds(returnTaskItemIdList);
        request.setFromWms(Boolean.TRUE);
        String url = String.format("%s/factory/purchase-return-item/confirm", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.REWORK_CONFIRM_RECEIPT, url,
            JsonMapper.toJson(request), returnTaskItemIdList.toString(), "退货单强制完成触发返工收货");
        try {
            this.restTemplate.postForEntity(url, request, String.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 更新发货信息
     *
     * @param request
     * @return false没更新成功=》表示退货单还未同步到supplier，此时要更新erp的退货单
     */
    public Boolean syncReturnDeliveryToVms(SyncReturnDeliveryRequest request) {
        String url = String.format("%s/purchase-return/update-return-delivery", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_RETURN_DELIVERY_DATE, url,
            JsonMapper.toJson(request), request.getReturnTaskIdList().toString(), "同步退货单发货时间到vms");
        try {
            ResponseEntity<Boolean> result = this.restTemplate.postForEntity(url, request, Boolean.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
            return result.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    public List<DeliveryBoxStickerDto> deliveryPrintBoxStickerBySku(String supplierDeliveryNo, Integer boxIndex) {
        String uri = String.format("%s/delivery-print-box-sticker-by-sku/%s/%s", apiUrl, supplierDeliveryNo, boxIndex);
        ResponseEntity<List> responseEntity = this.restTemplate.getForEntity(uri, List.class);
        return responseEntity.getBody();
    }

    public List<PurchaseOrderItemResponse> getPurchaseOrderItemByInfo(List<String> purchaseNumberList, String sku) {
        try {
            String url = String.format("%s/purchase-order-item/query-by-purchase-no-and-sku", apiUrl);
            PurchaseOrderItemQueryRequest request = new PurchaseOrderItemQueryRequest();
            request.setPurchaseNumberList(purchaseNumberList);
            request.setSku(sku);
            String responseStr = this.restTemplate.postForObject(url, request, String.class);
            return objectMapper.readValue(responseStr, new PurchaseOrderItemResponseReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    private static final class PurchaseOrderItemResponseReference extends TypeReference<ArrayList<PurchaseOrderItemResponse>> {
    }
}
