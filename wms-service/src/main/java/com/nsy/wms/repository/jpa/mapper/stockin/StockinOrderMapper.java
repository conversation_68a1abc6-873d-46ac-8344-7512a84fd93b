package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stockin.StockinOrderDetailExport;
import com.nsy.api.wms.domain.stockin.StockinOrderListInfo;
import com.nsy.api.wms.domain.stockin.StockinOrderStatisticsResponse;
import com.nsy.api.wms.domain.stockin.StockinOrderStatusDTO;
import com.nsy.api.wms.request.stockin.StockinOrderListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptRecordListRequest;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.stockin.StockinOrderInfoResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptItemListInfo;
import com.nsy.api.wms.response.stockin.StockinReceiptListInfo;
import com.nsy.api.wms.response.stockin.StockinReceiptRecordListResponse;
import com.nsy.permission.annatation.Permission;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 入库单Mapper
 * @date: 2021-07-26 15:37
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinOrderMapper extends BaseMapper<StockinOrderEntity> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockinOrderListInfo> pageSearchList(IPage page, @Param("query") StockinOrderListRequest request);

    IPage<StockinOrderDetailExport> pageStockinOrderItemList(IPage page, @Param("query") StockinOrderListRequest request);

    StockinOrderStatisticsResponse statisticsStockinOrderItemList(@Param("query") StockinOrderListRequest request);

    Integer pageSearchListCount(@Param("query") StockinOrderListRequest request);

    List<StockinOrderStatusDTO> statusCount();

    List<StockinOrderInfoResponse> searchByStockinOrderIdList(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinReceiptRecordListResponse> pageSearchReceiptRecordList(IPage page, @Param("query") StockinReceiptRecordListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    @Permission
    List<StockinReceiptListInfo> pageSearchReceiptList(IPage page, @Param("query") StockinReceiptListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    @Permission
    List<StockinReceiptListInfo> pageItemSearchReceiptList(IPage page, @Param("query") StockinReceiptListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    @Permission
    List<StockinReceiptItemListInfo> listSearchReceiptItemList(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Integer pageSearchReceiptListCount(@Param("query") StockinReceiptListRequest request);

    @Permission
    List<StatusTabResponse> getTabs();

    @InterceptorIgnore(tenantLine = "true")
    StockinOrderEntity findTopByLogisticsNoIgnoreTenant(@Param("logisticsNo") String logisticsNo);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderEntity> searchByRealStockinOrderIdListIgnoreTenant(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderEntity> searchByStockinOrderIdListIgnoreTenant(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<String> searchStockinOrderNoByStockinOrderIdListIgnoreTenant(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    @InterceptorIgnore(tenantLine = "true")
    Integer deleteByStockinOrderIdIgnoreTenant(@Param("stockinOrderId") Integer stockinOrderId);

    @InterceptorIgnore(tenantLine = "true")
    void updateIgnoreTenant(StockinOrderEntity stockinOrderEntity);

    @InterceptorIgnore(tenantLine = "true")
    StockinOrderEntity findTopByStockinOrderIdIgnoreTenant(@Param("stockinOrderId") Integer stockinOrderId);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderEntity> searchByStockinTaskIdListIgnoreTenant(@Param("stockinOrderTaskIdList") List<Integer> stockinOrderTaskIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderEntity> findTopBySupplierDeliveryBoxCodeIgnoreTenant(@Param("supplierDeliveryBoxCode") String supplierDeliveryBoxCode);

    Long countStockinOrderItemList(@Param("query") StockinOrderListRequest downloadRequest);

    StockPlatformScheduleEntity getPlatformScheduleInfoByStockinOrderId(@Param("stockinOrderId") Integer stockinOrderId);

    List<Integer> findIdBySupplierDeliveryNo(@Param("supplierDeliveryNo") String supplierDeliveryNo);

    /**
     * 统计入库收货列表数据
     *
     * @param request 查询条件
     * @return 统计结果
     */
    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Integer statisticsShipmentQty(@Param("query") StockinReceiptListRequest request);

    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Map<String, BigDecimal> statisticsShelveInfo(@Param("query") StockinReceiptListRequest request);

    /**
     * 统计需退货总件数
     *
     * @param request 查询条件
     * @return 需退货总件数
     */
    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Integer statisticsWaitReturnQtyTotal(@Param("query") StockinReceiptListRequest request);

    /**
     * 统计已退货总件数
     *
     * @param request 查询条件
     * @return 已退货总件数
     */
    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Integer statisticsRealWaitReturnQtyTotal(@Param("query") StockinReceiptListRequest request);


    /**
     * 统计需退货总件数
     *
     * @param request 查询条件
     * @return 需退货总件数
     */
    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Integer statisticsReturnedQtyTotal(@Param("query") StockinReceiptListRequest request);

    /**
     * 统计已退货总件数
     *
     * @param request 查询条件
     * @return 已退货总件数
     */
    @Permission
    @InterceptorIgnore(tenantLine = "true")
    Integer statisticsRealReturnedQtyTotal(@Param("query") StockinReceiptListRequest request);
}
