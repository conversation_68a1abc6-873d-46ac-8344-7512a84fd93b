<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskItemMapper">

    <select id="searchItemListByInternalBoxCodeAndStatus"
            resultType="com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo">
        select st.shelve_task_item_id as shelveTaskItemId,
        s.task_type as taskType,
        s.space_id as spaceId,
        s.space_area_id as spaceAreaId,
        st.shelve_task_id as shelveTaskId,
        st.source_id as sourceId,
        st.sku as sku,
        st.seller_sku as sellerSku,
        st.seller_barcode as sellerBarcode,
        ps.barcode as barcode,
        ps.preview_image_url as previewImageUrl,
        st.spec_id as specId,
        st.status as status,
        st.stockin_qty as stockinQty,
        st.returned_qty as returnedQty,
        st.shelved_qty as shelvedQty,
        st.position_code as positionCode,
        st.purchase_plan_no as purchasePlanNo,
        st.brand_name as brandName,
        st.is_fba_quick as isFbaQuick
        from stockin_shelve_task_item st
        left join stockin_shelve_task s on st.shelve_task_id = s.shelve_task_id
        left join product_spec_info ps on st.spec_id = ps.spec_id
        <where>
            <if test="internalBoxCode!=null and internalBoxCode!=''">
                and s.internal_box_code = #{internalBoxCode}
            </if>
            <if test="statusList!=null and statusList.size() > 0">
                and s.status in
                <foreach collection="statusList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findShelveTaskItemList"
            resultType="com.nsy.api.wms.response.projection.StockinShelveTaskItemProjection">
        select st.shelve_task_item_id as shelveTaskItemId, ps.thumbnail_image_url as thumbnailImageUrl, p.spu as spu,
        ps.sku as sku, st.status as status, ps.preview_image_url as previewImageUrl, st.stockin_qty as stockinQty,
        st.returned_qty as returnedQty, st.shelved_qty as shelvedQty,
        st.position_code as positionCode, st.operator as operator, st.is_fba_quick as isFbaQuick,
        st.brand_name as brandName,
        s.task_type as taskType,st.source_id as sourceId,
        st.operator_date as operatorDate,
        ps.color as color, ps.size as size,
        oi.create_date as stockinDate,
        oi.unqualified_category as unqualifiedCategory
        from stockin_shelve_task_item st
        left join product_spec_info ps on st.spec_id = ps.spec_id
        left join product_info p on p.product_id = ps.product_id
        left join stockin_shelve_task s on st.shelve_task_id = s.shelve_task_id
        left join stockin_order_item oi on (oi.stockin_order_id = st.source_id and oi.internal_box_code =
        s.internal_box_code and oi.sku = st.sku and oi.purchase_plan_no = st.purchase_plan_no)
        <where>
            <if test="query.shelveTaskId!=null and query.shelveTaskId!=''">
                and st.shelve_task_id = #{query.shelveTaskId}
            </if>
            <if test="query.spu!=null and query.spu!=''">
                and p.spu like concat(#{query.spu},'%')
            </if>
            <if test="query.sku!=null and query.sku!=''">
                and ps.sku like concat(#{query.sku},'%')
            </if>
            <if test="query.barcode!=null and query.barcode!=''">
                and st.barcode = #{query.barcode}
            </if>
            <if test="query.brandName!=null and query.brandName!=''">
                and st.brand_name = #{query.brandName}
            </if>
            <if test="query.status!=null and query.status!=''">
                and st.status = #{query.status}
            </if>
            <if test="query.positionCode!=null and query.positionCode!=''">
                and st.position_code like concat(#{query.positionCode},'%')
            </if>
        </where>
    </select>

    <select id="searchShelveTaskItemList" resultType="com.nsy.api.wms.domain.stockin.ShelveTaskItemInfo">
        select
        s.shelve_task_id as shelveTaskId,
        s.status as status,
        st.source_id as sourceId,
        st.shelve_task_item_id as shelveTaskItemId,
        st.sku as sku,
        st.purchase_plan_no as purchasePlanNo,
        st.stockin_qty as stockinQty,st.returned_qty as returnedQty,
        st.shelved_qty as shelvedQty,
        st.operator_date as shelvedDate
        from stockin_shelve_task_item st
        left join stockin_shelve_task s on st.shelve_task_id = s.shelve_task_id
        <where>
            <if test="sourceIdList!=null and sourceIdList.size() > 0">
                and st.source_id in
                <foreach collection="sourceIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskType!=null and taskType !=''">
                and s.task_type = #{taskType}
            </if>
        </where>
    </select>

    <select id="find32HourUnshelvedSupplierDeliveryNoList" resultType="java.lang.String">
        SELECT
        DISTINCT sot.supplier_delivery_no
        FROM
        stockin_shelve_task sst
        LEFT JOIN stockin_shelve_task_item ssti ON sst.shelve_task_id = ssti.shelve_task_id
        LEFT JOIN stockin_order so ON so.stockin_order_id = ssti.source_id
        LEFT JOIN stockin_order_task sot ON sot.task_id = so.task_id
        WHERE
        sst.`status` IN ( "PENDING", "SHELVING" )
        AND sst.task_type = "STOCKIN_SHELVE"
        AND sst.location = "QUANZHOU"
        AND sst.create_date &lt;= DATE_SUB(
        CURDATE(),
        INTERVAL 20 HOUR
        )
        and so.status != "COMPLETED"
    </select>

    <select id="queryShelveRecordGroupByStockinOrderId"
            resultType="com.nsy.api.wms.domain.stockin.StockShelveQtyByStockinOrderIdInfo">
        SELECT source_id as stockinOrderId,sum(shelved_qty) as shelvedQty,GROUP_CONCAT(DISTINCT operator) as operator
        FROM stockin_shelve_task_item
        WHERE shelved_qty > 0
        <if test="request.purchasePlanNo !=null and request.purchasePlanNo !=''">
            and purchase_plan_no = #{request.purchasePlanNo}
        </if>
        <if test="request.sku !=null and request.sku !=''">
            and sku = #{request.sku}
        </if>
        GROUP BY source_id
    </select>

    <select id="queryLatestLatestPutByPurchasePlanNo"
            resultType="com.nsy.api.wms.response.stockin.QueryPurchaseOrderLatestPutDateResponse">
        SELECT
        ti.purchase_plan_no,
        MAX( ti.operator_date ) AS latestPutDate
        FROM stockin_shelve_task t
        INNER JOIN stockin_shelve_task_item ti ON ti.shelve_task_id = t.shelve_task_id
        <where>
            <if test="request.purchasePlanNoList != null and request.purchasePlanNoList.size() > 0">
                ti.purchase_plan_no in
                <foreach collection="request.purchasePlanNoList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        ti.purchase_plan_no
    </select>

    <select id="queryExportData" resultType="com.nsy.api.wms.domain.stockin.StockinShelveTaskItemExport">
        select
        t.internal_box_code,
        ti.sku,
        ot.supplier_delivery_no,
        ti.stockin_qty,
        CAST(ti.stockin_qty AS signed) - CAST(ti.shelved_qty AS signed) - CAST(ti.returned_qty AS signed) as pendingQty,
        ti.returned_qty,
        ti.shelved_qty,
        oi.status as status,
        oi.create_date as stockinDate,
        oi.unqualified_category as unqualifiedCategory,
        ti.position_code,
        ti.operator,
        ti.operator_date,
        a.area_name,
        o.supplier_name,
        t.internal_box_code as internalBoxCode
        from stockin_shelve_task_item ti
        left join stockin_shelve_task t on t.shelve_task_id = ti.shelve_task_id
        left join stockin_order o on ti.source_id = o.stockin_order_id
        left JOIN stockin_order_task ot on o.task_id = ot.task_id
        left join stockin_order_item oi on (oi.stockin_order_id = o.stockin_order_id and oi.internal_box_code =
        t.internal_box_code and oi.sku = ti.sku and oi.purchase_plan_no = ti.purchase_plan_no)
        left join bd_position bp on bp.position_code = ti.position_code
        left join bd_space_area sa on t.space_area_id = sa.space_area_id
        left join bd_area a on sa.area_id = a.area_id
        left join stock_internal_box sib ON t.internal_box_id = sib.internal_box_id
        <include refid="itemCondition"></include>
        order by t.shelve_task_id
    </select>

    <select id="queryExportDataByDate" resultType="com.nsy.api.wms.domain.stockin.StockinShelveTaskItemMonthExport">
        select
        t.internal_box_code,
        ti.sku,
        ot.supplier_delivery_no,
        o.supplier_delivery_box_code,
        ti.purchase_plan_no,
        ti.stockin_qty,
        CAST(ti.stockin_qty AS signed) - CAST(ti.shelved_qty AS signed) - CAST(ti.returned_qty AS signed) as pendingQty,
        ti.returned_qty,
        ti.shelved_qty,
        oi.status as status,
        oi.create_date as stockinDate,
        oi.unqualified_category as unqualifiedCategory,
        ti.position_code,
        ti.operator,
        ti.operator_date,
        o.supplier_name
        from stockin_shelve_task_item ti
        left join stockin_shelve_task t on t.shelve_task_id = ti.shelve_task_id
        left join stockin_order o on ti.source_id = o.stockin_order_id
        left JOIN stockin_order_task ot on o.task_id = ot.task_id
        left join stockin_order_item oi on (oi.stockin_order_id = o.stockin_order_id and oi.internal_box_code = t.internal_box_code and oi.sku = ti.sku and oi.purchase_plan_no = ti.purchase_plan_no)
        where ti.operator_date BETWEEN #{startDate} and #{endDate}
        order by t.shelve_task_id
    </select>

    <select id="findAllByReceiptSummaryRequestIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity">
        select i.* from stockin_shelve_task t
        inner join stockin_shelve_task_item i on i.shelve_task_id = t.shelve_task_id
        <if test="request!=null and request.spu != null and request.spu !=''">
            INNER JOIN product_info p ON p.product_id = i.product_id
        </if>
        WHERE i.shelved_qty > 0
        <if test="request!=null and request.stockinOrderIdList != null and request.stockinOrderIdList.size() > 0">
            and i.source_id in
            <foreach collection="request.stockinOrderIdList" separator="," item="stockinOrderId" open="(" close=")">
                #{stockinOrderId}
            </foreach>
        </if>
        <if test="request!=null and request.purchaseNo !=null and request.purchaseNo !=''">
            and i.purchase_plan_no = #{request.purchaseNo}
        </if>
        <if test="request!=null and request.sku !=null and request.sku !=''">
            and i.sku = #{request.sku}
        </if>
        <if test="request!=null and request.spu !=null and request.spu !=''">
            and p.spu = #{request.spu}
        </if>
        <if test="request!=null and request.spaceId !=null">
            and t.space_id = #{request.spaceId}
        </if>

    </select>
    <select id="queryAllWaitUpShelvesInfo"
            resultType="com.nsy.api.wms.response.stockin.QueryAllWaitShelveInfoResponse">
        SELECT
        ssti.purchase_plan_no,
        ssti.sku,
        sum( CASE WHEN ssti.shelved_qty + ssti.returned_qty > ssti.stockin_qty THEN 0 ELSE ssti.stockin_qty -
        ssti.shelved_qty - ssti.returned_qty END ) AS waitUpShelfQty
        FROM
        stockin_shelve_task sst
        LEFT JOIN stockin_shelve_task_item ssti ON sst.shelve_task_id = ssti.shelve_task_id
        WHERE
        ssti.purchase_plan_no != ''
        AND sst.STATUS IN
        <foreach collection="statusList" separator="," item="status" open="(" close=")">
            #{status}
        </foreach>
        and ssti.status IN
        <foreach collection="statusList" separator="," item="status" open="(" close=")">
            #{status}
        </foreach>
        GROUP BY
        ssti.purchase_plan_no,
        ssti.sku
        HAVING
        waitUpShelfQty > 0
    </select>
    <select id="getShelveDetail" resultType="com.nsy.api.wms.domain.stockin.StockinShelveDetailExport">
        select
         o.supplier_id,
        o.supplier_name,
        o.stockin_order_no,
        o.supplier_delivery_box_code,
        ti.purchase_plan_no,
        bs.space_name,
        ti.sku,
        ti.shelved_qty as shelveQty,
        ti.operator_date as operateDate
        from stockin_shelve_task_item ti
        INNER JOIN stockin_shelve_task t on t.shelve_task_id = ti.shelve_task_id
        INNER JOIN stockin_order o on ti.source_id = o.stockin_order_id
        INNER join stockin_order_item oi on
        (ti.source_id = oi.stockin_order_id
        and oi.internal_box_code = t.internal_box_code
        and ti.spec_id = oi.spec_id
        and ti.purchase_plan_no = oi.purchase_plan_no
        )
        INNER JOIN bd_space bs on oi.space_id = bs.space_id
        where ti.status = 'SHELVED'
        and ti.operator_date between #{request.shelvedStartDate} and #{request.shelvedEndDate}

    </select>

    <select id="countShelvedQtyTotalByStockinOrderId"
            resultType="com.nsy.api.wms.domain.stockin.StockInShelveQtySumDTO">
        select ssti.seller_sku as sellerSku,ssti.sku as sku,sum(shelved_qty) as shelvedQtyTotal from
        stockin_shelve_task_item ssti
        where ssti.status = 'SHELVED'
        and ssti.source_id in
        <foreach collection="stockinOrderIdList" separator="," item="stockinOrderId" open="(" close=")">
            #{stockinOrderId}
        </foreach>
        GROUP BY ssti.seller_sku,ssti.sku
    </select>

    <select id="countNotShelvedTask" resultType="java.lang.Integer">
        select count(*) from stockin_shelve_task_item ti
		INNER JOIN stockin_order o on ti.source_id = o.stockin_order_id
		inner join stockin_order_task sot on sot.task_id = o.task_id
		inner join stockin_shelve_task t on t.shelve_task_id = ti.shelve_task_id
		where sot.supplier_delivery_no = #{supplierDeliveryNo}  and t.`status` in ('PENDING','SHELVING')
    </select>


    <select id="getSupplierDeliveryNoByTaskId" resultType="java.lang.String">
        select DISTINCT sot.supplier_delivery_no from stockin_shelve_task t
		inner join stockin_shelve_task_item ti  on t.shelve_task_id = ti.shelve_task_id
		inner JOIN stockin_order o on ti.source_id = o.stockin_order_id
		inner join stockin_order_task sot on sot.task_id = o.task_id
		where t.shelve_task_id = #{shelveTaskId} limit 1
    </select>

    <select id="queryLatestPutInfoByPurchasePlanNoAndSku"
            resultType="com.nsy.api.wms.response.stockin.StockInShelveTaskLatestPutInfoResponse">
        SELECT ti.purchase_plan_no as purchasePlanNo, ti.sku as sku, MAX(ti.operator_date) AS latestPutDate
        FROM stockin_shelve_task t INNER JOIN stockin_shelve_task_item ti ON ti.shelve_task_id = t.shelve_task_id
        WHERE (ti.purchase_plan_no, ti.sku) IN
        <foreach collection="request" separator="," item="info" open="(" close=")">
            (#{info.purchasePlanNo},#{info.sku})
        </foreach>
        GROUP BY ti.purchase_plan_no, ti.sku
    </select>

    <select id="getShelvedStatisticsInfo" resultType="com.nsy.api.wms.response.stockin.ShelveTaskStatisticsResponse">
        select
        COALESCE(sum(ti.shelved_qty), 0)  as shelvedTotalQty,
        COALESCE(sum(ti.stockin_qty ), 0) - COALESCE(sum(ti.shelved_qty), 0) - COALESCE(sum(ti.returned_qty), 0) as pendingTotalQty
        from stockin_shelve_task_item ti
        left join stockin_shelve_task t on t.shelve_task_id = ti.shelve_task_id
        left join stockin_order o on ti.source_id = o.stockin_order_id
        left JOIN stockin_order_task ot on o.task_id = ot.task_id
        left join stockin_order_item oi on (oi.stockin_order_id = o.stockin_order_id and oi.internal_box_code =
        t.internal_box_code and oi.sku = ti.sku and oi.purchase_plan_no = ti.purchase_plan_no)
        left join bd_position bp on bp.position_code = ti.position_code
        left join bd_space_area sa on t.space_area_id = sa.space_area_id
        left join bd_area a on sa.area_id = a.area_id
        LEFT JOIN stock_internal_box sib ON t.internal_box_id = sib.internal_box_id
        <include refid="itemCondition"></include>
    </select>

    <sql id="itemCondition">
        <where>
            <if test="request.stockinShelvedIdList != null and request.stockinShelvedIdList.size() > 0">
                ti.shelve_task_id in
                <foreach collection="request.stockinShelvedIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.internalBoxCode!=null and request.internalBoxCode!=''">
                and t.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.stockinOrderNo!=null and request.stockinOrderNo!=''">
                and o.stockin_order_No = #{request.stockinOrderNo}
            </if>
            <if test="request.sku!=null and request.sku!=''">
                and ti.sku like concat(#{request.sku}, '%')
            </if>
            <if test="request.supplierDeliveryBoxCode!=null and request.supplierDeliveryBoxCode!=''">
                and ot.supplier_delivery_box_code = #{request.supplierDeliveryBoxCode}
            </if>
            <if test="request.supplierDeliveryNo!=null and request.supplierDeliveryNo!=''">
                and ot.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>
            <if test="request.statusList !=null and request.statusList.size() > 0">
                and t.status in
                <foreach collection="request.statusList" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.stockinTypeList!=null and request.stockinTypeList.size() > 0">
                and ot.stockin_type in
                <foreach collection="request.stockinTypeList" separator="," index="index" item="stockinType" open="("
                         close=")">
                    #{stockinType}
                </foreach>
            </if>
            <if test="request.areaId!=null and request.areaId > 0 ">
                and bp.area_id = #{request.areaId}
            </if>
            <if test="request.taskType!=null and request.taskType!=''">
                and t.task_type = #{request.taskType}
            </if>
            <if test="request.operateStartDate!=null">
                and t.operate_start_date &gt;= #{request.operateStartDate}
            </if>
            <if test="request.operateEndDate!=null">
                and t.operate_start_date &lt;= #{request.operateEndDate}
            </if>
            <if test="request.taskStartDate != null">
                and t.create_date &gt;= #{request.taskStartDate}
            </if>
            <if test="request.taskEndDate != null">
                and t.create_date &lt;= #{request.taskEndDate}
            </if>
            <if test="request.spaceAreaId!=null and request.spaceAreaId!=''">
                and t.space_area_id = #{request.spaceAreaId}
            </if>
            <if test="request.hasBrand!=null">
                and t.has_brand = #{request.hasBrand}
            </if>
            <if test="request.noContainsWaitQc!=null and request.noContainsWaitQc == true">
                 and sib.status != 'WAIT_QC'
            </if>
            <trim prefix="having" prefixOverrides="and">
                <if test="request.shouldWaitPurchaseConfirm != null">
                    <if test="request.shouldWaitPurchaseConfirm">
                        and EXISTS (select 1 from stock_internal_box_item sibi where sibi.status in
                        ('WAIT_PURCHASE_CONFIRM','QC_PROCESSING','WAIT_DEAL') and sibi.internal_box_code =
                        internalBoxCode limit 1)
                    </if>
                    <if test="!request.shouldWaitPurchaseConfirm">
                        and not EXISTS (select 1 from stock_internal_box_item sibi where sibi.status in
                        ('WAIT_PURCHASE_CONFIRM','QC_PROCESSING','WAIT_DEAL') and sibi.internal_box_code =
                        internalBoxCode limit 1)
                    </if>
                </if>
            </trim>
        </where>
    </sql>
    <select id="findMinShelveDateByProductId"
                  resultType="com.nsy.api.wms.response.stockin.StockinReceiptShelveDateResponse">
        select st.product_id,
               min(st.operator_date) as shelvedDate
        from stockin_shelve_task_item st
                 inner join stockin_shelve_task s on st.shelve_task_id = s.shelve_task_id
        where st.product_id in
        <foreach collection="productIds" open="(" close=")" separator="," item="productId">
            #{productId}
        </foreach>
          and s.task_type = 'STOCKIN_SHELVE'
        GROUP BY st.product_id
    </select>
    <select id="getPurchaseOrderRequest"
            resultType="com.nsy.wms.business.manage.scm.request.PurchaseOrderRequestItem">
        select
            sti.operator_date as shelfTime,
            m.erp_space_id as spaceId
            from stockin_shelve_task_item sti
        inner join stockin_shelve_task t
        on t.shelve_task_id = sti.shelve_task_id
        left join bd_position p
        on p.position_code = sti.position_code
        left join bd_erp_space_mapping m
        on p.area_id = m.area_id
        where t.internal_box_code = #{internalBoxCode}
        and sti.source_id = #{stockinOrderId}
        and sti.purchase_plan_no = #{purchaseOrderPlanNo}
        and sti.sku = #{sku}
        limit 1
    </select>
</mapper>
