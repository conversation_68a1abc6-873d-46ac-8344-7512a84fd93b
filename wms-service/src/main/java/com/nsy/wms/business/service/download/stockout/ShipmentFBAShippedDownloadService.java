package com.nsy.wms.business.service.download.stockout;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Sets;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductCustomsDeclareElementService;
import com.nsy.wms.business.service.stockout.StockoutOrderPackMappingInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentAmazonRelationService;
import com.nsy.wms.business.service.stockout.StockoutShipmentDownloadService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPackMappingInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShipmentFBAShippedDownloadService implements IDownloadService {
    @Autowired
    private StockoutShipmentDownloadService downloadService;
    @Autowired
    private ShipmentAllShippedDownloadService shipmentAllShippedDownloadService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Inject
    StockoutOrderPackMappingInfoService packMappingInfoService;
    @Autowired
    StockoutShipmentAmazonRelationService stockoutShipmentAmazonRelationService;
    @Resource
    ProductCustomsDeclareElementService productCustomsDeclareElementService;
    @Resource
    StockoutShipmentItemMapper shipmentItemMapper;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_SHIPMENT_FBA_SHIPPED;
    }

    // 按查询条件导出已发货
    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        ShipmentDownloadRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentDownloadRequest.class);
        List<String> orderNos;
        if (!CollectionUtils.isEmpty(downloadRequest.getShipmentIds())) {
            downloadRequest.getSearchRequest().setShipmentIdList(downloadRequest.getShipmentIds());
        }
        if (downloadRequest.getSearchRequest() != null && !CollectionUtils.isEmpty(downloadRequest.getSearchRequest().getFbaShipmentIdList())) {
            orderNos = stockoutShipmentAmazonRelationService.getBaseMapper().findOrderNosByFbaShipmentId(downloadRequest.getSearchRequest().getFbaShipmentIdList(), downloadRequest.getSearchRequest().getShipmentIdList());
        } else {
            StockoutBuilding.buildAmazonListRequest(downloadRequest.getSearchRequest());
            orderNos = shipmentAllShippedDownloadService.buildOrderNos(downloadRequest, request, response);
        }
        if (CollectionUtils.isEmpty(orderNos))
            return response;
        ArrayList<ShipmentBoxSkuExport> skuExports = new ArrayList<>();
        downloadRequest.setShipmentIds(shipmentItemMapper.searchShipmentIdByOrderNoAndShipmentId(orderNos, downloadRequest.getSearchRequest().getShipmentIdList()));
        List<ShipmentBoxSkuExport> shipmentBoxSkuExports = downloadService.shipmentBoxSkuList(downloadRequest);
        Map<String, StockoutOrderEntity> collect = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>().select(StockoutOrderEntity::getStockoutOrderNo, StockoutOrderEntity::getWorkspace, StockoutOrderEntity::getStockoutType)
                .in(StockoutOrderEntity::getStockoutOrderNo, shipmentBoxSkuExports.stream().map(ShipmentBoxSkuExport::getStockoutOrderNo).distinct().collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(StockoutOrderEntity::getStockoutOrderNo, Function.identity()));
        Map<String, List<ShipmentBoxSkuExport>> shipmentBoxSkuExportsMap = shipmentBoxSkuExports.stream().collect(Collectors.groupingBy(ShipmentBoxSkuExport::getOrderNo));
        buildResult(collect, shipmentBoxSkuExportsMap, skuExports, orderNos);
        response.setDataJsonStr(JsonMapper.toJson(skuExports));
        return response;
    }

    private void buildResult(Map<String, StockoutOrderEntity> collect, Map<String, List<ShipmentBoxSkuExport>> operateMap, List<ShipmentBoxSkuExport> skuExports, List<String> orderNos) {
        int i = 0;
        for (String orderNo : orderNos) {
            List<ShipmentBoxSkuExport> shipmentBoxSkuExports = operateMap.get(orderNo);
            if (CollectionUtils.isEmpty(shipmentBoxSkuExports)) {
                continue;
            }
            ShipmentBoxSkuExport orderNoRow = new ShipmentBoxSkuExport();
            orderNoRow.setBoxIndex(orderNo);
            skuExports.add(orderNoRow);
            Set<Integer> set = Sets.newHashSet();
            for (ShipmentBoxSkuExport export : shipmentBoxSkuExports) {
                boolean makeBlank = false;
                export.setElementValue(productCustomsDeclareElementService.replaceStoreSku(export.getSpu(), export.getStoreSku()));
                if (export.getBoxIndex() == null || export.getBoxIndex().contains("NO"))
                    continue;
                Integer index = export.getShipmentId();
                if (set.contains(index)) {
                    export.setBoxIndex(null);
                    export.setWeight(null);
                    export.setVolumeWeight(null);
                    export.setBoxSize(null);
                } else {
                    makeBlank = !CollectionUtils.isEmpty(set);
                    set.add(index);
                    export.setBoxIndex(String.format("NO.%s", ++i));
                }
                if (makeBlank) {
                    skuExports.add(new ShipmentBoxSkuExport());
                    skuExports.add(new ShipmentBoxSkuExport());
                }
                StockoutOrderEntity stockoutOrderEntity = collect.getOrDefault(export.getStockoutOrderNo(), new StockoutOrderEntity());
                if (StockoutOrderWorkSpaceEnum.B2B_AREA.name().equals(stockoutOrderEntity.getWorkspace())) {
                    export.setStoreSku(export.getSku());
                    export.setStoreBarcode(JSONUtils.toJSON(CollUtil.newArrayList(export.getColor(), export.getSize())));
                }
                if (StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType()) && StringUtils.hasText(export.getOrderItemId())) {
                    StockoutOrderPackMappingInfoEntity mapping = packMappingInfoService.getByOrderItemNo(export.getOrderItemId());
                    export.setStoreSku(mapping == null ? export.getStoreSku() : mapping.getOriginSku());
                    export.setStoreBarcode(export.getSku());
                }
                skuExports.add(export);
            }
            skuExports.add(new ShipmentBoxSkuExport());
            skuExports.add(new ShipmentBoxSkuExport());
        }
    }

    public Map<String, List<ShipmentBoxSkuExport>> sortByShipmentCount(List<String> declareDocumentNo, Map<String, List<ShipmentBoxSkuExport>> shipmentBoxSkuExportsMap) {
        Map<String, List<ShipmentBoxSkuExport>> operateMap;
        String orderNo = declareDocumentNo.get(0);
        // 单据同名订单放第一
        operateMap = new LinkedHashMap<>(shipmentBoxSkuExportsMap.size());
        if (Objects.nonNull(shipmentBoxSkuExportsMap.getOrDefault(orderNo, null))) {
            operateMap.put(orderNo, shipmentBoxSkuExportsMap.get(orderNo));
        }
        // 根据箱数由多到少排序
        List<Map.Entry<String, List<ShipmentBoxSkuExport>>> entries = new ArrayList<>(shipmentBoxSkuExportsMap.entrySet());
        LinkedHashMap<String, List<ShipmentBoxSkuExport>> sort = entries.stream().filter(item -> !orderNo.equals(item.getKey())).sorted(Comparator.comparingInt(v -> Math.negateExact(v.getValue().size())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1, LinkedHashMap::new));
        operateMap.putAll(sort);
        return operateMap;
    }

}
