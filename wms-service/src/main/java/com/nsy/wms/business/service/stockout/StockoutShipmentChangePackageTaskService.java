package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.request.stockout.StockoutShipmentChangePackageTaskPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentChangePackageTaskPageResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentChangePackageTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentChangePackageTaskMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 打包任务Service
 */
@Service
public class StockoutShipmentChangePackageTaskService extends ServiceImpl<StockoutShipmentChangePackageTaskMapper, StockoutShipmentChangePackageTaskEntity> {


    @Autowired
    private StockoutShipmentService shipmentService;

    @Autowired
    private StockoutShipmentItemService shipmentItemService;

    @Autowired
    private StockoutOrderService stockoutOrderService;

    @Autowired
    private LoginInfoService loginInfoService;

    /**
     * 分页查询打包任务列表
     *
     * @param request 查询参数
     * @return 分页结果
     */
    public PageResponse<StockoutShipmentChangePackageTaskPageResponse> searchPage(StockoutShipmentChangePackageTaskPageRequest request) {
        PageResponse<StockoutShipmentChangePackageTaskPageResponse> pageResponse = new PageResponse<>();
        IPage<StockoutShipmentChangePackageTaskPageResponse> pageResult = this.baseMapper.searchPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }

    public void saveChangePackageRecord(String shipmentBoxCode) {
        LambdaQueryWrapper<StockoutShipmentChangePackageTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentChangePackageTaskEntity::getShipmentBoxCode, shipmentBoxCode);
        StockoutShipmentChangePackageTaskEntity changePackageTask = this.getOne(queryWrapper);
        if (Objects.nonNull(changePackageTask)) {
            throw new BusinessServiceException(String.format("该箱子任务已被%s处理!", changePackageTask.getOperator()));
        }
        StockoutShipmentEntity shipmentInfo = shipmentService.findTopByShipmentBoxCode(shipmentBoxCode);
        List<StockoutShipmentItemEntity> shipmentItemEntityList = shipmentItemService.findByShipmentId(shipmentInfo.getShipmentId());
        if (CollectionUtils.isEmpty(shipmentItemEntityList)) {
            throw new BusinessServiceException("箱子为空请检查!");
        }
        changePackageTask = new StockoutShipmentChangePackageTaskEntity();
        changePackageTask.setLocation(TenantContext.getTenant());
        changePackageTask.setShipmentId(shipmentInfo.getShipmentId());
        changePackageTask.setShipmentBoxCode(shipmentBoxCode);
        changePackageTask.setOperator(loginInfoService.getName());
        changePackageTask.setCreateBy(loginInfoService.getName());
        changePackageTask.setOperateDate(new Date());
        changePackageTask.setCreateDate(new Date());
        changePackageTask.setQty(shipmentItemEntityList.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum());
        StockoutOrderEntity orderEntity = stockoutOrderService.findByStockoutOrderNo(shipmentItemEntityList.get(0).getStockoutOrderNo());
        changePackageTask.setWorkspace(Objects.isNull(orderEntity) ? "" : orderEntity.getWorkspace());
        this.save(changePackageTask);
    }
} 