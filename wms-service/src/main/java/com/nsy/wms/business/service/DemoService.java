package com.nsy.wms.business.service;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.wms.repository.entity.demo.DemoEntity;
import com.nsy.wms.repository.jpa.mapper.demo.DemoMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class DemoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DemoService.class);

    @Resource
    DemoMapper demoMapper;

    @Cacheable(value = "UserConfig", key = "'username-'+#username")
    public String getUserConfig(String username) {
        LOGGER.info("没有命中缓存。。。。");
        return String.format("我叫：%s", username);
    }

    public String hello(String name) {
        return "hello, " + name;
    }

    @Transactional
    public void transactionalTest(Boolean isThrow) {
        DemoEntity demoEntity = new DemoEntity();
        demoEntity.setName("transactionalTest");
        demoEntity.setIsDeleted(0);
        demoMapper.insert(demoEntity);
        if (isThrow)
            throw new BusinessServiceException("回滚测试");
    }
}
