package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "stock_platform_schedule")
@TableName("stock_platform_schedule")
public class StockPlatformScheduleEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer platformScheduleId;

    /**
     * 地区
     */
    private String location;

    /**
     * 入库类型
     */
    private String platformScheduleType;

    /**
     * 发货类型：0-普通发货;1-排单发货;2-返工发货;3-直发发货;4-生产发货;5-快捷发货;
     */
    private Integer deliveryType;

    /**
     * 工厂出库单号
     */
    private String supplierDeliveryNo;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 类型 1：分公司下单，需生成泉州接收单
     */
    private Integer orderType;

    /**
     * 采购员userName,例000040
     */
    private String purchaseUserName;

    /**
     * 采购员真实姓名,例张三
     */
    private String purchaseUserRealName;

    /**
     * 采购员 权限系统的userId  nsy_user.sys_uer.user_id
     */
    private Integer purchaseUserId;

    /**
     * 采购模式 现货采购(SPOT)、大货采购（BULK）
     */
    private String purchaseModel;

    /**
     * 工厂发货时间
     */
    public Date deliveryDate;

    /**
     * 计划箱数
     */
    private Integer planBoxNum;

    /**
     * 计划到达时间
     */
    private Date planArriveDate;

    /**
     * 车牌
     */
    private String truckLicense;

    /**
     * 车型
     */
    private String truckType;

    /**
     * 司机信息
     */
    private String driver;

    /**
     * 状态 no appointment-未预约,appointment-已预约,cancel-取消
     */
    private String status;

    /**
     * 入库状态:运输中、待入库、入库中、已入库
     */
    private String stockinStatus;

    /**
     * 月台id
     */
    private Integer platformId;

    /**
     * 月台名称
     */
    private String platformName;

    /**
     * 排班开始时间
     */
    private Date scheduleStartDate;

    /**
     * 排班结束时间
     */
    private Date scheduleEndDate;

    /**
     * 真实到达时间
     */
    private Date realArriveDate;

    /**
     * 真实到货箱数
     */
    private Integer realBoxNum;

    /**
     * 收货地
     */
    private String receiptPlace;
    /**
     * 物流公司
     */
    private Integer logisticsCompanyId;
    /**
     * 物流公司名称
     */
    private String logisticsCompany;
    /**
     * 直发备注
     */
    private String remarks;

    private Date auditDate;

    private String auditBy;

    /**
     * 自动创建补货单
     */
    private Integer isAutoCreateFbaReplenishment;

    /**
     * 齐色齐码
     */
    private Integer isHomogeneous;

    /**
     * 审核备注信息
     */
    private String auditDescription;

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getAuditBy() {
        return auditBy;
    }

    public void setAuditBy(String auditBy) {
        this.auditBy = auditBy;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPurchaseModel() {
        return purchaseModel;
    }

    public void setPurchaseModel(String purchaseModel) {
        this.purchaseModel = purchaseModel;
    }

    public Integer getPlatformScheduleId() {
        return platformScheduleId;
    }

    public void setPlatformScheduleId(Integer platformScheduleId) {
        this.platformScheduleId = platformScheduleId;
    }

    public String getPlatformScheduleType() {
        return platformScheduleType;
    }

    public void setPlatformScheduleType(String platformScheduleType) {
        this.platformScheduleType = platformScheduleType;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public String getStockinStatus() {
        return stockinStatus;
    }

    public void setStockinStatus(String stockinStatus) {
        this.stockinStatus = stockinStatus;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPurchaseUserName() {
        return purchaseUserName;
    }

    public void setPurchaseUserName(String purchaseUserName) {
        this.purchaseUserName = purchaseUserName;
    }

    public String getPurchaseUserRealName() {
        return purchaseUserRealName;
    }

    public void setPurchaseUserRealName(String purchaseUserRealName) {
        this.purchaseUserRealName = purchaseUserRealName;
    }

    public Integer getPlanBoxNum() {
        return planBoxNum;
    }

    public void setPlanBoxNum(Integer planBoxNum) {
        this.planBoxNum = planBoxNum;
    }

    public Date getPlanArriveDate() {
        return planArriveDate;
    }

    public void setPlanArriveDate(Date planArriveDate) {
        this.planArriveDate = planArriveDate;
    }

    public String getTruckLicense() {
        return truckLicense;
    }

    public void setTruckLicense(String truckLicense) {
        this.truckLicense = truckLicense;
    }

    public String getTruckType() {
        return truckType;
    }

    public void setTruckType(String truckType) {
        this.truckType = truckType;
    }

    public String getDriver() {
        return driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Date getScheduleStartDate() {
        return scheduleStartDate;
    }

    public void setScheduleStartDate(Date scheduleStartDate) {
        this.scheduleStartDate = scheduleStartDate;
    }

    public Date getScheduleEndDate() {
        return scheduleEndDate;
    }

    public void setScheduleEndDate(Date scheduleEndDate) {
        this.scheduleEndDate = scheduleEndDate;
    }

    public Date getRealArriveDate() {
        return realArriveDate;
    }

    public void setRealArriveDate(Date realArriveDate) {
        this.realArriveDate = realArriveDate;
    }

    public Integer getRealBoxNum() {
        return realBoxNum;
    }

    public void setRealBoxNum(Integer realBoxNum) {
        this.realBoxNum = realBoxNum;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getPurchaseUserId() {
        return purchaseUserId;
    }

    public void setPurchaseUserId(Integer purchaseUserId) {
        this.purchaseUserId = purchaseUserId;
    }

    public String getReceiptPlace() {
        return receiptPlace;
    }

    public void setReceiptPlace(String receiptPlace) {
        this.receiptPlace = receiptPlace;
    }

    public Integer getLogisticsCompanyId() {
        return logisticsCompanyId;
    }

    public void setLogisticsCompanyId(Integer logisticsCompanyId) {
        this.logisticsCompanyId = logisticsCompanyId;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getIsAutoCreateFbaReplenishment() {
        return isAutoCreateFbaReplenishment;
    }

    public void setIsAutoCreateFbaReplenishment(Integer isAutoCreateFbaReplenishment) {
        this.isAutoCreateFbaReplenishment = isAutoCreateFbaReplenishment;
    }

    public Integer getIsHomogeneous() {
        return isHomogeneous;
    }

    public void setIsHomogeneous(Integer isHomogeneous) {
        this.isHomogeneous = isHomogeneous;
    }

    public String getAuditDescription() {
        return auditDescription;
    }

    public void setAuditDescription(String auditDescription) {
        this.auditDescription = auditDescription;
    }
}
