package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.enumeration.stockout.StockoutBarcodeScanRecordsBusinessTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/27 10:27
 */
@ApiModel(value = "StockoutBarcodeScanRecordsResponse", description = "贴码扫描人员记录表")
public class StockoutBarcodeScanRecordsPageResponse {

    @ApiModelProperty(value = "id", name = "id")
    private Integer id;

    /**
     * 装箱清单
     */
    @ApiModelProperty(value = "装箱清单", name = "shipmentBoxCode")
    private String shipmentBoxCode;

    /**
     * 业务单号
     */
    @ApiModelProperty(value = "业务单号", name = "orderNo")
    private String orderNo;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", name = "businessType")
    private String businessType;

    /**
     * 平台
     */
    @ApiModelProperty(value = "平台", name = "platformName")
    private String platformName;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", name = "operateName")
    private String operateName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间", name = "operateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;


    /**
     * 出库件数
     */
    @ApiModelProperty(value = "出库件数", name = "deliveryQty")
    private Integer deliveryQty;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getPlatformNameStr() {
        return Optional.ofNullable(this.getPlatformName())
                .filter(StringUtils::hasText)
                .map(StockoutOrderPlatformEnum::of)
                .map(StockoutOrderPlatformEnum::getDesc)
                .orElse(null);
    }

    public String getBusinessTypeStr() {
        if (StringUtils.hasText(this.getBusinessType())) {
            return StockoutBarcodeScanRecordsBusinessTypeEnum.valueOf(this.getBusinessType()).getName();
        }
        return null;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Integer getDeliveryQty() {
        return deliveryQty;
    }

    public void setDeliveryQty(Integer deliveryQty) {
        this.deliveryQty = deliveryQty;
    }

    public String getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }
}
