package com.nsy.wms.business.service.stockin;

import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.bd.BdSpace;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemQcInfo;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.domain.stockin.QcInboundsPriceItem;
import com.nsy.api.wms.enumeration.QcInboundsResultStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.request.external.ExternalQcReturnBackRequest;
import com.nsy.api.wms.response.external.ErpInternalBoxSkuResponse;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.supplier.response.SpotInfoResponse;
import com.nsy.wms.business.service.bd.BdQaRuleService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stockin.facade.StockinQcFacade;
import com.nsy.wms.repository.entity.bd.BdQaRuleEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinQcInboundsItemEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinQcBuildService {

    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockPlatformScheduleService stockPlatformScheduleService;
    @Autowired
    StockPlatformScheduleItemService stockPlatformScheduleItemService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    BdQaRuleService bdQaRuleService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    StockinQcFacade stockinQcFacade;
    @Autowired
    StockinQcInboundsItemService stockinQcInboundsItemService;
    @Autowired
    BdSpaceService spaceService;

    public ErpInternalBoxSkuResponse buildSkuResponse(StockInternalBox internalBox, List<StockInternalBoxItemQcInfo> list) {
        ErpInternalBoxSkuResponse erpInternalBoxSkuResponse = new ErpInternalBoxSkuResponse();
        erpInternalBoxSkuResponse.setBoxStatus(StockInternalBoxStatusEnum.valueOf(internalBox.getStatus()).getStatus());
        erpInternalBoxSkuResponse.setBoxBarcode(internalBox.getInternalBoxCode());
        erpInternalBoxSkuResponse.setSku(list.get(0).getSku());
        erpInternalBoxSkuResponse.setSkuImage(list.get(0).getImageUrl());
        erpInternalBoxSkuResponse.setReceiveInfoList(buildReceiveInfo(list));
        erpInternalBoxSkuResponse.setSupplierQtyInfoList(buildSupplierQtyInfo(list));
        erpInternalBoxSkuResponse.setWorkmanshipVersion(list.get(0).getWorkmanshipVersion());
        erpInternalBoxSkuResponse.setBrandName(list.get(0).getBrandName());
        erpInternalBoxSkuResponse.setSkuBarcode(list.get(0).getBarcode());
        return erpInternalBoxSkuResponse;
    }

    private List<ErpInternalBoxSkuResponse.SupplierQtyInfo> buildSupplierQtyInfo(List<StockInternalBoxItemQcInfo> list) {
        return list.stream().map(entity -> {
            ErpInternalBoxSkuResponse.SupplierQtyInfo supplierQtyInfo = new ErpInternalBoxSkuResponse.SupplierQtyInfo();
            supplierQtyInfo.setCheckQty((int) Math.ceil(entity.getQty().doubleValue() / 5));
            supplierQtyInfo.setReceiveQty(entity.getQty());
            supplierQtyInfo.setReturnQty(0);
            return supplierQtyInfo;
        }).collect(Collectors.toList());
    }

    private List<ErpInternalBoxSkuResponse.ReceiveInfo> buildReceiveInfo(List<StockInternalBoxItemQcInfo> list) {
        return list.stream().map(entity -> {
            ErpInternalBoxSkuResponse.ReceiveInfo receiveInfo = new ErpInternalBoxSkuResponse.ReceiveInfo();
            receiveInfo.setPurchaseNumber(entity.getPurchasePlanNo());
            receiveInfo.setReceiveQty(entity.getQty());
            receiveInfo.setReceivingNo(entity.getOrderNo());
            return receiveInfo;
        }).collect(Collectors.toList());
    }

    private List<ErpInternalBoxSkuResponse.ReceiveInfo> buildReceiveInfo(List<StockinOrderTaskEntity> stockinOrderTaskEntityList, List<StockInternalBoxItemQcInfo> list, StockPlatformScheduleEntity stockPlatformScheduleEntity) {
        String receivingNo = stockinOrderTaskEntityList.stream().map(StockinOrderTaskEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(","));
        String supplierDeliveryNo = stockinOrderTaskEntityList.stream().map(StockinOrderTaskEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(","));
        return list.stream().map(entity -> {
            ErpInternalBoxSkuResponse.ReceiveInfo receiveInfo = new ErpInternalBoxSkuResponse.ReceiveInfo();
            receiveInfo.setReceivingNo(receivingNo);
            receiveInfo.setPurchaseNumber(entity.getPurchasePlanNo());
            receiveInfo.setSupplierDeliveryNo(supplierDeliveryNo);
            receiveInfo.setReceiveQty(entity.getQty());
            receiveInfo.setDeliveryDate(Objects.nonNull(stockPlatformScheduleEntity) ? stockPlatformScheduleEntity.getDeliveryDate() : null);
            return receiveInfo;
        }).collect(Collectors.toList());
    }

    public ErpInternalBoxSkuResponse buildStockinBoxSkuResponse(StockInternalBox internalBox, List<StockInternalBoxItemQcInfo> list) {
        ErpInternalBoxSkuResponse erpInternalBoxSkuResponse = new ErpInternalBoxSkuResponse();
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(list.stream().map(StockInternalBoxItemQcInfo::getStockinOrderNo).collect(Collectors.toList()));
        List<Integer> taskIdList = stockinOrderEntityList.stream().map(StockinOrderEntity::getTaskId).collect(Collectors.toList());
        List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.listByIds(taskIdList);
        StockPlatformScheduleEntity stockPlatformScheduleEntity = stockPlatformScheduleService.getById(stockinOrderTaskEntityList.get(0).getPlatformScheduleId());
        ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(list.get(0).getSku());
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdListAndSpecId(taskIdList, productSpecInfo.getSpecId());
        BdSpace space = spaceService.getSpaceById(internalBox.getSpaceId());
        int isAllCheck = taskItemEntityList.get(0).getIsPreQaQualified().equals(3) ? 1 : 0;
        erpInternalBoxSkuResponse.setIsWaitShelve(list.stream().allMatch(item -> StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())) ? 1 : 0);
        erpInternalBoxSkuResponse.setLocation(internalBox.getLocation());
        erpInternalBoxSkuResponse.setIsAllCheck(isAllCheck);
        erpInternalBoxSkuResponse.setIsNew(taskItemEntityList.stream().anyMatch(item -> StringConstant.FIRST_ORDER_LABEL.equals(item.getFirstOrderLabel())) ? 1 : 0);
        erpInternalBoxSkuResponse.setIsReturnOrder(taskItemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getPurchasingApplyType()) && 6 == item.getPurchasingApplyType()));
        erpInternalBoxSkuResponse.setInternalBoxType(internalBox.getInternalBoxType());
        erpInternalBoxSkuResponse.setStockinType(stockinOrderTaskEntityList.get(0).getStockinType());
        if (Objects.isNull(stockPlatformScheduleEntity)) {
            String logisticsNo = stockinOrderEntityList.get(0).getLogisticsNo();
            List<SpotInfoResponse> spotInfoList = scmApiService.getSpotInfoList(logisticsNo, null);
            erpInternalBoxSkuResponse.setPurchaseUserName(spotInfoList.get(0).getPurchaseUserName());
            erpInternalBoxSkuResponse.setPurchaseUserRealName(spotInfoList.get(0).getPurchaserRealName());
            erpInternalBoxSkuResponse.setSupplierQtyInfoList(buildStockinSupplierQtyInfo(internalBox, stockinOrderEntityList.get(0), list));
        } else {
            erpInternalBoxSkuResponse.setPurchaseUserName(stockPlatformScheduleEntity.getPurchaseUserName());
            erpInternalBoxSkuResponse.setPurchaseUserRealName(stockPlatformScheduleEntity.getPurchaseUserRealName());
            erpInternalBoxSkuResponse.setSupplierQtyInfoList(buildStockinSupplierQtyInfo(internalBox, stockPlatformScheduleEntity, list, isAllCheck));
        }
        erpInternalBoxSkuResponse.setBoxStatus(internalBox.getStatus());
        erpInternalBoxSkuResponse.setBoxBarcode(internalBox.getInternalBoxCode());
        erpInternalBoxSkuResponse.setSku(list.get(0).getSku());
        erpInternalBoxSkuResponse.setSkc(productSpecInfo.getSkc());
        erpInternalBoxSkuResponse.setProductId(productSpecInfo.getProductId());
        erpInternalBoxSkuResponse.setSkuImage(list.get(0).getImageUrl());
        erpInternalBoxSkuResponse.setWorkmanshipVersion(list.get(0).getWorkmanshipVersion());
        erpInternalBoxSkuResponse.setReceiveInfoList(buildReceiveInfo(stockinOrderTaskEntityList, list, stockPlatformScheduleEntity));
        erpInternalBoxSkuResponse.setBrandName(list.get(0).getBrandName());
        erpInternalBoxSkuResponse.setSkuBarcode(list.get(0).getBarcode());
        erpInternalBoxSkuResponse.setSpaceName(space.getSpaceName());
        erpInternalBoxSkuResponse.setPurchaseCountList(stockinQcFacade.getPurchaseCount(stockinOrderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList()), internalBox.getInternalBoxCode(), productSpecInfo.getSku(), null));
        return erpInternalBoxSkuResponse;
    }

    private List<ErpInternalBoxSkuResponse.SupplierQtyInfo> buildStockinSupplierQtyInfo(StockInternalBox internalBox, StockPlatformScheduleEntity stockPlatformScheduleEntity, List<StockInternalBoxItemQcInfo> list, int isAllCheck) {
        Map<Integer, SupplierDto> supplierDtoMap = new HashMap<>();
        List<StockPlatformScheduleItemEntity> stockPlatformScheduleItemEntities = stockPlatformScheduleItemService.listByPlatformScheduleIdAndSku(stockPlatformScheduleEntity.getPlatformScheduleId(), list.get(0).getSku());
        return list.stream().map(entity -> {
            ErpInternalBoxSkuResponse.SupplierQtyInfo supplierQtyInfo = new ErpInternalBoxSkuResponse.SupplierQtyInfo();
            supplierQtyInfo.setReceiveQty(entity.getQty());
            supplierQtyInfo.setReturnQty(0);
            if (CollectionUtils.isEmpty(stockPlatformScheduleItemEntities) || StringUtils.hasText(stockPlatformScheduleItemEntities.get(0).getSupplierName())) {
                supplierQtyInfo.setSupplierId(stockPlatformScheduleItemEntities.get(0).getSupplierId());
                supplierQtyInfo.setSupplierName(stockPlatformScheduleItemEntities.get(0).getSupplierName());
            } else {
                supplierQtyInfo.setSupplierId(stockPlatformScheduleEntity.getSupplierId());
                supplierQtyInfo.setSupplierName(stockPlatformScheduleEntity.getSupplierName());
            }
            supplierQtyInfo.setCheckQty(isAllCheck == 1 || StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType()) ? entity.getQty() : this.getQaQty(stockPlatformScheduleItemEntities.get(0).getSpaceId(), stockPlatformScheduleEntity.getSupplierId(), entity.getQty()));
            if (!supplierDtoMap.containsKey(supplierQtyInfo.getSupplierId())) {
                SupplierDto supplierDto = scmApiService.getSupplierInfoList(Collections.singletonList(stockPlatformScheduleEntity.getSupplierId())).stream().findFirst().orElse(new SupplierDto());
                supplierDtoMap.put(supplierQtyInfo.getSupplierId(), supplierDto);
            }
            supplierQtyInfo.setDepartment(supplierDtoMap.get(supplierQtyInfo.getSupplierId()).getAffiliateDeptName());
            supplierQtyInfo.setApplyDepartment(buildApplyDepartment(stockPlatformScheduleItemEntities));
            return supplierQtyInfo;
        }).collect(Collectors.toList());
    }

    private List<ErpInternalBoxSkuResponse.SupplierQtyInfo> buildStockinSupplierQtyInfo(StockInternalBox internalBox, StockinOrderEntity stockinOrderEntity, List<StockInternalBoxItemQcInfo> list) {
        return list.stream().map(entity -> {
            ErpInternalBoxSkuResponse.SupplierQtyInfo supplierQtyInfo = new ErpInternalBoxSkuResponse.SupplierQtyInfo();
            supplierQtyInfo.setReceiveQty(entity.getQty());
            supplierQtyInfo.setReturnQty(0);
            supplierQtyInfo.setSupplierId(stockinOrderEntity.getSupplierId());
            supplierQtyInfo.setSupplierName(stockinOrderEntity.getSupplierName());
            supplierQtyInfo.setCheckQty(StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType()) ? entity.getQty() : this.getQaQty(internalBox.getSpaceId(), stockinOrderEntity.getSupplierId(), entity.getQty()));
            return supplierQtyInfo;
        }).collect(Collectors.toList());
    }

    private String buildApplyDepartment(List<StockPlatformScheduleItemEntity> stockPlatformScheduleItemEntities) {
        if (CollectionUtils.isEmpty(stockPlatformScheduleItemEntities))
            return null;
        String collect = stockPlatformScheduleItemEntities.stream().map(StockPlatformScheduleItemEntity::getBusinessType).collect(Collectors.joining(","));
        if (!StringUtils.hasText(collect))
            return null;
        return Arrays.stream(collect.split(",")).distinct().collect(Collectors.joining(","));
    }

    public Integer getQaQty(Integer spaceId, Integer supplierId, Integer qty) {
        Optional<BdQaRuleEntity> bdQaRuleEntity = bdQaRuleService.getBdQaRuleEntity(supplierId, spaceId, qty);
        if (bdQaRuleEntity.isPresent()) {
            if (bdQaRuleEntity.get().getExtractRate().equals(0)) {
                return 0;
            } else {
                double value = (double) bdQaRuleEntity.get().getExtractRate() * (double) qty / 100;
                int ceil = (int) Math.ceil(value);
                return Math.min(Math.max(ceil, bdQaRuleEntity.get().getMinExtract()), qty);
            }
        } else {
            return (int) Math.ceil(qty.doubleValue() / 5);
        }
    }

    //让步接收更新入库单明细的让步接收数
    public void updateConcessionsCount(List<StockInternalBoxItemEntity> list, QcInboundsMessage messageContent) {
        String purchaseNumbers = messageContent.getPurchaseNumbers();
        List<QcInboundsPriceItem> priceList = messageContent.getPriceList();
        Integer concessionsCount = 0 + messageContent.getConcessionsCount();
        List<StockInternalBoxItemEntity> collect = list.stream().filter(item -> purchaseNumbers.contains(item.getPurchasePlanNo())).collect(Collectors.toList());
        List<StockinOrderItemEntity> updateItemList = new ArrayList<>(list.size());
        List<StockinQcInboundsItemEntity> qcInboundsItemEntityList = new ArrayList<>(list.size());
        List<StockinOrderEntity> byStockinOrderNoList = stockinOrderService.getByStockinOrderNoList(collect.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().collect(Collectors.toList()));
        Map<String, StockinOrderEntity> orderEntityMap = byStockinOrderNoList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderNo, entity -> entity));
        for (StockInternalBoxItemEntity stockInternalBoxItemEntity : collect) {
            if (concessionsCount <= 0)
                break;
            StockinOrderEntity entity = orderEntityMap.get(stockInternalBoxItemEntity.getStockInOrderNo());
            StockinOrderItemEntity orderItemEntity = stockinOrderItemService.findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(entity.getStockinOrderId(), stockInternalBoxItemEntity.getSku(), stockInternalBoxItemEntity.getInternalBoxCode(), stockInternalBoxItemEntity.getPurchasePlanNo(), "");

            Integer orderItemEntityQty = orderItemEntity.getQty();
            Integer returnQty = orderItemEntity.getReturnQty();
            int min = Math.min(concessionsCount, orderItemEntityQty - returnQty);
            StockinOrderItemEntity stockinOrderItemEntity = new StockinOrderItemEntity();
            stockinOrderItemEntity.setStockinOrderItemId(orderItemEntity.getStockinOrderItemId());
            stockinOrderItemEntity.setConcessionsCount(min);
            orderItemEntity.setConcessionsCount(min);
            StockinQcInboundsItemEntity qcInboundsItemEntity = stockinQcInboundsItemService.findByStockinOrderItemId(orderItemEntity.getStockinOrderItemId());
            if (Objects.nonNull(qcInboundsItemEntity)) {
                StockinQcInboundsItemEntity qcInboundsItemEntityNew = new StockinQcInboundsItemEntity();
                qcInboundsItemEntityNew.setId(qcInboundsItemEntity.getId());
                qcInboundsItemEntityNew.setUpdateBy(loginInfoService.getName());
                qcInboundsItemEntityNew.setConcessionsCount(min);
                qcInboundsItemEntityNew.setConcessionPrice(messageContent.getConcessionsPrice());
                Optional<QcInboundsPriceItem> any = Optional.ofNullable(priceList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(price -> StringUtils.hasText(price.getPurchasePlanNo()) && price.getPurchasePlanNo().contains(orderItemEntity.getPurchasePlanNo()))
                    .findAny();
                if (any.isPresent()) {
                    qcInboundsItemEntityNew.setConcessionPrice(any.get().getConcessionsPrice());
                }
                qcInboundsItemEntityList.add(qcInboundsItemEntityNew);
            }
            updateItemList.add(stockinOrderItemEntity);
            concessionsCount -= min;
        }
        stockinOrderItemService.updateBatchById(updateItemList);
        if (!CollectionUtils.isEmpty(qcInboundsItemEntityList))
            stockinQcInboundsItemService.updateBatchById(qcInboundsItemEntityList);
    }

    public void stockinQcCompleteUpdateItem(QcInboundsMessage messageContent, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        if (QcInboundsResultStatusEnum.PUT_ON.getDesc().equals(messageContent.getResult())
            || QcInboundsResultStatusEnum.CONCESSION_RECEIVE.getDesc().equals(messageContent.getResult())
            || QcInboundsResultStatusEnum.RECEIVE_BUT_SOME_RETURN.getDesc().equals(messageContent.getResult())) {
            List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockInternalBoxItemEntityList.stream()
                .map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()));
            //更新内部箱和入库单明细状态
            Map<String, StockinOrderEntity> collect = stockinOrderEntityList.stream().collect(Collectors.toMap(StockinOrderEntity::getStockinOrderNo, Function.identity()));
            List<StockinOrderItemEntity> orderItemEntityList = new ArrayList<>(stockInternalBoxItemEntityList.size());
            List<StockInternalBoxItemEntity> internalBoxItemEntities = stockInternalBoxItemEntityList.stream().map(item -> {
                StockinOrderEntity stockinOrderEntity = collect.get(item.getStockInOrderNo());
                StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemService.findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(stockinOrderEntity.getStockinOrderId(), messageContent.getSku(), messageContent.getBoxBarcode(), item.getPurchasePlanNo(), "");
                StockInternalBoxItemEntity itemEntity = new StockInternalBoxItemEntity();
                itemEntity.setInternalBoxItemId(item.getInternalBoxItemId());
                itemEntity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
                itemEntity.setUpdateBy(loginInfoService.getName());
                StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
                orderItemEntity.setStockinOrderItemId(stockinOrderItemEntity.getStockinOrderItemId());
                orderItemEntity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
                orderItemEntity.setUpdateBy(loginInfoService.getName());
                orderItemEntityList.add(orderItemEntity);
                return itemEntity;
            }).collect(Collectors.toList());
            stockinOrderItemService.updateBatchById(orderItemEntityList);
            stockInternalBoxItemService.updateBatchById(internalBoxItemEntities);
        }
    }

    public ExternalQcReturnBackRequest buildExternalQcReturnBackRequest(QcInboundsMessage messageContent, String stockinOrderNo, String purchasePlanNo, Integer returnQty, StockinOrderEntity stockinOrderEntity) {
        ExternalQcReturnBackRequest request = new ExternalQcReturnBackRequest();
        request.setSupplierId(messageContent.getSupplierId());
        request.setSupplierName(messageContent.getSupplierName());
        request.setOperator(messageContent.getQcUserName());
        request.setInternalBoxNo(messageContent.getInternalBoxNo());
        request.setBoxBarcode(messageContent.getBoxBarcode());
        request.setSku(messageContent.getSku());
        request.setDelQty(returnQty);
        StockinOrderTaskEntity taskEntity = stockinOrderTaskService.getById(stockinOrderEntity.getTaskId());
        request.setReceiveOrderNos(Collections.singletonList(taskEntity.getSupplierDeliveryBoxCode()));
        request.setSupplierDeliverNos(Collections.singletonList(taskEntity.getSupplierDeliveryNo()));
        request.setPurchasePlanNo(purchasePlanNo);
        request.setStockinOrderNo(stockinOrderNo);
        request.setQaType(messageContent.getQaType());
        return request;
    }
}
