package com.nsy.api.wms.response.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 查询sku仓库在途库存响应类
 */
@ApiModel("StockTransferTrackingSkuResponse")
public class StockTransferTrackingSkuResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("SKU编码")
    private String sku;

    @ApiModelProperty("仓库信息列表")
    private List<SpaceInfo> spaceInfoList;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public List<SpaceInfo> getSpaceInfoList() {
        return spaceInfoList;
    }

    public void setSpaceInfoList(List<SpaceInfo> spaceInfoList) {
        this.spaceInfoList = spaceInfoList;
    }

    @ApiModel("SpaceInfo")
    public static class SpaceInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty("仓库ID")
        private Integer spaceId;

        @ApiModelProperty("ERP仓库ID")
        private Integer erpSpaceId;

        @ApiModelProperty("在途库存数量")
        private Integer onTheWayStock;

        public Integer getSpaceId() {
            return spaceId;
        }

        public void setSpaceId(Integer spaceId) {
            this.spaceId = spaceId;
        }

        public Integer getErpSpaceId() {
            return erpSpaceId;
        }

        public void setErpSpaceId(Integer erpSpaceId) {
            this.erpSpaceId = erpSpaceId;
        }

        public Integer getOnTheWayStock() {
            return onTheWayStock;
        }

        public void setOnTheWayStock(Integer onTheWayStock) {
            this.onTheWayStock = onTheWayStock;
        }
    }
}
