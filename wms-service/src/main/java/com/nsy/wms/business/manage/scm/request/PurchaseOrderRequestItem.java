package com.nsy.wms.business.manage.scm.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class PurchaseOrderRequestItem {
    private Integer spaceId;
    private String arrivalTime;
    private Double concessionPrice;
    private Integer concessionQty;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDate;

    private Integer deliveryQty;
    private Integer packageQty;
    private String purchaseOrderPlanNo;
    private String receiveDate;
    private Integer receiveQty;
    private String receiver;
    private Integer receivingId;
    private Integer receivingItemId;
    private Integer returnQty;
    private String shelfTime;
    private String sku;
    private Integer unShelfQty;

    /**
     * 质检不合格原因归类
     */
    @JsonIgnore
    private String unqualifiedCategory;

    @JsonIgnore
    private String packageName;

    @JsonIgnore
    private String internalBoxCode;

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(String arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public Double getConcessionPrice() {
        return concessionPrice;
    }

    public void setConcessionPrice(Double concessionPrice) {
        this.concessionPrice = concessionPrice;
    }

    public Integer getConcessionQty() {
        return concessionQty;
    }

    public void setConcessionQty(Integer concessionQty) {
        this.concessionQty = concessionQty;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Integer getDeliveryQty() {
        return deliveryQty;
    }

    public void setDeliveryQty(Integer deliveryQty) {
        this.deliveryQty = deliveryQty;
    }

    public Integer getPackageQty() {
        return packageQty;
    }

    public void setPackageQty(Integer packageQty) {
        this.packageQty = packageQty;
    }

    public String getPurchaseOrderPlanNo() {
        return purchaseOrderPlanNo;
    }

    public void setPurchaseOrderPlanNo(String purchaseOrderPlanNo) {
        this.purchaseOrderPlanNo = purchaseOrderPlanNo;
    }

    public String getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(String receiveDate) {
        this.receiveDate = receiveDate;
    }

    public Integer getReceiveQty() {
        return receiveQty;
    }

    public void setReceiveQty(Integer receiveQty) {
        this.receiveQty = receiveQty;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public Integer getReceivingId() {
        return receivingId;
    }

    public void setReceivingId(Integer receivingId) {
        this.receivingId = receivingId;
    }

    public Integer getReceivingItemId() {
        return receivingItemId;
    }

    public void setReceivingItemId(Integer receivingItemId) {
        this.receivingItemId = receivingItemId;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public String getShelfTime() {
        return shelfTime;
    }

    public void setShelfTime(String shelfTime) {
        this.shelfTime = shelfTime;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getUnShelfQty() {
        return unShelfQty;
    }

    public void setUnShelfQty(Integer unShelfQty) {
        this.unShelfQty = unShelfQty;
    }
}