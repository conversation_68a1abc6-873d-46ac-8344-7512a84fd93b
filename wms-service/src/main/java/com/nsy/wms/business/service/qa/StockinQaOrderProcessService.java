package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.enumeration.bd.BdQaSopEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.response.qa.BdQaSopRuleDetailResponse;
import com.nsy.api.wms.response.qa.BdQaSopRuleItemDetailResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import com.nsy.wms.repository.entity.qa.StockinQaProductSampleRecordEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderProcessMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单流程表业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderProcessService extends ServiceImpl<StockinQaOrderProcessMapper, StockinQaOrderProcessEntity> {

    @Resource
    StockinQaProductSampleRecordService stockinQaProductSampleRecordService;
    @Autowired
    private LoginInfoService loginInfoService;


    @Transactional
    public void buildOrderProcess(StockinQaTaskEntity qaTaskEntity, StockinQaOrderEntity qaOrderEntity, BdQaSopRuleDetailResponse sopRule) {

        boolean needProductSample = false;
        //需判断SKU是否已做过产前样质检
        if (LocationEnum.MISI.name().equals(qaTaskEntity.getLocation())) {
            //misi默认需要样衣核对
            needProductSample = true;
        } else if (qaTaskEntity.getIsNew() == 1) {
            int count = stockinQaProductSampleRecordService.count(new LambdaQueryWrapper<StockinQaProductSampleRecordEntity>()
                .eq(StockinQaProductSampleRecordEntity::getProductId, qaTaskEntity.getProductId()));
            needProductSample = count == 0;
        }

        List<BdQaSopRuleItemDetailResponse> itemList = sopRule.getItemList();
        //如果做过产前样质检 ， 去掉样衣核对
        if (!needProductSample)
            itemList = itemList.stream().filter(item -> !BdQaSopEnum.QC_SAMPLE.getProcessName().equals(item.getProcessName())).collect(Collectors.toList());

        itemList.sort(Comparator.comparing(BdQaSopRuleItemDetailResponse::getSort));

        List<StockinQaOrderProcessEntity> processEntityList = new LinkedList<>();

        int sort = 1;
        for (BdQaSopRuleItemDetailResponse item : itemList) {
            StockinQaOrderProcessEntity processEntity = new StockinQaOrderProcessEntity();
            processEntity.setLocation(qaTaskEntity.getLocation());
            processEntity.setProcessName(item.getProcessName());
            processEntity.setSort(sort);
            processEntity.setStockinQaOrderId(qaOrderEntity.getStockinQaOrderId());
            sort++;
            processEntityList.add(processEntity);
        }
        if (!CollectionUtils.isEmpty(processEntityList))
            this.saveBatch(processEntityList);

    }

    public List<StockinQaOrderProcessEntity> listByStockinQaOrderId(Integer stockinQaOrderId) {
        //可能存在多个相同质检流程，取最后面那个
        Map<String, StockinQaOrderProcessEntity> processEntityMap = new LinkedHashMap<>();
        List<StockinQaOrderProcessEntity> list = this.list(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
            .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderId)
            .orderByAsc(StockinQaOrderProcessEntity::getSort));
        list.forEach(processEntity -> processEntityMap.put(processEntity.getProcessName(), processEntity));
        List<StockinQaOrderProcessEntity> processEntityListNew = new LinkedList<>();
        processEntityMap.values().forEach(item -> processEntityListNew.add(item));

        return processEntityListNew;
    }

    public StockinQaOrderProcessEntity getOneByStockinQaOrderIdAndProcessName(Integer stockinQaOrderId, String processName) {
        return this.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
            .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderId)
            .eq(StockinQaOrderProcessEntity::getProcessName, processName)
            .orderByDesc(StockinQaOrderProcessEntity::getSort)
            .last("limit 1"));
    }


    public List<StockinQaOrderProcessEntity> listByStockinQaOrderIdsAndProcessName(List<Integer> stockinQaOrderIds, String processName) {
        return this.list(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
            .in(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderIds)
            .eq(StockinQaOrderProcessEntity::getProcessName, processName));
    }

    public StockinQaOrderProcessEntity buildOrderProcessResult(StockinQaOrderEntity stockinQaOrderEntity, BdQaSopEnum qcResult) {
        StockinQaOrderProcessEntity processEntity = new StockinQaOrderProcessEntity();
        processEntity.setLocation(stockinQaOrderEntity.getLocation());
        processEntity.setProcessName(qcResult.getProcessName());

        StockinQaOrderProcessEntity lastProcessEntity = this.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
            .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderEntity.getStockinQaOrderId())
            .orderByDesc(StockinQaOrderProcessEntity::getSort)
            .last("limit 1"));

        processEntity.setSort(lastProcessEntity.getSort() + 1);
        processEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        processEntity.setCreateBy(loginInfoService.getName());
        processEntity.setOperator(loginInfoService.getName());
        this.save(processEntity);
        return processEntity;
    }

    /**
     * 获取对应流程的操作时间
     *
     * @param stockinQaOrderIdList
     * @param processName
     * @return
     */
    public Map<Integer, Date> getLastProcessDateInfo(List<Integer> stockinQaOrderIdList, String processName) {
        List<StockinQaOrderProcessEntity> processEntityList = new ArrayList<>();
        CollectionUtil.split(stockinQaOrderIdList.stream().distinct().collect(Collectors.toList()), 150).stream().forEach(
                detail -> {
                    processEntityList.addAll(this.getBaseMapper().getProcessInfoByInfo(detail, processName));
                }
        );
        if (CollectionUtils.isEmpty(processEntityList)) {
            return Collections.emptyMap();
        }
        return processEntityList.stream().collect(Collectors.toMap(StockinQaOrderProcessEntity::getStockinQaOrderId, StockinQaOrderProcessEntity::getCreateDate, (v1, v2) -> v1));
    }
}
