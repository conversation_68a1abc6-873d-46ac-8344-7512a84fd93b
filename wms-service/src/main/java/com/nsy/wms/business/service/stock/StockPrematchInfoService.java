package com.nsy.wms.business.service.stock;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.domain.stock.StockPrematchInfo;
import com.nsy.api.wms.domain.stock.StockSkuPositionInfo;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.PrematchProcessTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutDistributionTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPrematchLackOriginTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPrematchTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.request.stock.StockPrematchInfoPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockPrematchInfoResponse;
import com.nsy.wms.business.domain.bo.stock.StockPrematchBo;
import com.nsy.wms.business.domain.bo.stock.StockPrematchItemBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutOrderItemInfoBo;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdPrematchRuleItemService;
import com.nsy.wms.business.service.bd.BdPrematchRuleService;
import com.nsy.wms.business.service.bd.BdStockFifoService;
import com.nsy.wms.business.service.bd.BdStorePositionMappingService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.BdTagService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.build.StockPrematchInfoBuild;
import com.nsy.wms.business.service.stockout.StockoutBatchLogService;
import com.nsy.wms.business.service.stockout.StockoutBatchMergeService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderService;
import com.nsy.wms.business.service.stockout.StockoutBatchService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemProcessInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdPrematchRuleEntity;
import com.nsy.wms.repository.entity.bd.BdPrematchRuleItemEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemProcessInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockPrematchInfoMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchOrderMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockPrematchInfoService extends ServiceImpl<StockPrematchInfoMapper, StockPrematchInfoEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockPrematchInfoService.class);

    @Resource
    ProductSpecInfoService productSpecInfoService;
    @Resource
    StockoutBatchService batchService;
    @Resource
    BdPrematchRuleService prematchRuleService;
    @Resource
    BdPrematchRuleItemService prematchRuleItemService;
    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Resource
    StockoutOrderLogService stockoutOrderLogService;
    @Resource
    StockoutBatchOrderService batchOrderService;
    @Resource
    StockoutOrderItemProcessInfoService itemProcessInfoService;
    @Resource
    StockoutBatchMergeService stockoutBatchMergeService;
    @Resource
    StockPrematchInfoMapper prematchInfoMapper;
    @Resource
    StockService stockService;
    @Resource
    BdSystemParameterService systemParameterService;
    @Resource
    BdStockFifoService bdStockFifoService;
    @Resource
    BdTagService bdTagService;
    @Resource
    StockoutBatchLogService stockoutBatchLogService;
    @Resource
    StockoutBatchOrderMapper batchOrderMapper;
    @Resource
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    BdStorePositionMappingService bdStorePositionMappingService;
    @Resource
    BdAreaService areaService;
    @Resource
    StockMapper stockMapper;


    /**
     * 预配
     * 1、获取预占规则
     * 2、加工处理，赋值预配的胚款信息
     * 3、获取库存信息
     * 4、已预配信息
     * 5、获取预配结果
     * 6、回填预配类型
     * 7、保存预配信息
     *
     * @param stockPrematchBo
     */
    public List<StockPrematchInfoEntity> doPrematch(StockPrematchBo stockPrematchBo) {
        LOGGER.info("出库单预配 {}", JSONUtil.toJsonStr(stockPrematchBo));
        //1-6
        List<StockPrematchInfoEntity> prematchResultList = buildPrematch(stockPrematchBo);
        // 7、保存预配信息
        saveOrUpdatePrematch(prematchResultList, stockPrematchBo.getLackOriginType());

        return prematchResultList;
    }

    /**
     * 3、获取库存信息
     * 4、已预配信息
     * 5、获取预配结果
     * 6、回填预配类型
     *
     * @param stockPrematchBo
     * @return
     */
    public List<StockPrematchInfoEntity> buildPrematch(StockPrematchBo stockPrematchBo) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockPrematchBo.getStockoutOrderNo());


        List<StockPrematchInfoEntity> prematchResult = new LinkedList<>();
        stockPrematchBo.getItemList().stream().collect(Collectors.groupingBy(StockPrematchItemBo::getAreaId))
                .forEach((areaId, orderItemList) -> {
                    Integer areaIdTemp = areaId;
                    //group by时areaId不能为null值，所以先设置成0，这里重新设置成null
                    if (areaId == 0) {
                        areaIdTemp = null;
                        orderItemList.forEach(item -> item.setAreaId(null));
                    }
                    //根据店铺ID，明细拆分成店铺和非店铺
                    List<StockPrematchItemBo> storeItems = orderItemList.stream().filter(item -> Objects.nonNull(item.getStoreId())).collect(Collectors.toList());
                    List<StockPrematchItemBo> noStoreItems = orderItemList.stream().filter(item -> Objects.isNull(item.getStoreId())).collect(Collectors.toList());

                    // 3、获取库存信息
                    // 4、已预配信息
                    // 5、获取预配结果
                    if (!CollectionUtils.isEmpty(storeItems)) {
                        Map<Integer, List<StockPrematchItemBo>> itemMap = storeItems.stream().collect(Collectors.groupingBy(StockPrematchItemBo::getStoreId));
                        for (Map.Entry<Integer, List<StockPrematchItemBo>> entry : itemMap.entrySet()) {
                            prematchResult.addAll(prematchByArea(stockoutOrder, areaIdTemp, storeItems, entry.getKey()));
                        }
                    }
                    if (!CollectionUtils.isEmpty(noStoreItems))
                        prematchResult.addAll(prematchByArea(stockoutOrder, areaIdTemp, noStoreItems, null));

                });
        // 6、回填预配类型
        prematchResult.forEach(stockPrematchInfoEntity -> {
            stockPrematchInfoEntity.setPrematchType(stockPrematchBo.getPrematchType().name());
            stockPrematchInfoEntity.setBatchId(stockPrematchBo.getBatchId());
            if (!Objects.isNull(stockPrematchBo.getLackOriginType()))
                stockPrematchInfoEntity.setLackOriginType(stockPrematchBo.getLackOriginType().name());
        });
        return prematchResult;
    }

    /**
     * 通过地区预配
     * <p>
     * 3、获取库存信息
     * 4、已预配信息
     * 5、获取预配结果
     *
     * @return
     */
    private List<StockPrematchInfoEntity> prematchByArea(StockoutOrderEntity stockoutOrder, Integer areaId, List<StockPrematchItemBo> prematchItemBoList, Integer storeId) {
        List<Integer> specIdList = prematchItemBoList.stream().map(StockPrematchItemBo::getSpecId).collect(Collectors.toList());
        List<String> positionCodeList = new LinkedList<>();
        //根据店铺获取映射表 查询店铺下的活动库位
        BdAreaEntity activeArea = areaService.findByAreaNameAndSpaceId(SpaceAreaMapConstant.WmsArea.ACTIVITY_AREA, SpaceAreaMapConstant.QUANZHOU_MAIN_SPACE_ID);
        if (Objects.nonNull(storeId) && Objects.nonNull(activeArea) && activeArea.getAreaId().equals(areaId)) {
            positionCodeList = bdStorePositionMappingService.listPositionByStoreId(storeId);
            //如果没有映射关系 返回空
            if (CollectionUtils.isEmpty(positionCodeList)) {
                LOGGER.info("出库单: {} 店铺ID: {}，未配置活动仓库位映射", stockoutOrder.getStockoutOrderNo(), storeId);
                return new LinkedList<>();
            }
        }

        // 3、获取库存信息
        List<StockSkuPositionInfo> skuPositionInfoList = stockService.stockSkuPositionInfo(specIdList, stockoutOrder.getSpaceId(), areaId, positionCodeList);
        //如果不是活动区域，剔除掉活动库位
        if (Objects.isNull(activeArea) || !activeArea.getAreaId().equals(areaId))
            skuPositionInfoList = skuPositionInfoList.stream().filter(skuPositionInfo ->
                    !BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(skuPositionInfo.getPositionType())).collect(Collectors.toList());

        // 4、已预配信息
        List<StockPrematchInfo> skuPrematchInfoList = prematchInfoMapper.stockPrematchInfo(specIdList, stockoutOrder.getSpaceId(), areaId);
        for (StockSkuPositionInfo skuPositionInfo : skuPositionInfoList) {
            Integer skuPrematchInfoQty = skuPrematchInfoList.stream().filter(o -> o.getSpecId().equals(skuPositionInfo.getSpecId()) && o.getPositionCode().equals(skuPositionInfo.getPositionCode())).mapToInt(StockPrematchInfo::getPrematchQty).sum();
            skuPositionInfo.setPrematchQty(skuPrematchInfoQty);
        }

        // 获取分配规则
        List<BdPrematchRuleItemEntity> prematchRuleItemList = getPrematchRuleItem(stockoutOrder);
        //预配不足的原因
        LinkedList<String> reasonList = new LinkedList<>();
        // 5、获取预配结果
        List<StockSkuPositionInfo> finalSkuPositionInfoList = skuPositionInfoList;
        List<StockPrematchInfoEntity> result = prematchItemBoList.stream().map(prematchItemBo -> {
            // sku 库存信息
            List<StockSkuPositionInfo> filterStockList = finalSkuPositionInfoList.stream().filter(o -> o.getSpecId().equals(prematchItemBo.getSpecId())).collect(Collectors.toList());
            return getPrematchResult(prematchItemBo, prematchRuleItemList, filterStockList, reasonList);
        }).flatMap(Collection::stream).collect(Collectors.toList());

        //如果预配不足，需要记录日志
        if (!CollectionUtils.isEmpty(reasonList)) {
            String join = Strings.join(reasonList, ';');
            stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.STOCK_PREMATCH,
                    String.format("库存预配不足 %s", join.length() >= 480 ? join.substring(0, 479) : join));
        }


        return result;

    }


    /**
     * 获取预配结果
     * <p>
     * 1. 判断策略明细是否符合
     * 2. 库存过滤库位类型
     * 3. 库存按策略排序
     * 4. 判断数量是否充足，生成预配
     * 5. 预配数量回填stockInfo， 防止一直扣一个库位
     *
     * @param prematchItemBo
     * @param prematchRuleItemList
     * @param stockList
     * @param reasonList
     * @return
     */
    private List<StockPrematchInfoEntity> getPrematchResult(StockPrematchItemBo prematchItemBo, List<BdPrematchRuleItemEntity> prematchRuleItemList, List<StockSkuPositionInfo> stockList, List<String> reasonList) {
        List<StockPrematchInfoEntity> result = new LinkedList<>();
        int needQty = prematchItemBo.getQty();
        Integer skuLimitValue = getSkuLimitValue();
        //1. 库存过滤库位类型
        List<StockSkuPositionInfo> filterPositionTypeList = StockPrematchInfoBuild.filterPositionType(stockList);
        //2. 过滤未锁定的库位
        List<StockSkuPositionInfo> currentTypeList = filterPositionTypeList.stream().filter(stockItem -> stockItem.getIsLock().equals(0)).collect(Collectors.toList());
        int unlockQty = currentTypeList.stream().mapToInt(item -> item.getStock() - item.getPrematchQty()).sum();
        int lockQty = filterPositionTypeList.stream().filter(stockItem -> stockItem.getIsLock().equals(1)).mapToInt(item -> item.getStock() - item.getPrematchQty()).sum();
        LOGGER.info("sku:{}，WMS预占数: {} ，库存未锁定数 {} ，库存锁定数: {} ", prematchItemBo.getSku(), needQty, unlockQty, lockQty);
        LOGGER.info("库存预占信息:{}", stockList.stream().map(stock -> String.format("%s %s %s", stock.getPositionCode(), stock.getStock(), stock.getPrematchQty())).collect(Collectors.joining("; ")));
        for (BdPrematchRuleItemEntity ruleItemEntity : prematchRuleItemList) {
            //2. 判断策略明细是否符合
            if (StockPrematchInfoBuild.judgeRuleItemStepOver(ruleItemEntity, skuLimitValue, needQty))
                continue;
            if (currentTypeList.isEmpty())
                continue;
            //3. 库存按策略排序
            currentTypeList = buildByPrematchRuleSort(currentTypeList, ruleItemEntity);
            for (StockSkuPositionInfo stockInfo : currentTypeList) {
                int enableQty = stockInfo.getStock() - stockInfo.getPrematchQty();
                // 4. 判断数量是否充足，生成预配
                if (enableQty <= 0)
                    continue;
                Integer useQty = Math.min(enableQty, needQty);
                StockPrematchInfoEntity resultItem = buildPrematchInfo(stockInfo.getSpaceId(), prematchItemBo);
                resultItem.setPositionId(stockInfo.getPositionId());
                resultItem.setPositionCode(stockInfo.getPositionCode());
                resultItem.setPrematchQty(useQty);
                needQty = needQty - resultItem.getPrematchQty();
                //5. 预配数量回填stockInfo， 防止一直扣一个库位
                stockInfo.setPrematchQty(stockInfo.getPrematchQty() + useQty);
                result.add(resultItem);
                if (needQty <= 0) return result;
            }
        }

        if (needQty > 0) {
            LOGGER.info("sku:{}，库存不足，WMS未预配数:{}", prematchItemBo.getSku(), needQty);
            //记录预配不足原因
            reasonList.add(String.format("sku%s需预配%s件，未锁定库存%s件，锁定库存%s件", prematchItemBo.getSku(), prematchItemBo.getQty(), unlockQty, lockQty));
        }

        return result;
    }

    /**
     * 获取预配策略
     *
     * @param stockoutOrder
     * @return
     */
    private List<BdPrematchRuleItemEntity> getPrematchRuleItem(StockoutOrderEntity stockoutOrder) {
        BdPrematchRuleEntity prematchRuleEntity = prematchRuleService.getPrematchRuleByOrder(stockoutOrder);
        if (prematchRuleEntity == null) {
            throw new BusinessServiceException(String.format("当前出库单： %s，分配规则未配置", stockoutOrder.getStockoutOrderId()));
        }
        List<BdPrematchRuleItemEntity> prematchRuleItemList = prematchRuleItemService.getPrematchRuleItemEntityList(prematchRuleEntity.getPrematchRuleId());
        if (prematchRuleItemList.isEmpty()) {
            throw new BusinessServiceException(String.format("当前出库单： %s，分配规则明细未配置", stockoutOrder.getStockoutOrderId()));
        }
        return prematchRuleItemList;
    }


    /**
     * 获取分配sku阈值
     */
    private Integer getSkuLimitValue() {
        BdSystemParameterEntity bdSystemParameterEntity = systemParameterService.getByKey(BdSystemParameterEnum.WMS_ALLOCATION_RULES_SKU_SET_LIMIT.getKey());
        if (bdSystemParameterEntity == null || bdSystemParameterEntity.getConfigValue() == null) {
            return 0;
        }
        String limitValue = bdSystemParameterEntity.getConfigValue();
        return Integer.valueOf(limitValue);
    }

    /**
     * 库存排序
     *
     * @param currentTypeList
     * @param ruleItemEntity
     */
    private List<StockSkuPositionInfo> buildByPrematchRuleSort(List<StockSkuPositionInfo> currentTypeList, BdPrematchRuleItemEntity ruleItemEntity) {
        StockSkuPositionInfo firstSkuPositionInfo = currentTypeList.get(0);
        //先进先出
        if (bdTagService.checkStockFifo(firstSkuPositionInfo.getSku())) {
            return bdStockFifoService.prematchSort(currentTypeList);
        } else {  //正常流程
            // 根据规则名称排序
            List<StockSkuPositionInfo> sortedList = buildWithSorted(ruleItemEntity.getRuleItemType(), currentTypeList);
            // 优先库位的排序在前
            if (com.nsy.api.core.apicore.util.StringUtils.hasText(ruleItemEntity.getPositionType())) {
                List<StockSkuPositionInfo> otherTypeList = sortedList.stream().filter(o -> !o.getPositionType().equals(ruleItemEntity.getPositionType())).collect(Collectors.toList());
                sortedList = sortedList.stream().filter(o -> o.getPositionType().equals(ruleItemEntity.getPositionType())).collect(Collectors.toList());
                sortedList.addAll(otherTypeList);
            }
            return sortedList;
        }
    }

    /**
     * 适应规则排序
     */
    private List<StockSkuPositionInfo> buildWithSorted(String ruleItemType, List<StockSkuPositionInfo> orignList) {
        if (ruleItemType.equals(StockoutDistributionTypeEnum.CLEAR_LEAST_LOCATION.name())) {
            return orignList.stream().sorted(Comparator.comparing(StockSkuPositionInfo::getStock)
                    .thenComparing(StockSkuPositionInfo::getSpaceAreaSort)
                    .thenComparing(StockSkuPositionInfo::getPositionSort)).collect(Collectors.toList());
        }
        if (ruleItemType.equals(StockoutDistributionTypeEnum.LOCATION_SEQUENCE.name())) {
            return orignList.stream().sorted(Comparator.comparing(StockSkuPositionInfo::getSpaceAreaSort)
                    .thenComparing(StockSkuPositionInfo::getPositionSort)).collect(Collectors.toList());
        }
        return orignList;
    }

    /**
     * 填写胚款信息
     * <p>
     * 是定制款则赋值胚款, 非定制款则赋值普通款
     *
     * @param orderItemBoList
     */
    public void fillBaseSku(List<StockoutOrderItemInfoBo> orderItemBoList) {
        List<Integer> stockoutItemIdList = orderItemBoList.stream().map(StockoutOrderItemInfoBo::getStockoutItemId).distinct().collect(Collectors.toList());
        List<StockoutOrderItemProcessInfoEntity> itemProcessInfoList = itemProcessInfoService.list(new QueryWrapper<StockoutOrderItemProcessInfoEntity>().lambda()
                .in(StockoutOrderItemProcessInfoEntity::getStockoutOrderItemId, stockoutItemIdList));
        Map<Integer, StockoutOrderItemProcessInfoEntity> itemProcessInfoMap = itemProcessInfoList.stream().collect(Collectors.toMap(StockoutOrderItemProcessInfoEntity::getStockoutOrderItemId, Function.identity(), (v1, v2) -> v1));

        //有胚款去查胚款商品信息
        Map<String, ProductSpecInfoEntity> baseSkuMap = new HashMap<>();
        if (!itemProcessInfoList.isEmpty()) {
            List<String> baseSkuList = itemProcessInfoList.stream().map(StockoutOrderItemProcessInfoEntity::getBaseSku).distinct().collect(Collectors.toList());
            baseSkuMap = CollectionUtil.split(baseSkuList, 100).stream()
                    .map(splitBaseSkuList -> productSpecInfoService.findAllBySkuIn(splitBaseSkuList))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity(), (v1, v2) -> v1));
        }

        for (StockoutOrderItemInfoBo orderItemInfo : orderItemBoList) {
            StockoutOrderItemProcessInfoEntity stockoutOrderItemProcessInfoEntity = itemProcessInfoMap.get(orderItemInfo.getStockoutItemId());
            //是定制款则赋值胚款
            if (ObjectUtil.isNotNull(stockoutOrderItemProcessInfoEntity)) {
                ProductSpecInfoEntity baseSpec = baseSkuMap.get(stockoutOrderItemProcessInfoEntity.getBaseSku());
                if (Objects.isNull(baseSpec))
                    throw new BusinessServiceException(String.format("找不到 %s 的胚款商品信息", orderItemInfo.getStockoutItemId()));
                orderItemInfo.setBaseProductId(baseSpec.getProductId());
                orderItemInfo.setBaseSpecId(baseSpec.getSpecId());
                orderItemInfo.setBaseBarcode(baseSpec.getBarcode());
                orderItemInfo.setBaseSku(stockoutOrderItemProcessInfoEntity.getBaseSku());
                continue;
            }

            //非定制款则赋值普通款
            orderItemInfo.setBaseProductId(orderItemInfo.getProductId());
            orderItemInfo.setBaseSpecId(orderItemInfo.getSpecId());
            orderItemInfo.setBaseBarcode(orderItemInfo.getBarcode());
            orderItemInfo.setBaseSku(orderItemInfo.getSku());
        }
    }


    /**
     * 转换成波次
     * 1、 只回填波次号（合并波次号），预配类型改成 BATCH
     * 2、 加工、缝制： 判断是否加工，加工胚款挂子波次上
     * 3. 记录日志
     *
     * @param batchId
     */
    public void convertToBatch(Integer batchId, StockoutBatchLogTypeEnum logType) {
        StockoutBatchEntity batch = batchService.getStockoutBatchById(batchId);
        //合并波次找出子波次
        List<StockoutBatchOrderEntity> stockoutBatchOrderList;
        if (1 == batch.getIsMergeBatch()) {
            List<Integer> subBatchIdList = stockoutBatchMergeService.findBatchWithMerge(batch);
            if (CollectionUtils.isEmpty(subBatchIdList))
                throw new BusinessServiceException(String.format("子波次不存在 %s", batchId));
            stockoutBatchOrderList = batchOrderService.findAllByBatchIds(subBatchIdList);
        } else {
            stockoutBatchOrderList = batchOrderService.findAllByBatchId(batchId);
        }

        if (CollectionUtils.isEmpty(stockoutBatchOrderList))
            throw new BusinessServiceException(String.format("找不到该波次下的出库单 %s", batchId));


        List<Integer> stockoutOrderIdList = stockoutBatchOrderList.stream().map(StockoutBatchOrderEntity::getStockoutOrderId).collect(Collectors.toList());

        List<StockPrematchInfoEntity> prematchInfoList = findByStockoutOrderIdList(stockoutOrderIdList);
        Set<Integer> needAddLogStockoutOrderIdSet = new HashSet<>();
        prematchInfoList.forEach(prematchInfo -> {
            prematchInfo.setPrematchType(StockoutPrematchTypeEnum.BATCH.name());
            //加工和缝制而且是合并波次则不修改波次号
//            if ((PrematchProcessTypeEnum.PROCESS.getValue().equals(prematchInfo.getProcessType()) || PrematchProcessTypeEnum.SEW.getValue().equals(prematchInfo.getProcessType()))
//                    && 1 == batch.getIsMergeBatch())
//                return;
            if (PrematchProcessTypeEnum.PROCESS.getValue().equals(prematchInfo.getProcessType()) && 1 == batch.getIsMergeBatch())
                return;
            prematchInfo.setBatchId(batchId);
            needAddLogStockoutOrderIdSet.add(prematchInfo.getStockoutOrderId());
        });

        needAddLogStockoutOrderIdSet.forEach(stockoutOrderId -> {
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderId(stockoutOrderId);
            stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_PICK, String.format("预配修改波次号 %s", batchId));
        });

        this.updateBatchById(prematchInfoList);

        stockoutBatchLogService.addLog(batchId, logType.getStockoutBatchLogType(), "预配修改波次号");
    }

    /**
     * 转换成出库单
     * 合并波次，则改成子波次号
     * 正常波次，删除波次号（包含子波次），预配类型改成 ORDER
     * 记录日志
     *
     * @param batchId
     */
    public void convertToOrder(Integer batchId, StockoutBatchLogTypeEnum logType) {
        StockoutBatchEntity batch = batchService.getStockoutBatchById(batchId);
        List<StockPrematchInfoEntity> prematchInfoList = findByBatchId(batchId);
        //合并波次，则改成子波次号
        if (1 == batch.getIsMergeBatch()) {
            List<Integer> stockoutOrderIdList = prematchInfoList.stream().map(StockPrematchInfoEntity::getStockoutOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(stockoutOrderIdList)) {
                LOGGER.warn("波次:{} 找不到对应出库单", batch);
                return;
            }
            List<StockoutBatchOrderEntity> batchOrderList = batchOrderMapper.listByStockoutOrderIdListAndBatchType(stockoutOrderIdList, StockoutWavePlanTypeEnum.NORMAL_WAVE.name());
            Map<Integer, Integer> batchStockoutOrderIdMap = batchOrderList.stream().collect(Collectors.toMap(StockoutBatchOrderEntity::getStockoutOrderId, StockoutBatchOrderEntity::getBatchId, (v1, v2) -> v1));
            prematchInfoList.stream().map(prematchInfo -> {
                Integer subBatchId = batchStockoutOrderIdMap.get(prematchInfo.getStockoutOrderId());
                if (Objects.isNull(subBatchId))
                    throw new BusinessServiceException(String.format("出库单 %s 找不到波次号", prematchInfo.getStockoutOrderId()));
                prematchInfo.setBatchId(subBatchId);
                return subBatchId;
            }).distinct().forEach(subBatchId -> {
                stockoutBatchLogService.addLog(subBatchId, logType.getStockoutBatchLogType(), "合并波次撤销，预配修改为波次号");
            });

            updateBatchById(prematchInfoList);
        } else {    //正常波次，删除波次号，预配类型改ORDER
            prematchInfoList.stream().map(StockPrematchInfoEntity::getBatchId).distinct().forEach(tempBatchId -> {
                stockoutBatchLogService.addLog(tempBatchId, logType.getStockoutBatchLogType(), "波次取消，预配删除波次号");
            });
            update(new LambdaUpdateWrapper<StockPrematchInfoEntity>()
                    .set(StockPrematchInfoEntity::getPrematchType, StockoutPrematchTypeEnum.ORDER.name())
                    .set(StockPrematchInfoEntity::getBatchId, null)
                    .eq(StockPrematchInfoEntity::getBatchId, batchId));

        }
    }


    /**
     * 预占合并
     * 库位 + specId
     *
     * @param prematchInfoEntityList
     * @return
     */
    public List<StockPrematchInfo> combinePrematch(List<StockPrematchInfoEntity> prematchInfoEntityList) {
        List<Integer> distinctBatchIdList = prematchInfoEntityList.stream().map(StockPrematchInfoEntity::getBatchId).distinct().collect(Collectors.toList());
        if (distinctBatchIdList.size() > 1) {
            LOGGER.info("预占合并必须是相同波次号 {} ", distinctBatchIdList.stream().map(Object::toString).collect(Collectors.joining(",")));
            throw new BusinessServiceException("预占合并必须是相同波次号");
        }

        List<StockPrematchInfo> prematchInfoList = prematchInfoMapper.stockPrematchInfoByPrematchId(prematchInfoEntityList.stream().map(StockPrematchInfoEntity::getId).collect(Collectors.toList()));
        Map<String, List<StockPrematchInfo>> prematchInfoMap = prematchInfoList.stream().collect(Collectors.groupingBy(temp -> temp.getPositionCode() + "_" + (StrUtil.isEmpty(temp.getCustomerSku()) ? temp.getSku() : temp.getCustomerSku())));
        return prematchInfoMap.values().stream().map(tempPrematchInfoList -> {
            StockPrematchInfo stockPrematchInfo = tempPrematchInfoList.get(0);
            stockPrematchInfo.setPrematchQty(tempPrematchInfoList.stream().mapToInt(StockPrematchInfo::getPrematchQty).sum());
            return stockPrematchInfo;
        }).collect(Collectors.toList());
    }


    /**
     * 根据 波次出库单明细 构造 库位库存预占
     */
    public StockPrematchInfoEntity buildPrematchInfo(Integer spaceId, StockoutOrderItemInfoBo batchOrderItemInfo) {
        StockPrematchInfoEntity prematchInfoEntity = new StockPrematchInfoEntity();
        prematchInfoEntity.setLocation(batchOrderItemInfo.getLocation());
        prematchInfoEntity.setProductId(batchOrderItemInfo.getBaseProductId());
        prematchInfoEntity.setSpecId(batchOrderItemInfo.getBaseSpecId());
        prematchInfoEntity.setSku(batchOrderItemInfo.getBaseSku());
        if (1 == batchOrderItemInfo.getIsNeedProcess())
            prematchInfoEntity.setCustomerSku(batchOrderItemInfo.getSku());
        StockoutOrderItemEntity orderItemEntity = stockoutOrderItemService.getById(batchOrderItemInfo.getStockoutItemId());
        prematchInfoEntity.setStockoutOrderId(orderItemEntity.getStockoutOrderId());
        prematchInfoEntity.setPrematchQty(0);
        prematchInfoEntity.setPickedQty(0);
        prematchInfoEntity.setProcessType(fetchPrematchProcessType(batchOrderItemInfo.getIsNeedProcess(), batchOrderItemInfo.getChangeType()).getValue());
        prematchInfoEntity.setStockoutOrderItemId(batchOrderItemInfo.getStockoutItemId());
        prematchInfoEntity.setSpaceId(spaceId);
        return prematchInfoEntity;
    }

    /**
     * 根据 波次出库单明细 构造 库位库存预占
     */
    public StockPrematchInfoEntity buildPrematchInfo(Integer spaceId, StockPrematchItemBo prematchItemBo) {
        StockPrematchInfoEntity prematchInfoEntity = new StockPrematchInfoEntity();
        prematchInfoEntity.setProductId(prematchItemBo.getProductId());
        prematchInfoEntity.setSpecId(prematchItemBo.getSpecId());
        prematchInfoEntity.setSku(prematchItemBo.getSku());
        if (1 == prematchItemBo.getIsNeedProcess()) {
            StockoutOrderItemProcessInfoEntity itemProcessInfoEntity = itemProcessInfoService.findFirstByStockoutOrderItemId(prematchItemBo.getStockoutOrderItemId());
            if (Objects.isNull(itemProcessInfoEntity))
                throw new BusinessServiceException(String.format("明细 %s 的加工信息不存在", prematchItemBo.getStockoutOrderItemId()));
            prematchInfoEntity.setCustomerSku(itemProcessInfoEntity.getCustomerSku());
        }
        StockoutOrderItemEntity orderItemEntity = stockoutOrderItemService.getById(prematchItemBo.getStockoutOrderItemId());
        prematchInfoEntity.setStockoutOrderId(orderItemEntity.getStockoutOrderId());
        prematchInfoEntity.setPrematchQty(0);
        prematchInfoEntity.setPickedQty(0);
        prematchInfoEntity.setProcessType(fetchPrematchProcessType(orderItemEntity.getIsNeedProcess(), orderItemEntity.getChangeType()).getValue());
        prematchInfoEntity.setStockoutOrderItemId(prematchItemBo.getStockoutOrderItemId());
        prematchInfoEntity.setSpaceId(spaceId);
        return prematchInfoEntity;
    }


    /**
     * 获得sku在某个库位上的分配数
     *
     * @param sku
     * @param positionCode
     * @return
     */
    public Integer getSkuPreQty(String sku, String positionCode) {
        LambdaQueryWrapper<StockPrematchInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockPrematchInfoEntity::getSku, sku);
        wrapper.eq(StringUtils.hasText(positionCode), StockPrematchInfoEntity::getPositionCode, positionCode);
        List<StockPrematchInfoEntity> list = this.list(wrapper);
        int preQty = 0;
        if (CollectionUtils.isEmpty(list)) {
            return preQty;
        }
        for (StockPrematchInfoEntity item : list) {
            int qty = item.getPrematchQty() == null ? 0 : item.getPrematchQty();
            preQty += qty;
        }
        return preQty;
    }


    /**
     * 获得sku在某个库位上的分配数
     *
     * @param sku
     * @param positionCodeList
     * @return
     */
    public Integer getSkuPreQty(String sku, List<String> positionCodeList) {
        LambdaQueryWrapper<StockPrematchInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockPrematchInfoEntity::getSku, sku);
        wrapper.in(StockPrematchInfoEntity::getPositionCode, positionCodeList);
        List<StockPrematchInfoEntity> list = this.list(wrapper);
        int preQty = 0;
        if (CollectionUtils.isEmpty(list)) {
            return preQty;
        }
        for (StockPrematchInfoEntity item : list) {
            int qty = item.getPrematchQty() == null ? 0 : item.getPrematchQty();
            preQty += qty;
        }
        return list.stream().mapToInt(StockPrematchInfoEntity::getPrematchQty).sum();
    }


    /**
     * 通过出库单Id查找
     *
     * @param stockoutOrderIds
     * @return
     */
    public List<StockPrematchInfoEntity> findByStockoutOrderIdList(List<Integer> stockoutOrderIds) {
        if (CollectionUtils.isEmpty(stockoutOrderIds))
            return Collections.emptyList();
        return CollectionUtil.split(stockoutOrderIds, 100).stream().map(splitList ->
                this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>().in(StockPrematchInfoEntity::getStockoutOrderId, splitList))
        ).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 通过出库单Id查找
     *
     * @param stockoutOrderId
     * @return
     */
    public List<StockPrematchInfoEntity> findByStockoutOrderId(Integer stockoutOrderId) {
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>().eq(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrderId));
    }

    /**
     * 波次ID查找
     *
     * @param batchId
     * @return
     */
    public List<StockPrematchInfoEntity> findByBatchId(Integer batchId) {
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>().eq(StockPrematchInfoEntity::getBatchId, batchId));
    }

    public List<StockPrematchInfoEntity> findBySpaceIdAndSkuList(Integer spaceId, List<String> skuList) {
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>().eq(StockPrematchInfoEntity::getSpaceId, spaceId)
                .in(CollectionUtil.isNotEmpty(skuList), StockPrematchInfoEntity::getSku, skuList));
    }

    public List<StockPrematchInfoEntity> findByBatchIdList(List<Integer> batchIdList) {
        if (CollectionUtils.isEmpty(batchIdList)) return Collections.emptyList();
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>().in(StockPrematchInfoEntity::getBatchId, batchIdList));
    }

    /**
     * 波次ID查找
     *
     * @param stockoutOrderId
     * @return
     */
    public List<StockPrematchInfoEntity> findByStockoutOrderIdAndSpecIdAndPositionCode(Integer stockoutOrderId, Integer specId, String positionCode) {
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrderId)
                .eq(StockPrematchInfoEntity::getSpecId, specId)
                .eq(StockPrematchInfoEntity::getPositionCode, positionCode));
    }


    /**
     * 通过波次和子波次id查找
     *
     * @param batchId
     * @return
     */
    public List<StockPrematchInfoEntity> findWithSubBatchByBatchId(Integer batchId) {
        StockoutBatchEntity batch = batchService.getStockoutBatchById(batchId);
        List<Integer> batchIdList = new ArrayList<>();
        batchIdList.add(batch.getBatchId());
        if (1 == batch.getIsMergeBatch()) {
            List<Integer> subBatchIdList = stockoutBatchMergeService.findBatchWithMerge(batch);
            batchIdList.addAll(subBatchIdList);
        }

        return findByBatchIdList(batchIdList);
    }

    public List<StockPrematchInfoEntity> findByBatchIdSkuAndPositionCode(Integer batchId, String sku, String positionCode) {
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getBatchId, batchId)
                .eq(StockPrematchInfoEntity::getSku, sku)
                .eq(StockPrematchInfoEntity::getPositionCode, positionCode));
    }

    public List<StockPrematchInfoEntity> findBySkuAndPositionCode(String sku, String positionCode) {
        return this.list(new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getSku, sku)
                .eq(StockPrematchInfoEntity::getPositionCode, positionCode));
    }

    public StockPrematchInfoEntity findOneByStockoutOrderItemIdAndPositionCodeAndLackOriginType(Integer stockoutOrderItemId, String positionCode, StockoutPrematchLackOriginTypeEnum lackOriginType) {
        LambdaQueryWrapper<StockPrematchInfoEntity> wrapper = new LambdaQueryWrapper<StockPrematchInfoEntity>()
                .eq(StockPrematchInfoEntity::getStockoutOrderItemId, stockoutOrderItemId)
                .eq(StockPrematchInfoEntity::getPositionCode, positionCode);
        if (Objects.isNull(lackOriginType)) {
            wrapper.and(o -> o.isNull(StockPrematchInfoEntity::getLackOriginType).or().eq(StockPrematchInfoEntity::getLackOriginType, ""));
        } else {
            wrapper.eq(StockPrematchInfoEntity::getLackOriginType, lackOriginType.name());
        }
        return this.getOne(wrapper.last("limit 1"));
    }

    /**
     * 获取预配加工类型
     * 优先加工 然后缝制 都不是则普通
     *
     * @param isNeedProcess
     * @param changeType
     * @return
     */
    private PrematchProcessTypeEnum fetchPrematchProcessType(Integer isNeedProcess, String changeType) {
        return 1 == isNeedProcess ? PrematchProcessTypeEnum.PROCESS : StrUtil.isEmpty(changeType) ? PrematchProcessTypeEnum.NORMAL : PrematchProcessTypeEnum.SEW;
    }

    public void saveOrUpdatePrematch(List<StockPrematchInfoEntity> prematchResultList, StockoutPrematchLackOriginTypeEnum lackOriginType) {
        LOGGER.info("预占信息:{}", prematchResultList.stream().map(prematchInfo -> String.format("%s %s %s", prematchInfo.getStockoutOrderItemId(), prematchInfo.getPositionCode(), prematchInfo.getPrematchQty())).collect(Collectors.joining("; ")));
        List<StockPrematchInfoEntity> saveList = new ArrayList<>();
        List<StockPrematchInfoEntity> updateList = new ArrayList<>();
        //部分预配的情况，需要再之前预配的基础上加
        prematchResultList.forEach(prematchResult -> {
            StockPrematchInfoEntity oldPrematchInfoEntity = findOneByStockoutOrderItemIdAndPositionCodeAndLackOriginType(prematchResult.getStockoutOrderItemId(), prematchResult.getPositionCode(), lackOriginType);
            if (Objects.isNull(oldPrematchInfoEntity)) {
                saveList.add(prematchResult);
            } else {
                LOGGER.info(" {} 存在预占明细 {} ,则添加 {} ", prematchResult.getSku(), oldPrematchInfoEntity.getId(), prematchResult.getPrematchQty());
                oldPrematchInfoEntity.setPrematchQty(oldPrematchInfoEntity.getPrematchQty() + prematchResult.getPrematchQty());
                prematchResult.setId(oldPrematchInfoEntity.getId());
                updateList.add(oldPrematchInfoEntity);
            }
        });
        if (!saveList.isEmpty()) {
            this.saveBatch(saveList);
        }
        if (!updateList.isEmpty()) {
            this.updateBatchById(updateList);
        }
    }

    public PageResponse<StockPrematchInfoResponse> getPrematchInfoPage(StockPrematchInfoPageRequest request) {
        PageResponse<StockPrematchInfoResponse> pageResponse = new PageResponse<>();
        IPage<StockPrematchInfoResponse> pageResult = this.baseMapper.getStockPrematchInfoPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        Map<String, String> stockoutStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_BUSINESS_UNIT.getName());
        pageResult.getRecords().forEach(record -> record.setBusinessType(stockoutStatusEnumMap.get(record.getBusinessType())));

        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }

    /**
     * 查找可用库存 存储库位 库存 - 预配
     *
     * @param stockoutOrderItemEntity
     * @return
     */
    public Integer getAvailableStock(StockoutOrderItemEntity stockoutOrderItemEntity) {
        //是否加工
        StockoutOrderItemProcessInfoEntity itemProcessInfo = itemProcessInfoService.getOne(new QueryWrapper<StockoutOrderItemProcessInfoEntity>()
                .lambda()
                .eq(StockoutOrderItemProcessInfoEntity::getStockoutOrderItemId, stockoutOrderItemEntity.getStockoutOrderItemId())
                .last("limit 1"));
        String sku = Objects.isNull(itemProcessInfo) ? stockoutOrderItemEntity.getSku() : itemProcessInfo.getBaseSku();
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderId(stockoutOrderItemEntity.getStockoutOrderId());
        BdAreaEntity area = areaService.getByAreaNameAndSpaceId(stockoutOrder.getAreaName(), stockoutOrder.getSpaceId());
        Integer stock = stockMapper.sumStockInStockPositionByAreaIdAndSku(sku, area.getAreaId());
        Integer prematchQty = baseMapper.sumPrematchInStockPositionByAreaIdAndSku(sku, area.getAreaId());
        return stock - prematchQty;
    }
}
