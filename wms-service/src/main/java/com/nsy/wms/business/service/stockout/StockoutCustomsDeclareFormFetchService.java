package com.nsy.wms.business.service.stockout;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.wms.business.manage.external.DeclareFormApiService;
import com.nsy.wms.business.manage.external.request.DeclareFormFetchRequest;
import com.nsy.wms.business.manage.external.response.DeclareFormFetchResponse;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HXD
 * 2021/9/15
 **/
@Service
public class StockoutCustomsDeclareFormFetchService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareFormFetchService.class);
    @Autowired
    DeclareFormApiService declareFormApiService;
    @Autowired
    StockoutCustomsDeclareFormMQService declareFormMQService;
    @Autowired
    BdSystemParameterService parameterService;
    //api接口-新时颖电子
    @Value("${declare.form.fetch.channel-xsydz}")
    private String declareFormFetchChannelXsydz;
    @Value("${declare.form.fetch.key-xsydz}")
    private String declareFormFetchKeyXsydz;
//    //澜晞服饰
//    @Value("${declare.form.fetch.channel-lxfs}")
//    private String declareFormFetchChannelLxfs;
//    @Value("${declare.form.fetch.key-lxfs}")
//    private String declareFormFetchKeyLxfs;
//    // 时颖服饰
//    @Value("${declare.form.fetch.channel-syfs}")
//    private String declareFormFetchChannelSyfs;
//    @Value("${declare.form.fetch.key-syfs}")
//    private String declareFormFetchKeySyfs;
//    // 晞景商贸
//    @Value("${declare.form.fetch.channel-xjsm}")
//    private String declareFormFetchChannelXjsm;
//    @Value("${declare.form.fetch.key-xjsm}")
//    private String declareFormFetchKeyXjsm;



    private void startFetch(DeclareFormFetchRequest request, List<String> errorList, String declareFormFetchChannel, String declareFormFetchKey, Boolean justPrint) {
        LOGGER.info("开始获取口岸信息， {} ", JsonMapper.toJson(request));
        DeclareFormFetchResponse declareFormFetchResponse = declareFormApiService.fetchDeclareFormData(request, declareFormFetchChannel, declareFormFetchKey);
        if (declareFormFetchResponse == null || declareFormFetchResponse.getDecMessage() == null) {
            LOGGER.error("国综平台未返回数据，暂无数据更新");
            return;
        }

        //只打印 不新增
        if (justPrint) return;

        LOGGER.info("通过接口更新");
        // 如果有返回数据，就直接去 更新/新增
        declareFormFetchResponse.getDecMessage().forEach(decMessage -> {
            try {
                declareFormMQService.addOrUpdateForm(decMessage);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                errorList.add(e.getMessage());
            }
        });
    }

    public void fetchData(DeclareFormFetchRequest request) {
        List<String> errorList = new ArrayList<>();
        if (StrUtil.isNotBlank(request.getdDate())) {
            BdSystemParameterEntity rangeConfig = parameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_DECLARE_FORM_FETCH_RANGE.getKey());
            if (rangeConfig == null || !StringUtils.hasText(rangeConfig.getConfigValue()))
                throw new BusinessServiceException("关单获取时间长度未配置");
            int range = Integer.parseInt(rangeConfig.getConfigValue());
            request.setDayNum(range);
            fetchByDDate(request, errorList);
        } else if (StrUtil.isNotBlank(request.getEntryId())) {
            fetchByEntryId(request, errorList);
        } else if (StrUtil.isNotBlank(request.getiEDate())) {
            BdSystemParameterEntity rangeConfig = parameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_DECLARE_FORM_FETCH_RANGE.getKey());
            if (rangeConfig == null || !StringUtils.hasText(rangeConfig.getConfigValue()))
                throw new BusinessServiceException("关单获取时间长度未配置");
            int range = Integer.parseInt(rangeConfig.getConfigValue());
            request.setDayNum(range);
            fetchByIEDate(request, errorList);
        } else if (StrUtil.isNotBlank(request.getdDateBegin()) && StrUtil.isNotBlank(request.getdDateEnd())) {
            fetchByDDateRange(request, errorList);
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            throw new BusinessServiceException(String.join(";", errorList));
        }
    }

    // 根据日期来获取关单数据
    @Transactional
    public void fetchData(DeclareFormFetchRequest request, List<String> errorList, Boolean justPrint) {
        Map<String, String> map = new HashMap<>();
        map.put(declareFormFetchChannelXsydz, declareFormFetchKeyXsydz);
//        map.put(declareFormFetchChannelLxfs, declareFormFetchKeyLxfs);
//        map.put(declareFormFetchChannelSyfs, declareFormFetchKeySyfs);
//        map.put(declareFormFetchChannelXjsm, declareFormFetchKeyXjsm);
        map.forEach((k, v) -> {
            try {
                startFetch(request, errorList, k, v, justPrint);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                errorList.add(e.getMessage());
            }
        });
    }

    private void fetchByDDate(DeclareFormFetchRequest request, List<String> errorList) {
        for (int i = 0; i <= request.getDayNum(); i++) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtil.parse(request.getdDate()));
            cal.add(Calendar.DATE, -i);
            Date d = cal.getTime();
            DeclareFormFetchRequest request1 = new DeclareFormFetchRequest();
            request1.setdDate(DateUtil.format(d, "yyyy-MM-dd HH:mm:ss"));
            SpringUtil.getBean(StockoutCustomsDeclareFormFetchService.class).fetchData(request1, errorList, request.getJustPrint());
            try {
                Thread.sleep(6000);
            } catch (InterruptedException e) {
                LOGGER.error("error", e);
            }
        }
    }

    private void fetchByEntryId(DeclareFormFetchRequest request, List<String> errorList) {
        DeclareFormFetchRequest request1 = new DeclareFormFetchRequest();
        request1.setEntryId(request.getEntryId());
        SpringUtil.getBean(StockoutCustomsDeclareFormFetchService.class).fetchData(request1, errorList, request.getJustPrint());
    }

    private void fetchByIEDate(DeclareFormFetchRequest request, List<String> errorList) {
        for (int i = 0; i <= request.getDayNum(); i++) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(DateUtil.parse(request.getiEDate()));
            cal.add(Calendar.DATE, -i);
            Date d = cal.getTime();
            DeclareFormFetchRequest request1 = new DeclareFormFetchRequest();
            request1.setiEDate(DateUtil.format(d, "yyyy-MM-dd HH:mm:ss"));
            SpringUtil.getBean(StockoutCustomsDeclareFormFetchService.class).fetchData(request1, errorList, request.getJustPrint());
            try {
                Thread.sleep(6000);
            } catch (InterruptedException e) {
                LOGGER.error("error", e);
            }
        }
    }

    private void fetchByDDateRange(DeclareFormFetchRequest request, List<String> errorList) {
        long day = DateUtil.betweenDay(DateUtil.parse(request.getdDateBegin(), "yyyy-MM-dd"), DateUtil.parse(request.getdDateEnd(), "yyyy-MM-dd"), true);
        request.setdDate(request.getdDateEnd());
        request.setDayNum((int) day);
        fetchByDDate(request, errorList);
    }

}
