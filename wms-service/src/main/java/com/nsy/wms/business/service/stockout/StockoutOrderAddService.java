package com.nsy.wms.business.service.stockout;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ErpConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.enumeration.CustomsDeclareEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.LogisticsTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCheckTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutDeliveryNoticeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderBusinessTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderMergeStateEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutOrderAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderLabelAddRequest;
import com.nsy.api.wms.request.stockout.StockoutPackAddRequest;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.BdAreaCommonPositionService;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdPickingTypeRuleService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stockout.handle.StockoutTypeFactory;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.bd.BdTagMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLabelEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPackMappingInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderSheinExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderSheinExtendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTikTokExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTikTokExtendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutReceiverInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderLabelMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.encryption.AesEncryptUtil;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 出库单新增相关操作
 */
@Service
public class StockoutOrderAddService {
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutReceiverInfoService receiverInfoService;
    @Autowired
    StockoutShipperInfoService shipperInfoService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    StockoutOrderLabelMapper labelMapper;
    @Autowired
    StockPrematchInfoService prematchInfoService;
    @Autowired
    BdPickingTypeRuleService pickingTypeRuleService;
    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    BdSpaceService bdSpaceService;
    @Autowired
    StockoutOrderPackMappingInfoService packMappingInfoService;
    @Autowired
    StockoutTypeFactory stockoutTypeFactory;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    StockoutCheckQueueService checkQueueService;
    @Autowired
    MessageProducer producer;
    @Autowired
    StockoutOrderTemuExtendService temuExtendService;
    @Autowired
    StockoutOrderTemuExtendItemService temuExtendItemService;
    @Autowired
    BdAreaCommonPositionService bdAreaCommonPositionService;
    @Autowired
    StockoutOrderSheinExtendService sheinExtendService;
    @Autowired
    StockoutOrderTikTokExtendService tikTokExtendService;
    @Autowired
    StockoutOrderTikTokExtendItemService tikTokExtendItemService;
    @Autowired
    StockoutOrderSheinExtendItemService sheinExtendItemService;
    @Autowired
    StockoutOrderMapper stockoutOrderMapper;
    @Autowired
    StockoutOrderItemMapper stockoutOrderItemMapper;
    @Autowired
    BdSystemParameterService parameterService;
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;
    @Autowired
    BdTagMappingService bdTagMappingService;
    @Autowired
    BrandCommonService brandCommonService;
    @Resource
    StockoutOrderMemoService stockoutOrderMemoService;

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderAddService.class);

    /**
     * 合单操作
     */
    public Integer mergeStockoutOrder(StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> outOrderItemEntityList, StockoutReceiverInfo receiverInfo) {
        if (receiverInfo == null) {
            throw new BusinessServiceException("收货人信息不能为空！");
        }
        // String[] outOrderStatus = {StockoutOrderStatusEnum.READY.name(), StockoutOrderStatusEnum.READY_WAVE_GENERATED.name()};
        List<StockoutReceiverInfoEntity> sameReceiverInfoList = new ArrayList<>();
        // 合并出库单暂时关闭
        // List<StockoutReceiverInfoEntity> sameReceiverInfoList = receiverInfoService.getSameReceiverInfo(md5Result, outOrderStatus);
        if (sameReceiverInfoList.isEmpty()) {
            String encryptResult = AesEncryptUtil.encrypt(JsonMapper.toJson(receiverInfo));
            String md5Result = MD5Util.crypt(encryptResult);
            receiverInfoService.addReceiverInfo(stockoutOrderEntity, receiverInfo, encryptResult, md5Result);
            shipperInfoService.addShipperInfo(stockoutOrderEntity, stockoutOrderEntity.getLocation(), stockoutOrderEntity.getCreateBy());
            return -1;
        } else {
            StockoutReceiverInfoEntity receiverInfoEntity = sameReceiverInfoList.get(0);
            StockoutOrderEntity sameStockoutOrderEntity = stockoutOrderService.getById(receiverInfoEntity.getStockoutOrderId());
            if (sameStockoutOrderEntity == null) {
                throw new BusinessServiceException("未找到相同收货人信息的出库单");
            }
            List<StockoutOrderItemEntity> childOrderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().eq("stockout_order_id", sameStockoutOrderEntity.getStockoutOrderId()));
            childOrderItemEntityList.addAll(outOrderItemEntityList);

            StockoutOrderEntity parentOutOrderEntity = createParentOrder(sameStockoutOrderEntity, childOrderItemEntityList);

            setChildMergeStatus(stockoutOrderEntity, parentOutOrderEntity.getStockoutOrderId(), parentOutOrderEntity.getStockoutOrderNo());
            setChildMergeStatus(sameStockoutOrderEntity, parentOutOrderEntity.getStockoutOrderId(), parentOutOrderEntity.getStockoutOrderNo());

            receiverInfoService.updateReceiverInfo(receiverInfoEntity, parentOutOrderEntity, stockoutOrderEntity.getCreateBy());
            shipperInfoService.addShipperInfo(parentOutOrderEntity, stockoutOrderEntity.getLocation(), stockoutOrderEntity.getCreateBy());

            // 出库单 - 拣货模式
            String pickingTypeEnumStr = pickingTypeRuleService.getPickingTypeEnumStr(parentOutOrderEntity.getStockoutOrderId(), parentOutOrderEntity.getSpaceId(), parentOutOrderEntity.getStockoutType(), parentOutOrderEntity.getWorkspace());
            parentOutOrderEntity.setPickingType(pickingTypeEnumStr);
            stockoutOrderService.updateById(parentOutOrderEntity);

            return parentOutOrderEntity.getStockoutOrderId();
        }
    }

    /**
     * 新增一笔包含原单和加单的商品的出库单
     */
    private StockoutOrderEntity createParentOrder(StockoutOrderEntity sameStockoutOrderEntity, List<StockoutOrderItemEntity> childOrderItemEntityList) {
        StockoutOrderEntity parentOutOrderEntity = new StockoutOrderEntity();
        BeanUtils.copyProperties(sameStockoutOrderEntity, parentOutOrderEntity, "stockoutOrderId");
        parentOutOrderEntity.setStockoutOrderNo(FormNoGenerateUtil.generateFormNo(stockoutOrderService.getFormNoTypeEnumByStockoutOrderType(StockoutOrderTypeEnum.getBy(sameStockoutOrderEntity.getStockoutType()))));
        parentOutOrderEntity.setMergeState(StockoutOrderMergeStateEnum.PARENT.name());
        parentOutOrderEntity.setCreateBy(loginInfoService.getName());
        stockoutOrderService.save(parentOutOrderEntity);
        String content = String.format("合并出库单【%s】生成", parentOutOrderEntity.getStockoutOrderNo());
        logService.addLog(parentOutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.STOCKOUT_ORDER_GENERATE, content);
        stockoutLogQueueService.addLogQueue(Stream.of(parentOutOrderEntity.getStockoutOrderId()).collect(Collectors.toList()), content);

        List<StockoutOrderItemEntity> parentOrderItemList = new LinkedList<>();
        for (StockoutOrderItemEntity childEntity : childOrderItemEntityList) {
            StockoutOrderItemEntity parentOrderItemEntity = parentOrderItemList.stream().filter(o -> o.getOrderItemId().equals(childEntity.getOrderItemId()) && o.getSku().equals(childEntity.getSku())).findFirst().orElse(null);
            if (parentOrderItemEntity != null) {
                parentOrderItemEntity.setQty(parentOrderItemEntity.getQty() + childEntity.getQty());
            } else {
                parentOrderItemEntity = new StockoutOrderItemEntity();
                BeanUtils.copyProperties(childEntity, parentOrderItemEntity, "stockoutOrderItemId");
                parentOrderItemEntity.setStockoutOrderId(parentOutOrderEntity.getStockoutOrderId());
                parentOrderItemList.add(parentOrderItemEntity);
            }
        }
        if (!parentOrderItemList.isEmpty())
            stockoutOrderItemService.saveBatch(parentOrderItemList);

        return parentOutOrderEntity;
    }

    /**
     * 设置合并子单
     */
    private void setChildMergeStatus(StockoutOrderEntity childOutOrderEntity, Integer parentOutOrderId, String parentOutOrderNo) {
        childOutOrderEntity.setMergeState(StockoutOrderMergeStateEnum.CHILD.name());
        childOutOrderEntity.setMergeStockoutOrderId(parentOutOrderId);
        childOutOrderEntity.setStatus(StockoutOrderStatusEnum.CANCELLED.name());
        producer.sendMessage(KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), childOutOrderEntity.getStockoutOrderId()));
        stockoutOrderService.saveOrUpdate(childOutOrderEntity);
        String content = String.format("合并出库单【%s】->出库单【%s】", childOutOrderEntity.getStockoutOrderNo(), parentOutOrderNo);
        logService.addLog(childOutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.MERGE_STOCKOUT, content);
    }

    /**
     * 出库单面单
     *
     * @param stockoutOrderId
     * @param addRequest
     */
    public void addStockoutOrderLabel(Integer stockoutOrderId, StockoutOrderAddRequest addRequest) {
        StockoutOrderLabelAddRequest labelInfo = addRequest.getLabelInfo();
        if (labelInfo != null && (StringUtils.hasText(labelInfo.getLabelUrl()) || StringUtils.hasText(labelInfo.getPrintContent()))) {
            StockoutOrderLabelEntity labelEntity = new StockoutOrderLabelEntity();
            BeanUtils.copyProperties(labelInfo, labelEntity);
            labelEntity.setStockoutOrderId(stockoutOrderId);
            labelEntity.setCreateBy(labelInfo.getCreateBy());
            labelEntity.setLocation(addRequest.getLocation());
            labelMapper.insert(labelEntity);
        }
    }


    /**
     * 新增订单备注
     */
    public void addMemo(Integer stockoutOrderId) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(stockoutOrderId);
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderId);
        // 备注要同步到主表
        String description = stockoutOrderMemoService.getStockoutOrderMemo(stockoutOrderItemEntities);
        stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
        stockoutOrderEntity.setDescription(stockoutOrderItemService.processErpMemo(description, 490));
        stockoutOrderEntity.setStockoutOrderId(stockoutOrderId);
        stockoutOrderService.updateById(stockoutOrderEntity);
    }

    public void changePickingType(StockoutOrderEntity stockoutOrderEntity) {
        // 出库单 - 拣货模式
        String pickingTypeEnumStr = pickingTypeRuleService.getPickingTypeEnumStr(stockoutOrderEntity.getStockoutOrderId(), stockoutOrderEntity.getSpaceId(), stockoutOrderEntity.getStockoutType(), stockoutOrderEntity.getWorkspace());
        stockoutOrderEntity.setPickingType(pickingTypeEnumStr);
        // 变更出库单拣货模式
        String changePickingType = changeStockoutOrderPickingType(stockoutOrderEntity);
        stockoutOrderService.saveOrUpdate(stockoutOrderEntity);
        String stockoutOrderNo = stockoutOrderEntity.getStockoutOrderNo();
        // 借用出库单无对应商通拣货单id
        String erpPickId = stockoutOrderEntity.getErpPickId() != null ? stockoutOrderEntity.getErpPickId().toString() : "无";
        String content = String.format("erp拣货单id[%s]在wms生成出库单[%s]，匹配拣货模式[%s];%s", erpPickId, stockoutOrderNo, StockoutPickingTypeEnum.getNameBy(pickingTypeEnumStr), changePickingType);

        logService.addStockoutOrderLog(StockoutOrderLogTypeEnum.CHANGE_PICKING_TYPE, stockoutOrderNo, content, stockoutOrderEntity.getCreateBy(), stockoutOrderEntity.getLocation());
        stockoutLogQueueService.addLogQueue(Stream.of(stockoutOrderEntity.getStockoutOrderId()).collect(Collectors.toList()), content);
    }

    /**
     * 构建出库单
     *
     * @param addRequest
     * @return
     */
    public StockoutOrderEntity buildStockoutOrder(StockoutOrderAddRequest addRequest) {
        StockoutOrderEntity stockoutOrderEntity = new StockoutOrderEntity();
        StockoutOrderInfo stockoutOrderInfo = addRequest.getOutOrderInfo();
        BeanUtils.copyProperties(stockoutOrderInfo, stockoutOrderEntity);
        stockoutOrderEntity.setFbaReplenish(Objects.nonNull(stockoutOrderInfo.getIsFbaReplenish()) && stockoutOrderInfo.getIsFbaReplenish() == 1 ? Boolean.TRUE : Boolean.FALSE);
        if (!StringUtils.hasText(stockoutOrderInfo.getFbaReplenishType()))
            stockoutOrderEntity.setFbaReplenishType(FbaReplenishTypeEnum.NORMAL.getName());
        stockoutOrderEntity.setStockoutType(stockoutOrderInfo.getTypeEnum().name());
        stockoutOrderEntity.setStockoutOrderNo(FormNoGenerateUtil.generateFormNo(stockoutOrderService.getFormNoTypeEnumByStockoutOrderType(stockoutOrderInfo.getTypeEnum())));
        stockoutOrderEntity.setNotifyShipStatus(stockoutOrderInfo.getNotifyShip() == 0 ? StockoutDeliveryNoticeEnum.WAIT_NOTIC_DELIVERY.name() : StockoutDeliveryNoticeEnum.NORMAL_DELIVERY.name());
        stockoutOrderEntity.setCustomsDeclareType(stockoutOrderInfo.getCustomerDeclareType() == 1 ? CustomsDeclareEnum.DECLARE_9610.getValue() : CustomsDeclareEnum.DECLARE_NORMAL.getValue());
        stockoutOrderEntity.setLack(Boolean.FALSE);
        stockoutOrderEntity.setPickingType("");
        stockoutOrderEntity.setCreateBy(stockoutOrderInfo.getCreateBy());
        stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.WAIT_PRE_MATCH.name());
        stockoutOrderEntity.setHasPack(stockoutOrderInfo.getPack());
        // 出库单 - 仓库 + 区域
        this.updateSpaceAndAreaInfo(stockoutOrderEntity, addRequest);
        // 设置物流
        resetOrderLogisticsCompany(stockoutOrderEntity, stockoutOrderInfo);
        // 出库单 - 工作区域
        stockoutOrderEntity.setWorkspace(this.getWorkSpaceByLogisticsCompany(stockoutOrderInfo));
        if (StrUtil.equalsIgnoreCase(stockoutOrderInfo.getTypeEnum().name(), StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name()))
            stockoutOrderEntity.setFbaLabelStatus(FbaLabelStatusEnum.WAIT.name());
        // 如果该物流单号 在出库单中已经存在了，那就置空物流单号 【排除 包裹异常 - 是否沿用旧单号】
        if (StringUtils.hasText(stockoutOrderEntity.getLogisticsNo()) && stockoutOrderInfo.getUseOldLogisticsNo() <= 0) {
            LambdaQueryWrapper<StockoutOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StockoutOrderEntity::getLogisticsNo, stockoutOrderEntity.getLogisticsNo()).ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLED.name());
            LambdaQueryWrapper<StockoutShipmentEntity> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(StockoutShipmentEntity::getLogisticsNo, stockoutOrderEntity.getLogisticsNo()).eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
            int count = stockoutOrderService.count(wrapper) + shipmentService.count(wrapper1);
            if (count > 0) {
                stockoutOrderEntity.setLogisticsNo(null);
                addRequest.setLabelInfo(null);
            }
        }
        // 设置规格/状态【海外发货特殊case】/物流单号
        stockoutTypeFactory.differType(stockoutOrderInfo.getTypeEnum().name()).setStatusAndLogisticsInfo(stockoutOrderInfo, stockoutOrderEntity, addRequest);
        stockoutOrderService.saveOrUpdate(stockoutOrderEntity);

        logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.STOCKOUT_ORDER_GENERATE,
                String.format("出库单生成, 发货通知 【%s】", StockoutDeliveryNoticeEnum.of(stockoutOrderEntity.getNotifyShipStatus())));
        // 加入出库核对队列
        List<String> orderNoList = addRequest.getOutOrderItemList().stream().map(StockoutOrderItemAddRequest::getOrderNo).distinct().collect(Collectors.toList());
        orderNoList.forEach(o -> {
            checkQueueService.addQueue(stockoutOrderEntity.getStockoutOrderNo(), StockoutCheckTypeEnum.STOCKOUT.name(), o);
        });
        //设置购买配送
        stockoutOrderEntity.setAmazonBuyShippingInfo(addRequest.getOutOrderItemList().get(0).getAmazonBuyShippingInfo());
        return stockoutOrderEntity;
    }

    private void resetOrderLogisticsCompany(StockoutOrderEntity stockoutOrderEntity, StockoutOrderInfo stockoutOrderInfo) {
        // 联邦物流名称和erp大小写不匹配，做下转换
        if (LogisticsCompanyConstant.FEDEX.equalsIgnoreCase(stockoutOrderInfo.getLogisticsCompany())) {
            stockoutOrderEntity.setLogisticsCompany(LogisticsCompanyConstant.FEDEX);
            stockoutOrderInfo.setLogisticsCompany(LogisticsCompanyConstant.FEDEX);
        }
        String value = parameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKOUT_B2B_SELF_LOGISTICS.getKey());
        if (StringUtils.hasText(value) && StrUtil.equalsAnyIgnoreCase(stockoutOrderEntity.getLogisticsCompany(), value.split(",")) && StrUtil.isBlank(stockoutOrderEntity.getLogisticsNo()) && !StrUtil.equalsIgnoreCase(stockoutOrderEntity.getLocation(), LocationEnum.WEIYUE.name())) {
            stockoutOrderEntity.setLogisticsNo(stockoutOrderEntity.getStockoutOrderNo());
        }
    }

    /**
     * 出库单 - 仓库 + 区域
     */
    public void updateSpaceAndAreaInfo(StockoutOrderEntity stockoutOrderEntity, StockoutOrderAddRequest addRequest) {
        BdErpSpaceMappingEntity mappingEntity = bdErpSpaceMappingService.getEntityByErpSpaceName(addRequest.getOutOrderInfo().getSpaceName());
        stockoutOrderEntity.setSpaceId(mappingEntity.getSpaceId());
        stockoutOrderEntity.setAreaName(mappingEntity.getAreaName());
    }

    /**
     * 1、先根据出库类型  如果是，如果是
     * 头程出库  =>   FBA
     * 仓间调拨  =>  惠州/左海仓 是B2B区域，其余FBA区域
     * 轻定制    =>  B2B区域
     * 1.1 新增 按配置来读取对应的工作区域
     * 2、如果非上面出库类型，则进行物流公司判断，
     * 根据物流公司的区域 赋值出库单区域
     * 2.1 如果是内贸的国际快递，工作区域还是内贸
     * 3、如果公司是自提/没有物流公司，则根据出库单的部门判断
     *
     * <AUTHOR>
     * 2023-08-24
     */
    public String getWorkSpaceByLogisticsCompany(StockoutOrderInfo stockoutOrderInfo) {
        if (stockoutOrderInfo.getTypeEnum().name().equals(StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name())
                || stockoutOrderInfo.getTypeEnum().name().equals(StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name())) {
            if (StockoutOrderBusinessTypeEnum.DOMESTIC.getName().equals(stockoutOrderInfo.getBusinessType()))
                return StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name();
            if (StockoutOrderBusinessTypeEnum.B2B.getName().equals(stockoutOrderInfo.getBusinessType()))
                return StockoutOrderWorkSpaceEnum.B2B_AREA.name();
            return StockoutOrderWorkSpaceEnum.FBA_AREA.name();
        }
        if (stockoutOrderInfo.getTypeEnum().name().equals(StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name()))
            return StockoutOrderWorkSpaceEnum.OTHER_AREA.name();
        if (stockoutOrderInfo.getTypeEnum().name().equals(StockoutOrderTypeEnum.LIGHT_CUSTOMIZATION_DELIVERY.name()))
            return StockoutOrderWorkSpaceEnum.B2B_AREA.name();
        BdSystemParameterEntity bdSystemParameterEntity = parameterService.getByKey(BdSystemParameterEnum.WMS_STORE_WORKSPACE.getKey());
        if (bdSystemParameterEntity != null && StrUtil.isNotBlank(bdSystemParameterEntity.getConfigValue())) {
            Map<String, String> map = JsonMapper.fromJson(bdSystemParameterEntity.getConfigValue(), Map.class);
            String configWorkspace = map.get(String.valueOf(stockoutOrderInfo.getStoreId()));
            if (StrUtil.isNotBlank(configWorkspace)) {
                return configWorkspace;
            }
        }
        List<TmsLogisticsCompany> allCompany = tmsApiService.getAllLogisticsCompanyWithoutStatus();
        if (StringUtils.hasText(stockoutOrderInfo.getLogisticsCompany()) && !ErpConstant.KHZT.equals(stockoutOrderInfo.getLogisticsCompany()) && !allCompany.isEmpty()) {
            TmsLogisticsCompany company = allCompany.stream().filter(o -> o.getLogisticsCompany().equals(stockoutOrderInfo.getLogisticsCompany())).findFirst().orElse(null);
            if (company == null || !StringUtils.hasText(company.getWorkspace()))
                return getWorkSpaceByBusinessType(stockoutOrderInfo);
            if (StockoutOrderBusinessTypeEnum.DOMESTIC.getName().equals(stockoutOrderInfo.getBusinessType()) && StrUtil.equalsIgnoreCase(company.getChannelType(), LogisticsTypeEnum.INTERNATIONAL_EXPRESS.name())) {
                // 如果是内贸的国际快递，工作区域还是内贸
                return StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name();
            }
            if (StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name().equals(company.getWorkspace()) && StockoutOrderBusinessTypeEnum.B2B.getName().equals(stockoutOrderInfo.getBusinessType())) {
                return StockoutOrderWorkSpaceEnum.B2B_AREA.name();
            } else {
                return company.getWorkspace();
            }
        }
        return getWorkSpaceByBusinessType(stockoutOrderInfo);
    }

    public String getWorkSpaceByBusinessType(StockoutOrderInfo stockoutOrderInfo) {
        if (StockoutOrderBusinessTypeEnum.B2B.getName().equals(stockoutOrderInfo.getBusinessType()))
            return StockoutOrderWorkSpaceEnum.B2B_AREA.name();
        else if (StockoutOrderBusinessTypeEnum.B2C.getName().equals(stockoutOrderInfo.getBusinessType()))
            return StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name();
        else if (StockoutOrderBusinessTypeEnum.DOMESTIC.getName().equals(stockoutOrderInfo.getBusinessType()))
            return StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name();
        else if (StockoutOrderBusinessTypeEnum.INDEPENDENT_STATION.getName().equals(stockoutOrderInfo.getBusinessType())
                && ErpConstant.KHZT.equals(stockoutOrderInfo.getLogisticsCompany())) {
            return StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name();
        } else
            return StockoutOrderWorkSpaceEnum.OTHER_AREA.name();
    }


    /**
     * 新增出库单PACK 商品关系
     *
     * @param stockoutOrderId 出库单id
     * @param packInfoList    request list
     * @param createBy        创建人
     */
    public void addPackInfo(Integer stockoutOrderId, List<StockoutPackAddRequest> packInfoList, String createBy, String stockoutOrderType) {
        if (CollectionUtils.isEmpty(packInfoList)) {
            return;
        }
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = new LinkedList<>();
        if (StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name().equals(stockoutOrderType)) {
            stockoutOrderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda().eq(StockoutOrderItemEntity::getStockoutOrderId, stockoutOrderId));
        }
        List<StockoutOrderPackMappingInfoEntity> mappingInfoEntities = new LinkedList<>();
        for (StockoutPackAddRequest packAddRequest : packInfoList) {
            StockoutOrderPackMappingInfoEntity mappingInfoEntity = new StockoutOrderPackMappingInfoEntity();
            BeanUtils.copyProperties(packAddRequest, mappingInfoEntity);
            mappingInfoEntity.setStockoutOrderId(stockoutOrderId);
            mappingInfoEntity.setCreateBy(createBy);
            StockoutOrderItemEntity curItemEntity = stockoutOrderItemEntityList.stream().filter(o -> o.getOrderItemId().equals(mappingInfoEntity.getOrderItemNo())).findFirst().orElse(null);
            mappingInfoEntity.setPackSellerSku(curItemEntity != null && StringUtils.hasText(curItemEntity.getSellerSku()) ? curItemEntity.getSellerSku() : "");
            mappingInfoEntity.setPackSellerBarcode(curItemEntity != null && StringUtils.hasText(curItemEntity.getSellerBarcode()) ? curItemEntity.getSellerBarcode() : "");
            mappingInfoEntities.add(mappingInfoEntity);
        }
        packMappingInfoService.saveBatch(mappingInfoEntities);
    }

    /**
     * Temu扩展信息
     */
    public void addTemuInfo(StockoutOrderEntity stockoutOrderEntity, Long subWarehouseId) {
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (stockoutOrderItemEntities.isEmpty())
            throw new BusinessServiceException(String.format("【%s】出库单明细为空", stockoutOrderEntity.getStockoutOrderNo()));

        StockoutOrderTemuExtendEntity temuExtendEntity = new StockoutOrderTemuExtendEntity();
        temuExtendEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        temuExtendEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        temuExtendEntity.setUrgencyType(stockoutOrderEntity.getUrgent());
        temuExtendEntity.setSubWarehouseId(subWarehouseId);
        temuExtendEntity.setSubWarehouseShortname("");
        temuExtendEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
        temuExtendEntity.setUpdateBy(stockoutOrderEntity.getUpdateBy());
        temuExtendEntity.setOrderNo(stockoutOrderItemEntities.get(0).getOrderNo());
        temuExtendService.save(temuExtendEntity);

        List<StockoutOrderTemuExtendItemEntity> extendItemEntities = stockoutOrderItemEntities.stream().map(item -> {
            StockoutOrderTemuExtendItemEntity extendItemEntity = new StockoutOrderTemuExtendItemEntity();
            extendItemEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            extendItemEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            extendItemEntity.setStockoutOrderItemId(item.getStockoutOrderItemId());
            extendItemEntity.setExtendId(temuExtendEntity.getId());
            extendItemEntity.setSku(item.getSku());
            extendItemEntity.setProductSkuId(Long.valueOf(item.getSellerBarcode()));
            extendItemEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
            extendItemEntity.setUpdateBy(stockoutOrderEntity.getUpdateBy());
            return extendItemEntity;
        }).collect(Collectors.toList());
        temuExtendItemService.saveBatch(extendItemEntities);
//        最晚发货时间 = （ 急单 ）下单时间 + 7天
//                    （非急单） 下单时间 + 14天
        if (ObjectUtil.isNull(stockoutOrderEntity.getReadyDate()))
            return;
        if (stockoutOrderEntity.getUrgent()) {
            stockoutOrderEntity.setLatestDeliveryDate(DateUtil.offsetDay(stockoutOrderEntity.getReadyDate(), 7));
        } else {
            stockoutOrderEntity.setLatestDeliveryDate(DateUtil.offsetDay(stockoutOrderEntity.getReadyDate(), 14));
        }
        stockoutOrderService.updateById(stockoutOrderEntity);
    }

    /**
     * Shein扩展信息
     */
    public void addSheinInfo(StockoutOrderEntity stockoutOrderEntity, Integer orderType) {
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (stockoutOrderItemEntities.isEmpty())
            throw new BusinessServiceException(String.format("【%s】出库单明细为空", stockoutOrderEntity.getStockoutOrderNo()));


        StockoutOrderSheinExtendEntity sheinExtendEntity = new StockoutOrderSheinExtendEntity();

        sheinExtendEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        sheinExtendEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        sheinExtendEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
        sheinExtendEntity.setUpdateBy(stockoutOrderEntity.getUpdateBy());
        sheinExtendEntity.setOrderNo(stockoutOrderItemEntities.get(0).getOrderNo());
        sheinExtendEntity.setSupplierWarehouseId(0L);
        sheinExtendEntity.setOrderType(orderType);
        sheinExtendService.save(sheinExtendEntity);

        List<StockoutOrderSheinExtendItemEntity> extendItemEntities = stockoutOrderItemEntities.stream().map(item -> {
            StockoutOrderSheinExtendItemEntity extendItemEntity = new StockoutOrderSheinExtendItemEntity();
            extendItemEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            extendItemEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            extendItemEntity.setStockoutOrderItemId(item.getStockoutOrderItemId());
            extendItemEntity.setOrderNo(item.getOrderNo());
            extendItemEntity.setExtendId(sheinExtendEntity.getId());
            extendItemEntity.setSku(item.getSku());
            extendItemEntity.setSellerSku(item.getSellerSku());
            extendItemEntity.setSellerBarcode(item.getSellerBarcode());
            extendItemEntity.setQty(item.getQty());
            extendItemEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
            extendItemEntity.setUpdateBy(stockoutOrderEntity.getUpdateBy());
            return extendItemEntity;
        }).collect(Collectors.toList());
        sheinExtendItemService.saveBatch(extendItemEntities);
    }

    /**
     * tiktok扩展信息
     */
    public void addTikTokInfo(StockoutOrderEntity stockoutOrderEntity, Integer orderType) {
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (stockoutOrderItemEntities.isEmpty())
            throw new BusinessServiceException(String.format("【%s】出库单明细为空", stockoutOrderEntity.getStockoutOrderNo()));


        StockoutOrderTikTokExtendEntity tikTokExtendEntity = new StockoutOrderTikTokExtendEntity();

        tikTokExtendEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        tikTokExtendEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        tikTokExtendEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
        tikTokExtendEntity.setUpdateBy(stockoutOrderEntity.getUpdateBy());
        tikTokExtendEntity.setOrderNo(stockoutOrderItemEntities.get(0).getOrderNo());
        tikTokExtendEntity.setSupplierWarehouseId(0L);
        tikTokExtendEntity.setOrderType(orderType);
        tikTokExtendService.save(tikTokExtendEntity);

        List<StockoutOrderTikTokExtendItemEntity> extendItemEntities = stockoutOrderItemEntities.stream().map(item -> {
            StockoutOrderTikTokExtendItemEntity extendItemEntity = new StockoutOrderTikTokExtendItemEntity();
            extendItemEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            extendItemEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            extendItemEntity.setStockoutOrderItemId(item.getStockoutOrderItemId());
            extendItemEntity.setOrderNo(item.getOrderNo());
            extendItemEntity.setExtendId(tikTokExtendEntity.getId());
            extendItemEntity.setSku(item.getSku());
            extendItemEntity.setSellerSku(item.getSellerSku());
            extendItemEntity.setSellerBarcode(item.getSellerBarcode());
            extendItemEntity.setQty(item.getQty());
            extendItemEntity.setCreateBy(stockoutOrderEntity.getCreateBy());
            extendItemEntity.setUpdateBy(stockoutOrderEntity.getUpdateBy());
            return extendItemEntity;
        }).collect(Collectors.toList());
        tikTokExtendItemService.saveBatch(extendItemEntities);
    }


    /**
     * wms生成出库单时，满足以下条件做硬代码控制 拣货模式
     * 1.条件：满足配置表的店铺 操作 ==> 拣货模式改为 按单拣货(优先级最高，直接不做下面的判断)
     * 1.1  特殊店铺店铺 卓天商务：10件以下 改换成 二次分拣模式 按批次生成；10件以上 照常 按单拣货模式 打A4单出来备
     * 1.2  [Shein平台]拣货模式更改为[按单拣货]
     * 2.条件：工作区域=内贸，拣货模式=以货找单 物流公司不是圆通 ===> 操作：拣货模式改为 二次分拣
     * 3.条件：工作区域 = B2B， 某个库区拣货数 > 10  操作 ===> 拣货模式改为 按单拣货
     * 4 条件：拣货模式=以货找单，相同物流公司和相同SKU 状态为准备中 或 待生成波次，出库单数>=29(加上待生成的出库单>=30) ====>操作：拣货模式由以货找单变更为以单找货
     * 5 条件：工作区域 = 内贸， 平台=PinDuoDuo  操作 ==>  出库数 >= 35 拣货模式改为 按单拣货 否则  二次分拣
     */
    private String changeStockoutOrderPickingType(StockoutOrderEntity stockoutOrderEntity) {
        List<String> changelogs = new ArrayList<>();
        // 1.条件：满足配置表的店铺 操作 ==> 拣货模式改为 按单拣货(优先级最高，直接不做下面的判断)
        BdSystemParameterEntity bdSystemParameterEntity = parameterService.getByKey(BdSystemParameterEnum.WMS_WHOLE_PICK_STORE.getKey());
        if (bdSystemParameterEntity != null && StringUtils.hasText(bdSystemParameterEntity.getConfigValue())) {
            String[] split = bdSystemParameterEntity.getConfigValue().split(";");
            if (Arrays.stream(split).anyMatch(item -> Objects.equals(item, stockoutOrderEntity.getStoreName()))) {
                stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
                return "[满足配置表的店铺]拣货模式更改为[按单拣货]";
            }
        }
        // 1.1  特殊店铺店铺 卓天商务：10件以下 改换成 二次分拣模式 按批次生成；10件以上 照常 按单拣货模式 打A4单出来备
        matchSpecilaStoreConfig(stockoutOrderEntity, changelogs);
        // 1.2  [Shein平台]拣货模式更改为[按单拣货]
        if (StockoutOrderPlatformEnum.SHEIN.getName().equals(stockoutOrderEntity.getPlatformName())) {
            stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
            return "[Shein平台]拣货模式更改为[按单拣货]";
        }
        // 2 条件：工作区域=内贸，拣货模式=以货找单 物流公司不是菜鸟系的 ==> 操作：拣货模式改为 二次分拣
        if (StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name().equals(stockoutOrderEntity.getPickingType())
                && StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name().equals(stockoutOrderEntity.getWorkspace())
                && !StockoutOrderPlatformEnum.PDD.getName().equals(stockoutOrderEntity.getPlatformName())
                && !Objects.equals(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.YUAN_TONG)
                && !Objects.equals(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.ZTO)
                && !Objects.equals(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.YUN_DA)
                && !Objects.equals(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.JTSD)) {
            stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.SECOND_SORT.name());
            changelogs.add("[内贸以货找单(原)，且物流公司不是菜鸟]拣货模式更改为[二次分拣]");
        }
        // 3 条件：工作区域 = B2B， 某个库区拣货数 > 10  操作 ==> 拣货模式改为 按单拣货 （非加工单）
        if (StockoutOrderWorkSpaceEnum.B2B_AREA.name().equals(stockoutOrderEntity.getWorkspace()) && !stockoutOrderEntity.getNeedProcess()) {
            List<Integer> areaQtyList = stockoutOrderItemMapper.getSpaceAreaQtyMax(stockoutOrderEntity.getStockoutOrderId());
            if (areaQtyList.size() == 1 && areaQtyList.get(0) != null && areaQtyList.get(0) > 10) {
                stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
                changelogs.add("[工作区域 = B2B，只有1个拣货库区，且拣货数 > 10]拣货模式更改为[按单拣货]");
            }
        }
        // 4 条件：拣货模式=以货找单 & 工作区域=B2C小包 ，相同物流公司和相同SKU 状态为准备中 或 待生成波次，出库单数>=29(加上待生成的出库单>=30) ====>操作：拣货模式由以货找单变更为以单找货
        resetPickTypeBySkuAndCompany(stockoutOrderEntity, changelogs);
        // 5 条件：工作区域 = 内贸， 出库数 >= 35，平台=PinDuoDuo  操作 ==> 拣货模式改为 按单拣货 否则  二次分拣
        if (StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name().equals(stockoutOrderEntity.getWorkspace())
                && StockoutOrderPlatformEnum.PDD.getName().equals(stockoutOrderEntity.getPlatformName())
                && 2 == stockoutOrderEntity.getOriginType()) {
            return changeTemuPickingType(stockoutOrderEntity);
        }
        return String.join(";", changelogs);
    }

    /**
     * 条件：
     * 急单 =》 按单拣货
     * 工作区域 = 内贸， 出库数 >= 35，平台=PinDuoDuo  操作 ==> 拣货模式改为 按单拣货
     * 否则  二次分拣
     *
     * @param stockoutOrderEntity
     * @return
     */
    private String changeTemuPickingType(StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutOrderItemEntity> stockoutOrderItemList = stockoutOrderItemService.getListByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (stockoutOrderItemList.isEmpty())
            throw new BusinessServiceException(String.format("%s 的明细为空", stockoutOrderEntity.getStockoutOrderNo()));
        int totalQty = stockoutOrderItemList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum();

        Integer configValue = parameterService.getIntValue(BdSystemParameterEnum.WMS_TEMU_PREV_CREATE_SHIPORDER_QTY_LIMIT);
        if (0 == configValue)
            throw new BusinessServiceException(String.format("系统参数不能为 0 值 %s", BdSystemParameterEnum.WMS_TEMU_PREV_CREATE_SHIPORDER_QTY_LIMIT.getName()));

        if (stockoutOrderEntity.getUrgent()) { // 急单
            stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
            return "[工作区域 = 内贸，拼多多平台，急单]拣货模式更改为[按单拣货]";
        } else if (totalQty >= configValue) { // 超过35件 且 首单
            stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
            return "[工作区域 = 内贸，拼多多平台，出库数大于等于30]拣货模式更改为[按单拣货]";
        } else if (1 == stockoutOrderItemList.get(0).getIsFirstOrderByStore()) {
            stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
            return "[工作区域 = 内贸，拼多多平台，首单]拣货模式更改为[按单拣货]";
        } else {
            stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.SECOND_SORT.name());
            return "[PinDuoDuo平台且]拣货模式更改为[二次分拣]";
        }
    }

    private void resetPickTypeBySkuAndCompany(StockoutOrderEntity stockoutOrderEntity, List<String> changelogs) {
        if (!StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equalsIgnoreCase(stockoutOrderEntity.getWorkspace()))
            return;

        if (!StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name().equals(stockoutOrderEntity.getPickingType())
                && !StockoutPickingTypeEnum.FIND_GOODS_BY_DOC.name().equals(stockoutOrderEntity.getPickingType()))
            return;
        //判断是否品牌单
        String brandName = null;
        List<BdTagMappingEntity> tagMappingEntities = bdTagMappingService.getBrandTageByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (!CollectionUtils.isEmpty(tagMappingEntities))
            brandName = tagMappingEntities.get(0).getTagName();

        // 先更新，否则将不会更新数据存在bug
        stockoutOrderService.update(Wrappers.<StockoutOrderEntity>lambdaUpdate().eq(StockoutOrderEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId())
                .set(StockoutOrderEntity::getPickingType, stockoutOrderEntity.getPickingType()));
        List<String> statusList = new ArrayList<>();
        statusList.add(StockoutOrderStatusEnum.READY.name());
        statusList.add(StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.name());
        statusList.add(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name());
        statusList.add(StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
        List<String> pickingTypeList = new ArrayList<>();
        pickingTypeList.add(StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name());
        pickingTypeList.add(StockoutPickingTypeEnum.FIND_GOODS_BY_DOC.name());
        List<String> stockoutOrderStrList = stockoutOrderMapper.selectByPickingTypeListAndStatusList(pickingTypeList, statusList,
                brandCommonService.getBrandTagNameList(), brandName);
        if (CollectionUtils.isEmpty(stockoutOrderStrList)) {
            return;
        }
        for (String str : stockoutOrderStrList) {
            List<String> stockoutOrderIdStrList = Arrays.asList(str.split(","));
            List<Integer> idList = stockoutOrderIdStrList.stream().map(Integer::valueOf).collect(Collectors.toList());
            if (!idList.contains(stockoutOrderEntity.getStockoutOrderId())) {
                continue;
            }
            if (idList.size() >= 20) {
                List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.listByIds(idList);
                List<StockoutOrderEntity> filterStockoutOrderList = stockoutOrderList.stream().filter(order ->
                        StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name().equals(order.getPickingType())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filterStockoutOrderList)) {
                    List<StockoutOrderEntity> orderUpdateEntityList = filterStockoutOrderList.stream().map(order -> {
                        LOGGER.info("更改拣货模式，从以货找单更改为以单找货：{}", order.getStockoutOrderNo());
                        StockoutOrderEntity iOrder = new StockoutOrderEntity();
                        iOrder.setStockoutOrderId(order.getStockoutOrderId());
                        iOrder.setPickingType(StockoutPickingTypeEnum.FIND_GOODS_BY_DOC.name());
                        iOrder.setUpdateBy(loginInfoService.getName());
                        return iOrder;
                    }).collect(Collectors.toList());
                    stockoutOrderService.updateBatchById(orderUpdateEntityList);
                }
                stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.FIND_GOODS_BY_DOC.name());
                changelogs.add("[拣货模式=以货找单，相同物流相同SKU的出库单数>=20]拣货模式更改为[以单找货]");
                return;
            }
        }
    }

    private void matchSpecilaStoreConfig(StockoutOrderEntity stockoutOrderEntity, List<String> changelogs) {
        if (StringConstant.SPECIAL_STORE.equals(stockoutOrderEntity.getStoreName())) {
            Integer totalQty = stockoutOrderItemMapper.countQty(stockoutOrderEntity.getStockoutOrderId());
            if (totalQty >= 10 && !Objects.equals(stockoutOrderEntity.getPickingType(), StockoutPickingTypeEnum.WHOLE_PICK.name())) {
                stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
                changelogs.add("特殊店铺配置，拣货数量大于10，改为按单拣货");
            } else if (totalQty < 10 && !Objects.equals(stockoutOrderEntity.getPickingType(), StockoutPickingTypeEnum.SECOND_SORT.name())) {
                stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.SECOND_SORT.name());
                changelogs.add("特殊店铺配置，拣货数量小于10，改为二次分拣");
            }
        }
    }
}
