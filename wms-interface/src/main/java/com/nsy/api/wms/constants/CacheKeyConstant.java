package com.nsy.api.wms.constants;

/**
 * 缓存Key值
 * HXD
 * 2021/7/20
 **/

public class CacheKeyConstant {

    /**
     * 缓存前缀
     * Redis Cache PreFix
     */
    public static final String CACHE_PRE_FIX = "CACHE:S:";

    /**
     * 报关商品分类
     */
    public static final String WMS_CATEGORY_SELECT = CACHE_PRE_FIX + "WMS_CATEGORY_SELECT";

    /**
     * 报关商品分类列表
     */
    public static final String WMS_CATEGORY_LIST = CACHE_PRE_FIX + "WMS_CATEGORY_LIST";

    /**
     * 店铺下拉框
     */
    public static final String WMS_USER_STORE_LIST = CACHE_PRE_FIX + "WMS_USER_STORE_LIST";

    /**
     * 区域
     */
    public static final String AREA_NAME_SELECT = CACHE_PRE_FIX + "AREA_NAME_SELECT";

    /**
     * 调拨类型
     */
    public static final String TRANSFER_CONFIG_SELECT = CACHE_PRE_FIX + "TRANSFER_CONFIG_SELECT";

    /**
     * 区域列表
     */
    public static final String ALL_AREA_NAME_SELECT = CACHE_PRE_FIX + "ALL_AREA_NAME_SELECT";

    /**
     * 字典数据列表
     */
    public static final String DICTIONARY_ITEM_LIST = CACHE_PRE_FIX + "DICTIONARY_ITEM_LIST_2";

    /**
     * 波次生成规则
     */
    public static final String AUTO_BATCH_GENERATE_RULE = CACHE_PRE_FIX + "AUTO_BATCH_GENERATE_RULE";

    /**
     * 库位名称
     */
    public static final String POSITION_NAME_SELECT = CACHE_PRE_FIX + "POSITION_NAME_SELECT";
    /**
     * 库位编码
     */
    public static final String POSITION_CODE_SELECT = CACHE_PRE_FIX + "POSITION_CODE_SELECT";

    /**
     * 上架规则
     */
    public static final String SHELVE_RULE_NAME_SELECT = CACHE_PRE_FIX + "SHELVE_RULE_NAME_SELECT";

    /**
     * 质检规则
     */
    public static final String QA_RULE_SUPPLIER_ID = CACHE_PRE_FIX + "QA_RULE_SUPPLIER_ID";

    /**
     * aql规则
     */
    public static final String AQL_RULE_ID = CACHE_PRE_FIX + "QA_RULE_ID";

    /**
     * 上架规则by spaceId
     */
    public static final String SHELVE_RULE_NAME_SELECT_BY_SPACE_ID = CACHE_PRE_FIX + "SHELVE_RULE_NAME_SELECT_BY_SPACE_ID";

    /**
     * 物料列表
     */
    public static final String MATERIAL_NAME_LIST = CACHE_PRE_FIX + "MATERIAL_NAME_LIST";

    /**
     * 库区列表
     */
    public static final String SPACE_AREA_NAME_SELECT = CACHE_PRE_FIX + "SPACE_AREA_NAME_SELECT";

    /**
     * 库区
     */
    public static final String SPACE_AREA_NAME_SELECT_BY_SPACE_ID = CACHE_PRE_FIX + "SPACE_AREA_NAME_SELECT_BY_SPACE_ID";

    /**
     * 仓库列表
     */
    public static final String SPACE_NAME_SELECT = CACHE_PRE_FIX + "SPACE_NAME_SELECT";

    /**
     * 仓库编码列表
     */
    public static final String SPACE_CODE_SELECT = CACHE_PRE_FIX + "SPACE_CODE_SELECT";

    /**
     * 所有仓库列表
     */
    public static final String ALL_SPACE_NAME_SELECT = CACHE_PRE_FIX + "ALL_SPACE_NAME_SELECT";

    /**
     * 入库规则列表
     */
    public static final String STOCKIN_RULE_SELECT = CACHE_PRE_FIX + "STOCKIN_RULE_SELECT";

    /**
     * 物料列表
     */
    public static final String SUPPLIER_POSITION_MAPPING_LIST = CACHE_PRE_FIX + "SUPPLIER_POSITION_MAPPING_LIST";

    /**
     * 系统配置字典
     */
    public static final String SYSTEM_PARAMETER_MAP = CACHE_PRE_FIX + "SYSTEM_PARAMETER_MAP";

    /**
     * 系统配置字典
     */
    public static final String SYSTEM_PARAMETER = CACHE_PRE_FIX + "SYSTEM_PARAMETER";

    /**
     * 月台
     */
    public static final String PLATFORM_NAME_SELECT = CACHE_PRE_FIX + "PLATFORM_NAME_SELECT";

    /**
     * 启用物流公司集合
     */
    public static final String LOGISTICS_COMPANY_LIST = CACHE_PRE_FIX + "LOGISTICS_COMPANY_LIST";

    /**
     * 物流公司集合 包含禁用的
     */
    public static final String LOGISTICS_COMPANY_ALL = CACHE_PRE_FIX + "LOGISTICS_COMPANY_ALL";

    public static final String LOGISTICS_COMPANY_MAP = CACHE_PRE_FIX + "LOGISTICS_COMPANY_MAP";

    /**
     * 物流特定国家二字码下拉数据源
     */
    public static final String LOGISTICS_COUNTRY_SELECT = CACHE_PRE_FIX + "LOGISTICS_COUNTRY_SELECT";

    /**
     * 物流特定国家州二字码下拉数据源
     */
    public static final String LOGISTICS_COMPANY_PROVINCE_LIST = CACHE_PRE_FIX + "LOGISTICS_COMPANY_PROVINCE_LIST";

    /**
     * 物流账号下拉框
     */
    public static final String LOGISTICS_ACCOUNT_SELECT = CACHE_PRE_FIX + "LOGISTICS_ACCOUNT_SELECT";

    /**
     * 物流账号信息
     */
    public static final String LOGISTICS_ACCOUNT_INFO_LIST = CACHE_PRE_FIX + "LOGISTICS_ACCOUNT_INFO_LIST";

    /**
     * 公司抬头下拉框
     */
    public static final String COMPANY_NAME_SELECT = CACHE_PRE_FIX + "COMPANY_NAME_SELECT";

    public static final String LOCATION = CACHE_PRE_FIX + "LOCATION";

    public static final String SYSTEM_COMPANY = CACHE_PRE_FIX + "SYSTEM_COMPANY";

    /**
     * 供应商列表
     */
    public static final String SUPPLIER_INFO_SELECT = CACHE_PRE_FIX + "SUPPLIER_INFO_SELECT";

    /**
     * 供应商采购员列表
     */
    public static final String SUPPLIER_EMP_INFO_SELECT = CACHE_PRE_FIX + "SUPPLIER_EMP_INFO_SELECT";


    /**
     * 所有店铺
     */
    public static final String WMS_ALL_STORE = CACHE_PRE_FIX + "WMS_ALL_STORE";

    /**
     * 店铺选择
     */
    public static final String WMS_STORE_SELECT = CACHE_PRE_FIX + "WMS_STORE_SELECT";


    /**
     * 所有平台
     */
    public static final String WMS_ALL_PLATFORM = CACHE_PRE_FIX + "WMS_ALL_PLATFORM";

    /**
     * 拣货任务类型
     */
    public static final String PICKING_TASK_TYPE = CACHE_PRE_FIX + "PICKING_TASK_TYPE";

    /**
     * 内部箱号生成
     */
    public static final String STOCK_INTERNAL_BOX_CODE_CAHCE = "API-WMS:STOCK_INTERNAL_BOX_CODE_CAHCE_";

    /**
     * 区域统一库位编码
     */
    public static final String WMS_AREA_COMMON_POSITION_CODE = CACHE_PRE_FIX + "WMS_AREA_COMMON_POSITION_CODE";


    /**
     * 仓库默认区域统一库位编码
     */
    public static final String WMS_DEFAULT_AREA_COMMON_POSITION_CODE = CACHE_PRE_FIX + "WMS_DEFAULT_AREA_COMMON_POSITION_CODE";

    /**
     * 仓库默认区域统一库位编码
     */
    public static final String WMS_SPACE_COMMON_POSITION_CODE = CACHE_PRE_FIX + "WMS_SPACE_COMMON_POSITION_CODE";

    /**
     * 区域统一库位区域id
     */
    public static final String WMS_AREA_COMMON_POSITION_AREA_ID = CACHE_PRE_FIX + "WMS_AREA_COMMON_POSITION_AREA_ID";


    public static final String WMS_ERP_SPACE_MAPPING = CACHE_PRE_FIX + "WMS_ERP_SPACE_MAPPING";

    /**
     * ERP仓库与WMS区域映射
     */
    public static final String WMS_ERP_SPACE_MAPPING_AREA = CACHE_PRE_FIX + "WMS_ERP_SPACE_MAPPING_AREA";

    /**
     * ERP仓库与WMS区域映射
     */
    public static final String WMS_ERP_SPACE_MAPPING_ERP_SPACE = CACHE_PRE_FIX + "WMS_ERP_SPACE_MAPPING_ERP_SPACE";

    /**
     * ERP仓库与WMS区域映射
     */
    public static final String WMS_ERP_SPACE_MAPPING_AREA_DEFAULT = CACHE_PRE_FIX + "WMS_ERP_SPACE_MAPPING_AREA_DEFAULT";

    /**
     * 统一库位区域配置
     */
    public static final String WMS_STOCK_CENTER_AREA_ID_LIST = CACHE_PRE_FIX + "WMS_STOCK_CENTER_AREA_ID_LIST";

    /**
     * 强推款标签
     */
    public static final String WMS_STRONGLY_RECOMMEND_STYLE_LABELS = CACHE_PRE_FIX + "WMS_STRONGLY_RECOMMEND_STYLE_LABELS";

    /**
     * 品牌仓配置
     */
    public static final String WMS_ERP_SPACE_MAPPING_AREA_BRAND_LOCATION = CACHE_PRE_FIX + "WMS_ERP_SPACE_MAPPING_AREA_BRAND_LOCATION";

    /**
     * 品牌仓配置
     */
    public static final String WMS_ERP_SPACE_MAPPING_AREA_BRAND_LIST = CACHE_PRE_FIX + "WMS_ERP_SPACE_MAPPING_AREA_BRAND_LIST";


    /**
     * 箱规配置
     */
    public static final String WMS_BOX_SPECIFICATIONS = CACHE_PRE_FIX + "WMS_BOX_SPECIFICATIONS_LIST";


    /**
     * 标签下拉
     */
    public static final String WMS_PRODUCT_STYLE_NAME_LIST = CACHE_PRE_FIX + "WMS_PRODUCT_STYLE_NAME_LIST";

    /**
     * 亚马逊物流配送费重量范围
     */
    public static final String WMS_VOLUME_WEIGHT_RANGE = CACHE_PRE_FIX + "WMS_VOLUME_WEIGHT_RANGE";

    /**
     * 供应商部门信息
     */
    public static final String WMS_SUPPLIER_DEPARTMENT_LIST = CACHE_PRE_FIX + "WMS_SUPPLIER_DEPARTMENT_LIST";

}
