package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockout.StockoutShipmentChangePackageTaskPageRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentChangePackageTaskPageResponse;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentChangePackageTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 打包任务Mapper接口
 */
@Mapper
public interface StockoutShipmentChangePackageTaskMapper extends BaseMapper<StockoutShipmentChangePackageTaskEntity> {

    /**
     * 分页查询打包任务列表
     *
     * @param page 分页参数
     * @param request 查询参数
     * @return 分页结果
     */
    IPage<StockoutShipmentChangePackageTaskPageResponse> searchPage(IPage page, @Param("query") StockoutShipmentChangePackageTaskPageRequest request);
} 