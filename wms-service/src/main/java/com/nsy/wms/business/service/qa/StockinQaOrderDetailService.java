package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.qa.StockinQaBoxItemInfo;
import com.nsy.api.wms.request.qa.StockinQaOperateFirstAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateSecondAuditRequest;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderDetailMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单子表业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderDetailService extends ServiceImpl<StockinQaOrderDetailMapper, StockinQaOrderDetailEntity> {

    @Resource
    StockinOrderItemService stockinOrderItemService;

    public void buildQaOrderDetail(StockinQaTaskEntity qaTaskEntity, StockinQaOrderEntity qaOrderEntity, List<StockinQaBoxItemInfo> stockinQaBoxItemInfos, Date deliveryDate) {
        StockinQaOrderDetailEntity detailEntity = new StockinQaOrderDetailEntity();
        detailEntity.setBoxQty(stockinQaBoxItemInfos.stream().mapToInt(StockinQaBoxItemInfo::getQty).sum());
        detailEntity.setStockinDate(qaTaskEntity.getStockinDate());
        detailEntity.setDeliveryDate(deliveryDate);
        detailEntity.setLocation(qaTaskEntity.getLocation());
        detailEntity.setStockinQaOrderId(qaOrderEntity.getStockinQaOrderId());
        detailEntity.setWorkmanshipVersion(stockinQaBoxItemInfos.get(0).getWorkmanshipVersion());
        List<String> supplierDeliveryNoList = stockinQaBoxItemInfos.stream().map(StockinQaBoxItemInfo::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
        detailEntity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, qaTaskEntity.getSku()));
        this.save(detailEntity);
    }

    public StockinQaOrderDetailEntity findTopByStockinQaOrderId(Integer stockinQaOrderId) {
        return this.getOne(new LambdaUpdateWrapper<StockinQaOrderDetailEntity>()
            .eq(StockinQaOrderDetailEntity::getStockinQaOrderId, stockinQaOrderId)
            .last("limit 1"));
    }

    @Transactional
    public void completeOrderDetailBySop(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateRequest request, int totalDirectReturnCount) {
        StockinQaOrderDetailEntity detailEntity = this.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        detailEntity.setUnqualifiedCount(request.getUnqualifiedCount());
        detailEntity.setAttachmentUrl(request.getAttachmentUrl());
        detailEntity.setReturnCount(request.getUnqualifiedCount());
        detailEntity.setTestTotalCount(request.getTestTotalCount());
        detailEntity.setDirectReturnCount(totalDirectReturnCount);
        detailEntity.setMinorDefectCount(request.getMinorDefectCount());
        detailEntity.setMajorDefectCount(request.getMajorDefectCount());
        detailEntity.setCriticalDefectCount(request.getCriticalDefectCount());
        this.updateById(detailEntity);
    }

    @Transactional
    public StockinQaOrderDetailEntity completeOrderDetailByFirstAudit(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateFirstAuditRequest request) {
        StockinQaOrderDetailEntity detailEntity = this.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        detailEntity.setUnqualifiedCount(request.getUnqualifiedCount());
        detailEntity.setAttachmentUrl(request.getAttachmentUrl());
        detailEntity.setReturnCount(request.getReturnCount() + detailEntity.getDirectReturnCount());
        detailEntity.setConcessionsCount(request.getConcessionsCount());
        this.updateById(detailEntity);
        return detailEntity;
    }

    @Transactional
    public void updateSetDirectReturnCount(Integer stockinQaOrderId, int directReturnCount) {
        this.update(new LambdaUpdateWrapper<StockinQaOrderDetailEntity>()
            .set(StockinQaOrderDetailEntity::getDirectReturnCount, directReturnCount)
            .eq(StockinQaOrderDetailEntity::getStockinQaOrderId, stockinQaOrderId));
    }

    public StockinQaOrderDetailEntity completeOrderDetailBySecondAudit(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateSecondAuditRequest request) {
        StockinQaOrderDetailEntity detailEntity = this.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        detailEntity.setUnqualifiedCount(request.getUnqualifiedCount());
        detailEntity.setAttachmentUrl(request.getAttachmentUrl());
        detailEntity.setReturnCount(request.getReturnCount() + detailEntity.getDirectReturnCount());
        detailEntity.setConcessionsCount(request.getConcessionsCount());
        detailEntity.setDepartResponsibility(request.getDepartResponsibility());
        if (StringUtils.hasText(request.getResponsibilityRemark()))
            detailEntity.setResponsibilityRemark(request.getResponsibilityRemark().length() > 200 ? request.getResponsibilityRemark().substring(0, 199) : request.getResponsibilityRemark());
        detailEntity.setProcessingProgram(request.getProcessingProgram());
        detailEntity.setResponsibility(request.getResponsibility());
        this.updateById(detailEntity);
        return detailEntity;
    }

    /**
     * 获取到货数量
     *
     * @param stockinQaOrderId
     * @return
     */
    public Integer getArriveCount(Integer stockinQaOrderId) {
        LambdaQueryWrapper<StockinQaOrderDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockinQaOrderDetailEntity::getStockinQaOrderId, stockinQaOrderId)
            .select(StockinQaOrderDetailEntity::getArrivalCount);
        StockinQaOrderDetailEntity detailEntity = this.getOne(wrapper);
        if (ObjectUtils.isEmpty(detailEntity)) {
            return 0;
        }
        return detailEntity.getArrivalCount();
    }

    public StockinQaOrderDetailEntity completeOrderDetailByInspectCommitResult(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateInspectRequest request) {
        StockinQaOrderDetailEntity detailEntity = this.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        detailEntity.setInspectTotalCount(request.getInspectTotalCount());
        detailEntity.setAttachmentUrl(request.getAttachmentUrl());
        detailEntity.setReturnCount(request.getReturnCount() + detailEntity.getDirectReturnCount());
        this.updateById(detailEntity);
        return detailEntity;
    }
}
