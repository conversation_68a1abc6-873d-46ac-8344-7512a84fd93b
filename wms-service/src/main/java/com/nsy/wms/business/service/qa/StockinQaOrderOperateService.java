package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.domain.stockin.QcInboundsPriceItem;
import com.nsy.api.wms.enumeration.ProductEditEnum;
import com.nsy.api.wms.enumeration.QcInboundsResultStatusEnum;
import com.nsy.api.wms.enumeration.bd.BdQaSopEnum;
import com.nsy.api.wms.enumeration.qa.FlowTaskQaOperationEnum;
import com.nsy.api.wms.enumeration.qa.QaLogTypeEnum;
import com.nsy.api.wms.enumeration.qa.QaProcessEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaInspectStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaTaskStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxMaterialSizeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.qa.StockinCreateQaOrderRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchFirstAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchSecondAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateFirstAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateReturnRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateSecondAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaPunishmentsRequest;
import com.nsy.api.wms.response.qa.BdQaSopRuleDetailResponse;
import com.nsy.api.wms.response.qa.StockinCreateQaOrderResponse;
import com.nsy.api.wms.response.qa.StockinWaitQaTaskPageResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.dto.stockin.StockinQaAqlValidDTO;
import com.nsy.wms.business.domain.dto.stockin.StockinQaOrderAuditDTO;
import com.nsy.wms.business.domain.dto.stockin.StockinQaSyncDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.request.QaPunishmentsAttachRequest;
import com.nsy.wms.business.manage.supplier.request.QaPunishmentsRequest;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfoListResponse;
import com.nsy.wms.business.service.bd.BdAqlRuleService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.business.service.stockin.StockinReturnProductService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdAqlRuleEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderImgEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderItemEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderUnqualifiedEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskItemEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/22 10:23
 */
@Service
public class StockinQaOrderOperateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaOrderOperateService.class);

    @Resource
    StockinQaTaskService stockinQaTaskService;
    @Resource
    StockinQaTaskItemService stockinQaTaskItemService;
    @Resource
    StockinQaOrderService stockinQaOrderService;
    @Resource
    StockinQaOrderItemService stockinQaOrderItemService;
    @Resource
    StockInternalBoxService stockInternalBoxService;
    @Resource
    BdTagMappingService bdTagMappingService;
    @Resource
    ProductSpecInfoService productSpecInfoService;
    @Resource
    StockInternalBoxItemService stockInternalBoxItemService;
    @Resource
    StockinQaOrderLogService stockinQaOrderLogService;
    @Resource
    BdQaSopRuleService bdQaSopRuleService;
    @Resource
    StockinQaOrderProcessService stockinQaOrderProcessService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockinQaOrderUnqualifiedService stockinQaOrderUnqualifiedService;
    @Resource
    StockinQaOrderImgService stockinQaOrderImgService;
    @Resource
    StockinQcService stockinQcService;
    @Resource
    UserApiService userApiService;
    @Resource
    StockinQaOrderDetailService stockinQaOrderDetailService;
    @Resource
    StockinQaOrderConcessionRecevieService stockinQaOrderConcessionRecevieService;
    @Resource
    MessageProducer messageProducer;
    @Resource
    StockinQaOrderSearchService stockinQaOrderSearchService;
    @Resource
    StockinReturnProductService stockinReturnProductService;
    @Autowired
    private StockinQaOrderAuditService stockinQaOrderAuditService;
    @Autowired
    private ApplicationContext context;
    @Autowired
    private ProductSpecInfoService specInfoService;
    @Autowired
    private BdQaInspectRuleService bdQaInspectRuleService;
    @Autowired
    private StockinQaInspectService stockinQaInspectService;
    @Autowired
    private BdAqlRuleService aqlRuleService;
    @Autowired
    private SupplierApiService supplierApiService;

    /**
     * 查询内部箱，返回待质检任务
     * 1.校验箱子
     * 2.查询待质检的任务
     *
     * @param internalBoxCode
     * @return
     */
    public List<StockinWaitQaTaskPageResponse> queryWaitQaTask(String internalBoxCode) {
        StockInternalBoxEntity boxEntity = stockInternalBoxService.findByInternalBoxCode(internalBoxCode);
        if (Objects.isNull(boxEntity)) {
            throw new BusinessServiceException("找不到内部箱: " + internalBoxCode);
        }
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            throw new BusinessServiceException("箱子已删除");

        List<StockinWaitQaTaskPageResponse> taskEntityList = stockinQaTaskService.getBaseMapper().listWaitQaList(internalBoxCode);
        if (CollectionUtils.isEmpty(taskEntityList))
            return Collections.emptyList();

        List<String> skuList = taskEntityList.stream().map(StockinWaitQaTaskPageResponse::getSku).collect(Collectors.toList());
        Map<String, List<String>> skuTagMap = bdTagMappingService.getProductTagBySkus(skuList);

        List<Integer> taskIds = taskEntityList.stream().map(StockinWaitQaTaskPageResponse::getTaskId).collect(Collectors.toList());
        Map<Integer, List<StockinQaTaskItemEntity>> itemMap = stockinQaTaskItemService.list(new LambdaQueryWrapper<StockinQaTaskItemEntity>()
                .select(StockinQaTaskItemEntity::getQty, StockinQaTaskItemEntity::getPackageName,
                    StockinQaTaskItemEntity::getTaskId, StockinQaTaskItemEntity::getBrandName)
                .in(StockinQaTaskItemEntity::getTaskId, taskIds))
            .stream().collect(Collectors.groupingBy(StockinQaTaskItemEntity::getTaskId));

        taskEntityList.stream().forEach(response -> {
            List<StockinQaTaskItemEntity> taskItemEntityList = itemMap.get(response.getTaskId());
            Map<String, List<StockinQaTaskItemEntity>> collect = taskItemEntityList.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaTaskItemEntity::getPackageName));
            response.setPackageName(collect.entrySet().stream().map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue().stream().mapToInt(StockinQaTaskItemEntity::getQty).sum())).collect(Collectors.joining(",")));
            response.setBrandName(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getBrandName).filter(StringUtils::hasText).findAny().orElse(""));
            response.setBoxQty(taskItemEntityList.stream().mapToInt(StockinQaTaskItemEntity::getQty).sum());
            response.setFirstLabel(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getFirstLabel).filter(StringUtils::hasText).findAny().orElse(""));
            response.setProductTag(skuTagMap.get(response.getSku()));
            response.setIsReturnOrder(Objects.nonNull(response.getPurchasingApplyType()) && response.getPurchasingApplyType().equals(6) ? 1 : 0);
            StockinQaOrderEntity orderEntity = stockinQaOrderService.findLastCompletedRecordBySpu(response.getSpu());
            if (Objects.nonNull(orderEntity))
                response.setPreviousQaUserRealName(orderEntity.getQcUserName());
            //重新校验重量、高度是否需要测量，如果需要有同款尺码就赋值一个同款的重量、高度
            this.checkSkuNeedWeightAndNeedHeight(response);
        });
        return taskEntityList;
    }

    /**
     * 一. 校验箱内数据
     * 二. 判断是否存在已质检或质检中的质检单,校验重复质检
     * * 1.存在未质检完成时，返回质检单ID 并且提示 '质检任务已被XX领取，是否继续操作？'
     * * 2.存在初审和复审的质检单时，提醒用户 '已存在%s中的质检单，请等待相应人员操作'
     * * 3.存在已质检任务时，需判断箱子类型
     * * *一次性箱,相同sku不可重复质检
     * * *循环箱需判断质检单与箱内单据是否相同，相同则提示不可重复质检 ， 不相同则可继续质检
     * 三. 读取质检任务和质检sop，生成质检单，更新质检任务状态为质检中
     *
     * @param request
     * @return
     */
    @Transactional
    @JLock(keyConstant = "generateQaOrder", lockKey = "#barcode + '-' + #internalBoxCode")
    public StockinCreateQaOrderResponse generateQaOrder(StockinCreateQaOrderRequest request, String barcode, String internalBoxCode) {
        //一. 校验箱内数据
        StockInternalBoxEntity boxEntity = stockInternalBoxService.findByInternalBoxCode(request.getInternalBoxCode());
        if (Objects.isNull(boxEntity)) {
            throw new BusinessServiceException("找不到内部箱: " + request.getInternalBoxCode());
        }
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            throw new BusinessServiceException("箱子已删除");

        List<Integer> taskIds = stockInternalBoxItemService.getBaseMapper().findTaskIdByInternalBoxCode(request.getInternalBoxCode());
        if (CollectionUtils.isEmpty(taskIds))
            throw new BusinessServiceException("未找到内部箱明细！");

        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(request.getBarcode(), taskIds);
        if (Objects.isNull(specInfoEntity))
            throw new BusinessServiceException("未找到该商品！");

        List<StockInternalBoxItemEntity> boxItemEntities = stockInternalBoxItemService.getByInternalBoxCodeAndsku(boxEntity.getInternalBoxCode(), specInfoEntity.getSku());
        if (CollectionUtils.isEmpty(boxItemEntities))
            throw new BusinessServiceException("未找到内部箱明细！");

        //二. 判断是否存在已质检或质检中的质检单,校验重复质检
        StockinQaOrderEntity lastRecord = stockinQaOrderService.findLastRecordByInternalBoxCodeAndSku(boxEntity.getInternalBoxCode(), specInfoEntity.getSku());
        //校验重复质检
        StockinCreateQaOrderResponse response = validRepeat(lastRecord, boxItemEntities, boxEntity);
        if (Objects.nonNull(response))
            return response;

        //三. 读取质检任务和质检sop，生成质检单
        StockinQaTaskEntity qaTaskEntity = stockinQaTaskService.getOne(new LambdaQueryWrapper<StockinQaTaskEntity>()
            .eq(StockinQaTaskEntity::getInternalBoxCode, boxEntity.getInternalBoxCode())
            .eq(StockinQaTaskEntity::getSku, specInfoEntity.getSku())
            .in(StockinQaTaskEntity::getCheckStatus, Lists.newArrayList(StockinQaTaskStatusEnum.PENDING_QC.name(), StockinQaTaskStatusEnum.QC_PROCESSING.name()))
            .last("limit 1"));
        //1.如果没有质检任务，需要根据箱子明细补充一条质检任务
        if (Objects.isNull(qaTaskEntity))
            qaTaskEntity = stockinQaTaskService.buildQaTask(boxEntity, boxItemEntities);

        //需要入库类型，等查询除了质检单之后再做校验，校验是否需要量高度和称重
        boolean isNeedWeight = Objects.isNull(specInfoEntity.getActualWeight()) || specInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0;
        if (isNeedWeight && !StockinTypeEnum.SPOT.name().equalsIgnoreCase(qaTaskEntity.getStockinType()))
            throw new BusinessServiceException("请先完成称重,再开始质检");

        if (Objects.isNull(specInfoEntity.getPackageHeight()) && !StockinTypeEnum.SPOT.name().equalsIgnoreCase(qaTaskEntity.getStockinType()))
            throw new BusinessServiceException("请先完成量高度,再开始质检");

        //2.根据入库类型和商品标签读取SOP
        BdQaSopRuleDetailResponse sopRule = bdQaSopRuleService.getSopRuleByQaTask(qaTaskEntity.getStockinType(), qaTaskEntity.getLabelAttributeNames());
        //3.生成质检单
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.buildOrder(qaTaskEntity, sopRule, boxItemEntities);

        //开始质检
        stockinQcService.startQc(stockinQaOrderEntity.getInternalBoxCode(), stockinQaOrderEntity.getSku());

        //更新入库任务为质检中
        if (StockinQaTaskStatusEnum.PENDING_QC.name().equals(qaTaskEntity.getCheckStatus()))
            stockinQaTaskService.update(new LambdaUpdateWrapper<StockinQaTaskEntity>()
                .set(StockinQaTaskEntity::getCheckStatus, StockinQaTaskStatusEnum.QC_PROCESSING.name())
                .set(StockinQaTaskEntity::getOperator, loginInfoService.getName())
                .eq(StockinQaTaskEntity::getTaskId, qaTaskEntity.getTaskId()));

        response = new StockinCreateQaOrderResponse();
        response.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());

        return response;
    }

    /**
     * 判断是否存在已质检或质检中的质检单,校验重复质检
     * 1.存在未质检完成时，返回质检单ID 并且提示 '质检任务已被XX领取，是否继续操作？'
     * 2.存在初审和复审的质检单时，提醒用户 '已存在%s中的质检单，请等待相应人员操作'
     * 3.存在已质检任务时，需判断箱子类型
     * 一次性箱,相同sku不可重复质检
     * 循环箱需判断质检单与箱内单据是否相同，相同则提示不可重复质检 ， 不相同则可继续质检
     *
     * @param lastRecord
     * @return
     */
    private StockinCreateQaOrderResponse validRepeat(StockinQaOrderEntity lastRecord, List<StockInternalBoxItemEntity> boxItemList, StockInternalBoxEntity boxEntity) {
        if (Objects.isNull(lastRecord))
            return null;
        StockinCreateQaOrderResponse response = new StockinCreateQaOrderResponse();

        StockinQaOrderProcessStatusEnum statusEnum = StockinQaOrderProcessStatusEnum.valueOf(lastRecord.getProcessStatus());
        switch (statusEnum) {
            //1.存在未质检完成时，返回质检单ID 并且提示 '质检任务已被XX领取，是否继续操作？'
            case INCOMPLETE:
                response.setIsNotice(1);
                response.setNotice(String.format("质检任务已被%s领取，是否继续操作？", lastRecord.getQcUserName()));
                response.setStockinQaOrderId(lastRecord.getStockinQaOrderId());
                return response;
            // 2.存在初审和复审的质检单时，提醒用户 '已存在%s中的质检单，请等待相应人员操作完成'
            case FIRST_AUDIT:
            case SECOND_AUDIT:
            case INSPECT_AUDIT:
                throw new BusinessServiceException(String.format("已存在%s中的质检单，请等待相应人员操作", statusEnum.getStatus()));

            case COMPLETED:
                //3.存在已质检任务时，需判断箱子类型
                if (StockInternalBoxMaterialSizeEnum.SINGLE_USE_BOX.name().equals(boxEntity.getInternalBoxMaterialSize()))
                    // 一次性箱,相同sku不可重复质检
                    throw new BusinessServiceException("该箱子的该款已经质检过了！");

                //循环箱需判断质检单与箱内单据是否相同，相同则提示不可重复质检 ， 不相同则可继续质检
                if (StockInternalBoxMaterialSizeEnum.LOOP_BOX.name().equals(boxEntity.getInternalBoxMaterialSize())) {
                    Set<String> stockinOrderNoSet = stockinQaOrderItemService.findStockinOrderNoByOrderId(lastRecord.getStockinQaOrderId());
                    Set<String> collect = boxItemList.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().collect(Collectors.toSet());
                    if (stockinOrderNoSet.size() == collect.size() && stockinOrderNoSet.containsAll(collect)) {
                        throw new BusinessServiceException("该箱子的该款已经质检过了！");
                    }
                }
                return null;
            default:
                return null;
        }
    }

    /**
     * 质检操作接口
     * 一.校验质检单和质检单状态
     * 二.保存质检流程记录、不合格原因、图片
     * 三.如果是SOP流程最后一步，需要记录质检数量，修改质检状态
     * 判断需退货数量（不合格数量-直接退货数）
     * <=1走合格上架和部分退货流程
     * >1进入初审流程 发起工作流
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "qaOperate", lockKey = "#request.stockinQaOrderId")
    public void operate(StockinQaOperateRequest request) {
        //一.校验质检单和质检单状态
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        if (!StockinQaOrderProcessStatusEnum.INCOMPLETE.name().equals(stockinQaOrderEntity.getProcessStatus()))
            throw new BusinessServiceException(String.format("质检单状态为%s，无法继续操作",
                StockinQaOrderProcessStatusEnum.valueOf(stockinQaOrderEntity.getProcessStatus()).getStatus()));

        StockinQaOrderProcessEntity processEntity = null;

        if (BdQaSopEnum.QC_RESULT.getProcessName().equals(request.getProcessName())) {
            //生成一条质检结果的流程
            processEntity = stockinQaOrderProcessService.buildOrderProcessResult(stockinQaOrderEntity, BdQaSopEnum.QC_RESULT);
        }

        if (Objects.isNull(processEntity))
            processEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
                .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, request.getStockinQaOrderId())
                .eq(StockinQaOrderProcessEntity::getProcessName, request.getProcessName())
                .last("limit 1"));


        if (Objects.isNull(processEntity))
            throw new BusinessServiceException("未找到质检流程");

        //二.保存质检流程记录、不合格原因、图片
        StockinQaOrderProcessEntity qaOrderProcessEntity = new StockinQaOrderProcessEntity();
        BeanUtils.copyProperties(request, qaOrderProcessEntity);
        qaOrderProcessEntity.setQaCount(request.getTestTotalCount());
        qaOrderProcessEntity.setOperateDate(new Date());
        qaOrderProcessEntity.setOperator(loginInfoService.getName());
        qaOrderProcessEntity.setUpdateBy(loginInfoService.getName());
        qaOrderProcessEntity.setStockinQaOrderProcessId(processEntity.getStockinQaOrderProcessId());
        stockinQaOrderProcessService.updateById(qaOrderProcessEntity);

        //如果存在不合格原因，需要新增
        if (StringUtils.hasText(request.getUnqualifiedCategory())) {
            stockinQaOrderService.update(new LambdaUpdateWrapper<StockinQaOrderEntity>()
                .set(StockinQaOrderEntity::getUnqualifiedCategory, request.getUnqualifiedCategory())
                .eq(StockinQaOrderEntity::getStockinQaOrderId, request.getStockinQaOrderId()));
            stockinQaOrderUnqualifiedService.saveUnqualified(qaOrderProcessEntity, request);
        }
        //先删除原来的图片
        stockinQaOrderImgService.removeByStockinQaOrderIdAndProcessName(stockinQaOrderEntity.getStockinQaOrderId(), request.getProcessName());
        //图片不为空则保存
        if (!CollectionUtils.isEmpty(request.getImgUrl())) {
            stockinQaOrderImgService.saveImageList(processEntity, request.getImgUrl());
        }

        //记录质检操作日志
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.QA_PROCESS,
            String.format("【%s】操作质检流程【%s】%s", loginInfoService.getName(), request.getProcessName(),
                QaProcessEnum.getValueByName(request.getStatus())));

        //三.如果是SOP流程最后一步，需要记录质检数量，修改质检状态
        if (BdQaSopEnum.QC_RESULT.getProcessName().equals(processEntity.getProcessName())) {
            this.qaSopComplete(stockinQaOrderEntity, request);
        }
        //由于质检员进行了数据隔离，故每次操作都需要更新对应所属的用户id
        stockinQaOrderService.update(new LambdaUpdateWrapper<StockinQaOrderEntity>()
            .set(StockinQaOrderEntity::getUserId, loginInfoService.getUserId())
            .set(StockinQaOrderEntity::getQcUserName, loginInfoService.getName())
            .set(StockinQaOrderEntity::getQcUserCode, loginInfoService.getUserCode())
            .eq(StockinQaOrderEntity::getStockinQaOrderId, request.getStockinQaOrderId()));

    }

    /**
     * 三.如果是SOP流程最后一步，需要记录质检数量，修改质检状态
     * 判断需退货数量（不合格数量-直接退货数）
     * <=1走合格上架和部分退货流程
     * >1进入初审流程 发起工作流
     *
     * @param stockinQaOrderEntity
     * @param request
     */
    private void qaSopComplete(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateRequest request) {
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        //1.判断需退货数量（不合格数量-直接退货数），<=1走合格上架和部分退货流程   >1进入初审流程
        int unqualifiedCount = request.getUnqualifiedCount();
        //直接退货数
        int directReturnCount = Objects.nonNull(request.getDirectReturnCount()) ? request.getDirectReturnCount() : 0;
        //总的直接退货数
        int totalDirectReturnCount = (Objects.isNull(detailEntity.getDirectReturnCount()) ? 0 : detailEntity.getDirectReturnCount()) + directReturnCount;
        //需退货数量
        int realReturnCount = unqualifiedCount - directReturnCount;
        //查询对应aql规则
        String defectCountContent = "";
        if (request.getMinorDefectCount() > 0 || request.getMajorDefectCount() > 0 || request.getCriticalDefectCount() > 0) {
            defectCountContent = String.format(",轻微问题数量：%s,严重问题数量：%s,致命问题数量：%s", request.getMinorDefectCount(), request.getMajorDefectCount(), request.getCriticalDefectCount());
        }
        //获取当前质检数对应的aql规则
        BdAqlRuleEntity aqlRuleInfo = aqlRuleService.getAqlRuleInfo(stockinQaOrderEntity.getSpaceId(), request.getTestTotalCount());
        StockinQaAqlValidDTO stockinQaAqlValidDTO = new StockinQaAqlValidDTO(request.getMinorDefectCount(), request.getMajorDefectCount(), request.getCriticalDefectCount(), stockinQaOrderEntity.getSpaceId(), request.getTestTotalCount());
        //是否符合aql规则
        boolean aqlRuleValid = aqlRuleService.validateAqlRule(stockinQaAqlValidDTO, aqlRuleInfo);
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.QA_SUBMIT, String.format("%s操作初检完成", loginInfoService.getName()));
        //如果对应地区没有aql规则,则需退货数量<=1走合格上架和部分退货流程, 如果有aql规则，则需问题件<可接受数量走合格上架和部分退货流程
        if (realReturnCount <= 1 && Objects.isNull(aqlRuleInfo) || !aqlRuleValid && Objects.nonNull(aqlRuleInfo)) {
            //判断是否触发稽查规则
            if (bdQaInspectRuleService.validInspectByStockinQaOrder(stockinQaOrderEntity.getStockinQaOrderId())) {
                //由于宓思不填直接退货数了，所以不合格数=直接退货数， 总直接退货数 = 不合格数家原先的直接退货数
                stockinQaInspectService.stockinQaSopCompleteInspect(stockinQaOrderEntity, request, unqualifiedCount, (Objects.isNull(detailEntity.getDirectReturnCount()) ? 0 : detailEntity.getDirectReturnCount()) + unqualifiedCount, defectCountContent);
                return;
            }
            StockinQaOrderStatusEnum statusEnum = unqualifiedCount > 0
                ? totalDirectReturnCount == detailEntity.getArrivalCount()
                ? StockinQaOrderStatusEnum.BATCH_RETURN : StockinQaOrderStatusEnum.SOME_RETURN : StockinQaOrderStatusEnum.PUT_ON;
            stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.COMPLETED);
            StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
            orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            orderEntity.setResult(statusEnum.name());
            orderEntity.setCompleteDate(new Date());
            orderEntity.setUpdateBy(loginInfoService.getName());
            stockinQaOrderService.updateById(orderEntity);
            stockinQaOrderDetailService.completeOrderDetailBySop(stockinQaOrderEntity, request, totalDirectReturnCount);
            QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), unqualifiedCount);

            //完结质检任务
            stockinQaTaskService.completeTask(stockinQaOrderEntity);

            //前面已经退货了，质检完成需要将退货数量置0
            stockinQcService.qcComplete(qcInboundsMessage);
            //记录日志
            String content = String.format("【%s】操作质检完成，质检结果为【%s】%s", loginInfoService.getName(), statusEnum.getStatus(),
                    unqualifiedCount <= 0 ? "" : String.format("，不合格数：%s，退货数：%s%s，不合格分类： %s ，不合格原因：%s",
                            unqualifiedCount, unqualifiedCount, defectCountContent, request.getUnqualifiedCategory(), request.getUnqualifiedReason()));
            stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.COMPLETE, content);
        } else {
            // 需退货数量>1进入初审流程
            //获取当前用户的上级
            stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.FIRST_AUDIT);
            StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
            orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            orderEntity.setUpdateBy(loginInfoService.getName());
            SysUserInfoListResponse superiorUserInfo = userApiService.getSuperiorByUserAccount(loginInfoService.getUserName());
            if (Objects.nonNull(superiorUserInfo) && !CollectionUtils.isEmpty(superiorUserInfo.getContent()))
                orderEntity.setAppointor(superiorUserInfo.getContent().get(0).getUserName());
            stockinQaOrderService.updateById(orderEntity);

            //处理数据
            sopCompleteDealData(stockinQaOrderEntity, request, directReturnCount, totalDirectReturnCount);

            //发起工作流
            StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.SUBMIT, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName(), defectCountContent);
            messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC, 
                    Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
                    new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
        }
    }

    //初审、稽查处理数据
    public void sopCompleteDealData(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateRequest request, int directReturnCount, int totalDirectReturnCount) {
        //更新不合格数
        stockinQaOrderDetailService.update(new LambdaUpdateWrapper<StockinQaOrderDetailEntity>()
                .set(StockinQaOrderDetailEntity::getDirectReturnCount, totalDirectReturnCount)
                .set(StockinQaOrderDetailEntity::getTestTotalCount, request.getTestTotalCount())
                .set(StockinQaOrderDetailEntity::getUnqualifiedCount, request.getUnqualifiedCount())
                .set(StockinQaOrderDetailEntity::getMinorDefectCount, totalDirectReturnCount)
                .set(StockinQaOrderDetailEntity::getMajorDefectCount, request.getTestTotalCount())
                .set(StockinQaOrderDetailEntity::getCriticalDefectCount, request.getCriticalDefectCount())
                .set(StockinQaOrderDetailEntity::getUpdateBy, loginInfoService.getName())
                .eq(StockinQaOrderDetailEntity::getStockinQaOrderId, request.getStockinQaOrderId()));

        //存在直接退货数 需要退货
        if (directReturnCount > 0) {
            stockinQaOrderDetailService.updateSetDirectReturnCount(stockinQaOrderEntity.getStockinQaOrderId(), totalDirectReturnCount);
            QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), directReturnCount);
            stockinQcService.addReturnQuantity(qcInboundsMessage, false);
            stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.QA_PROCESS,
                String.format("【%s】操作质检流程【%s】直接退货%s件", loginInfoService.getName(), request.getProcessName(),
                    directReturnCount));
        }

        //通知进入待处理
        stockinQcService.notifyWaitDeal(stockinQaOrderEntity.getInternalBoxCode(), stockinQaOrderEntity.getSku());
    }

    public QcInboundsMessage buildQcMessage(Integer stockinQaOrderId, Integer goodsToRefundCount) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(stockinQaOrderId);

        List<StockinQaOrderItemEntity> itemEntityList = stockinQaOrderItemService.findByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        List<StockinQaOrderUnqualifiedEntity> unqualifiedEntityList = stockinQaOrderUnqualifiedService.findByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());

        QcInboundsMessage qcInboundsMessage = new QcInboundsMessage();
        BeanUtils.copyProperties(stockinQaOrderEntity, qcInboundsMessage);
        BeanUtils.copyProperties(detailEntity, qcInboundsMessage);
        qcInboundsMessage.setReceiveOrderNos(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(",")));
        qcInboundsMessage.setBoxBarcode(stockinQaOrderEntity.getInternalBoxCode());
        qcInboundsMessage.setGoodsToRefundCount(goodsToRefundCount);
        qcInboundsMessage.setIsAllCheck(0);
        qcInboundsMessage.setIsQcRoutingInspect(0);
        qcInboundsMessage.setQaType(StockChangeLogTypeEnum.RETURN_QC);
        qcInboundsMessage.setQcInboundsId(stockinQaOrderEntity.getStockinQaOrderId());
        qcInboundsMessage.setPurchaseNumbers(itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        if (!CollectionUtils.isEmpty(unqualifiedEntityList)) {
            StockinQaOrderUnqualifiedEntity unqualifiedEntity = unqualifiedEntityList.get(0);
            qcInboundsMessage.setUnqualifiedQuestion(unqualifiedEntity.getUnqualifiedQuestion());
            qcInboundsMessage.setUnqualifiedReason(unqualifiedEntity.getUnqualifiedReason());
        }
        qcInboundsMessage.setPurchaseUserName(stockinQaOrderEntity.getPurchaseUserCode());
        qcInboundsMessage.setPurchaseUserRealName(stockinQaOrderEntity.getPurchaseUserName());
        qcInboundsMessage.setQcUserName(stockinQaOrderEntity.getQcUserCode());
        qcInboundsMessage.setQcUserRealName(stockinQaOrderEntity.getQcUserName());
        qcInboundsMessage.setIsWmsQa(1);
        qcInboundsMessage.setResult(getStatusDesc(stockinQaOrderEntity));
        return qcInboundsMessage;
    }

    /**
     * 将质检单状态转化成旧质检系统的状态
     *
     * @param stockinQaOrderEntity
     * @return
     */
    public String getStatusDesc(StockinQaOrderEntity stockinQaOrderEntity) {
        String result = stockinQaOrderEntity.getResult();
        if (!StringUtils.hasText(result))
            return "";
        StockinQaOrderStatusEnum statusEnum = StockinQaOrderStatusEnum.valueOf(result);
        switch (statusEnum) {
            case PUT_ON:
                return QcInboundsResultStatusEnum.PUT_ON.getDesc();
            case SOME_RETURN:
                return QcInboundsResultStatusEnum.RECEIVE_BUT_SOME_RETURN.getDesc();
            case BATCH_RETURN:
                return QcInboundsResultStatusEnum.FACTORY_REWORK.getDesc();
            case CONCESSION_RECEIVE:
                return QcInboundsResultStatusEnum.CONCESSION_RECEIVE.getDesc();
            default:
                return "";
        }
    }


    /**
     * 质检初审
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "firstAudit", lockKey = "#request.stockinQaOrderId")
    public void firstAudit(StockinQaOperateFirstAuditRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        //校验工作流
        stockinQaOrderAuditService.getAndValidWorkFlowInfo(stockinQaOrderEntity, loginInfoService.isAdmin());

        if (!StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("该质检单不是初审状态");
        }
        StockinQaOrderDetailEntity qaOrderDetailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        //设置不合格数 =  min(箱内数 , 直接退货数 + 让步接收数 + 退货数)
        request.setUnqualifiedCount(Math.min(qaOrderDetailEntity.getBoxQty(), request.getReturnCount() + qaOrderDetailEntity.getDirectReturnCount() + request.getConcessionsCount()));

        if (request.getReturnCount() + qaOrderDetailEntity.getDirectReturnCount() + request.getConcessionsCount() > request.getUnqualifiedCount()) {
            throw new BusinessServiceException("直接退货数 + 退货数 + 让步接收数不能大于箱内数!");
        }

        //新增一条初审的流程记录
        StockinQaOrderProcessEntity lastProcessEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
            .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, request.getStockinQaOrderId())
            .orderByDesc(StockinQaOrderProcessEntity::getSort)
            .last("limit 1"));
        StockinQaOrderProcessEntity processEntity = new StockinQaOrderProcessEntity();
        BeanUtils.copyProperties(request, processEntity);
        processEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        processEntity.setProcessName(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());
        processEntity.setOperator(loginInfoService.getName());
        processEntity.setLocation(stockinQaOrderEntity.getLocation());
        processEntity.setSort(lastProcessEntity.getSort() + 1);
        stockinQaOrderProcessService.save(processEntity);


        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setUpdateBy(loginInfoService.getName());

        //如果存在不合格原因，需要新增
        if (StringUtils.hasText(request.getUnqualifiedCategory())) {
            orderEntity.setUnqualifiedCategory(request.getUnqualifiedCategory());
            stockinQaOrderUnqualifiedService.saveUnqualifiedFirstAudit(processEntity, request);
            stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.FIRST_AUDIT,
                String.format("%s将不合格归类由【%s】修改为【%s】", loginInfoService.getName(),
                    stockinQaOrderEntity.getUnqualifiedCategory(), request.getUnqualifiedCategory()));
        }

        //图片不为空则保存
        if (!CollectionUtils.isEmpty(request.getImageList())) {
            stockinQaOrderImgService.saveImageList(processEntity, request.getImageList());
        }
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.AUDIT_END, String.format("%s完成质检单初审", loginInfoService.getName()));
        //判断是否完结
        if (request.getIsComplete().equals(1)) {
            if (request.getConcessionsCount() > 0)
                throw new BusinessServiceException("让步接收数大于0请提交复审");
            //判断是否触发稽查规则,除了批量退货，有合格数的符合条件都要进行稽查
            if (bdQaInspectRuleService.validInspectByStockinQaOrder(stockinQaOrderEntity.getStockinQaOrderId()) && this.validInspectStatus(request, qaOrderDetailEntity)) {
                //
                this.firstAuditReturn(stockinQaOrderEntity, request.getReturnCount());
                firstAuditInspect(request, stockinQaOrderEntity);
                return;
            }

            StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.completeOrderDetailByFirstAudit(stockinQaOrderEntity, request);
            orderEntity.setCompleteDate(new Date());

            stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.COMPLETED);

            StockinQaOrderStatusEnum statusEnum = getStatusEnumByDetail(detailEntity);
            orderEntity.setResult(statusEnum.name());
            //记录日志
            String content = String.format("【%s】操作质检初审完成，质检结果为【%s】%s", loginInfoService.getName(), statusEnum.getStatus(),
                detailEntity.getUnqualifiedCount() <= 0 ? "" : String.format("，不合格数：%s，退货数：%s，不合格分类： %s",
                    detailEntity.getUnqualifiedCount(), detailEntity.getReturnCount(), stockinQaOrderEntity.getUnqualifiedCategory()));
            stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.COMPLETE, content);
            stockinQaOrderService.updateById(orderEntity);

            //完结质检任务
            stockinQaTaskService.completeTask(stockinQaOrderEntity);

            //返回入库单
            QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), request.getReturnCount());

            //前面已经退货了，质检完成需要将退货数量置0
            stockinQcService.qcComplete(qcInboundsMessage);
            //完结初审工作流
            StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.SUBMIT_TO_END, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName());
            messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC,
                Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
                new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
        } else {
            //提交复审
            toSecondAudit(request, stockinQaOrderEntity, orderEntity);
        }
    }

    /**
     * 查询符合批量稽查的状态
     * 除了批量退货，有合格数的符合条件都要进行稽查
     *
     * @param request
     * @param detailEntity
     * @return
     */
    private boolean validInspectStatus(StockinQaOperateFirstAuditRequest request, StockinQaOrderDetailEntity detailEntity) {
        int concessionsCount = Objects.isNull(request.getConcessionsCount()) ? 0 : request.getConcessionsCount();
        int boxQty = Objects.isNull(detailEntity.getBoxQty()) ? 0 : detailEntity.getBoxQty();
        int returnCount = (Objects.isNull(request.getReturnCount()) ? 0 : request.getReturnCount()) + (Objects.isNull(detailEntity.getDirectReturnCount()) ? 0 : detailEntity.getDirectReturnCount());
        StockinQaOrderStatusEnum stockinQaOrderStatusEnum = concessionsCount > 0 ? StockinQaOrderStatusEnum.CONCESSION_RECEIVE : returnCount <= 0
                ? StockinQaOrderStatusEnum.PUT_ON : returnCount < boxQty
                ? StockinQaOrderStatusEnum.SOME_RETURN : StockinQaOrderStatusEnum.BATCH_RETURN;
        return StockinQaOrderStatusEnum.BATCH_RETURN != stockinQaOrderStatusEnum;
    }

    private void toSecondAudit(StockinQaOperateFirstAuditRequest request, StockinQaOrderEntity stockinQaOrderEntity, StockinQaOrderEntity orderEntity) {
        //提交复审
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.SECOND_AUDIT);

        //记录日志
        String content = String.format("【%s】提交质检复审，不合格数：%s，退货数：%s，让步接收数：%s，备注：%s",
            loginInfoService.getName(), request.getUnqualifiedCount(), request.getReturnCount(), request.getConcessionsCount(),
            StringUtils.hasText(request.getRemark()) ? request.getRemark() : "");
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.FIRST_AUDIT, content);
        SysUserInfoListResponse superiorUserInfo = userApiService.getSuperiorByUserAccount(loginInfoService.getUserName());
        if (Objects.nonNull(superiorUserInfo) && !CollectionUtils.isEmpty(superiorUserInfo.getContent())) {
            orderEntity.setAppointor(superiorUserInfo.getContent().get(0).getUserName());
            stockinQaOrderService.updateById(orderEntity);
        } else {
            //如果没有指派人更新复审人为空
            stockinQaOrderService.updateById(orderEntity);
            stockinQaOrderService.clearAppointor(orderEntity.getStockinQaOrderId());
        }

        //开启复审工作流
        StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.SUBMIT_TO_AUDIT, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName());
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC,
            Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
            new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
    }

    private void firstAuditInspect(StockinQaOperateFirstAuditRequest request, StockinQaOrderEntity stockinQaOrderEntity) {
        stockinQaInspectService.firstAuditInspect(stockinQaOrderEntity, request);
        //完结初审工作流
        StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.DELETE, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName());
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC,
            Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
            new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
    }

    /**
     * 质检复审
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "secondAudit", lockKey = "#request.stockinQaOrderId")
    public void secondAudit(StockinQaOperateSecondAuditRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        //校验工作流
        stockinQaOrderAuditService.getAndValidWorkFlowInfo(stockinQaOrderEntity, loginInfoService.isAdmin());
        if (!StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("该质检单不是复审状态");
        }

        StockinQaOrderDetailEntity qaOrderDetailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());

        //设置不合格数 =  min(箱内数 , 直接退货数 + 让步接收数 + 退货数)
        request.setUnqualifiedCount(Math.min(qaOrderDetailEntity.getBoxQty(), request.getReturnCount() + qaOrderDetailEntity.getDirectReturnCount() + request.getConcessionsCount()));
        if (qaOrderDetailEntity.getDirectReturnCount() + request.getReturnCount() + request.getConcessionsCount() > request.getUnqualifiedCount()) {
            throw new BusinessServiceException("直接退货数 + 退货数+让步接收数不能大于箱内数!");
        }

        //参数校验
        validSecondAuditRequest(request);

        //新增一条复审的流程记录
        StockinQaOrderProcessEntity lastProcessEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
            .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, request.getStockinQaOrderId())
            .orderByDesc(StockinQaOrderProcessEntity::getSort)
            .last("limit 1"));
        StockinQaOrderProcessEntity processEntity = new StockinQaOrderProcessEntity();
        BeanUtils.copyProperties(request, processEntity);
        processEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        processEntity.setProcessName(StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus());
        processEntity.setLocation(stockinQaOrderEntity.getLocation());
        processEntity.setSort(lastProcessEntity.getSort() + 1);
        processEntity.setOperator(loginInfoService.getName());
        stockinQaOrderProcessService.save(processEntity);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setUpdateBy(loginInfoService.getName());

        //图片不为空则保存
        if (!CollectionUtils.isEmpty(request.getImageList())) {
            stockinQaOrderImgService.saveImageList(processEntity, request.getImageList());
        }

        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.completeOrderDetailBySecondAudit(stockinQaOrderEntity, request);
        orderEntity.setCompleteDate(new Date());

        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.COMPLETED);

        //判断是否存在让步接收数
        StockinQaOrderStatusEnum statusEnum = getStatusEnumByDetail(detailEntity);
        orderEntity.setResult(statusEnum.name());

        //记录日志
        String content = String.format("【%s】操作质检复审完成，质检结果为【%s】%s", loginInfoService.getName(), statusEnum.getStatus(),
            detailEntity.getUnqualifiedCount() <= 0 ? "" : String.format("，不合格数：%s，退货数：%s，让步接收数： %s",
                detailEntity.getUnqualifiedCount(), detailEntity.getReturnCount(), detailEntity.getConcessionsCount()));
        if (StringUtils.hasText(request.getResponsibility()))
            content = content + String.format("，责任方：%s%s%s", request.getResponsibility(),
                StringUtils.hasText(request.getProcessingProgram()) ? "，处理方案：" + request.getProcessingProgram() : "",
                StringUtils.hasText(request.getDepartResponsibility()) ? "，责任部门：" + request.getDepartResponsibility() : "");
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.COMPLETE, content);
        stockinQaOrderService.updateById(orderEntity);

        //完结质检任务
        stockinQaTaskService.completeTask(stockinQaOrderEntity);

        //反馈入库单
        QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), request.getReturnCount());

        if (StockinQaOrderStatusEnum.CONCESSION_RECEIVE.name().equals(statusEnum.name())) {
            //保存让步接收数据
            stockinQaOrderConcessionRecevieService.saveConcessionRecevieEntity(stockinQaOrderEntity, request);
            qcInboundsMessage.setPriceList(request.getPriceList());
        }

        stockinQcService.qcComplete(qcInboundsMessage);
        //完结复审工作流
        StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.AUDIT_TO_END, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName());
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC,
            Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
            new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
    }

    private void validSecondAuditRequest(StockinQaOperateSecondAuditRequest request) {
        if (!StringUtils.hasText(request.getResponsibility()))
            throw new BusinessServiceException("请选择责任方");
        if (!StringUtils.hasText(request.getProcessingProgram()))
            throw new BusinessServiceException("请选择处理方案");

        if (request.getResponsibility().contains("内部责任") && !StringUtils.hasText(request.getDepartResponsibility()))
            throw new BusinessServiceException("请选择责任部门");
    }

    @NotNull
    public StockinQaOrderStatusEnum getStatusEnumByDetail(StockinQaOrderDetailEntity detailEntity) {
        return detailEntity.getConcessionsCount() > 0 ? StockinQaOrderStatusEnum.CONCESSION_RECEIVE : detailEntity.getReturnCount() <= 0
            ? StockinQaOrderStatusEnum.PUT_ON : detailEntity.getReturnCount() < detailEntity.getBoxQty()
            ? StockinQaOrderStatusEnum.SOME_RETURN : StockinQaOrderStatusEnum.BATCH_RETURN;
    }

    @Transactional
    public void batchFirstAudit(StockinQaBatchFirstAuditRequest request) {
        StockinQaOrderOperateService bean = context.getBean(this.getClass());
        request.getList().forEach(item -> bean.firstAudit(item));
    }

    public void batchSecondAudit(StockinQaBatchSecondAuditRequest request) {
        //批量处理，如果存在让步接收数
        StockinQaOrderOperateService bean = context.getBean(this.getClass());
        request.getList().forEach(item -> {
            StockinQaOrderEntity orderEntity = stockinQaOrderService.getById(item.getStockinQaOrderId());
            List<String> purchasePlanNos = stockinQaOrderItemService.getBaseMapper().getPurchasePlanNoByStockinQaOrderId(item.getStockinQaOrderId());
            List<QcInboundsPriceItem> qcInboundsPriceItems = stockinQaOrderSearchService.buildPriceItemList(orderEntity, purchasePlanNos);

            qcInboundsPriceItems.forEach(priceItem -> {
                priceItem.setConcessionsPrice(priceItem.getPurchasePrice().multiply(priceItem.getDiscount()));
            });
            item.setPriceList(qcInboundsPriceItems);
            bean.secondAudit(item);
        });

    }

    /**
     * 1.校验箱内是否存在商品可供退货
     * 2.校验质检单的可退货数是否足够退货
     * 3.修改质检单退货数和不合格数，退货数>0：部分退货。退货数=箱内数：批量退货
     * 4.操作库存，反馈入库单
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "addReturnQuantity", lockKey = "#request.stockinQaOrderId")
    public void addReturnQuantity(StockinQaOperateReturnRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        if (!StockinQaOrderProcessStatusEnum.COMPLETED.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("该质检单不是质检完成状态");
        }
        if (request.getQty() <= 0) {
            throw new BusinessServiceException("退货数需大于0");
        }


        //1.校验箱内是否存在商品可供退货
        List<StockInternalBoxItemEntity> boxItemEntities = stockInternalBoxItemService.getByInternalBoxCodeAndsku(stockinQaOrderEntity.getInternalBoxCode(), stockinQaOrderEntity.getSku());
        int boxQty = boxItemEntities.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
        if (request.getQty() > boxQty) {
            throw new BusinessServiceException(String.format("箱内件数%s件，不足以退货%s件", boxQty, request.getQty()));
        }

        // 2.校验质检单的可退货数是否足够退货
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.getById(request.getStockinQaOrderId());
        //可退货数
        int canReturnQty = detailEntity.getArrivalCount() - detailEntity.getReturnCount();
        if (request.getQty() > canReturnQty)
            throw new BusinessServiceException(String.format("该质检单只能够退%s件，不足以退货%s件", canReturnQty, request.getQty()));

        detailEntity.setReturnCount(detailEntity.getReturnCount() + request.getQty());
        detailEntity.setUpdateBy(loginInfoService.getName());
        stockinQaOrderDetailService.updateById(detailEntity);

        if (!StringUtils.hasText(stockinQaOrderEntity.getUnqualifiedCategory())
            && StringUtils.hasText(request.getUnqualifiedCategory())) {
            stockinQaOrderService.addReturnSetUnqualifiedCategory(stockinQaOrderEntity, request);
        }

        //日志内容
        String content = String.format("【%s】增加【%s】退货数%s件", loginInfoService.getName(),
            stockinQaOrderEntity.getSku(), request.getQty());

        //3.修改质检单退货数和不合格数，退货数>0：部分退货。退货数=箱内数：批量退货
        StockinQaOrderStatusEnum statusEnum = getStatusEnumByDetail(detailEntity);
        if (!stockinQaOrderEntity.getResult().equals(statusEnum.name())) {
            StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
            orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            orderEntity.setResult(statusEnum.name());
            orderEntity.setUpdateBy(loginInfoService.getName());
            stockinQaOrderService.updateById(orderEntity);
            content += String.format("，修改质检单状态为%s", statusEnum.getStatus());
        }

        //记录日志
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.ADD_RETURN_QUANTITY, content);

        //4.操作库存，反馈入库单
        QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), request.getQty());

        stockinQcService.addReturnQuantity(qcInboundsMessage, false);

        StockinQaSyncDto qaSyncDto = new StockinQaSyncDto(stockinQaOrderEntity.getStockinQaOrderId(), "addReturnQuantity");
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_SYNC_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_SYNC_TOPIC,
            Key.of(TenantContext.getTenant() + "_" + stockinQaOrderEntity.getStockinQaOrderId()),
            new LocationWrapperMessage(TenantContext.getTenant(), qaSyncDto));
    }

    /**
     * 1.校验退货库位上库存是否足够减少
     * 2.校验质检单的退货数是否足够减少
     * 3.修改质检单退货数和不合格数，退货数>0：部分退货。退货数=箱内数：批量退货
     * 4.操作库存，反馈入库单
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "reduceReturnQuantity", lockKey = "#request.stockinQaOrderId")
    public void reduceReturnQuantity(StockinQaOperateReturnRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        if (!StockinQaOrderProcessStatusEnum.COMPLETED.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("该质检单不是质检完成状态");
        }

        List<StockinQaOrderItemEntity> itemEntityList = stockinQaOrderItemService.findByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());

        //1.校验退货库位上库存是否足够
        //先查询退货库位库存
        List<StockinReturnProductEntity> returnProductEntityList = stockinReturnProductService.list(new LambdaQueryWrapper<StockinReturnProductEntity>()
            .eq(StockinReturnProductEntity::getSku, stockinQaOrderEntity.getSku())
            .in(StockinReturnProductEntity::getStockinOrderNo, itemEntityList.stream().map(StockinQaOrderItemEntity::getStockinOrderNo).distinct().collect(Collectors.toList()))
            .in(StockinReturnProductEntity::getPurchasePlanNo, itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.toList())));
        int reutrnPositionQty = returnProductEntityList.stream().mapToInt(StockinReturnProductEntity::getReturnQty).sum();
        if (request.getQty() > reutrnPositionQty) {
            throw new BusinessServiceException(String.format("退货库位剩余%s件，不足以退货%s件", reutrnPositionQty, request.getQty()));
        }

        // 2.校验质检单的退货数是否足够减少
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.getById(request.getStockinQaOrderId());
        //可退货数
        int returnCount = detailEntity.getReturnCount();
        if (request.getQty() > returnCount)
            throw new BusinessServiceException(String.format("该质检单共退货%s件，不能减少%s件", returnCount, request.getQty()));

        detailEntity.setReturnCount(detailEntity.getReturnCount() - request.getQty());
        detailEntity.setUpdateBy(loginInfoService.getName());
        stockinQaOrderDetailService.updateById(detailEntity);

        //日志内容
        String content = String.format("【%s】减少【%s】退货数%s件", loginInfoService.getName(),
            stockinQaOrderEntity.getSku(), request.getQty());

        //3.修改质检单退货数和不合格数，退货数>0：部分退货。退货数=箱内数：批量退货
        StockinQaOrderStatusEnum statusEnum = getStatusEnumByDetail(detailEntity);
        if (!stockinQaOrderEntity.getResult().equals(statusEnum.name())) {
            StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
            orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            orderEntity.setResult(statusEnum.name());
            orderEntity.setUpdateBy(loginInfoService.getName());
            stockinQaOrderService.updateById(orderEntity);
            content += String.format("，修改质检单状态为%s", statusEnum.getStatus());
        }

        //记录日志
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.REDUCE_RETURN_QUANTITY, content);

        //4.操作库存，反馈入库单
        QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), request.getQty());
        qcInboundsMessage.setInternalBoxNo(request.getInternalBoxCode());
        stockinQcService.reduceReturnQuantity(qcInboundsMessage);

        StockinQaSyncDto qaSyncDto = new StockinQaSyncDto(stockinQaOrderEntity.getStockinQaOrderId(), "reduceReturnQuantity");
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_SYNC_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_SYNC_TOPIC,
            Key.of(TenantContext.getTenant() + "_" + stockinQaOrderEntity.getStockinQaOrderId()),
            new LocationWrapperMessage(TenantContext.getTenant(), qaSyncDto));
    }

    /**
     * 取消质检单
     *
     * @param stockinQaOrderId
     */
    @Transactional
    public void deleteOrder(Integer stockinQaOrderId) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(stockinQaOrderId);
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        if (StockinQaOrderProcessStatusEnum.COMPLETED.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("已质检完成的质检单无法取消");
        }
        //取消质检单
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.CANCELED);

        //记录日志
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.DELETED,
            String.format("%s取消质检单", loginInfoService.getName()));

        //质检初审复审才去取消工作流
        if (Lists.newArrayList(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name(), StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name()).contains(stockinQaOrderEntity.getProcessStatus())) {
            //取消工作流
            StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.DELETE, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName());
            messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC,
                Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
                new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
        }
    }

    /**
     * 修改质检初审、复审状态的质检单到未完成
     *
     * @param stockinQaOrderId
     */
    @Transactional
    public void backToIncomplete(Integer stockinQaOrderId) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(stockinQaOrderId);
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        if (!StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name().equals(stockinQaOrderEntity.getProcessStatus())
            && !StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name().equals(stockinQaOrderEntity.getProcessStatus())
            && !StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("只允许质检初审、复审、稽查状态的质检单返回未完成！");
        }
        //先处理
        if (Lists.newArrayList(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.name(), StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name()).contains(stockinQaOrderEntity.getProcessStatus())) {
            //取消工作流
            StockinQaOrderAuditDTO stockinQaOrderAuditDTO = new StockinQaOrderAuditDTO(FlowTaskQaOperationEnum.DELETE, stockinQaOrderEntity.getStockinQaOrderId(), loginInfoService.isAdmin(), loginInfoService.getUserName());
            messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC,
                    Key.of(TenantContext.getTenant() + "_" + stockinQaOrderAuditDTO.getQaOrderId()),
                    new LocationWrapperMessage(TenantContext.getTenant(), loginInfoService.getName(), stockinQaOrderAuditDTO));
        }
        //如果是待稽查返回未完成则修改为稽查完成,即稽查任务不删除
        if (StringUtils.hasText(stockinQaOrderEntity.getInspectStatus()) && StockinQaInspectStatusEnum.WAIT_INSPECT.name().equals(stockinQaOrderEntity.getInspectStatus())) {
            stockinQaOrderEntity.setInspectCompleteDate(new Date());
            stockinQaOrderEntity.setInspectStatus(StockinQaInspectStatusEnum.INSPECT_COMPLETE.name());
        }
        
        //返回未完成
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.INCOMPLETE);

        //记录日志
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.BACK_TO_INCOMPLETE,
            String.format("%s将质检单返回未完成状态", loginInfoService.getName()));
    }

    /**
     * 重新校验是否需要称重,量高度
     * ①若存在同款有其他颜色且颜色小于等于2个,则其中一个色的相同尺码要有重量才则赋值
     * ②若存在同款其他颜色且颜色大于2个，则其中两个颜色相同尺码的要有重量才则赋值
     *
     * @param qaTaskInfo
     */
    public void checkSkuNeedWeightAndNeedHeight(StockinWaitQaTaskPageResponse qaTaskInfo) {
        try {
            // 判断是否称重、量高度
            if (qaTaskInfo.getNeedWeight() == 0 && qaTaskInfo.getNeedHeight() == 0) {
                return;
            }
            //同款同尺码不同色商品
            List<ProductSpecInfoEntity> specInfoList = specInfoService.getSimilarityProduct(qaTaskInfo.getProductId(), qaTaskInfo.getSize());
            //无数据不做处理
            if (CollectionUtils.isEmpty(specInfoList)) {
                return;
            }
            //需要称重
            if (qaTaskInfo.getNeedWeight() == 1) {
                List<ProductSpecInfoEntity> weightList = specInfoList.stream().filter(specInfo -> specInfo.getActualWeight() != null && specInfo.getActualWeight().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(weightList) && (weightList.size() >= 2 || specInfoList.size() == 2)) {
                    qaTaskInfo.setNeedWeight(0);
                    this.updateSkuSizeInfo(qaTaskInfo.getSku(), specInfoList, weightList, ProductEditEnum.PRODUCT_EDIT_WEIGHT.getChangeLogType());
                }
            }
            //需要量高度
            if (qaTaskInfo.getNeedHeight() == 1) {
                List<ProductSpecInfoEntity> heightList = specInfoList.stream().filter(specInfo -> specInfo.getPackageHeight() != null && specInfo.getPackageHeight().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(heightList) && (heightList.size() >= 2 || specInfoList.size() == 2)) {
                    qaTaskInfo.setNeedHeight(0);
                    this.updateSkuSizeInfo(qaTaskInfo.getSku(), specInfoList, heightList, ProductEditEnum.PRODUCT_EDIT_HEIGHT.getChangeLogType());
                }
            }
        } catch (Exception e) {
            LOGGER.error("修改同款不同色测量数据失败", e);
        }
    }

    /**
     * 根据同款不同颜色称重高度信息平均值修改未称重高度数据
     *
     * @param sku
     * @param productSpecInfoList
     * @param filterList
     * @param productEditType
     */
    private void updateSkuSizeInfo(String sku, List<ProductSpecInfoEntity> productSpecInfoList, List<ProductSpecInfoEntity> filterList, String productEditType) {
        try {
            //无sku信息时或存在尺码的sku在时不做处理
            if (CollUtil.isEmpty(productSpecInfoList) || CollUtil.isEmpty(filterList) || !StringUtils.hasText(sku)) {
                return;
            }
            //找出需要更新的数据
            ProductSpecInfoEntity specInfo = productSpecInfoList.stream()
                .filter(detail -> sku.equalsIgnoreCase(detail.getSku()) && isMatchingEditType(productEditType, detail))
                .findFirst()
                .orElse(null);
            //不符合条件则不做处理，即当前sku对应的数据存在重量或高度
            if (Objects.isNull(specInfo)) {
                return;
            }
            //修改高度重量信息
            if (ProductEditEnum.PRODUCT_EDIT_HEIGHT.getChangeLogType().equalsIgnoreCase(productEditType)) {
                specInfoService.editHeight(specInfo.getProductSpecInfoId(), filterList.get(0).getPackageHeight());
            } else if (ProductEditEnum.PRODUCT_EDIT_WEIGHT.getChangeLogType().equalsIgnoreCase(productEditType)) {
                //如果么有实际重量则赋值预估重量
                specInfoService.editWeightAndAuctualWeight(specInfo.getProductSpecInfoId(), filterList.get(0).getWeight(), filterList.get(0).getActualWeight());
            }
        } catch (Exception e) {
            LOGGER.error("修改高度/重量信息失败", e);
        }
    }


    private boolean isMatchingEditType(String productEditType, ProductSpecInfoEntity detail) {
        if (ProductEditEnum.PRODUCT_EDIT_HEIGHT.getChangeLogType().equalsIgnoreCase(productEditType)) {
            return Objects.isNull(detail.getPackageHeight());
        }
        if (ProductEditEnum.PRODUCT_EDIT_WEIGHT.getChangeLogType().equalsIgnoreCase(productEditType)) {
            return Objects.isNull(detail.getActualWeight());
        }
        return false;
    }

    /**
     * 初审退货，如果进入稽查初审有退货则需要进行退货
     *
     * @param stockinQaOrderEntity
     * @param returnCount
     */
    private void firstAuditReturn(StockinQaOrderEntity stockinQaOrderEntity, Integer returnCount) {
        //不存在退货数则不处理
        if (Optional.ofNullable(returnCount).orElse(0) == 0) {
            return;
        }
        //质检明细
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        //修改直接退货数为请求的退货数+直接退货数
        Integer directReturnCount = returnCount + (Objects.isNull(detailEntity.getDirectReturnCount()) ? 0 : detailEntity.getDirectReturnCount());
        stockinQaOrderDetailService.updateSetDirectReturnCount(stockinQaOrderEntity.getStockinQaOrderId(), directReturnCount);
        //如果有退货数直接退货
        QcInboundsMessage qcInboundsMessage = buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), returnCount);
        stockinQcService.addReturnQuantity(qcInboundsMessage, false);
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.FIRST_AUDIT,
                String.format("【%s】操作质检流程【%s】退货%s件", loginInfoService.getName(), QaLogTypeEnum.FIRST_AUDIT.getValue(), returnCount));
    }

    @Transactional(rollbackFor = Exception.class)
    public void punishments(StockinQaPunishmentsRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getBaseMapper().getByIdPermission(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity)) {
            throw new BusinessServiceException("未找到质检单或无权查看");
        }
        if (stockinQaOrderLogService.existPunishments(request.getStockinQaOrderId())) {
            throw new BusinessServiceException("此质检单已发起过罚款惩罚无法重复发起请检查!");
        }
        StockinQaOrderDetailEntity stockinQaOrderDetail = stockinQaOrderDetailService.findTopByStockinQaOrderId(request.getStockinQaOrderId());
        if (request.getRewardAndPunishmentsCount() > stockinQaOrderDetail.getBoxQty()) {
            throw new BusinessServiceException("罚款件数不能大于箱内数!");
        }
        QaPunishmentsRequest qaPunishmentsRequest = new QaPunishmentsRequest();
        qaPunishmentsRequest.setSpu(stockinQaOrderEntity.getSpu());
        qaPunishmentsRequest.setSupplierId(stockinQaOrderEntity.getSupplierId());
        qaPunishmentsRequest.setRewardAndPunishmentsDate(request.getRewardAndPunishmentsDate());
        if (StringUtils.hasText(request.getRewardAndPunishmentsReason())) {
            qaPunishmentsRequest.setRewardAndPunishmentsReason(String.format("其他:%s", request.getRewardAndPunishmentsReason()));
        }
        qaPunishmentsRequest.setRewardAndPunishmentsCount(request.getRewardAndPunishmentsCount());
        qaPunishmentsRequest.setRewardAndPunishmentsType("其他");
        qaPunishmentsRequest.setConfirmTotalMoney(request.getConfirmTotalMoney());
        if (StringUtils.hasText(request.getRemark())) {
            qaPunishmentsRequest.setRemark(String.format("%s:%s", stockinQaOrderEntity.getSku(), request.getRemark()));
        }
        List<StockinQaOrderImgEntity> imageList = stockinQaOrderImgService.getStockinQaOrderImage(request.getStockinQaOrderId());
        if (!CollectionUtils.isEmpty(imageList)) {
            qaPunishmentsRequest.setRewardsAndPunishmentsAttaches(imageList.stream().map(item -> {
                QaPunishmentsAttachRequest attachRequest = new QaPunishmentsAttachRequest();
                attachRequest.setUrl(item.getImgUrl());
                attachRequest.setOriginName(item.getImgUrl().substring(item.getImgUrl().lastIndexOf('/') + 1));
                return attachRequest;
            }).collect(Collectors.toList()));
        }
        supplierApiService.qaPunishments(qaPunishmentsRequest);
        stockinQaOrderLogService.addLog(request.getStockinQaOrderId(), QaLogTypeEnum.PUNISHMENTS,
                String.format("%s发起罚款件数：%s件,罚款金额：%s,罚款原因：%s", loginInfoService.getName(), request.getRewardAndPunishmentsCount(),
                        request.getConfirmTotalMoney(), request.getRewardAndPunishmentsReason()));
    }
}
