package com.nsy.wms.business.service.stockout;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.LogisticsBatchStatusEnum;
import com.nsy.api.wms.enumeration.stockout.LogisticsDifferenceTypeEnum;
import com.nsy.api.wms.enumeration.stockout.PackageBillFinancialPushStatusEnum;
import com.nsy.api.wms.enumeration.stockout.PackageBillReviewResultEnum;
import com.nsy.api.wms.request.stockout.ChannelForwarderPriceRecordExistRequest;
import com.nsy.api.wms.request.stockout.StockoutLogisticsBatchInternalCreateRequest;
import com.nsy.api.wms.request.stockout.StockoutLogisticsBatchPageRequest;
import com.nsy.api.wms.request.stockout.StockoutLogisticsBatchUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.ChannelForwarderPriceRecordResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchPageResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutLogisticsBatchSumResponse;
import com.nsy.wms.business.manage.amazon.response.AmazonFulfillmentCenterVO;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.manage.user.upload.StockoutLogisticsBatchImport;
import com.nsy.wms.business.manage.user.upload.StockoutLogisticsBatchPriceImport;
import com.nsy.wms.business.manage.user.upload.StockoutLogisticsBatchShipmentImport;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutLogisticsBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutLogisticsBatchFeeDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutLogisticsBatchShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutLogisticsBatchMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * FBA出库批次(StockoutLogisticsBatch)服务层
 *
 * <AUTHOR>
 * @since 2023-03-27 09:50:21
 */
@Service
public class StockoutLogisticsBatchService extends ServiceImpl<StockoutLogisticsBatchMapper, StockoutLogisticsBatchEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutLogisticsBatchService.class);

    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutLogisticsBatchShipmentService batchShipmentService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    TmsApiService tmsApiService;
    @Resource
    TmsCacheService tmsCacheService;
    @Resource
    StockoutShipmentAmazonRelationService amazonRelationService;
    @Resource
    StockoutShipmentService shipmentService;
    @Resource
    StockoutLogisticsBatchLogService logisticsBatchLogService;
    @Resource
    StockoutShipmentItemService shipmentItemService;
    @Resource
    StockoutLogisticsBatchFeeDocumentService documentService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Resource
    StockoutReceiverInfoService stockoutReceiverInfoService;

    /**
     * 分页查询
     */
    public PageResponse<StockoutLogisticsBatchPageResponse> queryByPage(StockoutLogisticsBatchPageRequest request) {
        PageResponse<StockoutLogisticsBatchPageResponse> pageResponse = new PageResponse<>();
        Page<StockoutLogisticsBatchEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<String> iPage = this.getBaseMapper().pageSearchList(page, request);
        List<StockoutLogisticsBatchEntity> logisticsBatchEntities = listByLogisticsBatch(iPage.getRecords());
        Map<String, StockoutLogisticsBatchEntity> entityMap = logisticsBatchEntities.stream().collect(Collectors.toMap(StockoutLogisticsBatchEntity::getLogisticsBatch, item -> item, (k1, k2) -> k1));
        List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList = batchShipmentService.listByLogisticsBatch(iPage.getRecords());
        Map<String, List<StockoutLogisticsBatchShipmentEntity>> batchShipmentMap = batchShipmentEntityList.stream().collect(Collectors.groupingBy(StockoutLogisticsBatchShipmentEntity::getLogisticsBatch));
        Map<String, String> stockoutPlatformNameEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_ORDER_PLATFORM_NAME.getName());
        List<StockoutLogisticsBatchPageResponse> list = iPage.getRecords().stream().map(batch -> {
            StockoutLogisticsBatchPageResponse resp = new StockoutLogisticsBatchPageResponse();
            StockoutLogisticsBatchEntity entity = entityMap.get(batch);
            BeanUtils.copyProperties(entity, resp);
            resp.setStatusStr(LogisticsBatchStatusEnum.getEnumByDescOrName(entity.getStatus()).getDesc());
            resp.setPredictPrice(entity.getPredictUnitPrice());
            if (entity.getReviewResult() != null) {
                resp.setReviewResultCn(PackageBillReviewResultEnum.resolveByValue(entity.getReviewResult()).getDesc());
            }
            resp.setFinancialPushStatusCn(PackageBillFinancialPushStatusEnum.resolveByValue(entity.getFinancialPushStatus()).getDesc());
            if (entity.getDifferenceType() != null) {
                resp.setDifferenceTypeStr(LogisticsDifferenceTypeEnum.getEnumByCode(entity.getDifferenceType()).getDesc());
            }
            List<StockoutLogisticsBatchShipmentEntity> batchShipments = batchShipmentMap.get(batch);
            if (!CollectionUtils.isEmpty(batchShipments)) {
                resp.setShipmentList(batchShipments.stream().map(en -> {
                    StockoutLogisticsBatchShipmentResponse shipmentResponse = new StockoutLogisticsBatchShipmentResponse();
                    BeanUtilsEx.copyProperties(en, shipmentResponse);
                    shipmentResponse.setPredictPrice(en.getPredictTotalPrice());
                    shipmentResponse.setPlatformNameCn(StrUtil.isEmpty(shipmentResponse.getPlatformName()) ? "" : stockoutPlatformNameEnumMap.get(shipmentResponse.getPlatformName()));
                    return shipmentResponse;
                }).collect(Collectors.toList()));
            } else {
                resp.setShipmentList(new ArrayList<>());
            }
            if (entity.getCheckDocumentId() != null) {
                StockoutLogisticsBatchFeeDocumentEntity document = documentService.getById(entity.getCheckDocumentId());
                if (document != null) {
                    resp.setCheckDocumentName(document.getDocumentName());
                }
            }
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }


    public StockoutLogisticsBatchSumResponse statistics(StockoutLogisticsBatchPageRequest pageRequest) {
        StockoutLogisticsBatchSumResponse stockoutLogisticsBatchSumResponse = this.getBaseMapper().countSearchList(pageRequest);
        stockoutLogisticsBatchSumResponse.setStoreRealPrice(NumberUtil.add(stockoutLogisticsBatchSumResponse.getRealPrice(), stockoutLogisticsBatchSumResponse.getRealDeclarePrice(), stockoutLogisticsBatchSumResponse.getRealTax()));
        return stockoutLogisticsBatchSumResponse;
    }

    private List<StockoutLogisticsBatchEntity> listByLogisticsBatch(List<String> logisticsBatch) {
        if (CollectionUtils.isEmpty(logisticsBatch)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StockoutLogisticsBatchEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutLogisticsBatchEntity::getLogisticsBatch, logisticsBatch);
        return list(wrapper);
    }

    private List<StockoutLogisticsBatchEntity> listByLogisticsBatchAndFbaShipmentId(String logisticsBatch, String fbaShipmentId) {
        StockoutLogisticsBatchShipmentEntity entity = batchShipmentService.getOneByLogisticsBatchAndFbaShipmentId(logisticsBatch, fbaShipmentId);
        if (entity == null) {
            throw new BusinessServiceException("找不到对应的发货批次！");
        }
        LambdaQueryWrapper<StockoutLogisticsBatchEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutLogisticsBatchEntity::getLogisticsBatch, entity.getLogisticsBatch());
        return list(wrapper);
    }

    public StockoutLogisticsBatchResponse getOneById(Integer id) {
        StockoutLogisticsBatchEntity entity = getById(id);
        StockoutLogisticsBatchResponse resp = new StockoutLogisticsBatchResponse();
        BeanUtils.copyProperties(entity, resp);
        return resp;
    }


    // 修改
    @Transactional
    public void update(StockoutLogisticsBatchUpdateRequest updateRequest) {
        StockoutLogisticsBatchEntity entity = getById(updateRequest.getId());
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        if (entity.getCheckDocumentId() == null) {
            throw new BusinessServiceException("未上传核对文件，不允许修改！");
        }
        if (updateRequest.getRealDeclarePrice() != null) {
            logisticsBatchLogService.addLog(entity, "修改", StrUtil.format("【报关单价】由{}改成{}", entity.getRealDeclarePrice() == null ? "空" : String.valueOf(entity.getRealDeclarePrice()), updateRequest.getRealDeclarePrice()), updateRequest.getMemo());
            entity.setRealDeclarePrice(updateRequest.getRealDeclarePrice());
        }
        if (updateRequest.getRealPrice() != null) {
            logisticsBatchLogService.addLog(entity, "修改", StrUtil.format("【实际物流费用】由{}改成{}", entity.getRealPrice() == null ? "空" : String.valueOf(entity.getRealPrice()), updateRequest.getRealPrice()), updateRequest.getMemo());
            entity.setRealPrice(updateRequest.getRealPrice());
        }
        if (updateRequest.getRealTax() != null) {
            logisticsBatchLogService.addLog(entity, "修改", StrUtil.format("【税费】由{}改成{}", entity.getRealTax() == null ? "空" : String.valueOf(entity.getRealTax()), updateRequest.getRealTax()), updateRequest.getMemo());
            entity.setRealTax(updateRequest.getRealTax());
        }
        if (updateRequest.getRealUnitPrice() != null) {
            logisticsBatchLogService.addLog(entity, "修改", StrUtil.format("【实际物流单价】由{}改成{}", entity.getRealUnitPrice() == null ? "空" : String.valueOf(entity.getRealUnitPrice()), updateRequest.getRealUnitPrice()), updateRequest.getMemo());
            entity.setRealUnitPrice(updateRequest.getRealUnitPrice());
        }
        if (updateRequest.getRealWeight() != null) {
            logisticsBatchLogService.addLog(entity, "修改", StrUtil.format("【实际重量】由{}改成{}", entity.getRealWeight() == null ? "空" : String.valueOf(entity.getRealWeight()), updateRequest.getRealWeight()), updateRequest.getMemo());
            entity.setRealWeight(updateRequest.getRealWeight());
        }
        if (StringUtils.hasText(updateRequest.getMemoDocumentUrl())) {
            entity.setMemoDocumentUrl(updateRequest.getMemoDocumentUrl());
        }
        entity.setUpdateBy(loginInfoService.getName());
        updateById(entity);
        startCheckBatch(entity, "修改数据", true);
    }

    /**
     * 创建物流核对波次
     * 1、是否需要核对
     * 2、创建物流批次
     * 3、初始化数据
     */
    @Transactional
    public void createBatch(StockoutShipmentConfirmUpdateRequest request) {
        if (!StringUtils.hasText(request.getLogisticsNo()))
            return;
        // 判断物流公司，是否加入费用核对
        // 查询所有数据
        TenantContext.setDisableTenant(true);
        List<StockoutShipmentEntity> byShipmentIdsList = shipmentService.findByShipmentIdsList(request.getShipmentIds());
        TenantContext.setDisableTenant(false);
        List<String> companyList = byShipmentIdsList.stream().map(StockoutShipmentEntity::getLogisticsCompany)
                .filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(companyList))
            return;
        TenantContext.setDisableTenant(true);
        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findByShipmentIdList(request.getShipmentIds());
        TenantContext.setDisableTenant(false);
        if (CollectionUtils.isEmpty(shipmentItemList))
            return;
        List<TmsLogisticsCompany> allLogisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList();
        List<TmsLogisticsCompany> companyEntityList = allLogisticsCompanyList.stream().filter(i -> companyList.contains(i.getLogisticsCompany())).collect(Collectors.toList());
        Set<String> needCreateBatchLogisticsCompanySet = companyEntityList.stream().filter(i -> Objects.equals(1, i.getVerifyPrice()))
                .map(TmsLogisticsCompany::getLogisticsCompany).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(needCreateBatchLogisticsCompanySet)) {
            return;
        }
        // 把shipmentId重置为需要核对的shipmentId
        request.setShipmentIds(byShipmentIdsList.stream().filter(i -> needCreateBatchLogisticsCompanySet.contains(i.getLogisticsCompany()))
                .map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList()));
        // 查询结束
        TenantContext.setDisableTenant(false);
        // 创建物流批次
        StockoutLogisticsBatchEntity entity = buildBatch(request);
        // 创建批次Shipment、回填批次物流公司/仓库id
        buildBatchShipment(entity, request);
        // 重新核对批次数据
        initBatch(entity);
    }

    /**
     * 初步计算-只计算预估相关的
     * 1、计算该批次的预估单价、总重量、预估总价
     * 2、计算批次的预估总价格
     * <AUTHOR>
     * 2023-03-28
     */
    private void initBatch(StockoutLogisticsBatchEntity entity) {
        List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList = batchShipmentService.listByLogisticsBatch(Collections.singletonList(entity.getLogisticsBatch()));
        if (CollectionUtils.isEmpty(batchShipmentEntityList))
            return;
        entity.setPredictWeight(BigDecimal.ZERO);
        batchShipmentEntityList.forEach(it -> {
            entity.setPredictWeight(it.getWeight().add(entity.getPredictWeight()));
            it.setUpdateBy(loginInfoService.getName());
        });
        String postCode = null;
        String countryCode = null;
        AmazonFulfillmentCenterVO amazonCenterIdAddress = amazonRelationService.findAmazonCenterIdAddress(entity.getDestinationFulfillmentCenterId());
        if (amazonCenterIdAddress != null && StringUtils.hasText(amazonCenterIdAddress.getAddressOwnershipName())) {
            postCode = amazonCenterIdAddress.getPostalCode();
            countryCode = amazonCenterIdAddress.getCountryCode();
        }
        if (StrUtil.isBlank(countryCode) && StringUtils.hasText(batchShipmentEntityList.get(0).getOrderNo())) {
            TenantContext.setDisableTenant(true);
            List<StockoutOrderItemEntity> listByOrderNo = stockoutOrderItemService.getListByOrderNo(batchShipmentEntityList.get(0).getOrderNo());
            StockoutReceiverInfo receiverInfo = stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(listByOrderNo.get(0).getStockoutOrderId());
            TenantContext.setDisableTenant(false);
            countryCode = receiverInfo.getCountryCode();
            postCode = receiverInfo.getReceiverZip();
        }
        String priceChangeStr = "";
        // 预估价格
        ChannelForwarderPriceRecordResponse recordResponse = getChannelForwarderPriceRecord(entity, batchShipmentEntityList.get(0), countryCode, postCode, entity.getPredictWeight());
        if (recordResponse != null && recordResponse.getUnitPrice() != null && entity.getPredictUnitPrice() == null)
            priceChangeStr = "获取预估单价：" + recordResponse.getUnitPrice();
        if (recordResponse != null && entity.getPredictUnitPrice() != null && recordResponse.getUnitPrice() != null && entity.getPredictUnitPrice().compareTo(recordResponse.getUnitPrice()) != 0)
            priceChangeStr = "重新获取预估单价：从" + entity.getPredictUnitPrice() + "改为" + recordResponse.getUnitPrice();
        if (recordResponse != null && recordResponse.getPrice() != null) {
            buildPrice(entity, batchShipmentEntityList, recordResponse);
        }
        if (recordResponse != null && recordResponse.getChargeWeight() != null)
            entity.setChargeWeight(recordResponse.getChargeWeight());
        if (recordResponse != null && StrUtil.isNotBlank(recordResponse.getErrorStr()))
            priceChangeStr = recordResponse.getErrorStr();
        // 设置总价格
        if (entity.getPredictUnitPrice() == null) {
            entity.setStatus(LogisticsBatchStatusEnum.EXCEPTION.name());
        } else if (StrUtil.equals(entity.getStatus(), LogisticsBatchStatusEnum.EXCEPTION.name())) {
            entity.setStatus(LogisticsBatchStatusEnum.WAIT_DEAL.name());
        }
        batchShipmentService.updateBatchById(batchShipmentEntityList);
        entity.setUpdateBy(loginInfoService.getName());
        updateById(entity);
        if (StringUtils.hasText(priceChangeStr))
            logisticsBatchLogService.addLog(entity, "预估单价", priceChangeStr, "刷新预估单价");
    }

    private void buildPrice(StockoutLogisticsBatchEntity entity, List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList, ChannelForwarderPriceRecordResponse recordResponse) {
        entity.setPredictPriceId(recordResponse.getId());
        entity.setPredictUnitPrice(recordResponse.getUnitPrice());
        entity.setExactDay(Boolean.TRUE);
        // 设置每个shipment预估价格
        batchShipmentEntityList.forEach(it -> it.setPredictTotalPrice(entity.getPredictUnitPrice().multiply(it.getWeight())));
        entity.setPredictTotalPrice(recordResponse.getPrice());
    }

    // 设置一些固定的值
    private StockoutLogisticsBatchEntity buildBatch(StockoutShipmentConfirmUpdateRequest request) {
        List<StockoutLogisticsBatchEntity> stockoutLogisticsBatchEntities = listByLogisticsBatch(Collections.singletonList(request.getLogisticsNo()));
        if (CollectionUtils.isEmpty(stockoutLogisticsBatchEntities)) {
            StockoutLogisticsBatchEntity entity = new StockoutLogisticsBatchEntity();
            entity.setLogisticsBatch(request.getLogisticsNo());
            entity.setStatus(LogisticsBatchStatusEnum.WAIT_DEAL.name());
            entity.setDifferenceType(LogisticsDifferenceTypeEnum.WAIT_START.getCode());
            entity.setCreateBy(loginInfoService.getName());
            TenantContext.setDisableTenant(true);
            List<StockoutLogisticsBatchEntity> referenceList = listByLogisticsBatch(Collections.singletonList(request.getLogisticsNo()));
            if (!CollectionUtils.isEmpty(referenceList)) {
                entity.setReferenceId(referenceList.get(0).getId());
            }
            TenantContext.setDisableTenant(false);
            save(entity);
            logisticsBatchLogService.addLog(entity, "创建", "批次发货创建", "批次发货创建");
            return entity;
        } else {
            logisticsBatchLogService.addLog(stockoutLogisticsBatchEntities.get(0), "再次发货", "该批次再次被发货", "先前发货后，再次输入此批次进行发货");
            return stockoutLogisticsBatchEntities.get(0);
        }

    }


    // 费用核对明细
    private void buildBatchShipment(StockoutLogisticsBatchEntity entity, StockoutShipmentConfirmUpdateRequest request) {
        TenantContext.setDisableTenant(true);
        // 插入FBA 核对
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = amazonRelationService.listByShipmentId(request.getShipmentIds());
        amazonRelationEntities = CollectionUtils.isEmpty(amazonRelationEntities) ? Collections.emptyList() : amazonRelationService.listByFbaShipmentId(amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getFbaShipmentId).collect(Collectors.toList()));
        List<StockoutShipmentEntity> shipmentFBA = CollectionUtils.isEmpty(amazonRelationEntities) ? Collections.emptyList() : shipmentService.listByIds(amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).collect(Collectors.toList()));
        Map<String, List<StockoutShipmentAmazonRelationEntity>> fbaShipmentMap = amazonRelationEntities.stream().filter(it -> StringUtils.hasText(it.getFbaShipmentId())).collect(Collectors.groupingBy(StockoutShipmentAmazonRelationEntity::getFbaShipmentId));
        Set<Integer> fbaShipmentIdSet = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).collect(Collectors.toSet());
        TenantContext.setDisableTenant(false);
        fbaShipmentMap.forEach((fbaShipmentId, amazonList) -> buildFbaCheck(entity, request, shipmentFBA, fbaShipmentId, amazonList));


        // 插入普通货代核对
        List<Integer> normalShipmentIds = request.getShipmentIds().stream().filter(it -> !fbaShipmentIdSet.contains(it)).collect(Collectors.toList());
        TenantContext.setDisableTenant(true);
        List<StockoutShipmentItemEntity> shipmentItems = CollectionUtils.isEmpty(normalShipmentIds) ? Collections.emptyList() : shipmentItemService.findByShipmentIdList(normalShipmentIds);
        Map<String, Set<Integer>> normalShipmentMap = shipmentItems.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getOrderNo, Collectors.mapping(StockoutShipmentItemEntity::getShipmentId, Collectors.toSet())));
        Map<Integer, List<BigDecimal>> shipmentWeightListMap = getShipmentWeightListMap(normalShipmentIds, shipmentItems);
        normalShipmentMap.forEach((orderNo, shipmentIdSet) -> {
            TenantContext.setDisableTenant(false);
            StockoutLogisticsBatchShipmentEntity shipmentEntity = batchShipmentService.getOneByLogisticsBatchAndOrderNo(entity.getLogisticsBatch(), orderNo);
            if (shipmentEntity != null) {
                batchShipmentService.removeById(shipmentEntity.getId());
                logisticsBatchLogService.addLog(entity, "删除已添加的订单Shipment", orderNo + "此订单分多次发货，删除之前的统计记录", "订单批次发货操作");
            }
            StockoutLogisticsBatchShipmentEntity batchShipmentEntity = buildStockoutLogisticsBatchShipmentEntity(entity, null, orderNo);
            TenantContext.setDisableTenant(true);
            List<StockoutShipmentEntity> shipmentList = shipmentService.findByLogisticsNo(entity.getLogisticsBatch());
            // 箱子跟单据混装
            buildEachShipmentWeight(shipmentWeightListMap, batchShipmentEntity, shipmentList);
            batchShipmentEntity.setDeliveryDate(request.getDeliveryDate() == null ? new Date() : request.getDeliveryDate());
            batchShipmentEntity.setCreateBy(loginInfoService.getName());
            // 查询之前是否有没有明细
            StockoutLogisticsBatchShipmentEntity referenceEntity = batchShipmentService.getOneByLogisticsBatchAndOrderNo(entity.getLogisticsBatch(), orderNo);
            if (referenceEntity != null) {
                batchShipmentEntity.setReferenceId(referenceEntity.getId());
            }
            TenantContext.setDisableTenant(false);
            batchShipmentService.save(batchShipmentEntity);
            if (!StringUtils.hasText(entity.getLogisticsCompany())) {
                entity.setLogisticsCompany(shipmentList.get(0).getLogisticsCompany());
            }
            updateById(entity);
            logisticsBatchLogService.addLog(entity, "普通订单发货",
                    orderNo + "操作发货，箱子id：" + shipmentIdSet.stream().map(String::valueOf)
                            .collect(Collectors.joining(",")), "普通批次发货操作");
        });
        TenantContext.setDisableTenant(false);
    }

    private void buildEachShipmentWeight(Map<Integer, List<BigDecimal>> shipmentWeightListMap, StockoutLogisticsBatchShipmentEntity batchShipmentEntity, List<StockoutShipmentEntity> shipmentList) {
        batchShipmentEntity.setWeight(NumberUtil.add(shipmentList.stream().map(it -> {
            if (it.getWeight() == null) {
                return BigDecimal.ZERO;
            }
            List<BigDecimal> weights = shipmentWeightListMap.get(it.getShipmentId());
            if (weights == null) {
                return it.getWeight();
            } else {
                BigDecimal decimal = weights.get(0);
                weights.remove(0);
                return decimal;
            }
        }).toArray(BigDecimal[]::new)));
    }

    private Map<Integer, List<BigDecimal>> getShipmentWeightListMap(List<Integer> normalShipmentIds, List<StockoutShipmentItemEntity> shipmentItems) {
        if (CollectionUtils.isEmpty(normalShipmentIds) || CollectionUtils.isEmpty(shipmentItems)) {
            return new HashMap<>();
        }
        List<StockoutShipmentEntity> byShipmentIdsList = shipmentService.findByShipmentIdsList(normalShipmentIds);
        Map<Integer, BigDecimal> shipmentWeight = new HashMap<>();
        byShipmentIdsList.forEach(shipment -> {
            shipmentWeight.put(shipment.getShipmentId(), shipment.getWeight() == null ? BigDecimal.ZERO : shipment.getWeight());
        });
        Map<Integer, List<BigDecimal>> shipmentWeightListMap = new HashMap<>();
        // 统计每个箱子的订单数
        Map<Integer, Integer> collect = shipmentItems.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId, Collectors.mapping(
                StockoutShipmentItemEntity::getOrderNo,
                Collectors.collectingAndThen(Collectors.toSet(), Set::size)
        )));
        collect.forEach((k, v) -> {
            if (v > 1) {
                // 获取箱子总重量
                BigDecimal totalWeight = shipmentWeight.get(k);
                if (totalWeight == null || totalWeight.compareTo(BigDecimal.ZERO) == 0) {
                    return;
                }
                // 计算每个订单分配的重量(保留3位小数)
                BigDecimal perOrderWeight = totalWeight.divide(new BigDecimal(v), 2, BigDecimal.ROUND_DOWN);
                List<BigDecimal> weightList = new ArrayList<>(v);
                // 前v-1个订单平均分配
                for (int i = 0; i < v - 1; i++) {
                    weightList.add(perOrderWeight);
                }
                // 最后一个订单分配剩余重量
                weightList.add(totalWeight.subtract(perOrderWeight.multiply(new BigDecimal(v - 1))));
                shipmentWeightListMap.put(k, weightList);
            }
        });
        return shipmentWeightListMap;
    }

    private void buildFbaCheck(StockoutLogisticsBatchEntity entity, StockoutShipmentConfirmUpdateRequest request, List<StockoutShipmentEntity> shipmentFBA, String fbaShipmentId, List<StockoutShipmentAmazonRelationEntity> amazonList) {
        // 先查询之前有没有
        StockoutLogisticsBatchShipmentEntity shipmentEntity = batchShipmentService.getOneByLogisticsBatchAndFbaShipmentId(entity.getLogisticsBatch(), fbaShipmentId);
        if (shipmentEntity != null) {
            batchShipmentService.removeById(shipmentEntity.getId());
            logisticsBatchLogService.addLog(entity, "删除已添加的FbaShipment", fbaShipmentId + "此FBA Shipment 分多次发货，删除之前的统计记录", "FBA批次发货操作");
        }
        StockoutLogisticsBatchShipmentEntity batchShipmentEntity = buildStockoutLogisticsBatchShipmentEntity(entity, fbaShipmentId, amazonList.get(0).getErpTid());
        amazonList.forEach(amazon -> {
            StockoutShipmentEntity shipment = shipmentFBA.stream().filter(iEntity -> iEntity.getShipmentId().equals(amazon.getShipmentId())
                    && StrUtil.equals(iEntity.getLogisticsNo(), entity.getLogisticsBatch())).findFirst().orElse(new StockoutShipmentEntity());
            batchShipmentEntity.setWeight(batchShipmentEntity.getWeight().add(shipment.getWeight() == null ? BigDecimal.ZERO : shipment.getWeight()));
            if (StringUtils.hasText(amazon.getDestinationFulfillmentCenterId())) {
                entity.setDestinationFulfillmentCenterId(amazon.getDestinationFulfillmentCenterId());
            }
            if (StringUtils.hasText(shipment.getLogisticsCompany()) && StrUtil.equals(shipment.getLocation(), LocationEnum.QUANZHOU.name())) {
                entity.setLogisticsCompany(shipment.getLogisticsCompany());
            }
        });
        if (StrUtil.isBlank(entity.getLogisticsCompany())) {
            entity.setLogisticsCompany(shipmentFBA.get(0).getLogisticsCompany());
        }
        batchShipmentEntity.setDeliveryDate(request.getDeliveryDate() == null ? new Date() : request.getDeliveryDate());
        batchShipmentEntity.setCreateBy(loginInfoService.getName());
        TenantContext.setDisableTenant(true);
        StockoutLogisticsBatchShipmentEntity referenceEntity = batchShipmentService.getOneByLogisticsBatchAndFbaShipmentId(entity.getLogisticsBatch(), fbaShipmentId);
        if (referenceEntity != null) {
            batchShipmentEntity.setReferenceId(referenceEntity.getId());
        }
        TenantContext.setDisableTenant(false);
        batchShipmentService.save(batchShipmentEntity);
        updateById(entity);
        logisticsBatchLogService.addLog(entity, "Shipment发货", fbaShipmentId + "操作发货", "FBA批次发货操作");
    }

    private StockoutLogisticsBatchShipmentEntity buildStockoutLogisticsBatchShipmentEntity(StockoutLogisticsBatchEntity entity, String fbaShipmentId, String orderNo) {
        StockoutLogisticsBatchShipmentEntity batchShipmentEntity = new StockoutLogisticsBatchShipmentEntity();
        batchShipmentEntity.setLogisticsBatchId(entity.getId());
        batchShipmentEntity.setLogisticsBatch(entity.getLogisticsBatch());
        batchShipmentEntity.setFbaShipmentId(fbaShipmentId);
        StockoutOrderEntity topByOrderNo = stockoutOrderService.getBaseMapper().findTopByOrderNoOrShipmentId(orderNo, fbaShipmentId);
        if (topByOrderNo == null) {
            throw new BusinessServiceException("无法找到对应的出库单数据：" + orderNo);
        }
        batchShipmentEntity.setStoreId(topByOrderNo.getStoreId());
        batchShipmentEntity.setPlatformName(topByOrderNo.getPlatformName());
        batchShipmentEntity.setStoreName(topByOrderNo.getStoreName());
        batchShipmentEntity.setBusinessType(topByOrderNo.getBusinessType());
        batchShipmentEntity.setOrderNo(orderNo);
        batchShipmentEntity.setWeight(BigDecimal.ZERO);
        return batchShipmentEntity;
    }

    public void freshPrice(List<Integer> idList) {
        List<StockoutLogisticsBatchEntity> stockoutLogisticsBatchEntities = listByIds(idList);
        List<String> errorList = new ArrayList<>();
        stockoutLogisticsBatchEntities.forEach(entity -> {
            entity.setUpdateBy(loginInfoService.getName());
            initBatch(entity);
            if (entity.getPredictUnitPrice() == null) {
                errorList.add(entity.getLogisticsBatch() + "获取价格失败");
            } else {
                try {
                    startCheckBatch(entity, "重新获取价格", false);
                } catch (Exception e) {
                    if (StrUtil.contains(e.getMessage(), entity.getLogisticsBatch())) {
                        errorList.add("获取价格成功，触发核对失败：" + e.getMessage());
                    } else {
                        errorList.add(entity.getLogisticsBatch() + "获取价格成功，触发核对失败：" + e.getMessage());
                    }
                }
            }
            logisticsBatchLogService.addLog(entity, "重新获取价格", "重新获取价格", "重新获取价格");
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            throw new BusinessServiceException(String.join(";", errorList));
        }
    }

    private ChannelForwarderPriceRecordResponse getChannelForwarderPriceRecord(StockoutLogisticsBatchEntity entity, StockoutLogisticsBatchShipmentEntity batchShipment, String countryCode, String postCode, BigDecimal chargeWeight) {
        if (entity.getPredictPriceId() != null && entity.getPredictPriceId() == 0) {
            if (entity.getPredictUnitPrice() == null) {
                throw new BusinessServiceException("导入的单价不能为空！");
            }
            ChannelForwarderPriceRecordResponse recordResponse = new ChannelForwarderPriceRecordResponse();
            if (chargeWeight != null) {
                recordResponse.setPrice(entity.getPredictUnitPrice().multiply(chargeWeight));
                recordResponse.setChargeWeight(chargeWeight);
            }
            recordResponse.setId(0);
            recordResponse.setUnitPrice(entity.getPredictUnitPrice());
            return recordResponse;
        }
        if (!StringUtils.hasText(countryCode)) {
            ChannelForwarderPriceRecordResponse recordResponse = new ChannelForwarderPriceRecordResponse();
            recordResponse.setErrorStr("缺少发货国家");
            return recordResponse;
        }
        ChannelForwarderPriceRecordExistRequest recordExistRequest = new ChannelForwarderPriceRecordExistRequest();
        recordExistRequest.setLogisticsCompany(entity.getLogisticsCompany());
        recordExistRequest.setPriceDate(batchShipment.getDeliveryDate());
        recordExistRequest.setDestinationFulfillmentCenterId(entity.getDestinationFulfillmentCenterId());
        recordExistRequest.setPostCode(postCode);
        recordExistRequest.setCountryCode(countryCode);
        recordExistRequest.setPredictWeight(chargeWeight);
        return tmsApiService.recentPrice(recordExistRequest);
    }

    public void upload(StockoutLogisticsBatchImport row, UploadRequest request, StockoutLogisticsBatchFeeDocumentEntity documentEntity) {
        List<StockoutLogisticsBatchEntity> stockoutLogisticsBatchEntities;
        // 校验
        if ((row.getRealTaxPrice() != null || row.getRealCustomsPrice() != null) && !StrUtil.isNotBlank(row.getLogisticsBatch())) {
            throw new BusinessServiceException("有填写税费或报关费用时批次号必填");
        }
        if ((row.getRealTaxPrice() != null || row.getRealCustomsPrice() != null) && !StrUtil.isNotBlank(row.getFbaShipmentId())) {
            throw new BusinessServiceException("有填写税费或报关费用时ShipmentId必填");
        }
        // 先找上传记录，找不到上传记录就新增一条
        if (StrUtil.isAllNotBlank(row.getLogisticsBatch(), row.getFbaShipmentId())) {
            stockoutLogisticsBatchEntities = listByLogisticsBatchAndFbaShipmentId(row.getLogisticsBatch(), row.getFbaShipmentId());
        } else if (StrUtil.isNotBlank(row.getLogisticsBatch())) {
            stockoutLogisticsBatchEntities = listByLogisticsBatch(Collections.singletonList(row.getLogisticsBatch()));
        } else if (StrUtil.isNotBlank(row.getFbaShipmentId())) {
            StockoutLogisticsBatchShipmentEntity batchShipmentEntity = batchShipmentService.getOneByFbaShipmentId(row.getFbaShipmentId());
            if (batchShipmentEntity == null) {
                throw new BusinessServiceException(row.getFbaShipmentId() + "找不到批次数据");
            }
            stockoutLogisticsBatchEntities = listByLogisticsBatch(Collections.singletonList(batchShipmentEntity.getLogisticsBatch()));
        } else {
            throw new BusinessServiceException("批次号/ShipmentId必填");
        }
        if (CollectionUtils.isEmpty(stockoutLogisticsBatchEntities)) {
            throw new BusinessServiceException("找不到批次数据");
        }
        StockoutLogisticsBatchEntity batch = stockoutLogisticsBatchEntities.get(0);
        documentEntity.setLogisticsCompany(batch.getLogisticsCompany());
        documentService.updateById(documentEntity);
        logisticsBatchLogService.addLog(batch, "上传文件", "上传文件", "文件名称：" + request.getFileName());
        if (row.getRealWeight() == null) {
            throw new BusinessServiceException("重量必填");
        }
        if (row.getRealUnitPrice() == null) {
            throw new BusinessServiceException("单价必填");
        }
        if (row.getRealPrice() == null) {
            throw new BusinessServiceException("总价必填");
        }
        List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList = batchShipmentService.listByLogisticsBatch(Collections.singletonList(batch.getLogisticsBatch()));
        if (row.getRealTaxPrice() != null || row.getRealCustomsPrice() != null) {
            batch.setRealWeight(row.getRealWeight());
            batch.setRealUnitPrice(row.getRealUnitPrice());
            batch.setRealPrice(row.getRealPrice());
        } else if (batch.getCheckDocumentId() != null && batch.getCheckDocumentId().equals(documentEntity.getDocumentId())) {
            // 该次上传已经存在相同的批次，应该累加
            LOGGER.info("该次上传已经存在相同的批次，应该累加:{}", batch.getLogisticsBatch());
            batch.setRealWeight(NumberUtil.add(row.getRealWeight(), batch.getRealWeight()));
            batch.setRealUnitPrice(row.getRealUnitPrice());
            batch.setRealPrice(NumberUtil.add(row.getRealPrice(), batch.getRealPrice()));
        } else {
            // 应该去除之前的记录，重新登记
            batch.setRealWeight(row.getRealWeight());
            batch.setRealUnitPrice(row.getRealUnitPrice());
            batch.setRealPrice(row.getRealPrice());
        }
        batch.setRealDeclarePrice(BigDecimal.ZERO);
        batch.setRealTax(BigDecimal.ZERO);
        batchShipmentEntityList.forEach(batchShipmentEntity -> {
            buildShipmentInfo(row, batchShipmentEntity, batchShipmentEntityList, batch);
        });
        batchShipmentService.updateBatchById(batchShipmentEntityList);
        batch.setCheckDocumentId(documentEntity.getDocumentId());
        updateById(batch);
        startCheckBatch(batch, "上传", true);
    }

    private static void buildShipmentInfo(StockoutLogisticsBatchImport row, StockoutLogisticsBatchShipmentEntity batchShipmentEntity, List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList, StockoutLogisticsBatchEntity batch) {
        if (row.getRealTaxPrice() != null && StrUtil.equalsIgnoreCase(batchShipmentEntity.getFbaShipmentId(), row.getFbaShipmentId())) {
            batchShipmentEntity.setStoreRealTaxFee(row.getRealTaxPrice());
        }
        if (row.getRealCustomsPrice() != null && StrUtil.equalsIgnoreCase(batchShipmentEntity.getFbaShipmentId(), row.getFbaShipmentId())) {
            batchShipmentEntity.setStoreRealCustomsFee(row.getRealCustomsPrice());
        }
        // 店铺实际基础物流费用 = 如果是最后一票,则用实际物流费用减去前面几票的店铺基础物流费用之和,否则按重量比例计算
        BigDecimal storeRealBaseLogisticsFee;
        if (batchShipmentEntity.equals(batchShipmentEntityList.get(batchShipmentEntityList.size() - 1))) {
            BigDecimal sumPrevStoreRealBaseLogisticsFee = batchShipmentEntityList.subList(0, batchShipmentEntityList.size() - 1).stream().map(StockoutLogisticsBatchShipmentEntity::getStoreRealBaseLogisticsFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            storeRealBaseLogisticsFee = batch.getRealPrice().subtract(sumPrevStoreRealBaseLogisticsFee);
        } else {
            storeRealBaseLogisticsFee = batchShipmentEntity.getWeight().multiply(batch.getRealPrice()).divide(batch.getPredictWeight(), 2, RoundingMode.DOWN);
        }
        batchShipmentEntity.setStoreRealBaseLogisticsFee(storeRealBaseLogisticsFee);
        // 店铺实际物流总费用 = 店铺实际基础物流费用 + 店铺实际税费 + 店铺实际报关费用
        batchShipmentEntity.setStoreRealTotalLogisticsFee(NumberUtil.add(storeRealBaseLogisticsFee, batchShipmentEntity.getStoreRealCustomsFee(), batchShipmentEntity.getStoreRealTaxFee()));
        // 批次实际税费 = 店铺实际报关费累加
        batch.setRealDeclarePrice(NumberUtil.add(batch.getRealDeclarePrice(), batchShipmentEntity.getStoreRealCustomsFee()));
        // 批次实际税费 = 店铺实际税费累加
        batch.setRealTax(NumberUtil.add(batch.getRealTax(), batchShipmentEntity.getStoreRealTaxFee()));
        batchShipmentEntity.setStoreBaseLogisticsFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealBaseLogisticsFee(), batchShipmentEntity.getStorePredictBaseLogisticsFee()));
        batchShipmentEntity.setStoreCustomsFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealCustomsFee(), batchShipmentEntity.getStorePredictCustomsFee()));
        batchShipmentEntity.setStoreTaxFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealTaxFee(), batchShipmentEntity.getStorePredictTaxFee()));
        batchShipmentEntity.setStoreTotalLogisticsFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealTotalLogisticsFee(), batchShipmentEntity.getStorePredictTotalLogisticsFee()));
    }

    /**
     * 核对批次
     * freshPrice ==> 是否重新获取预估运价
     * <AUTHOR>
     * 2023-03-28
     */
    public void startCheckBatch(StockoutLogisticsBatchEntity batch, String logMsg, boolean freshPrice) {
        if (freshPrice) {
            initBatch(batch);
        }
        List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList = batchShipmentService.listByLogisticsBatch(Collections.singletonList(batch.getLogisticsBatch()));
        if (CollectionUtils.isEmpty(batchShipmentEntityList))
            return;
        validCheckData(batch);
        batch.setDiffPrice(batch.getRealPrice().subtract(batch.getPredictTotalPrice()));
        batch.setDiffWeight(batch.getRealWeight().subtract(batch.getPredictWeight()).abs());
        // 单价及重量差异：实际重量-仓库称重总重量>1 且 系统维护运费单价!=实际物流单价
        if (batch.getPredictUnitPrice().compareTo(batch.getRealUnitPrice()) != 0 && batch.getDiffWeight().compareTo(BigDecimal.ONE) > 0) {
            batch.setDifferenceType(LogisticsDifferenceTypeEnum.ALL_DIFFERENCE.getCode());
            batch.setReviewResult(PackageBillReviewResultEnum.REJECT.getValue());
            batch.setStatus(LogisticsBatchStatusEnum.DONE.name());
        } else if (batch.getPredictUnitPrice().compareTo(batch.getRealUnitPrice()) != 0) {
            batch.setDifferenceType(LogisticsDifferenceTypeEnum.PRICE_DIFFERENCE.getCode());
            batch.setReviewResult(PackageBillReviewResultEnum.REJECT.getValue());
            batch.setStatus(LogisticsBatchStatusEnum.DONE.name());
        } else if (batch.getDiffWeight().compareTo(BigDecimal.ONE) > 0) {
            batch.setDifferenceType(LogisticsDifferenceTypeEnum.WEIGHT_DIFFERENCE.getCode());
            batch.setReviewResult(PackageBillReviewResultEnum.REJECT.getValue());
            batch.setStatus(LogisticsBatchStatusEnum.DONE.name());
        } else {
            batch.setDifferenceType(LogisticsDifferenceTypeEnum.NO_DIFFERENCE.getCode());
            batch.setReviewResult(PackageBillReviewResultEnum.PASS.getValue());
            batch.setStatus(LogisticsBatchStatusEnum.DONE.name());
        }
        // 总费用
        BigDecimal totalFee = batch.getRealPrice().add(batch.getRealDeclarePrice() == null ? BigDecimal.ZERO : batch.getRealDeclarePrice())
                .add(batch.getRealTax() == null ? BigDecimal.ZERO : batch.getRealTax());
        BigDecimal beforeLastTotalFee = BigDecimal.ZERO;
        // 计算Shipment分摊运费
        for (int i = 0; i < batchShipmentEntityList.size(); i++) {
            StockoutLogisticsBatchShipmentEntity batchShipmentEntity = batchShipmentEntityList.get(i);
            if (batchShipmentEntityList.size() - 1 != i) {
                batchShipmentEntity.setRealPrice(batchShipmentEntity.getWeight().multiply(totalFee).divide(batch.getPredictWeight(), 2, RoundingMode.DOWN));
                beforeLastTotalFee = beforeLastTotalFee.add(batchShipmentEntity.getRealPrice());
            } else {
                batchShipmentEntity.setRealPrice(totalFee.subtract(beforeLastTotalFee));
            }
            batchShipmentEntity.setUpdateBy(loginInfoService.getName());
            batchShipmentService.updateById(batchShipmentEntity);
        }
        batch.setUpdateBy(loginInfoService.getName());
        updateById(batch);
        logisticsBatchLogService.addLog(batch, "核对运费", logMsg + "事件触发核对运费", "核对运费");
    }

    private void validCheckData(StockoutLogisticsBatchEntity batch) {
        if (batch.getPredictUnitPrice() == null) {
            throw new BusinessServiceException(batch.getLogisticsBatch() + ", 请维护[预估价格]");
        }
        if (batch.getRealPrice() == null) {
            throw new BusinessServiceException(batch.getLogisticsBatch() + ", 请维护[实际物流费用]");
        }
        if (batch.getRealUnitPrice() == null) {
            throw new BusinessServiceException(batch.getLogisticsBatch() + ", 请维护[实际物流单价]");
        }
        if (batch.getRealWeight() == null) {
            throw new BusinessServiceException(batch.getLogisticsBatch() + ", 请维护[实际重量]");
        }
    }

    @Transactional
    public void internalGenerateBatch(StockoutLogisticsBatchInternalCreateRequest request) {
        if (!CollectionUtils.isEmpty(request.getLogisticsNos())) {
            request.getLogisticsNos().forEach(logisticsNo -> {
                TenantContext.setDisableTenant(true);
                List<StockoutShipmentEntity> shipment = shipmentService.getByLogisticNo(logisticsNo);
                TenantContext.setDisableTenant(false);
                List<Integer> collect = shipment.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    return;
                }
                StockoutShipmentConfirmUpdateRequest confirmUpdateRequest = new StockoutShipmentConfirmUpdateRequest();
                confirmUpdateRequest.setShipmentIds(collect);
                confirmUpdateRequest.setDeliveryDate(shipment.get(0).getDeliveryDate());
                confirmUpdateRequest.setLogisticsNo(logisticsNo);
                confirmUpdateRequest.setLogisticsCompany(shipment.get(0).getLogisticsCompany());
                createBatch(confirmUpdateRequest);
            });
        }
        if (!CollectionUtils.isEmpty(request.getFbaShipmentIds())) {
            request.getFbaShipmentIds().forEach(fbaShipmentId -> {
                TenantContext.setDisableTenant(true);
                List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = amazonRelationService.listByFbaShipmentId(Collections.singletonList(fbaShipmentId));
                List<Integer> shipmentIds = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(shipmentIds)) {
                    return;
                }
                List<StockoutShipmentEntity> shipmentList = shipmentService.findByShipmentIdsList(shipmentIds);
                TenantContext.setDisableTenant(false);
                if (CollectionUtils.isEmpty(shipmentList)) {
                    return;
                }
                List<String> logisticsNo = shipmentList.stream().map(StockoutShipmentEntity::getLogisticsNo).distinct().collect(Collectors.toList());
                if (logisticsNo.size() != 1) {
                    throw new BusinessServiceException(fbaShipmentId + "此ShipmentId有多个物流单号");
                }
                StockoutShipmentConfirmUpdateRequest confirmUpdateRequest = new StockoutShipmentConfirmUpdateRequest();
                confirmUpdateRequest.setShipmentIds(shipmentList.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList()));
                confirmUpdateRequest.setDeliveryDate(shipmentList.get(0).getDeliveryDate());
                confirmUpdateRequest.setLogisticsNo(logisticsNo.get(0));
                confirmUpdateRequest.setLogisticsCompany(shipmentList.get(0).getLogisticsCompany());
                createBatch(confirmUpdateRequest);
            });
        }



    }

    @Transactional
    public void uploadShipment(StockoutLogisticsBatchShipmentImport row, Map<String, SaStorePageInfoResponse> storeMap, Map<String, String> companyMap) {
        SaStorePageInfoResponse storeConfig = storeMap.get(row.getStoreName());
        if (storeConfig == null) {
            throw new BusinessServiceException("未找到店铺：" + row.getStoreName());
        }
        if (!companyMap.containsKey(row.getLogisticsCompany()))
            throw new BusinessServiceException(row.getLogisticsCompany() + "此物流公司不正确");
        StockoutShipmentConfirmUpdateRequest request = new StockoutShipmentConfirmUpdateRequest();
        request.setLogisticsNo(row.getLogisticsBatch());
        request.setLogisticsCompany(row.getLogisticsCompany());
        StockoutLogisticsBatchEntity entity = buildBatch(request);
        entity.setDestinationFulfillmentCenterId(row.getDestinationFulfillmentCenterId());
        entity.setLogisticsCompany(row.getLogisticsCompany());
        updateById(entity);
        StockoutLogisticsBatchShipmentEntity batchShipment = batchShipmentService.getOneByLogisticsBatchAndFbaShipmentId(entity.getLogisticsBatch(), row.getFbaShipmentId());
        if (batchShipment == null) {
            batchShipment = new StockoutLogisticsBatchShipmentEntity();
            batchShipment.setLogisticsBatchId(entity.getId());
            batchShipment.setLogisticsBatch(entity.getLogisticsBatch());
            batchShipment.setFbaShipmentId(row.getFbaShipmentId());
        }
        batchShipment.setStoreId(storeConfig.getId());
        batchShipment.setStoreName(storeConfig.getStoreName());
        batchShipment.setBusinessType(storeConfig.getDepartment());
        batchShipment.setWeight(row.getShipmentWeight());
        batchShipment.setCreateBy(loginInfoService.getName());
        DateUtil.parse(row.getDeliveryDate());
        try {
            batchShipment.setDeliveryDate(DateUtil.parse(row.getDeliveryDate()));
        } catch (DateException e) {
            throw new BusinessServiceException(row.getDeliveryDate() + "日期格式不正确, 请填写标准化的时间格式，如：2023-01-01", e);
        }
        batchShipmentService.saveOrUpdate(batchShipment);
        initBatch(entity);
    }

    @Transactional
    public void uploadBatchPrice(StockoutLogisticsBatchPriceImport row) {
        List<StockoutLogisticsBatchEntity> stockoutLogisticsBatchEntities = listByLogisticsBatch(Collections.singletonList(row.getLogisticsBatch()));
        if (CollectionUtils.isEmpty(stockoutLogisticsBatchEntities)) {
            throw new BusinessServiceException("找不到该批次" + row.getLogisticsBatch());
        }
        StockoutLogisticsBatchEntity entity = stockoutLogisticsBatchEntities.get(0);
        entity.setPredictPriceId(0);
        entity.setPredictUnitPrice(row.getUnitPrice());
        updateById(entity);
        initBatch(entity);
        try {
            startCheckBatch(entity, "批量导入运费单价", false);
        } catch (Exception e) {
            logisticsBatchLogService.addLog(entity, "核对运费", "批量导入运费单价事件触发核对运费,且核对终止:" + e.getMessage(), "核对运费");
        }
    }

    public String pushFinance(List<Integer> idList) {
        List<String> errorList = new ArrayList<>();
        List<StockoutLogisticsBatchEntity> stockoutLogisticsBatchEntities = listByIds(idList);
        stockoutLogisticsBatchEntities.forEach(entity -> {
            validAndPush(errorList, entity);
        });
        return String.join(";", errorList);
    }

    private void validAndPush(List<String> errorList, StockoutLogisticsBatchEntity entity) {
        List<StockoutLogisticsBatchShipmentEntity> batchShipmentEntityList = batchShipmentService.listByLogisticsBatch(Collections.singletonList(entity.getLogisticsBatch()));
        if (CollectionUtils.isEmpty(batchShipmentEntityList)) {
            errorList.add(entity.getLogisticsBatch() + "物流单号找不到批次数据，无法推送");
            return;
        }
        // 检查是否已二次推送
        if (entity.getFinancialPushStatus() != null && entity.getFinancialPushStatus() == 2) {
            errorList.add(entity.getLogisticsBatch() + "物流单号已二次推送差异，不允许再次推送");
            return;
        }
        // 首次推送检查
        if (entity.getFinancialPushStatus() == null || entity.getFinancialPushStatus() == 0) {
            for (StockoutLogisticsBatchShipmentEntity batchShipmentEntity : batchShipmentEntityList) {
                if (batchShipmentEntity.getStoreRealBaseLogisticsFee() == null || batchShipmentEntity.getStoreRealCustomsFee() == null || batchShipmentEntity.getStoreRealTaxFee() == null) {
                    errorList.add(batchShipmentEntity.getFbaShipmentId() + "此票缺少实际基础物流费用或实际报关费用，无法推送");
                    return;
                }
                // 设置店铺暂估费用
                batchShipmentEntity.setStorePredictBaseLogisticsFee(batchShipmentEntity.getStoreRealBaseLogisticsFee());
                batchShipmentEntity.setStorePredictTaxFee(batchShipmentEntity.getStoreRealTaxFee());
                batchShipmentEntity.setStorePredictCustomsFee(batchShipmentEntity.getStoreRealCustomsFee());
                batchShipmentEntity.setStorePredictTotalLogisticsFee(batchShipmentEntity.getStoreRealTotalLogisticsFee());
                // 计算店铺实际费用与暂估费用的差异
                batchShipmentEntity.setStoreBaseLogisticsFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealBaseLogisticsFee(), batchShipmentEntity.getStorePredictBaseLogisticsFee()));
                batchShipmentEntity.setStoreCustomsFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealCustomsFee(), batchShipmentEntity.getStorePredictCustomsFee()));
                batchShipmentEntity.setStoreTaxFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealTaxFee(), batchShipmentEntity.getStorePredictTaxFee()));
                batchShipmentEntity.setStoreTotalLogisticsFeeDiff(NumberUtil.sub(batchShipmentEntity.getStoreRealTotalLogisticsFee(), batchShipmentEntity.getStorePredictTotalLogisticsFee()));
            }
            batchShipmentService.updateBatchById(batchShipmentEntityList);
            // 更新为已推送状态
            entity.setFinancialPushStatus(1);
            updateById(entity);
            return;
        }
        // 二次推送检查
        if (entity.getFinancialPushStatus() == 1) {
            for (StockoutLogisticsBatchShipmentEntity batchShipmentEntity : batchShipmentEntityList) {
                // 检查是否存在差异
                if (batchShipmentEntity.getStoreBaseLogisticsFeeDiff() == null || batchShipmentEntity.getStoreCustomsFeeDiff() == null || batchShipmentEntity.getStoreTaxFeeDiff() == null) {
                    errorList.add(batchShipmentEntity.getFbaShipmentId() + "此票缺少暂估与实际基础物流费用差异或其他费用差异，无法二次推送");
                    return;
                }

                // 检查差异是否为0
                if (batchShipmentEntity.getStoreBaseLogisticsFeeDiff().compareTo(BigDecimal.ZERO) == 0 && batchShipmentEntity.getStoreCustomsFeeDiff().compareTo(BigDecimal.ZERO) == 0 && batchShipmentEntity.getStoreTaxFeeDiff().compareTo(BigDecimal.ZERO) == 0) {
                    errorList.add(batchShipmentEntity.getFbaShipmentId() + "此票暂估与实际费用无差异，无需二次推送");
                    return;
                }
            }

            // 更新为已二次推送状态
            entity.setFinancialPushStatus(2);
            updateById(entity);
        }
    }
}
