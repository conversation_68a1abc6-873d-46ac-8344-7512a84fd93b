package com.nsy.wms.controller;

import com.nsy.api.wms.domain.esign.ESignSignFlowListRequest;
import com.nsy.api.wms.domain.esign.ESignSignFlowListResponse;
import com.nsy.api.wms.domain.esign.ESignSignUrlRequest;
import com.nsy.wms.business.domain.bo.esign.ESignCallbackResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowSignUrlResponse;
import com.nsy.wms.business.domain.bo.esign.ESignGetSignedFilesResponse;
import com.nsy.wms.business.domain.bo.esign.ESignOrganizationsIdentityInfoResponse;
import com.nsy.wms.business.domain.bo.esign.ESignPersonsIdentityInfoResponse;
import com.nsy.wms.business.service.esign.ESignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("esign")
public class ESignController extends BaseController {

    @Autowired
    ESignService eSignService;

    @PostMapping("callback")
    public ESignCallbackResponse callback(@RequestBody String body) {
        return eSignService.callback(body);
    }

    @PostMapping("revoke/{signFlowId}")
    public void revoke(@PathVariable String signFlowId) {
        eSignService.revoke(signFlowId);
    }

    @GetMapping("organizations/identity-info")
    public ESignOrganizationsIdentityInfoResponse organizationsIdentityInfo(@RequestParam String orgName) {
        return eSignService.organizationsIdentityInfo(orgName);
    }

    @GetMapping("persons/identity-info")
    public ESignPersonsIdentityInfoResponse personsIdentityInfo(@RequestParam String psnAccount) {
        return eSignService.personsIdentityInfo(psnAccount);
    }

    /**
     * 查询集成方企业流程列表
     */
    @PostMapping("sign-flow-list")
    public ESignSignFlowListResponse getSignFlowList(@RequestBody @Valid ESignSignFlowListRequest request) {
        return eSignService.getSignFlowList(request);
    }

    /**
     * 获取签署页面链接
     */
    @PostMapping("sign-flow-url/{signFlowId}")
    public ESignFlowSignUrlResponse getSignUrl(@PathVariable String signFlowId, @RequestBody @Valid ESignSignUrlRequest request) {
        ESignService.ContractUrlTypeEnum contractUrlType = request.getUrlType() == 1 ? ESignService.ContractUrlTypeEnum.PREVIEW : ESignService.ContractUrlTypeEnum.SIGN;

        return eSignService.getContractUrl(contractUrlType, request.getOrgName(), request.getPhone(), signFlowId);
    }

    /**
     * 获取已签署文件下载地址
     */
    @GetMapping("sign-flow-files/{signFlowId}")
    public ESignGetSignedFilesResponse getSignedFiles(@PathVariable String signFlowId) {
        return eSignService.getSignedFiles(signFlowId);
    }
}
