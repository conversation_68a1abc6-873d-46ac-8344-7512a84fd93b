package com.nsy.wms.business.manage.thirdparty.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-05-09 15:16
 */
public class SaStoreBrandDetail {
    @ApiModelProperty("主键id")
    private Integer brandStoreId;

    /**
     * 品牌id
     */
    @ApiModelProperty("品牌id")
    private Integer brandId;

    /**
     * 品牌名
     */
    @ApiModelProperty("品牌名")
    private String brandName;

    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private Integer storeId;

    /**
     * 店铺名
     */
    @ApiModelProperty("店铺名")
    private String storeName;

    public Integer getBrandStoreId() {
        return brandStoreId;
    }

    public void setBrandStoreId(Integer brandStoreId) {
        this.brandStoreId = brandStoreId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
}
