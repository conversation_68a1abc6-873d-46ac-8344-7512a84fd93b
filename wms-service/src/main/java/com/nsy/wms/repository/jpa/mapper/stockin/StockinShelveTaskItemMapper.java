package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stockin.ShelveTaskItemInfo;
import com.nsy.api.wms.domain.stockin.StockInShelveQtySumDTO;
import com.nsy.api.wms.domain.stockin.StockShelveQtyByStockinOrderIdInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveDetailExport;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemExport;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemMonthExport;
import com.nsy.api.wms.request.stockin.QtyRecordRequest;
import com.nsy.api.wms.request.stockin.QueryPurchaseOrderInfoRequest;
import com.nsy.api.wms.request.stockin.StockInShelveTaskLatestPutInfoRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptSummaryRequest;
import com.nsy.api.wms.request.stockin.StockinShelveDetailRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskItemListRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskListRequest;
import com.nsy.api.wms.response.projection.StockinShelveTaskItemProjection;
import com.nsy.api.wms.response.stockin.QueryAllWaitShelveInfoResponse;
import com.nsy.api.wms.response.stockin.QueryPurchaseOrderLatestPutDateResponse;
import com.nsy.api.wms.response.stockin.ShelveTaskStatisticsResponse;
import com.nsy.api.wms.response.stockin.StockInShelveTaskLatestPutInfoResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptShelveDateResponse;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderRequestItem;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
public interface StockinShelveTaskItemMapper extends BaseMapper<StockinShelveTaskItemEntity> {

    IPage<StockinShelveTaskItemProjection> findShelveTaskItemList(IPage iPage, @Param("query") StockinShelveTaskItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<ShelveTaskItemInfo> searchShelveTaskItemList(@Param("sourceIdList") List<Integer> sourceIdList, @Param("taskType") String taskType);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinReceiptShelveDateResponse> findMinShelveDateByProductId(@Param("productIds") List<Integer> productIds);

    List<StockinShelveTaskItemInfo> searchItemListByInternalBoxCodeAndStatus(@Param("internalBoxCode") String internalBoxCode, @Param("statusList") List<String> statusList);

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockShelveQtyByStockinOrderIdInfo> queryShelveRecordGroupByStockinOrderId(IPage iPage, @Param("request") QtyRecordRequest request);

    List<String> find32HourUnshelvedSupplierDeliveryNoList();

    List<QueryPurchaseOrderLatestPutDateResponse> queryLatestLatestPutByPurchasePlanNo(@Param("request") QueryPurchaseOrderInfoRequest request);

    IPage<StockinShelveTaskItemExport> queryExportData(IPage iPage, @Param("request") StockinShelveTaskListRequest request);

    IPage<StockinShelveTaskItemMonthExport> queryExportDataByDate(IPage iPage, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinShelveTaskItemEntity> findAllByReceiptSummaryRequestIgnoreTenant(@Param("request") StockinReceiptSummaryRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<QueryAllWaitShelveInfoResponse> queryAllWaitUpShelvesInfo(@Param("statusList") List<String> statusList);

    List<StockinShelveDetailExport> getShelveDetail(@Param("request") StockinShelveDetailRequest downloadRequest);

    /**
     * 根据入库单id统计对应sku的上架总数
     *
     * @param stockinOrderIdList
     * @return
     */
    List<StockInShelveQtySumDTO> countShelvedQtyTotalByStockinOrderId(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    /**
     * 统计工厂出库单下状态为未上架的单据数量
     *
     * @param supplierDeliveryNo
     * @return
     */
    Integer countNotShelvedTask(String supplierDeliveryNo);

    /**
     * 根据上架任务id获取工厂出库单号
     *
     * @param shelveTaskId
     * @return
     */
    String getSupplierDeliveryNoByTaskId(Integer shelveTaskId);

    /**
     * 获取最迟上架时间
     *
     * @param request
     * @return
     */
    List<StockInShelveTaskLatestPutInfoResponse> queryLatestPutInfoByPurchasePlanNoAndSku(List<StockInShelveTaskLatestPutInfoRequest> request);

    /**
     * 获取上架数量
     *
     * @param request
     * @return
     */
    ShelveTaskStatisticsResponse getShelvedStatisticsInfo(@Param("request") StockinShelveTaskListRequest request);

    PurchaseOrderRequestItem getPurchaseOrderRequest(Integer stockinOrderId, String internalBoxCode, String purchaseOrderPlanNo, String sku);
}
