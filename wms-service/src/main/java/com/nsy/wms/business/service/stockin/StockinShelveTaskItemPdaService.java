package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.bd.BdPositionInfo;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stockin.RecommendParamsInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.response.stockin.ScanUpShelvesBarcodeResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.response.SchedulingOrderPlanBomDetailResponse;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSpaceAreaService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.internal.common.PurchaseModuleService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxItemQueryWrapper;
import com.nsy.wms.business.service.stockin.building.StockinBuilding;
import com.nsy.wms.repository.entity.bd.BdSpaceAreaEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductStoreSkuMappingEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskItemMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockinShelveTaskItemPdaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinShelveTaskItemPdaService.class);

    @Inject
    StockinShelveTaskItemMapper stockinShelveTaskItemMapper;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;
    @Inject
    StockInternalBoxItemService stockInternalBoxItemService;
    @Inject
    BdSpaceAreaService spaceAreaService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private BdPositionService bdPositionService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockinReturnProductService stockinReturnProductService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockinShelveSearchPositionService shelveSearchPositionService;
    @Resource
    BdTagMappingService tagMappingService;
    @Autowired
    BdAreaService areaService;
    @Autowired
    PurchaseModuleService purchaseModuleService;
    @Autowired
    ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    BrandCommonService brandCommonService;
    @Autowired
    private ScmApiService scmApiService;
    private static final int IS_CEPTION_SHELVE = 1;

    /**
     * 根据内部箱号,sku条形码查询sku,可上架数,推荐库位
     */
    public ScanUpShelvesBarcodeResponse getShelveTaskItemInfo(String barcode, String internalBoxCode) {
        List<StockinShelveTaskItemInfo> shelveTaskItemInfoList = stockinShelveTaskItemMapper.searchItemListByInternalBoxCodeAndStatus(internalBoxCode, Lists.newArrayList(StockinShelveTaskStatusEnum.SHELVING.name()));
        if (CollectionUtils.isEmpty(shelveTaskItemInfoList)) {
            LOGGER.error("找不到该内部箱号:【 {}】的待上架上架任务", internalBoxCode);
            throw new BusinessServiceException(String.format("找不到该内部箱号:【%s】的待上架上架任务", internalBoxCode));
        }
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopByBarcodeFromShelve(barcode, shelveTaskItemInfoList.get(0).getShelveTaskId());
        List<StockinShelveTaskItemInfo> filterBarcodeItemList = shelveTaskItemInfoList.stream().filter(m -> specInfoEntity.getBarcode().equals(m.getBarcode())).collect(Collectors.toList());
        ScanUpShelvesBarcodeResponse data = new ScanUpShelvesBarcodeResponse();
        if (CollectionUtils.isEmpty(filterBarcodeItemList)) {
            LOGGER.error("上架任务: {} 中不存在商品条码: {},返回异常上架", shelveTaskItemInfoList.get(0).getShelveTaskId(), barcode);
            ProductSpecInfo productSpecInfoEntity = productSpecInfoService.getByBarcode(barcode);
            StockinBuilding.scanUpShelvesBarcodeResponse(data, productSpecInfoEntity, IS_CEPTION_SHELVE);
            Map<String, List<String>> productTagMap = tagMappingService.getProductTagBySkus(Collections.singletonList(data.getSku()));
            data.setProductTag(productTagMap.getOrDefault(data.getSku(), Collections.emptyList()));
            return data;
        }
        SchedulingOrderPlanBomDetailResponse bomDetail = scmApiService.getSchedulingOrderPlanBomDetail(specInfoEntity.getProductId(), specInfoEntity.getSkc());
        if (!Objects.isNull(bomDetail) && !CollectionUtils.isEmpty(bomDetail.getProductSkcBomMaterialOfferingDtoList())
                && bomDetail.getProductSkcBomMaterialOfferingDtoList().stream().filter(detail -> !CollectionUtils.isEmpty(detail.getRiskDescList()) && detail.getRiskDescList().stream().anyMatch(risk -> risk.contains("二创"))).findAny().isPresent()) {
            data.setSecondCreationLabel("二创");
        }

        data.setSku(filterBarcodeItemList.get(0).getSku());
        data.setImage(filterBarcodeItemList.get(0).getPreviewImageUrl());
        data.setBrandName(filterBarcodeItemList.get(0).getBrandName());
        data.setIsFbaQuick(filterBarcodeItemList.get(0).getIsFbaQuick());
        data.setIsClash(specInfoEntity.getIsClash());
        List<ProductStoreSkuMappingEntity> skuMappingEntityList = productStoreSkuMappingService.list(new LambdaQueryWrapper<ProductStoreSkuMappingEntity>().eq(ProductStoreSkuMappingEntity::getSku, filterBarcodeItemList.get(0).getSku())
                .eq(ProductStoreSkuMappingEntity::getStoreSku, filterBarcodeItemList.get(0).getSellerSku())
                .isNotNull(ProductStoreSkuMappingEntity::getStoreId));
        if (!CollectionUtils.isEmpty(skuMappingEntityList))
            data.setStoreName(skuMappingEntityList.get(0).getStoreName());
        Map<String, List<String>> productTagMap = tagMappingService.getProductTagBySkus(Collections.singletonList(data.getSku()));
        data.setProductTag(productTagMap.getOrDefault(data.getSku(), Collections.emptyList()));
        LambdaQueryWrapper<StockInternalBoxItemEntity> queryWrapperInternalBoxCodeAndSku = StockInternalBoxItemQueryWrapper.buildWrapperByInternalBoxCodeAndSku(internalBoxCode, filterBarcodeItemList.get(0).getSku());
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemService.list(queryWrapperInternalBoxCodeAndSku);
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList)) {
            LOGGER.error("resource internalBoxCode={} sku={} stockInternalBoxItemEntity not found", internalBoxCode, filterBarcodeItemList.get(0).getSku());
            data.setIsExceptionShelve(IS_CEPTION_SHELVE); // 内部箱不存在sku,返回异常上架
            return data;
        }
        List<StockInternalBoxItemEntity> collect = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() > 0 && !StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect))
            throw new BusinessServiceException(String.format("内部箱【%s】存在状态为【%s】的SKU，无法上架，请确定", internalBoxCode, enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), collect.get(0).getStatus())));
        // 可上架数
        int pendingShelveQty = stockinShelveTaskItemService.buildPendingShelveQty(filterBarcodeItemList);
        data.setQty(pendingShelveQty);
        //根据品牌信息赋值区域
        setAreaName(internalBoxCode, data, filterBarcodeItemList);
        // sku根据上架推荐规则获取推荐上架库位及对应库存数
        List<BdPositionInfo> bdPositionList = getRecommendPositionList(filterBarcodeItemList.get(0), internalBoxCode);
        if (!CollectionUtils.isEmpty(bdPositionList)) {
            BdPositionInfo bdPositionInfo = bdPositionList.get(0);
            data.setAreaName(bdPositionService.getPositionByCode(bdPositionInfo.getPositionCode()).getAreaName());
            buildSkuPositionStock(bdPositionList, filterBarcodeItemList.get(0).getSpecId(), data);
        }
        shelveSearchPositionService.buildInternalBoxStock(specInfoEntity, data);
        return data;
    }

    private void setAreaName(String internalBoxCode, ScanUpShelvesBarcodeResponse data, List<StockinShelveTaskItemInfo> filterBarcodeItemList) {
        List<Integer> sourceId = filterBarcodeItemList.stream().map(StockinShelveTaskItemInfo::getSourceId).distinct().collect(Collectors.toList());
        List<String> areaNameByShelveTask = stockinOrderItemService.getBaseMapper().getAreaNameByShelveTask(sourceId, filterBarcodeItemList.get(0).getSku(), internalBoxCode);
        if (CollectionUtils.isEmpty(areaNameByShelveTask))
            return;
        List<String> brandAreaNameList = brandCommonService.getBrandAreaNameList();
        for (String brandAreaName : brandAreaNameList) {
            if (areaNameByShelveTask.contains(brandAreaName))
                data.setAreaName(brandAreaName);
        }
        if (!StringUtils.hasText(data.getAreaName()))
            data.setAreaName(areaNameByShelveTask.get(0));
    }

    /**
     * 获取推荐库位 上架任务类型 1、入库上架 2、调拨上架 3、仓间调拨上架
     *
     * @param shelveTaskItemInfo 上架明细信息
     * @param internalBoxCode    内部箱
     * @return 推荐库位
     */
    private List<BdPositionInfo> getRecommendPositionList(StockinShelveTaskItemInfo shelveTaskItemInfo, String internalBoxCode) {
        Integer areaId = getAreaId(shelveTaskItemInfo, internalBoxCode);
        List<BdPositionInfo> bdPositionList = new ArrayList<>(16);
        if (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(shelveTaskItemInfo.getTaskType()) || StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name().equals(shelveTaskItemInfo.getTaskType())) {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getById(shelveTaskItemInfo.getSourceId());
            if (Objects.isNull(stockinOrderEntity))
                throw new BusinessServiceException(String.format("根据id: %s 找不到入库单信息--------", shelveTaskItemInfo.getSourceId()));
            StockinOrderTaskItemEntity stockinOrderTaskItemEntity = stockinOrderTaskItemService.findTopOneByTaskIdAndSpecIdAndPurchasePlanNo(stockinOrderEntity.getTaskId(), shelveTaskItemInfo.getSpecId(), shelveTaskItemInfo.getPurchasePlanNo());
            RecommendParamsInfo info = StockinBuilding.buildRecommendInfo(stockinOrderEntity.getStockinType(), stockinOrderTaskItemEntity.getSpaceId(), shelveTaskItemInfo.getSpecId(), areaId, StockConstant.RECOMMEND_POSITION);
            info.setStoreId(stockinOrderTaskItemEntity.getStoreId());
            info.setBusinessType(stockinOrderTaskItemEntity.getBusinessType());
            info.setIsFbaQuick(stockinOrderTaskItemEntity.getIsFbaQuick());
            bdPositionList = shelveSearchPositionService.recommendSpaceAreaAndPosition(info);
        } else if (StockinShelveTaskTypeEnum.TRANSFER_SHELVE.name().equals(shelveTaskItemInfo.getTaskType())) {
            bdPositionList = shelveSearchPositionService.recommendSpaceAreaAndPosition(StockinBuilding.buildRecommendInfo(
                    StockinTypeEnum.FACTORY.name(), shelveTaskItemInfo.getSpaceId(), shelveTaskItemInfo.getSpecId(), areaId, StockConstant.RECOMMEND_POSITION));
        }
        return bdPositionList;
    }

    private Integer getAreaId(StockinShelveTaskItemInfo shelveTaskItemInfo, String internalBoxCode) {
        if (shelveTaskItemInfo.getSourceId() == null || shelveTaskItemInfo.getSourceId() <= 0) {
            BdSpaceAreaEntity spaceAreaEntity = spaceAreaService.getById(shelveTaskItemInfo.getSpaceAreaId());
            return spaceAreaEntity.getAreaId();
        }
        List<StockinOrderItemEntity> list = stockinOrderItemService.list(new QueryWrapper<StockinOrderItemEntity>().lambda().eq(StockinOrderItemEntity::getStockinOrderId, shelveTaskItemInfo.getSourceId())
                .eq(StockinOrderItemEntity::getSku, shelveTaskItemInfo.getSku())
                .eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("找不到入库单明细，无法获取推荐库区");
        }
        StockinOrderItemEntity stockinOrderItem = list.get(0);
        if (list.size() > 1) {
            List<StockinOrderItemEntity> filterList = list.stream().filter(it -> Objects.equals(it.getPurchasePlanNo(), shelveTaskItemInfo.getPurchasePlanNo())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterList)) {
                stockinOrderItem = filterList.get(0);
            }
        }
        StockinOrderTaskItemEntity stockinOrderTaskItemEntity = stockinOrderTaskItemService.getById(stockinOrderItem.getTaskItemId());
        return areaService.findByAreaNameAndSpaceId(stockinOrderTaskItemEntity.getAreaName(), stockinOrderTaskItemEntity.getSpaceId()).getAreaId();
    }

    private void buildSkuPositionStock(List<BdPositionInfo> bdPositionList, Integer specId, ScanUpShelvesBarcodeResponse response) {
        List<ScanUpShelvesBarcodeResponse.SkuPositionView> skuPositionList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bdPositionList)) {
            for (BdPositionInfo positionInfo : bdPositionList) {
                if (BdPositionTypeEnum.CROSS_POSITION.name().equals(positionInfo.getPositionType()))
                    response.setCrossPosition(positionInfo.getPositionCode());
                if (BdPositionTypeEnum.STORE_POSITION.name().equals(positionInfo.getPositionType()))
                    response.setStorePosition(positionInfo.getPositionCode());
                ScanUpShelvesBarcodeResponse.SkuPositionView skuPositionView = new ScanUpShelvesBarcodeResponse.SkuPositionView();
                if (!specId.equals(positionInfo.getSpecId())) {
                    skuPositionView.setIsSameSkc(1);
                    ProductSpecInfoEntity specInfoEntity1 = productSpecInfoService.findTopBySpecId(specId);
                    ProductSpecInfoEntity specInfoEntity2 = productSpecInfoService.findTopBySpecId(specId);
                    skuPositionView.setRemark(specInfoEntity1.getColorCode().equals(specInfoEntity2.getColorCode()) ? StockConstant.SAME_SKC : StockConstant.SAME_PRODUCT);
                } else {
                    skuPositionView.setIsSameSkc(0);
                }
                if (positionInfo.getSpecId() != null) {
                    ProductSpecInfoEntity specInfo = productSpecInfoService.findTopBySpecId(positionInfo.getSpecId());
                    skuPositionView.setSku(specInfo == null ? null : specInfo.getSku());
                }
                skuPositionView.setPosition(positionInfo.getPositionCode());
                skuPositionView.setStock(positionInfo.getStock());
                skuPositionList.add(skuPositionView);
            }
        }
        response.setSkuPositionViewList(skuPositionList);
    }


}
