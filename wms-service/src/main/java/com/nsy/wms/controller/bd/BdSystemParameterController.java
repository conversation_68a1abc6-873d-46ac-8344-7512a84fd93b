package com.nsy.wms.controller.bd;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.nsy.api.wms.request.bd.BdSystemParameterListRequest;
import com.nsy.api.wms.request.bd.BdSystemParameterQueryRequest;
import com.nsy.api.wms.request.bd.BdSystemParameterRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdSystemParameterResponse;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.controller.BaseController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @author: caish<PERSON><PERSON>
 * @version: v1.0
 * @description: 系统配置表业务实现
 * @date: 2021-07-29 11:35
 */
@Api(tags = "系统配置相关接口")
@Controller
public class BdSystemParameterController extends BaseController {

    @Autowired
    BdSystemParameterService bdSystemParameterService;


    @ApiOperation(value = "配置系统参数", notes = "配置系统参数", produces = "application/json")
    @RequestMapping(value = "/system-parameter", method = RequestMethod.POST)
    public void setSystemParameter(@RequestBody BdSystemParameterRequest request) {
        bdSystemParameterService.setSystemParameter(request);
    }

    @ApiOperation(value = "查询系统参数", notes = "查询系统参数", produces = "application/json")
    @RequestMapping(value = "/system-parameter-select", method = RequestMethod.POST)
    public Map<String, Object> getSystemParameter(@RequestBody BdSystemParameterQueryRequest request) {
        return bdSystemParameterService.getSystemParameter(request);
    }

    @ApiOperation(value = "查询系统参数-根据key", notes = "查询系统参数-根据key", produces = "application/json")
    @RequestMapping(value = "/system-parameter-select-by-key", method = RequestMethod.GET)
    public BdSystemParameterResponse getSystemParameterByKey(@RequestParam String key) {
        return bdSystemParameterService.findByKey(key);
    }

    @ApiOperation(value = "查询系统参数列表", notes = "查询系统参数列表", produces = "application/json")
    @RequestMapping(value = "/system-parameter/list", method = RequestMethod.POST)
    public PageResponse<BdSystemParameterResponse> getSystemParameterList(@RequestBody BdSystemParameterListRequest request) {
        return bdSystemParameterService.getSystemParameterList(request);
    }

    @ApiOperation(value = "添加系统参数", notes = "添加系统参数", produces = "application/json")
    @RequestMapping(value = "/system-parameter/add", method = RequestMethod.POST)
    public void addSystemParameter(@RequestBody BdSystemParameterListRequest request) {
        bdSystemParameterService.addSystemParameter(request);
    }

    @ApiOperation(value = "更新系统参数", notes = "更新系统参数", produces = "application/json")
    @RequestMapping(value = "/system-parameter/update", method = RequestMethod.POST)
    public void updateSystemParameter(@RequestBody BdSystemParameterListRequest request) {
        bdSystemParameterService.updateSystemParameter(request);
    }
}
