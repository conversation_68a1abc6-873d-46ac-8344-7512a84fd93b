package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.domain.stockout.StockoutVasTaskItemList;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutVasTaskItemListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskItemListResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutValueAddServiceTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutValueAddServiceTaskItemMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StockoutVasTaskItemService extends ServiceImpl<StockoutValueAddServiceTaskItemMapper, StockoutValueAddServiceTaskItemEntity> {

    @Autowired
    LoginInfoService loginInfoService;

    /**
     * 创建增值任务明细
     *
     * @param stockoutOrderItemEntityList 出库单明细
     * @param id                          增值任务id
     */
    public void createByStockoutItems(List<StockoutOrderItemEntity> stockoutOrderItemEntityList, Integer id) {
        for (StockoutOrderItemEntity stockoutOrderItem : stockoutOrderItemEntityList) {
            StockoutValueAddServiceTaskItemEntity itemEntity = new StockoutValueAddServiceTaskItemEntity();
            itemEntity.setTaskId(id);
            itemEntity.setOrderNo(stockoutOrderItem.getOrderNo());
            itemEntity.setSku(stockoutOrderItem.getSku());
            itemEntity.setSku(stockoutOrderItem.getSku());
            itemEntity.setVasQty(stockoutOrderItem.getQty());
            itemEntity.setVasType(stockoutOrderItem.getVasType());
            itemEntity.setStatus(StockoutVasTaskStatusEnum.WAIT.name());
            itemEntity.setCreateBy(loginInfoService.getName());
            this.save(itemEntity);
        }
    }

    /**
     * 获取订单号数组
     *
     * @param id
     * @return
     */
    public List<String> getOrderNoByTaskId(Integer id) {
        return this.list(new QueryWrapper<StockoutValueAddServiceTaskItemEntity>().lambda().eq(StockoutValueAddServiceTaskItemEntity::getTaskId, id))
                .stream().map(StockoutValueAddServiceTaskItemEntity::getOrderNo).distinct().collect(Collectors.toList());
    }

    /**
     * 明细列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutVasTaskItemListResponse> getListByRequest(StockoutVasTaskItemListRequest request) {
        PageResponse<StockoutVasTaskItemListResponse> pageResponse = new PageResponse<>();
        Page<StockoutVasTaskItemList> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutVasTaskItemList> pageResult = this.baseMapper.pageSearchList(page, request);

        List<StockoutVasTaskItemListResponse> recordList = pageResult.getRecords().stream().map(projection -> {
            StockoutVasTaskItemListResponse response = new StockoutVasTaskItemListResponse();
            BeanUtils.copyProperties(projection, response);
            // 增值类型处理
            response.setVasTypeStr(StockoutVasTaskTypeEnum.getVasTypeStr(projection.getVasType()));
            return response;
        }).collect(Collectors.toList());

        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(recordList);
        return pageResponse;
    }

    /**
     * 修改增值类型
     *
     * @param id
     * @param vasType
     */
    public void updateVasType(Integer id, String vasType) {
        List<StockoutValueAddServiceTaskItemEntity> taskItemEntityList = this.list(new QueryWrapper<StockoutValueAddServiceTaskItemEntity>()
                .lambda().eq(StockoutValueAddServiceTaskItemEntity::getTaskId, id));
        for (StockoutValueAddServiceTaskItemEntity itemEntity : taskItemEntityList) {
            itemEntity.setVasType(vasType);
            itemEntity.setUpdateBy(loginInfoService.getName());
        }
        this.updateBatchById(taskItemEntityList);
    }

    /**
     * 完成增值任务
     *
     * @param id
     * @param status
     */
    public void updateVasStatus(Integer id, String status) {
        List<StockoutValueAddServiceTaskItemEntity> taskItemEntityList = this.list(new QueryWrapper<StockoutValueAddServiceTaskItemEntity>()
                .lambda().eq(StockoutValueAddServiceTaskItemEntity::getTaskId, id));
        for (StockoutValueAddServiceTaskItemEntity itemEntity : taskItemEntityList) {
            itemEntity.setStatus(status);
            itemEntity.setUpdateBy(loginInfoService.getName());
        }
        this.updateBatchById(taskItemEntityList);
    }
}
