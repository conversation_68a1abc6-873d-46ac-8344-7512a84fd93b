package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.StockoutCustomsDeclareContractTemplateEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.wms.business.domain.bo.esign.ESignCheckFileStatusResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFileUploadUrlResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareContractRenderModel;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareContractRenderModelCompany;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareContractRenderModelItem;
import com.nsy.wms.business.manage.scm.response.SupplierTaxGetSampleDatailDto;
import com.nsy.wms.business.service.bd.BdCompanyService;
import com.nsy.wms.business.service.esign.ESignService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.system.AliyunOssService;
import com.nsy.wms.repository.entity.bd.BdCompanyEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.NoSuchAlgorithmException;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 关单合同操作
 */
@Service
public class StockoutCustomsDeclareContractFileService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareContractFileService.class);

    @Resource
    StockoutCustomsDeclareFormService formService;
    @Resource
    StockoutCustomsDeclareContractService contractService;
    @Resource
    StockoutCustomsDeclareFormLogService formLogService;
    @Resource
    BdCompanyService bdCompanyService;
    @Resource
    StockoutCustomsDeclareContractLogService contractLogService;
    @Resource
    ESignService eSignService;
    @Resource
    StockoutCustomsDeclareFormItemService formItemService;
    @Resource
    AliyunOssService ossService;
    @Resource
    StockoutCustomsDeclareDocumentService stockoutCustomsDeclareDocumentService;

    /**
     * 上传e签宝合同
     * <p>
     * * 按照规则组合合同
     * * 1. 20条生成一份
     * * 2. <3条 且 < 10000元暂不生成合同
     * * 3. 相同抬头生成
     * 1.找出已处理 且 非重新选择供应商 的 关单
     * 2.生成合同 合同编号 回填关单合同号 合同状态【待审核】
     */
    @Transactional
    public void uploadFile() {
        //1.已处理 && 非重新选择供应商 && 非手动冲库存
        List<StockoutCustomsDeclareFormEntity> allFormList = formService.list(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>()
                .eq(StockoutCustomsDeclareFormEntity::getStatus, StockoutCustomsDeclareFormStatusEnum.DEALT.name())
                .ne(StockoutCustomsDeclareFormEntity::getSystemMark, StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name())
                .eq(StockoutCustomsDeclareFormEntity::getShouldChooseOtherSupplier, Boolean.FALSE));
        //按照规则组合合同
        Map<String, List<StockoutCustomsDeclareFormEntity>> formMap = allFormList.stream().filter(form -> !Objects.isNull(form.getSupplierId()) && !Objects.isNull(form.getCompanyId()))
                .collect(Collectors.groupingBy(form -> String.format("%s-%s", form.getCompanyId(), form.getSupplierId())));
        if (formMap.isEmpty()) return;
        //2.生成合同 合同编号 回填关单合同号 合同状态【待审核】
        //最多20条一个合同
        formMap.values().forEach(formList -> CollectionUtil.split(formList, 20).forEach(splitFormList -> {
            try {
                SpringUtil.getBean(StockoutCustomsDeclareContractFileService.class).uploadFile(splitFormList);
            } catch (IOException | NoSuchAlgorithmException | BusinessServiceException e) {
                LOGGER.error(String.format(" %s 生成合同失败 %s",
                        splitFormList.stream().map(temp -> temp.getDeclareFormId().toString()).collect(Collectors.joining(",")),
                        e.getMessage()), e);
                splitFormList.forEach(formEntity -> formLogService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.GENERATE_CONTRACT, e.getMessage()));

            }
        }));
    }

    /**
     * 上传e签宝合同 合同编号 回填关单合同号 合同状态【待审核】
     *
     * @param formList
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void uploadFile(List<StockoutCustomsDeclareFormEntity> formList) throws IOException, NoSuchAlgorithmException {
        StockoutCustomsDeclareFormEntity firstForm = formList.get(0);
        BdCompanyEntity company = bdCompanyService.getById(firstForm.getCompanyId());
        if (Objects.isNull(company)) {
            LOGGER.error("公司不存在 {}", firstForm.getCompanyId());
            return;
        }
        //生成合同
        StockoutCustomsDeclareContractEntity contractEntity = new StockoutCustomsDeclareContractEntity();
        contractEntity.setStatus(StockoutCustomsDeclareContractStatusEnum.WAIT_AUDIT.name());
        contractEntity.setCompanyId(company.getCompanyId());
        contractEntity.setCompanyName(company.getCompanyName());
        contractEntity.setSupplierId(firstForm.getSupplierId());
        contractEntity.setSupplierName(firstForm.getSupplierName());
        AtomicInteger qty = new AtomicInteger();
        final BigDecimal[] totalInputPrice = {BigDecimal.ZERO};
        formList.forEach(form -> {
            int realQty = form.getgQty() - form.getTotalManualInputQty();
            qty.addAndGet(realQty);
            BigDecimal currentInputPrice = form.getTaxInclusiveUnitPrice()
                    .multiply(BigDecimal.valueOf(realQty))
                    .divide(BigDecimal.valueOf(1.13f), 2, RoundingMode.HALF_UP);
            totalInputPrice[0] = totalInputPrice[0].add(currentInputPrice);
        });
        contractEntity.setQty(qty.get());
        contractEntity.setInputPrice(totalInputPrice[0]);
        //<3 条 且 < 10000元暂不生成合同
        if (formList.size() < 3) {
            throw new BusinessServiceException("关单小于3条，不生成合同， " + formList.stream().map(form -> form.getDeclareFormId().toString()).collect(Collectors.joining(",")));
        }
//        if (NumberUtil.isGreater(BigDecimal.valueOf(10000), contractEntity.getInputPrice())) {
//            throw new BusinessServiceException("关单金额之和小于10000，不生成合同， " + formList.stream().map(form -> form.getDeclareFormId().toString()).collect(Collectors.joining(",")));
//        }
        SupplierTaxGetSampleDatailDto supplierInfo = contractService.getSupplierInfo(contractEntity.getSupplierId(), null);


        // 先获取最早的一期签约日期
        Date signDate = getEarliestSignDate(formList);
        // 交货日期 = 签约日期 - 3天
        Date deliveryDate = DateUtil.offsetDay(signDate, -3);
        // 入库日期：根据是否省内决定
        Date stockinDate;
        if (supplierInfo.getAddress().contains("福建")) {
            // 省内入库日 = 交货日
            stockinDate = deliveryDate;
        } else {
            // 省外入库日 = 交货日 + 2天
            stockinDate = DateUtil.offsetDay(deliveryDate, 2);
        }
        int totalQty = formList.stream()
                .map(form -> formItemService.itemList(form.getDeclareFormId()))
                .flatMap(Collection::stream)
                .mapToInt(StockoutCustomsDeclareFormItemEntity::getInputQty)
                .sum();
        Date contractSignDate = contractService.buildSignDate(deliveryDate, totalQty);
        String contractNoPrefix = company.getAbbreviation() + DateUtil.format(contractSignDate, "yyyyMMdd");
        Integer count = contractService.countByDeclareContractNo(contractNoPrefix) + 1;
        contractEntity.setDeclareContractNo(contractNoPrefix + String.format("%04d", count));
        contractEntity.setDeliveryDate(deliveryDate);
        contractEntity.setSignDate(contractSignDate);
        contractService.save(contractEntity);


        //修改关单状态 回填合同号 入库日期
        formList.forEach(form -> formService.uploadContractFeedback(form, contractEntity, stockinDate));
        //更新签订日期
        formService.updateSignedDate(formList.stream().map(StockoutCustomsDeclareFormEntity::getDeclareFormId).collect(Collectors.toList()), contractEntity.getSignDate());

        contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.GENERATE_CONTRACT, "关单合同生成");
        //合同上传e签宝
        uploadToEsign(contractEntity.getDeclareContractId());
    }


    /**
     * 检查文件状态
     * <p>
     * 2. 获取文件状态
     * 3. 回填文件状态
     * 4. 上传合同到oss，回填oss路径
     */
    public void checkFileStatus(StockoutCustomsDeclareContractEntity contract) {
        // 2. 获取文件状态
        ESignCheckFileStatusResponse fileStatusResponse = eSignService.checkFileStatus(contract.getFileId());
        // 3. 回填文件状态
        if (2 != fileStatusResponse.getFileStatus() && 5 != fileStatusResponse.getFileStatus()) {
            return;
        }
        contract.setFileUploadStatus(Boolean.TRUE);
        // 4. 上传合同到oss，回填oss路径
        byte[] bytes = HttpUtil.downloadBytes(fileStatusResponse.getFileDownloadUrl());
        String fileUrl = ossService.putObject(IoUtil.toStream(bytes), "declareContractPreview", fileStatusResponse.getFileName());
        contract.setPreviewUrl(fileUrl);
        contractService.updateById(contract);

        contractLogService.addLog(contract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.GENERATE_CONTRACT, "合同上传完成");
    }


    /**
     * 上传合同到e签宝
     * * 1.渲染合同
     * * 2.获取合同上传链接
     * * 3.上传合同
     * * 4.修改fileId
     *
     * @param declareContractId
     */
    private void uploadToEsign(Integer declareContractId) throws IOException, NoSuchAlgorithmException {
        StockoutCustomsDeclareContractEntity contract = contractService.findById(declareContractId);
        //1.渲染合同
        SupplierTaxGetSampleDatailDto supplierInfo = contractService.getSupplierInfo(contract.getSupplierId(), "IN_COOPERATION");
        StockoutCustomsDeclareContractRenderModel renderModel = buildRenderModel(contract, supplierInfo);
        Map<String, Object> data = JsonMapper.convertToMap(renderModel);
        InputStream is = FreeMarkerTemplateUtils.renderTemplateToInputStream(StockoutCustomsDeclareContractTemplateEnum.getFileNameByTemplateNo(supplierInfo.getContractTemplate()), data);
        if (Objects.isNull(is)) {
            throw new BusinessServiceException("渲染模板失败");
        }

        // 保存流的内容，以便后续上传到OSS
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        // 从输入流读取数据到缓冲区
        while (true) {
            len = is.read(buffer);
            if (len <= -1) {
                break;
            }
            baos.write(buffer, 0, len);
        }
        baos.flush();
        byte[] contractBytes = baos.toByteArray();

        // 重新创建输入流用于上传到E签宝
        InputStream esignStream = IoUtil.toStream(contractBytes);

        long fileSize = esignStream.available();
        String contentMD5 = eSignService.getFileContentMD5(esignStream);

        String contentType = "application/octet-stream";
        //2.获取合同上传链接
        ESignFileUploadUrlResponse uploadUrlResponse = eSignService.getFileUploadUrl(String.format("%s.xls", contract.getDeclareContractNo()), contentMD5, contentType, fileSize);
        if (esignStream.markSupported()) {
            esignStream.reset();
        } else {
            // 如果流不支持重置，重新创建一个
            esignStream = IoUtil.toStream(contractBytes);
        }
        //3.上传合同
        eSignService.uploadFile(uploadUrlResponse, esignStream, contentMD5, contentType);
        esignStream.close();
        contract.setFileId(uploadUrlResponse.getFileId());

        //4.同时上传到OSS，存储Excel文件链接
        try {
            String excelFileName = contract.getDeclareContractNo() + ".xls";
            String fileUrl = ossService.putObject(IoUtil.toStream(contractBytes), "declareContractExcel", excelFileName);
            // 更新合同Excel预览链接
            contract.setExcelPreviewUrl(fileUrl);
            LOGGER.info("成功上传Excel合同并更新预览链接, 合同号: {}, 链接: {}", contract.getDeclareContractNo(), fileUrl);
        } catch (Exception e) {
            LOGGER.error("上传Excel合同文件到OSS失败", e);
        }

        contractService.updateById(contract);
    }

    /**
     * 构建渲染对象
     *
     * @param contract
     * @return
     */
    private StockoutCustomsDeclareContractRenderModel buildRenderModel(StockoutCustomsDeclareContractEntity contract, SupplierTaxGetSampleDatailDto supplierInfo) {
        List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractId(contract.getDeclareContractId());
        if (formList.isEmpty()) throw new BusinessServiceException("该合同对应的关单列表为空");
        StockoutCustomsDeclareContractRenderModel model = new StockoutCustomsDeclareContractRenderModel();
        //公司信息
        BdCompanyEntity company = bdCompanyService.getById(contract.getCompanyId());
        if (Objects.isNull(company))
            throw new BusinessServiceException(String.format("公司不存在 %s", contract.getCompanyName()));
        model.setCompany(buildCompanyModel(company, contract.getCompanyName()));

        //工厂信息
        model.setSupplier(buildSupplierModel(supplierInfo, contract.getSupplierName()));
        if (!StringUtils.hasText(supplierInfo.getFreightCarrierName()))
            throw new BusinessServiceException(String.format("【运费承担方】未维护 %s", contract.getCompanyName()));
        model.setFreightCarrierName(supplierInfo.getFreightCarrierName());
        //更新合同经办人信息
        if (!StringUtils.hasText(company.getAgentPhone())) throw new BusinessServiceException("【公司经办人电话】未维护");
        contract.setCompanyAgentPhone(company.getAgentPhone());

        if (!StringUtils.hasText(supplierInfo.getOperatorTel()))
            throw new BusinessServiceException(String.format("【供应商经办人电话】未维护 %s", contract.getCompanyName()));
        contract.setSupplierAgentPhone(supplierInfo.getOperatorTel());

        contractService.updateById(contract);
        model.setDeclareContractNo(contract.getDeclareContractNo());

        model.setDeliveryDate(DateUtil.format(contract.getDeliveryDate(), "yyyy/MM/dd"));
        model.setSignDate(DateUtil.format(contract.getSignDate(), "yyyy-MM-dd"));
        buildRenderModelItem(model, formList);
        return model;
    }

    /**
     * 构建渲染对象明细
     *
     * @param model
     * @param formList
     */
    public void buildRenderModelItem(StockoutCustomsDeclareContractRenderModel model, List<StockoutCustomsDeclareFormEntity> formList) {
        final int[] index = {1};
        final BigDecimal[] totalPrice = {BigDecimal.ZERO};
        final BigDecimal[] totalTaxPrice = {BigDecimal.ZERO};
        AtomicInteger totalQty = new AtomicInteger();

        List<StockoutCustomsDeclareContractRenderModelItem> itemList = formList.stream().map(form -> {
            StockoutCustomsDeclareContractRenderModelItem itemModel = new StockoutCustomsDeclareContractRenderModelItem();
            itemModel.setNo(String.valueOf(index[0]));
            itemModel.setgName(form.getgName());
            itemModel.setgUnit(StockoutBuilding.getDeclareUnitMap().get(form.getgUnit()));
            List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemListNoManual(form.getDeclareFormId());
            AtomicInteger formQty = new AtomicInteger();
            final BigDecimal[] formTaxInclusivePrice = {BigDecimal.ZERO};
            final BigDecimal[] formTaxPrice = {BigDecimal.ZERO};
            formItemList.forEach(item -> {
                formQty.addAndGet(item.getInputQty());
                formTaxInclusivePrice[0] = formTaxInclusivePrice[0].add(item.getTaxInclusivePrice());
                formTaxPrice[0] = formTaxPrice[0].add(item.getTaxPrice());
            });
            itemModel.setgQty(String.valueOf(formQty.get()));
            itemModel.setTaxInclusiveUnitPrice(form.getTaxInclusiveUnitPrice().setScale(2, RoundingMode.HALF_UP).toString());
            itemModel.setPrice(formTaxInclusivePrice[0].setScale(2, RoundingMode.HALF_UP).toString());
            //算总的
            totalQty.addAndGet(formQty.get());
            totalPrice[0] = totalPrice[0].add(formTaxInclusivePrice[0]);
            totalTaxPrice[0] = totalTaxPrice[0].add(formTaxPrice[0]);
            index[0] += 1;
            return itemModel;
        }).collect(Collectors.toList());
        model.setItemList(itemList);
        model.setTotalQty(String.valueOf(totalQty.get()));
        BigDecimal taxInclusivePrice = totalPrice[0].setScale(2, RoundingMode.HALF_UP);
        model.setTaxInclusivePrice(taxInclusivePrice.toString());
        model.setTaxPrice(totalTaxPrice[0].setScale(2, RoundingMode.HALF_UP).toString());
        model.setNoTaxInclusivePrice(totalPrice[0].subtract(totalTaxPrice[0]).setScale(2, RoundingMode.HALF_UP).toString());
        model.setRmb(NumberChineseFormatter.format(taxInclusivePrice.doubleValue(), true, true));
    }

    /**
     * 构建公司合同渲染对象
     *
     * @param company
     * @return
     */
    private StockoutCustomsDeclareContractRenderModelCompany buildCompanyModel(BdCompanyEntity company, String companyName) {
        StockoutCustomsDeclareContractRenderModelCompany companyModel = new StockoutCustomsDeclareContractRenderModelCompany();
        if (!StringUtils.hasText(company.getCompanyName()))
            throw new BusinessServiceException(String.format("【公司名称】未维护 %s", companyName));
        companyModel.setCompanyName(company.getCompanyName());

        if (!StringUtils.hasText(company.getBankAccount()))
            throw new BusinessServiceException(String.format("【公司开户账号】未维护 %s", companyName));
        companyModel.setAccount(company.getBankAccount());

        if (!StringUtils.hasText(company.getAddress()))
            throw new BusinessServiceException(String.format("【公司地址】未维护 %s", companyName));
        companyModel.setAddr(company.getAddress());

        if (!StringUtils.hasText(company.getBank()))
            throw new BusinessServiceException(String.format("【开户行】未维护 %s", companyName));
        companyModel.setBank(company.getBank());

        if (!StringUtils.hasText(company.getTaxNo()))
            throw new BusinessServiceException(String.format("【公司纳税人识别号】未维护 %s", companyName));
        companyModel.setCode(company.getTaxNo());

        if (!StringUtils.hasText(company.getPhone()))
            throw new BusinessServiceException(String.format("【公司电话】未维护 %s", companyName));
        companyModel.setPhone(company.getPhone());

        return companyModel;

    }


    /**
     * 构建供应商合同渲染对象
     *
     * @param supplierInfo
     * @return
     */
    private StockoutCustomsDeclareContractRenderModelCompany buildSupplierModel(SupplierTaxGetSampleDatailDto supplierInfo, String supplierName) {
        StockoutCustomsDeclareContractRenderModelCompany supplierModel = new StockoutCustomsDeclareContractRenderModelCompany();
        if (!StringUtils.hasText(supplierInfo.getInvoiceCompanyName()))
            throw new BusinessServiceException(String.format("【供应商公司名称】未维护 %s", supplierName));
        supplierModel.setCompanyName(supplierInfo.getInvoiceCompanyName());
        supplierModel.setPhone(supplierInfo.getPhone());

        if (!StringUtils.hasText(supplierInfo.getTaxNo()))
            throw new BusinessServiceException(String.format("【供应商纳税人识别号】未维护 %s", supplierName));
        supplierModel.setCode(supplierInfo.getTaxNo());

        if (!StringUtils.hasText(supplierInfo.getBank()))
            throw new BusinessServiceException(String.format("【供应商开户行】未维护 %s", supplierName));
        supplierModel.setBank(supplierInfo.getBank());

        if (!StringUtils.hasText(supplierInfo.getBankAccount()))
            throw new BusinessServiceException(String.format("【供应商开户行账号】未维护 %s", supplierName));
        supplierModel.setAccount(supplierInfo.getBankAccount());

        if (!StringUtils.hasText(supplierInfo.getAddress()))
            throw new BusinessServiceException(String.format("【供应商地址】未维护 %s", supplierName));
        supplierModel.setAddr(supplierInfo.getAddress());
        return supplierModel;
    }

    /**
     * 获取最早的一期签约日期
     * 通过合同找关单，通过关单找报关单据，拿到签约日期
     *
     * @param formList 关单列表
     * @return 最早的签约日期
     */
    private Date getEarliestSignDate(List<StockoutCustomsDeclareFormEntity> formList) {
        // 通过关单找到对应的报关单据，从报关单据中获取签约日期
        List<Date> signingDates = formList.stream()
                .map(form -> {
                    // 通过关单的协议号(protocolNo)获取报关单据
                    StockoutCustomsDeclareDocumentEntity document =
                            stockoutCustomsDeclareDocumentService.findByDeclareDocumentNo(form.getProtocolNo());
                    return document.getSigningDate();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (signingDates.isEmpty()) {
            throw new BusinessServiceException("签约日期获取不到");
        }

        // 返回最早的签约日期
        return signingDates.stream().min(Date::compareTo).orElse(new Date());
    }
}
