package com.nsy.api.wms.constants;

import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 盘点单常量
 *
 * <AUTHOR>
 * @date 2023/5/17 13:58
 */
public class StockTakeOrderConstant {

    /**
     * 符合进行盘点的库位类型
     */
    public static final List<String> STOCK_TAKE_ORDER_POSITION_TYPE = Collections.unmodifiableList(
            Arrays.asList(BdPositionTypeEnum.STOCK_POSITION.name(), BdPositionTypeEnum.SPARE_POSITION.name(), BdPositionTypeEnum.CROSS_POSITION.name(),
                    BdPositionTypeEnum.RETURN_POSITION.name(), BdPositionTypeEnum.ACTIVITY_POSITION.name(), BdPositionTypeEnum.OEM_POSITION.name(),
                    BdPositionTypeEnum.STORE_POSITION.name()));

}
