package com.nsy.wms.controller.stockin;

import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.StockinOrderTask;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItemDetail;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskSkuItem;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskSpot;
import com.nsy.api.wms.domain.supplier.DeliveryBoxStickerDto;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.feign.StockinOrderTaskClient;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxEditRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxFullRequest;
import com.nsy.api.wms.request.stock.StockinOrderTaskItemChangePlanRemarkRequest;
import com.nsy.api.wms.request.stockin.ModifySupplierDeliverySellerInfoRequest;
import com.nsy.api.wms.request.stockin.PrintSkuRequest;
import com.nsy.api.wms.request.stockin.QueryPurchaseOrderInfoBySkuRequest;
import com.nsy.api.wms.request.stockin.QueryPurchaseOrderInfoRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSetRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSpotDirectShelveListRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSpotSetListRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskCompleteRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskItemListRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskListRequest;
import com.nsy.api.wms.request.stockin.StockinSpotTaskListRequest;
import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockin.ModifySupplierDeliverySellerInfoResponse;
import com.nsy.api.wms.response.stockin.QueryPurchaseOrderEarliestReceiptDateResponse;
import com.nsy.api.wms.response.stockin.StatusStatisticsResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTaskBaseInfoResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTaskItemListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTaskListResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryListResponse;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskReceiveService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskSearchService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskSpotDirectShelveService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskSpotService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * Controller层
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "入库任务相关接口")
@RestController
public class StockinOrderTaskController extends BaseController implements StockinOrderTaskClient {

    @Autowired
    private StockinOrderService stockinOrderService;
    @Inject
    private StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    private GcApiService gcApiService;
    @Inject
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Inject
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockinOrderTaskItemService taskItemService;
    @Autowired
    private StockinOrderTaskSpotService stockinOrderTaskSpotService;
    @Autowired
    private StockinOrderTaskSearchService stockinOrderTaskSearchService;
    @Autowired
    private StockinOrderTaskSpotDirectShelveService stockinOrderTaskSpotDirectShelveService;
    @Autowired
    StockinOrderTaskReceiveService stockinOrderTaskReceiveService;
    @Autowired
    private ExternalApiLogService externalApiLogService;

    // 收货确认查询提示信息
    @RequestMapping(value = "/stockin-order-task/prompt-information/{taskId}/{barcode}", method = RequestMethod.GET)
    @ApiOperation(value = "收货确认查询提示信息", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "入库任务主键ID", dataType = "Integer", paramType = "path"), @ApiImplicitParam(name = "barcode", value = "商品条形码", dataType = "String", paramType = "path")})
    public List<String> getPromptInformation(@Valid @PathVariable Integer taskId, @Valid @PathVariable String barcode) {
        return stockinOrderTaskService.getPromptInformation(taskId, barcode);
    }

    // 查询入库任务(出库箱码)
    @RequestMapping(value = "/stockin-order-task/box-code/{supplierDeliveryBoxCode}", method = RequestMethod.GET)
    @ApiOperation(value = "查询入库任务(出库箱码)", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "supplierDeliveryBoxCode", value = "出库箱码", dataType = "String", paramType = "path")})
    public StockinOrderTask getBySupplierDeliveryBoxCode(@Valid @PathVariable String supplierDeliveryBoxCode) {
        return stockinOrderTaskService.getBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
    }

    // 查询条形码
    @RequestMapping(value = "/stockin-order-task/box-code/{supplierDeliveryBoxCode}/{barcode}", method = RequestMethod.GET)
    @ApiOperation(value = "查询条形码", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "supplierDeliveryBoxCode", value = "出库箱码", dataType = "String", paramType = "path"), @ApiImplicitParam(name = "barcode", value = "商品条形码", dataType = "String", paramType = "path")})
    public StockinOrderTaskSkuItem queryBarcode(@Valid @PathVariable String supplierDeliveryBoxCode, @Valid @PathVariable String barcode) {
        return stockinOrderService.queryBarcode(supplierDeliveryBoxCode, barcode);
    }

    // 查询入库任务(物流单号)
/*    @RequestMapping(value = "/stockin-order-task/logistics-no/{logisticsNo}", method = RequestMethod.GET)
    @ApiOperation(value = "查询入库任务(物流单号)", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "logisticsNo", value = "物流单号", dataType = "String", paramType = "path")})
    public List<StockinOrderTask> getByLogisticsNo(@Valid @PathVariable String logisticsNo) {
        return stockinOrderTaskService.getByLogisticsNo(logisticsNo);
    }*/


    @RequestMapping(value = "/stockin-order-task-spot-list", method = RequestMethod.POST)
    @ApiOperation(value = "查询现货入库任务", produces = "application/json")
    public StockinOrderTaskSpot getByLogisticsNo(@Valid @RequestBody StockinSpotTaskListRequest request) {
        return stockinOrderTaskSpotService.getByLogisticsNo(request);
    }

    // 查询内部箱
    @RequestMapping(value = "/internal-box/{internalBoxCode}", method = RequestMethod.GET)
    @ApiOperation(value = "查询内部箱", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "internalBoxCode", value = "内部箱号", dataType = "String", paramType = "path")})
    public StockInternalBox getInternalBox(@Valid @PathVariable String internalBoxCode) {
        return stockinOrderTaskService.getInternalBox(internalBoxCode);
    }

    // 修改内部箱状态
    @RequestMapping(value = "/internal-box/{internalBoxCode}", method = RequestMethod.PUT)
    @ApiOperation(value = "修改内部箱状态", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "internalBoxCode", value = "内部箱号", dataType = "String")})
    public void editInternalBoxStatus(@Valid @PathVariable String internalBoxCode, @Valid @RequestBody StockinInternalBoxEditRequest request) {
        stockInternalBoxService.changeStockInternalBoxStatus(internalBoxCode, request.getStatus());
    }

    // 满箱确认
    @RequestMapping(value = "/internal-box/full/{internalBoxCode}", method = RequestMethod.PUT)
    @ApiOperation(value = "满箱确认", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "internalBoxCode", value = "内部箱号", dataType = "String")})
    public void fullInternalBox(@Valid @PathVariable String internalBoxCode, @Valid @RequestBody StockinInternalBoxFullRequest request) {
        stockInternalBoxService.fullInternalBox(internalBoxCode, request);
    }

    // 数量调整
    @RequestMapping(value = "/stockin-order-task/modify/{taskId}", method = RequestMethod.PUT)
    @ApiOperation(value = "数量调整", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "Integer", value = "入库任务ID", dataType = "Integer", paramType = "path")})
    public void editQty(@Valid @PathVariable Integer taskId, @Valid @RequestBody StockinOrderTaskItemDetail request) {
        if (request.getRealQty() != null) {
            request.setQty(request.getRealQty());
        }
        stockinOrderTaskService.editSkuQty(taskId, request);
    }

    @RequestMapping(value = "/stockin-order-task-item/modify-plan-remark", method = RequestMethod.PUT)
    @ApiOperation(value = "计划单备注修改", produces = "application/json")
    public void editPlanRemark(@Valid @RequestBody StockinOrderTaskItemChangePlanRemarkRequest request) {
        stockinOrderTaskItemService.editPlanRemark(request);
    }

    // 工厂入库任务sku信息录入
    @RequestMapping(value = "/stockin-order-task/sku/{taskId}", method = RequestMethod.PUT)
    @ApiOperation(value = "工厂入库任务sku信息录入", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "入库任务主键ID", dataType = "Integer")})
    public void stockinSkuSet(@Valid @PathVariable Integer taskId, @Valid @RequestBody StockinOrderItemSetRequest stockinOrderItemSetRequest) {
        stockinOrderService.stockinOrderItemSet(taskId, stockinOrderItemSetRequest, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK);
    }

    // 现货入库任务sku信息录入
/*    @RequestMapping(value = "/stockin-order-task-spot/sku/{taskId}", method = RequestMethod.PUT)
    @ApiOperation(value = "现货入库任务sku信息录入", produces = "application/json")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "入库任务主键ID", dataType = "Integer")})
    public void spotSkuSet(@Valid @PathVariable Integer taskId, @Valid @RequestBody StockinOrderItemSetListRequest listRequest) {
        listRequest.getSkuList().forEach(request -> stockinOrderService.stockinOrderItemSet(taskId, request, StockConstant.STOCKIN_ORDER_NO_XHRK));
    }*/

    @RequestMapping(value = "/stockin-order-task-spot/sku", method = RequestMethod.PUT)
    @ApiOperation(value = "现货入库任务sku信息录入", produces = "application/json")
    public void spotSkuSet(@Valid @RequestBody StockinOrderItemSpotSetListRequest listRequest) {
        stockinOrderTaskSpotService.stockinSkuPut(listRequest);

    }

    // 市场上架
    @RequestMapping(value = "/stockin-order-task-spot/direct-shelve", method = RequestMethod.PUT)
    @ApiOperation(value = "市场上架", produces = "application/json")
    public void directShelve(@RequestBody StockinOrderItemSpotDirectShelveListRequest directShelveListRequest) {
        stockinOrderTaskSpotDirectShelveService.directShelve(directShelveListRequest);
    }


    // 入库收货确认
    @RequestMapping(value = "/stockin-order-task", method = RequestMethod.POST)
    @ApiOperation(value = "工厂入库收货确认", produces = "application/json")
    public void receiveComplete(@Valid @RequestBody StockinOrderTaskCompleteRequest stockinOrderTaskCompleteRequest) {
        stockinOrderTaskReceiveService.receiveComplete(stockinOrderTaskCompleteRequest);
    }

    // 现货入库收货确认
    @RequestMapping(value = "/stockin-order-task-spot-complete", method = RequestMethod.POST)
    @ApiOperation(value = "现货入库收货确认", produces = "application/json")
    public void receiveCompleteSpot(@Valid @RequestBody StockinSpotTaskListRequest request) {
        stockinOrderTaskSpotService.receiveCompleteSpot(request);
    }

    // 入库任务状态总数统计
    @RequestMapping(value = "/stockin-order-task/status-statistics", method = RequestMethod.GET)
    @ApiOperation(value = "入库任务状态总数统计", produces = "application/json")
    public StatusStatisticsResponse statusStatistics() {
        return stockinOrderTaskService.statusStatistics();
    }

    // 入库任务列表
    @RequestMapping(value = "/stockin-order-task/page-list", method = RequestMethod.POST)
    @ApiOperation(value = "入库任务列表", produces = "application/json")
    public PageResponse<StockinOrderTaskListResponse> pageList(@RequestBody StockinOrderTaskListRequest request) {
        return stockinOrderTaskService.pageList(request);
    }

    // 工厂出库单列表
    @RequestMapping(value = "/stockin-order-task/page-supplier-delivery", method = RequestMethod.POST)
    @ApiOperation(value = "工厂出库单列表", produces = "application/json")
    public PageResponse<StockinSupplierDeliveryListResponse> pageSupplierDeliveryNoList(@RequestBody StockinSupplierDeliveryListRequest request) {
        return stockinOrderTaskSearchService.pageSupplierDeliveryNoList(request);
    }

    // 入库任务基础信息
    @RequestMapping(value = "/stockin-order-task/base-info/{taskId}", method = RequestMethod.GET)
    @ApiOperation(value = "入库任务基础信息", produces = "application/json")
    public StockinOrderTaskBaseInfoResponse baseInfo(@PathVariable Integer taskId) {
        return stockinOrderTaskService.baseInfo(taskId);
    }

    // 入库任务基础信息
    @RequestMapping(value = "/stockin-order-task/base-info-box-code/{supplierDeliveryBoxCode}", method = RequestMethod.GET)
    @ApiOperation(value = "入库任务基础信息-出库箱码", produces = "application/json")
    public StockinOrderTaskBaseInfoResponse baseInfoBoxCode(@PathVariable String supplierDeliveryBoxCode) {
        return stockinOrderTaskService.baseInfoBoxCode(supplierDeliveryBoxCode);
    }

    // 入库任务item列表
    @RequestMapping(value = "/stockin-order-task/item-list", method = RequestMethod.POST)
    @ApiOperation(value = "入库任务item信息", produces = "application/json")
    public PageResponse<StockinOrderTaskItemListResponse> itemList(@RequestBody StockinOrderTaskItemListRequest request) {
        return stockinOrderTaskItemService.pageList(request);
    }

    // 入库任务打印
    @RequestMapping(value = "/stockin-order-task/detail/print", method = RequestMethod.POST)
    @ApiOperation(value = "入库任务明细打印", produces = "application/json")
    public PrintListResponse printDetail(@Valid @RequestBody IdListRequest idList) {
        return stockinOrderTaskService.printDetail(idList);
    }

    @ApiOperation(value = "出库箱码打印", produces = "application/json")
    @RequestMapping(value = "/stockin-order-task/print-box-no", method = RequestMethod.POST)
    public PrintListResponse printSupplierDeliveryBoxCode(@RequestParam(required = false) Boolean singlePrint, @Valid @RequestBody IdListRequest idListRequest) {
        return stockinOrderTaskService.printSupplierDeliveryBoxCode(idListRequest, singlePrint);
    }

    @ApiOperation(value = "商品条码打印", produces = "application/json")
    @RequestMapping(value = "/stockin-order-task/print-sku/{taskId}", method = RequestMethod.POST)
    public PrintListResponse printStockinTaskSku(@PathVariable Integer taskId, @RequestBody PrintSkuRequest printSkuRequest) {
        return taskItemService.printStockinTaskSku(taskId, printSkuRequest);
    }

    // erp 生成接收单
    @ApiOperation(value = " erp 生成接收单", produces = "application/json")
    @RequestMapping(value = "/stockin-order-task/generate-receiving-order/{supplierDeliveryBoxCode}/{checkUnCollect}", method = RequestMethod.PUT)
    public void printStockinTaskSku(@PathVariable String supplierDeliveryBoxCode, @PathVariable Integer checkUnCollect) {
        stockinOrderTaskService.generatePurchaseReceivingOrder(supplierDeliveryBoxCode, checkUnCollect.equals(1), new Date(), StockinTypeEnum.FACTORY.name());
    }

    // 根据现货入库任务生成月台任务
    @RequestMapping(value = "/stockin-order-task/generatePlatform", method = RequestMethod.POST)
    @ApiOperation(value = "根据现货入库任务生成月台任务", produces = "application/json")
    public void generatePlatform(@Valid @RequestBody IdListRequest idList) {
        stockinOrderTaskSpotService.generatePlatform(idList);
    }

    @ApiOperation(value = "打印出库箱码(sku)", notes = "打印出库箱码")
    @GetMapping("/delivery-print-box-sticker-by-sku/{supplierDeliveryNo}/{boxIndex}")
    public List<DeliveryBoxStickerDto> deliveryPrintBoxStickerBySku(@PathVariable("supplierDeliveryNo") String supplierDeliveryNo, @PathVariable("boxIndex") Integer boxIndex) {
        return gcApiService.deliveryPrintBoxStickerBySku(supplierDeliveryNo, boxIndex);
    }

    @Override
    public List<QueryPurchaseOrderEarliestReceiptDateResponse> queryEarliestReceiptDateByPurchasePlanNo(@Valid @RequestBody QueryPurchaseOrderInfoRequest request) {
        return stockinOrderTaskItemService.queryEarliestReceiptDateByPurchasePlanNo(request);
    }

    @Override
    public List<QueryPurchaseOrderEarliestReceiptDateResponse> queryEarliestReceiptDateByPurchasePlanNoBySku(List<QueryPurchaseOrderInfoBySkuRequest> requests) {
        return stockinOrderTaskItemService.queryEarliestReceiptDateByPurchasePlanNoBySku(requests);
    }

    @Override
    public void spotForcedCompleted(String purchasePlanNo) {
        stockinOrderTaskSpotService.spotForcedCompleted(purchasePlanNo);
    }

    @Override
    public ModifySupplierDeliverySellerInfoResponse modifySellerInfo(@Valid ModifySupplierDeliverySellerInfoRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SUPPLIER_DELIVERY_MODIFY_SELLER_INFO, "/stockin-order-task/modify-seller-info",
            JsonMapper.toJson(request), request.getSupplierDeliveryNo(), "更新sellerSku");
        ModifySupplierDeliverySellerInfoResponse response;
        try {
            response = stockinOrderTaskItemService.modifySellerInfo(request);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
        return response;
    }
}
