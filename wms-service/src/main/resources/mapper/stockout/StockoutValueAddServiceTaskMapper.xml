<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutValueAddServiceTaskMapper">
    <select id="pageSearchList" resultType="com.nsy.api.wms.domain.stockout.StockoutVasTaskList">
        SELECT
        t.id,
        t.stockout_order_no,
        t.workspace,
        t.business_type,
        t.vas_qty,
        t.vas_type,
        t.`status`,
        t.update_by AS updateBy,
        t.create_date AS createDate,
        t.update_date AS updateDate,
        t.vas_price AS vasPrice
        FROM
        stockout_value_add_service_task t
        <include refid="whereCondition"></include>
    </select>

    <select id="pageStatistics" resultType="com.nsy.api.wms.response.stockout.StockoutVasTaskCountResponse">
        select ifnull(sum(t.vas_qty),0) as vasTaskQtyTotal
        FROM
        stockout_value_add_service_task t
        <include refid="whereCondition"></include>
    </select>
    
    <select id="getVasTypeInfo" resultType="com.nsy.wms.business.domain.dto.stockout.StockoutStorePositionVasTaskDTO">
        select
        item.sku as sku,
        group_concat(distinct item.vas_type) as vasType
        FROM stockout_value_add_service_task t
        inner join stockout_value_add_service_task_item item on t.id = item.task_id
        where t.stockout_order_no in
        <foreach item="stockoutOrderNo" collection="stockoutOrderNoList" separator="," index="index" open="("
                 close=")">
            #{stockoutOrderNo}
        </foreach>
        group by item.sku
    </select>

    <sql id="whereCondition">
        <where>
            <if test="query!=null and query.status != null and query.status !=''">
                and t.status = #{query.status}
            </if>
            <if test="query!=null and query.stockoutOrderNo != null and query.stockoutOrderNo !=''">
                and t.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query!=null and query.workspace != null and query.workspace !=''">
                and t.workspace = #{query.workspace}
            </if>
            <if test="query!=null and query.createDateStart!=null">
                and t.create_date &gt;= #{query.createDateStart}
            </if>
            <if test="query!=null and query.createDateEnd!=null">
                and t.create_date &lt; #{query.createDateEnd}
            </if>
            <if test="query!=null and query.updateBy != null and query.updateBy !=''">
                and t.update_by = #{query.updateBy}
            </if>
            <if test="query!=null and query.orderNo != null and query.orderNo !=''">
                AND t.id in (select distinct item.task_id from stockout_value_add_service_task_item item where
                item.order_no = #{query.orderNo})
            </if>
            <if test="query!=null and query.vasType != null and query.vasType !=''">
                and t.vas_type like concat('%',#{query.vasType},'%')
            </if>
            <if test="query!=null and query.idList!=null">
                and t.id in
                <foreach collection="query.idList" separator="," index="index" item="id" open="("
                         close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </sql>
</mapper>
