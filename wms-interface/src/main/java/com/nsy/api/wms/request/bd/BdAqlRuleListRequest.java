package com.nsy.api.wms.request.bd;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("AQL规则列表查询请求")
public class BdAqlRuleListRequest extends PageRequest {

    @ApiModelProperty("仓库id")
    private Integer spaceId;
    
    private List<Integer> spaceIdList;

    @ApiModelProperty(value = "区间从", name = "rangeFrom")
    private Integer rangeFrom;

    @ApiModelProperty(value = "区间到", name = "rangeTo")
    private Integer rangeTo;
    
    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getRangeFrom() {
        return rangeFrom;
    }

    public void setRangeFrom(Integer rangeFrom) {
        this.rangeFrom = rangeFrom;
    }

    public Integer getRangeTo() {
        return rangeTo;
    }

    public void setRangeTo(Integer rangeTo) {
        this.rangeTo = rangeTo;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }
} 