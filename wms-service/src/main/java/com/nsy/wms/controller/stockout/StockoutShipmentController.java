package com.nsy.wms.controller.stockout;


import cn.hutool.core.date.DateUtil;
import com.nsy.api.pms.dto.response.BaseStringResponse;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.domain.stockout.ShipmentFedexInsteadShippedExport;
import com.nsy.api.wms.domain.stockout.StockoutShipmentModel;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchCount;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.bd.NoListRequest;
import com.nsy.api.wms.request.stockout.FbaShipmentLabelRequest;
import com.nsy.api.wms.request.stockout.ForwarderChannelDeliveryRequest;
import com.nsy.api.wms.request.stockout.OrderShipmentRequest;
import com.nsy.api.wms.request.stockout.PrintKjpBoxRequest;
import com.nsy.api.wms.request.stockout.ScanPrintRequest;
import com.nsy.api.wms.request.stockout.ShipmentFedexInsteadShippedDownloadRequest;
import com.nsy.api.wms.request.stockout.StaBoxInfoDetailRequest;
import com.nsy.api.wms.request.stockout.StaBoxInfoRequest;
import com.nsy.api.wms.request.stockout.StockoutFbaLabelStatusRequest;
import com.nsy.api.wms.request.stockout.StockoutSearchReplenishRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentGetShipDateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSyncRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUploadLabelRequest;
import com.nsy.api.wms.request.stockout.StockoutTCodeSkuPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutUpdateLogisticsNoRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.request.stockout.TCodeIndexPrintRequest;
import com.nsy.api.wms.request.stockout.TmsNotifyPackageExceptionRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.FbaPrintRequest;
import com.nsy.api.wms.response.stockout.StaShipmentRelationResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderLabelResponse;
import com.nsy.api.wms.response.stockout.StockoutReplenishResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentCopyRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentCountResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentLogisticsRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentShipDateResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentTransparencyCodeResponse;
import com.nsy.wms.business.manage.amazon.dto.StaShipmentBoxRelationMessage;
import com.nsy.wms.business.manage.amazon.response.StaBoxInfoDetailResponse;
import com.nsy.wms.business.manage.amazon.response.StaBoxInfoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.stockout.UPSEdsService;
import com.nsy.wms.business.service.internal.ErpApiInternalService;
import com.nsy.wms.business.service.stockout.StockoutFbaReplenishOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderShipService;
import com.nsy.wms.business.service.stockout.StockoutShipmentAmazonRelationService;
import com.nsy.wms.business.service.stockout.StockoutShipmentCustomsService;
import com.nsy.wms.business.service.stockout.StockoutShipmentDownloadService;
import com.nsy.wms.business.service.stockout.StockoutShipmentErpPickingBoxService;
import com.nsy.wms.business.service.stockout.StockoutShipmentInfoService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentOperateService;
import com.nsy.wms.business.service.stockout.StockoutShipmentPrintService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.StockoutTransparencyCodeService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.mp.TenantContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 装箱清单
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@Api(tags = "装箱清单")
public class StockoutShipmentController extends BaseController {

    @Autowired
    private StockoutShipmentService stockoutShipmentService;
    @Autowired
    private StockoutShipmentDownloadService downloadService;
    @Autowired
    private StockoutOrderLabelService labelService;
    @Autowired
    private StockoutShipmentPrintService shipmentPrintService;
    @Autowired
    private StockoutShipmentItemService shipmentItemService;
    @Autowired
    private StockoutShipmentInfoService stockoutShipmentInfoService;
    @Autowired
    StockoutShipmentErpPickingBoxService shipmentErpPickingBoxService;
    @Autowired
    StockoutShipmentAmazonRelationService syncAmazonService;
    @Autowired
    StockoutOrderShipService orderShipService;
    @Autowired
    ErpApiInternalService erpApiInternalService;
    @Autowired
    StockoutShipmentInfoService shipmentInfoService;
    @Autowired
    StockoutShipmentCustomsService stockoutShipmentCustomsService;
    @Resource
    StockoutShipmentOperateService stockoutShipmentOperateService;
    @Resource
    UPSEdsService upsEdsService;
    @Resource
    StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutFbaReplenishOrderService stockoutFbaReplenishOrderService;

    @RequestMapping(value = "/stockout-shipment/{id}", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-根据id获取详情", produces = "application/json")
    public StockoutShipmentResponse get(@PathVariable("id") Integer id) {
        return stockoutShipmentService.get(id);
    }


    @RequestMapping(value = "/stockout-shipment/shipmentBoxCode/{shipmentBoxCode}", method = RequestMethod.GET)
    @ApiOperation(value = "装箱清单-根据shipmentBoxCode获取详情", produces = "application/json")
    public StockoutShipmentResponse getByShipmentBoxCode(@PathVariable("shipmentBoxCode") String shipmentBoxCode) {
        return stockoutShipmentService.getByShipmentBoxCode(shipmentBoxCode);
    }

    @RequestMapping(value = "/stockout-shipment/info", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-修改", produces = "application/json")
    public void update(@Valid @RequestBody StockoutShipmentUpdateRequest request) {
        stockoutShipmentService.updateStockoutShipment(request);
    }

    @RequestMapping(value = "/stockout-shipment/list", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-列表", produces = "application/json")
    public PageResponse<StockoutShipmentSearchResult> searchList(@Valid @RequestBody StockoutShipmentSearchRequest request) {
        return stockoutShipmentService.searchList(request);
    }

    @RequestMapping(value = "/stockout-shipment/page-search-count", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-列表统计", produces = "application/json")
    public StockoutShipmentSearchCount pageSearchCount(@Valid @RequestBody StockoutShipmentSearchRequest request) {
        return stockoutShipmentInfoService.pageSearchCount(request);
    }


    /**
     * 校验通过后加载源箱的SKU列表信息
     * 若源箱号的状态【已发货】则不允许调拨，同时提示：箱号已经发货，无法调拨
     * 输入后回车查询装箱清单中该箱号是否存在，若不存在，则提示：源箱号不存在
     *
     * <AUTHOR>
     * 2021-08-20
     */
    @RequestMapping(value = "/stockout-shipment/transfer/{shipmentBoxCode}", method = RequestMethod.GET)
    @ApiOperation(value = "装箱清单-调拨-根据箱子编号shipmentBoxCode获取sku详情列表", produces = "application/json")
    public StockoutShipmentItemSkuInfoResponse getTransferBoxSkuInfo(@PathVariable("shipmentBoxCode") String shipmentBoxCode, @RequestParam Integer isOriginBox) {
        return stockoutShipmentService.getSkuListByShipmentBoxCode(shipmentBoxCode, isOriginBox);
    }

    @RequestMapping(value = "/stockout-shipment/transfer", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-调拨-确认调拨", produces = "application/json")
    public void transferShipmentBox(@Valid @RequestBody StockoutShipmentItemSkuInfoRequest request) {
        stockoutShipmentService.transferShipmentBox(request);
    }

    @RequestMapping(value = "/stockout-shipment/empty", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-删除空箱", produces = "application/json")
    public void deleteEmptyBox(@Valid @RequestBody StockoutShipmentRequest request) {
        stockoutShipmentService.deleteEmptyBox(request);
    }

    @RequestMapping(value = "/stockout-shipment/logistics", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-一键修改物流", produces = "application/json")
    public void changeLogistics(@Valid @RequestBody StockoutShipmentLogisticsRequest request) {
        stockoutShipmentService.changeLogistics(request);
    }

    @RequestMapping(value = "/stockout-shipment/get-same-shipment", method = RequestMethod.POST)
    @ApiOperation(value = "通过箱号查询装箱清单-包含该出库单的装箱", produces = "application/json")
    public List<StockoutShipmentModel> getShipmentsByBoxCode(@Valid @RequestBody IdListRequest request) {
        return shipmentItemService.getShipmentsByBoxCode(request);
    }

    @RequestMapping(value = "/stockout-shipment/selfGetLogistics", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-自提发货", produces = "application/json")
    public void selfGetLogistics(@Valid @RequestBody StockoutShipmentLogisticsRequest request) {
        stockoutShipmentService.selfGetLogistics(request);
    }

    @RequestMapping(value = "/stockout-shipment/async/self-get-logistics", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-自提发货(异步)", produces = "application/json")
    public AsyncProcessFlowResult asyncSelfGetLogistics(@Valid @RequestBody StockoutShipmentLogisticsRequest request) {
        return stockoutShipmentOperateService.asyncSelfGetLogistics(request);
    }

    @RequestMapping(value = "/stockout-shipment/fedex-instead-shipped-download", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-导出fedex代发货信息", produces = "application/json")
    public List<ShipmentFedexInsteadShippedExport> shipmentFedexInsteadShipped(@Valid @RequestBody ShipmentFedexInsteadShippedDownloadRequest request) {
        return downloadService.shipmentInsteadShipped(request, 1, 1000);
    }

    @ApiOperation(value = "打印60*40条形码", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-6040-barcode", method = RequestMethod.POST)
    public PrintListResponse printBarcode(@Valid @RequestBody IdListRequest idListRequest) {
        return shipmentPrintService.print6040Barcode(idListRequest.getIdList());
    }

    @ApiOperation(value = "打印80*40条形码", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-8040-barcode", method = RequestMethod.POST)
    public PrintListResponse print8040Barcode(@Valid @RequestBody StringListRequest request) {
        return shipmentPrintService.print8040Barcode(request.getStringList());
    }

    @RequestMapping(value = "/stockout-shipment/small-package-print", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-打印-小包打印", produces = "application/json")
    public PrintListResponse smallPackagePrint(@Valid @RequestBody StringListRequest stockoutOrderNos) {
        return labelService.printByStockOutOrderNo(stockoutOrderNos.getStringList());
    }

    @RequestMapping(value = "/stockout-shipment/small-package-re-print", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-打印-小包重新打印", produces = "application/json")
    public PrintListResponse smallPackageRePrint(@Valid @RequestBody IdListRequest request) {
        return labelService.smallPackageRePrint(request.getIdList());
    }

    @RequestMapping(value = "/stockout-shipment/label/print-by-shipment", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-货代发货", produces = "application/json")
    public PrintListResponse printLabelByShipment(@Valid @RequestBody ForwarderChannelDeliveryRequest request) {
        return labelService.printLabelByShipment(request);
    }

    @RequestMapping(value = "/async/stockout-shipment/label/print-by-shipment", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-货代发货(异步)", produces = "application/json")
    public AsyncProcessFlowResult asyncPrintLabelByShipment(@Valid @RequestBody ForwarderChannelDeliveryRequest request) {
        request.setShipmentIdString(request.getShipmentIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        return labelService.asyncGetChannelLabel(request);
    }

    @RequestMapping(value = "/stockout-shipment/re-get-label", method = RequestMethod.POST)
    @ApiOperation(value = "重新获取面单", produces = "application/json")
    public List<String> reGetLabel(@Valid @RequestBody StringListRequest stockoutOrderNos, @RequestParam(required = false) Boolean forceReGetLabel) {
        return labelService.reGetLabel(stockoutOrderNos.getStringList(), forceReGetLabel == null ? Boolean.FALSE : forceReGetLabel);
    }

    @ApiOperation(value = "装箱清单列表-打印装箱清单", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-shipment-list", method = RequestMethod.POST)
    public PrintListResponse printShipmentList(@Valid @RequestBody StockoutShipmentPrintRequest request) {
        return shipmentPrintService.printShipmentList(request);
    }

    @ApiOperation(value = "装箱清单列表-打印订单", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-order", method = RequestMethod.POST)
    public PrintListResponse printOrder(@Valid @RequestBody NoListRequest request) {
        return shipmentPrintService.printOrder(request);
    }

    @ApiOperation(value = "发货单-发货单打印", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-shipment-order", method = RequestMethod.POST)
    public PrintListResponse printStockoutOrderByShipment(@Valid @RequestBody OrderShipmentRequest request) {
        return shipmentPrintService.printStockoutOrderByShipment(request);
    }

    @ApiOperation(value = "打印菜鸟物流", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-cainiao-label", method = RequestMethod.POST)
    public StockoutOrderLabelResponse printShipmentLabel(@Valid @RequestBody NoListRequest request) {
        return shipmentPrintService.printShipmentLabel(request);
    }

    @ApiOperation(value = "打印fba条码(根据装箱清单)", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-fba-barcode/print-shipment-fba", method = RequestMethod.POST)
    public PrintListResponse printShipmentFba(@Valid @RequestBody FbaPrintRequest request) {
        if (!StringUtils.hasText(request.getTemplateName())) {
            request.setTemplateName(PrintTemplateNameEnum.SHIPMENT_FBA_BARCODE.getTemplateName());
        }
        return shipmentPrintService.printShipmentFba(request);
    }

    @ApiOperation(value = "查询装箱状态数量", notes = "查询装箱状态数量", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/tab-count", method = RequestMethod.GET)
    public List<StockoutShipmentCountResponse> stockoutShipmentTabCount() {
        return StockoutBuilding.stockoutShipmentTabCount();
    }

    @ApiOperation(value = "打印钰真条码", notes = "打印钰真条码", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-yu-zhen", method = RequestMethod.POST)
    public PrintListResponse printYuZhen(@Valid @RequestBody IdListRequest request) {
        return shipmentPrintService.printYuZhen(request);
    }

    @ApiOperation(value = "打印面单", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-label", method = RequestMethod.POST)
    public Map<String, List<String>> getLogisticsLabelUrl(@Valid @RequestBody NoListRequest request) {
        return shipmentPrintService.getLogisticsLabelUrl(request);
    }

    @ApiOperation(value = "打印外部面单", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-out-label", method = RequestMethod.POST)
    public PrintListResponse printOutLabelByLogisticsCompany(@Valid @RequestBody IdListRequest request, @RequestParam(required = false) String logisticsCompany) {
        return shipmentPrintService.printOutLabelByLogisticsCompany(request, logisticsCompany);
    }


    @ApiOperation(value = "同步装箱清单-手动同步", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/sync-shipments-internal", method = RequestMethod.POST)
    public void syncShipmentsByTime(@Valid @RequestBody StockoutShipmentSyncRequest request) {
        shipmentErpPickingBoxService.syncShipmentsInternal(request);
    }

    @ApiOperation(value = "手动同步amazon关系", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/sync-amazon-shipments-internal", method = RequestMethod.POST)
    public void syncAmazonRelation(@Valid @RequestBody StaShipmentBoxRelationMessage messageContent) {
        syncAmazonService.syncAmazonRelation(messageContent);
    }

    @ApiOperation(value = "获取单号的发货日期", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/order/delivery-date", method = RequestMethod.POST)
    public StockoutShipmentShipDateResponse shipmentShipDate(@Valid @RequestBody StockoutShipmentGetShipDateRequest request) {
        return stockoutShipmentInfoService.shipmentShipDate(request);
    }

    @ApiOperation(value = "获取当前出库单底下所有的FbaShipmentId", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/stockout-order/fba-shipment-id", method = RequestMethod.GET)
    public Map<String, Long> getFbaShipmentId(@RequestParam(required = false) String orderNo) {
        return syncAmazonService.getFbaShipmentId(orderNo);
    }

    @ApiOperation(value = "批量获取当前出库单底下所有的FbaShipmentId", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/stockout-order/batch/fba-shipment-id", method = RequestMethod.POST)
    public Map<String, Long> getBatchFbaShipmentId(@Valid @RequestBody IdListRequest request) {
        return syncAmazonService.getBatchFbaShipmentId(request.getIdList());
    }

    @ApiOperation(value = "根据订单号 获取shipmentId及其仓库地址", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/stockout-order/fba-shipment", method = RequestMethod.GET)
    public List<StaShipmentRelationResponse> getFbaShipment(@RequestParam String orderNo) {
        return syncAmazonService.getFbaShipment(orderNo);
    }

    @ApiOperation(value = "亚马逊同步装箱失败重试", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/internal/retry/sync-amazon-shipments", method = RequestMethod.POST)
    public StaBoxInfoResponse syncAmazonRelationRetry(@RequestBody StaBoxInfoRequest request) {
        return syncAmazonService.syncByErpTid(request);
    }

    @ApiOperation(value = "内部-同步拣货单拣货完成", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/internal/sync-erp-pick-finish", method = RequestMethod.POST)
    public void syncErpPickFinish(@Valid @RequestBody NoListRequest request) {
        erpApiInternalService.syncErpPickFinish(request);
    }

    @ApiOperation(value = "内部-同步拣货单扫描数", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/internal/sync-erp-pick-scan-qty", method = RequestMethod.POST)
    public void syncErpPickScanQty(@Valid @RequestBody NoListRequest request) {
        erpApiInternalService.syncUpdateErpPickScanQty(request);
    }

    @ApiOperation(value = "打印谷仓条码(根据装箱清单)", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-good-cang-barcode", method = RequestMethod.POST)
    public PrintListResponse printGoodCangBarcode(@Valid @RequestBody ScanPrintRequest request) {
        return shipmentPrintService.printGoodCangBarcode(request);
    }

    @ApiOperation(value = "根据出库单判断是否有多个装箱", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/shipment-by-stockout-order-no", method = RequestMethod.POST)
    public BaseStringResponse shipmentByStockoutOrderNo(@Valid @RequestBody NoListRequest request) {
        return shipmentInfoService.shipmentByStockoutOrderNo(request);
    }

    @ApiOperation(value = "装箱清单批量修改物流单号", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/change-logistics-no-batch", method = RequestMethod.POST)
    public void changeLogisticsNoBatch(@Valid @RequestBody StockoutUpdateLogisticsNoRequest request) {
        shipmentInfoService.changeLogisticsNoBatch(request);
    }

    /**
     * 内部接口
     *
     * <AUTHOR>
     * 2023-04-26
     */
    @RequestMapping(value = "/stockout-shipment/internal/generate-declare-order", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-生成报关单", produces = "application/json")
    public void generateDeclareOrder(@Valid @RequestBody IdListRequest request) {
        stockoutShipmentCustomsService.generateCustomsOrder(request.getIdList());
    }

    @RequestMapping(value = "/stockout-shipment/internal/send-ups-eds", method = RequestMethod.GET)
    @ApiOperation(value = "装箱清单-发送eds文件", produces = "application/json")
    public void sendUpsEds(@RequestParam String date) {
        upsEdsService.sendEdsToUps(DateUtil.parseDate(date));
    }


    @ApiOperation(value = "装箱清单-打印装箱箱号", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-box-code", method = RequestMethod.POST)
    public PrintListResponse printShipmentBoxCode(@Valid @RequestBody StringListRequest request) {
        return shipmentPrintService.printShipmentBoxCode(request.getStringList());
    }

    @ApiOperation(value = "装箱清单-打印透明计划条码", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-transparency-code/by-box", method = RequestMethod.POST)
    public PrintListResponse printTransparencyCodeByBox(@Valid @RequestBody StringListRequest request) {
        return transparencyCodeService.printTransparencyCodeByBox(request.getStringList(), false);
    }

    @ApiOperation(value = "装箱复核-重新打印透明计划条码", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-transparency-code/by-box/repeat", method = RequestMethod.POST)
    public PrintListResponse printTransparencyCodeByBoxRepeat(@Valid @RequestBody StringListRequest request) {
        return transparencyCodeService.printTransparencyCodeByBox(request.getStringList(), true);
    }

    @ApiOperation(value = "装箱复核-打印透明计划条码（单个条码-T code,通过序号）", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-transparency-code/by-code/print-by-index", method = RequestMethod.POST)
    public PrintListResponse printTransparencyCodeByIndex(@Valid @RequestBody TCodeIndexPrintRequest request) {
        return transparencyCodeService.printTransparencyCodeByIndex(request);
    }

    @ApiOperation(value = "装箱复核-重新打印透明计划条码（单个条码-T code）", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-transparency-code/by-code/repeat", method = RequestMethod.POST)
    public PrintListResponse printTransparencyCodeByCode(@Valid @RequestBody StringListRequest request) {
        return transparencyCodeService.printTransparencyCodeByCode(request.getStringList());
    }

    @ApiOperation(value = "装箱复核-重新打印透明计划条码（根据sku）", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-transparency-code/by-sku", method = RequestMethod.POST)
    public PrintListResponse printTransparencyCodeByCode(@Valid @RequestBody StockoutTCodeSkuPrintRequest request) {
        return transparencyCodeService.printTransparencyCodeBySku(request);
    }

    @ApiOperation(value = "通知包裹异常", produces = "application/json")
    @RequestMapping(value = "/notify-package-exception", method = RequestMethod.POST)
    public void notifyPackageException(@Valid @RequestBody TmsNotifyPackageExceptionRequest request) {
        LoginInfoService.setName(request.getOperator());
        TenantContext.setTenant(request.getLocation());
        shipmentInfoService.notifyPackageException(request);
        TenantContext.clear();
        LoginInfoService.removeName();
    }

    @RequestMapping(value = "/stockout-shipment/internal-match-t-code/{itemId}", method = RequestMethod.GET)
    @ApiOperation(value = "装箱明细匹配Tcode", produces = "application/json")
    public void matchTCode(@PathVariable("itemId") Integer itemId) {
        StockoutShipmentItemEntity byId = shipmentItemService.getById(itemId);
        transparencyCodeService.matchShipmentItem(byId);
    }

    @RequestMapping(value = "/stockout-shipment/quick-ship-shipment", method = RequestMethod.POST)
    @ApiOperation(value = "快速发货", produces = "application/json")
    public void quickShip(@Valid @RequestBody IdListRequest request) {
        stockoutShipmentOperateService.quickShip(request);
    }

    // 此接口用于亚马逊回传更新箱贴
    @RequestMapping(value = "/stockout-shipment/fba-label-status", method = RequestMethod.POST)
    @ApiOperation(value = "更新Fba箱贴申请状态", produces = "application/json")
    public void updateFbaLabelStatus(@Valid @RequestBody StockoutFbaLabelStatusRequest request) {
        syncAmazonService.updateFbaLabelStatus(request);
    }


    // 仓库人员 点击申请箱贴
    @RequestMapping(value = "/stockout-shipment/manual/fba-label-status", method = RequestMethod.POST)
    @ApiOperation(value = "更新Fba箱贴申请状态", produces = "application/json")
    public void manualUpdateFbaLabelStatus(@Valid @RequestBody StockoutFbaLabelStatusRequest request) {
        syncAmazonService.manualUpdateFbaLabelStatus(request);
    }

    @RequestMapping(value = "/stockout-shipment/back-fba-label-status", method = RequestMethod.POST)
    @ApiOperation(value = "回退Fba箱贴", produces = "application/json")
    public void backFbaLabelStatus(@RequestParam Integer shipmentId, @RequestParam String orderNo) {
        syncAmazonService.backFbaLabelStatus(shipmentId, orderNo);
    }

    @RequestMapping(value = "/stockout-shipment/sta-box-info-detail", method = RequestMethod.POST)
    @ApiOperation(value = "获取sta箱子明细", produces = "application/json")
    public StaBoxInfoDetailResponse getStaBoxInfoDetail(@Valid @RequestBody StaBoxInfoDetailRequest request) {
        return syncAmazonService.getStaBoxInfoDetail(request);
    }

    @RequestMapping(value = "/stockout-shipment/order/sta-box-info-detail", method = RequestMethod.POST)
    @ApiOperation(value = "回传sta箱子明细信息-根据订单号", produces = "application/json")
    public StaBoxInfoDetailResponse getStaBoxInfoDetailByOrderNo(@Valid @RequestBody StaBoxInfoDetailRequest request) {
        return syncAmazonService.getStaBoxInfoDetailByOrderNo(request);
    }

    @RequestMapping(value = "/stockout-shipment/transparency-code-info", method = RequestMethod.POST)
    @ApiOperation(value = "获取装箱清单对应的Code", produces = "application/json")
    public StockoutShipmentTransparencyCodeResponse getShipmentTCodeInfo(@Valid @RequestBody IdListRequest request) {
        return transparencyCodeService.getShipmentTCodeInfo(request);
    }

    @RequestMapping(value = "/stockout-shipment-item/transparency-code-info/{shipmentId}/{itemId}", method = RequestMethod.POST)
    @ApiOperation(value = "获取装箱清单明细对应的Code", produces = "application/json")
    public StockoutShipmentTransparencyCodeResponse getShipmentItemTCodeInfo(@PathVariable("shipmentId") Integer shipmentId, @PathVariable("itemId") Integer itemId) {
        return transparencyCodeService.getShipmentItemTCodeInfo(shipmentId, itemId);
    }

    // 上传面单
    @RequestMapping(value = "/stockout-shipment/upload-label", method = RequestMethod.POST)
    @ApiOperation(value = "上传物流面单", produces = "application/json")
    public void uploadLabel(@Valid @RequestBody StockoutShipmentUploadLabelRequest request) {
        labelService.uploadLabel(request);
    }

    @ApiOperation(value = "查询待补货装箱清单", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/wait-replenish/search", method = RequestMethod.POST)
    public List<StockoutReplenishResponse> getReplenishShipment(@Valid @RequestBody StockoutSearchReplenishRequest request) {
        return stockoutFbaReplenishOrderService.getReplenishShipment(request);
    }

    // 创建补货单
    @RequestMapping(value = "/stockout-shipment/wait-replenish/create", method = RequestMethod.POST)
    @ApiOperation(value = "创建补货单", produces = "application/json")
    public void createReplenishOrder(@Valid @RequestBody IdListRequest request) {
        stockoutFbaReplenishOrderService.createReplenishOrder(request, loginInfoService.getName());
    }

    @ApiOperation(value = "待补货装箱清单创建人下拉框", notes = "待补货装箱清单创建人下拉框", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/wait-replenish/create-by-select", method = RequestMethod.GET)
    public List<SelectModel> getReplenishShipmentCreateBySelect() {
        return stockoutFbaReplenishOrderService.getReplenishShipmentCreateBySelect();
    }

    // 回滚补货单
    @RequestMapping(value = "/stockout-shipment/wait-replenish/cancel", method = RequestMethod.POST)
    @ApiOperation(value = "回滚补货单", produces = "application/json")
    public void cancelReplenishOrder(@Valid @RequestBody StringListRequest request) {
        request.getStringList().forEach(replenishOrder -> {
            stockoutFbaReplenishOrderService.cancelReplenishOrder(replenishOrder);
        });
    }

    @ApiOperation(value = "打印fba发货label", notes = "打印fba发货label", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/new/print-fba-label", method = RequestMethod.POST)
    public PrintListResponse printFbaLabel(@Valid @RequestBody FbaShipmentLabelRequest request) {
        return shipmentPrintService.printFbaLabel(request);
    }

    @ApiOperation(value = "打印fba发货label", notes = "打印fba发货label", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/new/download-fba-label", method = RequestMethod.POST)
    public List<String> downloadFbaLabel(@Valid @RequestBody FbaShipmentLabelRequest request) {
        return shipmentPrintService.downloadFbaLabel(request);
    }

    @ApiOperation(value = "打印fba发货label-工厂发货计划打印箱贴", notes = "打印fba发货label-工厂发货计划打印箱贴", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/gc/print-fba-label", method = RequestMethod.POST)
    public PrintListResponse printFbaLabelForGc(@Valid @RequestBody FbaShipmentLabelRequest request) {
        return shipmentPrintService.printFbaLabelForGc(request);
    }

    @ApiOperation(value = "打印fba发货label-单Shipment", notes = "打印fba发货label-单Shipment", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/new/single-shipment/print-fba-label", method = RequestMethod.POST)
    public PrintListResponse printSingleShipmentFbaLabel(@Valid @RequestBody FbaShipmentLabelRequest request) {
        return shipmentPrintService.printSingleShipmentFbaLabel(request);
    }

    @RequestMapping(value = "/stockout-shipment/copy-shipment", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单-一键复制功能", produces = "application/json")
    public void copyShipment(@Valid @RequestBody StockoutShipmentCopyRequest request) {
        stockoutShipmentInfoService.copyShipment(request);
    }


    // 此接口用于oms删除FBT箱子关联信息
    @RequestMapping(value = "/stockout-shipment/clear-shipment-label-mapping/{orderNo}", method = RequestMethod.GET)
    @ApiOperation(value = "退回已装箱-删除shipment箱贴信息", produces = "application/json")
    public void clearShipmentLabelMapping(@PathVariable("orderNo") String orderNo) {
        stockoutShipmentInfoService.clearShipmentLabelMapping(orderNo, FbaLabelStatusEnum.APPLYING.name());
    }

    @ApiOperation(value = "打印空加派箱唛", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment/print-kjp-box-label", method = RequestMethod.POST)
    public PrintListResponse printKjpBoxLabel(@Valid @RequestBody PrintKjpBoxRequest request, @RequestParam String boxTitle) {
        return shipmentPrintService.printKjpBoxLabel(request.getIdList(), request.getPrintNum(), boxTitle);
    }
}
