package com.nsy.wms.business.service.stock;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.StockTakeOrderConstant;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.domain.stock.Stock;
import com.nsy.api.wms.domain.stock.TakeStockTaskSkuStockInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.common.OpenEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockDifferenceCheckOrderStatusEnum;
import com.nsy.api.wms.enumeration.stock.SyncErpTypeEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockPlanTypeEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockTaskModeEnum;
import com.nsy.api.wms.enumeration.stockout.StockTakeStockTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockListRequest;
import com.nsy.api.wms.request.stock.StockPDATakeStockTaskClearRequest;
import com.nsy.api.wms.request.stock.StockPDATakeStockTaskItemRequest;
import com.nsy.api.wms.request.stock.StockPDATakeStockTaskRequest;
import com.nsy.api.wms.request.stock.StockPDATakeStockTaskSubmitRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockListResponse;
import com.nsy.api.wms.response.stock.StockPDATakeStockTaskItemResponse;
import com.nsy.api.wms.response.stock.StockPDATakeStockTaskResponse;
import com.nsy.wms.business.domain.bo.mq.SyncErpMessage;
import com.nsy.wms.business.domain.bo.stock.StockPDATakeStockTaskSubmitBo;
import com.nsy.wms.business.domain.bo.stock.StockTakeOrderItemSaveBo;
import com.nsy.wms.business.domain.bo.stock.StockTakeOrderSaveBo;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.ErpTransferApiService;
import com.nsy.wms.business.manage.erp.request.ErpInventoryRequest;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSupplierPositionMappingService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockin.StockinReturnProductService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskItemService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSupplierPositionMappingEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockDifferenceCheckOrderEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.product.ProductSpecInfoMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockTakeStockTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockPDATakeStockTaskService {

    @Autowired
    StockTakeStockTaskMapper taskMapper;
    @Autowired
    StockTakeStockTaskItemService taskItemService;
    @Autowired
    StockTakeStockLogService logService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockService stockService;
    @Autowired
    BdPositionMapper positionMapper;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    ProductSpecInfoMapper specInfoMapper;
    @Autowired
    StockDifferenceCheckOrderService differenceCheckOrderService;
    @Autowired
    ErpTransferApiService erpTransferApiService;
    @Autowired
    BdPositionService positionService;
    @Autowired
    ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    StockInternalBoxService internalBoxService;
    @Autowired
    StockInternalBoxItemService internalBoxItemService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockTakeOrderService stockTakeOrderService;
    @Autowired
    StockLockService stockLockService;
    @Autowired
    StockCenterCommonService stockCenterCommonService;
    @Autowired
    StockChangeLogService stockChangeLogService;
    @Autowired
    private BdSystemParameterService bdSystemParameterService;
    @Autowired
    private BdSupplierPositionMappingService bdSupplierPositionMappingService;
    @Autowired
    private StockinReturnProductService stockinReturnProductService;
    @Autowired
    private ErpApiService erpApiService;

    public PageResponse<StockPDATakeStockTaskResponse> pageList(StockPDATakeStockTaskRequest request) {
        PageResponse<StockPDATakeStockTaskResponse> pageResponse = new PageResponse<>();
        Page<StockPDATakeStockTaskResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockPDATakeStockTaskResponse> list = this.taskMapper.pageListPDA(page, request);
        list.forEach(item -> {
            item.setPlanTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INVENTORY_TASK_TYPE.getName(), item.getPlanType()));
            item.setTaskGenerateModeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INVENTORY_TASK_METHOD.getName(), item.getTaskGenerateMode()));
            item.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INVENTORY_TASK_STATUS.getName(), item.getStatus()));
        });
        pageResponse.setContent(list);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    public PageResponse<StockPDATakeStockTaskItemResponse> itemList(StockPDATakeStockTaskItemRequest request) {
        if (Objects.isNull(request.getTaskId())) {
            throw new BusinessServiceException("参数错误");
        }
        PageResponse<StockPDATakeStockTaskItemResponse> pageResponse = new PageResponse<>();
        Page<StockPDATakeStockTaskItemResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockPDATakeStockTaskItemResponse> list = this.taskMapper.itemListPDA(page, request);
        list.forEach(item -> {
            item.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INVENTORY_TASK_STATUS.getName(), item.getStatus()));
            item.setPreMatchQty(stockPrematchInfoService.getSkuPreQty(item.getSku(), item.getPositionCode()));
            item.setPickingNum(stockoutPickingTaskItemService.countScanQtyByStatusAndSku(StockoutPickingTaskStatusEnum.PICKING.toString(), item.getSku(), item.getPositionCode()));
        });
        pageResponse.setContent(list);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @JLock(keyConstant = "submitTaskStock", lockKey = "#request.sku")
    public Integer submit(StockPDATakeStockTaskSubmitBo request) {
        StockInternalBoxEntity internalBox = internalBoxService.getOne(new LambdaQueryWrapper<StockInternalBoxEntity>().eq(StockInternalBoxEntity::getInternalBoxCode, request.getTargetCode()));
        if (internalBox != null) {
            return submitByBox(request, internalBox);
        }
        // 获取盘点任务
        StockTakeStockTaskEntity taskEntity = getStockTakeStockTaskEntity(request);
        // 获取盘点任务明细
        StockTakeStockTaskItemEntity taskItemEntity = getStockTakeStockTaskItemEntity(taskEntity, request);
        // 获取当前库位库存
        Integer skuOldStock = getSkuOldStock(request.getTargetCode(), request.getSku());
        // 获取当前库位预配数
/*        Integer skuPrematchQty = stockPrematchInfoService.getSkuPreQty(request.getSku(), request.getTargetCode());
        if (skuPrematchQty != null && skuPrematchQty > request.getStock()) {
            throw new BusinessServiceException(String.format("当前库位【%s】,sku【%s】,无法盘点库存小于预配数【%s】", request.getTargetCode(), request.getSku(), skuPrematchQty));
        }*/
        //解锁库存
        stockLockService.unlockStock(request.getSku(), request.getTargetCode());
        if (request.getClearStock()) {
            // 清除库存
            this.clearStock(request.getTargetCode(), request.getSku());
        } else {
            // 更新库存
            this.updateStock(request, skuOldStock);
        }
        // 更新状态及日志
        this.updateTakeStockTaskStatus(request, taskEntity, taskItemEntity, skuOldStock);
        //生成盘点单
        StockTakeOrderSaveBo stockTakeOrderSaveBo = new StockTakeOrderSaveBo();
        stockTakeOrderSaveBo.setCreateBy(StringUtils.hasText(request.getCreateBy()) ? request.getCreateBy() : loginInfoService.getName());
        stockTakeOrderSaveBo.setLocation(StringUtils.hasText(request.getLocation()) ? request.getLocation() : TenantContext.getTenant());
        stockTakeOrderSaveBo.setTakeEntity(taskEntity);
        StockTakeOrderItemSaveBo stockTakeOrderItemSaveBo = new StockTakeOrderItemSaveBo();
        BeanUtils.copyProperties(taskItemEntity, stockTakeOrderItemSaveBo);
        stockTakeOrderItemSaveBo.setOperateReason(request.getOperateReason());
        stockTakeOrderSaveBo.setTakeItemEntityList(Collections.singletonList(stockTakeOrderItemSaveBo));
        stockTakeOrderService.buildStockTakeOrder(stockTakeOrderSaveBo);
        // 同步盘点结果
        ErpInventoryRequest syncErpRequest = buildSyncErpRequest(taskItemEntity);
        SyncErpMessage<ErpInventoryRequest> syncErpMessage = new SyncErpMessage<>(TenantContext.getTenant(), loginInfoService.getName(), null, SyncErpTypeEnum.INVENTORY, syncErpRequest);
        messageProducer.sendMessage(KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC_NAME, KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC, Key.of(TenantContext.getTenant() + "_" + request.getSku()), syncErpMessage);

        return taskEntity.getTaskId();
    }


    public void syncInventoryToErp(ErpInventoryRequest request) {

        BdSystemParameterEntity bdSystemParameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_INVENTORY_SYNC_MARK.getKey());
        if (bdSystemParameterEntity != null && org.springframework.util.StringUtils.hasText(bdSystemParameterEntity.getConfigValue()) && bdSystemParameterEntity.getConfigValue().equals(OpenEnum.DISABLE.getValue())) {
            return;
        }

        // 是否退货库位，统计当前SKU退货库位总库存
        BdPositionEntity bdPositionEntity = positionService.getPositionByCode(request.getPositionCode());

        //退货库位逻辑
        if (BdPositionTypeEnum.RETURN_POSITION.name().equals(bdPositionEntity.getPositionType())) {
            int changeQty = request.getInventoryItemList().stream().mapToInt(ErpInventoryRequest.ErpInventoryItem::getQty).sum();
            request.setIsReturnPosition(1);
            ErpInventoryRequest.ErpInventoryItem item = new ErpInventoryRequest.ErpInventoryItem();
            item.setSku(request.getInventoryItemList().get(0).getSku());
            Integer returnPositionStock = stockService.getBaseMapper().findReturnPositionStockBySku(item.getSku());
            item.setQty(returnPositionStock);
            request.setInventoryItemList(Collections.singletonList(item));
            //删除退货明细（按时间顺序,最早的优先）
            if (changeQty < 0)
                delStockinReturnProduct(changeQty, bdPositionEntity, item.getSku(), request.getOperator());

            erpApiService.inventory(request);
            return;
        }
        //活动库位和OEM库位逻辑
        if (BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.OEM_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.STORE_POSITION.name().equals(bdPositionEntity.getPositionType())) {
            Integer stock = stockService.getBaseMapper().getActivityPositionStock(request.getInventoryItemList().get(0).getSku(), bdPositionEntity.getSpaceId(), bdPositionEntity.getPositionCode());
            request.getInventoryItemList().get(0).setQty(stock);
            erpApiService.inventory(request);
            return;
        }


        request.setPositionCode(stockCenterCommonService.changePositionCode(request.getPositionCode()));
        //统一库位逻辑
        if (stockCenterCommonService.isEnableConfig(bdPositionEntity.getAreaId()) || !bdPositionEntity.getPositionCode().equals(request.getPositionCode())) {
            //查询有效库存
            Integer stock = stockService.getBaseMapper().getEffectivePositionStock(request.getInventoryItemList().get(0).getSku(), bdPositionEntity.getSpaceId(), bdPositionEntity.getAreaId());
            request.getInventoryItemList().get(0).setQty(stock);
        }

        erpApiService.inventory(request);
    }

    private void delStockinReturnProduct(int changeQty, BdPositionEntity bdPositionEntity, String sku, String operator) {
        BdSupplierPositionMappingEntity mappingEntity = bdSupplierPositionMappingService.getEnableByPositionCode(bdPositionEntity.getPositionCode());
        if (mappingEntity == null)
            return;
        List<StockinReturnProductEntity> list = stockinReturnProductService.list(new LambdaQueryWrapper<StockinReturnProductEntity>().eq(StockinReturnProductEntity::getSupplierId, mappingEntity.getSupplierId()).eq(StockinReturnProductEntity::getSku, sku).orderByAsc(StockinReturnProductEntity::getStockinOrderNo).orderByAsc(StockinReturnProductEntity::getCreateDate));
        if (CollectionUtils.isEmpty(list))
            return;
        int qty = Math.abs(changeQty);
        List<Integer> delList = new ArrayList<>(8);
        for (StockinReturnProductEntity returnProductEntity : list) {
            if (returnProductEntity.getReturnQty() > qty) {
                StockinReturnProductEntity entity = new StockinReturnProductEntity();
                entity.setReturnProductId(returnProductEntity.getReturnProductId());
                entity.setUpdateBy(operator);
                entity.setReturnQty(returnProductEntity.getReturnQty() - qty);
                stockinReturnProductService.updateById(entity);
                qty = 0;
            } else {
                delList.add(returnProductEntity.getReturnProductId());
                qty -= returnProductEntity.getReturnQty();
            }
            if (qty <= 0)
                break;
        }
        if (!CollectionUtils.isEmpty(delList))
            stockinReturnProductService.removeByIds(delList);
    }

    private ErpInventoryRequest buildSyncErpRequest(StockTakeStockTaskItemEntity taskItemEntity) {
        ErpInventoryRequest request = new ErpInventoryRequest();
        request.setLocation(TenantContext.getTenant());
        request.setOperator(loginInfoService.getName());
        request.setPositionCode(taskItemEntity.getPositionCode());

        ErpInventoryRequest.ErpInventoryItem inventoryItem = new ErpInventoryRequest.ErpInventoryItem();
        inventoryItem.setQty(taskItemEntity.getAfterQty() - taskItemEntity.getBeforeQty());
        inventoryItem.setSku(taskItemEntity.getSku());
        request.setInventoryItemList(Collections.singletonList(inventoryItem));
        return request;
    }

    private StockTakeStockTaskEntity getStockTakeStockTaskEntity(StockPDATakeStockTaskSubmitRequest request) {
        StockTakeStockTaskEntity taskEntity;
        if (Objects.isNull(request.getTaskId())) {
            taskEntity = new StockTakeStockTaskEntity();
            taskEntity.setLocation(TenantContext.getTenant());
            taskEntity.setPlanType(TakeStockPlanTypeEnum.TEMPORARY_INVENTORY.name());
            taskEntity.setTaskGenerateMode(TakeStockTaskModeEnum.BY_POSITION.getCode());
            taskEntity.setStatus(StockTakeStockTaskStatusEnum.INVENTORYING.name());
            // 初始化为0，盘点提交后更新
            taskEntity.setSpaceAreaQty(0);
            taskEntity.setPositionQty(0);
            taskEntity.setCreateBy(loginInfoService.getName());
            taskEntity.setOperator(loginInfoService.getName());
            taskMapper.insert(taskEntity);
            logService.addLog(taskEntity.getTaskId(), "盘点任务生成", "盘点任务生成");
            request.setTaskId(taskEntity.getTaskId());
        } else {
            taskEntity = taskMapper.selectById(request.getTaskId());
            if (Objects.isNull(taskEntity)) {
                throw new BusinessServiceException("盘点任务不存在");
            }
        }
        return taskEntity;
    }

    private StockTakeStockTaskItemEntity getStockTakeStockTaskItemEntity(StockTakeStockTaskEntity taskEntity, StockPDATakeStockTaskSubmitRequest request) {
        StockTakeStockTaskItemEntity taskItemEntity;
        if (Objects.isNull(request.getId())) { // 临时盘点
            ProductSpecInfoEntity specInfoEntity = specInfoMapper.selectOne(new LambdaQueryWrapper<ProductSpecInfoEntity>().eq(ProductSpecInfoEntity::getSku, request.getSku()));
            if (Objects.isNull(specInfoEntity)) {
                throw new BusinessServiceException("sku不存在");
            }
            BdPositionEntity positionEntity = positionService.getPositionByCode(request.getTargetCode());
            if (Objects.isNull(positionEntity)) {
                throw new BusinessServiceException("库位不存在");
            }
         /*   if (!BdPositionTypeEnum.RETURN_POSITION.name().equals(positionEntity.getPositionType())) {
                throw new BusinessServiceException("当前盘点仅支持退货库位");
            }*/
            if (!StockTakeOrderConstant.STOCK_TAKE_ORDER_POSITION_TYPE.contains(positionEntity.getPositionType())) {
                throw new BusinessServiceException("盘点仅支持储存库位、零拣库位、退货库位、越库库位、活动库位、OEM库位、店铺库位");
            }
         /*   if (SpaceAreaMapConstant.WmsArea.OEM_AREA.equals(positionEntity.getAreaName())
                    || SpaceAreaMapConstant.WmsArea.ACTIVITY_AREA.equals(positionEntity.getAreaName()))
                throw new BusinessServiceException("活动仓和OEM仓的库位请到旧系统盘点");*/
            taskItemEntity = new StockTakeStockTaskItemEntity();
            taskItemEntity.setTaskId(taskEntity.getTaskId());
            taskItemEntity.setLocation(taskEntity.getLocation());
            taskItemEntity.setProductId(specInfoEntity.getProductId());
            taskItemEntity.setSku(request.getSku());
            taskItemEntity.setSpecId(specInfoEntity.getSpecId());
            taskItemEntity.setPositionId(positionEntity.getPositionId());
            taskItemEntity.setPositionCode(positionEntity.getPositionCode());
            taskItemEntity.setSpaceAreaId(positionEntity.getSpaceAreaId());
            taskItemEntity.setSpaceAreaName(positionEntity.getSpaceAreaName());
            taskItemEntity.setAreaId(positionEntity.getAreaId());
            taskItemEntity.setAreaName(positionEntity.getAreaName());
        } else {
            taskItemEntity = taskItemService.getBaseMapper().selectById(request.getId());
            if (!taskItemEntity.getSku().equals(request.getSku())) {
                throw new BusinessServiceException(String.format("sku:%s不是当前盘点任务明细下的", request.getSku()));
            }
        }
        return taskItemEntity;
    }

    /**
     * 清除sku库位库存
     */
    private void clearStock(String positionCode, String sku) {
        StockEntity stockEntity = stockService.getOne(new QueryWrapper<StockEntity>().lambda().eq(StockEntity::getSku, sku).eq(StockEntity::getPositionCode, positionCode).last(MybatisQueryConstant.QUERY_FIRST));
        if (stockEntity == null)
            throw new BusinessServiceException("未找到库存记录,无法清空，请确认");
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(sku);
        stockUpdateRequest.setPositionCode(stockEntity.getPositionCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.TAKE_STOCK);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_INSIDE);
        stockUpdateRequest.setQty(-stockEntity.getStock());
        stockService.updateStock(stockUpdateRequest);
    }

    /**
     * 更新sku库位库存
     */
    private void updateStock(StockPDATakeStockTaskSubmitRequest request, Integer skuOldStock) {
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(request.getSku());
        stockUpdateRequest.setPositionCode(request.getTargetCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.TAKE_STOCK);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_INSIDE);
        stockUpdateRequest.setQty(request.getStock() - skuOldStock);
        stockUpdateRequest.setUnLock(true);
        stockService.updateStock(stockUpdateRequest);
    }

    public Integer getSkuOldStock(String positionCode, String sku) {
        StockEntity skuPositionStock = stockService.getSkuPositionStock(sku, positionCode);
        return Objects.nonNull(skuPositionStock) ? skuPositionStock.getStock() : 0;
    }

    @Transactional
    public void updateTakeStockTaskStatus(StockPDATakeStockTaskSubmitRequest request, StockTakeStockTaskEntity taskEntity, StockTakeStockTaskItemEntity taskItemEntity, Integer skuOldStock) {
        // 更新明细状态
        taskItemEntity.setUpdateBy(loginInfoService.getName());
        taskItemEntity.setStatus(StockTakeStockTaskStatusEnum.INVENTORY_COMPLETED.name());
        if (Objects.isNull(taskItemEntity.getBeforeQty())) {
            taskItemEntity.setBeforeQty(skuOldStock);
        }
        taskItemEntity.setAfterQty(request.getStock());
        taskItemService.saveOrUpdate(taskItemEntity);
        Integer completeCount = taskItemService.getBaseMapper().selectCount(new LambdaQueryWrapper<StockTakeStockTaskItemEntity>().eq(StockTakeStockTaskItemEntity::getTaskId, taskEntity.getTaskId()).eq(StockTakeStockTaskItemEntity::getStatus, StockTakeStockTaskStatusEnum.INVENTORY_COMPLETED.name()));
        BdPositionEntity positionEntity = positionService.getPositionByCode(request.getTargetCode());
        if (completeCount.equals(1)) {
            logService.addLog(taskEntity.getTaskId(), "开始盘点", "开始盘点");
            taskEntity.setOperateDate(new Date());
            // 如果是差异盘点类型的，更新差异核对单的状态为盘点中
            if (taskEntity.getPlanType().equals(TakeStockPlanTypeEnum.VARIANCE_INVENTORY.getCode())) {
                this.updateDifferenceCheckOrderStatus(taskEntity.getTaskId(), StockDifferenceCheckOrderStatusEnum.INVENTORYING.name());
            }
        }
        logService.addLog(taskEntity.getTaskId(), "盘点", String.format("【%s】，库位【%s】，SKU【%s】，原%s件，盘点后【%s】件，异常【%s】件", positionEntity.getSpaceAreaName(), positionEntity.getPositionCode(), request.getSku(), skuOldStock, request.getStock(), skuOldStock - request.getStock()));
        List<StockTakeStockTaskItemEntity> taskItemEntityList = taskItemService.getBaseMapper().selectList(new LambdaQueryWrapper<StockTakeStockTaskItemEntity>().eq(StockTakeStockTaskItemEntity::getTaskId, taskItemEntity.getTaskId()));
        // 明细都为盘点完成状态则更新盘点任务状态为盘点完成
        if (taskItemEntityList.stream().allMatch(item -> item.getStatus().equals(StockTakeStockTaskStatusEnum.INVENTORY_COMPLETED.name()))) {
            taskEntity.setStatus(StockTakeStockTaskStatusEnum.INVENTORY_COMPLETED.name());
            // 临时盘点更新库区和库位数
            if (taskEntity.getPlanType().equals(TakeStockPlanTypeEnum.TEMPORARY_INVENTORY.name()) && taskEntity.getSpaceAreaQty().equals(0)) {
                taskEntity.setSpaceAreaQty((int) taskItemEntityList.stream().map(StockTakeStockTaskItemEntity::getSpaceAreaId).distinct().count());
                taskEntity.setPositionQty((int) taskItemEntityList.stream().map(StockTakeStockTaskItemEntity::getPositionId).distinct().count());
                //更新盘点单库区库位数
                stockTakeOrderService.updateSpaceAreaAndPositionQty(taskEntity.getSpaceAreaQty(), taskEntity.getPositionQty(), taskEntity.getTaskId());
            }
            logService.addLog(taskEntity.getTaskId(), "盘点完成", "完成盘点");
            // 如果是差异盘点类型的，更新差异核对单的状态为待确认
            if (taskEntity.getPlanType().equals(TakeStockPlanTypeEnum.VARIANCE_INVENTORY.getCode())) {
                this.updateDifferenceCheckOrderStatus(taskEntity.getTaskId(), StockDifferenceCheckOrderStatusEnum.TO_BE_CONFIRMED.name());
            }
        } else {
            taskEntity.setStatus(StockTakeStockTaskStatusEnum.INVENTORYING.name());
        }
        taskEntity.setOperator(loginInfoService.getName());
        taskEntity.setUpdateBy(loginInfoService.getName());
        taskMapper.updateById(taskEntity);
    }

    public void updateDifferenceCheckOrderStatus(Integer taskId, String status) {
        List<StockDifferenceCheckOrderEntity> differenceCheckOrderEntityList = differenceCheckOrderService.getBaseMapper().selectList(new LambdaQueryWrapper<StockDifferenceCheckOrderEntity>().eq(StockDifferenceCheckOrderEntity::getTaskId, taskId));
        if (CollectionUtils.isEmpty(differenceCheckOrderEntityList)) {
            return;
        }
        differenceCheckOrderEntityList.forEach(differenceCheckOrderEntity -> {
            differenceCheckOrderEntity.setStatus(status);
            differenceCheckOrderEntity.setUpdateBy(loginInfoService.getName());
        });
        differenceCheckOrderService.updateBatchById(differenceCheckOrderEntityList);
    }

    public Integer findPosition(String positionCode) {
        BdPositionEntity positionEntity = positionMapper.selectOne(new LambdaQueryWrapper<BdPositionEntity>().eq(BdPositionEntity::getPositionCode, positionCode).eq(BdPositionEntity::getIsDeleted, 0));
        if (Objects.isNull(positionEntity)) {
            throw new BusinessServiceException("库位不存在");
        }
        return positionEntity.getPositionId();
    }

    public TakeStockTaskSkuStockInfo getSkuStockInfo(String targetCode, String barcode, Integer taskId) {
        ProductSpecInfoEntity specInfoEntity = productStoreSkuMappingService.validBarcode(barcode);
        StockInternalBoxEntity internalBox = internalBoxService.getOne(new LambdaQueryWrapper<StockInternalBoxEntity>().eq(StockInternalBoxEntity::getInternalBoxCode, targetCode));
        if (internalBox != null) {
            return stockSearchByBox(internalBox, specInfoEntity);
        }

        BdPositionEntity bdPositionEntity = positionService.getPositionByCode(targetCode);
        TakeStockTaskSkuStockInfo skuStockInfo = taskMapper.getSkuStockInfo(bdPositionEntity.getPositionId(), specInfoEntity.getBarcode());
        if (Objects.isNull(skuStockInfo)) {
            skuStockInfo = new TakeStockTaskSkuStockInfo();
            BeanUtilsEx.copyProperties(specInfoEntity, skuStockInfo);
            skuStockInfo.setStock(0);
            skuStockInfo.setUnPrematchQty(0);
        } else {
            Integer unPrematchQty = stockService.getSkuPositionUnPrematchStock(specInfoEntity.getSku(), bdPositionEntity.getPositionId());
            skuStockInfo.setUnPrematchQty(unPrematchQty);
        }
        if (!Objects.isNull(taskId)) {
            StockTakeStockTaskItemEntity taskItemEntity = taskItemService.getBaseMapper().selectOne(new LambdaQueryWrapper<StockTakeStockTaskItemEntity>().eq(StockTakeStockTaskItemEntity::getTaskId, taskId).eq(StockTakeStockTaskItemEntity::getPositionId, bdPositionEntity.getPositionId()).eq(StockTakeStockTaskItemEntity::getSku, skuStockInfo.getSku()).orderByDesc(StockTakeStockTaskItemEntity::getId).last("limit 1"));
            if (Objects.isNull(taskItemEntity)) {
                throw new BusinessServiceException(String.format("%s不在当前盘点任务明细下", barcode));
            }
            skuStockInfo.setTaskItemId(taskItemEntity.getId());
        }
        return skuStockInfo;
    }

    private TakeStockTaskSkuStockInfo stockSearchByBox(StockInternalBoxEntity internalBox, ProductSpecInfoEntity specInfoEntity) {
        TakeStockTaskSkuStockInfo skuStockInfo = new TakeStockTaskSkuStockInfo();
        BeanUtilsEx.copyProperties(specInfoEntity, skuStockInfo);
        skuStockInfo.setUnPrematchQty(0);
        // 箱内库存
        List<StockEntity> boxStockList = stockService.list(new LambdaQueryWrapper<StockEntity>().eq(StockEntity::getInternalBoxId, internalBox.getInternalBoxId()).eq(StockEntity::getSku, specInfoEntity.getSku()));
        skuStockInfo.setStock(boxStockList.stream().mapToInt(StockEntity::getStock).sum());
        return skuStockInfo;
    }

    public List<SelectIntegerModel> findSpaceArea(Integer taskId) {
        return taskItemService.getBaseMapper().findSpaceArea(taskId);
    }

    @Transactional
    public void clearStockByRequest(StockPDATakeStockTaskClearRequest request) {
        if (request.getStockIdList().isEmpty())
            throw new BusinessServiceException("请选择需要清除的库存记录");

        List<StockEntity> stockEntityList = stockService.listByIds(request.getStockIdList());
        boolean existInternalBox = stockEntityList.stream().anyMatch(o -> !StringUtils.hasText(o.getPositionCode()));
        if (existInternalBox)
            throw new BusinessServiceException("仅支持库位盘零");

        for (StockEntity stockEntity : stockEntityList) {
            StockPDATakeStockTaskSubmitRequest submitRequest = new StockPDATakeStockTaskSubmitRequest();
            submitRequest.setClearStock(Boolean.TRUE);
            submitRequest.setTargetCode(stockEntity.getPositionCode());
            submitRequest.setSku(stockEntity.getSku());
            submitRequest.setStock(0);
            StockPDATakeStockTaskSubmitBo submitBo = new StockPDATakeStockTaskSubmitBo();
            BeanUtils.copyProperties(submitRequest, submitBo);
            this.submit(submitBo);
        }
    }

    public Integer submitByBox(StockPDATakeStockTaskSubmitRequest request, StockInternalBoxEntity internalBox) {
        List<StockInternalBoxItemEntity> boxItemList = internalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>().eq(StockInternalBoxItemEntity::getInternalBoxCode, request.getTargetCode()).eq(StockInternalBoxItemEntity::getSku, request.getSku()));
        if (boxItemList.isEmpty()) {
            StockInternalBoxItemEntity boxItemEntity = new StockInternalBoxItemEntity();
            boxItemEntity.setLocation(internalBox.getLocation());
            boxItemEntity.setSpaceId(internalBox.getSpaceId());
            boxItemEntity.setInternalBoxId(internalBox.getInternalBoxId());
            boxItemEntity.setInternalBoxCode(internalBox.getInternalBoxCode());

            ProductSpecInfoEntity specInfoEntity = productStoreSkuMappingService.validBarcode(request.getSku());
            boxItemEntity.setProductId(specInfoEntity.getProductId());
            boxItemEntity.setSpecId(specInfoEntity.getSpecId());
            boxItemEntity.setSku(specInfoEntity.getSku());
            boxItemEntity.setQty(0);
            boxItemEntity.setCreateBy(loginInfoService.getName());
            internalBoxItemService.addStockInternalBoxItemQty(boxItemEntity, request.getStock(), StockChangeLogTypeEnum.TAKE_STOCK, StockChangeLogTypeModuleEnum.STOCK_INSIDE, null);
            return boxItemEntity.getInternalBoxId();
        } else {
            Integer boxQty = boxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
            if (request.getStock() > boxQty) {
                internalBoxItemService.addStockInternalBoxItemQty(boxItemList.get(0), request.getStock() - boxQty, StockChangeLogTypeEnum.TAKE_STOCK, StockChangeLogTypeModuleEnum.STOCK_INSIDE, null);
            } else {
                internalBoxItemService.minusStockInternalBoxItemQty(boxItemList, boxQty - request.getStock(), StockChangeLogTypeEnum.TAKE_STOCK, StockChangeLogTypeModuleEnum.STOCK_INSIDE, null);
            }
        }
        return request.getStock();
    }

    public List<StockListResponse> findPositionStock(String positionCode, Integer isHideZeroStock) {
        BdPositionEntity positionEntity = positionService.getPositionByCode(positionCode);
        if (Objects.isNull(positionEntity))
            throw new BusinessServiceException("找不到该库位");
        if (BdPositionTypeEnum.SHIPPING_POSITION.name().equals(positionEntity.getPositionType()))
            throw new BusinessServiceException("不支持发货库位");
        if (!StockTakeOrderConstant.STOCK_TAKE_ORDER_POSITION_TYPE.contains(positionEntity.getPositionType())) {
            throw new BusinessServiceException("盘点仅支持储存库位、零拣库位、退货库位、越库库位、活动库位、OEM库位、店铺库位");
        }
        StockListRequest stockListRequest = new StockListRequest();
        stockListRequest.setExactPositionCode(positionCode);
        stockListRequest.setIsHideZeroStock(isHideZeroStock);
        List<Stock> stocks = stockService.getBaseMapper().pageSearchStock(stockListRequest, stockService.buildSortSql(null), 0, 100);
        List<StockListResponse> stockList = stocks.stream().map(projection -> {
            StockListResponse response = new StockListResponse();
            BeanUtils.copyProperties(projection, response);
            return response;
        }).collect(Collectors.toList());

        // 数据处理
        for (StockListResponse response : stockList) {
            Integer prematchQty = stockPrematchInfoService.findBySkuAndPositionCode(response.getSku(), response.getPositionCode()).stream().mapToInt(StockPrematchInfoEntity::getPrematchQty).sum();
            response.setPreQty(ObjectUtil.defaultIfNull(prematchQty, 0));
        }
        return stockList;
    }
}
