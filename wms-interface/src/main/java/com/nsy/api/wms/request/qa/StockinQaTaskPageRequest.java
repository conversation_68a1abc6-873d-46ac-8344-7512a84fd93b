package com.nsy.api.wms.request.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/18 11:09
 */
@ApiModel(value = "StockinQaTaskPageRequest", description = "入库质检任务分页查询参数")
public class StockinQaTaskPageRequest extends PageRequest {


    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "spu列表", name = "spuList")
    private List<String> spuList;

    @ApiModelProperty(value = "sku列表", name = "skuList")
    private List<String> skuList;

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;


    /**
     * 工厂出库单号
     */
    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;

    /**
     * 出库箱码
     */
    @ApiModelProperty(value = "出库箱码", name = "supplierDeliveryBoxCode")
    private String supplierDeliveryBoxCode;

    @ApiModelProperty(value = "采购单号", name = "purchasePlanNo")
    private String purchasePlanNo;

    @ApiModelProperty(value = "状态", name = "checkStatus")
    private String checkStatus;

    @ApiModelProperty(value = "状态-多选", name = "checkStatusList")
    private List<String> checkStatusList;

    @ApiModelProperty(value = "是否新款 1是  0否", name = "isNew")
    private Integer isNew;

    @ApiModelProperty(value = "是否退货返工 1是  0否", name = "isReturnApply")
    private Integer isReturnApply;

    @ApiModelProperty(value = "质检员", name = "operator")
    private String operator;

    @ApiModelProperty(value = "入库开始时间", name = "stockinStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockinStartDate;

    @ApiModelProperty(value = "入库结束时间", name = "stockinEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stockinEndDate;

    @ApiModelProperty(value = "仓库id", name = "spaceId")
    private Integer spaceId;

    @ApiModelProperty(value = "仓库id", name = "spaceIdList")
    private List<Integer> spaceIdList;

    @ApiModelProperty(value = "商品分类ID", name = "categoryIdList")
    private List<Integer> categoryIdList;

    @ApiModelProperty(value = "部门", name = "department")
    private String department;

    @ApiModelProperty(value = "商品标签", name = "skuTypeInfo")
    private List<String> skuTypeInfo;


    public List<Integer> getCategoryIdList() {
        return categoryIdList;
    }

    public void setCategoryIdList(List<Integer> categoryIdList) {
        this.categoryIdList = categoryIdList;
    }

    public List<String> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<String> spuList) {
        this.spuList = spuList;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public Integer getIsReturnApply() {
        return isReturnApply;
    }

    public void setIsReturnApply(Integer isReturnApply) {
        this.isReturnApply = isReturnApply;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getStockinStartDate() {
        return stockinStartDate;
    }

    public void setStockinStartDate(Date stockinStartDate) {
        this.stockinStartDate = stockinStartDate;
    }

    public Date getStockinEndDate() {
        return stockinEndDate;
    }

    public void setStockinEndDate(Date stockinEndDate) {
        this.stockinEndDate = stockinEndDate;
    }

    public List<String> getCheckStatusList() {
        return checkStatusList;
    }

    public void setCheckStatusList(List<String> checkStatusList) {
        this.checkStatusList = checkStatusList;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public List<String> getSkuTypeInfo() {
        return skuTypeInfo;
    }

    public void setSkuTypeInfo(List<String> skuTypeInfo) {
        this.skuTypeInfo = skuTypeInfo;
    }
}
