package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.request.stockout.StockoutShipmentChangePackageTaskPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentChangePackageTaskPageResponse;
import com.nsy.wms.business.service.stockout.StockoutShipmentChangePackageTaskService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 打包任务Controller
 */
@RestController
@RequestMapping("/stockout/shipment/change/package/task")
@Api("换包装任务管理")
public class StockoutShipmentChangePackageTaskController extends BaseController {

    @Autowired
    private StockoutShipmentChangePackageTaskService stockoutShipmentChangePackageTaskService;

    @ApiOperation(value = "查询列表", notes = "查询列表", produces = "application/json")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public PageResponse<StockoutShipmentChangePackageTaskPageResponse> searchPage(@RequestBody StockoutShipmentChangePackageTaskPageRequest request) {
        return stockoutShipmentChangePackageTaskService.searchPage(request);
    }

    @ApiOperation(value = "保存打包任务", notes = "保存打包任务", produces = "application/json")
    @RequestMapping(value = "/save/{shipmentBoxCode}", method = RequestMethod.POST)
    public void saveChangePackageTask(@PathVariable("shipmentBoxCode") String shipmentBoxCode) {
        stockoutShipmentChangePackageTaskService.saveChangePackageRecord(shipmentBoxCode);
    }
} 