package com.nsy.wms.controller.stock;

import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxAddRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxQcResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxResponse;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 * 内部箱接口
 */
@RestController
@Api(tags = "内部箱接口")
public class StockInternalBoxController extends BaseController {

    @Autowired
    private StockInternalBoxService stockInternalBoxService;

    @Autowired
    private StockInternalBoxItemService boxItemService;

    @ApiOperation(value = "手动新增内部箱", notes = "新增内部箱", produces = "application/json")
    @RequestMapping(value = "/internal-box/batch-save", method = RequestMethod.POST)
    public void batchSaveStockInternalBox(@RequestBody @Valid List<StockInternalBox> request) {
        stockInternalBoxService.batchSaveStockInternalBox(request);
    }


    @ApiOperation(value = "新增内部箱", notes = "新增内部箱", produces = "application/json")
    @RequestMapping(value = "/internal-box", method = RequestMethod.POST)
    public List<Integer> addStockInternalBox(@RequestBody @Valid StockinInternalBoxAddRequest request) {
        return stockInternalBoxService.saveStockInternalBox(request);
    }

    @ApiOperation(value = "内部箱删除", produces = "application/json")
    @RequestMapping(value = "/internal-box-delete/{internalBoxId}", method = RequestMethod.PUT)
    public void deleteStockInternalBox(@Valid @PathVariable("internalBoxId") Integer internalBoxId) {
        stockInternalBoxService.deleteStockInternalBox(internalBoxId);
    }

    @ApiOperation(value = "内部箱删除", produces = "application/json")
    @RequestMapping(value = "/internal-box-delete-batch", method = RequestMethod.POST)
    public void deleteStockInternalBox(@RequestBody @Valid IdListRequest idListRequest) {
        stockInternalBoxService.deleteStockInternalBoxBatch(idListRequest);
    }

    @ApiOperation(value = "启用内部箱", produces = "application/json")
    @RequestMapping(value = "/internal-box-enable/{internalBoxId}", method = RequestMethod.PUT)
    public void enableStockInternalBox(@Valid @PathVariable("internalBoxId") Integer internalBoxId) {
        stockInternalBoxService.enableStockInternalBox(internalBoxId);
    }

    @ApiOperation(value = "查询内部箱列表", notes = "查询内部箱列表", produces = "application/json")
    @RequestMapping(value = "/internal-box/list", method = RequestMethod.POST)
    public PageResponse<StockInternalBoxResponse> getInternalBoxList(@RequestBody StockinInternalBoxListRequest request) {
        return stockInternalBoxService.getStockInternalBoxList(request);
    }

    @ApiOperation(value = "内部箱打印", produces = "application/json")
    @RequestMapping(value = "/internal-box/print", method = RequestMethod.POST)
    public PrintListResponse printStockinInternalBox(@Valid @RequestBody IdListRequest idListRequest) {
        return stockInternalBoxService.printStockinInternalBox(idListRequest);
    }

    @ApiOperation(value = "内部箱信息", produces = "application/json")
    @RequestMapping(value = "/internal-box-info/{internalBoxCode}", method = RequestMethod.GET)
    public StockInternalBoxQcResponse getQcStockinInternalBoxInfo(@PathVariable("internalBoxCode") String internalBoxCode) {
        return stockInternalBoxService.getStockInternalBoxCodeInfo(internalBoxCode);
    }

    @ApiOperation(value = "质检内部箱信息", produces = "application/json")
    @RequestMapping(value = "/internal-box-info-by-purchase/{internalBoxCode}", method = RequestMethod.GET)
    public StockInternalBoxQcResponse getQcStockinInternalBoxInfoByPurchase(@PathVariable("internalBoxCode") String internalBoxCode) {
        return stockInternalBoxService.getStockInternalBoxCodeInfoByPurchase(internalBoxCode);
    }

    @ApiOperation(value = "打印内部箱详细信息", produces = "application/json")
    @RequestMapping(value = "/internal-box/transfer-detail/print", method = RequestMethod.POST)
    public PrintListResponse printInternalTransferBoxItem(@Valid @RequestBody IdListRequest idListRequest) {
        return boxItemService.printInternalTransferBoxItem(idListRequest);
    }

    @ApiOperation(value = "打印内部箱搬箱", produces = "application/json")
    @RequestMapping(value = "/internal-box/to-new-space/print", method = RequestMethod.POST)
    public PrintListResponse printNewSpaceBox(@Valid @RequestBody IdListRequest idListRequest) {
        return boxItemService.printNewSpaceBox(idListRequest);
    }
}
