package com.nsy.wms.repository.jpa.mapper.stockout;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintItemInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsReloadShipmentInfo;
import com.nsy.api.wms.domain.logistics.documents.UpsOtherChannelInfo;
import com.nsy.api.wms.domain.stockout.AmazonShipmentIdInfoExport;
import com.nsy.api.wms.domain.stockout.AmazonShipmentReferenceIdExport;
import com.nsy.api.wms.domain.stockout.AmazonShipmentSkuExport;
import com.nsy.api.wms.domain.stockout.AmazonShipmentSummaryExport;
import com.nsy.api.wms.domain.stockout.AmazonShipmentSummaryWeight;
import com.nsy.api.wms.domain.stockout.ShipmentAllShippedExport;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.domain.stockout.ShipmentDateShippedInfoDTO;
import com.nsy.api.wms.domain.stockout.ShipmentLogisticsTrackExport;
import com.nsy.api.wms.domain.stockout.ShipmentSkuInfo;
import com.nsy.api.wms.domain.stockout.StockOutShipmentPrintDTO;
import com.nsy.api.wms.domain.stockout.StockoutOrderReturnProductList;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItem;
import com.nsy.api.wms.domain.stockout.StockoutShipmentModel;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchCount;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsShipmentInfoListRequest;
import com.nsy.api.wms.request.stockout.StockoutSearchReplenishRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.response.stockout.ForwarderAccountExportResponse;
import com.nsy.api.wms.response.stockout.StockoutReplenishResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentCountResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutShipmentItemListFindBo;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface StockoutShipmentMapper extends BaseMapper<StockoutShipmentEntity> {

    IPage<Integer> pageSearchShipmentIds(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    IPage<Integer> pageSearchFBAShipmentIds(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    // pageSearchFBAShipmentIdsOrderByShipmentId
    IPage<Integer> pageSearchFBAShipmentIdsOrderByShipmentId(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    IPage<AmazonShipmentSkuExport> pageSearchFBAShipmentSku(IPage page, @Param("query") StockoutShipmentSearchRequest request);


    Integer pageSearchFBAShipmentSkuCount(@Param("query") StockoutShipmentSearchRequest request);


    Integer pageSearchFBAShipmentIdsCount(@Param("query") StockoutShipmentSearchRequest request);


    Integer pageSearchShipmentIdsCount(@Param("query") StockoutShipmentSearchRequest request);

    IPage<String> pageSearchFBAShipmentOrderNos(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    /**
     * 通过订单号/物流单号查询订单及sku信息
     *
     * @param
     * @param scanNumber
     * @return
     */
    List<StockoutOrderReturnProductList> findReturnProductList(@Param("scanNumber") String scanNumber);


    IPage<ForwarderAccountExportResponse> pageSearchForwarderChannel(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    Integer pageSearchForwarderChannelCount(@Param("query") StockoutShipmentSearchRequest request);


    IPage<StockoutShipmentSearchResult> shipmentConfirmList(IPage page, @Param("query") StockoutShipmentConfirmRequest request);

    IPage<StockoutShipmentSearchResult> shipmentConfirmListBySameReceiverInfo(IPage page, @Param("query") StockoutShipmentConfirmRequest request);

    List<ShipmentAllShippedExport> findAllShippedExport(@Param("shipmentIds") List<Integer> shipmentIds);

    // 装箱清单主表和明细表信息查询
    List<StockoutShipmentItem> getShipmentItemByQuery(@Param("query") StockoutShipmentItemRequest request);

    // 单证页面，时颖发票和无纸化发票打印明细
    List<LogisticsDocumentsPrintItemInfo> logisticsDocumentsCategoryItem(@Param("shipmentIdList") List<Integer> shipmentIdList);

    // 单证页面，详细发票打印明细
    List<LogisticsDocumentsPrintItemInfo> logisticsDocumentsSkuItem(@Param("shipmentIdList") List<Integer> shipmentIdList);

    // 单证页面，重载装箱清单数据源
    List<LogisticsDocumentsReloadShipmentInfo> logisticsDocumentsShipmentInfo(@Param("query") LogisticsDocumentsShipmentInfoListRequest request);

    IPage<ShipmentBoxSkuExport> shipmentBoxSku(IPage page, @Param("shipmentIds") List<Integer> shipmentIds);

    List<ShipmentDateShippedInfoDTO> dateShippedInfo(@Param("shipmentIds") List<Integer> shipmentIds);

    /**
     * 通过订单号/箱子编号/出库单号 查询装箱清单中的出库单号（去重） 状态：装箱完成
     */
    IPage<String> findStockOutOrderNoByScanNumber(IPage<String> page, @Param("no") String no);

    /**
     * 查询发货确认
     */
    List<StockoutShipmentSearchResult> findShipmentConfirmList(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList, @Param("status") String status, @Param("ids") List<Integer> ids);

    /**
     * 查询发货确认
     */
    List<StockoutShipmentSearchResult> findShipmentConfirmItemList(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList, @Param("status") String status, @Param("isDeleted") Integer isDeleted);

    List<StockoutShipmentSearchResult> findShipmentConfirmItemListByStockoutOrderNoList(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList);

    List<ShipmentSkuInfo> getPrintShipmentList(@Param("idList") List<Integer> idList);

    List<ShipmentSkuInfo> getPrintShipmentOrder(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList);

    /**
     * 查询发货确认列表
     */
    List<StockoutShipmentSearchResult> findShipmentItemList(@Param("shipmentIds") List<Integer> shipmentIds, @Param("status") String status, @Param("isDeleted") Integer isDeleted);

    IPage<StockoutShipmentSearchResult> findShipmentItemListByStatus(IPage page, StockoutShipmentItemListFindBo request);

    /**
     * 箱子编号 查询装箱清单中的Id
     */
    IPage<Integer> findShipmentIdByShipmentBoxCodeListAndStatusList(IPage<String> page, List<String> shipmentBoxCodeList, List<String> statusList);


    /**
     * 通过出库单号查询装箱清单
     *
     * @param stockoutOrderNoList
     * @param status
     * @param isDeleted
     * @return
     */
    List<StockoutShipmentSearchResult> findShipmentListByOrderNo(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList, @Param("status") String status, @Param("isDeleted") Integer isDeleted);

    List<StockoutShipmentCountResponse> countByStatus();

    List<ShipmentLogisticsTrackExport> getLogisticsTrackInfo(@Param("shipmentIdList") List<Integer> shipmentIdsList, @Param("status") String status);

    StockoutShipmentSearchCount pageSearchCount(@Param("query") StockoutShipmentSearchRequest request);

    List<StockoutShipmentModel> getShipmentListToEdlSync(@Param("logisticsCompanyList") List<String> logisticsCompanyList, @Param("day") Integer day);

    List<StockoutShipmentModel> getShipmentListToYUNTUSync(@Param("logisticsCompanyList") List<String> logisticsCompanyList, @Param("day") Integer day);

    List<StockoutShipmentModel> getShipmentListToZHONGCHENGHANGSync(@Param("logisticsCompanyList") List<String> logisticsCompanyList, @Param("day") Integer day);

    List<ShipmentBoxSkuExport> shipmentBoxSkuList(@Param("shipmentIds") List<Integer> shipmentIds, @Param("declareDocumentNo") List<String> declareDocumentNo);

    // shipmentBoxSkuListOrderByFba
    List<ShipmentBoxSkuExport> shipmentBoxSkuListOrderByFba(@Param("shipmentIds") List<Integer> shipmentIds, @Param("declareDocumentNo") List<String> declareDocumentNo);

    List<ShipmentBoxSkuExport> shipmentBoxSkuListOrderByStockoutOrderNo(@Param("shipmentIds") List<Integer> shipmentIds, @Param("declareDocumentNo") List<String> declareDocumentNo);

    Integer countQtyByIds(@Param("records") List<Integer> records);

    BigDecimal countWeightByIds(@Param("records") List<Integer> records);

    // 统计装箱清单中sku的重量（kg）, 并非装箱清单的重量
    BigDecimal countSkuWeightByShipmentBoxCode(@Param("shipmentBoxCode") String shipmentBoxCode);

    List<UpsOtherChannelInfo> upsOtherChannel(@Param("shipmentIdList") List<Integer> shipmentIdList);

    StockoutOrderEntity findTopStockoutOrderByShipmentId(@Param("id") Integer id);

    IPage<AmazonShipmentSummaryExport> pageSearchFBAShipmentSummary(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    List<AmazonShipmentSummaryWeight> pageSearchFBAShipmentSummaryWeight(@Param("fbaShipmentIds") List<String> fbaShipmentIds);

    Integer countSearchFBAShipmentSummary(@Param("query") StockoutShipmentSearchRequest downloadRequest);

    List<AmazonShipmentSummaryExport> searchShipmentSummaryByFbaShipmentId(@Param("fbaShipmentIds") List<String> strings, @Param("query") StockoutShipmentSearchRequest downloadRequest);

    List<StockOutShipmentPrintDTO> getStockOutShipmentBoxNumList(@Param("stockOutOderNo") String stockOutOderNo);

    List<ShipmentAllShippedExport> findAllShippedExportForCustomer(@Param("shipmentIds") List<Integer> shipmentIds);

    /**
     * 统计出库单未发货的装箱清单数
     *
     * @param stockOutOderNo
     * @return
     */
    int countWaitToShippedShipment(@Param("stockOutOderNo") String stockOutOderNo);

    IPage<AmazonShipmentReferenceIdExport> pageSearchFBAShipmentReferenceId(IPage page, @Param("query") StockoutShipmentSearchRequest request);

    Integer pageSearchFBAShipmentReferenceIdCount(@Param("query") StockoutShipmentSearchRequest request);

    List<StockoutReplenishResponse> getReplenishShipment(@Param("query") StockoutSearchReplenishRequest request);

    List<String> getReplenishShipmentCreateBySelect();

    Integer searchShipmentItemsCount(@Param("query") StockoutShipmentSearchRequest request);

    // 回滚FBA补货单
    void cancelReplenishOrder(String replenishOrder);

    List<StockoutShipmentEntity> listByStockinOrderNo(List<String> stockoutOrderNoList);

    List<StockoutShipmentEntity> listByOrderNo(List<String> orderNoList);

    List<String> searchAllFBAShipmentIds(@Param("query") StockoutShipmentSearchRequest request);

    void updateFbaLabelStatusByStockoutOrderNo(@Param("stockoutOrderNo") String stockoutOrderNo, @Param("fbaLabelStatus") String fbaLabelStatus);

    IPage<AmazonShipmentIdInfoExport> pageSearchAmazonShipmentIdInfo(IPage page, @Param("query") StockoutShipmentSearchRequest request);
}
