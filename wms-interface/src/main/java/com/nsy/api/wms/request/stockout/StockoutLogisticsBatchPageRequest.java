package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-27 09:50:21
 */
@ApiModel(value = "StockoutLogisticsBatchPageRequest", description = "列表查询request")
public class StockoutLogisticsBatchPageRequest extends PageRequest {
    @ApiModelProperty("下载的时候传的id")
    private List<Integer> ids;

    /**
     * 物流追踪波次
     */
    @ApiModelProperty("物流追踪波次")
    private String logisticsBatch;

    @ApiModelProperty("物流追踪波次")
    private String logisticsCompany;

    @ApiModelProperty("fbaShipmentId")
    private String fbaShipmentId;

    @ApiModelProperty("platformName")
    private String platformName;

    /**
     * 状态(待处理WAIT_DEAL、核对不通过DIFFERENCE、核对完成COMPLETION)
     */
    @ApiModelProperty("状态(待处理WAIT_DEAL、核对不通过DIFFERENCE、核对完成COMPLETION)")
    private List<String> statusList;

    /**
     * 差异类型(1待差异判断，2无差异，3重量差异，4单价差异，5重量和单价差异)
     */
    @ApiModelProperty("差异类型(1待差异判断，2无差异，3重量差异，4单价差异，5重量和单价差异)")
    private Integer differenceType;

    private Integer reviewResult;

    private Integer financialPushStatus;

    /**
     * 是否当天(1是 0否)
     */
    @ApiModelProperty("是否当天(是当天 /否)")
    private Boolean exactDay;

    /**
     * 核对文件id
     */
    @ApiModelProperty("核对文件名")
    private String checkDocumentName;

    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateBegin;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateEnd;

    @ApiModelProperty("发货开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDateBegin;

    @ApiModelProperty("发货结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDateEnd;

    private String businessType;

    @ApiModelProperty("店铺id")
    private List<Integer> storeIdList;

    public Integer getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(Integer reviewResult) {
        this.reviewResult = reviewResult;
    }

    public Integer getFinancialPushStatus() {
        return financialPushStatus;
    }

    public void setFinancialPushStatus(Integer financialPushStatus) {
        this.financialPushStatus = financialPushStatus;
    }

    public List<Integer> getStoreIdList() {
        return storeIdList;
    }

    public void setStoreIdList(List<Integer> storeIdList) {
        this.storeIdList = storeIdList;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getLogisticsBatch() {
        return logisticsBatch;
    }

    public void setLogisticsBatch(String logisticsBatch) {
        this.logisticsBatch = logisticsBatch;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public Integer getDifferenceType() {
        return differenceType;
    }

    public void setDifferenceType(Integer differenceType) {
        this.differenceType = differenceType;
    }

    public Boolean getExactDay() {
        return exactDay;
    }

    public void setExactDay(Boolean exactDay) {
        this.exactDay = exactDay;
    }

    public String getCheckDocumentName() {
        return checkDocumentName;
    }

    public void setCheckDocumentName(String checkDocumentName) {
        this.checkDocumentName = checkDocumentName;
    }

    public Date getCreateDateBegin() {
        return createDateBegin;
    }

    public void setCreateDateBegin(Date createDateBegin) {
        this.createDateBegin = createDateBegin;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public Date getDeliveryDateBegin() {
        return deliveryDateBegin;
    }

    public void setDeliveryDateBegin(Date deliveryDateBegin) {
        this.deliveryDateBegin = deliveryDateBegin;
    }

    public Date getDeliveryDateEnd() {
        return deliveryDateEnd;
    }

    public void setDeliveryDateEnd(Date deliveryDateEnd) {
        this.deliveryDateEnd = deliveryDateEnd;
    }
}

