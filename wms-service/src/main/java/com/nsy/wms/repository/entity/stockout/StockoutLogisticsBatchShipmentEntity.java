package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-03-27 14:01:17
 */
@Entity
@Table(name = "stockout_logistics_batch_shipment")
@TableName("stockout_logistics_batch_shipment")
public class StockoutLogisticsBatchShipmentEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    private Integer referenceId;

    /**
     * 物流波次id
     */
    private Integer logisticsBatchId;

    /**
     * 物流波次号
     */
    private String logisticsBatch;

    /**
     * FBA Shipment ID
     */
    private String fbaShipmentId;

    /**
     * orderNo
     */
    private String orderNo;


    private String platformName;

    /**
     * shipment重量
     */
    private BigDecimal weight;

    private BigDecimal predictTotalPrice;

    private BigDecimal realPrice;

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 部门
     */
    private String businessType;

    /**
     * 发货时间
     */
    private Date deliveryDate;

    /**
     * 店铺暂估基础物流费用
     */
    private BigDecimal storePredictBaseLogisticsFee;

    /**
     * 店铺暂估税费
     */
    private BigDecimal storePredictTaxFee;

    /**
     * 店铺暂估报关费
     */
    private BigDecimal storePredictCustomsFee;

    /**
     * 店铺暂估物流总费用
     */
    private BigDecimal storePredictTotalLogisticsFee;

    /**
     * 店铺实际基础物流费用
     */
    private BigDecimal storeRealBaseLogisticsFee;

    /**
     * 店铺实际税费
     */
    private BigDecimal storeRealTaxFee;

    /**
     * 店铺实际报关费
     */
    private BigDecimal storeRealCustomsFee;

    /**
     * 店铺实际物流总费用
     */
    private BigDecimal storeRealTotalLogisticsFee;

    /**
     * 店铺实际基础物流费用与暂估差异
     */
    private BigDecimal storeBaseLogisticsFeeDiff;

    /**
     * 店铺实际报关费与暂估差异
     */
    private BigDecimal storeCustomsFeeDiff;

    /**
     * 店铺实际税费与暂估差异
     */
    private BigDecimal storeTaxFeeDiff;

    /**
     * 店铺实际物流总费用与暂估差异
     */
    private BigDecimal storeTotalLogisticsFeeDiff;

    public BigDecimal getStorePredictBaseLogisticsFee() {
        return storePredictBaseLogisticsFee;
    }

    public void setStorePredictBaseLogisticsFee(BigDecimal storePredictBaseLogisticsFee) {
        this.storePredictBaseLogisticsFee = storePredictBaseLogisticsFee;
    }

    public BigDecimal getStorePredictTaxFee() {
        return storePredictTaxFee;
    }

    public void setStorePredictTaxFee(BigDecimal storePredictTaxFee) {
        this.storePredictTaxFee = storePredictTaxFee;
    }

    public BigDecimal getStorePredictCustomsFee() {
        return storePredictCustomsFee;
    }

    public void setStorePredictCustomsFee(BigDecimal storePredictCustomsFee) {
        this.storePredictCustomsFee = storePredictCustomsFee;
    }

    public BigDecimal getStorePredictTotalLogisticsFee() {
        return storePredictTotalLogisticsFee;
    }

    public void setStorePredictTotalLogisticsFee(BigDecimal storePredictTotalLogisticsFee) {
        this.storePredictTotalLogisticsFee = storePredictTotalLogisticsFee;
    }

    public BigDecimal getStoreRealBaseLogisticsFee() {
        return storeRealBaseLogisticsFee;
    }

    public void setStoreRealBaseLogisticsFee(BigDecimal storeRealBaseLogisticsFee) {
        this.storeRealBaseLogisticsFee = storeRealBaseLogisticsFee;
    }

    public BigDecimal getStoreRealTaxFee() {
        return storeRealTaxFee;
    }

    public void setStoreRealTaxFee(BigDecimal storeRealTaxFee) {
        this.storeRealTaxFee = storeRealTaxFee;
    }

    public BigDecimal getStoreRealCustomsFee() {
        return storeRealCustomsFee;
    }

    public void setStoreRealCustomsFee(BigDecimal storeRealCustomsFee) {
        this.storeRealCustomsFee = storeRealCustomsFee;
    }

    public BigDecimal getStoreRealTotalLogisticsFee() {
        return storeRealTotalLogisticsFee;
    }

    public void setStoreRealTotalLogisticsFee(BigDecimal storeRealTotalLogisticsFee) {
        this.storeRealTotalLogisticsFee = storeRealTotalLogisticsFee;
    }

    public BigDecimal getStoreBaseLogisticsFeeDiff() {
        return storeBaseLogisticsFeeDiff;
    }

    public void setStoreBaseLogisticsFeeDiff(BigDecimal storeBaseLogisticsFeeDiff) {
        this.storeBaseLogisticsFeeDiff = storeBaseLogisticsFeeDiff;
    }

    public BigDecimal getStoreCustomsFeeDiff() {
        return storeCustomsFeeDiff;
    }

    public void setStoreCustomsFeeDiff(BigDecimal storeCustomsFeeDiff) {
        this.storeCustomsFeeDiff = storeCustomsFeeDiff;
    }

    public BigDecimal getStoreTaxFeeDiff() {
        return storeTaxFeeDiff;
    }

    public void setStoreTaxFeeDiff(BigDecimal storeTaxFeeDiff) {
        this.storeTaxFeeDiff = storeTaxFeeDiff;
    }

    public BigDecimal getStoreTotalLogisticsFeeDiff() {
        return storeTotalLogisticsFeeDiff;
    }

    public void setStoreTotalLogisticsFeeDiff(BigDecimal storeTotalLogisticsFeeDiff) {
        this.storeTotalLogisticsFeeDiff = storeTotalLogisticsFeeDiff;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Integer getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Integer referenceId) {
        this.referenceId = referenceId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getPredictTotalPrice() {
        return predictTotalPrice;
    }

    public void setPredictTotalPrice(BigDecimal predictTotalPrice) {
        this.predictTotalPrice = predictTotalPrice;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getLogisticsBatchId() {
        return logisticsBatchId;
    }

    public void setLogisticsBatchId(Integer logisticsBatchId) {
        this.logisticsBatchId = logisticsBatchId;
    }

    public String getLogisticsBatch() {
        return logisticsBatch;
    }

    public void setLogisticsBatch(String logisticsBatch) {
        this.logisticsBatch = logisticsBatch;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

}

