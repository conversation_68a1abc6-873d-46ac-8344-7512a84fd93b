package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stockout.StockoutPickingBoxCodePrint;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.ProductSizeSegmentEnum;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingScanningStationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockLendItemService;
import com.nsy.wms.business.service.stock.StockPrintService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.utils.BarCodeUtils;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.PrintTransferUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * HXD
 * 2022/1/19
 **/
@Service
public class StockoutBatchPrintService {
    @Autowired
    StockoutPickingTaskService pickingTaskService;
    @Autowired
    StockoutPickingTaskPrintService pickingTaskPrintService;
    @Autowired
    StockoutOrderService orderService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    StockPrintService stockPrintService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    StockoutPickingItemRecordService stockoutPickingItemRecordService;
    @Autowired
    StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    StockoutPickingTaskPrintService taskPrintService;
    @Resource
    BdTagMappingService tagMappingService;
    @Resource
    StockoutOrderTemuExtendInfoService stockoutOrderTemuExtendInfoService;
    @Autowired
    StockLendItemService stockLendItemService;
    @Autowired
    BdTagMappingService bdTagMappingService;
    @Resource
    StockoutBatchOrderService stockoutBatchOrderService;
    @Resource
    StockoutOrderPrintService stockoutOrderPrintService;
    @Autowired
    StockoutVasTaskService stockoutVasTaskService;

    @Transactional
    public PrintListResponse printPickingTaskInBatch(IdListRequest request) {
        // 波次判断 已生成拣货任务才允许打印
        List<StockoutBatchEntity> stockoutBatchEntities = stockoutBatchService.listByIds(request.getIdList());
        if (stockoutBatchEntities.stream().anyMatch(entity ->
                StockoutWaveTaskStatusEnum.NEW.name().equals(entity.getStatus())
                        || StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name().equals(entity.getStatus())))
            throw new BusinessServiceException("请先生成拣货任务！");

        // 获取合并波次子波次ID
        LinkedList<Integer> batchIds = new LinkedList<>(request.getIdList());
        stockoutBatchEntities.stream().filter(batchEntity -> StockConstant.ENABLE.equals(batchEntity.getIsMergeBatch()))
                .forEach(batchEntity -> {
                    batchIds.addAll(stockoutBatchService.getSubBatchIdByBatchId(batchEntity.getBatchId()).stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList()));
                });


        List<StockoutPickingTaskEntity> list = pickingTaskService.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().select(StockoutPickingTaskEntity::getTaskId).in(StockoutPickingTaskEntity::getBatchId, batchIds));
        List<Integer> ids = list.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessServiceException("无法找到拣货任务");
        }
        // 缝制区拣货任务
        addSewTaskIdsByBatch(stockoutBatchEntities, ids);
        IdListRequest listRequest = new IdListRequest();
        listRequest.setIdList(ids);
        PrintListResponse response = pickingTaskPrintService.printPickingTask(listRequest);
        List<StockoutBatchEntity> collect = stockoutBatchEntities.stream().map(batchEntity -> {
            StockoutBatchEntity entity = new StockoutBatchEntity();
            entity.setIsPrint(1);
            entity.setUpdateBy(loginInfoService.getName());
            entity.setBatchId(batchEntity.getBatchId());
            entity.setMergeBatchId(batchEntity.getMergeBatchId());
            stockoutBatchLogService.addLog(entity.getBatchId(), StockoutBatchLogTypeEnum.PRINT_PICKING_TASK.getStockoutBatchLogType(), "打印波次下的拣货任务(热敏)");
            return entity;
        }).collect(Collectors.toList());
        stockoutBatchService.updateBatchById(collect);
        return response;
    }

    /**
     * 增加 合并波次，子波次的缝制拣货任务
     */
    private void addSewTaskIdsByBatch(List<StockoutBatchEntity> stockoutBatchEntities, List<Integer> ids) {
        List<Integer> mergeBatchIds = stockoutBatchEntities.stream().filter(o -> o.getIsMergeBatch() != null && o.getIsMergeBatch().equals(1)).map(StockoutBatchEntity::getBatchId).collect(Collectors.toList());
        if (mergeBatchIds.isEmpty()) {
            return;
        }
        List<Integer> subBatchIds = stockoutBatchService.list(new QueryWrapper<StockoutBatchEntity>().lambda().in(StockoutBatchEntity::getMergeBatchId, mergeBatchIds)).stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList());
        if (subBatchIds.isEmpty()) {
            return;
        }
        List<Integer> sewTaskIds = pickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda().in(StockoutPickingTaskEntity::getBatchId, subBatchIds)).stream()
                .filter(o -> StockoutPickingTaskTypeEnum.SEW_PICKING.name().equals(o.getTaskType()))
                .map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        if (sewTaskIds.isEmpty()) {
            return;
        }
        ids.addAll(sewTaskIds);
    }

    public PrintListResponse printProcessPickingTaskA4ByBatchId(IdListRequest request) {
        List<StockoutPickingTaskEntity> list = pickingTaskService.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().in(StockoutPickingTaskEntity::getBatchId, request.getIdList()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("波次还未生成拣货任务！");
        }
        if (request.getIdList().stream().anyMatch(id -> list.stream().noneMatch(taskEntity -> taskEntity.getBatchId().equals(id)))) {
            throw new BusinessServiceException("波次还未生成拣货任务！");
        }
        List<Integer> collect = list.stream().filter(item -> (StockoutPickingTaskTypeEnum.PROCESSING_PICKING.name().equals(item.getTaskType())
                        || StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name().equals(item.getTaskType())) && Objects.equals(item.getStatus(), StockoutPickingTaskStatusEnum.WAIT_PICK.name()))
                .map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new BusinessServiceException("该波次无加工拣货任务");
        }
        IdListRequest listRequest = new IdListRequest();
        listRequest.setIdList(collect);
        return taskPrintService.printPickingTaskA4(listRequest);
    }

    // 按单拣货的波次打印拣货单， 所有的任务打在一张纸张里面
    public Map<String, Object> printBatchWhole(List<StockoutBatchOrderEntity> allByBatchId, StockoutBatchEntity batchEntity, List<StockoutPickingTaskEntity> pickingTaskEntityList) {
        List<String> collect = pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getPickingBoxCode).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        String pickingBoxCode = !CollectionUtils.isEmpty(collect) ? collect.get(0) : buildPickingBoxCode(pickingTaskEntityList);
        List<Integer> orderIds = allByBatchId.stream().map(StockoutBatchOrderEntity::getStockoutOrderId).collect(Collectors.toList());
        List<StockoutOrderEntity> stockoutOrderEntitys = orderService.listByIds(orderIds);
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.listByStockoutOrderIds(orderIds);
        StockoutReceiverInfo receiverInfo = stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(orderIds.get(0));
        List<String> skuList = orderItemEntityList.stream().map(StockoutOrderItemEntity::getSku).distinct().collect(Collectors.toList());
        List<Integer> productIdList = orderItemEntityList.stream().map(StockoutOrderItemEntity::getProductId).distinct().collect(Collectors.toList());
        Map<Integer, String> productMap = productInfoService.findByProductIds(productIdList).stream().filter(it -> StrUtil.isNotBlank(it.getPackageVacuum())).collect(Collectors.toMap(ProductInfoEntity::getProductId, ProductInfoEntity::getPackageVacuum, (v1, v2) -> v1));
        Map<String, List<String>> productTagBySkus = tagMappingService.getProductTagBySkus(skuList);
        List<StockoutPickingTaskItemInfo> stockoutPickingTaskItemEntityList = stockoutPickingTaskItemService.getBaseMapper().findAllByTaskIdOrderBySpace(pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList()));
        Map<String, Integer> storeFirstOrderMap = orderItemEntityList.stream().filter(it -> it.getSku() != null && it.getIsFirstOrderByStore() != null).collect(Collectors.toMap(StockoutOrderItemEntity::getSku, StockoutOrderItemEntity::getIsFirstOrderByStore, (v1, v2) -> v1));
        Map<String, Boolean> isLeadGenerationMap = orderItemEntityList.stream().filter(it -> it.getSku() != null && it.getIsLeadGeneration() != null).collect(Collectors.toMap(StockoutOrderItemEntity::getSku, StockoutOrderItemEntity::getIsLeadGeneration, (v1, v2) -> v1));
        Map<String, String> vasTaskMap = stockoutVasTaskService.getSotrePositionVasTaskMapByNo(stockoutOrderEntitys.stream().map(StockoutOrderEntity::getStockoutOrderNo).distinct().collect(Collectors.toList()));
        Set<String> transparencySet = orderItemEntityList.stream().filter(it -> it.getSku() != null && it.getIsTransparency()).map(StockoutOrderItemEntity::getSku).collect(Collectors.toSet());
        Map<String, Object> map = new HashMap<>(32);
        if (!CollectionUtils.isEmpty(stockoutPickingTaskItemEntityList) && stockoutPickingTaskItemEntityList.stream().allMatch(taskItem -> ProcessConstant.AFTER_PROCESS_POSITION_AREA_NAME.equalsIgnoreCase(taskItem.getSpaceAreaName())))
            return map;
        map.put("batchId", batchEntity.getIsMergeBatch() == 0 ? allByBatchId.get(0).getBatchId() : "(合并波次)" + batchEntity.getBatchId());
        map.put("workspace", StockoutOrderWorkSpaceEnum.getNameBy(batchEntity.getWorkspace()));
        map.put("pickingType", StockoutPickingTypeEnum.getNameBy(stockoutOrderEntitys.get(0).getPickingType()));
        map.put("taskId", "波次号：" + (batchEntity.getIsMergeBatch() == 0 ? batchEntity.getBatchId() : "(合并波次)" + batchEntity.getBatchId()));
        map.put("pack", stockoutOrderEntitys.stream().anyMatch(StockoutOrderEntity::getHasPack) ? "(Pack商品)" : "");
        map.put("replenishNew", stockoutOrderEntitys.stream().anyMatch(order -> FbaReplenishTypeEnum.REPLENISH.getName().equalsIgnoreCase(order.getFbaReplenishType())) ? "(新)" : "");
        buildFirstPrint(map, batchEntity);
        map.put("internalBoxCode", pickingBoxCode);
        map.put("internalBoxCodeImg", BarCodeUtils.getBarCodeBase64(pickingBoxCode));
        map.put("orderNo", StringUtils.join(orderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList()), ','));
        map.put("storeName", batchEntity.getIsMergeBatch() == 0 ? stockoutOrderEntitys.get(0).getStoreName() : null);
        pickingTaskService.buildReceiver(map, receiverInfo);
        map.put("logisticsCompany", stockoutOrderEntitys.stream().map(StockoutOrderEntity::getLogisticsCompany).filter(StringUtils::hasText).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        map.put("qty", orderItemEntityList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum());
        BigDecimal weight = StockoutBuilding.getPayWeight(orderItemEntityList, productSpecInfoService.findAllBySkuIn(skuList));
        buildDateAndWeight(map, weight);
        map.put("transparency", orderItemEntityList.stream().anyMatch(StockoutOrderItemEntity::getIsTransparency) ? "(透明计划)" : "");
        processInfo(batchEntity, map);
        map.put("taskIdImg", BarCodeUtils.getBarCodeBase64(batchEntity.getBatchId().toString()));
        buildOrderMsg(stockoutOrderEntitys, orderItemEntityList, map);
        Map<String, String> productVersionMap = stockLendItemService.getProductVersionByLendCode(stockoutOrderEntitys.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList()));
        StockoutBuilding.getItemList(map, stockoutPickingTaskItemEntityList.stream().filter(it -> !ProcessConstant.AFTER_PROCESS_POSITION_AREA_NAME.equalsIgnoreCase(it.getSpaceAreaName())).collect(Collectors.toList()), storeFirstOrderMap, productTagBySkus, transparencySet, productVersionMap, productMap, isLeadGenerationMap, vasTaskMap);
        map.put("scanTypeMsg", pickingTaskEntityList.stream().map(it -> StockoutSortingScanningStationEnum.getNameBy(it.getScanType())).distinct().collect(Collectors.joining(",")));
        brandInfo(orderItemEntityList, map);
        map.put("isPdd", stockoutOrderTemuExtendInfoService.validTemuOrder(stockoutOrderEntitys.get(0).getStockoutOrderNo()));
        map.put("platformName", StringUtils.hasText(stockoutOrderEntitys.get(0).getPlatformName()) ? enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKOUT_ORDER_PLATFORM_NAME.getName(), stockoutOrderEntitys.get(0).getPlatformName()) : "");
        map.put("titleInfo", buildTitleInfo(orderItemEntityList, stockoutOrderEntitys));
        map.put("urgent", stockoutOrderEntitys.get(0).getUrgent());
        map.put("productSizeSegment", ProductSizeSegmentEnum.getCn(stockoutOrderEntitys.get(0).getProductSizeSegment()));
        map.put("fbaReplenish", stockoutOrderEntitys.stream().map(StockoutOrderEntity::getFbaReplenishType).filter(StringUtils::hasText).findAny().orElse(""));
        map.put("replenishOrder", stockoutOrderEntitys.get(0).getReplenishOrder());
        return map;
    }

    private void buildDateAndWeight(Map<String, Object> map, BigDecimal weight) {
        map.put("date", String.format("%s月%s日", LocalDate.now().getMonthValue(), LocalDate.now().getDayOfMonth()));
        map.put("nowDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        map.put("payWeight", weight);
        map.put("weight", weight);
    }

    private void buildOrderMsg(List<StockoutOrderEntity> stockoutOrderEntitys, List<StockoutOrderItemEntity> orderItemEntityList, Map<String, Object> map) {
        map.put("stockoutOrderNoImg", BarCodeUtils.getBarCodeBase64(stockoutOrderEntitys.get(0).getStockoutOrderNo()));
        map.put("stockoutOrderNo", stockoutOrderEntitys.get(0).getStockoutOrderNo());
        pickingTaskPrintService.buildSpaceMemo(map, orderItemEntityList);
        map.put("orderNoImgList", StockoutBuilding.getOrderNoImgList(orderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList())));
    }

    private void brandInfo(List<StockoutOrderItemEntity> orderItemEntityList, Map<String, Object> map) {
        // 品牌名称
        List<String> brandNames = orderItemEntityList.stream().map(StockoutOrderItemEntity::getChangeStoreName).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        map.put("brandName", CollectionUtils.isEmpty(brandNames) ? "" : "(" + String.join(",", brandNames) + ")");
    }

    private void processInfo(StockoutBatchEntity batchEntity, Map<String, Object> map) {
        if (StockoutOrderTypeEnum.LIGHT_CUSTOMIZATION_DELIVERY.name().equals(batchEntity.getStockoutType())) {
            map.put("processType", "(加工备货)");
        } else if (1 == batchEntity.getIsNeedProcess()) {
            map.put("processType", "(加工)");
        } else {
            map.put("processType", "");
        }
    }

    private String buildPickingBoxCode(List<StockoutPickingTaskEntity> pickingTaskEntityList) {
        String pickingBoxCode = stockPrintService.getPickBoxAndUpdatePrint(pickingTaskEntityList.get(0));
        for (StockoutPickingTaskEntity taskEntity : pickingTaskEntityList) {
            taskEntity.setUpdateBy(loginInfoService.getName());
            taskEntity.setPickingBoxCode(pickingBoxCode);
        }
        pickingTaskService.updateBatchById(pickingTaskEntityList);
        return pickingBoxCode;
    }

    private void buildFirstPrint(Map<String, Object> map, StockoutBatchEntity batchEntity) {
        if (batchEntity.getIsPrint() == 1) {
            map.put("firstPrint", "多次打印");
        } else {
            map.put("firstPrint", "首次打印");
        }
        map.put("totalOrderNum", stockoutOrderItemService.getBaseMapper().getOrderNumByBatch(batchEntity.getBatchId()));
    }


    @Transactional
    public PrintListResponse printPickingBoxCode(IdListRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_PICKING.getTemplateName());
        List<StockoutBatchEntity> stockoutBatchEntityList = stockoutBatchService.listByIds(request.getIdList());
        stockoutBatchEntityList.stream()
                .filter(entity -> StockoutWaveTaskStatusEnum.NEW.name().equals(entity.getStatus())
                        || StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name().equals(entity.getStatus())
                        || StockoutWaveTaskStatusEnum.WAIT_PICK.name().equals(entity.getStatus())
                        || StockoutWaveTaskStatusEnum.PICKING.name().equals(entity.getStatus()))
                .findAny()
                .ifPresent(batchEntity -> {
                    throw new BusinessServiceException(String.format("该波次【%s】尚未完成拣货，无法打印拣货箱号！", batchEntity.getBatchId()));
                });
        List<StockoutPickingTaskEntity> pickingTaskEntityList = stockoutPickingTaskService.findByBatchIds(request.getIdList());
        pickingTaskEntityList.stream().filter(taskEntity -> !StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())).findAny()
                .ifPresent(taskEntity -> {
                    throw new BusinessServiceException(String.format("该波次【%s】尚未完成拣货，无法打印拣货箱号！", taskEntity.getBatchId()));
                });
        Map<Integer, List<StockoutPickingBoxCodePrint>> collect = stockoutPickingItemRecordService.getBaseMapper().getPrintPickingBoxCodeByTask(request.getIdList())
                .stream().collect(Collectors.groupingBy(StockoutPickingBoxCodePrint::getBatchId));
        List<String> result = new ArrayList<>();
        collect.entrySet().forEach(entry -> {
            List<StockoutPickingBoxCodePrint> value = entry.getValue().stream().sorted(Comparator.comparing(StockoutPickingBoxCodePrint::getUpdateDate)).collect(Collectors.toList());
            int index = 9312; //字符①
            for (StockoutPickingBoxCodePrint print : value) {
                print.setIndex(Character.toString((char) index));
                result.add(PrintTransferUtils.transfer(templateEntity.getContent(), print));
                index++;
            }
        });
        response.setHtmlList(result);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 组装标题头
     *
     * @param orderItemEntityList
     * @param stockoutOrderEntitys
     * @return
     */
    public String buildTitleInfo(List<StockoutOrderItemEntity> orderItemEntityList, List<StockoutOrderEntity> stockoutOrderEntitys) {
        List<String> brandNames = orderItemEntityList.stream().map(StockoutOrderItemEntity::getChangeStoreName).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        String transparency = orderItemEntityList.stream().anyMatch(StockoutOrderItemEntity::getIsTransparency) ? "透明计划" : "";
        if (!CollectionUtils.isEmpty(brandNames) && StringUtils.hasText(transparency)) {
            brandNames.add(transparency);
        }
        if (CollectionUtils.isEmpty(brandNames) && StringUtils.hasText(transparency)) {
            brandNames = Lists.newArrayList(transparency);
        }
        Boolean labelOrderFlag = bdTagMappingService.validWithLabelOrder(stockoutOrderEntitys.get(0).getStockoutOrderNo());
        if (labelOrderFlag && !CollectionUtils.isEmpty(brandNames)) {
            brandNames.add("有标单");
        }
        if (labelOrderFlag && CollectionUtils.isEmpty(brandNames)) {
            brandNames = Collections.singletonList("有标单");
        }
        return CollectionUtils.isEmpty(brandNames) ? "" : "出库单标签：" + String.join(",", brandNames);
    }

    /**
     * 打印出库单
     * 波次里只包含了一单才需要打印；如果是波次包含多个订单则不允许打印，需弹窗提示：只允许打印包含一单的波次！
     *
     * @param request
     * @return
     */
    public PrintListResponse printStockoutOrderA4(IdListRequest request) {
        List<Integer> stockoutOrderIdList = request.getIdList().stream().map(batchId -> {
            List<StockoutBatchOrderEntity> batchOrderList = stockoutBatchOrderService.findAllByBatchId(batchId);
            if (batchOrderList.isEmpty())
                throw new BusinessServiceException(String.format("%s 波次下没有出库单", batchId));
            if (batchOrderList.size() > 1)
                throw new BusinessServiceException(String.format("%s 波次下有多个出库单", batchId));
            return batchOrderList.get(0).getStockoutOrderId();
        }).collect(Collectors.toList());
        IdListRequest idListRequest = new IdListRequest();
        idListRequest.setIdList(stockoutOrderIdList);
        return stockoutOrderPrintService.printStockoutOrderA4(idListRequest);
    }
}
