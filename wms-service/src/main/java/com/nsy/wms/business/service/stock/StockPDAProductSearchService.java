package com.nsy.wms.business.service.stock;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stock.Stock;
import com.nsy.api.wms.domain.stock.StockPDASkuInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockPDAProductSearchRequest;
import com.nsy.api.wms.response.stock.StockPDAProductSearchResponse;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskItemService;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HXD
 * 2021/8/27
 **/
@Service
public class StockPDAProductSearchService {
    @Autowired
    private StockService stockService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;

    public StockPDAProductSearchResponse searchList(StockPDAProductSearchRequest request) {
        ProductInfoEntity productInfoEntity = null;
        String sku = "";
        String spu = "";
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(request.getCodeOrSku());
        if (productSpecInfo == null) {
            //查询店铺条码
            productSpecInfo = productSpecInfoService.findByStoreBarcode(request.getCodeOrSku());
        }
        if (productSpecInfo == null) {
            productInfoEntity = productInfoService.findTopBySpu(request.getCodeOrSku());
            if (productInfoEntity == null)
                throw new BusinessServiceException(request.getCodeOrSku() + "商品不存在");
            else
                spu = request.getCodeOrSku();
        } else {
            sku = productSpecInfo.getSku();
        }
        List<String> positionTypeList = getPositionTypeList(request);
        List<Stock> list = stockService.findForPDASearch(positionTypeList, sku, spu, null, request.getSpaceId());
        StockPDAProductSearchResponse searchResponse = new StockPDAProductSearchResponse();
        if (productInfoEntity != null) {
            searchResponse.setSpu(productInfoEntity.getSpu());
            searchResponse.setImage(productInfoEntity.getImageUrl());
            searchResponse.setWeight(productInfoEntity.getWeight());
            searchResponse.setVolumeWeight(productInfoEntity.getVolumeWeight());
            searchResponse.setSize(productInfoEntity.getPackageSize());
            if (!CollectionUtils.isEmpty(list)) {
                searchResponse.setSkuList(makeUpSkuList(list));
            }
        } else {
            ProductInfoEntity product = productInfoService.findTopByProductId(productSpecInfo.getProductId());
            searchResponse.setSku(productSpecInfo.getSku());
            searchResponse.setImage(productSpecInfo.getImageUrl());
            searchResponse.setWeight(productSpecInfo.getWeight());
            searchResponse.setActualWeight(productSpecInfo.getActualWeight());
            searchResponse.setVolumeWeight(productSpecInfo.getVolumeWeight());
            searchResponse.setSize(product.getPackageSize());
            if (!Objects.equals(request.getIsSparePosition(), 1))
                buildInternalBoxStock(list, sku, null, request.getSpaceId());
            if (!CollectionUtils.isEmpty(list))
                searchResponse.setPositionList(makeUpPositionList(list));
        }
        return searchResponse;
    }

    /**
     * 查询同款
     */
    public StockPDAProductSearchResponse searchSameSkcList(StockPDAProductSearchRequest request) {
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.findTopBySku(request.getCodeOrSku());
        if (productSpecInfo == null) {
            productSpecInfo = productStoreSkuMappingService.validBarcode(request.getCodeOrSku());
        }
        StockPDAProductSearchResponse searchResponse = new StockPDAProductSearchResponse();
        if (productSpecInfo == null) {
            throw new BusinessServiceException(request.getCodeOrSku() + "商品不存在");
        }
        List<String> positionTypeList = getPositionTypeList(request);
        List<Stock> content = stockService.findForPDASearch(positionTypeList, null, null, productSpecInfo.getSkc(), request.getSpaceId());

        ProductInfoEntity product = productInfoService.findTopByProductId(productSpecInfo.getProductId());
        searchResponse.setSku(productSpecInfo.getSku());
        searchResponse.setImage(productSpecInfo.getImageUrl());
        searchResponse.setWeight(productSpecInfo.getWeight());
        searchResponse.setActualWeight(productSpecInfo.getActualWeight());
        searchResponse.setVolumeWeight(productSpecInfo.getVolumeWeight());
        searchResponse.setSize(product.getPackageSize());
        buildInternalBoxStock(content, null, productSpecInfo.getSkc(), request.getSpaceId());
        if (!CollectionUtils.isEmpty(content)) {
            String sku = productSpecInfo.getSku();
            List<Stock> filterContent = content.stream().filter(item -> !sku.equals(item.getSku()) && item.getStock() != 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterContent)) {
                searchResponse.setPositionList(makeUpPositionList(filterContent));
            }
        }
        return searchResponse;
    }

    private void buildInternalBoxStock(List<Stock> content, String sku, String skc, Integer spaceId) {
        List<Stock> internalBoxStockList;
        if (StringUtils.hasText(skc)) {
            internalBoxStockList = stockService.findForPDASearchInternalBoxStock(null, skc, spaceId);
        } else {
            internalBoxStockList = stockService.findForPDASearchInternalBoxStock(sku, null, spaceId);
        }
        if (!CollectionUtils.isEmpty(internalBoxStockList)) {
            List<Stock> filterList = internalBoxStockList.stream().filter(item -> item.getStock() > 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filterList)) {
                content.addAll(filterList);
            }
        }
    }

    private List<StockPDASkuInfo> makeUpSkuList(List<Stock> content) {
        Map<String, StockPDASkuInfo> map = new HashMap<>();
        content.forEach(item -> {
            StockPDASkuInfo skuInfo = map.get(item.getSku());
            if (skuInfo != null) {
                int stockNum = item.getStock() == null ? 0 : item.getStock();
                skuInfo.setStock(skuInfo.getStock() + stockNum);
            } else {
                skuInfo = new StockPDASkuInfo();
                int stockNum = item.getStock() == null ? 0 : item.getStock();
                skuInfo.setStockId(item.getStockId());
                skuInfo.setStock(stockNum);
                skuInfo.setSku(item.getSku());
                skuInfo.setPreMatchQty(0);
                map.put(skuInfo.getSku(), skuInfo);
            }
        });
        List<StockPDASkuInfo> skuList = new ArrayList<>(map.values());
        skuList.forEach(skuInfo -> {
            skuInfo.setPreMatchQty(stockPrematchInfoService.getSkuPreQty(skuInfo.getSku(), skuInfo.getLocationCode()));
            skuInfo.setPickingNum(stockoutPickingTaskItemService.countScanQtyByStatusAndSku(StockoutPickingTaskStatusEnum.PICKING.toString(), skuInfo.getSku(), skuInfo.getLocationCode()));
        });
        return skuList;
    }

    private List<StockPDASkuInfo> makeUpPositionList(List<Stock> content) {
        Map<String, String> internalBoxTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        Map<String, String> positionTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_POSITION_TYPE.getName());

        return content.stream().map(stock -> {
            StockPDASkuInfo skuInfo = new StockPDASkuInfo();
            skuInfo.setStockId(stock.getStockId());
            skuInfo.setSku(stock.getSku());
            skuInfo.setStock(stock.getStock());
            skuInfo.setLocationCode(stock.getPositionCode());
            skuInfo.setInternalBoxType(internalBoxTypeEnumMap.get(stock.getInternalBoxType()));
            skuInfo.setInternalBoxCode(stock.getInternalBoxCode());
            skuInfo.setPositionTypeName(positionTypeEnumMap.get(stock.getPositionType()));
            skuInfo.setPreMatchQty(stockPrematchInfoService.getSkuPreQty(skuInfo.getSku(), skuInfo.getLocationCode()));
            skuInfo.setPickingNum(stockoutPickingTaskItemService.countScanQtyByStatusAndSku(StockoutPickingTaskStatusEnum.PICKING.toString(), skuInfo.getSku(), skuInfo.getLocationCode()));
            return skuInfo;
        }).collect(Collectors.toList());
    }


    private List<String> getPositionTypeList(StockPDAProductSearchRequest request) {
        List<String> list = new ArrayList<>();
        if (Objects.equals(request.getIsSparePosition(), 1)) {
            list.add(BdPositionTypeEnum.SPARE_POSITION.name());
        } else {
            list.add(BdPositionTypeEnum.SPARE_POSITION.name());
            list.add(BdPositionTypeEnum.CROSS_POSITION.name());
            list.add(BdPositionTypeEnum.STOCK_POSITION.name());
            list.add(BdPositionTypeEnum.EXCEPTION_POSITION.name());
            list.add(BdPositionTypeEnum.OEM_POSITION.name());
            list.add(BdPositionTypeEnum.ACTIVITY_POSITION.name());
            list.add(BdPositionTypeEnum.RETURN_POSITION.name());
            list.add(BdPositionTypeEnum.RETURN_DEMAGE_POSITION.name());
            list.add(BdPositionTypeEnum.STORE_POSITION.name());
        }
        return list;
    }
}
