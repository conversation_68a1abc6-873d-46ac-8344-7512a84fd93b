package com.nsy.wms.controller.internal.scm;

import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.ReturnRecordRequest;
import com.nsy.api.wms.request.stockin.ReworkReturnsReceiptRequest;
import com.nsy.api.wms.request.stockin.StockinReturnOrderItemPageRequest;
import com.nsy.api.wms.request.stockin.StockinReturnOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.QueryReworkedReturnedRecordResponse;
import com.nsy.api.wms.response.stockin.QueryWaitReturnRecordResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderItemPageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderPageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderStatisticsResponse;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.business.service.internal.scm.ReturnOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * 2022/03/17
 */
@RestController
@Api(tags = "提供给scm,退货单列表相关接口")
public class StockinReturnController extends BaseController {
    @Autowired
    ReturnOrderService returnOrderService;

    @ApiOperation(value = "退货单列表-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/list", method = RequestMethod.POST)
    public PageResponse<StockinReturnOrderPageResponse> pageList(@RequestBody StockinReturnOrderPageRequest request) {
        return returnOrderService.pageList(request);
    }

    @ApiOperation(value = "退货单统计-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/list/statistics", method = RequestMethod.POST)
    public StockinReturnOrderStatisticsResponse statisticsStockinReturnOrderList(@RequestBody StockinReturnOrderPageRequest request) {
        return returnOrderService.statisticsStockinReturnOrderList(request);
    }

    @ApiOperation(value = "退货单明细列表-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/item-list", method = RequestMethod.POST)
    public PageResponse<StockinReturnOrderItemPageResponse> pageItemList(@RequestBody StockinReturnOrderItemPageRequest request) {
        return returnOrderService.pageItemList(request);
    }
    @ApiOperation(value = "强制完成按钮-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/force-complete", method = RequestMethod.PUT)
    public void returnForeComplete(@Valid @RequestBody IdListRequest request) {
        returnOrderService.returnForeComplete(request.getIdList());
    }

    @ApiOperation(value = "返工退货收货-supplier", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/rework-returns-receipt", method = RequestMethod.PUT)
    public void reworkReturnsReceipt(@Valid @RequestBody ReworkReturnsReceiptRequest request) {
        returnOrderService.reworkReturnsReceipt(request);
    }

    @ApiOperation(value = "待退货记录-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/wait-return-record", method = RequestMethod.POST)
    public PageResponse<QueryWaitReturnRecordResponse> waitReturnRecord(@Valid @RequestBody ReturnRecordRequest request) {
        return returnOrderService.waitReturnRecord(request);
    }

    @ApiOperation(value = "已退货返工记录-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/reworked-returned-record", method = RequestMethod.POST)
    public PageResponse<QueryReworkedReturnedRecordResponse> reworkedReturnedRecord(@Valid @RequestBody ReturnRecordRequest request) {
        return returnOrderService.reworkedReturnedRecord(request);
    }

    @ApiOperation(value = "返工退货待收数记录-scm", produces = "application/json")
    @RequestMapping(value = "/stockin-return-order/reworked-wait-receive-record", method = RequestMethod.POST)
    public PageResponse<QueryReworkedReturnedRecordResponse> reworkedWaitReceiveRecord(@Valid @RequestBody ReturnRecordRequest request) {
        return returnOrderService.reworkedWaitReceiveRecord(request);
    }
}
