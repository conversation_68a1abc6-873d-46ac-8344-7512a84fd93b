<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper">
    <select id="searchOrderNo" resultType="java.lang.String">
        SELECT item.order_no FROM
        stockout_shipment_item item
        LEFT JOIN stockout_shipment o ON o.shipment_id = item.shipment_id
        <where>
            <if test="shipmentBoxCode!=null and shipmentBoxCode !=''">
                and o.shipment_box_code = #{shipmentBoxCode}
            </if>
            and item.is_deleted = 0
        </where>
        GROUP BY item.order_no
    </select>


    <select id="searchShipmentItemSku" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemSku">
        SELECT
        item.order_no AS orderNo,
        item.order_item_id AS orderItemId,
        item.stockout_order_no AS stockoutOrderNo,
        item.sku AS sku,
        s.barcode AS barcode,
        item.qty AS qty
        FROM stockout_shipment_item item
        LEFT JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = item.spec_id
        <where>
            <if test="shipmentBoxCode!=null and shipmentBoxCode!='' ">
                and t.shipment_box_code = #{shipmentBoxCode}
            </if>
            and item.is_deleted = 0
        </where>
    </select>

    <select id="searchShipmentBoxCodeList" resultType="java.lang.String">
        SELECT
        DISTINCT t.shipment_box_code
        FROM stockout_shipment_item item
        LEFT JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        <where>
            <if test="stockoutOrderNo!=null and stockoutOrderNo!='' ">
                and item.stockout_order_no != #{stockoutOrderNo} and t.status != 'SHIPPED'
            </if>
            <if test="orderNoList!=null and orderNoList.size() > 0 ">
                and item.order_no in
                <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            and item.is_deleted = 0
        </where>
    </select>

    <select id="searchDeclareInfoListXZ" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemDeclareInfo">
        SELECT
        si.order_no AS orderNo,
        ANY_VALUE(si.stockout_order_no) AS stockoutOrderNo,
        SUM( si.qty ) AS qty,
        MAX( wc.`name` ) AS customsDeclareChineseName,
        MAX( wc.english_name ) AS customsDeclareEnglishName,
        MAX( c.hs_code ) AS hsCode,
        MAX( sh.box_size ) AS boxSize,
        MAX( sh.shipment_id ) AS shipmentId,
        MAX( sh.weight ) AS weight,
        ROUND(SUM( oi.invoice_price * si.qty ) / 2, 2)  AS invoicePrice,
        MAX( oi.currency ) AS currency,
        MAX( ri.receiver_info ) AS receiverInfo,
        MAX( p.fabric_type) AS fabricType
        FROM
        stockout_shipment_item si
        LEFT JOIN stockout_shipment sh ON sh.shipment_id = si.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = si.spec_id
        LEFT JOIN product_info p ON p.product_id = s.product_id
        left join product_category_mapping pcm on p.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category wc ON wc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare c ON c.wms_category_id = pcm.wms_category_id
        LEFT JOIN stockout_order o ON o.stockout_order_no = si.stockout_order_no
        LEFT JOIN stockout_order_item oi ON si.stockout_order_item_id = oi.stockout_order_item_id
        LEFT JOIN stockout_receiver_info ri ON ri.stockout_order_id = oi.stockout_order_id
        <where>
            <if test="orderNoList!=null and orderNoList.size() > 0 ">
                and o.notify_ship_status != 'WAIT_NOTIC_DELIVERY' and sh.`status` IN ( 'PACKING', 'PACKING_END' ) and
                si.order_no in
                <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
        GROUP BY si.order_no, wc.name
    </select>

    <select id="searchDeclareInfoListYFH" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemDeclareInfo">
        SELECT
        si.order_no AS orderNo,
        ANY_VALUE(si.stockout_order_no) AS stockoutOrderNo,
        SUM( si.qty ) AS qty,
        MAX( wc.`name` ) AS customsDeclareChineseName,
        MAX( wc.english_name ) AS customsDeclareEnglishName,
        MAX( c.hs_code ) AS hsCode,
        MAX( sh.box_size ) AS boxSize,
        MAX( sh.shipment_id ) AS shipmentId,
        MAX( sh.weight ) AS weight,
        ROUND(AVG( oi.invoice_price) / 2, 2)  AS invoicePrice,
        ROUND(SUM( oi.invoice_price * si.qty ) / 2, 2) AS totalInvoicePrice,
        MAX( oi.currency ) AS currency,
        MAX( ri.receiver_info ) AS receiverInfo
        FROM
        stockout_shipment_item si
        LEFT JOIN stockout_shipment sh ON sh.shipment_id = si.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = si.spec_id
        LEFT JOIN product_info p ON p.product_id = s.product_id
        left join product_category_mapping pcm on p.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category wc ON wc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare c ON c.wms_category_id = pcm.wms_category_id
        LEFT JOIN stockout_order o ON o.stockout_order_no = si.stockout_order_no
        LEFT JOIN stockout_order_item oi ON si.stockout_order_item_id = oi.stockout_order_item_id
        LEFT JOIN stockout_receiver_info ri ON ri.stockout_order_id = oi.stockout_order_id
        <where>
            <if test="orderNoList!=null and orderNoList.size() > 0 ">
                and o.notify_ship_status != 'WAIT_NOTIC_DELIVERY' and sh.`status` IN ( 'PACKING', 'PACKING_END' ) and
                si.order_no in
                <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
        GROUP BY si.order_no, si.shipment_id, wc.name
        order by sh.shipment_id
    </select>

    <select id="searchDeclareInfoListXZUPS" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemDeclareInfo">
        SELECT
        si.order_no AS orderNo,
        ANY_VALUE(si.stockout_order_no) AS stockoutOrderNo,
        SUM( si.qty ) AS qty,
        MAX( wc.`name` ) AS customsDeclareChineseName,
        MAX( wc.english_name ) AS customsDeclareEnglishName,
        MAX( c.hs_code ) AS hsCode,
        MAX( sh.box_size ) AS boxSize,
        MAX( sh.shipment_id ) AS shipmentId,
        MAX( sh.weight ) AS weight,
        ROUND(SUM( oi.invoice_price) / 2, 2)  AS invoicePrice,
        ROUND(SUM( oi.invoice_price / si.qty ) / 2, 2) AS invoicePriceOne,
        ROUND(SUM( oi.invoice_price * si.qty ) / 2, 2) AS totalInvoicePrice,
        MAX( oi.currency ) AS currency,
        MAX( ri.receiver_info ) AS receiverInfo,
        MAX( p.fabric_type) AS fabricType
        FROM
        stockout_shipment_item si
        LEFT JOIN stockout_shipment sh ON sh.shipment_id = si.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = si.spec_id
        LEFT JOIN product_info p ON p.product_id = s.product_id
        left join product_category_mapping pcm on p.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category wc ON wc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare c ON c.wms_category_id = pcm.wms_category_id
        LEFT JOIN stockout_order o ON o.stockout_order_no = si.stockout_order_no
        LEFT JOIN stockout_order_item oi ON si.stockout_order_item_id = oi.stockout_order_item_id
        LEFT JOIN stockout_receiver_info ri ON ri.stockout_order_id = oi.stockout_order_id
        <where>
            <if test="orderNoList!=null and orderNoList.size() > 0 ">
                and o.notify_ship_status != 'WAIT_NOTIC_DELIVERY' and sh.`status` IN ( 'PACKING', 'PACKING_END' ) and
                si.order_no in
                <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
        GROUP BY si.order_no, wc.name
    </select>

<!--    暂无使用-->
    <select id="searchInvoicePriceListXZ" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemInvoicePrice">
        SELECT
        si.order_no AS orderNo,
        ANY_VALUE(si.stockout_order_no) AS stockoutOrderNo,
        SUM( oi.invoice_price * si.qty ) AS invoicePrice,
        SUM( si.qty ) AS qty,
        MAX( oi.currency ) AS currency,
        MAX( ri.receiver_info ) AS receiverInfo
        FROM
        stockout_shipment_item si
        LEFT JOIN stockout_order_item oi ON si.stockout_order_item_id = oi.stockout_order_item_id
        LEFT JOIN stockout_receiver_info ri ON ri.stockout_order_id = oi.stockout_order_id
        <where>
            <if test="orderNoList!=null and orderNoList.size() > 0 ">
                and si.order_no in
                <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
        GROUP BY si.order_no
    </select>

    <!--扣减库存-->
    <select id="getDeductionStockList" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        SELECT
        si.shipment_id as shipmentId,
        si.stockout_order_no as stockoutOrderNo,
        si.batch_id as batchId,
        si.order_no as orderNo,
        si.spec_id as specId,
        si.sku as sku,
        si.qty as qty,
        si.create_by as createBy,
        si.create_date as createDate,
        si.update_by as updateBy,
        si.update_date as updateDate,
        o.space_id as spaceId
        FROM
        stockout_shipment_item si
        inner join stockout_order o
        on si.stockout_order_no = o.stockout_order_no
        <where>
            <if test="shipmentIdList != null and shipmentIdList.size() >0">
                si.shipment_id in
                <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
    </select>
    <select id="getShowOrder" resultType="java.lang.String">
        SELECT DISTINCT
            order_no
        FROM
            stockout_shipment_item ssi
        WHERE
            ssi.shipment_id = #{shipmentId} and ssi.is_deleted = 0
        ORDER BY
            order_no
        limit 1
    </select>

    <select id="searchFbaItem" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemCustoms">
        select
            i.stockout_order_no,
            s.stockout_type,
            i.order_no,
            o.store_id,
            o.store_name,
            min(d.can_declare) productCanDeclare,
            o.platform_name,
            IFNULL(min(cd.can_declare),0) categoryCanDeclare,
            o.stockout_order_id
        from stockout_shipment_item i
        left join stockout_shipment s on s.shipment_id = i.shipment_id
        left join stockout_order o on o.stockout_order_no = i.stockout_order_no
        left join product_spec_info si on si.spec_id = i.spec_id
        left join product_info p  on p.product_id =  si.product_id
        left join product_customs_declare d on d.product_id = si.product_id
        left join product_category_mapping m on m.category_id = p.category_id and m.declaration_type = 'GRANULAR_DECLARATION'
        left join product_category_customs_declare cd on cd.wms_category_id = m.wms_category_id
        where i.shipment_id in
        <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
            #{shipmentId}
        </foreach>
        and o.workspace = 'FBA_AREA'
        group by stockout_order_no,order_no
    </select>


    <select id="searchListByStockoutOrder" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemDetail">
        SELECT
            ssi.shipment_item_id,
            psi.thumbnail_image_url,
            psi.preview_image_url,
            psi.color,
            psi.size,
            psi.sku,
            psi.barcode,
            ssi.qty,
            ss.shipment_box_code,
            ss.box_index
        FROM
            stockout_shipment_item ssi
        LEFT JOIN stockout_shipment ss ON ssi.shipment_id = ss.shipment_id
        LEFT JOIN product_spec_info psi ON ssi.spec_id = psi.spec_id
        <where>
            ssi.is_deleted = '0'
            AND ssi.stockout_order_no = #{request.stockoutOrderNo}
            <if test="request.sku!=null and request.sku!='' ">
                AND ssi.sku = #{request.sku}
            </if>
            <if test="request.shipmentBoxCode!=null and request.shipmentBoxCode!='' ">
                AND ss.shipment_box_code = #{request.shipmentBoxCode}
            </if>
        </where>
    </select>
    <select id="validSameBoxIndex" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            stockout_shipment_item stockout_shipment_item
        LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        <where>
            stockout_shipment_item.stockout_order_no IN
        <foreach collection="stockoutOrderNo" separator="," index="index" item="orderNo" open="(" close=")">
            #{orderNo}
        </foreach>
          AND stockout_shipment.shipment_id != #{shipmentId} AND stockout_shipment.box_index = #{boxIndex} and stockout_shipment_item.is_deleted = 0
        </where>
    </select>
    <select id="findTopByStockoutOrderNo" resultType="com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity">
        SELECT
            stockout_shipment.*
        FROM
            stockout_shipment_item stockout_shipment_item
        LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        where stockout_shipment_item.stockout_order_no = #{stockoutOrderNo} order by stockout_shipment.delivery_date desc limit 1
    </select>

    <select id="searchDeclareInfoListYH" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemDeclareInfo">
        SELECT
        si.order_no AS orderNo,
        ANY_VALUE(si.stockout_order_no) AS stockoutOrderNo,
        SUM( si.qty ) AS qty,
        MAX( wc.`name` ) AS customsDeclareChineseName,
        MAX( wc.english_name ) AS customsDeclareEnglishName,
        MAX( c.hs_code ) AS hsCode,
        MAX( sh.box_size ) AS boxSize,
        MAX( sh.shipment_id ) AS shipmentId,
        GROUP_CONCAT(DISTINCT sh.shipment_id SEPARATOR ',') AS concatenatedShipmentId,
        MAX( sh.weight ) AS weight,
        temp.price / 2 AS invoicePriceOne,
        c.customs_declare_unit as unit,
        MAX( oi.currency ) AS currency,
        MAX( ri.receiver_info ) AS receiverInfo,
        MAX( p.fabric_type) AS fabricType,
        group_concat(distinct sh.shipment_id) as shipmentIdStr
        FROM
        stockout_shipment_item si
        LEFT JOIN stockout_shipment sh ON sh.shipment_id = si.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = si.spec_id
        LEFT JOIN product_info p ON p.product_id = s.product_id
        left join product_category_mapping pcm on p.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category wc ON wc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare c ON c.wms_category_id = pcm.wms_category_id
        LEFT JOIN (SELECT category_customs_declare_id, any_value(price) AS price FROM product_category_customs_declare_company GROUP BY category_customs_declare_id ) temp
        ON temp.category_customs_declare_id = c.`category_customs_declare_id`
        LEFT JOIN stockout_order o ON o.stockout_order_no = si.stockout_order_no
        LEFT JOIN stockout_order_item oi ON si.stockout_order_item_id = oi.stockout_order_item_id
        LEFT JOIN stockout_receiver_info ri ON ri.stockout_order_id = oi.stockout_order_id
        <where>
            <if test="shipmentIdList!=null and shipmentIdList.size() > 0 ">
                si.shipment_id in
                <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
        GROUP BY si.stockout_order_no, wc.name
    </select>

    <select id="getShippedCountByStockoutOrderNo" resultType="java.lang.Integer">
        select count(1) as count from stockout_shipment_item si
        inner join stockout_shipment s on s.shipment_id = si.shipment_id
        where si.stockout_order_no = #{stockoutOrderNo} and s.status = 'SHIPPED'
    </select>

    <select id="groupByWithSpace" resultType="java.lang.String">
        select distinct CONCAT(s.shipment_box_code,'-',esm.erp_space_id) from stockout_shipment s
        inner join stockout_shipment_item si on si.shipment_id = s.shipment_id
        inner join stockout_order_item oi on oi.stockout_order_item_id = si.stockout_order_item_id
        inner join bd_position p on p.position_code = oi.position_code  and p.is_deleted = 0 and p.location = oi.location
        inner join bd_erp_space_mapping esm on esm.space_id = p.space_id and esm.area_name = p.area_name
        where s.shipment_box_code = #{shipmentBoxCode}  and si.is_deleted = 0
    </select>

    <select id="getShipmentErpSpace" resultType="java.lang.Integer">
        select distinct esm.erp_space_id from stockout_shipment s
        inner join stockout_shipment_item si on si.shipment_id = s.shipment_id
        inner join stockout_order_item oi on oi.stockout_order_item_id = si.stockout_order_item_id
        inner join bd_position p on p.position_code = oi.position_code  and p.is_deleted = 0 and p.location = oi.location
        inner join bd_erp_space_mapping esm on esm.space_id = p.space_id and esm.area_name = p.area_name
        where s.shipment_box_code = #{shipmentBoxCode}  and si.is_deleted = 0
    </select>

    <select id="findByShipmentIdAndErpSpaceId" resultType="com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity">
        select si.* from stockout_shipment_item si
        inner join stockout_order_item oi on oi.stockout_order_item_id = si.stockout_order_item_id
        inner join bd_position p on p.position_code = oi.position_code and p.is_deleted = 0 and p.location = oi.location
        left join bd_erp_space_mapping esm on esm.space_id = p.space_id and esm.area_name = p.area_name
        where  si.shipment_id = #{shipmentId}  and si.is_deleted = 0
        <if test="erpSpaceId !=null and erpSpaceId > 0 ">
            AND esm.erp_space_id = #{erpSpaceId}
        </if>
    </select>
    <select id="findDeliveryDateByOrderNo" resultType="java.util.Date">
        SELECT
            stockout_shipment.delivery_date
        FROM
            stockout_shipment_item stockout_shipment_item
                LEFT JOIN stockout_shipment stockout_shipment ON stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        WHERE
            stockout_shipment_item.order_no = #{orderNo}
        ORDER BY
            stockout_shipment.delivery_date asc
            LIMIT 1
    </select>
    <select id="searchDeclareInfoListJRD"
            resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemDeclareInfo">
        SELECT
        ANY_VALUE(si.order_no) AS orderNo,
        si.stockout_order_no AS stockoutOrderNo,
        SUM( si.qty ) AS qty,
        MAX( wc.`name` ) AS customsDeclareChineseName,
        MAX( wc.english_name ) AS customsDeclareEnglishName,
        MAX( c.hs_code ) AS hsCode,
        MAX( sh.box_size ) AS boxSize,
        MAX( sh.shipment_id ) AS shipmentId,
        GROUP_CONCAT(DISTINCT sh.shipment_id SEPARATOR ',') AS concatenatedShipmentId,
        MAX( sh.weight ) AS weight,
        MAX( oi.invoice_price ) AS invoicePrice,
        ROUND(SUM( oi.invoice_price * si.qty ) / 2, 2)  AS totalInvoicePrice,
        MAX( oi.currency ) AS currency,
        MAX( ri.receiver_info ) AS receiverInfo,
        MAX( p.fabric_type_en) AS fabric_type_en,
        MAX( p.fabric_type) AS fabricType
        FROM
        stockout_shipment_item si
        LEFT JOIN stockout_shipment sh ON sh.shipment_id = si.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = si.spec_id
        LEFT JOIN product_info p ON p.product_id = s.product_id
        left join product_category_mapping pcm on p.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category wc ON wc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare c ON c.wms_category_id = pcm.wms_category_id
        LEFT JOIN stockout_order o ON o.stockout_order_no = si.stockout_order_no
        LEFT JOIN stockout_order_item oi ON si.stockout_order_item_id = oi.stockout_order_item_id
        LEFT JOIN stockout_receiver_info ri ON ri.stockout_order_id = oi.stockout_order_id
        <where>
            <if test="shipmentIds!=null and shipmentIds.size() > 0 ">
                and o.notify_ship_status != 'WAIT_NOTIC_DELIVERY' and sh.`status` IN ( 'PACKING', 'PACKING_END' ) and
                si.shipment_id in
                <foreach collection="shipmentIds" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and si.is_deleted = 0
        </where>
        GROUP BY si.stockout_order_no, wc.name
    </select>
    <select id="getLogisticsNoByOrderNos" resultType="java.lang.String">
        SELECT
            stockout_shipment.logistics_no
        FROM
            stockout_shipment_item stockout_shipment_item
                LEFT JOIN stockout_shipment stockout_shipment ON stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        WHERE
            stockout_shipment_item.order_no in
        <foreach collection="orderNos" separator="," index="index" item="orderNo" open="(" close=")">
            #{orderNo}
        </foreach>
    </select>

    <select id="getPickTypeByShipmentId" resultType="java.lang.String">
        select distinct so.picking_type from stockout_shipment_item ssi
        inner join stockout_order so on so.stockout_order_no = ssi.stockout_order_no
        where ssi.shipment_id = #{shipmentId}
    </select>

    <select id="getSkuByShipmentId" resultType="java.lang.String">
        select distinct ssi.sku from stockout_shipment_item ssi
        where ssi.shipment_id = #{shipmentId}
    </select>
    <select id="getDeclareInfoOfJiuFang" resultType="com.nsy.api.wms.domain.stockout.DeclareItemInfo">
        SELECT
        product_info.spu as spu,
        SUM(stockout_shipment_item.qty) as totalQty,
        GROUP_CONCAT(DISTINCT stockout_shipment_item.shipment_id SEPARATOR ',') AS concatenatedShipmentId,
        MAX(product_wms_category.english_name) as wmsCategoryEnName,
        MAX(product_wms_category.`name`) as wmsCategoryCnName,
        MAX(product_info.fabric_type_en) as fabricType,
        MAX(product_customs_declare.hs_code) as hsCode,
        MAX(bd_hs_code.unit_cn) as declareUnit,
        ANY_VALUE(product_info.image_url) as productImgUrl,
        MAX(product_customs_declare.product_customs_declare_id) as productCustomsDeclareId,
        product_customs_declare.purchase_price
        FROM
        stockout_shipment_item stockout_shipment_item
        LEFT JOIN product_spec_info product_spec_info on product_spec_info.sku = stockout_shipment_item.sku
        LEFT JOIN product_info product_info on product_spec_info.product_id = product_info.product_id
        left join product_category_mapping pcm on product_info.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category product_wms_category ON product_wms_category.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_customs_declare product_customs_declare ON product_customs_declare.spu = product_info.spu
        LEFT JOIN bd_hs_code bd_hs_code on bd_hs_code.hs_code = product_customs_declare.hs_code
        WHERE
            stockout_shipment_item.shipment_id IN
            <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                #{shipmentId}
            </foreach>
        GROUP BY product_info.spu
    </select>

    <select id="getDeclareInfoOfKaiQi" resultType="com.nsy.api.wms.domain.stockout.ShipmentDeclareKaiQiExportItem">
        SELECT
            pcd.customs_declare_cn,
            pcd.customs_declare_en,
            pcd.hs_code,
            SUM( ssi.qty ) AS qty,
            ssi.sku,
            CONCAT_WS('/', pi.fabric_type, pi.fabric_type_en) fabric_type,
            psi.thumbnail_image_url as image_url,
            ss.box_size,
            pi.spin_type,
            psi.actual_weight,
            ss.shipment_id,
            so.store_name as brandName,
            so.area_name,
            soi.stockout_order_id,
            ss.weight roughWeight,
            pi.spu,
            ss.logistics_company
        FROM
            stockout_shipment ss
                LEFT JOIN stockout_shipment_item ssi ON ss.shipment_id = ssi.shipment_id
                AND ssi.is_deleted = 0
                LEFT JOIN product_spec_info psi ON psi.sku = ssi.sku
                LEFT JOIN product_info pi ON pi.product_id = psi.product_id
                AND pi.is_deleted = 0
                LEFT JOIN product_customs_declare pcd ON pcd.spu = pi.spu
                left join stockout_order_item soi ON ssi.stockout_order_item_id = soi.stockout_order_item_id
                left join stockout_order so on so.stockout_order_id = soi.stockout_order_id
        WHERE
            ss.is_deleted = 0 and
            ss.shipment_id IN
            <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                #{shipmentId}
            </foreach>
        GROUP BY
            ss.shipment_id,
            pi.spu
    </select>

    <select id="getDeclareInfoOfLinChuang" resultType="com.nsy.api.wms.domain.stockout.ShipmentDeclareLinChuangExportItem">
        SELECT
        ss.box_index,
        ssi.sku,
        pcd.customs_declare_cn,
        pcd.customs_declare_en,
        ssi.qty,
        pi.fabric_type,
        pcd.hs_code,
        ss.weight box_weight,
        ss.box_size,
        psi.thumbnail_image_url as image_url,
        ss.logistics_company,
        ss.shipment_id,
        pcd.element_value,
        pi.spu
        FROM
        stockout_shipment ss
        LEFT JOIN stockout_shipment_item ssi ON ss.shipment_id = ssi.shipment_id
        AND ssi.is_deleted = 0
        LEFT JOIN product_spec_info psi ON psi.sku = ssi.sku
        LEFT JOIN product_info pi ON pi.product_id = psi.product_id
        AND pi.is_deleted = 0
        LEFT JOIN product_customs_declare pcd ON pcd.spu = pi.spu
        WHERE
        ss.is_deleted = 0 and
        ss.shipment_id IN
        <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
            #{shipmentId}
        </foreach>
    </select>

    <select id="findBoxCodeByStockoutOrderNoAndStatus" resultType="java.lang.String">
        SELECT DISTINCT s.shipment_box_code FROM `stockout_shipment` s
            INNER JOIN stockout_shipment_item si
             on s.shipment_id = si.shipment_id
        where  s.`status`  = #{status}
        and si.stockout_order_no = #{stockoutOrderNo}
          and s.is_deleted = 0
        and si.is_deleted = 0
        limit 1
    </select>
    <select id="searchShipmentItemDetail" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemSku">
        SELECT
        item.sku AS sku,
        sum(item.qty) AS qty,
        oi.barcode,
        oi.seller_sku,
       oi.seller_barcode
        FROM stockout_shipment_item item
        inner JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        inner join stockout_order_item oi on oi.stockout_order_item_id = item.stockout_order_item_id
        where t.shipment_box_code = #{shipmentBoxCode}
        and item.stockout_order_no = #{stockoutOrderNo}
        and t.is_deleted = 0
        and item.is_deleted = 0
        group by item.sku
    </select>

    <select id="sumSpuQtyByShipmentId" resultType="com.nsy.wms.business.domain.bo.stockout.StockoutShipmentSpuQtyBo">
        SELECT
            pi.spu,
            sum( ssi.qty ) qty
        FROM
            stockout_shipment_item ssi
                LEFT JOIN product_spec_info psi ON ssi.spec_id = psi.spec_id
                LEFT JOIN product_info pi ON psi.product_id = pi.product_id
                AND pi.is_deleted = 0
        WHERE
            ssi.shipment_id = #{shipmentId}
          AND ssi.is_deleted = 0
        GROUP BY
            pi.spu
    </select>

    <select id="sumSpuQtyByShipmentIdList" resultType="com.nsy.wms.business.domain.bo.stockout.StockoutShipmentSpuQtyBo">
        SELECT
            ssi.shipment_id,
            pi.spu,
            sum( ssi.qty ) qty
        FROM
            stockout_shipment_item ssi
                LEFT JOIN product_spec_info psi ON ssi.spec_id = psi.spec_id
                LEFT JOIN product_info pi ON psi.product_id = pi.product_id
                AND pi.is_deleted = 0
        WHERE
            ssi.shipment_id IN
            <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                #{shipmentId}
            </foreach>
          AND ssi.is_deleted = 0
        GROUP BY
            ssi.shipment_id,
            pi.spu
    </select>

    <select id="getStocktoutOrderByShipment" resultType="java.lang.String">
        select distinct ti.stockout_order_no from stockout_shipment_item ti
        inner join stockout_shipment t on t.shipment_id = ti.shipment_id
        where
        ti.is_deleted = 0 and
        t.shipment_box_code IN
        <foreach collection="shipmentBoxCodeList" separator="," index="index" item="shipmentBoxCode" open="(" close=")">
            #{shipmentBoxCode}
        </foreach>
    </select>


    <select id="searchShipmentBoxCodeListByOrderNo" resultType="java.lang.String">
        SELECT
        DISTINCT t.shipment_box_code
        FROM stockout_shipment_item item
        LEFT JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        <where>
            and item.order_no in
            <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                #{orderNo}
            </foreach>
            and item.is_deleted = 0
        </where>
    </select>


    <select id="searchShipmentBoxCodeListByStockoutOrderNo" resultType="java.lang.String">
        SELECT
        DISTINCT t.shipment_box_code
        FROM stockout_shipment_item item
        inner JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        <where>
            and item.stockout_order_no in
            <foreach collection="stockoutOrderNoList" separator="," index="index" item="stockoutOrderNo" open="(" close=")">
                #{stockoutOrderNo}
            </foreach>
            and item.is_deleted = 0
        </where>
    </select>

    <select id="searchShipmentIdCodeListByOrderNo" resultType="java.lang.Integer">
        SELECT
        DISTINCT t.shipment_id
        FROM stockout_shipment_item item
        LEFT JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        <where>
            and item.order_no in
            <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                #{orderNo}
            </foreach>
            and item.is_deleted = 0
        </where>
    </select>

    <select id="getShipmentDeclareDownloadItemBoList" resultType="com.nsy.wms.business.domain.bo.stockout.StockoutShipmentDeclareDownloadItemBo">
        SELECT
        pcd.hs_code,
        pcd.customs_declare_cn,
        pcd.customs_declare_en,
        ssi.sku,
        ssi.seller_sku,
        ssi.qty,
        IFNULL(pcd.purchase_price, 0) purchase_price,
        ss.box_index,
        ss.weight,
        ss.box_size,
        pi.fabric_type_en,
        pi.fabric_type,
        pi.spin_type,
        psi.thumbnail_image_url as image_url,
        ss.shipment_id,
        pcd.element_value,
        pi.spu
        FROM
        stockout_shipment ss
        LEFT JOIN stockout_shipment_item ssi ON ss.shipment_id = ssi.shipment_id
        AND ssi.is_deleted = 0
        LEFT JOIN product_spec_info psi ON psi.sku = ssi.sku
        LEFT JOIN product_info pi ON pi.product_id = psi.product_id
        AND pi.is_deleted = 0
        LEFT JOIN product_customs_declare pcd ON pcd.spu = pi.spu
        WHERE
        ss.is_deleted = 0 and
        ss.shipment_id IN
        <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
            #{shipmentId}
        </foreach>
    </select>

    <select id="getShipmentCountByStockoutOrderNo" resultType="java.lang.Integer">
        SELECT
        count(DISTINCT item.shipment_id)
        FROM stockout_shipment_item item
        where item.stockout_order_no = #{stockoutOrderNo}
        and item.is_deleted = 0
    </select>

    <select id="listByWorkspaceAndShipmentId" resultType="com.nsy.wms.business.domain.bo.stockout.StockoutShipmentItemBo">
        SELECT
            ssi.*,
            so.stockout_order_id
        FROM
            nsy_wms.stockout_shipment ss
                INNER JOIN nsy_wms.stockout_shipment_item ssi ON ss.shipment_id = ssi.shipment_id
                INNER JOIN nsy_wms.stockout_order so ON ssi.stockout_order_no = so.stockout_order_no
        WHERE
            so.workspace IN
            <foreach collection="workspaceList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
          AND ss.shipment_id = #{shipmentId}
          AND ss.is_deleted = 0
          AND ssi.is_deleted = 0
    </select>
    <select id="getDepponShipmentDeclareDownloadItemBoList"
            resultType="com.nsy.wms.business.domain.bo.stockout.StockoutShipmentDeclareDownloadItemBo">
        SELECT
        pcd.hs_code,
        pcd.customs_declare_cn,
        pcd.customs_declare_en,
        ssi.sku,
        ssi.seller_sku,
        ssi.qty,
        IFNULL(pcd.purchase_price, 0) as purchase_price,
        IFNULL(ss.box_index, 0) as box_index,
        IFNULL(ss.weight, 0) as weight,
        IFNULL(ss.box_size, '') as box_size,
        pi.fabric_type_en,
        IFNULL(psi.thumbnail_image_url, '') as image_url,
        ss.shipment_id,
        amazon_relation.fba_shipment_id,
        amazon_relation.amazon_reference_id,
        amazon_relation.destination_fulfillment_center_id,
        pcd.element_value,
        pi.spu
        FROM
        stockout_shipment ss
        LEFT JOIN stockout_shipment_item ssi ON ss.shipment_id = ssi.shipment_id
        AND ssi.is_deleted = 0
        LEFT JOIN product_spec_info psi ON psi.sku = ssi.sku
        LEFT JOIN product_info pi ON pi.product_id = psi.product_id
        AND pi.is_deleted = 0
        LEFT JOIN product_customs_declare pcd ON pcd.spu = pi.spu
        LEFT JOIN stockout_shipment_amazon_relation amazon_relation ON amazon_relation.shipment_id = ss.shipment_id
        WHERE
        ss.is_deleted = 0 and
        ss.shipment_id IN
        <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
            #{shipmentId}
        </foreach>
    </select>

    <select id="getOrderInfoByOrderNo" resultType="com.nsy.api.wms.response.stockout.StockoutOrderBoxPositionResponse">
        select
            ssi.order_no as orderNo,
            so.store_name as storeName,
            so.business_type as department,
            ss.replenish_order as replenishOrder,
            count(distinct ss.shipment_box_code) as boxNum,
            so.logistics_company as logisticsCompany
        from stockout_shipment ss
            inner join stockout_shipment_item ssi on ssi.shipment_id = ss.shipment_id
            inner join stockout_order so on so.stockout_order_no = ssi.stockout_order_no
        where ssi.order_no =  #{orderNo} and ss.is_deleted = 0 and ssi.is_deleted = 0
        GROUP BY ssi.order_no
    </select>
    <select id="searchShipmentIdByOrderNoAndShipmentId" resultType="java.lang.Integer">
        SELECT
        DISTINCT t.shipment_id
        FROM stockout_shipment_item item
        LEFT JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        <where>
            and item.order_no in
            <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                #{orderNo}
            </foreach>
            <if test="shipmentIdList!=null and shipmentIdList.size() > 0 ">
                and t.shipment_id in
                <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and item.is_deleted = 0 and t.is_deleted = 0
        </where>
    </select>
    <select id="queryItemPutShipmentId" resultType="java.lang.Integer">
        SELECT
        DISTINCT stockout_shipment_item.shipment_id
        FROM stockout_shipment_item stockout_shipment_item
        where stockout_shipment_item.is_deleted = 0
        <if test="query.orderNo != null and query.orderNo != ''">
            and stockout_shipment_item.order_no = #{query.orderNo}
        </if>
        <if test="query.orderNos != null and query.orderNos.size() > 0">
            and stockout_shipment_item.order_no in
            <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
            and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
        </if>
        <if test="query.stockoutNos != null and query.stockoutNos.size() > 0">
            and stockout_shipment_item.stockout_order_no in
            <foreach collection="query.stockoutNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.stockoutBatchId != null">
            and stockout_shipment_item.batch_id = #{query.stockoutBatchId}
        </if>
        <if test="query.sku != null and query.sku != ''">
            and stockout_shipment_item.sku like concat(#{query.sku}, '%')
        </if>

    </select>
</mapper>
