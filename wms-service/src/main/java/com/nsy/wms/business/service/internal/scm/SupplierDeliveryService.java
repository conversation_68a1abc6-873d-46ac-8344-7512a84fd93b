package com.nsy.wms.business.service.internal.scm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stock.StockPlatformSchedulePageCount;
import com.nsy.api.wms.domain.stock.StockPlatformSchedulePageSearchInfo;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.bd.PageShippingItemListRequest;
import com.nsy.api.wms.request.bd.PlatformScheduleSetArriveDateRequest;
import com.nsy.api.wms.request.stock.StockPlatformSchedulePageSearchRequest;
import com.nsy.api.wms.request.stock.StockinOrderTaskSetNeedQaRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockPlatformSchedulePageResponse;
import com.nsy.api.wms.response.stock.StockPlatformScheduleStatisticsResponse;
import com.nsy.api.wms.response.stock.StockPlatformScheduleStatusProcessResponse;
import com.nsy.api.wms.response.stock.StockShippingItemPageList;
import com.nsy.api.wms.response.stock.StockShippingItemPageListResponse;
import com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleLogService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.domain.bo.stock.StockPlatformScheduleStatisticsBo;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.business.service.stockin.StockinSpotInfoService;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleLogEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinSpotInfoEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 工厂发货相关逻辑
 */
@Service
public class SupplierDeliveryService {
    @Autowired
    StockPlatformScheduleService platformScheduleService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockPlatformScheduleItemService platformScheduleItemService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinSpotInfoService spotInfoService;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockPlatformScheduleLogService platformScheduleLogService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;

    /**
     * 工厂发货列表-scm  统计
     *
     * @param request
     * @return
     */
    public StockPlatformSchedulePageCount countSearchShippingList(StockPlatformSchedulePageSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        return platformScheduleService.getBaseMapper().countSearchShippingList(request, TenantContext.getTenant());
    }

    /**
     * 工厂发货列表统计-scm
     *
     * @param request 查询条件
     * @return 统计结果
     */
    public StockPlatformScheduleStatisticsResponse statisticsShippingList(StockPlatformSchedulePageSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getModelMerchandiserEmpId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getModelMerchandiserEmpId());
            request.setModelMerchandiserEmpName(userInfo.getUserName());
        }

        // 创建响应对象
        StockPlatformScheduleStatisticsResponse response = new StockPlatformScheduleStatisticsResponse();

        // 查询发货总箱数和发货总件数
        StockPlatformScheduleStatisticsBo shippingStats = platformScheduleService.getBaseMapper().statisticsShippingBoxAndQty(request, TenantContext.getTenant());
        if (shippingStats != null) {
            response.setBoxQtyTotal(shippingStats.getBoxQtyTotal());
            response.setShipmentQtyTotal(shippingStats.getShipmentQtyTotal());
        }

        // 查询收货总件数和上架总件数
        StockPlatformScheduleStatisticsBo receiveStats = platformScheduleService.getBaseMapper().statisticsReceiveAndShelvedAndReturnQty(request, TenantContext.getTenant());
        if (receiveStats != null) {
            response.setReceiveQtyTotal(receiveStats.getReceiveQtyTotal());
            response.setShelvedQtyTotal(receiveStats.getShelvedQtyTotal());
            response.setWaitReturnQtyTotal(receiveStats.getWaitReturnQtyTotal());
        }


        return response;
    }

    /**
     * 工厂发货列表-scm  统计箱数和发货数
     *
     * @param request
     * @return
     */
    public StatisticsByPlatformScheduleIdResponse statisticsSearchShippingTaskQty(StockPlatformSchedulePageSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getDeliveryDateStart()) && Objects.nonNull(request.getDeliveryDateEnd())) {
            //按七天一次查询
            List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getDeliveryDateStart(), request.getDeliveryDateEnd(), 7);
            Integer planBoxNum = 0;
            Integer shipmentQty = 0;
            for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
                request.setDeliveryDateStart(dateRangeRequest.getStartDate());
                request.setDeliveryDateEnd(dateRangeRequest.getEndDate());
                StatisticsByPlatformScheduleIdResponse response = platformScheduleService.getBaseMapper().statisticsSearchShippingTaskQty(request, TenantContext.getTenant());

                planBoxNum += response.getPlanBoxNum();
                shipmentQty += response.getShipmentQty();
            }
            StatisticsByPlatformScheduleIdResponse response = new StatisticsByPlatformScheduleIdResponse();
            response.setPlanBoxNum(planBoxNum);
            response.setShipmentQty(shipmentQty);
            return response;
        }
        return platformScheduleService.getBaseMapper().statisticsSearchShippingTaskQty(request, TenantContext.getTenant());
    }

    /**
     * 工厂发货列表-scm   统计收货数、退货数、上架数
     *
     * @param request
     * @return
     */
    public StatisticsByPlatformScheduleIdResponse statisticsSearchShippingOrderQty(StockPlatformSchedulePageSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getDeliveryDateStart()) && Objects.nonNull(request.getDeliveryDateEnd())) {
            //按七天一次查询
            List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getDeliveryDateStart(), request.getDeliveryDateEnd(), 7);
            Integer receiveQty = 0;
            Integer returnQty = 0;
            Integer shelvedQty = 0;

            for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
                request.setDeliveryDateStart(dateRangeRequest.getStartDate());
                request.setDeliveryDateEnd(dateRangeRequest.getEndDate());
                StatisticsByPlatformScheduleIdResponse response = platformScheduleService.getBaseMapper().statisticsSearchShippingOrderQty(request, TenantContext.getTenant());

                receiveQty += response.getReceiveQty();
                returnQty += response.getReturnQty();
                shelvedQty += response.getShelvedQty();
            }
            StatisticsByPlatformScheduleIdResponse response = new StatisticsByPlatformScheduleIdResponse();
            response.setReceiveQty(receiveQty);
            response.setReturnQty(returnQty);
            response.setShelvedQty(shelvedQty);
            return response;
        }
        return platformScheduleService.getBaseMapper().statisticsSearchShippingOrderQty(request, TenantContext.getTenant());
    }


    /**
     * 工厂发货列表-scm
     *
     * @param request
     * @return
     */
    public PageResponse<StockPlatformSchedulePageResponse> pageSearchShippingList(StockPlatformSchedulePageSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getPurchaseNo()) && request.getPurchaseNo().startsWith("V")) {
            request.setPurchaseNo(request.getPurchaseNo().substring(1));
        }
        validRequest(request);
        PageResponse<StockPlatformSchedulePageResponse> pageResponse = new PageResponse<>();
        Page<StockPlatformSchedulePageSearchInfo> page = new Page<>(request.getPageIndex(), request.getPageSize());
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getModelMerchandiserEmpId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getModelMerchandiserEmpId());
            request.setModelMerchandiserEmpName(userInfo.getUserName());
        }
        page.setSearchCount(false);
        List<StockPlatformSchedulePageSearchInfo> pageInfoList = platformScheduleService.getBaseMapper().pageSearchShippingList(page, request, TenantContext.getTenant());
        if (CollectionUtils.isEmpty(pageInfoList)) {
            return PageResponse.empty();
        }
        List<Integer> platformScheduleIdList = pageInfoList.stream().map(StockPlatformSchedulePageSearchInfo::getPlatformScheduleId).distinct().collect(Collectors.toList());
        List<StockPlatformScheduleItemEntity> scheduleItemEntityList = platformScheduleItemService.getBaseMapper().findAllByScheduleIdListIgnoreTenant(platformScheduleIdList);
        List<StatisticsByPlatformScheduleIdResponse> statisticsList = stockinOrderTaskService.statisticsByPlatformScheduleId(platformScheduleIdList);
        List<BdSpaceEntity> allSpaceEntityList = spaceService.getBaseMapper().findAllBySpaceIdListIgnoreTenant(scheduleItemEntityList.stream().map(StockPlatformScheduleItemEntity::getSpaceId).distinct().collect(Collectors.toList()));
        List<StockinSpotInfoEntity> spotInfoEntityList = spotInfoService.getBaseMapper().findAllByPurchaseNoListIgnoreTenant(scheduleItemEntityList.stream().map(StockPlatformScheduleItemEntity::getPurchasePlanNo).distinct().collect(Collectors.toList()));
        Map<String, String> stockinStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PLATFORM_STOCKIN_STATUS.getName());
        Map<String, String> purchaseModelMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PURCHASE_MODEL.getName());
        Map<String, String> platformAppointmentStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PLATFORM_APPOINTMENT_STATUS.getName());
        List<StockPlatformSchedulePageResponse> responsesList = pageInfoList.stream().map(info -> {
            List<StockPlatformScheduleItemEntity> itemEntityList = scheduleItemEntityList.stream().filter(t -> t.getPlatformScheduleId().equals(info.getPlatformScheduleId())).collect(Collectors.toList());
            StockPlatformSchedulePageResponse response = new StockPlatformSchedulePageResponse();
            BeanUtils.copyProperties(info, response);

            // 处理上架、接收、退货信息
            processShelveInfo(response, statisticsList.stream().filter(t -> t.getPlatformScheduleId().equals(info.getPlatformScheduleId())).collect(Collectors.toList()));

            // 处理发货信息
            processShipmentInfo(response, info, spotInfoEntityList, itemEntityList);

            response.setAppointmentStockinDate(String.format("%s-%s", DateUtils.format(info.getScheduleStartDate(), "yyyy-MM-dd HH:mm"), DateUtils.format(info.getScheduleEndDate(), "HH:mm")));
            response.setStockinStatusStr(stockinStatusMap.get(info.getStockinStatus()));
            response.setPurchaseModelStr(purchaseModelMap.get(info.getPurchaseModel()));
            response.setAppointmentStatusStr(platformAppointmentStatusMap.get(info.getAppointmentStatus()));
            List<Integer> spaceIdList = itemEntityList.stream().map(StockPlatformScheduleItemEntity::getSpaceId).distinct().collect(Collectors.toList());
            List<BdSpaceEntity> spaceEntityList = allSpaceEntityList.stream().filter(t -> !CollectionUtils.isEmpty(spaceIdList) && spaceIdList.contains(t.getSpaceId())).collect(Collectors.toList());
            response.setSpaceName(spaceEntityList.stream().map(BdSpaceEntity::getSpaceName).distinct().collect(Collectors.joining(StringConstant.COMMA)));
            response.setAreaName(itemEntityList.stream().map(StockPlatformScheduleItemEntity::getAreaName).distinct().collect(Collectors.joining(StringConstant.COMMA)));
            return response;
        }).collect(Collectors.toList());
        pageResponse.setTotalCount(platformScheduleService.getBaseMapper().countSearchShippingListTotal(request, TenantContext.getTenant()));
        pageResponse.setContent(responsesList);
        return pageResponse;
    }

    private void validRequest(StockPlatformSchedulePageSearchRequest request) {
        if (!StringUtils.hasText(request.getPurchaseNo())
                && !StringUtils.hasText(request.getSupplierDeliveryNo())
                && !StringUtils.hasText(request.getLogisticsNo())
                && CollectionUtils.isEmpty(request.getStockinStatusList())
                && Objects.isNull(request.getDeliveryDateStart())) {
            throw new BusinessServiceException("发货日期不可为空");
        }
    }

    private void processShelveInfo(StockPlatformSchedulePageResponse response, List<StatisticsByPlatformScheduleIdResponse> statistics) {
        response.setShelvedQty(statistics.stream().filter(t -> t.getShelvedQty() != null).mapToInt(StatisticsByPlatformScheduleIdResponse::getShelvedQty).sum());
        response.setReceiveQty(statistics.stream().filter(t -> t.getReceiveQty() != null).mapToInt(StatisticsByPlatformScheduleIdResponse::getReceiveQty).sum());
        response.setReturnQty(statistics.stream().filter(t -> t.getReturnQty() != null).mapToInt(StatisticsByPlatformScheduleIdResponse::getReturnQty).sum());
    }

    private void processShipmentInfo(StockPlatformSchedulePageResponse response, StockPlatformSchedulePageSearchInfo info, List<StockinSpotInfoEntity> spotInfoEntityList, List<StockPlatformScheduleItemEntity> itemEntityList) {
        if (StockinTypeEnum.SPOT.name().equals(info.getPlatformScheduleType())) {
            List<StockinSpotInfoEntity> spotInfoList = spotInfoEntityList.stream().filter(t -> Objects.equals(info.getSupplierDeliveryNo(), t.getSupplierDeliveryNo())).collect(Collectors.toList());
            response.setShipmentQty(spotInfoList.stream().filter(t -> t.getShipmentQty() != null).mapToInt(StockinSpotInfoEntity::getShipmentQty).sum());
            response.setLogisticsNo(spotInfoList.stream().map(StockinSpotInfoEntity::getLogisticsNo).distinct().collect(Collectors.joining(StringConstant.COMMA)));
            response.setLogisticsCompany(spotInfoList.stream().map(StockinSpotInfoEntity::getLogisticsCompany).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        } else {
            response.setShipmentQty(itemEntityList.stream().filter(t -> t.getQty() != null).mapToInt(StockPlatformScheduleItemEntity::getQty).sum());
        }
    }

    /**
     * 工厂发货详情列表-scm
     *
     * @param request
     * @return
     */
    public StockShippingItemPageListResponse pageShippingItemList(PageShippingItemListRequest request) {
        StockShippingItemPageListResponse pageResponse = pageShippingItemListSummary(request);
        Page<StockShippingItemPageList> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockShippingItemPageList> pageInfoList;
        int count = pageResponse.getTaskCount();
        //待审核的月台任务还未生成入库任务，这里分开查询
        if (count > 0)
            pageInfoList = platformScheduleService.getBaseMapper().pageSearchShippingItemList(page, request);
        else
            pageInfoList = platformScheduleService.getBaseMapper().pageSearchShippingItemListNoTask(page, request);
        Map<String, String> stockinOrderStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_ORDER_STATUS.getName());
        Map<String, ProductSpecInfoEntity> specInfoEntityMap = productSpecInfoService.findAllMapBySkuIn(pageInfoList.stream().map(StockShippingItemPageList::getSku).distinct().collect(Collectors.toList()));
        List<StockShippingItemPageList> responseList = pageInfoList.stream().peek(info -> {
            if (count > 0) {
                info.setStatusStr(stockinOrderStatusMap.get(info.getStatus()));
                info.setIsNeedQaStr(info.getIsNeedQa() == 1 ? "是" : "否");
            }
            Optional.ofNullable(specInfoEntityMap.get(info.getSku())).ifPresent(spec -> {
                info.setSkc(spec.getSkc());
                info.setSort(spec.getSort());
            });
        }).sorted(Comparator.comparing(StockShippingItemPageList::getSkc).thenComparing(StockShippingItemPageList::getSort)).collect(Collectors.toList());
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(responseList);
        return pageResponse;
    }

    private StockShippingItemPageListResponse pageShippingItemListSummary(PageShippingItemListRequest request) {
        int count = stockinOrderTaskService.getBaseMapper().countByPlatformScheduleIdIgnoreTenant(request.getPlatformScheduleId());
        List<StockShippingItemPageList> pageInfoList;
        //待审核的月台任务还未生成入库任务，这里分开查询
        if (count > 0)
            pageInfoList = platformScheduleService.getBaseMapper().pageSearchShippingItemList(request);
        else
            pageInfoList = platformScheduleService.getBaseMapper().pageSearchShippingItemListNoTask(request);
        StockShippingItemPageListResponse pageResponse = new StockShippingItemPageListResponse();
        pageResponse.setReturnTotalQty(pageInfoList.stream().mapToInt(StockShippingItemPageList::getReturnQty).sum());
        pageResponse.setReceiveTotalQty(pageInfoList.stream().mapToInt(StockShippingItemPageList::getReceiveQty).sum());
        pageResponse.setShipmentTotalQty(pageInfoList.stream().mapToInt(StockShippingItemPageList::getShipmentQty).sum());
        pageResponse.setShelvedTotalQty(pageInfoList.stream().mapToInt(StockShippingItemPageList::getShelvedQty).sum());
        pageResponse.setTaskCount(count);
        return pageResponse;
    }

    /**
     * 设置到货日期
     *
     * @param request
     * @return
     */
    @Transactional
    public void setArriveDate(PlatformScheduleSetArriveDateRequest request) {
        if (CollectionUtils.isEmpty(request.getPlatformScheduleId())) throw new BusinessServiceException("请选中至少一条数据");
        if (request.getArriveDate() == null) throw new BusinessServiceException("到货日期不能为空");
        platformScheduleService.update(new UpdateWrapper<StockPlatformScheduleEntity>().lambda()
                .in(StockPlatformScheduleEntity::getPlatformScheduleId, request.getPlatformScheduleId())
                .set(StockPlatformScheduleEntity::getPlanArriveDate, request.getArriveDate())
                .set(StockPlatformScheduleEntity::getUpdateBy, loginInfoService.getName()));
    }

    /**
     * 设置需要质检
     *
     * @param request
     */
    @Transactional
    public void setNeedQa(StockinOrderTaskSetNeedQaRequest request) {
        if (CollectionUtils.isEmpty(request.getTaskItemIdList())) throw new BusinessServiceException("请选择明细进行设置");
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.list(new LambdaQueryWrapper<StockinOrderTaskItemEntity>()
                .in(StockinOrderTaskItemEntity::getTaskItemId, request.getTaskItemIdList())
                .eq(StockinOrderTaskItemEntity::getIsNeedQa, 0));
        if (CollectionUtils.isEmpty(taskItemEntityList)) throw new BusinessServiceException("所选项都已需要质检，无需设置");
        stockinOrderTaskItemService.updateBatchById(taskItemEntityList.stream().peek(t -> {
            t.setUpdateBy(loginInfoService.getName());
            t.setIsNeedQa(1);
        }).collect(Collectors.toList()));
    }

    /**
     * 月台入库状态过程时间
     *
     * @param platformScheduleId
     * @return
     */
    public StockPlatformScheduleStatusProcessResponse getPlatformScheduleStatusProcess(Integer platformScheduleId) {
        StockPlatformScheduleStatusProcessResponse response = new StockPlatformScheduleStatusProcessResponse();
        List<StockPlatformScheduleLogEntity> platformScheduleLogEntityList = platformScheduleLogService.getByPlatformScheduleId(platformScheduleId);
        platformScheduleLogEntityList.stream().collect(Collectors.groupingBy(StockPlatformScheduleLogEntity::getPlatformScheduleLogType))
                .forEach((logType, logList) -> {
                    List<StockPlatformScheduleLogEntity> tempLogList = logList.stream().sorted(Comparator.comparing(StockPlatformScheduleLogEntity::getCreateDate)).collect(Collectors.toList());
                    if (StockPlatformScheduleLogTypeEnum.GENERATE.getLogType().equals(logType)) {
                        response.setTransitDate(tempLogList.get(0).getCreateDate());
                    } else if (StockPlatformScheduleLogTypeEnum.GENERATE_STOCKIN_TASK.getLogType().equals(logType)) {
                        response.setWaitInboundDate(tempLogList.get(0).getCreateDate());
                    } else if (StockPlatformScheduleLogTypeEnum.START_RECEIVING.getLogType().equals(logType)) {
                        response.setInboundingDate(tempLogList.get(0).getCreateDate());
                    } else if (StockPlatformScheduleLogTypeEnum.ALL_RECEIVED.getLogType().equals(logType)) {
                        response.setInboundedDate(tempLogList.get(0).getCreateDate());
                    }
                });
        return response;
    }
}
