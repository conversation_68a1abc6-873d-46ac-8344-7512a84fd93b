package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CountryCodeConstant;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.domain.stockout.StockoutOrderSkuDescription;
import com.nsy.api.wms.domain.stockout.StockoutPickingBoxCodePrint;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskPrint;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.ProductSizeSegmentEnum;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.wms.business.manage.erp.ErpStockoutApiService;
import com.nsy.wms.business.manage.erp.response.ErpTradeInfoResponse;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.GenerateCodeService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockLendItemService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockPrintService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.repository.entity.bd.BdTagMappingEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPickingItemRecordMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPickingTaskMapper;
import com.nsy.wms.utils.BarCodeUtils;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import freemarker.template.Template;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StockoutPickingTaskPrintService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutPickingTaskPrintService.class);

    @Autowired
    StockoutPickingTaskMapper stockoutPickingTaskMapper;
    @Autowired
    StockoutPickingTaskService taskService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Resource
    BdTagMappingService tagMappingService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    GenerateCodeService gcService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutPickingLogService pickingLogService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutPickingItemRecordMapper stockoutPickingItemRecordMapper;
    @Autowired
    StockoutPickingTaskConfirmService stockoutPickingTaskConfirmService;
    @Autowired
    StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    StockoutBatchMergeService batchMergeService;
    @Autowired
    StockPrintService stockPrintService;
    @Autowired
    StockoutBatchPrintService batchPrintService;
    @Autowired
    StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    StockoutOrderLackItemService stockoutOrderLackItemService;
    @Autowired
    StockoutOrderPrintService stockoutOrderPrintService;
    @Autowired
    StockoutOrderTemuExtendInfoService stockoutOrderTemuExtendInfoService;
    @Autowired
    StockLendItemService stockLendItemService;
    @Autowired
    BdTagMappingService bdTagMappingService;
    @Autowired
    ErpStockoutApiService erpApiService;
    @Autowired
    StockoutVasTaskService stockoutVasTaskService;


    @Transactional
    public PrintListResponse printPickingBoxCode(IdListRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_PICKING.getTemplateName());
        List<Integer> idList = request.getIdList();
        List<StockoutPickingTaskEntity> pickingTaskEntityList = taskService.listByIds(idList);
        pickingTaskEntityList.stream().filter(taskEntity -> !StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())).findAny()
                .ifPresent(taskEntity -> {
                    throw new BusinessServiceException(String.format("拣货任务【%s】尚未完成拣货，无法打印拣货箱号！", taskEntity.getTaskId()));
                });
        List<Integer> batchIds = pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getBatchId).distinct().collect(Collectors.toList());
        Map<Integer, List<StockoutPickingBoxCodePrint>> collect = stockoutPickingItemRecordMapper.getPrintPickingBoxCodeByTask(batchIds).stream()
                .collect(Collectors.groupingBy(StockoutPickingBoxCodePrint::getBatchId));
        List<String> result = new ArrayList<>(8);
        List<StockoutPickingBoxCodePrint> stockoutPickingBoxCodePrintList = new ArrayList<>(8);
        collect.entrySet().forEach(entry -> {
            int index = 9312; //字符①
            List<StockoutPickingBoxCodePrint> value = entry.getValue().stream().sorted(Comparator.comparing(StockoutPickingBoxCodePrint::getUpdateDate)).collect(Collectors.toList());
            for (StockoutPickingBoxCodePrint print : value) {
                print.setIndex(Character.toString((char) index));
                stockoutPickingBoxCodePrintList.add(print);
                index++;
            }
        });
        stockoutPickingBoxCodePrintList.stream().filter(print -> idList.stream().anyMatch(id -> id.equals(print.getTaskId()))).forEach(print -> result.add(PrintTransferUtils.transfer(templateEntity.getContent(), print)));
        response.setHtmlList(result);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    @Transactional
    public PrintListResponse printPickingTask(IdListRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_PICKING_TASK.getTemplateName());
        List<StockoutPickingTaskEntity> pickingTaskEntityList = taskService.listByIds(request.getIdList());
        List<String> result = new ArrayList<>();
        pickingTaskEntityList.forEach(entity -> {
            buildBatchScanHtml(result, entity, templateEntity);
        });
        response.setHtmlList(result);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    private void buildBatchScanHtml(List<String> result, StockoutPickingTaskEntity entity, PrintTemplateEntity templateEntity) {
        StockoutPickingTaskPrint print = new StockoutPickingTaskPrint();
        StockoutBatchEntity byId = stockoutBatchService.getById(entity.getBatchId());
        print.setBatchId(byId.getBatchId());
        List<Integer> batchWithMerge = batchMergeService.findBatchWithMerge(stockoutBatchService.getStockoutBatchById(byId.getBatchId()));
        List<StockoutBatchOrderEntity> batchOrderEntityList = stockoutBatchOrderService.findAllByBatchIds(CollectionUtils.isEmpty(batchWithMerge) ? Collections.singletonList(byId.getBatchId()) : batchWithMerge);

        List<StockoutBatchOrderItemEntity> batchOrderItemEntityList = stockoutBatchOrderItemService.findAllByBatchOrderIdIn(batchOrderEntityList.stream().map(StockoutBatchOrderEntity::getBatchOrderId).collect(Collectors.toList()));
        int batchQty = batchOrderItemEntityList.stream().mapToInt(StockoutBatchOrderItemEntity::getQty).sum();
        print.setBatchQty(batchQty);
        if (!StringUtils.hasText(entity.getPickingBoxCode())) {
            print.setFirstPrint("首次打印");
            genePickingBoxCode(entity);
        } else {
            print.setFirstPrint("");
        }
        print.setInternalBoxCode(entity.getPickingBoxCode());
        print.setDateTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        print.setLogisticsCompany(byId.getLogisticsCompany());
        List<StockoutPickingTaskItemEntity> allByTaskId = stockoutPickingTaskItemService.findAllByTaskId(entity.getTaskId());
        List<String> spaceAreaName = allByTaskId.stream().map(StockoutPickingTaskItemEntity::getSpaceAreaName).distinct().collect(Collectors.toList());
        print.setSpaceAreaName(StringUtils.join(spaceAreaName, ','));
        print.setQty(entity.getExpectedQty());
        print.setTaskId(entity.getTaskId());
        print.setInternalBoxCode(entity.getPickingBoxCode());
        print.setWorkspace(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), byId.getWorkspace()));
        print.setPickingType(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), byId.getPickingType()));
        print.setScanType(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_SORTING_SCANNING_STATION.getName(), entity.getScanType()));
        print.setTaskType(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PICKING_TASK_TYPE.getName(), entity.getTaskType()));
        //判断是否品牌单
        List<BdTagMappingEntity> tagMappingEntities = bdTagMappingService.getBrandTageByStockoutOrderId(batchOrderEntityList.get(0).getStockoutOrderId());
        print.setBrandName(CollectionUtils.isEmpty(tagMappingEntities) ? "" : tagMappingEntities.get(0).getTagName());
        buildProcess(entity, print, byId);
        // 特殊判断缝制任务
        if (StockoutPickingTaskTypeEnum.SEW_PICKING.name().equals(entity.getTaskType())) {
            print.setTaskProcessType("(缝制)");
        }
        String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), print);
        result.add(transfer);
        if (StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(byId.getWorkspace())) {
            // fba要多打一份
            result.add(transfer);
        }
    }

    private void buildProcess(StockoutPickingTaskEntity entity, StockoutPickingTaskPrint print, StockoutBatchEntity byId) {
        if (StockoutOrderTypeEnum.LIGHT_CUSTOMIZATION_DELIVERY.name().equals(byId.getStockoutType())) {
            print.setProcessType("(加工备货)");
            print.setTaskProcessType("(加工备货)");
        } else if (1 == byId.getIsNeedProcess()) {
            print.setProcessType("(加工)");
            print.setTaskProcessType("(加工)");
        } else {
            print.setProcessType("");
            print.setTaskProcessType(entity.getIsNeedProcess() == 1 ? "(加工)" : "");
        }
    }

    public PrintListResponse printPickingTaskA4(IdListRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_PICKING_TASK_A4.getTemplateName());
        List<StockoutPickingTaskEntity> pickingTaskEntityList = taskService.listByIds(request.getIdList());
        List<String> result = new ArrayList<>();
        for (StockoutPickingTaskEntity entity : pickingTaskEntityList) {
            StockoutBatchEntity byId = stockoutBatchService.getById(entity.getBatchId());
            List<StockoutBatchOrderEntity> allByBatchId = stockoutBatchOrderService.findAllByBatchId(byId.getBatchId());
            if (StockoutPickingTypeEnum.WHOLE_PICK.name().equals(byId.getPickingType()) && !CollectionUtils.isEmpty(allByBatchId) && allByBatchId.size() == 1) {
                Map<String, Object> map = getParamMapWholePick(entity, allByBatchId, byId);
                if (CollectionUtils.isEmpty(map))
                    continue;
                String templateName = PrintTemplateNameEnum.STOCKOUT_PICKING_TASK_A4_BATCH.getTemplateName();
                PrintTemplateEntity templateEntity1 = printService.getByName(templateName);
                String dynamicHtml = PrintTransferUtils.dynamicHtml(templateEntity1, map);
                result.add(dynamicHtml);
                if (StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(byId.getWorkspace()) && !StrUtil.equalsAny(byId.getLocation(), LocationEnum.GUANGZHOU.name(), LocationEnum.MISI.name())) {
                    // fba要多打一份
                    result.add(dynamicHtml);
                }
            } else {
                Map<String, Object> map = getParamMap(entity, byId);
                if (CollectionUtils.isEmpty(map))
                    continue;
                Template template = FreeMarkerTemplateUtils.getTemplate("StockoutPickingTask.ftl");
                String renderTemplate = FreeMarkerTemplateUtils.renderTemplate(template, map);
                result.add(renderTemplate);
                if (StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(byId.getWorkspace()) && !StrUtil.equalsAny(byId.getLocation(), LocationEnum.GUANGZHOU.name(), LocationEnum.MISI.name())) {
                    // fba要多打一份
                    result.add(renderTemplate);
                }
            }
        }
        response.setHtmlList(result);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    @Transactional
    public PrintListResponse printPickingTaskA4ByBatchId(IdListRequest request) {
        // 波次判断 已生成拣货任务才允许打印
        List<StockoutBatchEntity> stockoutBatchEntities = stockoutBatchService.getBaseMapper().listByIdsOrderByIds(request.getIdList());
        if (stockoutBatchEntities.stream().anyMatch(entity ->
                StockoutWaveTaskStatusEnum.NEW.name().equals(entity.getStatus())
                        || StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name().equals(entity.getStatus())))
            throw new BusinessServiceException("请先生成拣货任务！");

        // 按单拣货/以货找单  需要打印出整单A4
        List<StockoutBatchEntity> wholeBatch = stockoutBatchEntities.stream().filter(batch -> batch.getIsMergeBatch().equals(0)
                && (batch.getPickingType().equals(StockoutPickingTypeEnum.WHOLE_PICK.name()) || batch.getPickingType().equals(StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name()))
                || batch.getIsMergeBatch().equals(1) && batch.getPickingType().equals(StockoutPickingTypeEnum.WHOLE_PICK.name()) && Objects.equals(batch.getWorkspace(), StockoutOrderWorkSpaceEnum.FBA_AREA.name())).collect(Collectors.toList());
        List<Integer> otherBatchIds = CollectionUtils.isEmpty(wholeBatch) ? stockoutBatchEntities.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList())
                : stockoutBatchEntities.stream().map(StockoutBatchEntity::getBatchId).filter(batchId -> wholeBatch.stream().noneMatch(item -> item.getBatchId().equals(batchId))).collect(Collectors.toList());
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        List<StockoutBatchEntity> fbaBatch = new ArrayList<>();
        if (!CollectionUtils.isEmpty(wholeBatch)) {
            wholeBatch.forEach(batch -> {
                fbaBatch.add(batch);
                List<StockoutBatchOrderEntity> allByBatchId = stockoutBatchOrderService.findAllByBatchId(batch.getBatchId());
                if (CollectionUtils.isEmpty(allByBatchId))
                    allByBatchId = stockoutBatchOrderService.findAllByBatchIds(batchMergeService.findBatchWithMerge(batch));
                List<StockoutPickingTaskEntity> list = taskService.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().eq(StockoutPickingTaskEntity::getBatchId, batch.getBatchId()));
                List<StockoutPickingTaskEntity> processList = new ArrayList<>();
                if (batch.getIsNeedProcess() == 1) {
                    processList = list.stream().filter(it -> it.getIsNeedProcess() == 1).collect(Collectors.toList());
                    list = list.stream().filter(it -> it.getIsNeedProcess() != 1).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(list))
                    printBatchByTaskInPaper(allByBatchId, batch, list, response);
                if (!CollectionUtils.isEmpty(processList))
                    printBatchByTaskInPaper(allByBatchId, batch, processList, response);
            });
        }
        if (!CollectionUtils.isEmpty(otherBatchIds)) {
            IdListRequest idListRequest = new IdListRequest();
            List<Integer> batchList = getAllBatchIds(otherBatchIds);
            List<StockoutPickingTaskEntity> pickingTaskEntityList = taskService.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().in(StockoutPickingTaskEntity::getBatchId, batchList));
            idListRequest.setIdList(pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList()));
            validTask(otherBatchIds, idListRequest);
            response.getHtmlList().addAll(printPickingTaskA4(idListRequest).getHtmlList());
        }
        // 打印出库单
        fbaBatch.forEach(batch -> printMergeBatchOrder(batch, response.getHtmlList()));
        updateStockoutOrder(stockoutBatchEntities);
        return response;
    }

    private void validTask(List<Integer> otherBatchIds, IdListRequest idListRequest) {
        if (!CollectionUtils.isEmpty(idListRequest.getIdList())) {
            return;
        }
        List<StockoutBatchEntity> batchEntities = stockoutBatchService.listByIds(otherBatchIds);
        if (batchEntities.stream().allMatch(it -> it.getMergeBatchId() != null)) {
            throw new BusinessServiceException(JsonMapper.toJson(otherBatchIds) + "该波次下无拣货任务，请用父波次打印");
        }
        throw new BusinessServiceException("请生成拣货任务后打印");
    }

    private void updateStockoutOrder(List<StockoutBatchEntity> stockoutBatchEntities) {
        LOGGER.info("修改出库单状态");
        List<StockoutBatchEntity> collect = stockoutBatchEntities.stream().map(batchEntity -> {
            StockoutBatchEntity entity = new StockoutBatchEntity();
            entity.setIsPrint(1);
            entity.setUpdateBy(loginInfoService.getName());
            entity.setBatchId(batchEntity.getBatchId());
            entity.setMergeBatchId(batchEntity.getMergeBatchId());
            stockoutBatchLogService.addLog(entity.getBatchId(), StockoutBatchLogTypeEnum.PRINT_PICKING_TASK.getStockoutBatchLogType(), "打印波次下的拣货任务");
            return entity;
        }).collect(Collectors.toList());
        stockoutBatchService.updateBatchById(collect);
    }

    private void printBatchByTaskInPaper(List<StockoutBatchOrderEntity> allByBatchId, StockoutBatchEntity batch, List<StockoutPickingTaskEntity> list, PrintListResponse response) {
        Map<String, Object> map = batchPrintService.printBatchWhole(allByBatchId, batch, list);
        if (CollectionUtils.isEmpty(map))
            return;
        String templateName = PrintTemplateNameEnum.STOCKOUT_PICKING_TASK_A4_BATCH.getTemplateName();
        if (batch.getPickingType().equals(StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name())
                || batch.getIsMergeBatch().equals(1) && batch.getPickingType().equals(StockoutPickingTypeEnum.WHOLE_PICK.name()) && Objects.equals(batch.getWorkspace(), StockoutOrderWorkSpaceEnum.FBA_AREA.name()))
            templateName = PrintTemplateNameEnum.STOCKOUT_PICKING_TASK_A4_FDBG.getTemplateName();
        PrintTemplateEntity templateEntity = printService.getByName(templateName);
        String dynamicHtml = PrintTransferUtils.dynamicHtml(templateEntity, map);
        response.getHtmlList().add(dynamicHtml);
        if (batch.getWorkspace().equals(StockoutOrderWorkSpaceEnum.FBA_AREA.name()) && !StrUtil.equalsAny(batch.getLocation(), LocationEnum.GUANGZHOU.name(), LocationEnum.MISI.name()) && batch.getIsMergeBatch().equals(0))
            response.getHtmlList().add(dynamicHtml);
    }

    private List<Integer> getAllBatchIds(List<Integer> otherBatchIds) {
        List<Integer> result = new ArrayList<>();
        otherBatchIds.forEach(it -> {
            StockoutBatchEntity batch = stockoutBatchService.getStockoutBatchById(it);
            result.add(batch.getBatchId());
            if (batch.getIsMergeBatch() == 1 && batch.getIsNeedProcess() == 1) {
                // 如果是加工合并波次,一并打印子波次的任务
                List<Integer> batchWithMerge = batchMergeService.findBatchWithMerge(batch);
                result.addAll(batchWithMerge);
            }
        });
        return result;
    }

    private void printMergeBatchOrder(StockoutBatchEntity batch, List<String> result) {
        if (batch.getIsMergeBatch() != null && batch.getIsMergeBatch() == 1 && Objects.equals(StockoutOrderWorkSpaceEnum.FBA_AREA.name(), batch.getWorkspace())) {
            List<StockoutBatchOrderEntity> batchOrderList = stockoutBatchOrderService.getBatchOrderList(batch);
            IdListRequest request1 = new IdListRequest();
            request1.setIdList(batchOrderList.stream().map(StockoutBatchOrderEntity::getStockoutOrderId).distinct().collect(Collectors.toList()));
            PrintListResponse response1 = stockoutOrderPrintService.printStockoutOrderA4(request1);
            result.addAll(response1.getHtmlList());
        }
    }

    private Map<String, Object> getParamMap(StockoutPickingTaskEntity entity, StockoutBatchEntity stockoutBatchEntity) {
        Map<String, Object> map = new HashMap<>(32);
        List<StockoutPickingTaskItemInfo> pickingTaskItemEntityList = stockoutPickingTaskItemService.getBaseMapper().findAllByTaskIdOrderBySpace(Lists.newArrayList(entity.getTaskId()));
        if (!CollectionUtils.isEmpty(pickingTaskItemEntityList) && pickingTaskItemEntityList.stream().allMatch(taskItem -> ProcessConstant.AFTER_PROCESS_POSITION_AREA_NAME.equalsIgnoreCase(taskItem.getSpaceAreaName())))
            return map;
        List<String> skuList = pickingTaskItemEntityList.stream().map(StockoutPickingTaskItemInfo::getSku).distinct().collect(Collectors.toList());
        List<ProductSpecInfoEntity> allBySkuIn = productSpecInfoService.findAllBySkuIn(skuList);
        Map<String, List<String>> productTagBySkus = tagMappingService.getProductTagBySkus(skuList);
        map.put("batchId", stockoutBatchEntity.getBatchId().toString());
        List<Integer> batchWithMerge = batchMergeService.findBatchWithMerge(stockoutBatchEntity);
        List<StockoutBatchOrderEntity> batchOrderEntityList = stockoutBatchOrderService.findAllByBatchIds(CollectionUtils.isEmpty(batchWithMerge) ? Collections.singletonList(stockoutBatchEntity.getBatchId()) : batchWithMerge);
        List<StockoutBatchOrderItemEntity> batchOrderItemEntityList = stockoutBatchOrderItemService.findAllByBatchOrderIdIn(batchOrderEntityList.stream().map(StockoutBatchOrderEntity::getBatchOrderId).collect(Collectors.toList()));
        int batchQty = batchOrderItemEntityList.stream().mapToInt(StockoutBatchOrderItemEntity::getQty).sum();
        List<StockoutOrderSkuDescription> orderInfoByBatch = stockoutBatchEntity.getWorkspace().contains("FBA") ? stockoutOrderItemService.getBaseMapper().getOrderInfoByBatch(stockoutBatchEntity.getBatchId()) : Collections.emptyList();
        Map<String, Integer> storeFirstOrderMap = orderInfoByBatch.stream().filter(it -> it.getSku() != null && it.getIsFirstOrderByStore() != null).collect(Collectors.toMap(StockoutOrderSkuDescription::getSku, StockoutOrderSkuDescription::getIsFirstOrderByStore, (v1, v2) -> v1));
        Map<String, Boolean> isLeadGenerationMap = orderInfoByBatch.stream().filter(it -> it.getSku() != null && it.getIsLeadGeneration() != null).collect(Collectors.toMap(StockoutOrderSkuDescription::getSku, StockoutOrderSkuDescription::getIsLeadGeneration, (v1, v2) -> v1));
        Set<String> transparencySet = orderInfoByBatch.stream().filter(it -> it.getSku() != null && it.getIsTransparency() == 1).map(StockoutOrderSkuDescription::getSku).collect(Collectors.toSet());
        Map<String, String> vasTypeMap = stockoutVasTaskService.getSotrePositionVasTaskMapById(batchOrderEntityList.stream().map(StockoutBatchOrderEntity::getStockoutOrderId).distinct().collect(Collectors.toList()));
        List<Integer> productIdList = pickingTaskItemEntityList.stream().map(StockoutPickingTaskItemInfo::getProductId).distinct().collect(Collectors.toList());
        Map<Integer, String> productMap = productInfoService.findByProductIds(productIdList).stream().filter(it -> StrUtil.isNotBlank(it.getPackageVacuum())).collect(Collectors.toMap(ProductInfoEntity::getProductId, ProductInfoEntity::getPackageVacuum, (v1, v2) -> v1));
        map.put("batchQty", batchQty);
        map.put("totalOrderNum", stockoutOrderItemService.getBaseMapper().getOrderNumByBatch(stockoutBatchEntity.getBatchId()));
        if (!StringUtils.hasText(entity.getPickingBoxCode())) {
            map.put("firstPrint", "首次打印");
            genePickingBoxCode(entity);
        } else {
            map.put("firstPrint", "");
        }
        //判断是否品牌单
        List<BdTagMappingEntity> tagMappingEntities = bdTagMappingService.getBrandTageByStockoutOrderId(batchOrderEntityList.get(0).getStockoutOrderId());
        map.put("brandName", CollectionUtils.isEmpty(tagMappingEntities) ? "" : tagMappingEntities.get(0).getTagName());
        buildProcessInfo(stockoutBatchEntity, map, entity);
        map.put("internalBoxCode", entity.getPickingBoxCode());
        map.put("internalBoxCodeImg", BarCodeUtils.getBarCodeBase64(entity.getPickingBoxCode()));
        map.put("workspace", enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(),
                stockoutBatchEntity.getWorkspace()) + (StockoutPickingTaskTypeEnum.OUT_STOCK_PICKING.name().equals(entity.getTaskType()) ? "(缺货拣货)" : ""));
        map.put("pickingType", enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), stockoutBatchEntity.getPickingType()));
        map.put("scanType", enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_SORTING_SCANNING_STATION.getName(), entity.getScanType()));
        map.put("taskId", entity.getTaskId().toString());
        map.put("spaceArea", pickingTaskItemEntityList.stream().map(StockoutPickingTaskItemInfo::getSpaceAreaName).distinct().collect(Collectors.joining("、")));
        map.put("nowDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        map.put("logisticsCompany", stockoutBatchEntity.getLogisticsCompany());
        map.put("qty", pickingTaskItemEntityList.stream().mapToInt(StockoutPickingTaskItemInfo::getExpectedQty).sum());
        map.put("weight", getWeight(pickingTaskItemEntityList, allBySkuIn));
        map.put("taskIdImg", BarCodeUtils.getBarCodeBase64(entity.getTaskId().toString()));
        map.put("batchIdImg", BarCodeUtils.getBarCodeBase64(stockoutBatchEntity.getBatchId().toString()));
        StockoutBuilding.getItemList(map, pickingTaskItemEntityList, storeFirstOrderMap, productTagBySkus, transparencySet, Collections.emptyMap(), productMap, isLeadGenerationMap, vasTypeMap);
        return map;
    }

    private void genePickingBoxCode(StockoutPickingTaskEntity entity) {
        String boxAndUpdatePrint = stockPrintService.getPickBoxAndUpdatePrint(entity);
        entity.setPickingBoxCode(boxAndUpdatePrint);
        entity.setUpdateBy(loginInfoService.getName());
        taskService.updateById(entity);
    }

    @NotNull
    private Map<String, Object> getParamMapWholePick(StockoutPickingTaskEntity entity, List<StockoutBatchOrderEntity> allByBatchId, StockoutBatchEntity byId) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(allByBatchId.get(0).getStockoutOrderId());
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        List<String> skuList = orderItemEntityList.stream().map(StockoutOrderItemEntity::getSku).distinct().collect(Collectors.toList());
        List<ProductSpecInfoEntity> allBySkuIn = productSpecInfoService.findAllBySkuIn(skuList);
        Map<String, List<String>> productTagBySkus = tagMappingService.getProductTagBySkus(skuList);
        List<StockoutOrderSkuDescription> orderInfoByBatch = stockoutOrderItemService.getBaseMapper().getOrderInfoByBatch(byId.getBatchId());
        Map<String, Integer> storeFirstOrderMap = orderInfoByBatch.stream().filter(it -> it.getSku() != null && it.getIsFirstOrderByStore() != null).collect(Collectors.toMap(StockoutOrderSkuDescription::getSku, StockoutOrderSkuDescription::getIsFirstOrderByStore, (v1, v2) -> v1));
        Map<String, Boolean> isLeadGenerationMap = orderInfoByBatch.stream().filter(it -> it.getSku() != null && it.getIsLeadGeneration() != null).collect(Collectors.toMap(StockoutOrderSkuDescription::getSku, StockoutOrderSkuDescription::getIsLeadGeneration, (v1, v2) -> v1));
        Set<String> transparencySet = orderInfoByBatch.stream().filter(it -> it.getSku() != null && it.getIsTransparency() == 1).map(StockoutOrderSkuDescription::getSku).collect(Collectors.toSet());
        Map<String, String> vasTypeMap = stockoutVasTaskService.getSotrePositionVasTaskMapByNo(Collections.singletonList(stockoutOrderEntity.getStockoutOrderNo()));
        List<StockoutPickingTaskItemInfo> stockoutPickingTaskItemEntityList = stockoutPickingTaskItemService.getBaseMapper().findAllByTaskIdOrderBySpace(Lists.newArrayList(entity.getTaskId()));
        List<Integer> productIdList = orderItemEntityList.stream().map(StockoutOrderItemEntity::getProductId).distinct().collect(Collectors.toList());
        Map<Integer, String> productMap = productInfoService.findByProductIds(productIdList).stream().filter(it -> StrUtil.isNotBlank(it.getPackageVacuum())).collect(Collectors.toMap(ProductInfoEntity::getProductId, ProductInfoEntity::getPackageVacuum, (v1, v2) -> v1));
        Map<String, Object> map = new HashMap<>(32);
        if (!CollectionUtils.isEmpty(stockoutPickingTaskItemEntityList) && stockoutPickingTaskItemEntityList.stream().allMatch(taskItem -> ProcessConstant.AFTER_PROCESS_POSITION_AREA_NAME.equalsIgnoreCase(taskItem.getSpaceAreaName())))
            return map;
        //判断是否品牌单
        List<BdTagMappingEntity> tagMappingEntities = bdTagMappingService.getBrandTageByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        map.put("brandName", CollectionUtils.isEmpty(tagMappingEntities) ? "" : "出库单标签:" + tagMappingEntities.stream().map(BdTagMappingEntity::getTagName).distinct().collect(Collectors.joining(",")));
        LocalDate nowDate = LocalDate.now();
        map.put("date", String.format("%s月%s日", nowDate.getMonthValue(), nowDate.getDayOfMonth()));
        map.put("workspace", enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(),
                stockoutOrderEntity.getWorkspace()) + (StockoutPickingTaskTypeEnum.OUT_STOCK_PICKING.name().equals(entity.getTaskType()) ? "(缺货拣货)" : ""));
        map.put("pickingType", enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), stockoutOrderEntity.getPickingType()));
        map.put("taskId", "拣货任务ID：" + entity.getTaskId());
        if (!StringUtils.hasText(entity.getPickingBoxCode())) {
            map.put("firstPrint", "首次打印");
            genePickingBoxCode(entity);
        } else
            map.put("firstPrint", "");
        buildProcessInfo(byId, map, entity);
        map.put("internalBoxCode", entity.getPickingBoxCode());
        map.put("internalBoxCodeImg", BarCodeUtils.getBarCodeBase64(entity.getPickingBoxCode()));
        map.put("nowDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        map.put("orderNo", StringUtils.join(orderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList()), ','));
        buildReceiver(map, stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId()));
        map.put("qty", orderItemEntityList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum());
        BigDecimal weight = StockoutBuilding.getPayWeight(orderItemEntityList, allBySkuIn);
        map.put("payWeight", weight);
        map.put("weight", weight);
        buildSpaceMemo(map, orderItemEntityList);
        map.put("taskIdImg", BarCodeUtils.getBarCodeBase64(entity.getTaskId().toString()));
        buildByStockoutOrder(stockoutOrderEntity, map);
        StockoutBuilding.getItemList(map, stockoutPickingTaskItemEntityList, storeFirstOrderMap, productTagBySkus, transparencySet, Collections.emptyMap(), productMap, isLeadGenerationMap, vasTypeMap);
        map.put("orderNoImgList", StockoutBuilding.getOrderNoImgList(orderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList())));
        map.put("isPdd", stockoutOrderTemuExtendInfoService.validTemuOrder(stockoutOrderEntity.getStockoutOrderNo()));
        map.put("platformName", StringUtils.hasText(stockoutOrderEntity.getPlatformName()) ? enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKOUT_ORDER_PLATFORM_NAME.getName(), stockoutOrderEntity.getPlatformName()) : "");
        map.put("titleInfo", batchPrintService.buildTitleInfo(orderItemEntityList, Collections.singletonList(stockoutOrderEntity)));
        map.put("urgent", stockoutOrderEntity.getUrgent());
        map.put("productSizeSegment", ProductSizeSegmentEnum.getCn(stockoutOrderEntity.getProductSizeSegment()));
        map.put("fbaReplenish", stockoutOrderEntity.getFbaReplenishType());
        map.put("replenishOrder", stockoutOrderEntity.getReplenishOrder());
        return map;
    }

    public void buildSpaceMemo(Map<String, Object> map, List<StockoutOrderItemEntity> orderItemEntityList) {
        String spaceMemo = orderItemEntityList.get(0).getSpaceMemo();
        if (StrUtil.contains(spaceMemo, "请到erp系统查看详细备注")) {
            List<ErpTradeInfoResponse> tradeInfo = erpApiService.getExchangeTradeInfo(Collections.singletonList(orderItemEntityList.get(0).getOrderNo()));
            if (CollectionUtils.isEmpty(tradeInfo)) {
                map.put("remark", spaceMemo);
            }
            ErpTradeInfoResponse erpTradeInfoResponse = tradeInfo.stream().filter(it -> StrUtil.equals(it.getPlatformTrade().getTid(), orderItemEntityList.get(0).getOrderNo())).findFirst().orElse(new ErpTradeInfoResponse());
            String erpOrderMemo = erpTradeInfoResponse.getPlatformTrade().getOrderMemo();
            if (StrUtil.isNotBlank(erpOrderMemo)) {
                map.put("remark", erpOrderMemo);
            } else {
                map.put("remark", spaceMemo);
            }
        } else {
            map.put("remark", spaceMemo);
        }
    }

    private void buildByStockoutOrder(StockoutOrderEntity stockoutOrderEntity, Map<String, Object> map) {
        map.put("stockoutOrderNoImg", BarCodeUtils.getBarCodeBase64(stockoutOrderEntity.getStockoutOrderNo()));
        map.put("stockoutOrderNo", stockoutOrderEntity.getStockoutOrderNo());
        map.put("logisticsCompany", stockoutOrderEntity.getLogisticsCompany());
        map.put("storeName", stockoutOrderEntity.getStoreName());
        map.put("pack", stockoutOrderEntity.getHasPack() ? "(Pack商品)" : "");
    }

    private void buildProcessInfo(StockoutBatchEntity byId, Map<String, Object> map, StockoutPickingTaskEntity entity) {
        if (StockoutOrderTypeEnum.LIGHT_CUSTOMIZATION_DELIVERY.name().equals(byId.getStockoutType())) {
            map.put("processType", "(加工备货)");
            map.put("taskProcessType", "(加工备货)");
        } else if (byId.getIsNeedProcess() == 1) {
            map.put("processType", "(加工)");
            map.put("taskProcessType", "(加工)");
        } else {
            map.put("processType", "");
            map.put("taskProcessType", entity.getIsNeedProcess() == 1 ? "(加工)" : "");
        }
    }

    public void buildReceiver(Map<String, Object> map, StockoutReceiverInfo receiverInfo) {
        map.put("receiverName", receiverInfo.getReceiverName());
        if (StringUtils.hasText(receiverInfo.getReceiverCountry()))
            map.put("receiverCountry", CountryCodeConstant.getCountryCodeMap().get(receiverInfo.getReceiverCountry()));
        map.put("receiverMobile", receiverInfo.getReceiverMobile());
        map.put("receiverPhone", receiverInfo.getReceiverPhone());
        map.put("receiverAddress", receiverInfo.getReceiverAddress());
        map.put("receiverZip", receiverInfo.getReceiverZip());
    }


    private BigDecimal getWeight(List<StockoutPickingTaskItemInfo> taskItemEntityList, List<ProductSpecInfoEntity> allBySkuIn) {
        BigDecimal result = BigDecimal.ZERO;
        Map<String, List<ProductSpecInfoEntity>> collect = allBySkuIn.stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku));
        for (StockoutPickingTaskItemInfo item : taskItemEntityList) {
            ProductSpecInfoEntity specInfoEntity = collect.get(item.getSku()).get(0);
            result = result.add(ProductSpecInfoService.getSkuWeightDefault(specInfoEntity).multiply(new BigDecimal(item.getExpectedQty())));
        }
        return result.divide(new BigDecimal(1000));
    }
}
