<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.bd.BdTagMappingMapper">
    <select id="findEnableList" resultType="com.nsy.wms.repository.entity.bd.BdTagMappingEntity">
        SELECT
        btm.*
        FROM
        bd_tag_mapping btm
        left JOIN bd_tag bt ON bt.tag_id = btm.tag_id
        WHERE
        bt.is_enabled = TRUE
        <if test="request.referenceType != null and request.referenceType !=''">
            and btm.reference_type = #{request.referenceType}
        </if>
        <if test="request.referenceNo != null and request.referenceNo !=''">
            and btm.reference_no = #{request.referenceNo}
        </if>
        <if test="request.referenceNoList != null and request.referenceNoList.size() > 0">
            and btm.reference_no in
            <foreach collection="request.referenceNoList" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.tagName != null and request.tagName !=''">
            and btm.tag_name = #{request.tagName}
        </if>
    </select>
    <select id="getBrandTageByStockoutOrderId"
            resultType="com.nsy.wms.repository.entity.bd.BdTagMappingEntity">
        select m.* from stockout_order o
        INNER JOIN bd_tag_mapping m on m.reference_no = o.stockout_order_no
        where o.stockout_order_id = #{stockoutOrderId}
        and m.tag_name in
        <foreach collection="brandList" separator="," index="index" item="brand" open="("
                 close=")">
            #{brand}
        </foreach>
    </select>
    <select id="countBrandTagByBatchIds" resultType="java.lang.Integer">
        select count(DISTINCT ifnull(m.tag_name,'非品牌')) from stockout_batch_order bo
            INNER JOIN stockout_order o
            on o.stockout_order_id = bo.stockout_order_id
            left join bd_tag_mapping m on m.reference_no = o.stockout_order_no
            and m.reference_type = 'ORDER'
            and m.tag_name in
            <foreach collection="brandList" separator="," index="index" item="brand" open="("
                     close=")">
                #{brand}
            </foreach>
        where bo.batch_id in
        <foreach collection="batchIdList" separator="," index="index" item="batchId" open="("
                 close=")">
            #{batchId}
        </foreach>
    </select>
</mapper>
