package com.nsy.wms.business.service.stockin;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.bd.BdPositionInfo;
import com.nsy.api.wms.domain.product.ProductSameItem;
import com.nsy.api.wms.domain.stockin.RecommendParamsInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.ShelveRuleItemTypeEnum;
import com.nsy.api.wms.request.stockin.StockinOrderItemListRequest;
import com.nsy.api.wms.response.projection.BdPositionListProjection;
import com.nsy.api.wms.response.stockin.ScanUpShelvesBarcodeResponse;
import com.nsy.api.wms.response.stockin.StockinOrderBoxListResponse;
import com.nsy.wms.business.service.bd.BdShelveRuleService;
import com.nsy.wms.business.service.bd.BdSpaceAreaService;
import com.nsy.wms.business.service.bd.BdStockFifoService;
import com.nsy.wms.business.service.bd.BdStorePositionMappingService;
import com.nsy.wms.business.service.bd.BdTagService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdShelveRuleEntity;
import com.nsy.wms.repository.entity.bd.BdShelveRuleItemEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceAreaEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdShelveRuleItemMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.WmsStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinShelveSearchPositionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinShelveSearchPositionService.class);

    private static final List<String> POSITION_TYPE_LIST = Lists.newArrayList(BdPositionTypeEnum.STOCK_POSITION.name(), BdPositionTypeEnum.ACTIVITY_POSITION.name());

    @Inject
    BdShelveRuleItemMapper shelveRuleItemMapper;
    @Inject
    BdPositionMapper positionMapper;
    @Inject
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockService stockService;
    @Autowired
    private BdSpaceAreaService bdSpaceAreaService;
    @Autowired
    private BdStorePositionMappingService positionMappingService;
    @Autowired
    StockInternalBoxItemService boxItemService;
    @Autowired
    BdStockFifoService bdStockFifoService;
    @Autowired
    BdTagService bdTagService;
    @Autowired
    BdShelveRuleService bdShelveRuleService;

    private static final Integer NEARBY_POSITION_NUM = 10;

    /**
     * 1.根据spaceId查询上架规则
     * 2.通过stockinType匹配上架规则，匹配不到，则使用入库类型为空的上架规则
     * 3.通过area_id匹配上架规则明细，未找到抛出异常
     * 4.遍历上架规则明细，匹配库位
     * (1).当查询类型为库区，且明细存在目标库区，直接返回对应库区
     * (2).根据明细类型匹配库位
     *
     * @param paramsInfo 请求参数
     * @stockinType：入库类型
     * @spaceId：仓库ID
     * @specId： 商品规格ID
     * @areaId：区域ID
     * @recommendType：查询类型 库区/库位
     * @storeId：店铺ID
     * @businessType：部门
     * @isFbaQuick 是否快进快出 默认否
     */
    public List<BdPositionInfo> recommendSpaceAreaAndPosition(RecommendParamsInfo paramsInfo) {
        List<BdPositionInfo> recommendPositionList = new LinkedList<>();
        //1.根据spaceId查询上架规则
        List<BdShelveRuleEntity> list = bdShelveRuleService.list(new LambdaQueryWrapper<BdShelveRuleEntity>()
                .eq(BdShelveRuleEntity::getSpaceId, paramsInfo.getSpaceId())
                .eq(BdShelveRuleEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED));
        if (CollectionUtils.isEmpty(list)) {
            LOGGER.error("匹配推荐库区/库位时找不到上架规则 stockinType={}, spaceId={} ", paramsInfo.getStockinType(), paramsInfo.getSpaceId());
            throw new BusinessServiceException("匹配推荐库区/库位时找不到上架规则");
        }
        //2.通过stockinType匹配上架规则，匹配不到，则使用入库类型为空的上架规则
        BdShelveRuleEntity ruleEntity = list.stream().filter(rule -> paramsInfo.getStockinType().equals(rule.getStockinType())).findAny()
                .orElse(null);
        if (Objects.isNull(ruleEntity))
            ruleEntity = list.stream().filter(rule -> !com.nsy.api.core.apicore.util.StringUtils.hasText(rule.getStockinType())
                    || "ALL".equals(rule.getStockinType())).findAny().orElse(null);
        if (Objects.isNull(ruleEntity)) {
            LOGGER.error("匹配推荐库区/库位时找不到上架规则 stockinType={}, spaceId={} ", paramsInfo.getStockinType(), paramsInfo.getSpaceId());
            throw new BusinessServiceException("匹配推荐库区/库位时找不到上架规则");
        }
        LOGGER.info("匹配推荐库区/库位-----上架规则id: {}, 商品规格id: {}, 区域id: {} 开始匹配推荐库区/库位", ruleEntity.getShelveRuleId(), paramsInfo.getSpecId(), paramsInfo.getAreaId());
        //3.通过area_id匹配上架规则明细，未找到抛出异常
        LambdaQueryWrapper<BdShelveRuleItemEntity> wrapperBdShelveRuleItem = StockinShelveSearchPositionService.buildWrapperShelveRuleIdAndIsDeletedOrderBySortAsc(
                ruleEntity.getShelveRuleId(), paramsInfo.getAreaId());
        wrapperBdShelveRuleItem.orderByAsc(BdShelveRuleItemEntity::getSort);
        List<BdShelveRuleItemEntity> shelveRuleItemList = shelveRuleItemMapper.selectList(wrapperBdShelveRuleItem);
        if (CollectionUtils.isEmpty(shelveRuleItemList)) {
            LOGGER.error("匹配推荐库区/库位-----上架规则id: {} 没有找到规则明细", ruleEntity.getShelveRuleId());
            //若未找到对应规则明细，默认用相同商品查找库位
            BdShelveRuleItemEntity bdShelveRuleItemEntity = new BdShelveRuleItemEntity();
            bdShelveRuleItemEntity.setShelveRuleItemType(ShelveRuleItemTypeEnum.SAME_PRODUCT_SEARCH_POSITION.name());
            shelveRuleItemList.add(bdShelveRuleItemEntity);
        }
        //4.遍历上架规则明细，匹配库位
        for (BdShelveRuleItemEntity shelveRuleItemEntity : shelveRuleItemList) {
            //(1).当查询类型为库区，且明细存在目标库区，直接返回对应库区
            if (StockConstant.RECOMMEND_SPACE_AREA.equals(paramsInfo.getRecommendType().name()) && Objects.nonNull(shelveRuleItemEntity.getSpaceAreaId())) {
                BdSpaceAreaEntity bdSpaceAreaEntity = bdSpaceAreaService.getById(shelveRuleItemEntity.getSpaceAreaId());
                BdPositionInfo bdPositionInfo = new BdPositionInfo();
                BeanUtilsEx.copyProperties(bdSpaceAreaEntity, bdPositionInfo);
                recommendPositionList.add(bdPositionInfo);
                break;
            }
            //(2).根据明细类型匹配库位
            recommendPositionList = matchPosition(paramsInfo, shelveRuleItemEntity);
            if (!CollectionUtils.isEmpty(recommendPositionList))
                break;
        }
        // 打印匹配库位结果日志
        if (!CollectionUtils.isEmpty(recommendPositionList) && StringUtils.hasText(recommendPositionList.get(0).getPositionCode())) {
            LOGGER.info("匹配推荐库位---成功-- 商品规格id: {}, 推荐库位: {} ", paramsInfo.getSpecId(), recommendPositionList.get(0).getPositionCode());
        } else if (!CollectionUtils.isEmpty(recommendPositionList) && StringUtils.hasText(recommendPositionList.get(0).getSpaceAreaName())) {
            LOGGER.error("匹配推荐库区---成功--,, 推荐库区: {} ", recommendPositionList.get(0).getSpaceAreaName());
        } else {
            LOGGER.error("匹配推荐库区/库位---失败--, 商品规格id: {} ", paramsInfo.getSpecId());
        }
        return recommendPositionList;
    }

    /**
     * 匹配库位
     * SAME_PRODUCT_SEARCH_POSITION("相同商品查找库位"), ------目前唯一在用
     * SCOPE_SEARCH_STOCK_POSITION("目标库位范围内查找存储空库位"),
     * SAME_PRODUCT_NEARBY_SEARCH_STOCK_POSITION("相同商品就近查找存储空库位"),
     * RANDOM_SEARCH_STOCK_POSITION("随机查找存储空库位"),
     * SAME_PRODUCT_NEARBY_SEARCH_SPARE_POSITION("相同商品查找零拣库位"),
     * SCOPE_SEARCH_STORE_POSITION("目标库存内店铺查询存储库位");
     */
    public List<BdPositionInfo> matchPosition(RecommendParamsInfo paramsInfo, BdShelveRuleItemEntity shelveRuleItemEntity) {
        String shelveRuleItemType = shelveRuleItemEntity.getShelveRuleItemType();
        List<BdPositionInfo> positionEntityList = new LinkedList<>();
        if (!StringUtils.hasText(shelveRuleItemType)) {
            return positionEntityList;
        }
        LOGGER.info("开始匹配上架规则明细-> {} 参数： {}", ShelveRuleItemTypeEnum.getTypeByName(shelveRuleItemType), JsonMapper.toJson(paramsInfo));
        ShelveRuleItemTypeEnum shelveRuleItemTypeEnum = ShelveRuleItemTypeEnum.valueOf(shelveRuleItemType);
        switch (shelveRuleItemTypeEnum) {
            //相同商品查找库位
            case SAME_PRODUCT_SEARCH_POSITION:
                positionEntityList = sameProductSearchPosition(paramsInfo, shelveRuleItemEntity.getSpaceAreaId());
                break;
            //目标库位范围内查找存储空库位
            case SCOPE_SEARCH_STOCK_POSITION:
                positionEntityList = scopeSearchStockPosition(paramsInfo.getSpaceId(), paramsInfo.getAreaId(), shelveRuleItemEntity.getPositionStart(), shelveRuleItemEntity.getPositionEnd(), BdPositionTypeEnum.STOCK_POSITION.name());
                break;
            //相同商品就近查找存储空库位
            case SAME_PRODUCT_NEARBY_SEARCH_STOCK_POSITION:
                positionEntityList = sameProductNearbySearchStockPosition(paramsInfo.getSpaceId(), paramsInfo.getAreaId(), shelveRuleItemEntity.getSpaceAreaId(), paramsInfo.getSpecId(), BdPositionTypeEnum.STOCK_POSITION.name());
                break;
            //随机查找存储空库位
            case RANDOM_SEARCH_STOCK_POSITION:
                positionEntityList = randomSearchStockPosition(paramsInfo.getSpaceId(), paramsInfo.getAreaId(), shelveRuleItemEntity.getSpaceAreaId(), BdPositionTypeEnum.STOCK_POSITION.name());
                break;
            //相同商品查找零拣库位
            case SAME_PRODUCT_NEARBY_SEARCH_SPARE_POSITION:
                positionEntityList = sameProductNearBySearchSparePosition(paramsInfo.getSpaceId(), paramsInfo.getAreaId(), shelveRuleItemEntity.getSpaceAreaId(), paramsInfo.getSpecId());
                break;
            //目标库存内店铺查询存储库位
            case SCOPE_SEARCH_STORE_POSITION:
                positionEntityList = positionMappingService.matchStorePosition(paramsInfo);
                break;
            default:
                break;
        }
        return positionEntityList;
    }


    /**
     * 目标库位范围内查找存储空库位
     */
    public List<BdPositionInfo> scopeSearchStockPosition(Integer spaceId, Integer areaId, String positionStart, String positionEnd, String positionType) {
        List<BdPositionInfo> recommendPositionList = new ArrayList<>(16);
        String positionTypeStr = BdPositionTypeEnum.STOCK_POSITION.name();
        if (positionType != null) {
            positionTypeStr = positionType;
        }
        List<BdPositionListProjection> positionListProjectionList = positionMapper.findBdPositionListBetweenPositionStartAndPositionEnd(spaceId, areaId, positionStart, positionEnd, positionTypeStr);
        if (!CollectionUtils.isEmpty(positionListProjectionList)) {
            for (BdPositionListProjection projection : positionListProjectionList) {
                BdPositionInfo positionEntity = new BdPositionInfo();
                BeanUtilsEx.copyProperties(projection, positionEntity);
                recommendPositionList.add(positionEntity);
            }
        }
        return recommendPositionList;
    }

    /**
     * 相同商品就近查找存储空库位
     */
    public List<BdPositionInfo> sameProductNearbySearchStockPosition(Integer spaceId, Integer areaId, Integer spaceAreaId, Integer specId, String positionType) {
        List<BdPositionInfo> recommendPositionList = new ArrayList<>(16);
        List<BdPositionEntity> positionEntityList;
        // 查相同商品库存/库位
        List<StockEntity> stockEntityList = stockService.getBySpaceIdAndAreaIdAndSpaceAreaIdAndSpecId(spaceId, areaId, spaceAreaId, specId);
        if (CollectionUtils.isEmpty(stockEntityList)) {
            LOGGER.error("目标仓库相同商品就近查找存储空库位-------不存在目标仓库spaceId:{}, 商品specId:{} 的库存记录", spaceId, specId);
            return recommendPositionList;
        }
        List<StockEntity> filterStockList = stockEntityList.stream().filter(m -> m.getStock() != null && m.getStock() > 0 && m.getPositionId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterStockList)) {
            LOGGER.error("目标仓库相同商品就近查找存储空库位-------目标仓库spaceId:{}, 商品specId:{} 的只有库存为空的记录", spaceId, specId);
            return recommendPositionList;
        }
        String positionTypeStr = BdPositionTypeEnum.STOCK_POSITION.name();
        if (positionType != null) {
            positionTypeStr = positionType;
        }
        LambdaQueryWrapper<BdPositionEntity> wrapperSpaceAreaIdAndPositionType = wrapperSpaceIdAndAreaIdAndSpaceAreaIdAndPositionType(spaceId, areaId, spaceAreaId, positionTypeStr);
        positionEntityList = positionMapper.selectList(wrapperSpaceAreaIdAndPositionType);
        if (CollectionUtils.isEmpty(positionEntityList)) {
            return recommendPositionList;
        }
        for (StockEntity stockEntity : filterStockList) {
            LambdaQueryWrapper<BdPositionEntity> wrapperSpaceAreaIdAndPositionId = buildPositionWrapperSpaceIdAndSpaceAreaIdAndPositionId(spaceId, spaceAreaId, stockEntity.getPositionId());
            BdPositionEntity positionEntity = positionMapper.selectOne(wrapperSpaceAreaIdAndPositionId);
            List<BdPositionEntity> filterBdPositionEntityList;
            filterBdPositionEntityList = getNearByPosition(positionEntity.getSort(), positionEntityList);
            // 查找空库位
            if (!CollectionUtils.isEmpty(filterBdPositionEntityList)) {
                List<Integer> positionIdList = filterBdPositionEntityList.stream().map(BdPositionEntity::getPositionId).distinct().collect(Collectors.toList());
                List<BdPositionListProjection> bdPositionList = positionMapper.findSameProductNearByBdPositionList(spaceId, spaceAreaId, positionIdList, BdPositionTypeEnum.STOCK_POSITION.name());
                if (!CollectionUtils.isEmpty(bdPositionList)) {
                    List<BdPositionListProjection> positionListProjectionList = sortPositionList(positionEntity.getSort(), bdPositionList);
                    for (BdPositionListProjection projection : positionListProjectionList) {
                        BdPositionInfo recommendPosition = new BdPositionInfo();
                        BeanUtilsEx.copyProperties(projection, recommendPosition);
                        recommendPositionList.add(recommendPosition);
                    }
                }
            }
        }
        return recommendPositionList;
    }

    // 根据距离sort由小到大排序
    private List<BdPositionListProjection> sortPositionList(Integer sort, List<BdPositionListProjection> bdPositionList) {
        List<BdPositionListProjection> list = new ArrayList<>();
        List<BdPositionListProjection> filterGrantSortList = bdPositionList.stream().filter(m -> m.getSort().compareTo(sort) > 0).collect(Collectors.toList());
        List<BdPositionListProjection> filterLessSortList = bdPositionList.stream().filter(m -> m.getSort().compareTo(sort) <= 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterGrantSortList)) {
            filterGrantSortList.forEach(item -> {
                item.setSort(item.getSort() - sort);
                list.add(item);
            });
        }
        if (!CollectionUtils.isEmpty(filterLessSortList)) {
            filterLessSortList.forEach(item -> {
                item.setSort(sort - item.getSort());
                list.add(item);
            });
        }
        return list.stream().sorted(Comparator.comparing(BdPositionListProjection::getSort)).collect(Collectors.toList());
    }

    /**
     * 随机查找存储空库位
     */
    public List<BdPositionInfo> randomSearchStockPosition(Integer spaceId, Integer areaId, Integer spaceAreaId, String positionType) {
        List<BdPositionInfo> recommendPositionList = new ArrayList<>(16);
        String positionTypeStr = BdPositionTypeEnum.STOCK_POSITION.name();
        if (positionType != null) {
            positionTypeStr = positionType;
        }
        List<BdPositionListProjection> positionListProjectionList = positionMapper.findRandomBdPositionList(spaceId, areaId, spaceAreaId, positionTypeStr);
        if (!CollectionUtils.isEmpty(positionListProjectionList)) {
            for (BdPositionListProjection projection : positionListProjectionList) {
                BdPositionInfo positionEntity = new BdPositionInfo();
                BeanUtilsEx.copyProperties(projection, positionEntity);
                recommendPositionList.add(positionEntity);
            }
        }
        return recommendPositionList;
    }

    /**
     * 相同商品查找库位
     * 1.快进快出 && 店铺ID不为空 ，优先匹配店铺库位（透明计划融合标）
     * 先进先出查找相应季度的库位
     * skc查找时，size 为字母和数字要分开推荐（即sku的size为数字时，只推荐相同size为数字的库位）
     * 2.查找相同sku有库存库位
     * 3.查找相同skc有库存库位
     * 4.查找相同sku零库存库位
     * 5.查找相同skc零库存库位
     */
    public List<BdPositionInfo> sameProductSearchPosition(RecommendParamsInfo paramsInfo, Integer spaceAreaId) {
        List<BdPositionInfo> recommendPositionList = new LinkedList<>();
        Integer specId = paramsInfo.getSpecId();
        ProductSameItem productSameItem = new ProductSameItem();
        BeanUtilsEx.copyProperties(paramsInfo, productSameItem);
        productSameItem.setSpaceAreaId(spaceAreaId);
        productSameItem.setPositionTypeList(POSITION_TYPE_LIST);

        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySpecId(specId);
        if (productSpecInfoEntity == null) {
            LOGGER.error("cannot find specId:{} productSpecInfoEntity", specId);
            return recommendPositionList;
        }

        List<BdPositionListProjection> positionListProjectionList = sameProductSearchPosition(productSameItem, productSpecInfoEntity, paramsInfo.getSpaceId());
        for (BdPositionListProjection bdPositionListProjection : positionListProjectionList) {
            BdPositionInfo recommendPosition = new BdPositionInfo();
            BeanUtilsEx.copyProperties(bdPositionListProjection, recommendPosition);
            recommendPositionList.add(recommendPosition);
        }
        return recommendPositionList;
    }

    public List<BdPositionListProjection> sameProductSearchPosition(ProductSameItem productSameItem, ProductSpecInfoEntity productSpecInfoEntity, Integer spaceId) {
        // 1.快进快出 && 店铺ID不为空 ，优先匹配越库库位和店铺库位
        boolean matchStorePosition = productSameItem.getIsFbaQuick().equals(1) && Objects.nonNull(productSameItem.getStoreId());
        if (matchStorePosition) {
            productSameItem.setPositionTypeList(Lists.newArrayList(BdPositionTypeEnum.STORE_POSITION.name(), BdPositionTypeEnum.CROSS_POSITION.name()));
        } else {
            productSameItem.setStoreId(null);
        }

        //是否先进先出
        Boolean isFifo = bdTagService.checkStockFifo(productSpecInfoEntity.getSku());
        List<BdPositionListProjection> positionListProjectionList = sameProductSearchPosition(productSameItem, productSpecInfoEntity, isFifo);
        if (isFifo && CollectionUtils.isEmpty(positionListProjectionList))  //先进先出查不到的兜底
            positionListProjectionList = sameProductSearchPosition(productSameItem, productSpecInfoEntity, Boolean.FALSE);
        return positionListProjectionList;
    }

    private List<BdPositionListProjection> sameProductSearchPosition(ProductSameItem productSameItem, ProductSpecInfoEntity productSpecInfoEntity, Boolean isFifo) {
        String skc = productSpecInfoEntity.getSkc();
        Date current = new Date();
        String quarter = String.format("%s0%s", DateUtil.year(current), DateUtil.quarter(current));
        // 2.查找相同sku有库存库位
        List<BdPositionListProjection> positionListProjectionList = positionMapper.findSameProductBdPositionList(productSameItem);
        // 商品的size是否为数字
        Integer isNotNum = WmsStringUtils.isNumeric(productSpecInfoEntity.getSize()) ? 0 : 1;

        if (isFifo) {
            positionListProjectionList = positionListProjectionList.stream().filter(bdPositionInfo -> quarter.equals(bdPositionInfo.getQuarter())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(positionListProjectionList)) {
            // 3.查找相同skc有库存库位
            productSameItem.setSpecId(null);
            productSameItem.setSkc(skc);
            positionListProjectionList = positionMapper.findSameProductBdPositionList(productSameItem).stream().filter(item -> isNotNum.equals(item.getIsNotNum())).collect(Collectors.toList());
            if (isFifo) {
                positionListProjectionList = positionListProjectionList.stream().filter(bdPositionInfo -> quarter.equals(bdPositionInfo.getQuarter())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(positionListProjectionList)) {
            // 4.查找相同sku零库存库位
            productSameItem.setSpecId(productSpecInfoEntity.getSpecId());
            productSameItem.setProductId(null);
            productSameItem.setSkc(null);
            positionListProjectionList = positionMapper.findSameProductBdPositionListZero(productSameItem);
            if (isFifo) {
                positionListProjectionList = positionListProjectionList.stream().filter(bdPositionInfo -> quarter.equals(bdPositionInfo.getQuarter())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(positionListProjectionList)) {
            // 5.查找相同skc零库存库位
            productSameItem.setSpecId(null);
            productSameItem.setSkc(skc);
            positionListProjectionList = positionMapper.findSameProductBdPositionListZero(productSameItem).stream().filter(item -> isNotNum.equals(item.getIsNotNum())).collect(Collectors.toList());
            if (isFifo) {
                positionListProjectionList = positionListProjectionList.stream().filter(bdPositionInfo -> quarter.equals(bdPositionInfo.getQuarter())).collect(Collectors.toList());
            }
        }

        return positionListProjectionList;
    }

    /**
     * 相同商品查找零拣库位
     */
    public List<BdPositionInfo> sameProductNearBySearchSparePosition(Integer spaceId, Integer areaId, Integer spaceAreaId, Integer specId) {
        List<BdPositionInfo> recommendPositionList = new ArrayList<>(16);
        ProductSameItem productSameItem = new ProductSameItem();
        productSameItem.setSpaceId(spaceId);
        productSameItem.setSpaceAreaId(spaceAreaId);
        productSameItem.setSpecId(specId);
        productSameItem.setAreaId(areaId);
        productSameItem.setPositionTypeList(Lists.newArrayList(BdPositionTypeEnum.SPARE_POSITION.name()));
        List<BdPositionListProjection> positionListProjectionList = positionMapper.findSameProductBdPositionList(productSameItem);
        if (!CollectionUtils.isEmpty(positionListProjectionList)) {
            for (BdPositionListProjection projection : positionListProjectionList) {
                BdPositionInfo positionEntity = new BdPositionInfo();
                BeanUtilsEx.copyProperties(projection, positionEntity);
                recommendPositionList.add(positionEntity);
            }
        }
        return recommendPositionList;
    }

    /**
     * 获取邻近库位(目标库位向上加 NEARBY_POSITION_NUM个,向下加 NEARBY_POSITION_NUM)
     */
    public List<BdPositionEntity> getNearByPosition(Integer positionSort, List<BdPositionEntity> positionEntityList) {
        List<BdPositionEntity> lessTargetPositionList = positionEntityList.stream().filter(item -> item.getSort() < positionSort).collect(Collectors.toList());
        List<BdPositionEntity> greaterTargetPositionList = positionEntityList.stream().filter(item -> item.getSort() > positionSort).collect(Collectors.toList());
        List<BdPositionEntity> positionFilterList = new ArrayList<>();
        // 比目标库位code向下查找NEARBY_POSITION_NUM个,按positionSort由小到大返回
        int lessSize = lessTargetPositionList.size();
        if (lessSize > 0 && lessSize <= NEARBY_POSITION_NUM) { // 若查找到总数少于 NEARBY_POSITION_NUM个
            int size = lessSize - 1;
            for (int i = 0; i <= size; i++) {
                if (lessTargetPositionList.get(size - i) != null) {
                    positionFilterList.add(lessTargetPositionList.get(size - i));
                }
            }
        } else if (lessSize > NEARBY_POSITION_NUM) {  // 若查找到总数多个于 NEARBY_POSITION_NUM个
            Comparator<BdPositionEntity> comparator = (h1, h2) -> h1.getSort().compareTo(h2.getSort());
            lessTargetPositionList.sort(comparator.reversed());
            int size = NEARBY_POSITION_NUM - 1;
            for (int i = 0; i <= size; i++) {
                if (lessTargetPositionList.get(size - i) != null) {
                    positionFilterList.add(lessTargetPositionList.get(size - i));
                }
            }
        }
        // 比目标库位code向上查找 NEARBY_POSITION_NUM个,按positionSort由大到小返回
        int greaterSize = greaterTargetPositionList.size();
        if (greaterSize > 0 && greaterSize <= NEARBY_POSITION_NUM) { // 若查找到总数少于 NEARBY_POSITION_NUM个
            int size = greaterSize - 1;
            for (int i = 0; i <= size; i++) {
                if (greaterTargetPositionList.get(i) != null) {
                    positionFilterList.add(greaterTargetPositionList.get(i));
                }
            }
        } else if (greaterSize > NEARBY_POSITION_NUM) { // 若查找到总数多于 NEARBY_POSITION_NUM个
            int size = NEARBY_POSITION_NUM - 1;
            for (int i = 0; i <= size; i++) {
                if (greaterTargetPositionList.get(i) != null) {
                    positionFilterList.add(greaterTargetPositionList.get(i));
                }
            }
        }
        return positionFilterList;
    }

    public static LambdaQueryWrapper<BdShelveRuleItemEntity> buildWrapperShelveRuleIdAndIsDeletedOrderBySortAsc(Integer shelveRuleId, Integer areaId) {
        LambdaQueryWrapper<BdShelveRuleItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(shelveRuleId)) {
            queryWrapper.eq(BdShelveRuleItemEntity::getShelveRuleId, shelveRuleId);
        }
        if (Objects.nonNull(areaId)) {
            queryWrapper.eq(BdShelveRuleItemEntity::getAreaId, areaId);
        }
        if (Objects.isNull(areaId)) {
            queryWrapper.isNull(BdShelveRuleItemEntity::getAreaId);
        }
        queryWrapper.eq(BdShelveRuleItemEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        queryWrapper.orderByAsc(BdShelveRuleItemEntity::getSort);
        return queryWrapper;
    }

    private static LambdaQueryWrapper<BdPositionEntity> wrapperSpaceIdAndAreaIdAndSpaceAreaIdAndPositionType(Integer spaceId, Integer areaId, Integer spaceAreaId, String positionType) {
        LambdaQueryWrapper<BdPositionEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(spaceId)) {
            queryWrapper.eq(BdPositionEntity::getSpaceId, spaceId);
        }
        if (Objects.nonNull(areaId)) {
            queryWrapper.eq(BdPositionEntity::getAreaId, areaId);
        }
        if (Objects.isNull(areaId)) {
            queryWrapper.isNull(BdPositionEntity::getAreaId);
        }
        if (Objects.nonNull(spaceAreaId)) {
            queryWrapper.eq(BdPositionEntity::getSpaceAreaId, spaceAreaId);
        }
        if (StringUtils.hasText(positionType)) {
            queryWrapper.eq(BdPositionEntity::getPositionType, positionType);
        }
        queryWrapper.eq(BdPositionEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        queryWrapper.orderByAsc(BdPositionEntity::getSort);
        return queryWrapper;
    }

    private static LambdaQueryWrapper<BdPositionEntity> buildPositionWrapperSpaceIdAndSpaceAreaIdAndPositionId(Integer spaceId, Integer spaceAreaId, Integer positionId) {
        LambdaQueryWrapper<BdPositionEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(spaceAreaId)) {
            queryWrapper.eq(BdPositionEntity::getSpaceAreaId, spaceAreaId);
        }
        if (Objects.nonNull(spaceId)) {
            queryWrapper.eq(BdPositionEntity::getSpaceId, spaceId);
        }
        if (Objects.nonNull(positionId)) {
            queryWrapper.eq(BdPositionEntity::getPositionId, positionId);
        }
        queryWrapper.eq(BdPositionEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return queryWrapper;
    }

    /**
     * 获取内部箱库存数
     *
     * @param specInfoEntity sku
     * @param data           上架数据
     */
    public void buildInternalBoxStock(ProductSpecInfoEntity specInfoEntity, ScanUpShelvesBarcodeResponse data) {
        List<ScanUpShelvesBarcodeResponse.SkuPositionView> skuPositionList = new LinkedList<>();
        StockinOrderItemListRequest request = new StockinOrderItemListRequest();
        request.setSku(specInfoEntity.getSku());
        List<StockinOrderBoxListResponse> responseList = boxItemService.getBaseMapper().stockinOrderDetailList(new Page<>(1, 20), request).getRecords();
        for (StockinOrderBoxListResponse response : responseList) {
            ScanUpShelvesBarcodeResponse.SkuPositionView skuPositionView = new ScanUpShelvesBarcodeResponse.SkuPositionView();
            skuPositionView.setPosition(response.getInternalBoxCode());
            skuPositionView.setSku(specInfoEntity.getSku());
            skuPositionView.setStock(response.getQty());
            skuPositionView.setIsSameSkc(0);
            skuPositionView.setPreMatchQty(0);
            skuPositionList.add(skuPositionView);
        }
        if (!skuPositionList.isEmpty()) {
            if (data.getSkuPositionViewList() == null || data.getSkuPositionViewList().isEmpty()) {
                data.setSkuPositionViewList(skuPositionList);
            } else {
                data.getSkuPositionViewList().addAll(skuPositionList);
            }
        }
    }
}
