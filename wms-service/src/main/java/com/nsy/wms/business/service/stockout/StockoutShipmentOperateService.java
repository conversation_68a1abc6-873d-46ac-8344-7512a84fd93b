package com.nsy.wms.business.service.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StockoutShipmentLogisticsRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.async.AsyncProcessFlowService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 装箱清单相关操作类
 * HXD
 * 2023/5/8
 **/
@Service
public class StockoutShipmentOperateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentOperateService.class);


    @Resource
    LoginInfoService loginInfoService;
    @Resource
    AsyncProcessFlowService asyncProcessFlowService;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    private StockoutShipmentConfirmService shipmentConfirmService;
    @Autowired
    private StockoutShipmentItemService itemService;


    // 异步-自提发货
    @Transactional
    @JLock(keyConstant = "asyncSelfGetLogistics", lockKey = "#request.shipmentBoxCodeList[0]")
    public AsyncProcessFlowResult asyncSelfGetLogistics(StockoutShipmentLogisticsRequest request) {
        LocationWrapperMessage<StockoutShipmentLogisticsRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request);
        return asyncProcessFlowService.createFlow(KafkaConstant.STOCKOUT_SHIPMENT_SELF_SHIPPED, KafkaConstant.STOCKOUT_SHIPMENT_SELF_SHIPPED_MARK, message, request.getShipmentBoxCodeList().get(0));
    }

    /**
     * 勾选装箱清单，直接发货
     * <AUTHOR>
     * 2023-10-26
     */
    @Transactional
    public void quickShip(IdListRequest request) {
        LOGGER.info("装箱清单-快速发货：{}", JsonMapper.toJson(request.getIdList()));
        List<StockoutShipmentEntity> stockoutShipmentEntities = shipmentService.listByIds(request.getIdList());
        if (stockoutShipmentEntities.stream().anyMatch(item -> !StringUtils.hasText(item.getLogisticsCompany()) || !StringUtils.hasText(item.getLogisticsNo()))) {
            throw new BusinessServiceException("物流公司和物流单号均要填写！");
        }
        stockoutShipmentEntities.forEach(item -> {
            StockoutShipmentConfirmUpdateRequest confirmUpdateRequest = new StockoutShipmentConfirmUpdateRequest();
            confirmUpdateRequest.setShipmentIds(Collections.singletonList(item.getShipmentId()));
            confirmUpdateRequest.setLogisticsNo(item.getLogisticsNo());
            confirmUpdateRequest.setLogisticsCompany(item.getLogisticsCompany());
            List<StockoutShipmentItemEntity> byShipmentId = itemService.findByShipmentId(item.getShipmentId());
            confirmUpdateRequest.setStockoutOrderNos(byShipmentId.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList()));
            shipmentConfirmService.updateShipmentConfirm(confirmUpdateRequest);
        });
    }

    public BigDecimal getShipmentSkuWeight(String shipmentBoxCode) {
        BigDecimal weight = shipmentService.getBaseMapper().countSkuWeightByShipmentBoxCode(shipmentBoxCode);
        if (weight == null) {
            return BigDecimal.ZERO;
        }
        return weight;
    }
}
