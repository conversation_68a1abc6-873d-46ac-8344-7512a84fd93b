package com.nsy.wms.business.service.supplier;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.business.base.utils.SelectModel;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.mq.TableListenerMessageType;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.repository.jpa.mapper.supplier.SupplierMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SupplierService extends ServiceImpl<SupplierMapper, SupplierEntity> {
    @Autowired
    ScmApiService scmApiService;

    @CacheEvict(value = CacheKeyConstant.WMS_SUPPLIER_DEPARTMENT_LIST, allEntries = true)
    public void syncSupplier(List<Integer> supplierIdList, TableListenerMessageType type) {
        if (TableListenerMessageType.INSERT == type
                || TableListenerMessageType.UPDATE == type) {
            List<SupplierEntity> entityList = supplierIdList.stream().map(supplierId -> {
                SupplierDto newDto = scmApiService.getSupplierDtoBySupplierId(supplierId);
                return BeanUtil.toBean(newDto, SupplierEntity.class);
            }).collect(Collectors.toList());
            if (entityList.isEmpty()) return;
            this.saveOrUpdateBatch(entityList);
        } else {
            if (supplierIdList.isEmpty()) return;
            removeByIds(supplierIdList);
        }
    }

    //是否现货供应商
    public static boolean isStockSupplier(SupplierDto supplierDto) {
        if (StringConstant.STOCK_SUPPLIER.equals(supplierDto.getSupplierType())
                || StringConstant.VIRTUAL_SUPPLIER.equals(supplierDto.getSupplierType()))
            return true;

        return false;

    }

    //是否现货供应商
    public boolean isStockSupplierById(Integer supplierId) {
        List<SupplierDto> supplierInfoList = scmApiService.getSupplierInfoList(Collections.singletonList(supplierId));
        List<Integer> stockSupplierIdList = supplierInfoList.stream().filter(t -> SupplierService.isStockSupplier(t)).map(SupplierDto::getSupplierId).distinct().collect(Collectors.toList());
        return stockSupplierIdList.contains(supplierId);
    }

    public SupplierEntity getBySupplierName(String supplierName) {
        if (StrUtil.isBlank(supplierName)) {
            return null;
        }
        LambdaQueryWrapper<SupplierEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SupplierEntity::getSupplierName, supplierName);
        wrapper.last(MybatisQueryConstant.QUERY_FIRST);
        return getOne(wrapper);
    }

    public SupplierEntity getBySupplierId(Integer supplierId) {
        SupplierEntity one = getOne(new LambdaQueryWrapper<SupplierEntity>()
                .eq(SupplierEntity::getSupplierId, supplierId)
                .eq(SupplierEntity::getIsDelete, 0)
                .last("limit 1"));
        if (Objects.isNull(one))
            throw new BusinessServiceException(String.format("供应商不存在 id : %s", supplierId));
        return one;
    }

    @Cacheable(CacheKeyConstant.WMS_SUPPLIER_DEPARTMENT_LIST)
    public List<SelectModel> getSupplierDepartmentList() {
        return this.getBaseMapper().getSupplierDepartmentInfo();
    }
}
