package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.CurrencyTypeEnum;
import com.nsy.api.wms.enumeration.stockout.CustomsDeclareExchangeRateEnum;
import com.nsy.api.wms.enumeration.stockout.ExportDrawbackInformationTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.request.base.PageRequest;
import com.nsy.wms.business.domain.bo.stockout.StockoutAEOAsynMsgBo;
import com.nsy.wms.business.manage.external.response.DecHead;
import com.nsy.wms.business.manage.external.response.DecList;
import com.nsy.wms.business.manage.external.response.DecMessage;
import com.nsy.wms.business.manage.external.response.DeclareFormBaseResponse;
import com.nsy.wms.business.service.QConsumerRecordService;
import com.nsy.wms.business.service.bd.BdCompanyService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductCategoryCustomsDeclareService;
import com.nsy.wms.mq.QConsumerRecordStatusEnum;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.repository.entity.QConsumerRecordEntity;
import com.nsy.wms.repository.entity.bd.BdCompanyEntity;
import com.nsy.wms.repository.entity.product.ProductCategoryCustomsDeclareEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * HXD
 * 2022/12/16
 **/
@Service
public class StockoutCustomsDeclareFormMQService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareFormMQService.class);

    @Resource
    private ApplicationContext context;
    @Resource
    QConsumerRecordService consumerRecordService;
    @Resource
    StockoutCustomsDeclareFormService declareFormService;
    @Resource
    StockoutCustomsDeclareFormLogService declareFormLogService;
    @Resource
    BdCompanyService bdCompanyService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutCustomsDeclareDocumentService declareDocumentService;
    @Resource
    ProductCategoryCustomsDeclareService categoryCustomsDeclareService;
    @Resource
    StockoutCustomsDeclareDocumentFileService declareDocumentFileService;
    @Resource
    StockoutCustomsDeclareExchangeRateService exchangeRateService;
    @Resource
    StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;
    @Resource
    StockoutCustomsDeclareAEOService stockoutCustomsDeclareAEOService;
    @Resource
    StockoutCustomsDeclareExchangeRateService stockoutCustomsDeclareExchangeRateService;


    /**
     * 1、消息体  目前直接json内部转
     * 2、处理消息日志(先记录，后续统一整理)
     * 3、先不做错误处理：Channel channel, Message mqMessage
     *
     * <AUTHOR>
     * 2022-12-20
     */
    @RabbitListener(containerFactory = "vh0ListenerContainerFactory", queues = "${spring.rabbitmq.listener.declare-queue}", autoStartup = "${spring.rabbitmq.listener.declare-queue-enable}")
    public void onFormMessageSY(String message) {
        acceptFormMessage(message, "SY");
    }


//    @RabbitListener(containerFactory = "vh1ListenerContainerFactory", queues = "91350503MA33311F3U_EntryFile_Send", autoStartup = "${spring.rabbitmq.listener.declare-queue-enable}")
//    public void onFormMessageLxfs(String message) {
//        acceptFormMessage(message, "LXFS");
//    }
//
//    @RabbitListener(containerFactory = "vh2ListenerContainerFactory", queues = "91350503611608429U_EntryFile_Send", autoStartup = "${spring.rabbitmq.listener.declare-queue-enable}")
//    public void onFormMessageSyfs(String message) {
//        acceptFormMessage(message, "SYFS");
//    }
//
//    @RabbitListener(containerFactory = "vh3ListenerContainerFactory", queues = "91350503MA35D98R4D_EntryFile_Send", autoStartup = "${spring.rabbitmq.listener.declare-queue-enable}")
//    public void onFormMessageXjsm(String message) {
//        acceptFormMessage(message, "XJSM");
//    }


    public void acceptFormMessage(String message, String title) {
        LOGGER.info("----------抬头:{},收到关单消息，开始消费-----------", title);
        LOGGER.info("消息体：{}", message);
        QConsumerRecordEntity qConsumerRecordEntity = null;
        try {
            TenantContext.setTenant(LocationEnum.QUANZHOU.name());
            LoginInfoService.setName("关单消息");
            QMessage<String> receiveMessage = new QMessage<>();
            //特殊说明，messageContent字段在mysql类型为TEXT(65536), 由于报文中可能包含中文等占据多字节的字符，
            // 所以截断值要尽量小点
            receiveMessage.setMessageContent(StrUtil.maxLength(message, 20000));
            receiveMessage.setBusinessMark("关单列表获取");
            Date date = new Date();
            receiveMessage.setMessageCreateTimestamp(date.getTime());
            receiveMessage.setMessageId(UUID.randomUUID().toString());
            qConsumerRecordEntity = consumerRecordService.writeRMQConsumerRecord(receiveMessage);
            DeclareFormBaseResponse declareFormBaseResponse = JsonMapper.fromJson(message, DeclareFormBaseResponse.class);
            if (declareFormBaseResponse != null && declareFormBaseResponse.getData() != null && declareFormBaseResponse.getData().getDecMessage() != null) {
                qConsumerRecordEntity.setMessageId(declareFormBaseResponse.getData().getDecMessage().getDecHead().getEntryId());
                context.getBean(this.getClass()).addOrUpdateForm(declareFormBaseResponse.getData().getDecMessage());
                qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_COMPLETE.getCode());
                //                channel.basicAck(mqMessage.getMessageProperties().getDeliveryTag(), false);
                consumerRecordService.updateById(qConsumerRecordEntity);
            } else if (declareFormBaseResponse != null && Objects.equals(declareFormBaseResponse.getMessageType(), "CusStatusCond_Query")) {
                qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_COMPLETE.getCode());
                String errorMsg = StrUtil.format("消费成功，报关单状态变更：", message);
                qConsumerRecordEntity.setErrorMsg(StrUtil.maxLength(errorMsg, 1800));
                consumerRecordService.updateById(qConsumerRecordEntity);
            } else {
                qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_FAILED.getCode());
                String errorMsg = StrUtil.format("消费失败，消息体找不到关单信息：{}", message);
                qConsumerRecordEntity.setErrorMsg(StrUtil.maxLength(errorMsg, 1800));
                consumerRecordService.updateById(qConsumerRecordEntity);
                //                if (mqMessage.getMessageProperties().getRedelivered())
                //                    // 重复消费，又再次失败，就不重新投递
                //                    channel.basicReject(mqMessage.getMessageProperties().getDeliveryTag(), false);
            }
        } catch (Exception e) {
            LOGGER.error("消费失败，消费时报错：", e);
        } finally {
            // 先都改成消费成功，不做任何处理
            if (qConsumerRecordEntity != null) {
                qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_COMPLETE.getCode());
                consumerRecordService.updateById(qConsumerRecordEntity);
            }
            TenantContext.clear();
            LoginInfoService.removeName();
        }
        LOGGER.info("--------------消费结束--------------------");
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void addOrUpdateForm(DecMessage decMessage) {
        LOGGER.info("口岸新增关单， {} ", JsonMapper.toJson(decMessage));
        // 进出口日期没有，就代表未结关
        if (!StringUtils.hasText(decMessage.getDecHead().getIEDate()))
            throw new BusinessServiceException(String.format("%s %s 没有进出口日期", decMessage.getDecHead().getEntryId(), decMessage.getDecHead().getContrNo()));
        if (decMessage.getDecResult().stream().noneMatch(result -> "已结关".equals(result.getCusStatusName())))
            throw new BusinessServiceException(String.format("%s %s 未结关", decMessage.getDecHead().getEntryId(), decMessage.getDecHead().getContrNo()));
        DecHead decHead = decMessage.getDecHead();
        List<DecList> decList = decMessage.getDecList();
        StockoutCustomsDeclareDocumentEntity documentEntity = declareDocumentService.getBaseMapper().findEnableByDeclareDocumentNoIgnoreTenant(decHead.getContrNo());
        if (documentEntity == null) {
            throw new BusinessServiceException("找不到对应的报关单据：" + decHead.getContrNo());
        }

        TenantContext.setTenant(documentEntity.getLocation());

        // 查找发货时间
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageIndex(1);
        pageRequest.setPageSize(-1);
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = declareDocumentAggregatedItemService.getAggregatedItemResultPage(documentEntity.getDeclareDocumentId(), pageRequest).getContent();
        if (CollectionUtil.isEmpty(aggregatedItemResultList)) {
            throw new BusinessServiceException("找不到对应的报关单据明细：" + decHead.getContrNo());
        }
        decList.forEach(decItem -> {
            StockoutCustomsDeclareFormEntity declareFormEntity = new StockoutCustomsDeclareFormEntity();
            // 固定值 赋值
            buildBaseInfo(declareFormEntity, decItem, decHead, documentEntity.getShippingDate());
            StockoutCustomsDeclareFormEntity entity = declareFormService.findByDeclareDocumentNoAndProtocolNoAndGNo(
                    declareFormEntity.getDeclareDocumentNo(), declareFormEntity.getProtocolNo(), declareFormEntity.getgNo());
            if (entity != null)
                return;
            // 计算值  根据协议号和项号 找报关单据 todo  目前先默认取序号
            buildDeclareFormCal(declareFormEntity, aggregatedItemResultList);
            declareFormEntity.setCreateDate(new Date());
            declareFormEntity.setCreateBy(loginInfoService.getName());
            declareFormEntity.setStatus(StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name());
            declareFormService.saveOrUpdate(declareFormEntity);
            declareFormLogService.addLog(declareFormEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.FETCH_FORM_DATA, "新增关单数据消息");
            // 电子口岸获取的海关单号需自动赋值到报关单据的【海关报关单】和【放行通知书】中
            declareDocumentFileService.saveOrUpdate(documentEntity.getDeclareDocumentId(), ExportDrawbackInformationTypeEnum.CUSTOMS_DECLARATION, declareFormEntity.getDeclareDocumentNo());
            declareDocumentFileService.saveOrUpdate(documentEntity.getDeclareDocumentId(), ExportDrawbackInformationTypeEnum.RELEASE_NOTICE, declareFormEntity.getDeclareDocumentNo());
            // 生成aeo
            stockoutCustomsDeclareAEOService.sendAsynOpMsg(new StockoutAEOAsynMsgBo(StockoutAEOAsynMsgBo.OperationType.SAVE_BY_FORM, declareFormEntity.getDeclareFormId(), declareFormEntity.getProtocolNo()));
        });
        TenantContext.clear();
    }

    // 重新设置报关单据的 毛重和净重
//    private void resetDeclareDocument(StockoutCustomsDeclareDocumentEntity documentEntity, List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemList, DecHead decHead) {
//        updateDocumentDeclareCode(documentEntity, decHead);
//        Boolean isRoughWeightDiff = resetRoughWeight(decHead, documentEntity, itemList);
//        Boolean isNetWeightDiff = resetNetWeight(decHead, documentEntity, itemList);
//        if (isRoughWeightDiff || isNetWeightDiff)
//            uploadNewDocument(documentEntity.getDeclareDocumentId());
//    }

//    private Boolean resetRoughWeight(DecHead decHead, StockoutCustomsDeclareDocumentEntity documentEntity, List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemList) {
//        //字段不存在 或者 没有偏差值
//        if (!StringUtils.hasText(decHead.getGrossWet())
//                || documentEntity.getWeight().compareTo(NumberUtil.toBigDecimal(decHead.getGrossWet())) == 0) {
//            return Boolean.FALSE;
//        }
//
//        BigDecimal realWeight = NumberUtil.toBigDecimal(decHead.getGrossWet());
//        BigDecimal sysWeight = documentEntity.getWeight();
//        // 毛重不相等
//        documentEntity.setWeight(realWeight);
//        documentEntity.setUpdateBy(loginInfoService.getName());
//        declareDocumentService.updateById(documentEntity);
//        //差值
//        BigDecimal differWeight = realWeight.subtract(sysWeight);
//        //平均重量
//        BigDecimal avgWeight = differWeight.divide(BigDecimal.valueOf(itemList.size()), 2, RoundingMode.DOWN);
//        //剩余重量
//        BigDecimal restWeight = differWeight.subtract(avgWeight.multiply(BigDecimal.valueOf(itemList.size())));
//        //先排序
//        List<StockoutCustomsDeclareDocumentAggregatedItemResult> sortResultList = itemList.stream().sorted(Comparator.comparing(StockoutCustomsDeclareDocumentAggregatedItemResult::getRoughWeight)).collect(Collectors.toList());
//        for (int i = 0; i < sortResultList.size() - 1; i++) {
//            StockoutCustomsDeclareDocumentAggregatedItemResult result = sortResultList.get(i);
//            BigDecimal changeWeight = result.getRoughWeight().add(avgWeight);
//            //小于0 则毛重设置0 然后剩余重量加回去
//            if (NumberUtil.isGreater(BigDecimal.ZERO, changeWeight)) {
//                restWeight = restWeight.add(changeWeight);
//                result.setRoughWeight(BigDecimal.ZERO);
//            } else {
//                result.setRoughWeight(changeWeight);
//            }
//        }
//
//        //设置最后一个
//        StockoutCustomsDeclareDocumentAggregatedItemResult lastResult = sortResultList.get(sortResultList.size() - 1);
//        BigDecimal changeWeight = lastResult.getRoughWeight().add(restWeight);
//        lastResult.setRoughWeight(changeWeight);
//        //设置明细
//        sortResultList.forEach(result -> {
//            List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = declareDocumentItemService.getByDeclareDocumentAggregatedItemId(result.getDeclareDocumentAggregatedItemId());
//            itemEntityList.forEach(itemEntity -> {
//                itemEntity.setRoughWeight(result.getRoughWeight());
//                declareDocumentItemService.updateById(itemEntity);
//            });
//        });
//        return Boolean.TRUE;
//    }
//
//    private Boolean resetNetWeight(DecHead decHead, StockoutCustomsDeclareDocumentEntity documentEntity, List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemList) {
//        //字段不存在 或者 没有偏差值
//        if (!StringUtils.hasText(decHead.getNetWt())
//                || documentEntity.getNetWeight().compareTo(NumberUtil.toBigDecimal(decHead.getNetWt())) == 0) {
//            return Boolean.FALSE;
//        }
//
//
//        BigDecimal realWeight = NumberUtil.toBigDecimal(decHead.getNetWt());
//        BigDecimal sysWeight = documentEntity.getNetWeight();
//        // 净重不相等
//        documentEntity.setNetWeight(realWeight);
//        documentEntity.setUpdateBy(loginInfoService.getName());
//        declareDocumentService.updateById(documentEntity);
//
//        //差值
//        BigDecimal differWeight = realWeight.subtract(sysWeight);
//        //平均重量
//        BigDecimal avgWeight = differWeight.divide(BigDecimal.valueOf(itemList.size()), 2, RoundingMode.DOWN);
//        //剩余重量
//        BigDecimal restWeight = differWeight.subtract(avgWeight.multiply(BigDecimal.valueOf(itemList.size())));
//        //先排序
//        List<StockoutCustomsDeclareDocumentAggregatedItemResult> sortResultList = itemList.stream().sorted(Comparator.comparing(StockoutCustomsDeclareDocumentAggregatedItemResult::getNetWeight)).collect(Collectors.toList());
//        for (int i = 0; i < sortResultList.size() - 1; i++) {
//            StockoutCustomsDeclareDocumentAggregatedItemResult result = sortResultList.get(i);
//            BigDecimal changeWeight = result.getNetWeight().add(avgWeight);
//
//            //小于0 则毛重设置0 然后剩余重量加回去
//            if (NumberUtil.isGreater(BigDecimal.ZERO, changeWeight)) {
//                restWeight = restWeight.add(changeWeight);
//                result.setNetWeight(BigDecimal.ZERO);
//            } else {
//                result.setNetWeight(changeWeight);
//            }
//        }
//        //设置最后一个
//        StockoutCustomsDeclareDocumentAggregatedItemResult lastResult = sortResultList.get(sortResultList.size() - 1);
//        BigDecimal changeWeight = lastResult.getNetWeight().add(restWeight);
//        lastResult.setNetWeight(changeWeight);
//        //设置明细
//        sortResultList.forEach(result -> {
//            List<StockoutCustomsDeclareDocumentItemEntity> itemEntityList = declareDocumentItemService.getByDeclareDocumentAggregatedItemId(result.getDeclareDocumentAggregatedItemId());
//            itemEntityList.forEach(itemEntity -> {
//                itemEntity.setNetWeight(result.getNetWeight());
//                declareDocumentItemService.updateById(itemEntity);
//            });
//        });
//        return Boolean.TRUE;
//    }

//    /**
//     * 上传新报关单据
//     *
//     * @param declareDocumentId
//     * @return
//     */
//    public String uploadNewDocument(Integer declareDocumentId) {
//        StockoutCustomsDeclareDocumentEntity documentEntity = declareDocumentService.getById(declareDocumentId);
//        if (Objects.isNull(documentEntity) || IsDeletedConstant.DELETED.equals(documentEntity.getIsDeleted())) {
//            throw new BusinessServiceException(String.format("单据不存在:%s", declareDocumentId));
//        }
//
//        //上传文件
//        Map<String, Object> map = new HashMap<>();
//        //这边type为null为全部数据
//        declareDocumentItemService.generateDataMap(map, declareDocumentId, null);
//        String fileName = System.currentTimeMillis() + ".xls";
//        InputStream in = FreeMarkerTemplateUtils.renderTemplateToInputStream("CustomsDeclareDocument.ftl", map);
//        String fileUrl = aliyunOssService.putObject(in, "declareDocument", fileName);
//        IOUtils.close(in);
//
//        //关联文件
//        StockoutCustomsDeclareDocumentFileService.RelateBo relateBo = new StockoutCustomsDeclareDocumentFileService.RelateBo(documentEntity, ExportDrawbackInformationTypeEnum.NEW_DOCUMENT, documentEntity.getDeclareDocumentNo(), fileUrl, fileName, Boolean.TRUE);
//        declareDocumentFileService.relate(relateBo);
//
//        return fileUrl;
//    }
//
//
//    private void updateDocumentDeclareCode(StockoutCustomsDeclareDocumentEntity documentEntity, DecHead decHead) {
//        // 修改海关报关单号
//        if (!StringUtils.hasText(documentEntity.getCustomsDeclareCode())
//                || !StringUtils.hasText(documentEntity.getAgentName())
//                || !StringUtils.hasText(documentEntity.getExportPort())) {
//            documentEntity.setCustomsDeclareCode(decHead.getEntryId());
//            documentEntity.setExportPort(decHead.getIEPort());
//            documentEntity.setAgentName(decHead.getAgentName());
//            declareDocumentService.updateById(documentEntity);
//        }
//    }

    private void buildBaseInfo(StockoutCustomsDeclareFormEntity declareFormEntity, DecList decItem, DecHead decHead, Date date) {
        declareFormEntity.setDeclareDocumentNo(decHead.getEntryId());
        declareFormEntity.setProtocolNo(decHead.getContrNo());
        declareFormEntity.setCompanyName(decHead.getOwnerName());
        LambdaQueryWrapper<BdCompanyEntity> queryWrapper = new LambdaQueryWrapper<BdCompanyEntity>().eq(BdCompanyEntity::getCompanyName, decHead.getOwnerName());
        List<BdCompanyEntity> list = bdCompanyService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            declareFormEntity.setCompanyId(list.get(0).getCompanyId());
        } else {
            throw new BusinessServiceException("报关公司中找不到此公司：" + decHead.getOwnerName());
        }
        declareFormEntity.setExportPort(decHead.getIEPort());
        declareFormEntity.setgName(decItem.getGName());
        declareFormEntity.setgQty(NumberUtil.toBigDecimal(decItem.getGQty()).intValue());
        declareFormEntity.setCfPrice(NumberUtil.toBigDecimal(decItem.getDeclTotal()).setScale(2, RoundingMode.HALF_UP));
        declareFormEntity.setCustomer(decHead.getOverseasConsigneeEname());
        declareFormEntity.setExportDate(DateUtil.parse(decHead.getIEDate()));
        BigDecimal inputRate = stockoutCustomsDeclareExchangeRateService.getLastRate(CustomsDeclareExchangeRateEnum.INPUT_RATE, BigDecimal.ONE);
        declareFormEntity.setInputRate(inputRate);
        declareFormEntity.setgNo(decItem.getGNo());
        declareFormEntity.setgCode(decItem.getCodeTS());
        declareFormEntity.setgName(decItem.getGName());
        declareFormEntity.setDeclareElement(decItem.getGModel());
        declareFormEntity.setgUnit(decItem.getFirstUnit());
        declareFormEntity.setgQty(NumberUtil.toBigDecimal(decItem.getFirstQty()).intValue());
        declareFormEntity.setHsCode(decItem.getCodeTS());
        declareFormEntity.setStockoutDate(date);
        declareFormEntity.setdDate(StrUtil.isEmpty(decHead.getDDate()) ? null : DateUtil.parse(decHead.getDDate(), "yyyyMMdd"));
        declareFormEntity.setAgentName(decHead.getAgentName());
    }

    private void buildDeclareFormCal(StockoutCustomsDeclareFormEntity declareFormEntity, List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList) {
        int i = NumberUtil.toBigDecimal(declareFormEntity.getgNo()).intValue();
        Map<Integer, StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultMap = aggregatedItemResultList.stream().collect(Collectors.toMap(StockoutCustomsDeclareDocumentAggregatedItemResult::getgNo, Function.identity(), (v1, v2) -> v1));
        StockoutCustomsDeclareDocumentAggregatedItemResult aggregatedItemResult = aggregatedItemResultMap.get(i);
        if (Objects.isNull(aggregatedItemResult)) {
            throw new BusinessServiceException(String.format("协议号 %s 项号 %s 的报关单据不存在", declareFormEntity.getProtocolNo(), i));
        }
        declareFormEntity.setDocumentSigningDate(aggregatedItemResult.getSigningDate());
        declareFormEntity.setBoxQty(aggregatedItemResult.getActualBoxQty());
        declareFormEntity.setApportionedFreight(aggregatedItemResult.getApportionFreight().setScale(2, RoundingMode.HALF_UP));
        declareFormEntity.setFobPrice(declareFormEntity.getCfPrice().subtract(declareFormEntity.getApportionedFreight()).setScale(2, RoundingMode.HALF_UP));
        //设置汇率
        StockoutCustomsDeclareDocumentEntity declareDocument = declareDocumentService.getEnableByDeclareDocumentId(aggregatedItemResult.getDeclareDocumentId());
        buildExchangeRate(declareFormEntity, declareDocument);
        declareFormEntity.setFobPriceCny(declareFormEntity.getFobPrice().multiply(declareFormEntity.getExchangeRate()).setScale(2, RoundingMode.HALF_UP));
        declareFormEntity.setRoughWeight(aggregatedItemResult.getActualRoughWeight());
        //通过进项比例和税率计算含税金额/含税单价/fob总价
        declareFormService.computeByInputRateAndExchangeRate(declareFormEntity, declareFormEntity.getExchangeRate(), declareFormEntity.getInputRate());
        Integer categoryCustomsDeclareId = aggregatedItemResult.getCategoryCustomsDeclareId();
        ProductCategoryCustomsDeclareEntity wmsCategory = categoryCustomsDeclareService.getById(categoryCustomsDeclareId);
        if (wmsCategory == null) {
            throw new BusinessServiceException("找不到wms分类报关：" + categoryCustomsDeclareId);
        }
        declareFormEntity.setWmsCategoryId(wmsCategory.getWmsCategoryId());
//        declareFormEntity.setStockinDate(DateUtil.offsetDay(declareFormEntity.getStockoutDate(), -15));
    }

    /**
     * 设置汇率
     *
     * @param declareFormEntity
     */
    private void buildExchangeRate(StockoutCustomsDeclareFormEntity declareFormEntity, StockoutCustomsDeclareDocumentEntity declareDocument) {
        BigDecimal exchangeRate;
        if (CurrencyTypeEnum.CNY.name().equals(declareDocument.getCurrency())) {
            exchangeRate = BigDecimal.ONE;
        } else {
            exchangeRate = exchangeRateService.getRateByRecordDate(DateUtil.format(declareFormEntity.getExportDate(), "yyyy-MM"), CustomsDeclareExchangeRateEnum.FORM_EXCHANGE_RATE, BigDecimal.ZERO);
        }
        declareFormEntity.setExchangeRate(exchangeRate);
    }

}
