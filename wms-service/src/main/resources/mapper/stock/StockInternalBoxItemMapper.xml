<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper">

    <select id="searchInternalBoxItemList" resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select
        item.internal_box_item_id as internal_box_item_id,
        item.batch_id as batchId,
        item.internal_box_code as internalBoxCode,
        item.source_area_id as sourceAreaId,
        item.source_position_code as sourcePositionCode,
        item.sku as sku,
        item.qty as qty,
        item.internal_box_item_id as internalBoxItemId,
        item.product_id as productId, item.spec_id as specId,
        item.space_id as spaceId,
        box.internal_box_id as internalBoxId
        from stock_internal_box_item item
        left join stock_internal_box box on item.internal_box_id = box.internal_box_id
        <where>
            <if test="internalBoxType!=null and internalBoxType!=''">
                and box.internal_box_type = #{internalBoxType}
            </if>
            <if test="batchId!=null and batchId!=''">
                and item.batch_id = #{batchId}
            </if>
            <if test="sku!=null and sku !=''">
                and item.sku = #{sku}
            </if>
        </where>
    </select>

    <select id="searchInternalBoxItemListByBatchId"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select item.batch_id as batchId,
        item.internal_box_item_id as internal_box_item_id,
        item.internal_box_code as internalBoxCode,
        item.sku as sku,
        item.qty as qty,
        item.source_area_id as sourceAreaId,
        item.source_position_code as sourcePositionCode
        from stock_internal_box_item item
        left join stock_internal_box box on item.internal_box_id = box.internal_box_id
        <where>
            <if test="internalBoxType!=null and internalBoxType!=''">
                and box.internal_box_type = #{internalBoxType}
            </if>
            <if test="batchId!=null and batchId!=''">
                and item.batch_id = #{batchId}
            </if>
        </where>
    </select>

    <select id="searchInternalBoxItemListByBatchIdAndInternalBoxType"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select
        item.batch_id as batchId,
        item.internal_box_item_id as internal_box_item_id,
        item.source_area_id as sourceAreaId,
        item.source_position_code as sourcePositionCode,
        item.internal_box_id as internalBoxId,
        item.internal_box_code as internalBoxCode,
        item.sku as sku, item.qty as qty,
        item.internal_box_item_id as internalBoxItemId,
        item.product_id as productId, item.spec_id as specId,
        item.space_id as spaceId,
        item.purchase_plan_no as purchasePlanNo,
        item.stock_in_order_no as stockInOrderNo,
        item.order_no as order_no,item.stockout_order_no as stockoutOrderNo,
        item.return_order_no as returnOrderNo,
        item.transfer_in_space_task_id as transferInSpaceTaskId,
        item.transfer_internal_box_task_id as transferInternalBoxTaskId
        from stock_internal_box_item item
        left join stock_internal_box box on item.internal_box_id = box.internal_box_id
        <where>
            <if test="internalBoxType!=null and internalBoxType!=''">
                and box.internal_box_type = #{internalBoxType}
            </if>
            <if test="batchId!=null and batchId!=''">
                and item.batch_id = #{batchId}
            </if>
        </where>
    </select>

    <select id="searchInternalBoxItemListByRequestList"
            resultType="com.nsy.api.wms.domain.stock.StockInternalBoxItemInfo">
        select i.internal_box_item_id as internalBoxItemId,
        i.internal_box_code as internalBoxCode,
        i.internal_box_id as internalBoxId,
        i.batch_id as batchId,
        i.sku as sku,
        i.qty as qty,
        i.purchase_plan_no as purchasePlanNo,
        i.transfer_in_space_task_id as transferInSpaceTaskId,
        i.stock_in_order_no as stockInOrderNo,
        i.stockout_order_no as stockoutOrderNo,
        i.order_no as orderNo,
        i.return_order_no as returnOrderNo,
        i.create_date as createDate,
        i.description as description,
        i.create_by as createBy,
        i.update_date as updateDate,
        i.update_by as updateBy,
        i.status as skuStatusEn,
        i.source_position_code as sourcePositionCode,
        box.internal_box_type as internalBoxType,
        box.status as status,
        box.workspace as workspace,
        p.spu as spu
        from stock_internal_box_item i
        left join stock_internal_box box on i.internal_box_code = box.internal_box_code
        left join product_info p on i.product_id = p.product_id
        <where>
            <if test="request.internalBoxItemIds != null and request.internalBoxItemIds.size() >0">
                and i.internal_box_item_id in
                <foreach collection="request.internalBoxItemIds" separator="," index="index" item="internalBoxItemId"
                         open="("
                         close=")">
                    #{internalBoxItemId}
                </foreach>
            </if>
            <if test="request != null and request.internalBoxCode!=null and request.internalBoxCode!=''">
                and i.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request !=null and request.batchId!=null">
                and i.batch_id = #{request.batchId}
            </if>
            <if test="request.internalBoxTypeList != null and request.internalBoxTypeList.size() >0">
                and box.internal_box_type in
                <foreach collection="request.internalBoxTypeList" separator="," index="index" item="internalBoxType"
                         open="("
                         close=")">
                    #{internalBoxType}
                </foreach>
            </if>
            <if test="request != null and request.internalBoxType!=null and request.internalBoxType!=''">
                and box.internal_box_type = #{request.internalBoxType}
            </if>
            <if test="request != null and request.status!=null and request.status!=''">
                and box.status = #{request.status}
            </if>
            <if test="request != null and request.sku!=null and request.sku!=''">
                and i.sku = #{request.sku}
            </if>
            <if test="request !=null and request.workspace!=null and request.workspace!=''">
                and box.workspace = #{request.workspace}
            </if>
            <if test="request != null and request.stockInOrderNo!=null and request.stockInOrderNo!=''">
                and i.stock_in_order_no = #{request.stockInOrderNo}
            </if>
            <if test="request != null and request.purchasePlanNo!=null and request.purchasePlanNo!=''">
                and i.purchase_plan_no = #{request.purchasePlanNo}
            </if>
            <if test="request !=null and request.stockoutOrderNo!=null and request.stockoutOrderNo!=''">
                and i.stockout_order_no = #{request.stockoutOrderNo}
            </if>
            <if test="request !=null and request.returnOrderNo!=null and request.returnOrderNo!=''">
                and i.return_order_no = #{request.returnOrderNo}
            </if>
            <if test="request != null and request.specId!=null">
                and i.spec_id = #{request.specId}
            </if>
            <if test="request != null and request.createStartDate!=null">
                and i.create_date &gt;= #{request.createStartDate}
            </if>
            <if test="request != null and request.createEndDate!=null">
                and i.create_date &lt;= #{request.createEndDate}
            </if>
        </where>
        order by i.create_date asc
    </select>

    <select id="searchListByInternalBoxCodeAndInternalBoxType"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select
        i.internal_box_code as internalBoxCode,
        i.sku as sku,
        i.qty as qty,
        i.transfer_internal_box_task_id as transferInternalBoxTaskId
        from stock_internal_box_item i
        left join stock_internal_box b on i.internal_box_id = b.internal_box_id
        left join stock_transfer_internal_box_task t on i.transfer_internal_box_task_id = t.transfer_task_id
        <where>
            <if test="internalBoxCode!=null and internalBoxCode!=''">
                and i.internal_box_code = #{internalBoxCode}
            </if>
            <if test="internalBoxType!=null and internalBoxType!=''">
                and b.internal_box_type = #{internalBoxType}
            </if>
        </where>
    </select>

    <select id="searchTransferBoxListByInternalBoxCodeList"
            resultType="com.nsy.api.wms.domain.stockin.StockShelveSplitTaskTransferBoxInfo">
        select
        s.space_id as spaceId,
        s.space_name as spaceName,
        ti.up_shelve_space_area_id as spaceAreaId,
        ti.up_shelve_space_area_name as spaceAreaName,
        ti.up_shelve_position_id as positionId,
        ti.up_shelve_position_code as positionCode,
        ti.product_id as productId,
        ti.spec_id as specId,
        ti.sku as sku,
        ti.seller_sku as sellerSku,
        ti.seller_barcode as sellerBarcode,
        ti.expected_transfer_qty as qty,
        ti.barcode as barcode,
        t.transfer_task_id as transferTaskId,
        t.transfer_box_code as transferBoxCode
        from stock_transfer_internal_box_task_item ti
        left join stock_transfer_internal_box_task t on ti.transfer_task_id = t.transfer_task_id
        left join bd_space s on t.space_id = s.space_id
        <where>
            <if test="statusList !=null and statusList.size() > 0">
                and t.status in
                <foreach collection="statusList" separator="," index="index" item="status" open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="internalBoxCodeList !=null and internalBoxCodeList.size() > 0">
                and t.transfer_box_code in
                <foreach collection="internalBoxCodeList" separator="," index="index" item="internalBoxCode" open="("
                         close=")">
                    #{internalBoxCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="searchByInternalBoxCodeAndStatus"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select
        i.internal_box_item_id as internalBoxItemId,
        i.space_id as spaceId,
        i.qty as qty,
        i.internal_box_id as internalBoxId,
        i.internal_box_code as internalBoxCode,
        i.purchase_plan_no as spaceAreaName,
        i.stock_in_order_no as stockInOrderNo,
        i.return_order_no as returnOrderNo,
        i.product_id as productId,
        i.spec_id as specId,
        i.sku as sku,
        i.status status
        from stock_internal_box_item i
        left join stock_internal_box b on i.internal_box_id = b.internal_box_id
        <where>
            <if test="internalBoxCode!=null and internalBoxCode!=''">
                and b.internal_box_code = #{internalBoxCode}
            </if>
            <if test="status !=null and status !=''">
                and b.status = #{status}
            </if>
        </where>
    </select>
    <select id="stockinOrderDetailList" resultType="com.nsy.api.wms.response.stockin.StockinOrderBoxListResponse">
        select
        ANY_VALUE(stock_internal_box_item.internal_box_code) as internalBoxCode,
        ANY_VALUE(stock_internal_box.status) as status,
        ANY_VALUE(stock_internal_box.space_area_name) as spaceAreaName,
        sum(stock_internal_box_item.qty) as qty,
        ANY_VALUE(stock_internal_box.update_by) as updateBy,
        ANY_VALUE(stock_internal_box.update_date) as updateDate
        from stock_internal_box_item stock_internal_box_item
        left join stock_internal_box stock_internal_box on stock_internal_box_item.internal_box_id =
        stock_internal_box.internal_box_id
        <where>
            <if test="request.internalBoxCode!=null and request.internalBoxCode!=''">
                and stock_internal_box.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.stockinOrderNo!=null and request.stockinOrderNo!=''">
                and stock_internal_box_item.stock_in_order_no = #{request.stockinOrderNo}
            </if>
            <if test="request.sku!=null and request.sku!=''">
                and stock_internal_box_item.sku = #{request.sku}
            </if>
        </where>
        group by stock_internal_box_item.internal_box_code
    </select>


    <select id="searchSupplierDeliveryNoByInternalBoxCode"
            resultType="com.nsy.api.wms.domain.stock.StockInternalBoxSupplierDeliveryNoInfo">
        select
        i.qty as qty,
        i.sku as sku,
        i.spec_id as specId,
        i.space_id as spaceId,
        i.status as status,
        ti.area_name as areaName,
        ti.is_pre_qa_qualified as isPreQaQualified,
        ti.store_id as storeId,
        ti.is_fba_quick as isFbaQuick,
        ti.business_type as businessType,
        t.supplier_delivery_no as supplierDeliveryNo,
        o.stockin_type as stockinType,
        st.position_code as positionCode
        from stock_internal_box_item i
        left join stockin_order o on i.stock_in_order_no = o.stockin_order_no
        left join stockin_order_task t on o.task_id = t.task_id
        left join stockin_order_task_item ti on( ti.task_id = t.task_id
        and ti.sku = i.sku and ti.purchase_plan_no = i.purchase_plan_no)
        left join stockin_shelve_task s on i.internal_box_code = s.internal_box_code and s.status!='SHELVED'
        left join stockin_shelve_task_item st on s.shelve_task_id = st.shelve_task_id
        and i.sku = st.sku and i.purchase_plan_no = st.purchase_plan_no
        and st.source_id = o.stockin_order_id
        <where>
            <if test="internalBoxCode!=null and internalBoxCode!=''">
                and i.internal_box_code = #{internalBoxCode} and i.qty >0
            </if>
        </where>
    </select>

    <select id="searchWaitQcList" resultType="com.nsy.api.wms.domain.stock.StockInternalBoxQcInfo">
        select oi.sku,
               oi.qty          as productInboundsCount,
               ti.qa_qty       as demandQcCount,
               o.supplier_id   as supplierId,
               o.supplier_name as supplierName
        from stockin_order_item oi
                 left join stockin_order_task_item ti on oi.task_item_id = ti.task_item_id
                 left join stockin_order o on oi.stockin_order_id = o.stockin_order_id
        where oi.internal_box_code = #{request.boxBarcode}
          AND oi.status IN ('WAIT_QC', 'QC_PROCESSING', 'WAIT_DEAL', 'WAIT_PURCHASE_CONFIRM')
          and ti.is_need_qa = 1
    </select>

    <select id="searchRoutingInspectionList"
            resultType="com.nsy.api.wms.domain.stock.StockInternalBoxRoutingInspectionInfo">
        select
        bi.internal_box_item_id as boxDetailId,
        ps.supplier_id as supplierId,
        ps.supplier_name as supplierName,
        bi.purchase_plan_no as purchaseNumber,
        o.supplier_delivery_box_code as receiveOrder,
        bi.sku as sku,
        psi.barcode as skuBarcode,
        box.internal_box_code as boxBarcode,
        bi.qty as productInboundsCount,
        bi.stock_in_order_no as stockinOrderNo,
        psi.skc as colorSku,
        p.spu as productSku,
        bi.workmanship_version as workmanshipVersion,
        (select if(ti.first_order_label = '新',1,if(ti.purchase_skc_first_order = '新',1,0)) from stockin_order_task_item ti
        where ti.task_id = t.task_id
            and ti.sku = bi.sku
            and ti.purchase_plan_no =bi.purchase_plan_no
            limit 1) as isNew,
        (select ti.purchasing_apply_type from stockin_order_task_item ti
        where ti.task_id = t.task_id
        and ti.sku = bi.sku
        and ti.purchase_plan_no =bi.purchase_plan_no
        limit 1) as purchasingApplyType
        from stock_internal_box_item bi
        left join stockin_order o on bi.stock_in_order_no = o.stockin_order_no
        left join stockin_order_task t on o.task_id=t.task_id
        left join stock_internal_box box on bi.internal_box_code = box.internal_box_code
        left join stock_platform_schedule ps on t.platform_schedule_id = ps.platform_schedule_id
        left join product_spec_info psi on bi.spec_id = psi.spec_id
        left join product_info p on bi.product_id = p.product_id

        <where>
            <if test="request.boxBarcode!=null and request.boxBarcode!=''">
                and box.internal_box_code = #{request.boxBarcode}
            </if>
            <if test="internalBoxTypeList !=null and internalBoxTypeList.size() > 0">
                and box.internal_box_type in
                <foreach collection="internalBoxTypeList" separator="," index="index" item="internalBoxType" open="("
                         close=")">
                    #{internalBoxType}
                </foreach>
            </if>
            <if test="statusList !=null and statusList.size() > 0">
                and box.status not in
                <foreach collection="statusList" separator="," index="index" item="status" open="(" close=")">
                    #{status}
                </foreach>
            </if>
        </where>

    </select>

    <select id="searchWaitDealRoutingInspectionList" resultType="com.nsy.api.wms.domain.stock.StockInternalBoxRoutingInspectionInfo">
        select
        bi.internal_box_item_id as boxDetailId,
        ps.supplier_id as supplierId,
        ps.supplier_name as supplierName,
        GROUP_CONCAT(distinct bi.purchase_plan_no) as purchaseNumber,
        GROUP_CONCAT(distinct o.supplier_delivery_box_code) as receiveOrder,
        bi.sku as sku,
        psi.barcode as skuBarcode,
        box.internal_box_code as boxBarcode,
        sum(bi.qty) as productInboundsCount,
        bi.stock_in_order_no as stockinOrderNo,
        psi.skc as colorSku,
        p.spu as productSku,
        bi.workmanship_version as workmanshipVersion,
        (select if(ti.first_order_label = '新',1,if(ti.purchase_skc_first_order = '新',1,0)) from stockin_order_task_item ti
        where ti.task_id = t.task_id
        and ti.sku = bi.sku
        and ti.purchase_plan_no =bi.purchase_plan_no
        limit 1) as isNew,
        (select ti.purchasing_apply_type from stockin_order_task_item ti
        where ti.task_id = t.task_id
        and ti.sku = bi.sku
        and ti.purchase_plan_no =bi.purchase_plan_no
        limit 1) as purchasingApplyType
        from stock_internal_box_item bi
        left join stockin_order o on bi.stock_in_order_no = o.stockin_order_no
        left join stockin_order_task t on o.task_id=t.task_id
        left join stock_internal_box box on bi.internal_box_code = box.internal_box_code
        left join stock_platform_schedule ps on t.platform_schedule_id = ps.platform_schedule_id
        left join product_spec_info psi on bi.spec_id = psi.spec_id
        left join product_info p on bi.product_id = p.product_id
        where   bi.status in ("WAIT_QC","QC_PROCESSING","WAIT_DEAL")
            <if test="request.boxBarcode!=null and request.boxBarcode!=''">
                and box.internal_box_code = #{request.boxBarcode}
            </if>
            <if test="internalBoxTypeList !=null and internalBoxTypeList.size() > 0">
                and box.internal_box_type in
                <foreach collection="internalBoxTypeList" separator="," index="index" item="internalBoxType" open="("
                         close=")">
                    #{internalBoxType}
                </foreach>
            </if>
        group by box.internal_box_code,bi.sku
    </select>


    <select id="searchByInternalBoxCode" resultType="com.nsy.api.wms.domain.stock.StockInternalBoxItemQcInfo">
        select bi.sku as sku,
        bi.seller_sku as sellerSku,
        bi.seller_barcode as sellerBarcode,
        psi.barcode as barcode,
        bi.stock_in_order_no as stockinOrderNo,
        bi.order_no as orderNo,
        bi.qty as qty,
        bi.purchase_plan_no as purchasePlanNo,
        psi.image_url as imageUrl,
        bi.workmanship_version as workmanshipVersion,
        bi.brand_name as brandName,
        bi.status as status,
        (select ti.barcode from stockin_order_item oi
        inner join stockin_order o
        on oi.stockin_order_id = oi.stockin_order_id
        inner join stockin_order_task_item ti
        on ti.task_item_id = oi.task_item_id
            where oi.internal_box_code = bi.internal_box_code
            and oi.purchase_plan_no = bi.purchase_plan_no
            and oi.sku = bi.sku
            and o.stockin_order_no = bi.stock_in_order_no
            limit 1) as stockinBarcode
        from stock_internal_box_item bi
        left join product_spec_info psi on bi.spec_id = psi.spec_id
        <where>
            <if test="internalBoxCode!=null and internalBoxCode!=''">
                and bi.internal_box_code = #{internalBoxCode}
            </if>
        </where>
    </select>

    <select id="searchByOderItem">
        select *
        from stock_internal_box_item bi

    </select>

    <select id="queryExportData" resultType="com.nsy.api.wms.domain.stockin.StockInternalBoxItemExport">
        SELECT
        bi.internal_box_code,
        bi.sku,
        bi.`status` as statusStr,
        bi.qty,
        bi.purchase_plan_no
        FROM
        stock_internal_box_item bi
        where
        bi.qty > 0 and
        bi.internal_box_id in
        <foreach collection="idList" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        order by bi.internal_box_id
    </select>

    <select id="sumQtyByInternalBoxCodeList"
            resultType="com.nsy.wms.business.domain.bo.stock.StockInternalBoxSumQtyByCodeBo">
        SELECT
        sibi.internal_box_code,
        sum( sibi.qty ) as qty
        FROM
        stock_internal_box_item sibi
        <where>
            <if test="internalBoxCodeList !=null and internalBoxCodeList.size() > 0">
                sibi.internal_box_code in
                <foreach collection="internalBoxCodeList" separator="," index="index" item="internalBoxCode" open="("
                         close=")">
                    #{internalBoxCode}
                </foreach>
            </if>
        </where>
        GROUP BY
        sibi.internal_box_code
    </select>

    <select id="findSupplierByBoxCodeAndProductId" resultType="java.lang.Integer">
        select DISTINCT o.supplier_id
        from stock_internal_box_item i
                 left join stockin_order o on i.stock_in_order_no = o.stockin_order_no
        where i.internal_box_code = #{internalBoxCode}
          and i.product_id = #{productId}
          and i.qty > 0
    </select>

    <select id="findSupplierByBoxCodeAndSku" resultType="java.lang.Integer">
        select DISTINCT o.supplier_id
        from stock_internal_box_item i
                 left join stockin_order o on i.stock_in_order_no = o.stockin_order_no
        where i.internal_box_code = #{internalBoxCode}
          and i.sku = #{sku}
          and i.qty > 0
    </select>

    <select id="findListByTypeBatchIdAndSku"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        SELECT sibi.*
        FROM stock_internal_box_item sibi
                 LEFT JOIN stock_internal_box sib ON sibi.internal_box_id = sib.internal_box_id
        WHERE sib.`internal_box_type` = #{type}
          AND sibi.batch_id = #{batchId}
          AND sibi.sku = #{sku}
    </select>
    <select id="searchInternalBoxItemListByBatchIds"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select item.batch_id as batchId,
        item.internal_box_item_id as internal_box_item_id,
        item.internal_box_code as internalBoxCode,
        item.sku as sku,
        item.qty as qty,
        item.source_area_id as sourceAreaId,
        item.source_position_code as sourcePositionCode
        from stock_internal_box_item item
        left join stock_internal_box box on item.internal_box_id = box.internal_box_id
        <where>
            <if test="internalBoxType!=null and internalBoxType!=''">
                and box.internal_box_type = #{internalBoxType}
            </if>
            and item.batch_id in
            <foreach collection="batchIds" separator="," index="index" item="batchId" open="("
                     close=")">
                #{batchId}
            </foreach>
        </where>
    </select>
    <select id="getWaitClearPickingBoxItem"
            resultType="com.nsy.api.wms.domain.stockout.StockoutClearPickingBoxItem">
        select bi.internal_box_item_id, sb.batch_id, sb.batch_type, sb.picking_type
        from stock_internal_box_item bi
                 INNER JOIN stock_internal_box b on b.internal_box_id = bi.internal_box_id
                 INNER JOIN stockout_batch sb on sb.batch_id = bi.batch_id
        where b.internal_box_type = 'PICKING_BOX'
          and sb.`status` = 'COMPLETED'
          and sb.stockout_type != 'LIGHT_CUSTOMIZATION_DELIVERY'
          and bi.source_position_code is not null
          and bi.qty >0
          and bi.update_date &lt; #{afterDate}


    </select>
    <select id="getWaitClearPickingBoxItemWholePick"
            resultType="com.nsy.api.wms.domain.stock.StockInternalBoxItemOrder">
        SELECT bi.internal_box_item_id as internalBoxItemId,
               so.stockout_order_no as stockoutOrderNo
        FROM nsy_wms.`stock_internal_box_item` bi
        INNER JOIN nsy_wms.`stock_internal_box` b ON bi.`internal_box_id` = b.`internal_box_id`
        INNER JOIN nsy_wms.`stockout_batch` sb ON bi.`batch_id` = sb.batch_id
        INNER JOIN nsy_wms.`stockout_batch_order` sbo ON sb.`batch_id` = sbo.`batch_id`
        INNER JOIN nsy_wms.`stockout_order` so ON sbo.`stockout_order_id` = so.`stockout_order_id`
        WHERE  b.internal_box_type  ='PICKING_BOX'
        AND sb.picking_type = 'WHOLE_PICK'
          AND  so.`status` IN ('DELIVERED','READY_DELIVERY','CANCELLED','CANCELLING')
          and so.update_date &lt; #{afterDate}
          and sb.stockout_type != 'LIGHT_CUSTOMIZATION_DELIVERY'
          and bi.source_position_code is not null
          and bi.qty >0
        group by bi.internal_box_item_id
    </select>
    <select id="findAreaNameByBoxCode" resultType="java.lang.String">
        select DISTINCT ti.area_name
        from stock_internal_box_item i
                 left join stockin_order o on i.stock_in_order_no = o.stockin_order_no
                 left join stockin_order_item oi on oi.stockin_order_id = o.stockin_order_id
            and i.sku = oi.sku
            and i.purchase_plan_no = oi.purchase_plan_no
            and i.internal_box_code = oi.internal_box_code
                 left join stockin_order_task_item ti on ti.task_item_id = oi.task_item_id
        where i.internal_box_code = #{internalBoxCode}
        and i.qty > 0
    </select>
    <select id="listStockByBoxCode"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        SELECT distinct bi.*
        FROM nsy_wms.`stock_internal_box_item` bi
        inner join stock s
        on bi.internal_box_id = s.internal_box_id
        and bi.sku = s.sku
        where bi.internal_box_code in
        <foreach collection="list" separator="," index="index" item="item" open="("
                 close=")">
            #{item}
        </foreach>
        and bi.qty > 0
    </select>
    <select id="searchInternalBoxItemListByBatchIdsAndSku"
            resultType="com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity">
        select item.batch_id as batchId,
        item.internal_box_item_id as internal_box_item_id,
        item.internal_box_code as internalBoxCode,
        item.sku as sku,
        item.qty as qty,
        item.source_area_id as sourceAreaId,
        item.source_position_code as sourcePositionCode
        from stock_internal_box_item item
        left join stock_internal_box box on item.internal_box_id = box.internal_box_id
        <where>
            <if test="internalBoxType!=null and internalBoxType!=''">
                and box.internal_box_type = #{internalBoxType}
            </if>
            and item.batch_id in
            <foreach collection="batchIds" separator="," index="index" item="batchId" open="("
                     close=")">
                #{batchId}
            </foreach>
            <if test="sku !=null and sku!=''">
                and item.sku = #{sku}
            </if>
        </where>

    </select>

    <select id="queryWaitQcQtySummaryIgnoreTenant"
            resultType="com.nsy.api.wms.response.stockin.QueryWaitQcQtySummaryResponse">
        SELECT bi.purchase_plan_no as orderNo, bi.sku as sku, MAX(ps.`skc`) AS skc, SUM(bi.qty) as waitQcQty FROM nsy_wms.stock_internal_box_item bi
            INNER JOIN nsy_wms.stock_internal_box b ON b.internal_box_id = bi.internal_box_id
            INNER JOIN nsy_wms.`product_spec_info` ps ON ps.`sku` = bi.`sku`
            WHERE b.internal_box_type IN ('RECEIVE_BOX', 'QA_BOX')
            AND bi.`status` IN ('WAIT_QC', 'QC_PROCESSING', 'WAIT_DEAL', 'WAIT_PURCHASE_CONFIRM')
            <if test="query.orderNoList != null and query.orderNoList.size() > 0">
                and bi.purchase_plan_no in
                <foreach collection="query.orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="query.skcList != null and query.skcList.size() > 0">
                and ps.skc in
                <foreach collection="query.skcList" separator="," index="index" item="skc" open="(" close=")">
                    #{skc}
                </foreach>
            </if>
            GROUP BY bi.purchase_plan_no, bi.sku
    </select>

    <select id="getSpaceIdByInfo" resultType="java.lang.Integer">
        select oi.space_id from stock_internal_box_item ti
        inner join stockin_order o on o.stockin_order_no = ti.stock_in_order_no
        inner join  stockin_order_item oi on o.stockin_order_id = oi.stockin_order_id and ti.purchase_plan_no = oi.purchase_plan_no and ti.sku = oi.sku
        where ti.internal_box_code =#{internalBoxCode} and ti.sku = #{sku} and   ti.space_id is not null limit 1
    </select>
    <select id="findTaskIdByInternalBoxCode" resultType="java.lang.Integer">
        select distinct o.task_id from stock_internal_box_item bi
        inner join stockin_order o
        on o.stockin_order_no = bi.stock_in_order_no
        where bi.internal_box_code = #{internalBoxCode}
        and bi.qty > 0
    </select>

    <select id="getArriveCountByInfo" resultType="java.lang.Integer">
        select  IFNULL(sum(soi.qty) , 0)    from stock_internal_box_item sibi
        left join stockin_order so on so.stockin_order_no = sibi.stock_in_order_no
        left join stockin_order_item soi on soi.stockin_order_id = so.stockin_order_id and sibi.sku = soi.sku and sibi.purchase_plan_no = soi.purchase_plan_no
        where sibi.internal_box_code = #{internalBoxCode} and sibi.sku = #{sku}
    </select>

    <select id="getLabelAttributeNames" resultType="java.lang.String">
        select distinct ti.label_attribute_names
        from stock_internal_box_item bi
        INNER JOIN stockin_order o on o.stockin_order_no = bi.stock_in_order_no
        INNER JOIN stockin_order_item oi on oi.stockin_order_id = o.stockin_order_id and oi.sku = bi.sku and oi.purchase_plan_no = bi.purchase_plan_no
        INNER JOIN stockin_order_task_item ti on ti.task_item_id = oi.task_item_id
        where bi.internal_box_code = #{internalBoxCode} and bi.sku = #{sku}
        and bi.qty > 0
    </select>
    
    <select id="getBusinessType" resultType="java.lang.String">
        select ti.business_type
        from stock_internal_box_item bi
        INNER JOIN stockin_order o on o.stockin_order_no = bi.stock_in_order_no
        INNER JOIN stockin_order_task t on t.task_id = o.task_id
        INNER JOIN stockin_order_item oi on oi.stockin_order_id = o.stockin_order_id and oi.sku = bi.sku and oi.purchase_plan_no = bi.purchase_plan_no and oi.internal_box_code = bi.internal_box_code
        INNER JOIN stockin_order_task_item ti on ti.task_item_id = oi.task_item_id
        where bi.internal_box_code = #{internalBoxCode} and bi.sku = #{sku} and bi.qty > 0
        limit 1
    </select>
    
    <select id="getPurchaseNoByInfo" resultType="java.lang.String">
        select bi.purchase_plan_no
        from stock_internal_box_item bi
        where bi.internal_box_code = #{internalBoxCode} and bi.sku = #{sku}
          and  bi.purchase_plan_no is not null
            limit 1
    </select>


    <select id="getBoxQtyByInternalBoxCodeAndSku" resultType="java.lang.Integer">
        select
            sum(qty) as qty
        from stock_internal_box_item
        where internal_box_code = #{internalBoxCode}
          and sku = #{sku}
    </select>
    <select id="countNewStockinItem" resultType="java.lang.Integer">
        select count(bi.internal_box_item_id)
        from stock_internal_box_item bi
                 INNER JOIN stockin_order o on o.stockin_order_no = bi.stock_in_order_no
                 INNER JOIN stockin_order_item oi on oi.stockin_order_id = o.stockin_order_id and oi.sku = bi.sku and oi.purchase_plan_no = bi.purchase_plan_no
                 INNER JOIN stockin_order_task_item ti on ti.task_item_id = oi.task_item_id
        where bi.internal_box_code = #{internalBoxCode} and bi.sku = #{sku}
          and bi.qty > 0
        and ti.first_order_label = '新'
    </select>
    
    <select id="getStockInternalBoxAutoShelveList" resultType="com.nsy.wms.elasticjob.stockin.StockInternalBoxAutoShelveResponse">
        select distinct sti.shelve_task_id,sti.shelve_task_item_id,t.sku as sku, t.internal_box_code as internalBoxCode,
               (sti.stockin_qty- sti.shelved_qty- sti.returned_qty) as stockinQty,r.position_code as positionCode 
        from
            stock_internal_box_item t
        inner join bd_stockin_auto_shelve_rule r on r.internal_box_code = t.internal_box_code
        inner join stockin_qa_order q on q.internal_box_code = t.internal_box_code and q.sku = t.sku
        inner join stockin_shelve_task st on st.internal_box_code = t.internal_box_code and st.status in ('SHELVING', 'PENDING')
        inner join stockin_shelve_task_item sti on sti.shelve_task_id = sti.shelve_task_id 
                                                and sti.sku = t.sku and  sti.status in ('PENDING','SHELVING','WAIT_SHELVE')
                                                and sti.purchase_plan_no = t.purchase_plan_no
        where  t.status = 'WAIT_SHELVE' and q.complete_date &lt;= #{thirtyMinutesAgo}
        and t.internal_box_code in
        <foreach collection="internalBoxCodeList" separator="," index="index" item="internalBoxCode" open="("
                 close=")">
            #{internalBoxCode}
        </foreach>
    </select>

</mapper>
