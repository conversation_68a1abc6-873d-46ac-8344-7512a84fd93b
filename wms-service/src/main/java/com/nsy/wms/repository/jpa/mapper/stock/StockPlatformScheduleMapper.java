package com.nsy.wms.repository.jpa.mapper.stock;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stock.StockDeliveryQtyBySupplierDeliveryBoxCodeInfo;
import com.nsy.api.wms.domain.stock.StockPlatformSchedulePageCount;
import com.nsy.api.wms.domain.stock.StockPlatformSchedulePageSearchInfo;
import com.nsy.api.wms.request.bd.PageShippingItemListRequest;
import com.nsy.api.wms.request.stock.QueryStockPlatformScheduleReceiveInfoRequest;
import com.nsy.api.wms.request.stock.StockPlatformSchedulePageSearchRequest;
import com.nsy.api.wms.request.stockin.ModifyStockPlatformScheduleRequest;
import com.nsy.api.wms.request.stockin.QtyRecordRequest;
import com.nsy.api.wms.request.stockin.StockinPlatformScheduleListRequest;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.stock.QueryStockPlatformScheduleReceiveInfoResponse;
import com.nsy.api.wms.response.stock.StockShippingItemPageList;
import com.nsy.api.wms.response.stockin.QueryAllInTransitInfoResponse;
import com.nsy.api.wms.response.stockin.QueryStockinPurchaseReportItem;
import com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse;
import com.nsy.api.wms.response.stockin.StockinPlatformScheduleListResponse;
import com.nsy.api.wms.response.stockin.StockinPlatformScheduleStatisticResponse;
import com.nsy.permission.annatation.Permission;
import com.nsy.wms.business.domain.bo.stock.StockPlatformScheduleStatisticsBo;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */

public interface StockPlatformScheduleMapper extends BaseMapper<StockPlatformScheduleEntity> {

    // 需要区域隔离
    @InterceptorIgnore(tenantLine = "true")
    @Permission
    List<StockPlatformSchedulePageSearchInfo> pageSearchShippingList(Page page, @Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);

    @InterceptorIgnore(tenantLine = "true")
    @Permission
    Integer countSearchShippingListTotal(@Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);

    @InterceptorIgnore(tenantLine = "true")
    @Permission
    StockPlatformSchedulePageCount countSearchShippingList(@Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);

    //统计箱数和发货数
    @InterceptorIgnore(tenantLine = "true")
    StatisticsByPlatformScheduleIdResponse statisticsSearchShippingTaskQty(@Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);

    //统计收货数、退货数、上架数
    @InterceptorIgnore(tenantLine = "true")
    StatisticsByPlatformScheduleIdResponse statisticsSearchShippingOrderQty(@Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);

    @InterceptorIgnore(tenantLine = "true")
    List<StockShippingItemPageList> pageSearchShippingItemList(Page page, @Param("query") PageShippingItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockShippingItemPageList> pageSearchShippingItemList(@Param("query") PageShippingItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockShippingItemPageList> pageSearchShippingItemListNoTask(Page page, @Param("query") PageShippingItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockShippingItemPageList> pageSearchShippingItemListNoTask(@Param("query") PageShippingItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockShippingItemPageList> searchShippingItemListDownLoad(@Param("query") PageShippingItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockShippingItemPageList> searchShippingItemListNoTaskDownLoad(@Param("query") PageShippingItemListRequest request);

    @Permission
    List<StatusTabResponse> getTabs(@Param("stockinStatusList") List<String> stockinStatusList);

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockDeliveryQtyBySupplierDeliveryBoxCodeInfo> queryDeliveryQtyGroupBySupplierDeliveryBoxCode(IPage iPage, @Param("request") QtyRecordRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<QueryAllInTransitInfoResponse> queryAllInTransitInfo();

    @InterceptorIgnore(tenantLine = "true")
    StockPlatformScheduleEntity findById(@Param("platformScheduleId") Integer platformScheduleId);

    IPage<StockinPlatformScheduleListResponse> pagePlatformScheduleList(IPage objectPage, @Param("request") StockinPlatformScheduleListRequest request);

    List<Integer> getPlatformIdByCondition(@Param("request") StockinPlatformScheduleListRequest request);

    StockinPlatformScheduleStatisticResponse statisticsQty(@Param("request") StockinPlatformScheduleListRequest request);

    StockinPlatformScheduleListResponse getPlatformScheduleInfo(@Param("supplierDeliveryNo") String supplierDeliveryNo);

    void updateRemarkInfo(@Param("request") ModifyStockPlatformScheduleRequest request);

    Integer countPlatformScheduleList(@Param("request") StockinPlatformScheduleListRequest request);

    String getStockNameBySupplierDeliveryNo(String supplierDeliveryNo);

    /**
     * 统计工厂发货列表 - 发货总箱数和发货总件数
     *
     * @param request  查询条件
     * @param location 仓库位置
     * @return 统计结果
     */
    @InterceptorIgnore(tenantLine = "true")
    @Permission
    StockPlatformScheduleStatisticsBo statisticsShippingBoxAndQty(@Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);

    /**
     * 统计工厂发货列表 - 收货总件数和上架总件数 退货数
     *
     * @param request  查询条件
     * @param location 仓库位置
     * @return 统计结果
     */
    @InterceptorIgnore(tenantLine = "true")
    @Permission
    StockPlatformScheduleStatisticsBo statisticsReceiveAndShelvedAndReturnQty(@Param("query") StockPlatformSchedulePageSearchRequest request, @Param("location") String location);


    @InterceptorIgnore(tenantLine = "true")
    List<QueryStockPlatformScheduleReceiveInfoResponse> queryPlatformScheduleReceiveInfo(@Param("query") QueryStockPlatformScheduleReceiveInfoRequest request);

    List<QueryStockinPurchaseReportItem> queryStockinPurchaseReport(List<String> purchasePlanNos);

    @InterceptorIgnore(tenantLine = "true")
    Integer countUncompletedOrder(@Param("supplierDeliveryNo") String supplierDeliveryNo);

    @InterceptorIgnore(tenantLine = "true")
    Integer getMaxId();

    @InterceptorIgnore(tenantLine = "true")
    List<String> listSupplierDeliveryNoBetweenIdRange(@Param("idStart") int idStart, @Param("idEnd") int idEnd);

    @InterceptorIgnore(tenantLine = "true")
    void updateStockinStatus(@Param("supplierDeliveryNos") List<String> supplierDeliveryNos, @Param("stockinStatus") String stockinStatus);

    Date getDeliveryDateBysetSupplierDeliveryNo(List<String> supplierDeliveryNos);
}
