package com.nsy.wms.business.service.bd;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.TagTypeEnum;
import com.nsy.api.wms.request.bd.BdTagInsertRequest;
import com.nsy.api.wms.request.bd.BdTagPageRequest;
import com.nsy.api.wms.request.bd.BdTagUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdTagResponse;
import com.nsy.wms.business.domain.bo.bd.BdFindEnableTagMappingBo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.bd.BdTagEntity;
import com.nsy.wms.repository.entity.bd.BdTagMappingEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdTagMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdTagMappingMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 标签表(BdTag)服务层
 *
 * <AUTHOR>
 * @since 2023-04-18 14:28:39
 */
@Service
public class BdTagService extends ServiceImpl<BdTagMapper, BdTagEntity> {

    @Resource
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    BdTagMappingMapper tagMappingMapper;
    @Resource
    ProductSpecInfoService productSpecInfoService;

    /**
     * 分页查询
     */
    public PageResponse<BdTagResponse> queryByPage(BdTagPageRequest request) {
        PageResponse<BdTagResponse> pageResponse = new PageResponse<>();
        Page<BdTagEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<BdTagEntity> iPage = page(page, buildPageQueryWrapper(request));
        Map<String, String> tagMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_BD_TAG_TYPE.getName());
        List<BdTagResponse> list = iPage.getRecords().stream().map(entity -> {
            BdTagResponse resp = new BdTagResponse();
            BeanUtils.copyProperties(entity, resp);
            resp.setTypeCn(tagMap.get(entity.getType()));
            return resp;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }

    public BdTagResponse getOneById(Integer id) {
        BdTagEntity entity = getById(id);
        Map<String, String> tagMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_BD_TAG_TYPE.getName());
        BdTagResponse resp = new BdTagResponse();
        BeanUtils.copyProperties(entity, resp);
        resp.setTypeCn(tagMap.get(entity.getType()));
        return resp;
    }

    public BdTagEntity getOneByName(String name) {
        return getOne(new LambdaQueryWrapper<BdTagEntity>()
                .eq(BdTagEntity::getName, name)
                .eq(BdTagEntity::getIsEnabled, Boolean.TRUE)
                .last("limit 1"));
    }


    // 新增
    @Transactional(rollbackFor = Exception.class)
    public void insert(BdTagInsertRequest insertRequest) {
        Map<String, String> tagMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_BD_TAG_TYPE.getName());
        // 校验参数
        validInsertParams(insertRequest, tagMap);
        BdTagEntity entity = new BdTagEntity();
        BeanUtilsEx.copyProperties(insertRequest, entity);
        entity.setLocation(TenantContext.getTenant());
        entity.setCreateBy(loginInfoService.getName());
        save(entity);
    }


    // 修改
    @Transactional(rollbackFor = Exception.class)
    public void update(BdTagUpdateRequest updateRequest) {
        BdTagEntity entity = getById(updateRequest.getTagId());
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        if (!entity.getName().equalsIgnoreCase(updateRequest.getName())) {
            // 校验相同名称
            LambdaQueryWrapper<BdTagEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BdTagEntity::getName, updateRequest.getName());
            int count = count(wrapper);
            if (count > 0) {
                throw new BusinessServiceException("标签名称重复");
            }
        }
        BeanUtilsEx.copyProperties(updateRequest, entity);
        entity.setUpdateBy(loginInfoService.getName());
        updateById(entity);
    }

    // 构造列表查询条件
    private LambdaQueryWrapper<BdTagEntity> buildPageQueryWrapper(BdTagPageRequest request) {
        LambdaQueryWrapper<BdTagEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(request.getIsEnabled() != null, BdTagEntity::getIsEnabled, request.getIsEnabled());
        wrapper.eq(request.getName() != null, BdTagEntity::getName, request.getName());
        wrapper.eq(request.getType() != null, BdTagEntity::getType, request.getType());
        wrapper.orderByDesc(BdTagEntity::getTagId);
        return wrapper;
    }

    private void validInsertParams(BdTagInsertRequest insertRequest, Map<String, String> tagMap) {
        if (!StringUtils.hasText(insertRequest.getName())) {
            throw new BusinessServiceException("请输入名称");
        }
        // 查看名称是否唯一
        LambdaQueryWrapper<BdTagEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdTagEntity::getName, insertRequest.getName());
        int count = count(wrapper);
        if (count > 0) {
            throw new BusinessServiceException("标签名称重复");
        }
        if (!tagMap.containsKey(insertRequest.getType())) {
            throw new BusinessServiceException("标签类型不存在");
        }
    }

    public BdTagResponse changeStatus(Integer id) {
        BdTagEntity entity = getById(id);
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        if (entity.getIsEnabled()) {
            entity.setIsEnabled(Boolean.FALSE);
        } else {
            entity.setIsEnabled(Boolean.TRUE);
        }
        entity.setUpdateBy(loginInfoService.getName());
        updateById(entity);
        return getOneById(id);
    }

    public List<SelectModel> findOrderTagSelectList() {
        return this.list(new LambdaQueryWrapper<BdTagEntity>()
                        .eq(BdTagEntity::getType, TagTypeEnum.ORDER.name())
                        .eq(BdTagEntity::getIsEnabled, Boolean.TRUE))
                .stream()
                .map(tag -> new SelectModel(tag.getTagId().toString(), tag.getName()))
                .collect(Collectors.toList());
    }

    public String findTag(BdFindEnableTagMappingBo bo) {
        return tagMappingMapper.findEnableList(bo)
                .stream()
                .map(BdTagMappingEntity::getTagName)
                .distinct()
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.joining(","));
    }

    public List<SelectModel> findProductTagSelectList() {
        return this.list(new LambdaQueryWrapper<BdTagEntity>()
                        .eq(BdTagEntity::getType, TagTypeEnum.PRODUCT.name())
                        .eq(BdTagEntity::getIsEnabled, Boolean.TRUE))
                .stream()
                .map(tag -> new SelectModel(tag.getTagId().toString(), tag.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 检查是否配置先进先出
     *
     * @param sku 商品SKU
     * @return 是否先进先出
     */
    public Boolean checkStockFifo(String sku) {
        // 检查SKC是否配置了先进先出标签
        BdFindEnableTagMappingBo bo = new BdFindEnableTagMappingBo(TagTypeEnum.PRODUCT.name(), sku, "先进先出");
        List<BdTagMappingEntity> tagMappings = tagMappingMapper.findEnableList(bo);

        return CollectionUtil.isNotEmpty(tagMappings);
    }
}
