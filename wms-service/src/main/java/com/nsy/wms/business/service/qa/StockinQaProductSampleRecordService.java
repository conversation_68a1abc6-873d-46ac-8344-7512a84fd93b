package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.ProductInfoConstant;
import com.nsy.api.wms.domain.qa.ProductBomAttachmentDto;
import com.nsy.api.wms.domain.qa.StockinQaProductSampleProcessDto;
import com.nsy.api.wms.domain.qa.StockinQaProductSampleRecordDto;
import com.nsy.api.wms.domain.qa.StockinQaProductSampleRecordPageDto;
import com.nsy.api.wms.domain.qc.QcInboundsItem;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdQaSopEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.qa.QaProcessEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaProductSampleRecordStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxMaterialSizeEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaTaskStatusEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.product.ProductTryOnValidRequest;
import com.nsy.api.wms.request.qa.StockinCreateQaOrderRequest;
import com.nsy.api.wms.request.qa.StockinQaProductSampleOperateRequest;
import com.nsy.api.wms.request.qa.StockinQaProductSampleRecordPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.qa.StockinCreateQaSampleRecordResponse;
import com.nsy.api.wms.response.qa.StockinQaInfoResponse;
import com.nsy.api.wms.response.qa.StockinQaProductSampleProcessResponse;
import com.nsy.api.wms.response.qa.StockinQaProductSampleRecordPageResponse;
import com.nsy.api.wms.response.qa.StockinQaProductSampleRecordProcessInfo;
import com.nsy.api.wms.response.qa.StockinQaProductSampleRecordResponse;
import com.nsy.api.wms.response.qa.StockinWaitQaTaskPageResponse;
import com.nsy.wms.business.manage.product.ProductApiService;
import com.nsy.wms.business.manage.product.response.QcRuleResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.response.ProductMainMarkTagResponse;
import com.nsy.wms.business.manage.scm.response.SchedulingOrderPlanBomDetailResponse;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.product.ProductTryOnTaskService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaProductSampleRecordEntity;
import com.nsy.wms.repository.entity.qa.StockinQaProductSampleRecordImgEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskItemEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaProductSampleRecordMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 产前样记录表业务实现
 * @date: 2024-11-13 10:09
 */
@Service
public class StockinQaProductSampleRecordService extends ServiceImpl<StockinQaProductSampleRecordMapper, StockinQaProductSampleRecordEntity> implements IDownloadService {

    @Autowired
    private StockinQaProductSampleSkuTypeService skuTypeService;
    @Autowired
    private StockinQaProductSampleRecordImgService recordImgService;
    @Autowired
    private StockinQaProductSampleRecordItemService sampleRecordItemService;
    @Autowired
    private StockinQaProductSampleRecordProcessService recordProcessService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StockinQaTaskService stockinQaTaskService;
    @Autowired
    private StockinQaTaskItemService stockinQaTaskItemService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinQaProductSampleRecordLogService recordLogService;
    @Autowired
    private BdTagMappingService bdTagMappingService;
    @Autowired
    private SupplierApiService supplierApiService;
    @Autowired
    private ProductTryOnTaskService productTryOnTaskService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private ProductApiService productApiService;
    @Autowired
    private StockinQaOrderOperateService qaOrderOperateService;

    /**
     * 获取skc上次质检完成的产前样结果
     *
     * @param skc
     * @return
     */
    public StockinQaProductSampleRecordDto getLastRecordBySkc(String skc) {
        StockinQaProductSampleRecordEntity productSampleRecordEntity = this.getOne(new LambdaUpdateWrapper<StockinQaProductSampleRecordEntity>()
                .eq(StockinQaProductSampleRecordEntity::getSkc, skc)
                .eq(StockinQaProductSampleRecordEntity::getStatus, StockinQaProductSampleRecordStatusEnum.QC_COMPLETED.name())
                .orderByDesc(StockinQaProductSampleRecordEntity::getCompleteDate)
                .last("limit 1"));
        if (Objects.isNull(productSampleRecordEntity))
            return null;

        StockinQaProductSampleRecordDto recordDto = new StockinQaProductSampleRecordDto();
        BeanUtils.copyProperties(productSampleRecordEntity, recordDto);

        List<StockinQaProductSampleRecordProcessInfo> processInfoList = recordProcessService.getProcessInfoByRecordId(productSampleRecordEntity.getId());


        Map<String, List<StockinQaProductSampleRecordImgEntity>> collect = recordImgService.list(new LambdaQueryWrapper<StockinQaProductSampleRecordImgEntity>()
                        .eq(StockinQaProductSampleRecordImgEntity::getRecordId, productSampleRecordEntity.getId()))
                .stream().collect(Collectors.groupingBy(StockinQaProductSampleRecordImgEntity::getProcessName));

        processInfoList.forEach(item -> {
            List<StockinQaProductSampleRecordImgEntity> imgEntities = collect.get(item.getProcessName());
            if (!CollectionUtils.isEmpty(imgEntities)) {
                List<String> imageList = imgEntities.stream().map(StockinQaProductSampleRecordImgEntity::getImgUrl).collect(Collectors.toList());
                item.setImgList(imageList);
            } else {
                item.setImgList(new LinkedList<>());
            }
        });
        recordDto.setQaProcessList(processInfoList);

        return recordDto;
    }

    /**
     * 产前样分页信息
     *
     * @param request
     * @return
     */
    public PageResponse<StockinQaProductSampleRecordPageResponse> pageList(StockinQaProductSampleRecordPageRequest request) {
        Page<StockinQaProductSampleRecordPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<StockinQaProductSampleRecordPageResponse> pageInfo = this.getBaseMapper().pageList(page, request);
        PageResponse<StockinQaProductSampleRecordPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(pageInfo.getTotal());
        if (CollectionUtil.isEmpty(pageInfo.getRecords())) {
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        Map<Integer, List<String>> skuTypeMap = skuTypeService.getMapByQcRoutingInspectId(pageInfo.getRecords().stream().map(StockinQaProductSampleRecordPageResponse::getId).collect(Collectors.toList()));
        Map<String, String> statusEnumMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_STOCKIN_QA_PRODUCT_SAMPLE_RECORD_STATUS.getName());
        pageInfo.getRecords().forEach(detail -> {
            detail.setResultStr(Objects.isNull(detail.getResult()) ? "" : detail.getResult() == 1 ? "合格" : "不合格");
            detail.setStatusStr(statusEnumMap.get(detail.getStatus()));
            if (CollectionUtils.isEmpty(skuTypeMap.get(detail.getId()))) {
                return;
            }
            detail.setSkuTypeStr(String.join(",", skuTypeMap.get(detail.getId())));
            detail.setSkuTypeList(skuTypeMap.get(detail.getId()));
        });
        pageResponse.setContent(pageInfo.getRecords());
        return pageResponse;
    }

    /**
     * 查询产前样详情
     *
     * @param id
     * @return
     */
    public StockinQaProductSampleRecordResponse findDetail(Integer id) {
        StockinQaProductSampleRecordResponse response = this.getBaseMapper().findDetail(id);
        if (Objects.isNull(response)) {
            throw new BusinessServiceException("查询不到相关的数据!");
        }
        //补充流程节点信息
        List<StockinQaProductSampleRecordProcessInfo> processInfoList = recordProcessService.getProcessInfoByRecordId(id);
        response.setRecordProcessList(processInfoList);
        //补充流程图片信息
        List<StockinQaProductSampleRecordImgEntity> imagList = recordImgService.getImagListByRecordId(response.getId());
        Map<String, List<String>> processMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imagList)) {
            processMap = imagList.stream().collect(Collectors.groupingBy(StockinQaProductSampleRecordImgEntity::getProcessName,
                    Collectors.mapping(StockinQaProductSampleRecordImgEntity::getImgUrl, Collectors.toList())));
        }
        //赋值流程图片信息
        for (StockinQaProductSampleRecordProcessInfo processInfo : processInfoList) {
            if (CollectionUtils.isEmpty(processMap.get(processInfo.getProcessName()))) {
                continue;
            }
            processInfo.setImgList(processMap.get(processInfo.getProcessName()));
        }
        //判断处于哪个流程
        processInfoList.stream().sorted(Comparator.comparing(StockinQaProductSampleRecordProcessInfo::getSort)).filter(
                item -> Objects.isNull(item.getStatus())).findFirst().ifPresent(item -> response.setProcessName(item.getProcessName()));
        return response;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_QA_PRODUCT_SAMPLE_RECORD_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinQaProductSampleRecordPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinQaProductSampleRecordPageRequest.class);
        // 设置每次的查询数量
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        PageResponse<StockinQaProductSampleRecordPageResponse> pageResponse = this.pageList(downloadRequest);
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getContent()));
        return response;
    }

    /**
     * 进行新款质检
     * 一、校验箱子内数据,校验是否新款
     * 二、判断是否存在产前样订单信息，校验是否存在重复质检
     * 三、获取对应的质检任务信息并生成产前样信息
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public StockinCreateQaSampleRecordResponse generateQaSampleRecord(StockinCreateQaOrderRequest request) {
        //一. 校验箱内数据
        StockInternalBoxEntity boxEntity = stockInternalBoxService.findByInternalBoxCode(request.getInternalBoxCode());
        if (Objects.isNull(boxEntity)) {
            throw new BusinessServiceException("找不到内部箱: " + request.getInternalBoxCode());
        }
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            throw new BusinessServiceException("箱子已删除");
        List<Integer> taskIds = stockInternalBoxItemService.getBaseMapper().findTaskIdByInternalBoxCode(request.getInternalBoxCode());
        if (CollectionUtils.isEmpty(taskIds))
            throw new BusinessServiceException("未找到内部箱明细！");

        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(request.getBarcode(), taskIds);
        if (Objects.isNull(specInfoEntity))
            throw new BusinessServiceException("未找到该商品！");
        List<StockInternalBoxItemEntity> boxItemEntities = stockInternalBoxItemService.getByInternalBoxCodeAndsku(boxEntity.getInternalBoxCode(), specInfoEntity.getSku());
        if (org.springframework.util.CollectionUtils.isEmpty(boxItemEntities))
            throw new BusinessServiceException("未找到内部箱明细！");
        //校验是否新款
        if (!skuTypeService.validNewOrder(request.getInternalBoxCode(), specInfoEntity.getSku())) {
            throw new BusinessServiceException("当前订单非新品首单或新款无非进行新款质检！");
        }
        //校验是否需要量高度和称重
        boolean isNeedWeight = Objects.isNull(specInfoEntity.getActualWeight()) || specInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0;
        if (isNeedWeight)
            throw new BusinessServiceException("请先完成称重,再开始质检");
        if (Objects.isNull(specInfoEntity.getPackageHeight()))
            throw new BusinessServiceException("请先完成量高度,再开始质检");
        //二. 判断是否存在产前样订单,校验重复质检
        StockinQaProductSampleRecordEntity lastRecord = this.findLastRecordByInternalBoxCodeAndSku(boxEntity.getInternalBoxCode(), specInfoEntity.getSku());
        //校验重复质检
        StockinCreateQaSampleRecordResponse response = validRepeat(lastRecord, boxItemEntities, boxEntity);
        if (Objects.nonNull(response)) {
            return response;
        }
        //三. 读取质检任务生成产前样
        StockinQaTaskEntity qaTaskEntity = stockinQaTaskService.getOne(new LambdaQueryWrapper<StockinQaTaskEntity>()
                .eq(StockinQaTaskEntity::getInternalBoxCode, boxEntity.getInternalBoxCode())
                .eq(StockinQaTaskEntity::getSku, specInfoEntity.getSku())
                .in(StockinQaTaskEntity::getCheckStatus, Lists.newArrayList(StockinQaTaskStatusEnum.PENDING_QC.name(), StockinQaTaskStatusEnum.QC_PROCESSING.name()))
                .last("limit 1"));
        if (Objects.isNull(qaTaskEntity)) {
            throw new BusinessServiceException("对应的质检任务不存在无法进行质检!");
        }
        //四. 生成产前样信息
        StockinQaProductSampleRecordEntity recordEntity = this.buildQaSampleRecords(qaTaskEntity);
        //保存明细
        sampleRecordItemService.saveRecordItemByQaTaskId(qaTaskEntity.getTaskId(), recordEntity.getId());
        //保存流程信息
        recordProcessService.saveProcessInfo(recordEntity.getId());
        //保存标签信息
        skuTypeService.saveLabelInfo(request.getInternalBoxCode(), specInfoEntity.getSku(), recordEntity.getId());
        //保存操作日志
        recordLogService.addLog(recordEntity.getId(), "商品无误开始质检", "操作商品无误开始质检");
        response = new StockinCreateQaSampleRecordResponse();
        response.setRecordId(recordEntity.getId());
        return response;
    }

    /**
     * 根据内部箱和sku查询产前样信息
     *
     * @param internalBoxCode
     * @param sku
     * @return
     */
    public StockinQaProductSampleRecordEntity findLastRecordByInternalBoxCodeAndSku(String internalBoxCode, String sku) {
        return this.getOne(new LambdaQueryWrapper<StockinQaProductSampleRecordEntity>()
                .eq(StockinQaProductSampleRecordEntity::getSku, sku)
                .eq(StockinQaProductSampleRecordEntity::getInternalBoxCode, internalBoxCode)
                .orderByDesc(StockinQaProductSampleRecordEntity::getId)
                .last("limit 1"));
    }

    /**
     * 校验是否存在对应的产前样信息
     *
     * @param lastRecord
     * @param boxItemList
     * @param boxEntity
     * @return
     */
    private StockinCreateQaSampleRecordResponse validRepeat(StockinQaProductSampleRecordEntity lastRecord, List<StockInternalBoxItemEntity> boxItemList, StockInternalBoxEntity boxEntity) {
        if (Objects.isNull(lastRecord)) {
            return null;
        }
        //质检中则判断是否为同一个操作人
        if (StockinQaProductSampleRecordStatusEnum.QC_PROCESSING.name().equals(lastRecord.getStatus())) {
            StockinCreateQaSampleRecordResponse response = new StockinCreateQaSampleRecordResponse();
            response.setRecordId(lastRecord.getId());
            //登入人是否为报告的操作人
            if (!loginInfoService.getName().equalsIgnoreCase(lastRecord.getOperator())) {
                response.setIsNotice(1);
                response.setNotice(String.format("质检任务已被%s领取，是否继续操作？", lastRecord.getOperator()));
            }
            return response;
        }
        if (StockInternalBoxMaterialSizeEnum.SINGLE_USE_BOX.name().equals(boxEntity.getInternalBoxMaterialSize())) {
            // 一次性箱,相同sku不可重复质检
            throw new BusinessServiceException("该箱子的该款已经质检过了！");
        }

        //循环箱需判断质检单与箱内单据是否相同，相同则提示不可重复质检 ， 不相同则可继续质检
        if (StockInternalBoxMaterialSizeEnum.LOOP_BOX.name().equals(boxEntity.getInternalBoxMaterialSize())) {
            Set<String> stockinOrderNoSet = sampleRecordItemService.findStockinOrderNoByOrderId(lastRecord.getId());
            Set<String> collect = boxItemList.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().collect(Collectors.toSet());
            if (stockinOrderNoSet.size() == collect.size() && stockinOrderNoSet.containsAll(collect)) {
                throw new BusinessServiceException("该箱子的该款已经质检过了！");
            }
        }
        return null;
    }

    /**
     * 组装产前样表头信息
     *
     * @param qaTaskEntity
     * @return
     */
    private StockinQaProductSampleRecordEntity buildQaSampleRecords(StockinQaTaskEntity qaTaskEntity) {
        StockinQaProductSampleRecordEntity recordEntity = new StockinQaProductSampleRecordEntity();
        BeanUtils.copyProperties(qaTaskEntity, recordEntity, "id", "createDate", "createBy", "updateDate", "updateBy", "version");
        recordEntity.setStatus(StockinQaProductSampleRecordStatusEnum.QC_PROCESSING.name());
        recordEntity.setOperator(loginInfoService.getName());
        recordEntity.setCreateBy(loginInfoService.getName());
        recordEntity.setUpdateBy(loginInfoService.getName());
        this.save(recordEntity);
        return recordEntity;
    }

    /**
     * 查询待质检的产前样列表
     *
     * @param internalBoxCode
     * @return
     */
    public List<StockinWaitQaTaskPageResponse> queryWaitQaTask(String internalBoxCode) {
        StockInternalBoxEntity boxEntity = stockInternalBoxService.findByInternalBoxCode(internalBoxCode);
        if (Objects.isNull(boxEntity)) {
            throw new BusinessServiceException("找不到内部箱: " + internalBoxCode);
        }
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            throw new BusinessServiceException("箱子已删除");
        //查询对应内部箱的质检任务信息,并过滤掉对应的非新款得到数据
        List<StockinWaitQaTaskPageResponse> taskEntityList = stockinQaTaskService.getWaitQaTaskExcludeNotNew(internalBoxCode);
        if (CollectionUtils.isEmpty(taskEntityList)) {
            return Collections.emptyList();
        }
        //组装商品标签
        List<String> skuList = taskEntityList.stream().map(StockinWaitQaTaskPageResponse::getSku).collect(Collectors.toList());
        Map<String, List<String>> skuTagMap = bdTagMappingService.getProductTagBySkus(skuList);
        //获取对应明细的包装、品牌信息
        List<Integer> taskIds = taskEntityList.stream().map(StockinWaitQaTaskPageResponse::getTaskId).collect(Collectors.toList());
        Map<Integer, List<StockinQaTaskItemEntity>> itemMap = stockinQaTaskItemService.list(new LambdaQueryWrapper<StockinQaTaskItemEntity>()
                        .select(StockinQaTaskItemEntity::getQty, StockinQaTaskItemEntity::getPackageName,
                                StockinQaTaskItemEntity::getTaskId, StockinQaTaskItemEntity::getBrandName)
                        .in(StockinQaTaskItemEntity::getTaskId, taskIds))
                .stream().collect(Collectors.groupingBy(StockinQaTaskItemEntity::getTaskId));
        taskEntityList.stream().forEach(response -> {
            List<StockinQaTaskItemEntity> taskItemEntityList = itemMap.get(response.getTaskId());
            Map<String, List<StockinQaTaskItemEntity>> collect = taskItemEntityList.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaTaskItemEntity::getPackageName));
            //根据包装方式分组汇总数量展示信息
            response.setPackageName(collect.entrySet().stream().map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue().stream().mapToInt(StockinQaTaskItemEntity::getQty).sum())).collect(Collectors.joining(",")));
            response.setBrandName(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getBrandName).filter(StringUtils::hasText).findAny().orElse(""));
            response.setFirstLabel(taskItemEntityList.stream().map(StockinQaTaskItemEntity::getFirstLabel).filter(StringUtils::hasText).findAny().orElse(""));
            response.setProductTag(skuTagMap.get(response.getSku()));
            //查找对应spu上一次质检操作人员
            StockinQaProductSampleRecordEntity orderEntity = this.findLastCompletedRecordBySpu(response.getSpu());
            response.setBoxQty(taskItemEntityList.stream().mapToInt(StockinQaTaskItemEntity::getQty).sum());
            response.setIsReturnOrder(Objects.nonNull(response.getPurchasingApplyType()) && response.getPurchasingApplyType().equals(6) ? 1 : 0);

            if (Objects.nonNull(orderEntity))
                response.setPreviousQaUserRealName(orderEntity.getOperator());
            //重新校验重量、高度是否需要测量，如果需要有同款尺码就赋值一个同款的重量、高度
            qaOrderOperateService.checkSkuNeedWeightAndNeedHeight(response);
        });
        return taskEntityList;
    }

    /**
     * 根据spu查询上一次质检完成信息
     *
     * @param spu
     * @return
     */
    public StockinQaProductSampleRecordEntity findLastCompletedRecordBySpu(String spu) {
        return this.getOne(new LambdaQueryWrapper<StockinQaProductSampleRecordEntity>()
                .eq(StockinQaProductSampleRecordEntity::getSpu, spu)
                .eq(StockinQaProductSampleRecordEntity::getStatus, StockinQaProductSampleRecordStatusEnum.QC_COMPLETED.name())
                .orderByDesc(StockinQaProductSampleRecordEntity::getId)
                .last("limit 1"));
    }

    /**
     * 产前样操作流程节点
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOperate(StockinQaProductSampleOperateRequest request) {
        StockinQaProductSampleRecordEntity recordEntity = this.getById(request.getRecordId());
        if (Objects.isNull(recordEntity)) {
            throw new BusinessServiceException("查询不到对应质检信息!");
        }
        recordEntity.setOperator(loginInfoService.getName());
        recordEntity.setUpdateBy(loginInfoService.getName());
        String logMsg = String.format("%s%s", request.getProcessName(), QaProcessEnum.getValueByName(request.getStatus()));
        //如果为质检结果则为最后一个步骤
        if (BdQaSopEnum.QC_RESULT.getProcessName().equals(request.getProcessName())) {
            if (QaProcessEnum.QUALIFIED.name().equalsIgnoreCase(request.getStatus())) {
                recordEntity.setResult(1);
                logMsg = String.format("%s%s", request.getProcessName(), QaProcessEnum.getValueByName(request.getStatus()));
            } else if (QaProcessEnum.UNQUALIFIED.name().equalsIgnoreCase(request.getStatus())) {
                //不合格这赋值对应的不合格原因
                recordEntity.setResult(0);
                recordEntity.setUnqualifiedCategory(request.getUnqualifiedCategory());
                recordEntity.setUnqualifiedQuestion(request.getUnqualifiedQuestion());
                recordEntity.setUnqualifiedReason(request.getUnqualifiedReason());
                logMsg = String.format("%s%s;,不合格原因：%s;不合格原因分类:%s;不合格原因说明：%s", request.getProcessName(), QaProcessEnum.getValueByName(request.getStatus()),
                        request.getUnqualifiedReason(), request.getUnqualifiedCategory(), request.getUnqualifiedQuestion());
            }
            //修改为质检完成
            recordEntity.setStatus(StockinQaProductSampleRecordStatusEnum.QC_COMPLETED.name());
            recordEntity.setCompleteDate(new Date());
        } else {
            //更新流程节点信息
            recordProcessService.updateProcessInfo(request);
            //保存图片信息
            recordImgService.saveImageList(request.getRecordId(), request.getProcessName(), request.getImgList());
        }
        //保存操作日志
        recordLogService.addLog(request.getRecordId(), request.getProcessName(), logMsg);
        this.updateById(recordEntity);
        //同步旧质检系统
        if (StockinQaProductSampleRecordStatusEnum.QC_COMPLETED.name().equalsIgnoreCase(recordEntity.getStatus())) {
            supplierApiService.syncQaProductSample(recordEntity.getInternalBoxCode(), recordEntity.getSku(), recordEntity.getResult());
        }
    }

    /**
     * 获取质检操作中信息区数据
     *
     * @param recordId
     * @return
     */
    public StockinQaInfoResponse getQaInfo(Integer recordId) {
        StockinQaProductSampleRecordEntity sampleRecordEntity = this.getById(recordId);
        if (Objects.isNull(sampleRecordEntity))
            throw new BusinessServiceException("未找到质检单");
        List<StockinQaTaskItemEntity> itemEntityList = stockinQaTaskItemService.getTaskItemByTaskId(sampleRecordEntity.getTaskId());
        StockinQaTaskEntity qaTaskEntity = stockinQaTaskService.getById(sampleRecordEntity.getTaskId());
        //组装商品信息
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopBySku(sampleRecordEntity.getSku());
        StockinQaInfoResponse.ProductBaseInfo productBaseInfo = new StockinQaInfoResponse.ProductBaseInfo();
        BeanUtils.copyProperties(qaTaskEntity, productBaseInfo);
        productBaseInfo.setBrandName(itemEntityList.stream().map(StockinQaTaskItemEntity::getBrandName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        productBaseInfo.setPurchasePlanNo(itemEntityList.stream().map(StockinQaTaskItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        productBaseInfo.setSupplierDeliveryNo(itemEntityList.stream().map(StockinQaTaskItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
        productBaseInfo.setColor(specInfoEntity.getColor());
        productBaseInfo.setSize(specInfoEntity.getSize());
        productBaseInfo.setBoxQty(itemEntityList.stream().mapToInt(StockinQaTaskItemEntity::getQty).sum());
        //赋值采购员、部门
        SupplierEntity supplierInfo = supplierService.getBySupplierId(qaTaskEntity.getSupplierId());
        productBaseInfo.setPurchaseUserName(supplierInfo.getContactPurchaserEmpName());
        productBaseInfo.setDepartment(supplierInfo.getAffiliateDeptName());
        productBaseInfo.setApplyDepartment(stockInternalBoxItemService.getBusinessType(sampleRecordEntity.getInternalBoxCode(), sampleRecordEntity.getSku()));
        //商品是否需要试穿
        ProductTryOnValidRequest productTryOnValidRequest = new ProductTryOnValidRequest();
        productTryOnValidRequest.setSku(sampleRecordEntity.getSku());
        productBaseInfo.setTryOn(productTryOnTaskService.validIsTryOn(productTryOnValidRequest));
        //包装信息
        Map<String, List<StockinQaTaskItemEntity>> collect = itemEntityList.stream().filter(item -> com.nsy.api.core.apicore.util.StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaTaskItemEntity::getPackageName));
        productBaseInfo.setPackageName(collect.entrySet().stream().map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue().stream().mapToInt(StockinQaTaskItemEntity::getQty).sum())).collect(Collectors.joining(",")));

        //获取商品对应的面料信息
        ProductInfoEntity productInfoEntity = productInfoService.findTopByProductId(sampleRecordEntity.getProductId());
        StockinQaInfoResponse.DesignInfo designInfo = new StockinQaInfoResponse.DesignInfo();
        designInfo.setPackageSize(productInfoEntity.getPackageSize());
        //工艺版本
        List<StockInternalBoxItemEntity> boxItemEntities = stockInternalBoxItemService.getByInternalBoxCodeAndsku(sampleRecordEntity.getInternalBoxCode(), sampleRecordEntity.getSku());
        if (CollectionUtil.isNotEmpty(boxItemEntities)) {
            designInfo.setWorkmanshipVersion(boxItemEntities.get(0).getWorkmanshipVersion());
        }
        designInfo.setFabricType(productInfoEntity.getFabricType());
        designInfo.setFabricTypeEn(productInfoEntity.getFabricTypeEn());
        designInfo.setPackageName(itemEntityList.stream().map(StockinQaTaskItemEntity::getPackageName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        designInfo.setWashLabel(productInfoEntity.getWashLabel());
        //获取商品主唛吊牌信息
        List<ProductMainMarkTagResponse> productMainMarkTagResponses = scmApiService.queryProductMainMarkTagByProductId(sampleRecordEntity.getProductId());
        if (!org.springframework.util.CollectionUtils.isEmpty(productMainMarkTagResponses))
            designInfo.setBdTagDto(productMainMarkTagResponses.get(0).getBdTagDto());
        StockinQaInfoResponse response = new StockinQaInfoResponse();
        response.setBaseInfo(productBaseInfo);
        //如果没有bom信息则不赋值
        SchedulingOrderPlanBomDetailResponse bomDetail = scmApiService.getSchedulingOrderPlanBomDetail(sampleRecordEntity.getProductId(), sampleRecordEntity.getSkc());
        if (!Objects.isNull(bomDetail)) {
            //审版意见单
            List<ProductBomAttachmentDto> reviewFeedbackAttachList = bomDetail.getProductBomAttachmentDtoList()
                    .stream().filter(dto -> "审版意见单".equals(dto.getAttachmentType())).collect(Collectors.toList());
            designInfo.setReviewFeedbackAttachList(reviewFeedbackAttachList);
            designInfo.setProductBomAttachmentDtoList(bomDetail.getProductBomAttachmentDtoList());

            bomDetail.getProductSkcBomMaterialOfferingDtoList().forEach(item -> {
                item.setPreviewImageUrl(item.getImgUrl() + ProductInfoConstant.PRODUCT_INFO_PREVIEW_IMAGE_URL);
                item.setThumbnailImageUrl(item.getImgUrl() + ProductInfoConstant.PRODUCT_INFO_THUMBNAIL_IMAGE_URL);
            });

            response.setProductSkcBomMaterialOfferingDtoList(bomDetail.getProductSkcBomMaterialOfferingDtoList());
        }
        response.setDesignInfo(designInfo);
        return response;
    }

    /**
     * 流程节点信息
     *
     * @param recordId
     * @return
     */
    public StockinQaProductSampleProcessResponse getProcess(Integer recordId) {
        StockinQaProductSampleRecordEntity recordEntity = this.getById(recordId);
        if (Objects.isNull(recordEntity))
            throw new BusinessServiceException("未找到产前样质检单");
        List<StockinQaProductSampleRecordProcessInfo> processEntityList = recordProcessService.getProcessInfoByRecordId(recordId);
        //补充流程图片信息
        List<StockinQaProductSampleRecordImgEntity> imagList = recordImgService.getImagListByRecordId(recordId);
        //补充流程节点所需信息
        List<StockinQaProductSampleProcessDto> processDtoList = processEntityList.stream().map(item -> {
            StockinQaProductSampleProcessDto dto = new StockinQaProductSampleProcessDto();
            BeanUtils.copyProperties(item, dto);
            dto.setStatusStr(QaProcessEnum.getValueByName(dto.getStatus()));
            //每个流程需要赋值特有的质检参数 （对色图、质检要点等等）
            buildProcessParam(dto, recordEntity, imagList);
            dto.setIsComplete(Objects.nonNull(item.getStatus()) ? 1 : 0);
            return dto;
        }).collect(Collectors.toList());

        StockinQaProductSampleProcessResponse response = new StockinQaProductSampleProcessResponse();
        response.setProcessList(processDtoList);
        //判断处于哪个流程
        processDtoList.stream().sorted(Comparator.comparing(StockinQaProductSampleProcessDto::getSort)).filter(
                item -> Objects.isNull(item.getStatus())).findFirst().ifPresent(item -> response.setProcessName(item.getProcessName()));
        return response;
    }

    /**
     * 构造sop质检参数
     *
     * @param dto
     * @param recordEntity
     */
    private void buildProcessParam(StockinQaProductSampleProcessDto dto, StockinQaProductSampleRecordEntity recordEntity, List<StockinQaProductSampleRecordImgEntity> imagList) {
        BdQaSopEnum sopEnum = BdQaSopEnum.getEnumByStatusOrName(dto.getProcessName());
        if (CollectionUtils.isNotEmpty(imagList)) {
            dto.setImgList(imagList.stream().filter(item -> item.getProcessName().equals(sopEnum.getProcessName())).map(StockinQaProductSampleRecordImgEntity::getImgUrl).collect(Collectors.toList()));
        } else {
            dto.setImgList(Collections.emptyList());
        }
        switch (sopEnum) {
            case PRODUCT_CHECK:
                //对色图信息
                dto.setProductSpecInfo(productInfoService.findInboundsInfoFromProduct(recordEntity.getSku()));
                break;
            case QC_ITEMS:
                //工艺尺寸信息
                QcRuleResponse qcRuleResponse = productApiService.findQcRuleFromProduct(recordEntity.getProductId());
                if (Objects.nonNull(qcRuleResponse) && !CollectionUtils.isEmpty(qcRuleResponse.getQcRuleList())) {
                    List<QcInboundsItem> collect = qcRuleResponse.getQcRuleList().stream().map(rule -> {
                        QcInboundsItem qcInboundsItem = new QcInboundsItem();
                        qcInboundsItem.setPart(rule.getPart());
                        qcInboundsItem.setDescription(rule.getDescription());
                        return qcInboundsItem;
                    }).collect(Collectors.toList());
                    dto.setQcInboundsItemList(collect);
                }
                break;
            default:
                break;
        }

    }

    /**
     * 根据质检任务id获取信息
     *
     * @param taskIdList
     * @return
     */
    public List<StockinQaProductSampleRecordPageDto> getSamplerInfoByTaskId(List<Integer> taskIdList) {
        return this.getBaseMapper().getSamplerInfoByTaskId(taskIdList);
    }
}
