package com.nsy.wms.controller.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentDetailResult;
import com.nsy.api.wms.request.base.PageRequest;
import com.nsy.api.wms.request.stockout.DeclareDocumentDownloadCallbackRequest;
import com.nsy.api.wms.request.stockout.DeclareDocumentItemWeightChangeRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentAggregatedSkuQtyPageRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentItemUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.CustomsPrintListResponse;
import com.nsy.api.wms.response.stockout.DeclareDocumentItemWeightChangeResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomDeclareDocumentItemSkuQtyResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomDeclareDocumentItemTotalResponse;
import com.nsy.wms.business.service.download.stockout.StockoutCustomsDocumentDownloadService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareDocumentAggregatedItemService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareDocumentItemService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareDocumentService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/23
 */
@RestController
@Api(tags = "报关单据制作相关接口")
public class StockoutCustomsDeclareDocumentItemController extends BaseController {
    @Autowired
    StockoutCustomsDeclareDocumentItemService stockoutCustomsDeclareDocumentItemService;
    @Resource
    StockoutCustomsDocumentDownloadService stockoutCustomsDocumentDownloadService;
    @Autowired
    StockoutCustomsDeclareDocumentService stockoutCustomsDeclareDocumentService;
    @Resource
    StockoutCustomsDeclareDocumentAggregatedItemService aggregatedItemService;


    @ApiOperation(value = "查询报关单据详情", notes = "查询报关单据详情", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-detail/{declareDocumentId}", method = RequestMethod.GET)
    public StockoutCustomsDeclareDocumentDetailResult getStockoutCustomsDeclareDocumentDetail(@PathVariable Integer declareDocumentId) {
        return stockoutCustomsDeclareDocumentItemService.getStockoutCustomsDeclareDocumentDetail(declareDocumentId);
    }

    @ApiOperation(value = "查询报关单据明细分页", notes = "查询报关单据明细分页", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-item-page/{declareDocumentId}", method = RequestMethod.POST)
    public PageResponse<StockoutCustomsDeclareDocumentAggregatedItemResult> getStockoutCustomsDeclareDocumentItemPage(@PathVariable Integer declareDocumentId, @RequestBody PageRequest request) {
        return aggregatedItemService.getAggregatedItemResultPage(declareDocumentId, request);
    }

    @ApiOperation(value = "查询报关单据明细总计", notes = "查询报关单据明细总计", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-item-total/{declareDocumentId}", method = RequestMethod.GET)
    public StockoutCustomDeclareDocumentItemTotalResponse getTotalValues(@PathVariable Integer declareDocumentId) {
        return stockoutCustomsDeclareDocumentItemService.getTotalValues(declareDocumentId);
    }

    @ApiOperation(value = "修改报关单据详情", notes = "修改报关单据详情", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document/update", method = RequestMethod.PUT)
    public void updateStockoutCustomsDeclareDocument(@Valid @RequestBody StockoutCustomsDeclareDocumentUpdateRequest request) {
        StockoutCustomsDeclareDocumentEntity entity = stockoutCustomsDeclareDocumentService.getById(request.getDeclareDocumentId());
        if (Objects.isNull(entity) || IsDeletedConstant.DELETED.equals(entity.getIsDeleted()))
            throw new BusinessServiceException("修改的报关单据不存在");
        request.setOldCurrency(entity.getCurrency());
        stockoutCustomsDeclareDocumentItemService.updateStockoutCustomsDeclareDocument(request);
    }


    @ApiOperation(value = "修改报关单据明细列表", notes = "修改报关单据明细列表", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-item/update", method = RequestMethod.PUT)
    public void updateStockoutCustomsDeclareDocumentItem(@Valid @RequestBody StockoutCustomsDeclareDocumentItemUpdateRequest request) {
        stockoutCustomsDeclareDocumentItemService.updateStockoutCustomsDeclareDocumentItem(request);
    }

    @ApiOperation(value = "单据预览", notes = "单据预览", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-preview/{declareDocumentId}", method = RequestMethod.GET)
    public String previewDocument(@PathVariable Integer declareDocumentId, @RequestParam String type) {
        return stockoutCustomsDeclareDocumentItemService.generateDocument(declareDocumentId, type);
    }

    @ApiOperation(value = "单据打印", notes = "单据打印", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-print/{declareDocumentId}", method = RequestMethod.GET)
    public CustomsPrintListResponse printDocument(@PathVariable Integer declareDocumentId, @RequestParam String type) {
        return stockoutCustomsDeclareDocumentItemService.printDocument(declareDocumentId, type);
    }

    @ApiOperation(value = "重置数据", notes = "重置数据", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-item-reset/{declareDocumentId}", method = RequestMethod.PUT)
    public void resetItem(@PathVariable Integer declareDocumentId) {
        stockoutCustomsDeclareDocumentItemService.resetItem(declareDocumentId);
    }

    @ApiOperation(value = "单件重量修改时获取其他值", notes = "单件重量修改时获取其他值", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-item/change-weight", method = RequestMethod.POST)
    public DeclareDocumentItemWeightChangeResponse getInfoWhenWeightChanged(@Valid @RequestBody DeclareDocumentItemWeightChangeRequest request) {
        return stockoutCustomsDeclareDocumentItemService.getInfoWhenWeightChanged(request);
    }

    @ApiOperation(value = "单据导出回调", notes = "单据导出回调", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document/download-callback", method = RequestMethod.PUT)
    public void downloadCallback(@RequestBody DeclareDocumentDownloadCallbackRequest request) {
        stockoutCustomsDocumentDownloadService.downloadCallback(request);
    }


    @ApiOperation(value = "报关单据聚合明细skuQty分页展示", notes = "点击报关单据项进入明细", produces = "application/json")
    @RequestMapping(value = "/stockout-customs-declare-document-aggregated-sku-qty-page", method = RequestMethod.POST)
    public PageResponse<StockoutCustomDeclareDocumentItemSkuQtyResponse> getDeclareDocumentAggregatedSkuQtyPage(@RequestBody StockoutCustomsDeclareDocumentAggregatedSkuQtyPageRequest request) {
        return aggregatedItemService.getDeclareDocumentAggregatedSkuQtyPage(request);
    }
}
