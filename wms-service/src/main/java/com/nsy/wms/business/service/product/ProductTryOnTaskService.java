package com.nsy.wms.business.service.product;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.activiti.dto.request.AssignTaskRequest;
import com.nsy.api.activiti.dto.request.ClaimTaskRequest;
import com.nsy.api.activiti.dto.request.CompleteTaskRequest;
import com.nsy.api.activiti.dto.request.MyTaskQueryRequest;
import com.nsy.api.activiti.dto.request.StartProcessRequest;
import com.nsy.api.activiti.dto.response.TaskResponse;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.product.ProductInfo;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.product.ProductTryOnTaskAttachmentInfo;
import com.nsy.api.wms.domain.product.ProductTryOnTaskDetailInfo;
import com.nsy.api.wms.domain.product.ProductTryOnTaskPageResponse;
import com.nsy.api.wms.enumeration.ActivitiBusinessKeyPrefixEnum;
import com.nsy.api.wms.enumeration.ActivitiGeneralOperationEventEnum;
import com.nsy.api.wms.enumeration.ActivitiProcessDefinitionKeyEnum;
import com.nsy.api.wms.enumeration.AuditResult;
import com.nsy.api.wms.enumeration.FlowTaskDefinitionEnum;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.product.ProductTryOnTaskAssignRequest;
import com.nsy.api.wms.request.product.ProductTryOnTaskAuditRequest;
import com.nsy.api.wms.request.product.ProductTryOnTaskPageRequest;
import com.nsy.api.wms.request.product.ProductTryOnTaskSaveRequest;
import com.nsy.api.wms.request.product.ProductTryOnValidRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.business.manage.activiti.FlowOperateService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.product.ProductTryOnTaskEntity;
import com.nsy.wms.repository.jpa.mapper.product.ProductTryOnTaskMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/9 9:45
 */
@Service
public class ProductTryOnTaskService extends ServiceImpl<ProductTryOnTaskMapper, ProductTryOnTaskEntity> implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductTryOnTaskService.class);

    @Autowired
    private FlowOperateService flowOperateService;
    @Autowired
    private ProductTryOnTaskAttachmentService attachmentService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private ProductTryOnTaskItemService productTryOnTaskItemService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ProductTryOnTaskLogService tryOnTaskLogService;
    @Autowired
    private ProductInfoService productInfoService;

    public PageResponse<ProductTryOnTaskPageResponse> pageList(ProductTryOnTaskPageRequest request) {
        IPage page = new Page(request.getPageIndex(), request.getPageSize());
        IPage<ProductTryOnTaskPageResponse> pageList = this.getBaseMapper().pageList(page, request);
        PageResponse<ProductTryOnTaskPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }


    public ProductTryOnTaskDetailInfo getDetailInfo(Integer taskId) {
        ProductTryOnTaskEntity tryOnTaskEntity = this.getOne(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .eq(ProductTryOnTaskEntity::getTaskId, taskId));
        if (Objects.isNull(tryOnTaskEntity)) {
            throw new BusinessServiceException("查询不到对应的试穿任务信息!");
        }
        ProductTryOnTaskDetailInfo taskDetailInfo = new ProductTryOnTaskDetailInfo();
        BeanUtils.copyProperties(tryOnTaskEntity, taskDetailInfo);
        List<ProductTryOnTaskAttachmentInfo> attachmentInfoList = attachmentService.getTaskAttachmentInfoList(taskId);
        taskDetailInfo.setAttachmentInfoList(attachmentInfoList);
        return taskDetailInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void audit(ProductTryOnTaskAuditRequest request) {
        ProductTryOnTaskEntity tryOnTaskEntity = this.getOne(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .eq(ProductTryOnTaskEntity::getTaskId, request.getTaskId()));
        if (Objects.isNull(tryOnTaskEntity)) {
            throw new BusinessServiceException("查询不到对应的试穿任务信息!");
        }
        if (1 == tryOnTaskEntity.getStatus()) {
            //更新信息,已审核只处理意见和附件
            tryOnTaskEntity.setSuggestions(request.getSuggestions());
            tryOnTaskEntity.setOperator(loginInfoService.getName());
            tryOnTaskEntity.setOperateDate(new Date());
            tryOnTaskEntity.setStatus(1);
            this.updateById(tryOnTaskEntity);
            //保存附件
            attachmentService.saveAttachmentInfoList(request.getAttachmentInfoList(), request.getTaskId());
            if (CollectionUtils.isNotEmpty(request.getAttachmentInfoList())) {
                tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "上传试穿图片", "上传试穿图片");
            }
            if (StringUtils.hasText(request.getSuggestions())) {
                tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "填写试穿意见", "填写试穿意见");
            }
            return;
        }
        String businessKey = flowOperateService.genBusinessKey(request.getTaskId(), ActivitiBusinessKeyPrefixEnum.TRYON);
        MyTaskQueryRequest taskQueryRequest = new MyTaskQueryRequest();
        taskQueryRequest.setBusinessKeys(Collections.singletonList(businessKey));
        List<TaskResponse> taskResponseList = flowOperateService.queryTasksByBusinessKeys(taskQueryRequest);
        if (CollectionUtils.isEmpty(taskResponseList)) {
            throw new BusinessServiceException("任务工作流不存在");
        }
        LOGGER.info("试穿工作流信息为:{},当前操作人为:{}", JsonMapper.toJson(taskResponseList), loginInfoService.getName());
        TaskResponse taskResponse = taskResponseList.get(0);
        if (!StringUtils.hasText(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException("当前任务未领用请先领用任务");
        }
        if (!loginInfoService.getName().equalsIgnoreCase(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException(String.format("当前任务属于%s,无法进行操作", taskResponse.getAssigneeName()));
        }
        //更新信息
        tryOnTaskEntity.setSuggestions(request.getSuggestions());
        tryOnTaskEntity.setOperator(loginInfoService.getName());
        tryOnTaskEntity.setOperateDate(new Date());
        tryOnTaskEntity.setStatus(1);
        this.updateById(tryOnTaskEntity);
        //保存附件
        attachmentService.saveAttachmentInfoList(request.getAttachmentInfoList(), request.getTaskId());
        if (CollectionUtils.isNotEmpty(request.getAttachmentInfoList())) {
            tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "上传试穿图片", "上传试穿图片");
        }
        if (StringUtils.hasText(request.getSuggestions())) {
            tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "填写试穿意见", "填写试穿意见");
        }
        //工作流审核
        Map<String, Object> variables = new HashMap<>(4);
        variables.put("auditResult", AuditResult.PASS.getName());
        flowOperateService.completeTaskByTaskId(taskResponse.getTaskId(), variables);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAudit(ProductTryOnTaskAuditRequest request) {
        List<SysUserInfo> userInfoList = this.checkReassignmentGetUsers();
        if (CollectionUtils.isEmpty(userInfoList) || !userInfoList.stream().filter(detail -> loginInfoService.getName().equalsIgnoreCase(detail.getUserName())).findAny().isPresent()) {
            throw new BusinessServiceException("当前用户无处理权限请检查!");
        }
        List<ProductTryOnTaskEntity> taskList = this.list(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .in(ProductTryOnTaskEntity::getTaskId, request.getTaskIdList()));
        if (CollectionUtils.isEmpty(taskList)) {
            throw new BusinessServiceException("查询不到对应的试穿任务信息!");
        }
        //工作流审核
        Map<String, Object> variables = new HashMap<>(4);
        variables.put("auditResult", AuditResult.PASS.getName());
        List<CompleteTaskRequest> completeTaskRequestList = new ArrayList<>(taskList.size());
        for (ProductTryOnTaskEntity detail : taskList) {
            //更新信息,已审核只处理意见和附件
            detail.setSuggestions(request.getSuggestions());
            detail.setOperator(loginInfoService.getName());
            detail.setOperateDate(new Date());
            if (CollectionUtils.isNotEmpty(request.getAttachmentInfoList())) {
                tryOnTaskLogService.saveLogInfo(detail.getTaskId(), "上传试穿图片", "上传试穿图片");
            }
            if (StringUtils.hasText(request.getSuggestions())) {
                tryOnTaskLogService.saveLogInfo(detail.getTaskId(), "填写试穿意见", "填写试穿意见");
            }
            //保存附件
            attachmentService.saveAttachmentInfoList(request.getAttachmentInfoList(), detail.getTaskId());
            if (1 == detail.getStatus()) {
                continue;
            }
            detail.setStatus(1);
            //组装工作流参数
            CompleteTaskRequest completeTaskRequest = new CompleteTaskRequest();
            completeTaskRequest.setBusinessKey(flowOperateService.genBusinessKey(detail.getTaskId(), ActivitiBusinessKeyPrefixEnum.TRYON));
            completeTaskRequest.setAssignee(loginInfoService.getUserName());
            completeTaskRequest.setLocalVariable(true);
            completeTaskRequest.setOperateBy(loginInfoService.getUserName());
            completeTaskRequest.setVariables(variables);
            completeTaskRequest.setTaskName(FlowTaskDefinitionEnum.TRY_ON_INITIATE.getTaskName());
            completeTaskRequest.setTaskDefinitionKey(FlowTaskDefinitionEnum.TRY_ON_INITIATE.getTaskDefinitionKey());
            completeTaskRequestList.add(completeTaskRequest);
        }
        this.updateBatchById(taskList);
        flowOperateService.batchCompleteByBusinessKeyList(completeTaskRequestList);
    }

    public void claimTask(Integer taskId) {
        ProductTryOnTaskEntity tryOnTaskEntity = this.getOne(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .eq(ProductTryOnTaskEntity::getTaskId, taskId));
        if (Objects.isNull(tryOnTaskEntity)) {
            throw new BusinessServiceException("查询不到对应的试穿任务信息!");
        }
        if (1 == tryOnTaskEntity.getStatus()) {
            throw new BusinessServiceException("当前试穿任务已审核!");
        }
        String businessKey = flowOperateService.genBusinessKey(taskId, ActivitiBusinessKeyPrefixEnum.TRYON);
        MyTaskQueryRequest taskQueryRequest = new MyTaskQueryRequest();
        taskQueryRequest.setBusinessKeys(Collections.singletonList(businessKey));
        List<TaskResponse> taskResponseList = flowOperateService.queryTasksByBusinessKeys(taskQueryRequest);
        if (CollectionUtils.isEmpty(taskResponseList)) {
            throw new BusinessServiceException("任务工作流不存在");
        }
        TaskResponse taskResponse = taskResponseList.get(0);
        if (StringUtils.hasText(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException(String.format("任务已被%s,无法重复领取", taskResponse.getAssigneeName()));
        }
        tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "领取", "领取试穿任务");
        ClaimTaskRequest waitPickTaskRequest = new ClaimTaskRequest();
        waitPickTaskRequest.setClaimUser(loginInfoService.getUserName());
        waitPickTaskRequest.setBusinessKey(Collections.singletonList(taskResponse.getBusinessKey()));
        waitPickTaskRequest.setTaskName(taskResponse.getTaskName());
        waitPickTaskRequest.setResult(ActivitiGeneralOperationEventEnum.PICK.getValue());
        flowOperateService.claimTask(waitPickTaskRequest);
    }

    public void assignTask(ProductTryOnTaskAssignRequest request) {
        ProductTryOnTaskEntity tryOnTaskEntity = this.getOne(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .eq(ProductTryOnTaskEntity::getTaskId, request.getTaskId()));
        if (Objects.isNull(tryOnTaskEntity)) {
            throw new BusinessServiceException("查询不到对应的试穿任务信息!");
        }
        if (1 == tryOnTaskEntity.getStatus()) {
            throw new BusinessServiceException("当前试穿任务已审核!");
        }
        String businessKey = flowOperateService.genBusinessKey(request.getTaskId(), ActivitiBusinessKeyPrefixEnum.TRYON);
        MyTaskQueryRequest taskQueryRequest = new MyTaskQueryRequest();
        taskQueryRequest.setBusinessKeys(Collections.singletonList(businessKey));
        List<TaskResponse> taskResponseList = flowOperateService.queryTasksByBusinessKeys(taskQueryRequest);
        if (CollectionUtils.isEmpty(taskResponseList)) {
            throw new BusinessServiceException("任务工作流不存在");
        }
        TaskResponse taskResponse = taskResponseList.get(0);
        if (!StringUtils.hasText(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException("当前任务未领用请先领用任务");
        }
        if (StringUtils.hasText(taskResponse.getAssigneeName()) && !loginInfoService.getName().equalsIgnoreCase(taskResponse.getAssigneeName())) {
            throw new BusinessServiceException(String.format("当前任务属于%s,无法进行操作", taskResponse.getAssigneeName()));
        }
        tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "转派", String.format("负责人由%s转派给%s", loginInfoService.getName(), request.getAssignName()));
        AssignTaskRequest assignTaskRequest = new AssignTaskRequest();
        assignTaskRequest.setTaskId(taskResponse.getTaskId());
        assignTaskRequest.setAssignee(request.getAssignAccount());
        flowOperateService.assignTask(assignTaskRequest);
    }

    @Transactional(rollbackFor = Exception.class)
    @JLock(keyConstant = "tryOnSaveInfo", lockKey = "#request.sku")
    public void saveInfo(ProductTryOnTaskSaveRequest request) {
        ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(request.getSku());
        List<ProductTryOnTaskEntity> tryOnTaskEntityList = this.list(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .eq(ProductTryOnTaskEntity::getSkc, productSpecInfo.getSkc())
                .orderByDesc(ProductTryOnTaskEntity::getCreateDate));
        if (!CollectionUtils.isEmpty(tryOnTaskEntityList)) {
            if (1 == tryOnTaskEntityList.get(0).getStatus()) {
                //已存在试穿任务不在处理
                return;
            }
            productTryOnTaskItemService.saveItemInfo(tryOnTaskEntityList.get(0).getTaskId(), request.getStockinOrderNo());
            return;
        }
        ProductInfo productInfo = productInfoService.getByProductId(productSpecInfo.getProductId());
        ProductTryOnTaskEntity tryOnTaskEntity = new ProductTryOnTaskEntity();
        tryOnTaskEntity.setStatus(0);
        tryOnTaskEntity.setSkc(productSpecInfo.getSkc());
        tryOnTaskEntity.setSku(request.getSku());
        tryOnTaskEntity.setSpu(productInfo.getSpu());
        tryOnTaskEntity.setFirstStockinDate(new Date());
        tryOnTaskEntity.setLocation(TenantContext.getTenant());
        tryOnTaskEntity.setCreateBy(loginInfoService.getName());
        tryOnTaskEntity.setProductId(productSpecInfo.getProductId());
        tryOnTaskEntity.setSpecId(productSpecInfo.getSpecId());
        tryOnTaskEntity.setLabelName(request.getLabelName());
        this.save(tryOnTaskEntity);
        tryOnTaskLogService.saveLogInfo(tryOnTaskEntity.getTaskId(), "新增", "入库收货满足试穿需求新增");
        productTryOnTaskItemService.saveItemInfo(tryOnTaskEntity.getTaskId(), request.getStockinOrderNo());
        //开启工作流
        StartProcessRequest startProcessRequest = new StartProcessRequest();
        startProcessRequest.setBusinessKey(String.format("%s:%s", ActivitiBusinessKeyPrefixEnum.TRYON.name(), tryOnTaskEntity.getTaskId()));
        startProcessRequest.setSearchCode(productSpecInfo.getSkc());
        startProcessRequest.setDescription(String.format("商品编码:%s,试穿任务", productSpecInfo.getSkc()));
        startProcessRequest.setImageUrl(productSpecInfo.getImageUrl());
        startProcessRequest.setCreateBy("admin");
        startProcessRequest.setCreateName("admin");
        startProcessRequest.setCompanyId(String.valueOf(LocationEnum.getCompanyIdByLocation(TenantContext.getTenant())));
        startProcessRequest.setProcessDefinitionKey(ActivitiProcessDefinitionKeyEnum.TRYON.getName());
        flowOperateService.startProcessInstanceByKey(startProcessRequest);
    }

    public void updateShelvedInfo(String stockinOrderNo, String sku, Date shelverDate) {
        try {
            ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(sku);
            List<ProductTryOnTaskEntity> tryOnTaskEntityList = this.list(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                    .eq(ProductTryOnTaskEntity::getSkc, productSpecInfo.getSkc())
                    .orderByDesc(ProductTryOnTaskEntity::getCreateDate));
            if (CollectionUtils.isEmpty(tryOnTaskEntityList) || tryOnTaskEntityList.stream().filter(detail -> 1 == detail.getStatus()).findAny().isPresent()) {
                return;
            }
            productTryOnTaskItemService.updateShelvedInfo(tryOnTaskEntityList.get(0).getTaskId(), stockinOrderNo, shelverDate);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }

    public boolean validIsTryOn(ProductTryOnValidRequest request) {
        ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(request.getSku());
        List<ProductTryOnTaskEntity> tryOnTaskEntityList = this.list(new LambdaQueryWrapper<ProductTryOnTaskEntity>()
                .eq(ProductTryOnTaskEntity::getSkc, productSpecInfo.getSkc()));
        if (CollectionUtils.isEmpty(tryOnTaskEntityList)) {
            return false;
        }
        return !tryOnTaskEntityList.stream().filter(detail -> 1 == detail.getStatus()).findAny().isPresent();
    }

    public List<SysUserInfo> checkReassignmentGetUsers() {
        return flowOperateService.getConfigCandidateUser(FlowTaskDefinitionEnum.TRY_ON_INITIATE.getTaskDefinitionKey(), loginInfoService.getCompanyId());
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_TRY_ON_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        ProductTryOnTaskPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), ProductTryOnTaskPageRequest.class);
        // 设置每次的查询数量
        IPage page = new Page(request.getPageIndex(), request.getPageSize());
        IPage<ProductTryOnTaskPageResponse> pageResponse = this.baseMapper.pageList(page, downloadRequest);
        response.setTotalCount(pageResponse.getTotal());
        if (CollectionUtils.isEmpty(pageResponse.getRecords())) {
            response.setDataJsonStr(JsonMapper.toJson(Lists.newArrayList()));
            return response;
        }
        //保存附件信息
        Map<Integer, String> imageMap = attachmentService.getImageMapByTaskIdList(pageResponse.getRecords().stream().map(ProductTryOnTaskPageResponse::getTaskId).collect(Collectors.toList()));
        pageResponse.getRecords().forEach(detail -> {
            detail.setAttachImg(imageMap.get(detail.getTaskId()));
        });
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getRecords()));
        return response;
    }
}
