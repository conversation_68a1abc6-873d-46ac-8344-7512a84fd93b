package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.StockoutGenerateBatch;
import com.nsy.api.wms.domain.stockout.StockoutGenerateBatchId;
import com.nsy.api.wms.domain.stockout.StockoutGenerateBatchWhole;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchByDocRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchDomesticWholeRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchHotRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchSecondRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateBatchWholeRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateWaveHotRequest;
import com.nsy.api.wms.request.stockout.StockoutGenerateWaveRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderGenerateWaveRequest;
import com.nsy.api.wms.request.stockout.StockoutReadyWaveGeneratedRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchByDocPrintResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchByDocResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchDomesticWholeResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchHotResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchSecondResponse;
import com.nsy.api.wms.response.stockout.StockoutGenerateBatchWholeResponse;
import com.nsy.api.wms.response.stockout.StockoutGeneratedWaveResponse;
import com.nsy.api.wms.response.stockout.StockoutReadyWaveGeneratedResponse;
import com.nsy.api.wms.response.stockout.StockoutReadyWaveGeneratedWholeResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.GenerateBatchRequest;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.base.BaseService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stockout.valid.StockoutOrderValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdTagMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderProcessInfoEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class StockoutGenerateBatchService extends BaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutGenerateBatchService.class);

    private final String[] secondProjectTypes = {"大波次", "中波次", "小波次"};

    private final String[] wholeProjectTypes = {"急单整单", "普通整单"};

    @Autowired
    LoginInfoService loginInfoService;

    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutOrderProcessInfoService processInfoService;
    @Autowired
    ApplicationContext context;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    BdTagMappingService bdTagMappingService;
    @Autowired
    StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    BrandCommonService brandCommonService;
    @Autowired
    private BdPositionService bdPositionService;

    public PageResponse<StockoutGenerateBatchResponse> getBagFindDocByGoods(StockoutGenerateBatchRequest request) {
        request.setBrandList(brandCommonService.getBrandTagNameList());
        IPage<StockoutGenerateBatch> stockoutGenerateBatchList = stockoutOrderService.getBaseMapper().findSmallBagStockoutGenerateBatchList(new Page(request.getPageIndex(), request.getPageSize()),
                TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name(), StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name());
        List<StockoutGenerateBatchId> stockoutIdsGenerateBatchList = stockoutOrderService.getBaseMapper().findSmallBagStockoutIdsGenerateBatchList(TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name(), StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name());
        Map<String, List<StockoutGenerateBatchId>> idMap = CollectionUtils.isEmpty(stockoutIdsGenerateBatchList) ? Collections.EMPTY_MAP
                : stockoutIdsGenerateBatchList.stream().collect(Collectors.groupingBy(item -> item.getLogisticsCompany() + "_" + item.getTagName()));
        List<StockoutGenerateBatchResponse> collect = stockoutGenerateBatchList.getRecords().stream().map(stockoutGenerateBatch -> {
            StockoutGenerateBatchResponse stockoutGenerateBatchResponse = new StockoutGenerateBatchResponse();
            BeanUtilsEx.copyProperties(stockoutGenerateBatch, stockoutGenerateBatchResponse);
            List<StockoutGenerateBatchId> stockoutGenerateBatchIds = idMap.get(stockoutGenerateBatch.getLogisticsCompany() + "_" + stockoutGenerateBatch.getTagName());
            stockoutGenerateBatchResponse.setReadyGeneratedBatchId(CollectionUtils.isEmpty(stockoutGenerateBatchIds) ? Collections.EMPTY_LIST : stockoutGenerateBatchIds.stream().map(StockoutGenerateBatchId::getStockoutOrderId).collect(Collectors.toList()));
            return stockoutGenerateBatchResponse;
        }).collect(Collectors.toList());
        PageResponse<StockoutGenerateBatchResponse> pageResponse = PageResponse.of(stockoutGenerateBatchList.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    public PageResponse<StockoutGenerateBatchResponse> getSecondSort(StockoutGenerateBatchRequest request) {
        request.setBrandList(brandCommonService.getBrandTagNameList());
        IPage<StockoutGenerateBatch> stockoutGenerate = stockoutOrderService.getBaseMapper().findSmallBagStockoutGenerateBatchList(new Page(request.getPageIndex(), request.getPageSize()),
                TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name(), StockoutPickingTypeEnum.SECOND_SORT.name());
        List<StockoutGenerateBatchId> stockoutIdsGenerateBatchList = stockoutOrderService.getBaseMapper().findSmallBagStockoutIdsGenerateBatchList(TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name(), StockoutPickingTypeEnum.SECOND_SORT.name());
        Map<String, List<StockoutGenerateBatchId>> idMap = CollectionUtils.isEmpty(stockoutIdsGenerateBatchList) ? Collections.EMPTY_MAP
                : stockoutIdsGenerateBatchList.stream().collect(Collectors.groupingBy(item -> item.getLogisticsCompany() + "_" + item.getTagName()));
        List<StockoutGenerateBatchResponse> collect = stockoutGenerate.getRecords().stream().map(stockoutGenerateBatch -> {
            StockoutGenerateBatchResponse stockoutGenerateBatchResponse = new StockoutGenerateBatchResponse();
            BeanUtilsEx.copyProperties(stockoutGenerateBatch, stockoutGenerateBatchResponse);
            List<StockoutGenerateBatchId> stockoutGenerateBatchIds = idMap.get(stockoutGenerateBatch.getLogisticsCompany() + "_" + stockoutGenerateBatch.getTagName());
            stockoutGenerateBatchResponse.setReadyGeneratedBatchId(CollectionUtils.isEmpty(stockoutGenerateBatchIds) ? Collections.EMPTY_LIST : stockoutGenerateBatchIds.stream().map(StockoutGenerateBatchId::getStockoutOrderId).collect(Collectors.toList()));
            return stockoutGenerateBatchResponse;
        }).collect(Collectors.toList());
        PageResponse<StockoutGenerateBatchResponse> pageResponse = PageResponse.of(stockoutGenerate.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    public PageResponse<StockoutGenerateBatchHotResponse> getHot(StockoutGenerateBatchHotRequest request) {
        IPage<StockoutGenerateBatchHotResponse> stockoutGenerate = stockoutOrderService.getBaseMapper().findStockoutGenerateBatchHotList(
                new Page(request.getPageIndex(), request.getPageSize()), TenantContext.getTenant(), request);
        List<StockoutGenerateBatchHotResponse> collect = stockoutGenerate.getRecords().stream().map(entity -> {
            entity.setReadyGeneratedBatchId(getStockoutOrderId(entity.getStockoutOrderIdStr()));
            StockoutGenerateBatchHotResponse stockoutGenerateBatchHotResponse = new StockoutGenerateBatchHotResponse();
            BeanUtilsEx.copyProperties(entity, stockoutGenerateBatchHotResponse);
            stockoutGenerateBatchHotResponse.setStockoutOrderIdStr(null);
            return stockoutGenerateBatchHotResponse;
        }).collect(Collectors.toList());
        PageResponse<StockoutGenerateBatchHotResponse> pageResponse = PageResponse.of(stockoutGenerate.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    private List<Integer> getStockoutOrderId(String stockoutOrderIdStr) {
        if (!StringUtils.hasText(stockoutOrderIdStr)) {
            return new ArrayList<>();
        }
        return Arrays.stream(stockoutOrderIdStr.split(",")).map(Integer::valueOf).collect(Collectors.toList());
    }

    public PageResponse<StockoutGenerateBatchByDocResponse> getFindGoodsByDoc(StockoutGenerateBatchByDocRequest request) {
        request.setBrandList(brandCommonService.getBrandTagNameList());
        IPage<StockoutGenerateBatchByDocResponse> stockoutGenerate = stockoutOrderService.getBaseMapper().findFindGoodsByDoc(new Page(request.getPageIndex(), request.getPageSize()),
                TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name());
        List<StockoutGenerateBatchByDocResponse> collect = stockoutGenerate.getRecords().stream().map(entity -> {
            entity.setReadyGeneratedBatchId(getStockoutOrderId(entity.getStockoutOrderIdStr()));
            StockoutGenerateBatchByDocResponse stockoutGenerateBatchByDocResponse = new StockoutGenerateBatchByDocResponse();
            BeanUtilsEx.copyProperties(entity, stockoutGenerateBatchByDocResponse);
            stockoutGenerateBatchByDocResponse.setStockoutOrderIdStr(null);
            return stockoutGenerateBatchByDocResponse;
        }).collect(Collectors.toList());
        PageResponse<StockoutGenerateBatchByDocResponse> pageResponse = PageResponse.of(stockoutGenerate.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    public PageResponse<StockoutGenerateBatchByDocPrintResponse> getFindGoodsByDocPrint(StockoutGenerateBatchByDocRequest request) {
        request.setBrandList(brandCommonService.getBrandTagNameList());
        IPage<StockoutGenerateBatchByDocPrintResponse> stockoutGenerate = stockoutOrderService.getBaseMapper().findFindGoodsByDocPrint(new Page(request.getPageIndex(), request.getPageSize()), request);
        PageResponse<StockoutGenerateBatchByDocPrintResponse> pageResponse = PageResponse.of(stockoutGenerate.getTotal());
        pageResponse.setContent(stockoutGenerate.getRecords());
        return pageResponse;
    }

    public PageResponse<StockoutGenerateBatchWholeResponse> getWholePick(StockoutGenerateBatchWholeRequest request) {
        IPage<StockoutGenerateBatchWhole> stockoutGenerate = stockoutOrderService.getBaseMapper().findWholePick(new Page(request.getPageIndex(), request.getPageSize()),
                TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name());
        List<StockoutGenerateBatchWholeResponse> collect = stockoutGenerate.getRecords().stream().map(stockoutGenerateBatch -> {
            StockoutGenerateBatchWholeResponse response = new StockoutGenerateBatchWholeResponse();
            BeanUtilsEx.copyProperties(stockoutGenerateBatch, response);
            response.setSpaceAreaQty(getQty(stockoutGenerateBatch.getSpaceAreaStr()));
            response.setReadyGeneratedBatchId(getStockoutOrderId(stockoutGenerateBatch.getStockoutOrderIdStr()));
            return response;
        }).collect(Collectors.toList());
        PageResponse<StockoutGenerateBatchWholeResponse> pageResponse = PageResponse.of(stockoutGenerate.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    private Integer getQty(String str) {
        return !StringUtils.hasText(str) ? 0 : new HashSet<String>(Arrays.asList(str.split(","))).size();
    }

    public PageResponse<StockoutGenerateBatchResponse> getDomesticFindDocByGoods(StockoutGenerateBatchRequest request) {
        request.setBrandList(brandCommonService.getBrandTagNameList());
        IPage<StockoutGenerateBatch> stockoutGenerate = stockoutOrderService.getBaseMapper().findStockoutGenerateBatchList(new Page(request.getPageIndex(), request.getPageSize()),
                TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name(), StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name());
        List<StockoutGenerateBatchId> stockoutIdsGenerateBatchList = stockoutOrderService.getBaseMapper().findStockoutIdsGenerateBatchList(TenantContext.getTenant(), request, StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name(), StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name());
        Map<String, List<StockoutGenerateBatchId>> idMap = CollectionUtils.isEmpty(stockoutIdsGenerateBatchList) ? Collections.EMPTY_MAP
                : stockoutIdsGenerateBatchList.stream().collect(Collectors.groupingBy(item -> item.getLogisticsCompany() + "_" + item.getLogisticsLabelSize() + "_" + item.getTagName()));
        List<StockoutGenerateBatchResponse> collect = stockoutGenerate.getRecords().stream().map(stockoutGenerateBatch -> {
            StockoutGenerateBatchResponse stockoutGenerateBatchResponse = new StockoutGenerateBatchResponse();
            BeanUtilsEx.copyProperties(stockoutGenerateBatch, stockoutGenerateBatchResponse);
            List<StockoutGenerateBatchId> stockoutGenerateBatchIds = idMap.get(stockoutGenerateBatch.getLogisticsCompany() + "_" + stockoutGenerateBatch.getLogisticsLabelSize() + "_" + stockoutGenerateBatch.getTagName());
            stockoutGenerateBatchResponse.setReadyGeneratedBatchId(CollectionUtils.isEmpty(stockoutGenerateBatchIds) ? Collections.EMPTY_LIST : stockoutGenerateBatchIds.stream().map(StockoutGenerateBatchId::getStockoutOrderId).collect(Collectors.toList()));
            return stockoutGenerateBatchResponse;
        }).collect(Collectors.toList());
        PageResponse<StockoutGenerateBatchResponse> pageResponse = PageResponse.of(stockoutGenerate.getTotal());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    public List<StockoutGenerateBatchSecondResponse> getSecondSortLimit(StockoutGenerateBatchSecondRequest request, StockoutOrderWorkSpaceEnum stockoutWorkLocationEnum) {
        Map<String, String> bdSystemParameterMap = bdSystemParameterService.getBdSystemParameterMap();
        Integer smallBatchUpperLimit = getLimit(bdSystemParameterMap, StockoutOrderWorkSpaceEnum.DOMESTIC_AREA == stockoutWorkLocationEnum
                ? BdSystemParameterEnum.DOMESTIC_SMALL_BATCH_UPPER_LIMIT : BdSystemParameterEnum.B2B_SMALL_BATCH_UPPER_LIMIT);
        Integer bigBatchLowerLimit = getLimit(bdSystemParameterMap, StockoutOrderWorkSpaceEnum.DOMESTIC_AREA == stockoutWorkLocationEnum
                ? BdSystemParameterEnum.DOMESTIC_BIG_BATCH_LOWER_LIMIT : BdSystemParameterEnum.B2B_BIG_BATCH_LOWER_LIMIT);
        List<StockoutGenerateBatch> stockoutGenerateList = stockoutOrderService.getBaseMapper().findSecondSortList(TenantContext.getTenant(), request,
                stockoutWorkLocationEnum.name());
        Map<String, List<StockoutGenerateBatch>> generateBatchMap = initMap(stockoutGenerateList.stream().collect(Collectors.groupingBy(stockoutGenerate -> {
            Integer skuQty = stockoutGenerate.getSkuQty();
            return skuQty > smallBatchUpperLimit ? skuQty > bigBatchLowerLimit ? "大波次" : "中波次" : "小波次";
        })));
        addPddBatchSelection(stockoutWorkLocationEnum, generateBatchMap, request);
        return generateBatchMap.entrySet().stream().map(generateBatchEntry -> {
            StockoutGenerateBatchSecondResponse stockoutGenerateBatchSecondResponse = new StockoutGenerateBatchSecondResponse();
            stockoutGenerateBatchSecondResponse.setProject(generateBatchEntry.getKey());
            List<StockoutGenerateBatch> value = generateBatchEntry.getValue();
            stockoutGenerateBatchSecondResponse.setReadyGeneratedBatchQty(CollectionUtils.isEmpty(value) ? 0 : value.size());
            stockoutGenerateBatchSecondResponse.setReadyGeneratedBatchId(CollectionUtils.isEmpty(value) ? new ArrayList<>() : value.stream().map(batch -> Integer.valueOf(batch.getStockoutOrderId())).collect(Collectors.toList()));
            stockoutGenerateBatchSecondResponse.setSkuQty(value.stream().mapToInt(StockoutGenerateBatch::getSkuQty).sum());
            return stockoutGenerateBatchSecondResponse;
        }).collect(Collectors.toList());

    }

    // 增加内贸PDD波次生成选项
    private void addPddBatchSelection(StockoutOrderWorkSpaceEnum stockoutWorkLocationEnum, Map<String, List<StockoutGenerateBatch>> listMap, StockoutGenerateBatchSecondRequest request) {
        if (StockoutOrderWorkSpaceEnum.DOMESTIC_AREA != stockoutWorkLocationEnum) {
            return;
        }
        List<StockoutGenerateBatch> stockoutPddGenerateList = stockoutOrderService.getBaseMapper().findPddSecondSortList(TenantContext.getTenant(), request,
                stockoutWorkLocationEnum.name());
        listMap.put("拼多多波次", stockoutPddGenerateList);
    }

    private Integer getLimit(Map<String, String> bdSystemParameterMap, BdSystemParameterEnum bdSystemParameterEnum) {
        Integer limit = getInteger(bdSystemParameterMap.get(bdSystemParameterEnum.getKey()));
        if (Objects.isNull(limit)) {
            throw new BusinessServiceException(String.format("请先配置%s", bdSystemParameterEnum.getName()));
        }
        return limit;
    }


    private Map<String, List<StockoutGenerateBatch>> initMap(Map<String, List<StockoutGenerateBatch>> map) {
        HashMap<String, List<StockoutGenerateBatch>> generateBatchMap = new HashMap<>();
        for (String type : secondProjectTypes) {
            if (!map.containsKey(type)) {
                generateBatchMap.put(type, new ArrayList<StockoutGenerateBatch>());
            } else {
                generateBatchMap.put(type, map.get(type));
            }
        }
        return generateBatchMap;
    }

    public List<StockoutGenerateBatchDomesticWholeResponse> getWholePick2(StockoutGenerateBatchDomesticWholeRequest request, StockoutOrderWorkSpaceEnum stockoutWorkLocationEnum) {
        List<StockoutGenerateBatch> stockoutGenerateList = stockoutOrderService.getBaseMapper().findWholePickList(TenantContext.getTenant(), request,
                stockoutWorkLocationEnum.name());
        List<StockoutGenerateBatchDomesticWholeResponse> result = new ArrayList<>(2);
        for (String type : wholeProjectTypes) {
            Integer isUrgent = "急单整单".equals(type) ? 1 : 0;
            StockoutGenerateBatchDomesticWholeResponse response = new StockoutGenerateBatchDomesticWholeResponse();
            List<StockoutGenerateBatch> collect = stockoutGenerateList.stream().filter(stockoutGenerate -> isUrgent.equals(stockoutGenerate.getIsUrgent())).collect(Collectors.toList());
            response.setProject(type);
            Long count = collect.stream().filter(stockoutGenerateBatch ->
                    StockoutOrderStatusEnum.READY_WAVE_GENERATED.name().equals(stockoutGenerateBatch.getStatus())).count();
            response.setReadyGeneratedBatchQty(count.intValue());
            response.setSpaceAreaQty(getQty(StringUtils.join(collect.stream().map(StockoutGenerateBatch::getSpaceAreaStr).collect(Collectors.toList()), ',')));
            response.setSkuQty(collect.stream().mapToInt(StockoutGenerateBatch::getSkuQty).sum());

            response.setReadyGeneratedBatchId(collect.stream().filter(generateBatch ->
                            StockoutOrderStatusEnum.READY_WAVE_GENERATED.name().equals(generateBatch.getStatus()))
                    .map(stockoutGenerateBatch -> Integer.valueOf(stockoutGenerateBatch.getStockoutOrderId())).collect(Collectors.toList()));
            result.add(response);
        }
        return result;
    }

    private Integer getInteger(String num) {
        if (StringUtils.hasText(num)) {
            return Integer.valueOf(num);
        }
        return null;
    }

    public List<StockoutReadyWaveGeneratedResponse> getReadyWaveGeneratedSpaceArea(StockoutReadyWaveGeneratedRequest request) {
        StockoutOrderValid.validateStockoutReadyWaveGeneratedRequest(request);
        LambdaQueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<StockoutOrderEntity>().lambda()
                .in(StockoutOrderEntity::getStockoutOrderId, request.getReadyGeneratedBatchId())
                .ne(StockoutOrderEntity::getStockoutType, StockoutOrderTypeEnum.OVERSEA_DELIVERY.name());
        if (StringUtils.hasText(request.getDeclareType())) {
            queryWrapper.eq(StockoutOrderEntity::getCustomsDeclareType, request.getDeclareType());
        }
        List<StockoutOrderEntity> orderEntityList = stockoutOrderService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderEntityList)) {
            return new ArrayList<>();
        }
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda()
                .in(StockoutOrderItemEntity::getStockoutOrderId, orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList())));
        Map<Integer, List<StockoutOrderItemEntity>> collect = orderItemEntityList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getStockoutOrderId));
        Map<String, List<StockoutOrderEntity>> stockoutOrderMap = orderEntityList.stream().collect(Collectors.groupingBy(entity -> {
            List<StockoutOrderItemEntity> itemEntityList = collect.get(entity.getStockoutOrderId());
            if (CollectionUtils.isEmpty(itemEntityList) || itemEntityList.stream().anyMatch(o -> !StringUtils.hasText(o.getSpaceAreaName()))) {
                return "无预配";
            }
            if (itemEntityList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getSpaceAreaName)).size() > 1) {
                return "跨库区";
            } else {
                return itemEntityList.get(0).getSpaceAreaName();
            }
        }));
        return stockoutOrderMap.entrySet().stream().filter(o -> !"无预配".equals(o.getKey())).map(entry -> {
            StockoutReadyWaveGeneratedResponse response = new StockoutReadyWaveGeneratedResponse();
            response.setSpaceAreaName(entry.getKey());
            response.setReadyGeneratedBatchQty(entry.getValue().size());
            response.setReadyGeneratedBatchId(entry.getValue().stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()));
            return response;
        }).collect(Collectors.toList());
    }

    public StockoutReadyWaveGeneratedWholeResponse getReadyWaveGenerated(StockoutReadyWaveGeneratedRequest request) {
        StockoutOrderValid.validateStockoutReadyWaveGeneratedRequest(request);
        LambdaQueryWrapper<StockoutOrderEntity> queryWrapper = new QueryWrapper<StockoutOrderEntity>().lambda()
                .in(StockoutOrderEntity::getStockoutOrderId, request.getReadyGeneratedBatchId())
                .ne(StockoutOrderEntity::getStockoutType, StockoutOrderTypeEnum.OVERSEA_DELIVERY.name());
        if (StringUtils.hasText(request.getDeclareType())) {
            queryWrapper.eq(StockoutOrderEntity::getCustomsDeclareType, request.getDeclareType());
        }
        List<StockoutOrderEntity> orderEntityList = stockoutOrderService.list(queryWrapper);
        StockoutReadyWaveGeneratedWholeResponse response = new StockoutReadyWaveGeneratedWholeResponse();
        response.setReadyGeneratedBatchId(orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()));
        response.setReadyGeneratedBatchQty(orderEntityList.size());
        return response;
    }

    @JLock(keyConstant = "generateWave", lockKey = "#ids")
    public void generateWaveLock(StockoutGenerateWaveRequest request, String[] ids) {
        context.getBean(this.getClass()).generateWave(request);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void generateWave(StockoutGenerateWaveRequest request) {
        StockoutOrderValid.validateStockoutGenerateWaveRequest(request);
        List<Integer> readyGeneratedBatchId = request.getReadyGeneratedBatchId();
        readyGeneratedBatchId.sort(Integer::compareTo);
        List<Integer> integers = readyGeneratedBatchId.subList(0, request.getGeneratedBatchQty());
        List<StockoutOrderEntity> orderEntityList = stockoutOrderService.list(new QueryWrapper<StockoutOrderEntity>().lambda()
                .in(StockoutOrderEntity::getStockoutOrderId, integers)
                .eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.READY_WAVE_GENERATED.name())
                .ne(StockoutOrderEntity::getStockoutType, StockoutOrderTypeEnum.OVERSEA_DELIVERY.name()));
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("找不到出库单");
        }
        this.validateStockoutOrder(orderEntityList);
        StockoutOrderEntity stockoutOrderEntity = orderEntityList.get(0);
        String logisticsLabelSize = stockoutOrderEntity.getLogisticsLabelSize();
        if (StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equals(stockoutOrderEntity.getWorkspace()) && !orderEntityList.stream().allMatch(entity -> {
            if (Objects.isNull(logisticsLabelSize)) {
                return Objects.isNull(entity.getLogisticsLabelSize());
            }
            return logisticsLabelSize.equals(entity.getLogisticsLabelSize());
        })) {
            throw new BusinessServiceException("请选择相同标签规格生成波次");
        }
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda()
                .in(StockoutOrderItemEntity::getStockoutOrderId, orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList())));
        Map<Integer, List<StockoutOrderItemEntity>> collect = orderItemEntityList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getStockoutOrderId));

        List<List<StockoutOrderEntity>> needBuildStockoutOrderList = new ArrayList<>();

        //按单拣货，勾选几条生成几条波次
        if (StockoutPickingTypeEnum.WHOLE_PICK.name().equals(stockoutOrderEntity.getPickingType())) {
            for (StockoutOrderEntity stockoutOrder : orderEntityList) {
                needBuildStockoutOrderList.add(Collections.singletonList(stockoutOrder));
            }
            //生成统一波次
        } else {
            needBuildStockoutOrderList.add(orderEntityList);
        }

        needBuildStockoutOrderList.forEach(stockoutOrderList -> {
            generateWave(stockoutOrderList, request, collect);
        });
    }

    private void generateWave(List<StockoutOrderEntity> stockoutOrderList, StockoutGenerateWaveRequest request, Map<Integer, List<StockoutOrderItemEntity>> collect) {
        //生成波次
        StockoutBatchEntity stockoutBatchEntity = buildSockoutBatch(stockoutOrderList, request.getBatchSplitType());
        List<StockoutBatchOrderItemEntity> orderItemList = new ArrayList<>();
        stockoutOrderList.forEach(orderEntity -> {
            StockoutBatchOrderEntity stockoutBatchOrder = buildStockoutBatchOrder(stockoutBatchEntity, orderEntity);
            List<StockoutOrderItemEntity> itemEntityList = collect.get(orderEntity.getStockoutOrderId());
            orderItemList.addAll(itemEntityList.stream().map(orderItemEntity -> buildStockoutBatchOrderItem(orderItemEntity, stockoutBatchOrder)).collect(Collectors.toList()));
        });
        stockoutBatchOrderItemService.saveBatch(orderItemList);

        List<StockoutOrderEntity> orderEntityListNew = stockoutOrderList.stream().map(orderEntity -> {
            if (StockoutOrderStatusEnum.checkUpdateStatus(orderEntity.getStatus(), StockoutOrderStatusEnum.READY_PICK.name())) {
                orderEntity.setStatus(StockoutOrderStatusEnum.READY_PICK.name());
            } else {
                LOGGER.info("出库单:{}无法回退状态:{}到:{}", orderEntity.getStockoutOrderNo(), orderEntity.getStatus(), StockoutOrderStatusEnum.READY_PICK.name());
            }
            stockoutOrderLogService.addLog(orderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_PICK,
                    String.format("【%s】生成波次，波次号%s", loginInfoService.getName(), stockoutBatchEntity.getBatchId()));
            stockoutLogQueueService.addLogQueue(Stream.of(orderEntity.getStockoutOrderId()).collect(Collectors.toList()), String.format("【%s】生成波次, 波次号【%s】", loginInfoService.getName(), stockoutBatchEntity.getBatchId()));
            StockoutOrderEntity entity = new StockoutOrderEntity();
            BeanUtilsEx.copyProperties(orderEntity, entity);
            return entity;
        }).collect(Collectors.toList());

        stockoutOrderService.updateBatchById(orderEntityListNew);

        stockoutBatchLogService.addLog(stockoutBatchEntity.getBatchId(), StockoutBatchLogTypeEnum.NEW.getStockoutBatchLogType(),
                String.format("【%s】生成波次，拣货模式【%s】，波次号%s",
                        enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), stockoutBatchEntity.getWorkspace()),
                        enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), stockoutBatchEntity.getPickingType()),
                        stockoutBatchEntity.getBatchId()));

        //转换预配关系
        stockPrematchInfoService.convertToBatch(stockoutBatchEntity.getBatchId(), StockoutBatchLogTypeEnum.NEW);

        //生成波次后生成加工计划、加工波次
        if (!Objects.isNull(stockoutBatchEntity.getIsNeedProcess()) && 1 == stockoutBatchEntity.getIsNeedProcess()) {
            generateProcessBatch(stockoutBatchEntity.getBatchId(), stockoutBatchEntity.getPickingType(), stockoutBatchEntity.getWorkspace());
        }
    }

    public void generateProcessBatch(Integer batchId, String pickingType, String workspace) {
        GenerateBatchRequest generateBatchRequest = new GenerateBatchRequest();
        generateBatchRequest.setProcessMode("KA_INNER_PROCESS");
        generateBatchRequest.setProcessorId(244);
        generateBatchRequest.setWmsBatchId(batchId);
        generateBatchRequest.setPickingType(pickingType);
        generateBatchRequest.setWorkspace(workspace);
        generateBatchRequest.setPlanDTOList(processInfoService.getByTaskItemList(batchId));
        generateBatchRequest.setUserName(loginInfoService.getName());
        scmApiService.generateSplitBatch(generateBatchRequest);
    }

    @Transactional
    public void changeFindGoodsByDoc(StockoutGenerateWaveHotRequest request) {
        StockoutOrderValid.validateStockoutGenerateWaveHotRequest(request);
        List<StockoutOrderEntity> orderEntityList = stockoutOrderService.list(new QueryWrapper<StockoutOrderEntity>().lambda()
                .in(StockoutOrderEntity::getStockoutOrderId, request.getReadyGeneratedBatchId())
                .eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.READY_WAVE_GENERATED.name())
                .eq(StockoutOrderEntity::getWorkspace, StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name())
                .eq(StockoutOrderEntity::getPickingType, StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name()));
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("找不到出库单");
        }
        orderEntityList.stream().forEach(entity -> {
            entity.setPickingType(StockoutPickingTypeEnum.FIND_GOODS_BY_DOC.name());
            stockoutOrderService.updateById(entity);
            stockoutOrderLogService.addLog(entity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_FIND_GOODS_BY_DOC,
                    String.format("【%s】修改出库单【%s】为以单找货", loginInfoService.getName(), entity.getStockoutOrderNo()));
        });
    }

    private StockoutBatchEntity buildSockoutBatch(List<StockoutOrderEntity> orderEntityList, String batchSplitType) {
        StockoutOrderEntity stockoutOrderEntity = orderEntityList.get(0);
        StockoutBatchEntity stockoutBatchEntity = new StockoutBatchEntity();
        Date dateStart = WmsDateUtils.getDateStart(new Date());
        StockoutBatchEntity stockoutBatchEntityLast = stockoutBatchService.getOne(new LambdaQueryWrapper<StockoutBatchEntity>()
                .ge(StockoutBatchEntity::getCreateDate, dateStart).orderByDesc(StockoutBatchEntity::getCreateDate).last("limit 1"));
        stockoutBatchEntity.setBatchIndex(Objects.isNull(stockoutBatchEntityLast) ? 1 : stockoutBatchEntityLast.getBatchIndex() + 1);
        stockoutBatchEntity.setBatchType(StockoutWavePlanTypeEnum.NORMAL_WAVE.name());
        stockoutBatchEntity.setStatus(StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name());
        stockoutBatchEntity.setCreateBy(loginInfoService.getName());
        stockoutBatchEntity.setUpdateBy(loginInfoService.getName());
        stockoutBatchEntity.setCustomsDeclareType(stockoutOrderEntity.getCustomsDeclareType());
        stockoutBatchEntity.setIsMergeBatch(0);
        stockoutBatchEntity.setLocation(stockoutOrderEntity.getLocation());
        stockoutBatchEntity.setPickingType(stockoutOrderEntity.getPickingType());
        stockoutBatchEntity.setSpaceId(stockoutOrderEntity.getSpaceId());
        stockoutBatchEntity.setStockoutType(stockoutOrderEntity.getStockoutType());
        stockoutBatchEntity.setWorkspace(stockoutOrderEntity.getWorkspace());
        stockoutBatchEntity.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
        String batchSplitTypeNew = StockoutPickingTypeEnum.WHOLE_PICK.name().equals(stockoutOrderEntity.getPickingType()) ? StockoutSortingTypeEnum.MANUAL_SORT.name() : batchSplitType;
        stockoutBatchEntity.setBatchSplitType(batchSplitTypeNew);
        stockoutBatchEntity.setScanType(StockoutScanTypeService.getScanType(stockoutBatchEntity.getPickingType(), stockoutBatchEntity.getWorkspace(), batchSplitTypeNew));
        stockoutBatchEntity.setLogisticsLabelSize(stockoutOrderEntity.getLogisticsLabelSize());
        if (StockoutOrderTypeEnum.PROCESS_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())) {
            stockoutBatchEntity.setExternalBatchId(Integer.valueOf(stockoutOrderEntity.getStockoutOrderNo().replace(StockConstant.STOCKOUT_ORDER_NO_JGBCH, "")));
        }
        // 生成波次，只要有一个出库单是加工，生成波次是加工波次，有一个出库单是跨库，生成波次是跨库
        boolean isNeedProcess = orderEntityList.stream().anyMatch(item -> item.getNeedProcess() != null && item.getNeedProcess());
        boolean multipleSpace = orderEntityList.stream().anyMatch(item -> item.getMultipleSpace() != null && item.getMultipleSpace().equals(1));
        stockoutBatchEntity.setIsNeedProcess(isNeedProcess ? 1 : 0);
        stockoutBatchEntity.setMultipleSpace(multipleSpace ? 1 : 0);
        stockoutBatchService.save(stockoutBatchEntity);
        return stockoutBatchEntity;
    }

    private StockoutBatchOrderEntity buildStockoutBatchOrder(StockoutBatchEntity stockoutBatchEntity, StockoutOrderEntity stockoutOrderEntity) {
        StockoutBatchOrderEntity stockoutBatchOrderEntity = new StockoutBatchOrderEntity();
        stockoutBatchOrderEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        stockoutBatchOrderEntity.setBatchId(stockoutBatchEntity.getBatchId());
        stockoutBatchOrderEntity.setCreateBy(loginInfoService.getName());
        stockoutBatchOrderEntity.setUpdateBy(loginInfoService.getName());
        stockoutBatchOrderEntity.setIsLack(0);
        stockoutBatchOrderEntity.setStoreId(stockoutOrderEntity.getStoreId());
        stockoutBatchOrderEntity.setStoreName(stockoutOrderEntity.getStoreName());
        stockoutBatchOrderEntity.setLocation(TenantContext.getTenant());
        stockoutBatchOrderEntity.setStatus(StockoutOrderStatusEnum.READY_PICK.name());
        stockoutBatchOrderService.save(stockoutBatchOrderEntity);
        return stockoutBatchOrderEntity;
    }

    private StockoutBatchOrderItemEntity buildStockoutBatchOrderItem(StockoutOrderItemEntity itemEntity, StockoutBatchOrderEntity orderEntity) {
        StockoutBatchOrderItemEntity stockoutBatchOrderItemEntity = new StockoutBatchOrderItemEntity();
        if (StringUtils.hasText(itemEntity.getPositionCode())) {
            BdPositionEntity positionByCode = bdPositionService.getPositionByCode(itemEntity.getPositionCode());
            if (Objects.nonNull(positionByCode)) {
                stockoutBatchOrderItemEntity.setSpaceAreaId(positionByCode.getSpaceAreaId());
                stockoutBatchOrderItemEntity.setSpaceAreaName(positionByCode.getSpaceAreaName());
            }
        }
        stockoutBatchOrderItemEntity.setBarcode(itemEntity.getBarcode());
        stockoutBatchOrderItemEntity.setBatchOrderId(orderEntity.getBatchOrderId());
        stockoutBatchOrderItemEntity.setCreateBy(loginInfoService.getName());
        stockoutBatchOrderItemEntity.setLocation(itemEntity.getLocation());
        stockoutBatchOrderItemEntity.setProductId(itemEntity.getProductId());
        stockoutBatchOrderItemEntity.setQty(itemEntity.getQty());
        stockoutBatchOrderItemEntity.setScanQty(0);
        stockoutBatchOrderItemEntity.setSku(itemEntity.getSku());
        stockoutBatchOrderItemEntity.setSpecId(itemEntity.getSpecId());
        stockoutBatchOrderItemEntity.setStockoutOrderItemId(itemEntity.getStockoutOrderItemId());
        stockoutBatchOrderItemEntity.setUpdateBy(loginInfoService.getName());
        stockoutBatchOrderItemEntity.setIsFirstOrderByStore(itemEntity.getIsFirstOrderByStore());
        return stockoutBatchOrderItemEntity;
    }

    @Transactional
    public StockoutGeneratedWaveResponse generateWaveFromStockoutOrder(StockoutOrderGenerateWaveRequest request) {
        StockoutGeneratedWaveResponse stockoutGeneratedWaveResponse = new StockoutGeneratedWaveResponse();
        List<StockoutOrderEntity> orderEntityList = stockoutOrderService.listByIds(request.getStockoutOrderId());
        if (orderEntityList.stream().anyMatch(entity -> !StockoutOrderStatusEnum.READY_WAVE_GENERATED.name().equals(entity.getStatus()))) {
            throw new BusinessServiceException("请选择待生成波次的出库单！");
        }
        if (orderEntityList.stream().anyMatch(entity -> StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equals(entity.getStockoutType()))) {
            throw new BusinessServiceException(String.format("请选择非【%s】的出库单！", StockoutOrderTypeEnum.OVERSEA_DELIVERY.getName()));
        }
        if (!StockoutPickingTypeEnum.WHOLE_PICK.name().equals(orderEntityList.get(0).getPickingType()) && !StringUtils.hasText(request.getBatchSplitType())) {
            stockoutGeneratedWaveResponse.setShowNotice(Boolean.TRUE);
            stockoutGeneratedWaveResponse.setNotice("请选择分拣类型");
            return stockoutGeneratedWaveResponse;
        }
        StockoutGenerateWaveRequest stockoutGenerateWaveRequest = new StockoutGenerateWaveRequest();
        stockoutGenerateWaveRequest.setBatchSplitType(request.getBatchSplitType());
        stockoutGenerateWaveRequest.setReadyGeneratedBatchId(request.getStockoutOrderId());
        stockoutGenerateWaveRequest.setGeneratedBatchQty(request.getStockoutOrderId().size());
        context.getBean(this.getClass()).generateWaveLock(stockoutGenerateWaveRequest, request.getStockoutOrderId().stream().map(String::valueOf).toArray(String[]::new));
        stockoutGeneratedWaveResponse.setShowNotice(Boolean.FALSE);
        return stockoutGeneratedWaveResponse;
    }

    public void validateStockoutOrder(List<StockoutOrderEntity> orderEntityList) {
        Map<String, List<StockoutOrderEntity>> collect = orderEntityList.stream().collect(Collectors.groupingBy(entity ->
                String.format("%s_%s_%s_%s_%s", entity.getSpaceId(), entity.getStockoutType(), entity.getPickingType(), entity.getWorkspace(), entity.getNeedProcess())));
        if (collect.size() > 1) {
            Map<String, List<StockoutOrderEntity>> collect1 = orderEntityList.stream().collect(Collectors.groupingBy(entity ->
                    String.format("%s", entity.getNeedProcess())));
            if (collect1.size() > 1) {
                throw new BusinessServiceException("加工单不允许和正常出库单一起生成波次！");
            }
            throw new BusinessServiceException("请选择出库单相同仓库、出库类型、拣货模式、工作区域的出库单！");
        }
        if (orderEntityList.stream().anyMatch(it -> StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name().equals(it.getPickingType())
                && StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name().equals(it.getWorkspace())
                && StockoutOrderPlatformEnum.PDD.getName().equals(it.getPlatformName()))
                && orderEntityList.stream().anyMatch(it -> !StockoutOrderPlatformEnum.PDD.getName().equals(it.getPlatformName()))) {
            // 内贸 以货找单 且 部分包含pdd的
            throw new BusinessServiceException("拼多多以货找单不能和其他的平台的混合扫描，请检查！");
        }
        if (orderEntityList.get(0).getNeedProcess()) {
            List<Integer> stockoutOrderIds = orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList());
            List<Integer> processTypes = processInfoService.list(new QueryWrapper<StockoutOrderProcessInfoEntity>().lambda().in(StockoutOrderProcessInfoEntity::getStockoutOrderId, stockoutOrderIds))
                    .stream().map(StockoutOrderProcessInfoEntity::getProcessType).distinct().collect(Collectors.toList());
            if (!processTypes.isEmpty() && processTypes.size() > 1)
                throw new BusinessServiceException("请选择相同加工类型的出库单！");
        }
        //B2B波次生成去掉品牌单和非品牌单不能一起生成波次的限制
        if (orderEntityList.stream().anyMatch(entity -> StockoutOrderWorkSpaceEnum.B2B_AREA.name().equals(entity.getWorkspace()))) {
            return;
        }

        //品牌单与非品牌单不能混波次
        List<BdTagMappingEntity> mappingEntityList = bdTagMappingService.list(new LambdaQueryWrapper<BdTagMappingEntity>()
                .in(BdTagMappingEntity::getReferenceNo, orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList()))
                .in(BdTagMappingEntity::getTagName, brandCommonService.getBrandTagNameList()));
        if (!CollectionUtils.isEmpty(mappingEntityList) && mappingEntityList.stream().map(BdTagMappingEntity::getTagName).distinct().count() > 1)
            throw new BusinessServiceException("品牌单不允许混合生成波次！");
        if (!CollectionUtils.isEmpty(mappingEntityList) && mappingEntityList.size() < orderEntityList.size())
            throw new BusinessServiceException("品牌单不允许混合生成波次！");
    }

}
