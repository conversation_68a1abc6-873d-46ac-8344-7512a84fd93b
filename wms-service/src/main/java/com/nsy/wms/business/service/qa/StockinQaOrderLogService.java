package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.enumeration.qa.QaLogTypeEnum;
import com.nsy.api.wms.request.base.LogListRequest;
import com.nsy.api.wms.response.base.LogListResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderLogEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderLogMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单日志业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderLogService extends ServiceImpl<StockinQaOrderLogMapper, StockinQaOrderLogEntity> {

    @Resource
    LoginInfoService loginInfoService;

    @Transactional
    public void addLog(Integer stockinQaOrderId, QaLogTypeEnum qaLogTypeEnum, String content) {
        StockinQaOrderLogEntity logEntity = new StockinQaOrderLogEntity();
        logEntity.setStockinQaOrderId(stockinQaOrderId);
        logEntity.setContent(content);
        logEntity.setIpAddress(loginInfoService.getIpAddress());
        logEntity.setLocation(TenantContext.getTenant());
        logEntity.setType(qaLogTypeEnum.getValue());
        logEntity.setCreateBy(loginInfoService.getName());
        this.save(logEntity);
    }

    public PageResponse<LogListResponse> pageList(LogListRequest request) {
        if (CollectionUtils.isEmpty(request.getIdList()) && Objects.isNull(request.getId())) {
            throw new BusinessServiceException("参数错误");
        }
        PageResponse<LogListResponse> pageResponse = new PageResponse<>();
        Page<StockinQaOrderLogEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinQaOrderLogEntity> pageResult = this.getBaseMapper().pageSearchQaOrderLog(page, request);
        List<StockinQaOrderLogEntity> logEntityList = pageResult.getRecords();
        List<LogListResponse> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(logEntityList)) {
            resultList = logEntityList.stream().map(entity -> {
                LogListResponse log = new LogListResponse();
                BeanUtils.copyProperties(entity, log);
                log.setTypeStr(entity.getType());
                return log;
            }).collect(Collectors.toList());
        }
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    /**
     * 是否存在罚款任务
     *
     * @param stockinQaOrderId
     * @return
     */
    public boolean existPunishments(Integer stockinQaOrderId) {
        LambdaQueryWrapper<StockinQaOrderLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaOrderLogEntity::getStockinQaOrderId, stockinQaOrderId);
        queryWrapper.eq(StockinQaOrderLogEntity::getType, QaLogTypeEnum.PUNISHMENTS.getValue());
        return this.count(queryWrapper) > 0;
    }
}
