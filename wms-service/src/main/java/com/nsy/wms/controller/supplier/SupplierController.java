package com.nsy.wms.controller.supplier;

import com.nsy.business.base.utils.SelectModel;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-05-16 9:43
 */
@RestController
@Api(tags = "入库规则接口")
public class SupplierController extends BaseController {

    @Autowired
    private SupplierService supplierService;

    @ApiOperation(value = "获取部门下拉", notes = "获取部门下拉", produces = "application/json")
    @RequestMapping(value = "/get-supplier-department/list", method = RequestMethod.GET)
    public List<SelectModel> initRules() {
        return supplierService.getSupplierDepartmentList();
    }
}
