package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.response.BdPurchaseOrderLabelDto;
import com.nsy.wms.business.manage.supplier.response.PurchaseOrderItemResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.StockinQaRoutingInspectSkuTypeInfoEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaRoutingInspectSkuTypeInfoMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/22 15:04
 */
@Service
public class StockinQaRoutingInspectSkuTypeInfoService extends ServiceImpl<StockinQaRoutingInspectSkuTypeInfoMapper, StockinQaRoutingInspectSkuTypeInfoEntity> {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private PurchaseOrderLabelService purchaseOrderLabelService;
    @Autowired
    private GcApiService gcApiService;

    public void saveLabelInfo(String purchaseNumber, String sku, Integer stockinQcRoutingInspectId) {
        //获取所有采购标签
        List<BdPurchaseOrderLabelDto> allBdPurchaseOrderLabel = scmApiService.getAllBdPurchaseOrderLabel();
        if (CollectionUtils.isEmpty(allBdPurchaseOrderLabel)) {
            return;
        }
        List<PurchaseOrderItemResponse> productOrderItemList = gcApiService.getPurchaseOrderItemByInfo(Arrays.asList(purchaseNumber.split(",")), sku);
        if (CollectionUtils.isEmpty(productOrderItemList)) {
            return;
        }
        String itemTypes = productOrderItemList.stream().map(PurchaseOrderItemResponse::getItemType).distinct().collect(Collectors.joining(","));
        String label = purchaseOrderLabelService.buildItemType(itemTypes, allBdPurchaseOrderLabel);
        List<StockinQaRoutingInspectSkuTypeInfoEntity> skuTypeInfoEntityList = new ArrayList<>();
        Arrays.stream(label.split(",")).forEach(detail -> {
            if (this.existItem(detail, stockinQcRoutingInspectId)) {
                return;
            }
            StockinQaRoutingInspectSkuTypeInfoEntity entity = new StockinQaRoutingInspectSkuTypeInfoEntity();
            entity.setStockinQcRoutingInspectId(stockinQcRoutingInspectId);
            entity.setLocation(TenantContext.getTenant());
            entity.setCreateBy(loginInfoService.getName());
            entity.setUpdateBy(loginInfoService.getName());
            entity.setSkuType(detail);
            skuTypeInfoEntityList.add(entity);
        });
        this.saveBatch(skuTypeInfoEntityList);
    }

    public boolean existItem(String skuType, Integer stockinQcRoutingInspectId) {
        LambdaQueryWrapper<StockinQaRoutingInspectSkuTypeInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaRoutingInspectSkuTypeInfoEntity::getSkuType, skuType);
        queryWrapper.eq(StockinQaRoutingInspectSkuTypeInfoEntity::getStockinQcRoutingInspectId, stockinQcRoutingInspectId);
        return this.count(queryWrapper) > 0;
    }

    public Map<Integer, List<String>> getMapByQcRoutingInspectId(List<Integer> qcRoutingInspectIdList) {
        if (CollectionUtils.isEmpty(qcRoutingInspectIdList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<StockinQaRoutingInspectSkuTypeInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockinQaRoutingInspectSkuTypeInfoEntity::getStockinQcRoutingInspectId, qcRoutingInspectIdList);
        List<StockinQaRoutingInspectSkuTypeInfoEntity> skuTypeInfoList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(skuTypeInfoList)) {
            return Collections.emptyMap();
        }
        return skuTypeInfoList.stream().collect(Collectors.groupingBy(
            StockinQaRoutingInspectSkuTypeInfoEntity::getStockinQcRoutingInspectId,
            Collectors.mapping(StockinQaRoutingInspectSkuTypeInfoEntity::getSkuType, Collectors.toList())
        ));
    }
}
