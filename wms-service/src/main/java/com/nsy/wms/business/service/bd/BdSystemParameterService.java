package com.nsy.wms.business.service.bd;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.domain.bd.BdSystemParameter;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.request.bd.BdSystemParameterListRequest;
import com.nsy.api.wms.request.bd.BdSystemParameterQueryRequest;
import com.nsy.api.wms.request.bd.BdSystemParameterRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdSystemParameterResponse;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdSystemParameterMapper;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 系统配置表业务实现
 * @date: 2021-07-29 11:35
 */
@Service
public class BdSystemParameterService extends ServiceImpl<BdSystemParameterMapper, BdSystemParameterEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BdSystemParameterService.class);

    @Autowired
    BdChangeLogService changeLogService;

    @Autowired
    BdSystemParameterMapper bdSystemParameterMapper;

    @Cacheable(value = CacheKeyConstant.SYSTEM_PARAMETER_MAP, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public Map<String, String> getBdSystemParameterMap() {
        Map<String, String> result = new HashMap<>();
        this.list(new QueryWrapper<>()).stream().forEach(entity -> result.put(entity.getConfigKey(), entity.getConfigValue()));
        return result;
    }

    @Transactional
    @CacheEvict(value = CacheKeyConstant.SYSTEM_PARAMETER_MAP, allEntries = true)
    public void setSystemParameter(BdSystemParameterRequest request) {
        List<BdSystemParameter> configList = request.getConfigList();
        Validator.isValid(configList, configs -> !CollectionUtils.isEmpty(configs), "入参不能为空");
        configList.stream().forEach(requestParam -> {
            Validator.isValid(requestParam.getConfigKey(), StringUtils::hasText, "配置值不能为空");
            Validator.isValid(requestParam.getConfigName(), StringUtils::hasText, "配置名不能为空");
        });

        List<BdSystemParameterEntity> collect = configList.stream().map(requestParam -> {
            BdSystemParameterEntity entity = this.getByKey(requestParam.getConfigKey());
            if (Objects.isNull(entity)) {
                entity = new BdSystemParameterEntity();
                entity.setLocation(TenantContext.getTenant());
            }
            BeanUtilsEx.copyProperties(requestParam, entity);
            return entity;
        }).collect(Collectors.toList());
        this.saveOrUpdateBatch(collect);
    }

    public String getCacheByKey(String key) {
        return this.getBdSystemParameterMap().get(key);
    }

    public BdSystemParameterEntity getByKey(String key) {
        return this.getOne(new QueryWrapper<BdSystemParameterEntity>().lambda().eq(BdSystemParameterEntity::getConfigKey, key).last("limit 1"));
    }

    public BdSystemParameterResponse findByKey(String key) {
        BdSystemParameterResponse response = new BdSystemParameterResponse();
        response.setConfigKey(key);
        response.setConfigValue(this.getBdSystemParameterMap().get(key));
        return response;
    }

    public Map<String, Object> getSystemParameter(BdSystemParameterQueryRequest request) {
        Validator.isValid(request.getConfigKey(), strings -> !CollectionUtils.isEmpty(strings), "配置健不能为空");
        Map<String, Object> result = new HashMap<>();
        this.list(new QueryWrapper<BdSystemParameterEntity>().lambda().in(BdSystemParameterEntity::getConfigKey, request.getConfigKey())).stream().forEach(entity -> {
            result.put(entity.getConfigKey(), entity.getConfigValue());
        });
        return result;
    }

    public PageResponse<BdSystemParameterResponse> getSystemParameterList(BdSystemParameterListRequest request) {
        PageResponse<BdSystemParameterResponse> pageResponse = new PageResponse<>();
        LambdaQueryWrapper<BdSystemParameterEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .likeRight(StringUtils.hasText(request.getConfigKey()), BdSystemParameterEntity::getConfigKey, request.getConfigKey())
                .likeRight(StringUtils.hasText(request.getConfigName()), BdSystemParameterEntity::getConfigName, request.getConfigName())
                .eq(StringUtils.hasText(request.getConfigValue()), BdSystemParameterEntity::getConfigValue, request.getConfigValue());
        queryWrapper.orderByDesc(BdSystemParameterEntity::getId);
        Page<BdSystemParameterEntity> page = bdSystemParameterMapper.selectPage(
                new Page<>(request.getPageIndex(), request.getPageSize()), queryWrapper);
        List<BdSystemParameterEntity> records = page.getRecords();
        List<BdSystemParameterResponse> resultList = records.stream().map(entity -> {
            BdSystemParameterResponse response = new BdSystemParameterResponse();
            BeanUtilsEx.copyProperties(entity, response);
            return response;
        }).collect(Collectors.toList());
        pageResponse.setContent(resultList);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    @Transactional
    public void addSystemParameter(BdSystemParameterListRequest request) {
        LambdaQueryWrapper<BdSystemParameterEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdSystemParameterEntity::getConfigKey, request.getConfigKey());
        BdSystemParameterEntity one = this.getOne(queryWrapper);
        if (!Objects.isNull(one)) {
            String configKey = one.getConfigKey();
            throw new BusinessServiceException(String.format("参数【%s】已存在", configKey));
        } else {
            BdSystemParameterEntity entity = new BdSystemParameterEntity();
            entity.setConfigKey(request.getConfigKey());
            entity.setConfigName(request.getConfigName());
            entity.setConfigValue(request.getConfigValue());
            entity.setLocation(TenantContext.getTenant());
            this.save(entity);
        }
    }

    @Transactional
    public void updateSystemParameter(BdSystemParameterListRequest request) {
        LambdaUpdateWrapper<BdSystemParameterEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(BdSystemParameterEntity::getConfigKey, request.getConfigKey())
                .set(BdSystemParameterEntity::getConfigName, request.getConfigName())
                .set(BdSystemParameterEntity::getConfigValue, request.getConfigValue());
        this.update(updateWrapper);
    }

    /**
     * 获取值
     *
     * @param systemParameterEnum
     * @return
     */
    public String getConfigValue(BdSystemParameterEnum systemParameterEnum) {
        BdSystemParameterEntity bdSystemParameterEntity = this.getByKey(systemParameterEnum.getKey());
        if (Objects.isNull(bdSystemParameterEntity)) {
            throw new BusinessServiceException(String.format("找不到系统参数 %s", systemParameterEnum.getName()));
        }
        String value = bdSystemParameterEntity.getConfigValue();
        if (StrUtil.isEmpty(value)) {
            throw new BusinessServiceException(String.format("系统参数未配置 %s", systemParameterEnum.getName()));
        }

        return value;
    }

    /**
     * 获取整数值
     *
     * @param systemParameterEnum
     * @return
     */
    public Integer getIntValue(BdSystemParameterEnum systemParameterEnum) {
        String configValue = getConfigValue(systemParameterEnum);
        if (!NumberUtil.isNumber(configValue)) {
            throw new BusinessServiceException(String.format("系统参数非整数 %s", systemParameterEnum.getName()));
        }
        return Integer.valueOf(configValue);
    }

    /**
     * 获取布尔值
     *
     * @param systemParameterEnum
     * @return
     */
    public Boolean getBooleanValue(BdSystemParameterEnum systemParameterEnum) {
        String configValue = getConfigValue(systemParameterEnum);
        boolean isBoolean = configValue.matches("(?i)true|false");
        if (!isBoolean) {
            throw new BusinessServiceException(String.format("系统参数非布尔值 %s", systemParameterEnum.getName()));
        }
        return Boolean.valueOf(configValue);
    }

    /**
     * 获取BigDecimal值
     *
     * @param systemParameterEnum
     * @return
     */
    public BigDecimal getBigDecimalValue(BdSystemParameterEnum systemParameterEnum) {
        String configValue = getConfigValue(systemParameterEnum);
        if (!NumberUtils.isParsable(configValue))
            throw new BusinessServiceException(String.format("系统参数非数字类型 %s", systemParameterEnum.getName()));
        return new BigDecimal(configValue);
    }


    public String findConfigValue(BdSystemParameterEnum systemParameterEnum) {
        BdSystemParameterEntity bdSystemParameterEntity = this.getByKey(systemParameterEnum.getKey());
        if (Objects.isNull(bdSystemParameterEntity)) {
            LOGGER.error("找不到系统参数 {} ", systemParameterEnum.getName());
            return null;
        }
        String value = bdSystemParameterEntity.getConfigValue();
        if (StrUtil.isEmpty(value)) {
            LOGGER.error("系统参数未配置 {} ", systemParameterEnum.getName());
            return null;
        }

        return value;
    }
}
