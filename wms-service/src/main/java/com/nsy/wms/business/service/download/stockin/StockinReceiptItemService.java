package com.nsy.wms.business.service.download.stockin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.nsy.api.wms.domain.stockin.ShelveTaskItemInfo;
import com.nsy.api.wms.domain.supplier.DeliveryCheckInfoDto;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptSummaryRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptItemListInfo;
import com.nsy.api.wms.response.stockin.StockinReceiptItemListInfoResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptListInfo;
import com.nsy.api.wms.response.stockin.StockinReceiptListResponse;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinReturnProductService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockinReceiptItemService implements IDownloadService {
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    private StockinReturnProductService stockinReturnProductService;
    @Autowired
    private StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    private GcApiService gcApiService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_ORDER_RECEIPT_ITEM_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        StockinReceiptListRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinReceiptListRequest.class);
        request.setLocation(TenantContext.getTenant());
        Page<StockinReceiptListResponse> page = new Page<>(downloadRequest.getPageIndex(), downloadRequest.getPageSize());
        if (Objects.nonNull(request.getPurchaseUserId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getPurchaseUserId());
            request.setPurchaseUserName(userInfo.getUserAccount());
        }
        if (Objects.nonNull(request.getModelMerchandiserEmpId())) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(request.getModelMerchandiserEmpId());
            request.setModelMerchandiserEmpName(userInfo.getUserName());
        }
        page.setSearchCount(false);
        List<StockinReceiptListInfo> pageInfoList = stockinOrderService.getBaseMapper().pageItemSearchReceiptList(page, request);
        // 分公司的需要找出真实入库单
        List<Integer> stockinOrderIdList = pageInfoList.stream().map(t -> Objects.equals(t.getOrderType(), 1) && t.getRealStockinOrderId() != null && t.getRealStockinOrderId() > 0 ? t.getRealStockinOrderId() : t.getStockinOrderId()).distinct().collect(Collectors.toList());
        DownloadResponse response = new DownloadResponse();
        if (CollectionUtils.isEmpty(stockinOrderIdList)) {
            return response;
        }
        List<String> stockinOrderNoList = stockinOrderService.getBaseMapper().searchStockinOrderNoByStockinOrderIdListIgnoreTenant(stockinOrderIdList);
        List<StockinReceiptItemListInfoResponse> responseList = getReceiptItemListInfo(stockinOrderIdList, stockinOrderNoList);
        response.setDataJsonStr(JsonMapper.toJson(responseList));
        response.setTotalCount((long) stockinOrderService.getBaseMapper().pageSearchReceiptListCount(request));
        return response;
    }

    private List<StockinReceiptItemListInfoResponse> getReceiptItemListInfo(List<Integer> stockinOrderIdList, List<String> stockinOrderNoList) {
        List<StockinReceiptItemListInfo> itemInfoList = stockinOrderService.getBaseMapper().listSearchReceiptItemList(stockinOrderIdList);
        if (CollectionUtils.isEmpty(itemInfoList))
            return Collections.emptyList();
        // 待退货数
        StockinReceiptSummaryRequest stockinReceiptSummaryRequest = new StockinReceiptSummaryRequest();
        stockinReceiptSummaryRequest.setStockinOrderNoList(stockinOrderNoList);
        Map<String, List<StockinReturnProductEntity>> returnProductEntityMap = stockinReturnProductService.getBaseMapper().findAllByReceiptSummaryRequestIgnoreTenant(stockinReceiptSummaryRequest)
            .stream().collect(Collectors.groupingBy(t -> String.format("%s##%s##%s", t.getStockinOrderNo(), t.getPurchasePlanNo(), t.getSku())));
        // 已上架数 + 总退货数
        Map<String, List<ShelveTaskItemInfo>> shelveTaskItemInfoMap = stockinShelveTaskItemService.getBaseMapper().searchShelveTaskItemList(stockinOrderIdList, StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name()).stream()
            .collect(Collectors.groupingBy(t -> String.format("%s##%s##%s", t.getSourceId(), t.getPurchasePlanNo(), t.getSku())));
        // 发货数
        List<Integer> taskIdList = itemInfoList.stream().map(StockinReceiptItemListInfo::getTaskId).distinct().collect(Collectors.toList());
        Map<String, List<StockinOrderTaskItemEntity>> taskItemEntityMap = stockinOrderTaskItemService.getBaseMapper().findAllByTaskIdListIgnoreTenant(taskIdList).stream()
            .collect(Collectors.groupingBy(t -> String.format("%s##%s##%s", t.getTaskId(), t.getPurchasePlanNo(), t.getSku())));

        //获取核对时间
        List<String> supplierDeliveryNoList = itemInfoList.stream().map(StockinReceiptItemListInfo::getSupplierDeliveryNo).collect(Collectors.toList());
        List<DeliveryCheckInfoDto> receiveCheckInfo = gcApiService.getReceiveCheckInfo(supplierDeliveryNoList);
        Map<String, List<DeliveryCheckInfoDto>> receiveCheckInfoMap = receiveCheckInfo.stream().collect(Collectors.groupingBy(t -> String.format("%s##%s##%s", t.getBoxNo(), t.getPurchaseOrderPlanNo(), t.getSku())));


        return itemInfoList.stream().map(itemInfo -> {
            StockinReceiptItemListInfoResponse response = new StockinReceiptItemListInfoResponse();
            BeanUtils.copyProperties(itemInfo, response);
            int waitReturnQty = returnProductEntityMap.getOrDefault(String.format("%s##%s##%s", itemInfo.getStockinOrderNo(), itemInfo.getPurchasePlanNo(), itemInfo.getSku()), Lists.newArrayList()).stream()
                .filter(t -> Objects.nonNull(t.getReturnQty())).mapToInt(StockinReturnProductEntity::getReturnQty).sum();
            List<ShelveTaskItemInfo> shelveTaskItemInfoList = shelveTaskItemInfoMap.getOrDefault(String.format("%s##%s##%s", itemInfo.getStockinOrderId(), itemInfo.getPurchasePlanNo(), itemInfo.getSku()), Lists.newArrayList());
            List<ShelveTaskItemInfo> sortShelveTaskItemInfoList = shelveTaskItemInfoList.stream().sorted(Comparator.comparing(ShelveTaskItemInfo::getShelvedDate, Comparator.nullsFirst(Date::compareTo)).reversed()).collect(Collectors.toList());
            int shipmentQty = taskItemEntityMap.getOrDefault(String.format("%s##%s##%s", itemInfo.getTaskId(), itemInfo.getPurchasePlanNo(), itemInfo.getSku()), Lists.newArrayList()).stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
            int shelvedQty = itemInfo.getShelvedQty();
            int returnedQty = itemInfo.getReturnedQty();
            response.setStatusStr(StockinOrderItemStatusEnum.getDescByName(itemInfo.getStatus()));
            response.setWaitReturnQty(waitReturnQty);
            response.setShelvedTotalQty(shelvedQty);
            response.setShelvedQty(Math.max(shelvedQty - response.getConcessionsQty(), 0));
            response.setShipmentQty(shipmentQty);
            response.setReturnedQty(returnedQty - waitReturnQty);
            response.setDifferenceQty(Math.abs(shelvedQty + returnedQty - response.getShipmentQty()));
            response.setSettlementQty(Math.min(shelvedQty, response.getShipmentQty() - returnedQty));
            response.setShelveDate(CollectionUtils.isEmpty(sortShelveTaskItemInfoList) ? null : sortShelveTaskItemInfoList.get(0).getShelvedDate());
            List<DeliveryCheckInfoDto> deliveryCheckInfoDtos = receiveCheckInfoMap.get(String.format("%s##%s##%s", itemInfo.getSupplierDeliveryBoxCode(), itemInfo.getPurchasePlanNo(), itemInfo.getSku()));
            if (!CollectionUtils.isEmpty(deliveryCheckInfoDtos))
                response.setReceiveCheckTime(deliveryCheckInfoDtos.get(0).getReceiveCheckTime());
            return response;
        }).collect(Collectors.toList());
    }

}
