package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/27 10:27
 */
@ApiModel(value = "StockoutShipmentChangePackageTaskPageResponse", description = "打包任务分页查询响应")
public class StockoutShipmentChangePackageTaskPageResponse {

    @ApiModelProperty(value = "任务id", name = "taskId")
    private Integer taskId;

    /**
     * 地区
     */
    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    /**
     * 装箱清单id
     */
    @ApiModelProperty(value = "装箱清单id", name = "shipmentId")
    private Integer shipmentId;

    /**
     * 箱子编号
     */
    @ApiModelProperty(value = "箱子编号", name = "shipmentBoxCode")
    private String shipmentBoxCode;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", name = "qty")
    private Integer qty;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", name = "operator")
    private String operator;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间", name = "operateDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateDate;

    /**
     * 工作区
     */
    @ApiModelProperty(value = "工作区", name = "workspace")
    private String workspace;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(Integer shipmentId) {
        this.shipmentId = shipmentId;
    }

    public String getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }
} 