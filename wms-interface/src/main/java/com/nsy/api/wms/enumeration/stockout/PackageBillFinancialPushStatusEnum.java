package com.nsy.api.wms.enumeration.stockout;

import java.util.Arrays;
import java.util.Objects;

public enum PackageBillFinancialPushStatusEnum {

    // 财务推送状态(0-未推送,1-已推送,2-已二次推送)
    NOT_PUSH("未推送", 0), PUSHED("已推送", 1), SECOND_PUSHED("已二次推送", 2);

    private final String desc;

    private final Integer value;

    PackageBillFinancialPushStatusEnum(String desc, Integer value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getValue() {
        return value;
    }

    public static PackageBillFinancialPushStatusEnum resolveByValue(Integer value) {
        return Arrays.stream(PackageBillFinancialPushStatusEnum.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }
}
