package com.nsy.wms.repository.jpa.mapper.bd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.bd.BdAqlRuleListRequest;
import com.nsy.api.wms.response.bd.BdAqlRuleListResponse;
import com.nsy.wms.repository.entity.bd.BdAqlRuleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * AQL规则Mapper接口
 */
@Mapper
public interface BdAqlRuleMapper extends BaseMapper<BdAqlRuleEntity> {

    /**
     * 获取规则列表
     *
     * @param request 查询参数
     * @return 规则列表
     */
    IPage<BdAqlRuleListResponse> getRuleList(IPage page, @Param("request") BdAqlRuleListRequest request);
} 