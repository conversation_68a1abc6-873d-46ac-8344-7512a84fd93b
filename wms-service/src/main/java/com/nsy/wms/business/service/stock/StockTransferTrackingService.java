package com.nsy.wms.business.service.stock;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferTrackingStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferTrackingTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stock.StockTransferTrackingDetailRequest;
import com.nsy.api.wms.request.stock.StockTransferTrackingItemRequest;
import com.nsy.api.wms.request.stock.StockTransferTrackingPageRequest;
import com.nsy.api.wms.request.stock.StockTransferTrackingSkuRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockTrackingInfoResponse;
import com.nsy.api.wms.response.stock.StockTrackingItemInfoResponse;
import com.nsy.api.wms.response.stock.StockTransferTrackingDetailResponse;
import com.nsy.api.wms.response.stock.StockTransferTrackingSkuResponse;
import com.nsy.wms.business.manage.erp.ErpTransferApiService;
import com.nsy.wms.business.manage.erp.request.ErpTransferStockInfo;
import com.nsy.wms.business.manage.erp.request.ErpTransferStockInfoRequest;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.manage.user.upload.StockoutHuiZhouSpaceShelvedImport;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.internal.common.DistributedModuleService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.CangsouOverseaSpaceTrackService;
import com.nsy.wms.business.service.stockout.StockAmfOverseaSpaceTrackService;
import com.nsy.wms.business.service.stockout.StockDianCangOverseaSpaceTrackService;
import com.nsy.wms.business.service.stockout.StockJiufangyuanCangOverseaSpaceTrackService;
import com.nsy.wms.business.service.stockout.StockZuoHaiSpaceTrackService;
import com.nsy.wms.business.service.stockout.StockoutGoodCangTrackService;
import com.nsy.wms.business.service.stockout.StockoutLingXingTrackService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentPackItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.WinitOverseaSpaceTrackService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockTransferTrackingEntity;
import com.nsy.wms.repository.entity.stock.StockTransferTrackingItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockTransferTrackingItemMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockTransferTrackingMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HXD
 * 2022/8/31
 **/
@Service
public class StockTransferTrackingService extends ServiceImpl<StockTransferTrackingMapper, StockTransferTrackingEntity> implements IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockTransferTrackingService.class);
    @Autowired
    private StockTransferTrackingItemService transferTrackingItemService;
    @Autowired
    private StockTransferTrackingMapper transferTrackingMapper;
    @Autowired
    private BdSpaceService spaceService;
    @Autowired
    private ProductSpecInfoService specInfoService;
    @Autowired
    LoginInfoService loginInfoService;
    @Resource
    private StockTransferTrackingItemMapper stockTransferTrackingItemMapper;
    @Autowired
    private StockTransferTrackingService trackingService;
    @Autowired
    StockoutShipmentPackItemService packItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockService stockService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    ErpTransferApiService erpTransferApiService;
    @Autowired
    DistributedModuleService distributedModuleService;
    @Autowired
    StockoutGoodCangTrackService goodCangTrackService;
    @Autowired
    StockZuoHaiSpaceTrackService zuoHaiSpaceTrackService;
    @Autowired
    CangsouOverseaSpaceTrackService cangsouOverseaSpaceTrackService;
    @Autowired
    WinitOverseaSpaceTrackService winitOverseaSpaceTrackService;
    @Autowired
    StockDianCangOverseaSpaceTrackService dianCangTrackService;
    @Autowired
    StockAmfOverseaSpaceTrackService amfOverseaSpaceTrackService;
    @Autowired
    StockoutLingXingTrackService lingXingTrackService;
    @Autowired
    StockJiufangyuanCangOverseaSpaceTrackService jiufangyuanCangOverseaSpaceTrackService;
    @Autowired
    TmsCacheService tmsCacheService;
    @Autowired
    private BdErpSpaceMappingService erpSpaceMappingService;

    public PageResponse<StockTrackingInfoResponse> pageList(StockTransferTrackingPageRequest pageRequest) {
        IPage<StockTrackingInfoResponse> response = transferTrackingMapper.findPage(new Page<>(pageRequest.getPageIndex(), pageRequest.getPageSize()), pageRequest);
        PageResponse<StockTrackingInfoResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        pageResponse.getContent().forEach(resp -> {
            resp.setStatusStr(StockTransferTrackingStatusEnum.getStatusByName(resp.getStatus()));
            List<StockTransferTrackingItemEntity> itemList = transferTrackingItemService.findByTrackingId(resp.getId());
            List<Integer> spaceIds = itemList.stream().map(StockTransferTrackingItemEntity::getSpaceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(spaceIds)) {
                List<BdSpaceEntity> bdSpaceEntities = spaceService.listBySpaceIdList(spaceIds);
                resp.setSpaceName(bdSpaceEntities.stream().map(BdSpaceEntity::getSpaceName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
            }
        });
        return pageResponse;
    }

    public PageResponse<StockTrackingItemInfoResponse> itemList(StockTransferTrackingItemRequest request) {
        LambdaQueryWrapper<StockTransferTrackingItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockTransferTrackingItemEntity::getTrackingId, request.getTrackId());
        StockTransferTrackingEntity track = getById(request.getTrackId());
        Page<StockTransferTrackingItemEntity> page = transferTrackingItemService.page(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper);
        PageResponse<StockTrackingItemInfoResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(new ArrayList<>());
        pageResponse.setTotalCount(page.getTotal());
        if (track == null) {
            return pageResponse;
        }
        page.getRecords().forEach(item -> {
            StockTrackingItemInfoResponse response = new StockTrackingItemInfoResponse();
            BeanUtilsEx.copyProperties(item, response);
            ProductSpecInfoEntity topBySku = specInfoService.findTopBySku(item.getSku());
            if (topBySku != null) {
                response.setImageUrl(topBySku.getImageUrl());
                response.setPreviewImageUrl(topBySku.getPreviewImageUrl());
                response.setThumbnailImageUrl(topBySku.getThumbnailImageUrl());
            }
            if (StockTransferTrackingStatusEnum.SHELVED.name().equalsIgnoreCase(track.getStatus())) {
                response.setDifferQty(item.getShelvedQty() - item.getStockOutQty());
            } else {
                response.setDifferQty(0);
            }
            pageResponse.getContent().add(response);
        });
        return pageResponse;
    }


    // 在途库存跟踪
    public void startTracking(StockTransferTrackingEntity track) {
        try {
            LOGGER.info("{}开始在途追踪", track.getOrderNo());
            if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.GOOD_CANG_OVERSEA.name())) {
                // 追踪谷仓入库单状态
                goodCangTrackService.tracking(track);
            } else if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.ZUO_HAI_SPACE.name())) {
                zuoHaiSpaceTrackService.tracking(track);
            } else if (StrUtil.equalsAnyIgnoreCase(track.getTransferType(), StockTransferTrackingTypeEnum.TEMU_OVERSEA_SPACE.name(),
                    StockTransferTrackingTypeEnum.TIKTOK_SPACE.name(), StockTransferTrackingTypeEnum.PLATFORM_B2C_TK_SPACE.name(), StockTransferTrackingTypeEnum.ZHIYUN_SPACE.name(),
                    StockTransferTrackingTypeEnum.B2B_TK_SPACE.name(), StockTransferTrackingTypeEnum.DOMESTIC_SHEWIN_TK_SPACE.name())) {
                dianCangTrackService.tracking(track);
            } else if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.CANGSOU_OVERSEA_SPACE.name())) {
                cangsouOverseaSpaceTrackService.tracking(track);
            } else if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.WINIT_CANGSOU_OVERSEA_SPACE.name())) {
                winitOverseaSpaceTrackService.tracking(track);
            } else if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.AFM_OVERSEA.name())) {
                amfOverseaSpaceTrackService.tracking(track);
            } else if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.LING_XING.name())) {
                lingXingTrackService.tracking(track);
            } else if (Objects.equals(track.getTransferType(), StockTransferTrackingTypeEnum.B2C_JIUFANGYUNCANG_OVERSEA.name())) {
                jiufangyuanCangOverseaSpaceTrackService.tracking(track);
            } else {
                LOGGER.error("{}无法跟踪", track.getOrderNo());
            }
        } catch (Exception e) {
            LOGGER.error(JsonMapper.toJson(track) + "在途跟踪失败：", e);
        }
    }


    public void startTrackingList(List<String> transferType) {
        LambdaQueryWrapper<StockTransferTrackingEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockTransferTrackingEntity::getTransferType, transferType);
        wrapper.and(wp -> wp.eq(StockTransferTrackingEntity::getStatus, StockTransferTrackingStatusEnum.TRANSPORT.name())
                .or().eq(StockTransferTrackingEntity::getStatus, StockTransferTrackingStatusEnum.RECEIVED.name()));
        List<StockTransferTrackingEntity> waitTrackingList = list(wrapper);
        waitTrackingList.forEach(track -> trackingService.startTracking(track));
    }

    // 创建在途数据
    public StockTransferTrackingEntity createNormalTracking(StockoutOrderEntity orderEntity, String type, String positionCode, Integer spaceId) {
        List<StockoutShipmentItemEntity> itemList = shipmentItemService.findItemByStockoutOrderNo(orderEntity.getStockoutOrderNo());
        StockTransferTrackingEntity tracking = trackingService.getByOrderNo(itemList.get(0).getOrderNo());
        if (tracking != null) {
            throw new BusinessServiceException("已经有在途订单，请核对");
        }
        List<Integer> shipmentId = itemList.stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = shipmentService.listByIds(shipmentId);
        StockTransferTrackingEntity createTrack = new StockTransferTrackingEntity();
        createTrack.setOrderNo(itemList.get(0).getOrderNo());
        createTrack.setTransferType(type);
        createTrack.setStatus(StockTransferTrackingStatusEnum.TRANSPORT.name());
        createTrack.setCreateBy(loginInfoService.getName());
        createTrack.setLogisticsCompany(shipmentList.stream().map(StockoutShipmentEntity::getLogisticsCompany).filter(StringUtils::hasText).findFirst().orElse(null));
        createTrack.setLogisticsNo(shipmentList.stream().map(StockoutShipmentEntity::getLogisticsNo).filter(StringUtils::hasText).findFirst().orElse(null));
        createTrack.setForwarderChannel(shipmentList.stream().map(StockoutShipmentEntity::getForwarderChannel).filter(StringUtils::hasText).findFirst().orElse(null));
        createTrack.setLocation(TenantContext.getTenant());
        save(createTrack);
        Map<String, Integer> skuMap = new HashMap<>();
        itemList.forEach(boxItem -> skuMap.merge(boxItem.getSku(), boxItem.getQty(), Integer::sum));
        skuMap.forEach((sku, qty) -> {
            if (qty <= 0) {
                return;
            }
            StockTransferTrackingItemEntity itemEntity = StockoutBuilding.buildTrackingItem(sku, qty, createTrack, spaceId, positionCode);
            transferTrackingItemService.save(itemEntity);
        });
        return createTrack;
    }


    public StockTrackingInfoResponse detail(Integer trackingId) {
        StockTransferTrackingEntity trackingEntity = getById(trackingId);
        if (trackingEntity == null) {
            throw new BusinessServiceException("找不到对应的在途信息，请核对");
        }
        StockTrackingInfoResponse response = new StockTrackingInfoResponse();
        BeanUtilsEx.copyProperties(trackingEntity, response);
        response.setTransferTypeStr(StockTransferTrackingTypeEnum.getTypeByName(trackingEntity.getTransferType()));
        response.setStatusStr(StockTransferTrackingStatusEnum.getStatusByName(response.getStatus()));
        List<StockTransferTrackingItemEntity> itemList = transferTrackingItemService.findByTrackingId(response.getId());
        List<Integer> spaceIds = itemList.stream().map(StockTransferTrackingItemEntity::getSpaceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(spaceIds)) {
            List<BdSpaceEntity> bdSpaceEntities = spaceService.listBySpaceIdList(spaceIds);
            response.setSpaceName(bdSpaceEntities.stream().map(BdSpaceEntity::getSpaceName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        }
        return response;
    }

    /**
     * 根据订单号获取
     *
     * @param orderNo 订单号
     * @return 在途单
     */
    public StockTransferTrackingEntity getByOrderNo(String orderNo) {
        return this.getOne(new QueryWrapper<StockTransferTrackingEntity>().lambda().eq(StockTransferTrackingEntity::getOrderNo, orderNo).last(MybatisQueryConstant.QUERY_FIRST));
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_TRACKING_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockTransferTrackingPageRequest downloadRequest = JSONObject.parseObject(request.getRequestContent(), StockTransferTrackingPageRequest.class);
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        IPage<StockTrackingInfoExportResponse> pageResponse = transferTrackingMapper.findExportInfo(
                new Page<>(request.getPageIndex(), request.getPageSize()), downloadRequest);
        pageResponse.getRecords().forEach(resp -> {
            resp.setStatusStr(StockTransferTrackingStatusEnum.getStatusByName(resp.getStatus()));
        });
        response.setTotalCount((long) (pageResponse.getRecords().size() >= request.getPageSize()
                ? (request.getPageIndex() + 1) * request.getPageSize()
                : (request.getPageIndex() - 1) * request.getPageSize() + pageResponse.getRecords().size()));
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getRecords()));
        return response;
    }

    @Transactional
    public void importShelvedSku(List<StockoutHuiZhouSpaceShelvedImport> importList, List<StockoutHuiZhouSpaceShelvedImport> errorList, String req) {
        IdListRequest dateRequest = JsonMapper.fromJson(req, IdListRequest.class);
        if (CollectionUtils.isEmpty(dateRequest.getIdList()) || dateRequest.getIdList().size() > 1) {
            throw new BusinessServiceException("请勾选一条在途数据进行上架，一次导入只允许对一个在途数据进行上架");
        }
        StockTransferTrackingEntity track = trackingService.getById(dateRequest.getIdList().get(0));
        if (StockTransferTrackingStatusEnum.SHELVED.name().equalsIgnoreCase(track.getStatus())) {
            throw new BusinessServiceException("该跟踪单号已经完成上架，无法继续上架");
        }
        List<StockTransferTrackingItemEntity> trackItemList = transferTrackingItemService.findByTrackingId(track.getId());
        Map<String, List<StockTransferTrackingItemEntity>> itemMap = trackItemList.stream().collect(Collectors.groupingBy(StockTransferTrackingItemEntity::getSku));
        String name = loginInfoService.getName();
        List<ErpTransferStockInfo> synErpInfoList = new LinkedList<>();
        importList.forEach(row -> {
            try {
                // 开始上架sku
                startShelved(row, track, itemMap, name, synErpInfoList);
            } catch (Exception e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });
        if (!synErpInfoList.isEmpty()) {
            ErpTransferStockInfoRequest syncErpRequest = new ErpTransferStockInfoRequest();
            syncErpRequest.setUserName(name);
            syncErpRequest.setLocation(TenantContext.getTenant());
            syncErpRequest.setErpTransferStockInfos(synErpInfoList);
            erpTransferApiService.syncSpaceTransferStock(syncErpRequest, track.getId());
        }
        track.setStatus(StockTransferTrackingStatusEnum.SHELVED.name());
        track.setEtlInDate(new Date());
        track.setUpdateBy(name);
        trackingService.updateById(track);
        distributedModuleService.pushTransferInToEtl(track);
    }

    private void startShelved(StockoutHuiZhouSpaceShelvedImport row, StockTransferTrackingEntity track, Map<String, List<StockTransferTrackingItemEntity>> itemMap, String name, List<ErpTransferStockInfo> synErpInfoList) {
        if (row.getQty() == null || !StringUtils.hasText(row.getSku())) {
            throw new BusinessServiceException("sku及上架数量必填");
        }
        if (row.getQty() <= 0) {
            return;
        }
        List<StockTransferTrackingItemEntity> stockTransferTrackingItemEntities = itemMap.get(row.getSku());
        if (CollectionUtils.isEmpty(stockTransferTrackingItemEntities)) {
            throw new BusinessServiceException(track.getOrderNo() + "未找到跟踪明细sku：" + row.getSku());
        }
        if (!StockTransferTrackingStatusEnum.TRANSPORT.name().equals(track.getStatus())) {
            throw new BusinessServiceException("非在途数据无法导入上架");
        }
        Integer qty = row.getQty();
        for (StockTransferTrackingItemEntity item : stockTransferTrackingItemEntities) {
            int oldShelvedQty = item.getShelvedQty() == null ? 0 : item.getShelvedQty();
            if (item.getStockOutQty() >= row.getQty() + oldShelvedQty) {
                // 如果出库数大于等于上架数，直接回填。否则回填下一个明细
                item.setReceivedQty(row.getQty() + oldShelvedQty);
                item.setShelvedQty(row.getQty() + oldShelvedQty);
                item.setUpdateBy(name);
                row.setQty(0);
                updateStock(qty, item.getShelvedPositionCode(), item.getSku());
                break;
            }
            item.setReceivedQty(item.getStockOutQty());
            item.setShelvedQty(item.getStockOutQty());
            item.setUpdateBy(name);
            row.setQty(row.getQty() - item.getStockOutQty() + oldShelvedQty);
            updateStock(item.getStockOutQty() - oldShelvedQty, item.getShelvedPositionCode(), item.getSku());
        }
        if (row.getQty() > 0) {
            stockTransferTrackingItemEntities.get(0).setShelvedQty(row.getQty() + stockTransferTrackingItemEntities.get(0).getShelvedQty());
            stockTransferTrackingItemEntities.get(0).setReceivedQty(stockTransferTrackingItemEntities.get(0).getShelvedQty());
        }
        updateStock(row.getQty(), stockTransferTrackingItemEntities.get(0).getShelvedPositionCode(), stockTransferTrackingItemEntities.get(0).getSku());
        transferTrackingItemService.updateBatchById(stockTransferTrackingItemEntities);
        synErpInfoList.add(new ErpTransferStockInfo(stockTransferTrackingItemEntities.get(0).getSpaceId(), stockTransferTrackingItemEntities.get(0).getShelvedPositionCode(), stockTransferTrackingItemEntities.get(0).getSku(), qty));
    }

    private void updateStock(Integer qty, String positionCode, String sku) {
        StockUpdateRequest request = new StockUpdateRequest();
        request.setQty(qty);
        request.setPositionCode(positionCode);
        request.setSku(sku);
        request.setChangeLogType(StockChangeLogTypeEnum.POSITION_IN);
        request.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_INSIDE);
        stockService.updateStock(request);
    }

    /**
     * 查询对应订单是否推送，如果推送返回对应的订单号
     *
     * @param orderNoList
     * @return
     */
    public List<String> getPushOrderNo(List<String> orderNoList) {
        LambdaQueryWrapper<StockTransferTrackingEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockTransferTrackingEntity::getOrderNo, orderNoList);
        List<StockTransferTrackingEntity> trackingEntityList = this.list(wrapper);
        if (CollectionUtils.isEmpty(trackingEntityList)) {
            return Collections.emptyList();
        }
        return trackingEntityList.stream().map(StockTransferTrackingEntity::getOrderNo).distinct().collect(Collectors.toList());
    }

    /**
     * 查询sku仓库在途库存
     *
     * @param request 请求参数
     * @return 在途库存信息
     */
    public List<StockTransferTrackingSkuResponse> queryOnTheWayStock(StockTransferTrackingSkuRequest request) {
        List<StockTransferTrackingSkuResponse> responseList = new ArrayList<>(request.getSkuInfoList() != null ? request.getSkuInfoList().size() : 10);

        if (CollectionUtils.isEmpty(request.getSkuInfoList())) {
            return responseList;
        }

        // 遍历每个SKU信息
        for (StockTransferTrackingSkuRequest.SkuInfo skuInfo : request.getSkuInfoList()) {
            String sku = skuInfo.getSku();
            List<Integer> erpSpaceIdList = skuInfo.getErpSpaceIdList();
            List<Integer> spaceIdList = new ArrayList<>();

            // 如果erpSpaceIdList为空，则不进行过滤，查询所有仓库
            if (!CollectionUtils.isEmpty(erpSpaceIdList)) {
                // 将ERP仓库ID转换为WMS仓库ID
                for (Integer erpSpaceId : erpSpaceIdList) {
                    try {
                        BdErpSpaceMappingEntity mappingEntity = erpSpaceMappingService.getEntityByErpSpaceId(erpSpaceId);
                        if (mappingEntity != null && mappingEntity.getSpaceId() != null) {
                            spaceIdList.add(mappingEntity.getSpaceId());
                        }
                    } catch (Exception e) {
                        LOGGER.error("根据ERP仓库ID[{}]获取WMS仓库ID失败", erpSpaceId, e);
                    }
                }
            }

            // 查询在途库存
            List<StockTransferTrackingSkuResponse.SpaceInfo> spaceInfoList =
                    stockTransferTrackingItemMapper.queryOnTheWayStock(sku, spaceIdList);

            // 将结果中的spaceId转换为erpSpaceId
            if (!CollectionUtils.isEmpty(spaceInfoList)) {
                for (StockTransferTrackingSkuResponse.SpaceInfo spaceInfo : spaceInfoList) {
                    Integer spaceId = spaceInfo.getSpaceId();
                    try {
                        BdErpSpaceMappingEntity mappingEntity = erpSpaceMappingService.getDefaultEntityBySpaceId(spaceId);
                        if (mappingEntity != null && mappingEntity.getErpSpaceId() != null) {
                            spaceInfo.setErpSpaceId(mappingEntity.getErpSpaceId());
                        }
                    } catch (Exception e) {
                        LOGGER.error("根据WMS仓库ID[{}]获取ERP仓库ID失败", spaceId, e);
                    }
                }
            }

            // 如果有在途库存，则添加到响应列表中
            StockTransferTrackingSkuResponse response = new StockTransferTrackingSkuResponse();
            response.setSku(sku);
            response.setSpaceInfoList(spaceInfoList != null ? spaceInfoList : new ArrayList<>(0));
            responseList.add(response);
        }

        return responseList;
    }

    /**
     * 查询仓库在途明细
     *
     * @param request 请求参数
     * @return 在途明细信息
     */
    public List<StockTransferTrackingDetailResponse> queryTransferTrackingDetail(StockTransferTrackingDetailRequest request) {
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            return new ArrayList<>(0);
        }

        // 查询在途明细
        List<StockTransferTrackingDetailResponse> detailResponses = stockTransferTrackingItemMapper.queryTransferTrackingDetail(request.getSkuList());

        // 获取所有物流公司信息
        List<TmsLogisticsCompany> allLogisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList();

        // 处理运输方式和仓库ID映射
        for (StockTransferTrackingDetailResponse response : detailResponses) {
            // 处理运输方式
            if (StringUtils.hasText(response.getLogisticsCompany())) {
                allLogisticsCompanyList.stream()
                        .filter(item -> item.getLogisticsCompany().equalsIgnoreCase(response.getLogisticsCompany()))
                        .findFirst()
                        .ifPresent(item -> {
                            if ("SEA_FREIGHT_FORWARDER".equalsIgnoreCase(item.getChannelType()) || "海运货代".equalsIgnoreCase(item.getChannelType())) {
                                response.setShippingType("海运");
                            } else if ("AIR_FREIGHT_FORWARDER".equalsIgnoreCase(item.getChannelType()) || "空运货代".equalsIgnoreCase(item.getChannelType())) {
                                response.setShippingType("空运");
                            }
                        });
            }

            // 将spaceId转换为erpSpaceId
            Integer spaceId = response.getSpaceId();
            try {
                BdErpSpaceMappingEntity mappingEntity = erpSpaceMappingService.getDefaultEntityBySpaceId(spaceId);
                if (mappingEntity != null && mappingEntity.getErpSpaceId() != null) {
                    response.setErpSpaceId(mappingEntity.getErpSpaceId());
                }
            } catch (Exception e) {
                LOGGER.error("根据WMS仓库ID[{}]获取ERP仓库ID失败", spaceId, e);
            }
        }

        return detailResponses;
    }
}
