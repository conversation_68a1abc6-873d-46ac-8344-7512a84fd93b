package com.nsy.wms.business.service.bd;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stock.StockSkuPositionInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.wms.business.domain.bo.stock.StockReplenishmentTaskGenerateBo;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockReplenishmentTaskService;
import com.nsy.wms.repository.entity.bd.BdStockFifoEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdStockFifoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class BdStockFifoService extends ServiceImpl<BdStockFifoMapper, BdStockFifoEntity> {

    @Resource
    StockReplenishmentTaskService replenishmentTaskService;
    @Resource
    ProductSpecInfoService productSpecInfoService;


    public List<BdStockFifoEntity> findAllEnable() {
        return list(new LambdaQueryWrapper<BdStockFifoEntity>()
                .eq(BdStockFifoEntity::getIsEnable, Boolean.TRUE));
    }

    /**
     * 预配排序
     * 零拣 > 季度 > 库存
     *
     * @param oldList
     * @return
     */
    public List<StockSkuPositionInfo> prematchSort(List<StockSkuPositionInfo> oldList) {
        List<StockSkuPositionInfo> spacePositionList = oldList.stream()
                .filter(positionInfo -> BdPositionTypeEnum.SPARE_POSITION.name().equals(positionInfo.getPositionType()))
                .collect(Collectors.toList());

        //检查是否需要补货
        checkAndGenerateReplenishmentTask(spacePositionList);

        spacePositionList = spacePositionList.stream().sorted(Comparator.comparing(StockSkuPositionInfo::getStock)
                .thenComparing(StockSkuPositionInfo::getSpaceAreaSort)
                .thenComparing(StockSkuPositionInfo::getPositionSort)).collect(Collectors.toList());

        List<StockSkuPositionInfo> otherPositionList = oldList.stream()
                .filter(positionInfo -> !BdPositionTypeEnum.SPARE_POSITION.name().equals(positionInfo.getPositionType()))
                .collect(Collectors.toList());

        Map<Boolean, List<StockSkuPositionInfo>> otherPositionMap = otherPositionList.stream().collect(Collectors.groupingBy(positionInfo -> StrUtil.isEmpty(positionInfo.getQuarter())));
        //没有配置季度的库位
        List<StockSkuPositionInfo> nosetPositionInfoList = otherPositionMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        nosetPositionInfoList = nosetPositionInfoList.stream().sorted(Comparator.comparing(StockSkuPositionInfo::getStock)
                .thenComparing(StockSkuPositionInfo::getSpaceAreaSort)
                .thenComparing(StockSkuPositionInfo::getPositionSort)).collect(Collectors.toList());
        //有配置季度 按季度排序
        List<StockSkuPositionInfo> setPositionInfoList = otherPositionMap.getOrDefault(Boolean.FALSE, new ArrayList<>());
        setPositionInfoList = setPositionInfoList.stream().sorted(Comparator.comparing(StockSkuPositionInfo::getQuarter)
                .thenComparing(StockSkuPositionInfo::getStock)
                .thenComparing(StockSkuPositionInfo::getSpaceAreaSort)
                .thenComparing(StockSkuPositionInfo::getPositionSort)).collect(Collectors.toList());
        spacePositionList.addAll(setPositionInfoList);
        spacePositionList.addAll(nosetPositionInfoList);
        return spacePositionList;

    }

    /**
     * 检查是否需要补货
     *
     * @param spacePositionList
     */
    private void checkAndGenerateReplenishmentTask(List<StockSkuPositionInfo> spacePositionList) {
        List<StockReplenishmentTaskGenerateBo> boList = spacePositionList.stream().map(spacePosition -> {
            if (spacePosition.getStock() > spacePosition.getMinStock())
                return null;

            ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(spacePosition.getSku());
            //撞色不生成补货任务
            if (ObjectUtil.isNotNull(productSpecInfo) && 1 == productSpecInfo.getIsClash())
                return null;

            StockReplenishmentTaskGenerateBo bo = new StockReplenishmentTaskGenerateBo();
            bo.setSku(spacePosition.getSku());
            bo.setToPositionCode(spacePosition.getPositionCode());
            bo.setToPositionId(spacePosition.getPositionId());
            return bo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        replenishmentTaskService.generateTask(boList);
    }
}
