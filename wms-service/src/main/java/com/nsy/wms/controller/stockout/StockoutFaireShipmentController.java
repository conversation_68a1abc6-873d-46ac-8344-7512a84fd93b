package com.nsy.wms.controller.stockout;

import com.nsy.api.pms.dto.request.base.IdListRequest;
import com.nsy.api.wms.domain.stockout.StockoutFaireShipmentSearchResult;
import com.nsy.api.wms.request.stockout.StockoutFairePrintRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentOpClearRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentOpMergeRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentScanRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentShipRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StockoutFaireMergeShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentScanResponse;
import com.nsy.wms.business.service.stockout.StockoutFaireShipmentPrintService;
import com.nsy.wms.business.service.stockout.StockoutFaireShipmentService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Api(tags = "Faire装箱清单")
public class StockoutFaireShipmentController extends BaseController {

    @Autowired
    StockoutFaireShipmentService faireShipmentService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutFaireShipmentPrintService faireShipmentPrintService;

    @ApiOperation(value = "Faire装箱清单-列表", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/list", method = RequestMethod.POST)
    public PageResponse<StockoutFaireShipmentSearchResult> searchList(@Valid @RequestBody StockoutFaireShipmentSearchRequest request) {
        return faireShipmentService.searchList(request);
    }

    @ApiOperation(value = "Faire装箱清单-批次发货", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/batch-ship", method = RequestMethod.POST)
    @ResponseBody
    public void updateStockoutOrderLogisticsCompany(@RequestBody StockoutFaireShipmentShipRequest request) {
        faireShipmentService.batchShipV1(request);
    }

    @ApiOperation(value = "Faire装箱清单-批次发货V2", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/batch-ship-v2", method = RequestMethod.POST)
    @ResponseBody
    public void updateStockoutOrderLogisticsCompanyV2(@RequestBody StockoutFaireShipmentShipRequest request) {
        faireShipmentService.batchShipV2(request);
    }

    @ApiOperation(value = "Faire装箱清单-批次发货(异步)", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/async/batch-ship", method = RequestMethod.POST)
    @ResponseBody
    public AsyncProcessFlowResult asyncUpdateStockoutOrderLogisticsCompany(@RequestBody StockoutFaireShipmentShipRequest request) {
        return faireShipmentService.asyncBatchShip(request);
    }

    @ApiOperation(value = "Faire装箱清单-创建或更新", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/{shipmentId}", method = RequestMethod.PUT)
    public void createOrUpdate(@PathVariable("shipmentId") Integer shipmentId) {
        StockoutShipmentEntity shipmentEntity = shipmentService.getById(shipmentId);
        faireShipmentService.createOrUpdateByShipment(shipmentEntity);
    }

    @ApiOperation(value = "Faire装箱清单-根据boxCode获取详情", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/box-code/{boxCode}", method = RequestMethod.GET)
    public StockoutFaireShipmentResponse getByBoxCode(@PathVariable("boxCode") String boxCode) {
        return faireShipmentService.getByBoxCode(boxCode);
    }

    @ApiOperation(value = "Faire装箱清单-修改", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/info", method = RequestMethod.POST)
    public void update(@Valid @RequestBody StockoutFaireShipmentUpdateRequest request) {
        faireShipmentService.updateFaireShipment(request);
    }

    @ApiOperation(value = "人工新增Faire箱子", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/create", method = RequestMethod.GET)
    public String getFaireBoxNo() {
        return faireShipmentService.createShipmentBoxByUser();
    }

    @ApiOperation(value = "Faire装箱-扫描单号", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/scan", method = RequestMethod.POST)
    public StockoutFaireShipmentScanResponse scanBoxCode(@Valid @RequestBody StockoutFaireShipmentScanRequest request) {
        return faireShipmentService.scanOrderNo(request);
    }

    @ApiOperation(value = "Faire装箱清单-移除", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/remove-faire/{faireShipmentId}", method = RequestMethod.PUT)
    public void removeFaire(@PathVariable("faireShipmentId") Integer faireShipmentId) {
        faireShipmentService.removeFaire(faireShipmentId);
    }

    @ApiOperation(value = "Faire装箱清单-根据mergeBoxCode获取详情", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/merge-box-code/{boxCode}", method = RequestMethod.GET)
    public StockoutFaireMergeShipmentResponse getByMergeBoxCode(@PathVariable("boxCode") String boxCode) {
        return faireShipmentService.getByMergeBoxCode(boxCode);
    }

    @ApiOperation(value = "Faire装箱-确认合并", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/confirm-merge", method = RequestMethod.POST)
    public void confirmMerge(@Valid @RequestBody StockoutFaireShipmentOpMergeRequest request) {
        faireShipmentService.confirmMerge(request);
    }

    @ApiOperation(value = "Faire装箱-清空箱子", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/remove-merge", method = RequestMethod.POST)
    public void removeMerge(@Valid @RequestBody StockoutFaireShipmentOpClearRequest request) {
        faireShipmentService.removeMerge(request);
    }


    @ApiOperation(value = "faire箱码打印", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/print-box-code", method = RequestMethod.POST)
    public PrintListResponse printFaireShipmentBoxCode(@RequestParam(required = false) Boolean singlePrint, @Valid @RequestBody StockoutFairePrintRequest idListRequest) {
        return faireShipmentPrintService.printFaireShipmentBoxCode(idListRequest, singlePrint);
    }

    @ApiOperation(value = "Faire装箱清单-更新状态", produces = "application/json")
    @RequestMapping(value = "/stockout-faire-shipment/update-status", method = RequestMethod.POST)
    public void updateFaireShipmentStatus(@Valid @RequestBody IdListRequest request) {
        faireShipmentService.updateFaireShipmentStatus(request.getIdList());
    }

}
