package com.nsy.api.wms.enumeration.erp;

/**
 * ERP订单状态名称枚举
 */
public enum ErpOrderStateNameEnum {
    DELETED("已删除"),
    UNPAID("未付款"),
    PENDING_REVIEW("待审核"),
    PROCESSED("已处理"),
    PRINT_EXCEPTION("打印异常"),
    HANG_UP("挂起"),
    PENDING_SHIPMENT("待发货"),
    SHIPPED("已发货"),
    COMPLETED("已完成");

    private final String name;

    ErpOrderStateNameEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据名称获取枚举值
     *
     * @param name 状态名称
     * @return 对应的枚举值，如果没有找到则返回null
     */
    public static ErpOrderStateNameEnum getByName(String name) {
        for (ErpOrderStateNameEnum stateEnum : values()) {
            if (stateEnum.getName().equals(name)) {
                return stateEnum;
            }
        }
        return null;
    }
}
