package com.nsy.wms.business.manage.supplier;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.Attach;
import com.nsy.api.wms.domain.qc.QcDictionary;
import com.nsy.api.wms.domain.qc.QcDictionaryItem;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.request.qa.StockinQaRoutingInspectStatusQueryRequest;
import com.nsy.api.wms.request.qc.DictionaryRequest;
import com.nsy.api.wms.response.qa.StockinQaRoutingInspectStatusQueryResponse;
import com.nsy.wms.business.manage.supplier.request.InboundsRequiredCheck;
import com.nsy.wms.business.manage.supplier.request.QaPunishmentsRequest;
import com.nsy.wms.business.manage.supplier.request.StockinQaRoutingInspectRequest;
import com.nsy.wms.business.manage.supplier.request.StockinQaTaskSyncRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractSyncDataOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractSyncDataRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractWmsAuditRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractWmsSignRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsAuditRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsClearRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectRequest;
import com.nsy.wms.business.manage.supplier.request.SyncInboundsRequiredCheckRequest;
import com.nsy.wms.business.manage.supplier.response.QcInboundsResponse;
import com.nsy.wms.business.manage.supplier.response.SyncInboundsRequiredCheckResponse;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SupplierApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SupplierApiService.class);

    @Autowired
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.supplier}")
    private String apiUrl;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Autowired
    private ObjectMapper objectMapper;

    public SyncInboundsRequiredCheckResponse syncInboundsRequiredCheck(SyncInboundsRequiredCheckRequest syncInboundsRequiredCheckRequest) {
        String uri = String.format("%s/inbounds-required-check/import", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_QC_TASK, uri,
            JsonMapper.toJson(syncInboundsRequiredCheckRequest), syncInboundsRequiredCheckRequest.getInboundsRequiredChecks().get(0).getBoxBarcode(), "wms生成质检任务，同步QMS");
        try {
            ResponseEntity<SyncInboundsRequiredCheckResponse> result = this.restTemplate.postForEntity(uri, syncInboundsRequiredCheckRequest, SyncInboundsRequiredCheckResponse.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(result), ExternalApiLogStatusEnum.SUCCESS);
            return result.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    public InboundsRequiredCheck queryInboundsRequiredCheck(InboundsRequiredCheck inboundsRequiredCheck) {
        String uri = String.format("%s/inbounds-required-check/query", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GET_QC_TASK, uri,
            JsonMapper.toJson(inboundsRequiredCheck), inboundsRequiredCheck.getSku(), "wms查询质检任务");
        try {
            ResponseEntity<InboundsRequiredCheck> result = this.restTemplate.postForEntity(uri, inboundsRequiredCheck, InboundsRequiredCheck.class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(result), ExternalApiLogStatusEnum.SUCCESS);
            return result.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 同步待退货数
     *
     * @param request
     */
    public void qaPunishments(QaPunishmentsRequest request) {
        String url = String.format("%s/quality_inspection_penalty_task/reconciliation-rewards-and-punishments-summary/create", apiUrl);
        this.restTemplate.postForEntity(url, request, Void.class);
    }

    // 获取质检不合格原因归类
    public List<QcDictionary> searchQcDictionary(DictionaryRequest request) {
        String url = String.format("%s/qc/dictionary/list", apiUrl);
        List<QcDictionary> qcDictionaryList = new ArrayList<>();
        ResponseEntity<QcDictionaryItem> responseEntity = this.restTemplate.postForEntity(url, request, QcDictionaryItem.class);
        QcDictionaryItem response = responseEntity.getBody();
        if (!ObjectUtils.isEmpty(response)) {
            qcDictionaryList = response.getQcDictionaryItem();
        }

        return qcDictionaryList;
    }

    public List<Attach> getDevelopAttachList(Integer productId) {
        String url = String.format("%s/qc/inbounds/product-attach/%s", apiUrl, productId);
        String attachListStr = restTemplate.getForObject(url, String.class);
        List<Attach> attachList = new ArrayList<>();
        if (StringUtils.hasText(attachListStr)) {
            attachList = JSONUtils.fromJSONArray(attachListStr, Attach.class);
        }
        return attachList;
    }

    public Date findStartQcStcokinOrder(String stockinOrderNo) {
        String url = String.format("%s/qc/inbounds/routing-inspect/qc-start-date/%s", apiUrl, stockinOrderNo);
        try {
            ResponseEntity<Date> result = this.restTemplate.getForEntity(url, Date.class);
            return result.getBody();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 同步合同数据
     *
     * @return
     */
    public void syncCustomsContract(List<StockoutCustomsDeclareContractSyncDataOneRequest> syncSupplierRequestList) {
        StockoutCustomsDeclareContractSyncDataRequest result = new StockoutCustomsDeclareContractSyncDataRequest();
        result.setList(syncSupplierRequestList);

        String url = String.format("%s/stockout-customs-declare-contract/wms/sync-data", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SUPPLIER_SYNC_CONTRACT_DATA, url,
            JsonMapper.toJson(result), syncSupplierRequestList.get(0).getDeclareContractNo(), "同步合同数据");
        try {
            this.restTemplate.postForEntity(url, result, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
    }

    /**
     * 同步合同已签署
     *
     * @param request
     * @return
     */
    public void syncCustomsContractSign(StockoutCustomsDeclareContractWmsSignRequest request) {
        List<StockoutCustomsDeclareContractWmsSignRequest> result = new ArrayList<>();
        result.add(request);
        String url = String.format("%s/stockout-customs-declare-contract/wms/sign", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SUPPLIER_SYNC_CONTRACT_SIGN, url,
            JsonMapper.toJson(result), request.getDeclareContractId().toString(), "同步合同已签署数据");
        try {
            this.restTemplate.postForEntity(url, result, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
    }

    /**
     * WMS - 进项明细 -审核不通过
     *
     * @param request
     * @return
     */
    public void syncCustomsFormItemAudit(StockoutCustomsDeclareFormWmsAuditRequest request) {
        String url = String.format("%s/stockout-customs-declare-form/wms/audit", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.WMS_FORM_ITEM_AUDIT, url,
            JsonMapper.toJson(request), request.getDeclareForm().get(0).getDeclareFormId().toString(), "WMS关单进项明细审核");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 关单驳回
     *
     * @param request
     * @return
     */
    public void customsDeclareFormReject(StockoutCustomsDeclareFormWmsRejectRequest request) {
        String url = String.format("%s/stockout-customs-declare-form/wms/reject", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.WMS_CONTRACT_AUDIT, url,
            JsonMapper.toJson(request), request.getDeclareForm().get(0).getDeclareFormId().toString(), "wms合同审核不通过");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * WMS - 合同 - 驳回
     *
     * @param request
     * @return
     */
    public void syncCustomsContractReject(StockoutCustomsDeclareContractWmsAuditRequest request) {
        String url = String.format("%s/stockout-customs-declare-contract/wms/audit", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.WMS_CONTRACT_REJECT, url,
            JsonMapper.toJson(request), request.getDeclareContract().get(0).getDeclareContractId().toString(), "wms合同驳回");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * WMS - 合同 - 驳回
     *
     * @param request
     * @return
     */
    public void customsDeclareFormClear(StockoutCustomsDeclareFormWmsClearRequest request) {
        String url = String.format("%s/stockout-customs-declare-form/wms/clear", apiUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.WMS_FORM_CLEAR, url,
            JsonMapper.toJson(request), request.getFormId().toString(), "WMS清楚关单");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 根据采购单号 返回待发货数大于0的sku列表
     *
     * @param purchasePlanNo
     * @return
     */
    public List<QcInboundsResponse> getInboundInfoByPurchaseNumbers(List<String> purchasePlanNo) {
        try {
            String url = String.format("%s/qa/inbounds/get-info-by-purchase", apiUrl);
            String responseStr = this.restTemplate.postForObject(url, purchasePlanNo, String.class);
            return objectMapper.readValue(responseStr, new QcInboundsResponseReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 同步旧系统
     *
     * @param internalBoxCode
     * @param sku
     */
    public void syncStockinQaTask(String internalBoxCode, String sku) {
        //@todo 系统并行时使用后续稳定时可去除
        try {
            String url = String.format("%s/inbounds-required-check/sync", apiUrl);
            StockinQaTaskSyncRequest request = new StockinQaTaskSyncRequest();
            request.setInternalBoxCode(internalBoxCode);
            request.setSku(sku);
            this.restTemplate.postForObject(url, request, Void.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }

    }

    /**
     * 同步旧系统 -- 产前样
     *
     * @param internalBoxCode
     * @param sku
     */
    public void syncQaProductSample(String internalBoxCode, String sku, Integer result) {
        //@todo 系统并行时使用后续稳定时可去除
        try {
            String url = String.format("%s/qc/product/sample/complete", apiUrl);
            StockinQaTaskSyncRequest request = new StockinQaTaskSyncRequest();
            request.setInternalBoxCode(internalBoxCode);
            request.setResult(result);
            request.setSku(sku);
            this.restTemplate.postForObject(url, request, Void.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }

    }

    /**
     * 同步旧系统 --巡检单
     *
     * @param qaRoutingInspectDTO
     */
    public void syncStockinQaRoutingInspect(StockinQaRoutingInspectRequest qaRoutingInspectDTO) {
        //@todo 系统并行时使用后续稳定时可去除
        try {
            String url = String.format("%s/qc/inbounds/routing-inspect-list/sync", apiUrl);
            this.restTemplate.postForObject(url, qaRoutingInspectDTO, Void.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    public void syncStockinQaOrder(QcInboundsMessage qcInboundsMessage) {
        try {
            String url = String.format("%s/qa/inbounds/stockin-qa-order/sync", apiUrl);
            this.restTemplate.postForObject(url, qcInboundsMessage, Void.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    /**
     * 查询就系统巡检状态
     *
     * @param request
     */
    public StockinQaRoutingInspectStatusQueryResponse getCheckStatusByInfo(StockinQaRoutingInspectStatusQueryRequest request) {
        //@todo 系统并行时使用后续稳定时可去除
        try {
            String url = String.format("%s/qc/inbounds/routing-inspect-list/get-check-status-by-info", apiUrl);
            return this.restTemplate.postForObject(url, request, StockinQaRoutingInspectStatusQueryResponse.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    private static final class QcInboundsResponseReference extends TypeReference<ArrayList<QcInboundsResponse>> {
    }


}
