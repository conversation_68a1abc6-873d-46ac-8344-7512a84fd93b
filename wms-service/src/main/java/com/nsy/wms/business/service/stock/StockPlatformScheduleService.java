package com.nsy.wms.business.service.stock;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.StatusTabConstants;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.TabUrlEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleStockinStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinAppointmentTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stock.QueryStockPlatformScheduleReceiveInfoRequest;
import com.nsy.api.wms.request.stock.StockPlatformScheduleSupplierAddRequest;
import com.nsy.api.wms.request.stock.StockPlatformScheduleUpdateRequest;
import com.nsy.api.wms.request.stockin.ModifyStockPlatformScheduleRequest;
import com.nsy.api.wms.request.stockin.StockinPlatformScheduleListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.external.FeignClientResponse;
import com.nsy.api.wms.response.stock.ErpSpotInfoResponse;
import com.nsy.api.wms.response.stock.QueryStockPlatformScheduleReceiveInfoResponse;
import com.nsy.api.wms.response.stockin.QueryAllInTransitInfoResponse;
import com.nsy.api.wms.response.stockin.StockinPlatformScheduleListResponse;
import com.nsy.api.wms.response.stockin.StockinPlatformScheduleStatisticResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.request.SyncPlatformScheduleToErpRequest;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.supplier.response.LogisticsMappingInfo;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdPlatformRuleService;
import com.nsy.wms.business.service.bd.BdPlatformService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.service.stockin.StockinSpotInfoService;
import com.nsy.wms.business.service.tab.TabService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPlatformEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinSpotInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockPlatformScheduleMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockPlatformScheduleService extends ServiceImpl<StockPlatformScheduleMapper, StockPlatformScheduleEntity> implements TabService, IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockPlatformScheduleService.class);

    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    BdPlatformService platformService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    BdPlatformRuleService platformRuleService;
    @Autowired
    StockPlatformScheduleCreateService createService;
    @Autowired
    StockinSpotInfoService spotInfoService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    StockPlatformScheduleLogService platformSchedulerLogService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockPlatformScheduleOperateLogService stockPlatformScheduleOperateLogService;
    @Autowired
    private BdErpSpaceMappingService bdErpSpaceMappingService;

    public void supplierAddStockPlatformSchedule(StockPlatformScheduleSupplierAddRequest request) {
        List<Exception> exceptions = new ArrayList<>();
        for (StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier stockPlatformScheduleSupplier : request.getPlatformScheduleSupplierList()) {
            try {
                createService.supplierAddStockPlatformSchedule(stockPlatformScheduleSupplier, request.getOrderType(), request.getLocation());
            } catch (Exception e) {
                exceptions.add(e);
            }
        }
        if (!exceptions.isEmpty()) {
            throw new RuntimeException(exceptions.toString());
        }
    }

    /**
     * 设置平台和排班时间,并生成入库任务
     *
     * @param platformScheduleId
     * @param request
     */
    @Transactional
    public void updatePlatformAndSchedule(Integer platformScheduleId, StockPlatformScheduleUpdateRequest request) {
        StockPlatformScheduleEntity scheduleEntity = getById(platformScheduleId);
        if (Objects.isNull(scheduleEntity)) throw new InvalidRequestException("没有找到对应月台调度记录");
        if (StockPlatformScheduleStatusEnum.AUDITED.name().equals(scheduleEntity.getStatus())) {
            throw new BusinessServiceException("该月台调度记录已审核！");
        }
        checkStockinRuleAndGenerateStockinOrderTask(scheduleEntity, request, loginInfoService.getName());
    }

    /**
     * 根据入库规则，生成相应入库任务
     *
     * @param scheduleEntity
     * @param request
     * @param userName
     */
    public void checkStockinRuleAndGenerateStockinOrderTask(StockPlatformScheduleEntity scheduleEntity, StockPlatformScheduleUpdateRequest request, String userName) {
        BdPlatformEntity platformEntity = platformService.getByPlatformIdAndStatus(request.getPlatformId());
        scheduleEntity.setScheduleStartDate(request.getScheduleStartDate());
        scheduleEntity.setScheduleEndDate(request.getScheduleEndDate());
        scheduleEntity.setPlatformId(request.getPlatformId());
        scheduleEntity.setPlatformName(platformEntity.getPlatformName());
        scheduleEntity.setLocation(platformEntity.getLocation());
        // todo 匹配入库优先级 暂不开发
        // 月台入库规则自动审核
        if (platformRuleService.isAutoStockinAuditRule(platformEntity.getLocation(), StockinAppointmentTypeEnum.FACTORY_STOCKIN_APPOINTMENT.name())) {
            scheduleEntity.setStatus(StockPlatformScheduleStatusEnum.AUDITED.name());
            scheduleEntity.setAuditBy(loginInfoService.getName());
            scheduleEntity.setAuditDate(new Date());
            // 根据入库规则生成相应的入库任务
            stockinOrderTaskService.generateStockinOrderTask(scheduleEntity, userName);
        } else {
            scheduleEntity.setStatus(StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name());
        }
        scheduleEntity.setStockinStatus(StockPlatformScheduleStockinStatusEnum.WART_INBOUND.name());
        updateById(scheduleEntity);
    }

    /**
     * 根据出库单号获取唯一的月台排班信息
     *
     * @param supplierDeliveryNo
     * @return
     */
    public StockPlatformScheduleEntity getBySupplierDeliveryNo(String supplierDeliveryNo) {
        QueryWrapper<StockPlatformScheduleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockPlatformScheduleEntity::getSupplierDeliveryNo, supplierDeliveryNo);
        return getOne(queryWrapper);
    }

    /**
     * 现货入库签收物流 Tower 任务: 现货收货改动 ( https://tower.im/teams/545075/todos/442867 )
     * 1、物流签收仅校验物流单号是否存在 是否重复
     * 2、物流签收不生成ERP工厂出库单，入库任务
     */
    public void getSpotInfo(String documentNo) {
        int count = spotInfoService.count(new QueryWrapper<StockinSpotInfoEntity>().lambda().eq(StockinSpotInfoEntity::getLogisticsNo, documentNo));
        if (count > 0) {
            return;
        }

        List<LogisticsMappingInfo> logisticsMappingList = scmApiService.getLogisticsMappingList(documentNo, false);
        if (CollectionUtils.isEmpty(logisticsMappingList)) {
            return;
        }
        //校验地区，非该地区的抛出异常
        if (logisticsMappingList.stream().filter(item -> StringUtils.hasText(item.getLocation())).anyMatch(item -> !TenantContext.getTenant().equals(item.getLocation())))
            throw new BusinessServiceException(String.format("该物流单为%s地区，请勿扫描！", LocationEnum.getNameBy(logisticsMappingList.get(0).getLocation())));
        List<String> logisticsNoList = logisticsMappingList.stream().map(LogisticsMappingInfo::getLogisticsNo).distinct().collect(Collectors.toList());
        if (documentNo.equals(logisticsMappingList.get(0).getPurchasePlanNo())) {
            Boolean isGenerateLogisticsNo = Boolean.TRUE;
            for (String logisticsNo : logisticsNoList) {
                StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByLogisticsNo(logisticsNo);
                if (Objects.isNull(stockinOrderEntity)) {
                    isGenerateLogisticsNo = Boolean.FALSE;
                    continue;
                }
                StockinOrderTaskEntity stockinOrderTaskEntity = stockinOrderTaskService.getById(stockinOrderEntity.getTaskId());
                if (!StockinOrderTaskStatusEnum.RECEIVED.name().equals(stockinOrderTaskEntity.getStatus())) {
                    isGenerateLogisticsNo = Boolean.FALSE;
                }
            }
            if (isGenerateLogisticsNo) {
                logisticsMappingList = scmApiService.getLogisticsMappingList(documentNo, isGenerateLogisticsNo);
            }
        } else {
            int count2 = spotInfoService.count(new QueryWrapper<StockinSpotInfoEntity>().lambda().in(StockinSpotInfoEntity::getLogisticsNo, logisticsNoList));
            if (count2 > 0) return;
        }

        spotInfoService.addSpotInfo(logisticsMappingList);
    }

    public void createStockPlatform(List<ErpSpotInfoResponse.StockPlatformScheduleSupplier> platformScheduleSupplierList) {
        // step2、生成月台预约单
        StockPlatformScheduleSupplierAddRequest request = new StockPlatformScheduleSupplierAddRequest();
        List<StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier> supplierList = new LinkedList<>();
        List<StockPlatformScheduleSupplierAddRequest.Item> itemList = new LinkedList<>();
        for (ErpSpotInfoResponse.StockPlatformScheduleSupplier scheduleSupplier : platformScheduleSupplierList) {
            StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier supplierRequest = new StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier();
            BeanUtils.copyProperties(scheduleSupplier, supplierRequest);
            itemList.clear();
            for (ErpSpotInfoResponse.Item item : scheduleSupplier.getItem()) {
                StockPlatformScheduleSupplierAddRequest.Item itemRequest = new StockPlatformScheduleSupplierAddRequest.Item();
                BeanUtils.copyProperties(item, itemRequest);
                itemList.add(itemRequest);
            }
            supplierRequest.setItem(itemList);
            supplierList.add(supplierRequest);
        }
        request.setPlatformScheduleSupplierList(supplierList);
        supplierAddStockPlatformSchedule(request);
        // step3、生成入库任务
        createStockinTask();
    }

    private void createStockinTask() {
        Page<StockPlatformScheduleEntity> page = new Page<>(0, 1000);
        QueryWrapper<StockPlatformScheduleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockPlatformScheduleEntity::getStatus, StockPlatformScheduleStatusEnum.NO_APPOINTMENT.name());
        Page<StockPlatformScheduleEntity> platformScheduleEntityPage = this.page(page, queryWrapper);
        List<StockPlatformScheduleEntity> platformScheduleEntityList = platformScheduleEntityPage.getRecords();
        if (CollectionUtils.isEmpty(platformScheduleEntityList)) {
            return;
        }
        List<BdPlatformEntity> platformEntityList = platformService.list();
        if (platformEntityList.isEmpty()) throw new BusinessServiceException("没有可用月台，请设置!");
        // todo 月台暂时默认匹配
        BdPlatformEntity platformEntity = platformEntityList.get(0);
        for (StockPlatformScheduleEntity scheduleEntity : platformScheduleEntityList) {
            StockPlatformScheduleUpdateRequest request = new StockPlatformScheduleUpdateRequest();
            request.setPlatformId(platformEntity.getPlatformId());
            request.setScheduleStartDate(scheduleEntity.getPlanArriveDate());
            request.setScheduleEndDate(scheduleEntity.getPlanArriveDate());
            try {
                this.checkStockinRuleAndGenerateStockinOrderTask(scheduleEntity, request, loginInfoService.getName());
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 入库任务收货完成，校验该入库任务关联的月台下的入库任务是否都全部收货完成
     *
     * @param platformScheduleId
     */
    public void receivedUpdatePlatformStatus(int platformScheduleId) {
        List<StockinOrderTaskEntity> taskEntityList = stockinOrderTaskService.list(new LambdaQueryWrapper<StockinOrderTaskEntity>().eq(StockinOrderTaskEntity::getPlatformScheduleId, platformScheduleId));
        boolean allReceived = taskEntityList.stream().anyMatch(t -> !StockinOrderTaskStatusEnum.RECEIVED.name().equals(t.getStatus()));
        StockPlatformScheduleEntity stockPlatformScheduleEntity = this.getById(platformScheduleId);
        if (StockPlatformScheduleStockinStatusEnum.COMPLETED.name().equalsIgnoreCase(stockPlatformScheduleEntity.getStockinStatus()))
            return;
        if (!allReceived) {
            this.update(new UpdateWrapper<StockPlatformScheduleEntity>().lambda()
                    .eq(StockPlatformScheduleEntity::getPlatformScheduleId, platformScheduleId)
                    .set(StockPlatformScheduleEntity::getStockinStatus, StockPlatformScheduleStockinStatusEnum.INBOUNDED)
                    .set(StockPlatformScheduleEntity::getUpdateBy, loginInfoService.getName()));
            platformSchedulerLogService.addLog(platformScheduleId, StockPlatformScheduleLogTypeEnum.ALL_RECEIVED.getLogType(), "月台下入库单全部收货完成");
        }
    }

    /**
     * 入库单完成，校验工厂出库单下入库单是否全部完成，修改入库状态为已完成
     *
     * @param supplierDeliveryNo
     */
    public void stockinOrderCompleteUpdatePlatformStatus(String supplierDeliveryNo) {
        if (!StringUtils.hasText(supplierDeliveryNo))
            return;
        StockPlatformScheduleEntity stockPlatformScheduleEntity = this.getBySupplierDeliveryNo(supplierDeliveryNo);
        if (Objects.isNull(stockPlatformScheduleEntity))
            return;
        Integer count = this.getBaseMapper().countUncompletedOrder(supplierDeliveryNo);
        if (count > 0)
            return;

        this.update(new UpdateWrapper<StockPlatformScheduleEntity>().lambda()
                .eq(StockPlatformScheduleEntity::getPlatformScheduleId, stockPlatformScheduleEntity.getPlatformScheduleId())
                .set(StockPlatformScheduleEntity::getStockinStatus, StockPlatformScheduleStockinStatusEnum.COMPLETED.name())
                .set(StockPlatformScheduleEntity::getUpdateBy, loginInfoService.getName()));
        platformSchedulerLogService.addLog(stockPlatformScheduleEntity.getPlatformScheduleId(), StockPlatformScheduleLogTypeEnum.ALL_COMPLETED.getLogType(), "月台下入库单全部完结");
    }


    public void updateStockinStatus(Integer platformScheduleId, StockPlatformScheduleStockinStatusEnum statusEnum) {
        this.update(new LambdaUpdateWrapper<StockPlatformScheduleEntity>().eq(StockPlatformScheduleEntity::getPlatformScheduleId, platformScheduleId)
                .set(StockPlatformScheduleEntity::getStockinStatus, statusEnum.name()).set(StockPlatformScheduleEntity::getUpdateBy, loginInfoService.getName()));
    }

    public SyncPlatformScheduleToErpRequest buildSyncPlatformScheduleToErpRequest(StockPlatformScheduleEntity platformScheduleEntity, List<StockPlatformScheduleItemEntity> platformScheduleItemEntityList) {
        List<SyncPlatformScheduleToErpRequest.SyncPlatformScheduleItem> scheduleItemList = platformScheduleItemEntityList.stream().map(entity -> {
            SyncPlatformScheduleToErpRequest.SyncPlatformScheduleItem item = new SyncPlatformScheduleToErpRequest.SyncPlatformScheduleItem();
            item.setBoxIndex(entity.getBoxIndex());
            item.setDeliveryQty(entity.getQty());
            item.setSku(entity.getSku());
            item.setSupplierDeliveryBoxNo(entity.getSupplierDeliveryBoxCode());
            item.setPurchasePlanNo(entity.getPurchasePlanNo());
            item.setSpaceName(bdErpSpaceMappingService.getEntityBySpaceIdAndAreaName(entity.getSpaceId(), entity.getAreaName()).getErpSpaceName());
            return item;
        }).collect(Collectors.toList());
        SyncPlatformScheduleToErpRequest request = new SyncPlatformScheduleToErpRequest();
        request.setDeliveryDate(platformScheduleEntity.getDeliveryDate());
        request.setStockinType(platformScheduleEntity.getPlatformScheduleType());
        request.setLocation(platformScheduleEntity.getLocation());
        request.setSupplierId(platformScheduleEntity.getSupplierId());
        request.setSupplierDeliveryNo(platformScheduleEntity.getSupplierDeliveryNo());
        request.setItemList(scheduleItemList);
        request.setOperator(loginInfoService.getName());
        request.setAuditDate(platformScheduleEntity.getAuditDate());
        return request;
    }

    @Override
    public List<StatusTabResponse> getTabs() {
        List<StatusTabResponse> tabs = baseMapper.getTabs(Arrays.asList(StockPlatformScheduleStockinStatusEnum.WART_INBOUND.name(), StockPlatformScheduleStockinStatusEnum.IN_TRANSIT.name(), StockPlatformScheduleStockinStatusEnum.INBOUNDING.name()));
        List<String> statusList = Arrays.stream(StockPlatformScheduleStockinStatusEnum.values()).map(StockPlatformScheduleStockinStatusEnum::name).collect(Collectors.toList());
        for (String status : statusList) {
            if (tabs.stream().noneMatch(tab -> status.equals(tab.getStatus()))) {
                StatusTabResponse response = new StatusTabResponse();
                response.setNum(0);
                response.setName(status);
                response.setStatus(status);
                tabs.add(response);
            }
        }
        tabs.forEach(tab -> {
            tab.setLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PLATFORM_STOCKIN_STATUS.getName(), tab.getStatus()));
            tab.setName(tab.getStatus());
        });
        StatusTabResponse response = new StatusTabResponse();
        List<StatusTabResponse> newTabs = tabs.stream().filter(tab -> statusList.stream().anyMatch(status -> status.equals(tab.getStatus())))
                .sorted(Comparator.comparing(tab -> Enum.valueOf(StockPlatformScheduleStockinStatusEnum.class, tab.getStatus()).ordinal())).collect(Collectors.toList());
        response.setNum(0);
        response.setLabel(StatusTabConstants.CN_ALL);
        response.setName(StatusTabConstants.ALL);
        response.setStatus(StatusTabConstants.ALL);
        newTabs.add(response);
        return newTabs;
    }

    @Override
    public TabUrlEnum tabType() {
        return TabUrlEnum.SCM_SHIPPING_LIST;
    }

    public List<QueryAllInTransitInfoResponse> queryAllInTransitInfo() {
        return this.getBaseMapper().queryAllInTransitInfo();
    }

    public StockinPlatformScheduleListResponse getPlatformScheduleInfo(String supplierDeliveryNo) {
        StockinPlatformScheduleListResponse response = this.getBaseMapper().getPlatformScheduleInfo(supplierDeliveryNo);
        if (Objects.isNull(response)) {
            throw new BusinessServiceException("查询不到对应的月台记录!");
        }
        if (StockPlatformScheduleStatusEnum.AUDITED.name().equalsIgnoreCase(response.getStatus())) {
            throw new BusinessServiceException("当前月台记录已审核请检查!");
        }
        return response;
    }

    public void modifyRemark(List<ModifyStockPlatformScheduleRequest> request) {
        if (CollectionUtils.isEmpty(request)) {
            return;
        }
        request.forEach(detail -> {
            this.getBaseMapper().updateRemarkInfo(detail);
        });
    }

    /**
     * 月台调度列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockinPlatformScheduleListResponse> pagePlatformScheduleList(StockinPlatformScheduleListRequest request) {
        PageResponse<StockinPlatformScheduleListResponse> pageResponse = new PageResponse<>();
        //待审核包含 未预约 和 已预约待审核
        if (!CollectionUtils.isEmpty(request.getStatusList()) && request.getStatusList().size() == 1 && StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name().equalsIgnoreCase(request.getStatusList().get(0))) {
            request.setStatusList(CollUtil.newArrayList(StockPlatformScheduleStatusEnum.NO_APPOINTMENT.name(), StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name()));
        }
        if (StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name().equals(request.getStatus()) || request.getOverTenDayUnAudit())
            request.setStatusList(CollUtil.newArrayList(StockPlatformScheduleStatusEnum.NO_APPOINTMENT.name(), StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name()));
        if (StockPlatformScheduleStatusEnum.AUDITED.name().equals(request.getStatus()))
            request.setStatusList(CollUtil.newArrayList(StockPlatformScheduleStatusEnum.AUDITED.name()));
        Page<Object> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        if (!this.buildIdRequestInfo(request)) {
            pageResponse.setTotalCount(0L);
            return pageResponse;
        }

        IPage<StockinPlatformScheduleListResponse> pageResult = this.getBaseMapper().pagePlatformScheduleList(page, request);
        pageResult.getRecords().forEach(item -> {
            item.setStatusStr(StockPlatformScheduleStatusEnum.AUDITED.name().equals(item.getStatus()) ? "已审核" : "待审核");
        });
        pageResponse.setTotalCount(this.getBaseMapper().countPlatformScheduleList(request));
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }

    /**
     * 统计数量
     *
     * @param request
     * @return
     */
    public StockinPlatformScheduleStatisticResponse statisticsQty(StockinPlatformScheduleListRequest request) {
        if (!CollectionUtils.isEmpty(request.getStatusList()) && request.getStatusList().size() == 1 && StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name().equalsIgnoreCase(request.getStatusList().get(0))) {
            request.setStatusList(CollUtil.newArrayList(StockPlatformScheduleStatusEnum.NO_APPOINTMENT.name(), StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name()));
        }
        if (StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name().equals(request.getStatus()) || request.getOverTenDayUnAudit())
            request.setStatusList(CollUtil.newArrayList(StockPlatformScheduleStatusEnum.NO_APPOINTMENT.name(), StockPlatformScheduleStatusEnum.APPOINTMENT_PENDING.name()));
        if (StockPlatformScheduleStatusEnum.AUDITED.name().equals(request.getStatus()))
            request.setStatusList(CollUtil.newArrayList(StockPlatformScheduleStatusEnum.AUDITED.name()));
        if (!this.buildIdRequestInfo(request)) {
            return null;
        }
        return this.getBaseMapper().statisticsQty(request);
    }

    /**
     * 月台人工审核
     *
     * @param platformScheduleId
     */
    @Transactional
    public void audit(Integer platformScheduleId, String auditDescription) {
        StockPlatformScheduleEntity scheduleEntity = getById(platformScheduleId);
        if (Objects.isNull(scheduleEntity)) throw new InvalidRequestException("没有找到对应月台调度记录");
        if (StockPlatformScheduleStatusEnum.AUDITED.name().equals(scheduleEntity.getStatus())) {
            throw new BusinessServiceException(String.format("工厂出库单号为:%s的月台调度记录已审核！", scheduleEntity.getSupplierDeliveryNo()));
        }
        StockPlatformScheduleEntity stockPlatformScheduleEntity = new StockPlatformScheduleEntity();
        stockPlatformScheduleEntity.setPlatformScheduleId(platformScheduleId);
        if (StockPlatformScheduleStatusEnum.NO_APPOINTMENT.name().equals(scheduleEntity.getStatus())) {
            List<BdPlatformEntity> platformEntityList = platformService.list(new QueryWrapper<BdPlatformEntity>().last("limit 1"));
            if (platformEntityList.isEmpty()) throw new BusinessServiceException("没有可用月台，请设置!");
            // todo 月台暂时默认匹配
            BdPlatformEntity platformEntity = platformEntityList.get(0);
            stockPlatformScheduleEntity.setScheduleStartDate(scheduleEntity.getPlanArriveDate());
            stockPlatformScheduleEntity.setScheduleEndDate(scheduleEntity.getPlanArriveDate());
            stockPlatformScheduleEntity.setPlatformId(platformEntity.getPlatformId());
            stockPlatformScheduleEntity.setPlatformName(platformEntity.getPlatformName());
            stockPlatformScheduleEntity.setLocation(platformEntity.getLocation());
        }
        stockPlatformScheduleEntity.setUpdateBy(loginInfoService.getName());
        stockPlatformScheduleEntity.setStatus(StockPlatformScheduleStatusEnum.AUDITED.name());
        stockPlatformScheduleEntity.setAuditBy(loginInfoService.getName());
        stockPlatformScheduleEntity.setAuditDate(new Date());
        stockPlatformScheduleEntity.setStockinStatus(StockPlatformScheduleStockinStatusEnum.WART_INBOUND.name());
        stockPlatformScheduleEntity.setAuditDescription(auditDescription);
        this.updateById(stockPlatformScheduleEntity);
        // 根据入库规则生成相应的入库任务
        messageProducer.sendMessage(KafkaConstant.STOCKIN_ORDER_TASK_CREATE_TOPIC_NAME, KafkaConstant.STOCKIN_ORDER_TASK_CREATE_TOPIC,
                new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), scheduleEntity));
        stockPlatformScheduleOperateLogService.addLog(stockPlatformScheduleEntity.getPlatformScheduleId(), "审核", "月台调度任务审核");
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_PLATFORM_SCHEDULE_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();

        StockinPlatformScheduleListRequest listRequest = JSONObject.parseObject(request.getRequestContent(), StockinPlatformScheduleListRequest.class);
        if (!this.buildIdRequestInfo(listRequest)) {
            response.setTotalCount(0L);
            return response;
        }
        listRequest.setPageIndex(request.getPageIndex());
        listRequest.setPageSize(request.getPageSize());
        PageResponse<StockinPlatformScheduleListResponse> pageResponse = this.pagePlatformScheduleList(listRequest);
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JSON.toJSONString(pageResponse.getContent()));

        return response;
    }

    /**
     * 根据工厂出库单获取仓库
     *
     * @param supplierDeliveryNo
     * @return
     */
    public String getStockNameBySupplierDeliveryNo(String supplierDeliveryNo) {
        return this.getBaseMapper().getStockNameBySupplierDeliveryNo(supplierDeliveryNo);
    }

    public FeignClientResponse queryPlatformScheduleReceiveInfo(QueryStockPlatformScheduleReceiveInfoRequest request) {
        FeignClientResponse response = new FeignClientResponse();
        try {
            List<QueryStockPlatformScheduleReceiveInfoResponse> queryStockPlatformScheduleReceiveInfoResponses = baseMapper.queryPlatformScheduleReceiveInfo(request);
            response.setData(JsonMapper.toJson(queryStockPlatformScheduleReceiveInfoResponses));
            response.setStatus(200);
        } catch (BusinessServiceException e) {
            response.setMsg(e.getMessage());
            response.setStatus(500);
        }
        return response;
    }


    /**
     * 返回是否需要继续查询
     * 如果要查询明细的信息先查询出id,再作为条件去查，不然统计有问题
     *
     * @param request
     */
    private boolean buildIdRequestInfo(StockinPlatformScheduleListRequest request) {
        if (CollectionUtils.isEmpty(request.getSkuList()) && CollectionUtils.isEmpty(request.getSpuList()) && !StringUtils.hasText(request.getSupplierDeliveryBoxCode())) {
            return true;
        }
        List<Integer> platformIdList = this.getBaseMapper().getPlatformIdByCondition(request);
        //有条件，但是查询不到数据则返回false 并不在继续查询
        if (CollectionUtils.isEmpty(platformIdList)) {
            return false;
        }
        request.setPlatformScheduleIdList(platformIdList);
        return true;
    }
}
