<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderMapper">

    <select id="countProcessingByInfo" resultType="java.lang.Integer">
        select count(1) from stockin_qa_order t
        inner join stockin_qa_order_item ti on ti.stockin_qa_order_id = t.stockin_qa_order_id
        where
        t.process_status in ('FIRST_AUDIT','SECOND_AUDIT')
        <if test="internalBoxCode!= null and internalBoxCode!=''">
            AND t.internal_box_code = #{internalBoxCode}
        </if>
        <if test="sku!= null and sku!=''">
            AND t.sku = #{sku}
        </if>
    </select>

    <select id="getPreviousOrder" resultType="com.nsy.api.wms.response.qa.PreviousStockinQaOrderResponse">
        select sqo.stockin_qa_order_id                          as stockinQaOrderId,
               sqo.internal_box_code                            as internalBoxCode,
               sqo.spu,
               sqo.skc,
               sqo.sku,
               sqo.product_id,
               sqod.arrival_count                               as arrivalCount,
               sqod.test_total_count                            as testTotalCount,
               sqt.qa_qty                                       as qaQty,
               sqod.unqualified_count                           as unqualifiedCount,
               sqod.direct_return_count                         as directReturnCount,
               sqod.return_count                                as returnCount,
               sqo.result                                       as result,
               GROUP_CONCAT(DISTINCT qop.remark)                as remark,
               GROUP_CONCAT(DISTINCT sqou.unqualified_category) as unqualifiedCategories,
               GROUP_CONCAT(DISTINCT sqou.unqualified_question) as unqualifiedQuestions,
               GROUP_CONCAT(DISTINCT sqou.unqualified_reason)   as unqualifiedReasons
        from stockin_qa_order sqo
                 inner join stockin_qa_order_detail sqod on sqod.stockin_qa_order_id = sqo.stockin_qa_order_id
                 inner join stockin_qa_order_process qop on qop.stockin_qa_order_id = sqo.stockin_qa_order_id
                 inner join stockin_qa_task sqt on sqt.task_id = sqo.task_id
                 left join stockin_qa_order_unqualified sqou on sqou.stockin_qa_order_id = sqo.stockin_qa_order_id
        where sqo.sku = #{request.sku}
          and sqo.process_status = 'COMPLETED'
        group by sqo.stockin_qa_order_id
        order by sqo.create_date desc
        limit 1
    </select>

    <select id="pageQaHistory" resultType="com.nsy.api.wms.response.qa.StockinQaHistoryResponse">
        select sqo.stockin_qa_order_id,
               sqo.complete_date,
               sqo.result,
               GROUP_CONCAT(DISTINCT sqou.unqualified_category) as unqualifiedCategory,
               GROUP_CONCAT(DISTINCT sqou.unqualified_question) as unqualifiedQuestion,
               GROUP_CONCAT(DISTINCT sqou.unqualified_reason)   as unqualifiedReason
        from stockin_qa_order sqo
                 inner join stockin_qa_order_unqualified sqou
                           on sqou.stockin_qa_order_id = sqo.stockin_qa_order_id
        where sqo.sku = #{request.sku}
          and sqo.process_status = 'COMPLETED'
        group by sqo.stockin_qa_order_id
        order by sqo.complete_date desc


    </select>
    <select id="pageQaSpuReturnHistory"
            resultType="com.nsy.api.wms.response.qa.StockinQaSpuReturnHistoryResponse">
        select sqo.sku,
               sqou.unqualified_category               as unqualifiedCategory,
               sqou.unqualified_reason                 as unqualifiedReason,
               count(distinct sqo.stockin_qa_order_id) as returnCount
        from stockin_qa_order sqo
                 inner join stockin_qa_order_detail sqod
                            on sqo.stockin_qa_order_id = sqod.stockin_qa_order_id
                 left join stockin_qa_order_unqualified sqou
                           on sqou.stockin_qa_order_id = sqo.stockin_qa_order_id
        where sqo.spu = #{request.spu}
          and sqo.process_status = 'COMPLETED'
          and sqod.return_count > 0
        group by sqo.sku, sqou.unqualified_category, sqou.unqualified_reason
        order by sqo.sku
    </select>
    <select id="pageList" resultType="com.nsy.api.wms.response.qa.StockinQaOrderPageResponse">
        select
        o.stockin_qa_order_id,
        o.internal_box_code,
        o.product_id,
        p.category_id,
        p.category_name,
        o.spu,
        o.sku,
        o.skc,
        o.department,
        o.apply_department,
        detail.box_qty,
        o.supplier_name,
        detail.direct_return_count,
        detail.test_total_count,
        detail.unqualified_count,
        detail.return_count,
        detail.concessions_count,
        o.unqualified_category,
        o.process_status,
        o.result,
        o.appointor,
        o.qc_user_name,
        detail.stockin_date,
        o.complete_date,
        o.update_date,
        o.create_date as qcStartDate,
        s.contact_qc_emp_name as lastQcUserRealName,
        bs.space_name
        from stockin_qa_order o
        inner join product_info p on p.product_id = o.product_id
        left join stockin_qa_order_detail detail on detail.stockin_qa_order_id = o.stockin_qa_order_id
        left join bd_space bs on o.space_id = bs.space_id
        left join supplier s on s.supplier_id = o.supplier_id
        <include refid="page_where"></include>
        order by o.update_date desc
    </select>
    <select id="pageCount" resultType="java.lang.Long">
        select
        count(o.stockin_qa_order_id)
        from stockin_qa_order o
        inner join product_info p on p.product_id = o.product_id
        left join stockin_qa_order_detail detail on detail.stockin_qa_order_id = o.stockin_qa_order_id
        <include refid="page_where"></include>
    </select>

    <select id="countByProcessStatus" resultType="com.nsy.api.wms.response.base.StatusTabResponse">
        select o.process_status        status,
               count(o.process_status) num
        FROM stockin_qa_order o
        where o.process_status not in ('COMPLETED', 'CANCELED')
        GROUP BY o.process_status
    </select>
    <select id="getByIdPermission" resultType="com.nsy.wms.repository.entity.qa.StockinQaOrderEntity">
        select o.*
        from stockin_qa_order o
        where o.stockin_qa_order_id = #{stockinQaOrderId}
    </select>
    <select id="getAuditList" resultType="com.nsy.api.wms.response.qa.StockinQaOrderSkcResponse">
        select o.stockin_qa_order_id,
               o.internal_box_code,
               o.spu,
               o.sku,
               o.skc,
               detail.box_qty,
               detail.arrival_count,
               o.supplier_name,
               detail.direct_return_count,
               detail.test_total_count,
               (detail.box_qty - detail.unqualified_count) as qualifiedCount,
               detail.unqualified_count,
               detail.return_count,
               detail.concessions_count,
               o.unqualified_category,
               o.process_status,
               o.result,
               o.qc_user_name,
               detail.stockin_date,
               o.complete_date,
               GROUP_CONCAT(DISTINCT oi.supplier_delivery_no) as supplierDeliveryNo,
               s.contact_qc_emp_name                             as lastQcUserRealName
        from stockin_qa_order o
                 left join supplier s on s.supplier_id = o.supplier_id
                 inner join stockin_qa_order_detail detail
                            on detail.stockin_qa_order_id = o.stockin_qa_order_id
                 inner join stockin_qa_order_item oi
                            on o.stockin_qa_order_id = oi.stockin_qa_order_id
        where
        o.process_status = #{processStatus}
        <if test="spu != null and spu !=''">
            AND o.spu = #{spu}
        </if>
        <if test="skc != null and skc !=''">
            AND o.skc = #{skc}
        </if>
        and oi.supplier_delivery_no in
            <foreach collection="supplierDeliveryNoList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        and (o.appointor = #{appointor} or o.appointor is null)
        group by o.stockin_qa_order_id
    </select>
    <select id="countQty" resultType="com.nsy.api.wms.response.qa.StockinQaOrderCountQtyResponse">
        select
        ifnull(sum(detail.box_qty),0) as boxQty,
        ifnull(sum(detail.arrival_count),0) as arrivalCount,
        ifnull(sum(detail.test_total_count),0) as testTotalCount,
        ifnull(sum(detail.unqualified_count),0) as unqualifiedCount,
        ifnull(sum(detail.direct_return_count),0) as directReturnCount,
        ifnull(sum(if(o.process_status = 'COMPLETED',detail.return_count,detail.direct_return_count)),0) as returnCount,
        ifnull(sum(detail.concessions_count),0) as concessionsCount
        from stockin_qa_order o
        inner join product_info p on p.product_id = o.product_id
        left join stockin_qa_order_detail detail on detail.stockin_qa_order_id = o.stockin_qa_order_id
        <include refid="page_where"></include>
    </select>
    <select id="findPreQaOrder" resultType="java.lang.Integer">
        select o.stockin_qa_order_id from stockin_qa_order o
        inner join stockin_qa_order_item oi
        on oi.stockin_qa_order_id = o.stockin_qa_order_id
        where o.sku = #{sku}
        and oi.purchase_plan_no in
        <foreach collection="purchasePlanNos" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        and o.result in ('SOME_RETURN','BATCH_RETURN')
        and o.stockin_qa_order_id != #{stockinQaOrderId}
        order by o.stockin_qa_order_id desc
        limit 1
    </select>
    <select id="countQcColorOrderBySupplierDeliveryNoAndSkc" resultType="java.lang.Integer">
        select count(distinct o.stockin_qa_order_id)
        from stockin_qa_order o
        inner join stockin_qa_order_item oi
        on oi.stockin_qa_order_id = o.stockin_qa_order_id
        inner join stockin_qa_order_process p
        on p.stockin_qa_order_id = o.stockin_qa_order_id
        and p.process_name = '核对色卡'
               and p.status in ('QUALIFIED','UNQUALIFIED')
        where o.skc = #{skc}
        and o.process_status = 'COMPLETED'
        <if test="supplierDeliveryNoList != null and supplierDeliveryNoList.size() > 0 ">
            and oi.supplier_delivery_no in
            <foreach collection="supplierDeliveryNoList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>
    
    
    
    <select id="pageInspectTaskList" resultType="com.nsy.api.wms.response.qa.StockinQaInspectPageResponse">
        select
        o.stockin_qa_order_id,
        o.internal_box_code,
        o.product_id,
        o.sku,
        o.skc,
        o.department,
        o.apply_department,
        o.qc_user_name,
        o.complete_date,
        detail.box_qty,
        o.supplier_name,
        detail.direct_return_count,
        detail.test_total_count,
        detail.concessions_count,
        p.inspect_count as inspectTotalCount,
        o.unqualified_category,
        o.inspect_status,
        p.status as result,
        o.update_date,
        bs.space_name,
        p.create_date as qaInspectDate,
        p.operate_date AS inspectCompleteDate,
        p.unqualified_count,
        p.return_count,
        p.qa_count,
        p.stockin_qa_order_process_id,
        COALESCE(p.operator,p.create_by) as inspectUserName,
        p.create_date as createDate
        from stockin_qa_order o
        inner join stockin_qa_order_detail detail on detail.stockin_qa_order_id = o.stockin_qa_order_id
        inner join stockin_qa_order_process p on p.stockin_qa_order_id = o.stockin_qa_order_id and p.process_name = '质检稽查' and p.status &lt;&gt; 'WAIT_INSPECT'
        left join bd_space bs on o.space_id = bs.space_id
        left join supplier s on s.supplier_id = o.supplier_id
        <include refid="page_inspect_where"></include>
        order by p.create_date desc
    </select>
    <select id="pageInspectCount" resultType="java.lang.Long">
        select count(p.stockin_qa_order_process_id)
        from stockin_qa_order o
        inner join stockin_qa_order_detail detail on detail.stockin_qa_order_id = o.stockin_qa_order_id
        inner join stockin_qa_order_process p on p.stockin_qa_order_id = o.stockin_qa_order_id and p.process_name = '质检稽查'  and p.status &lt;&gt; 'WAIT_INSPECT'
        <include refid="page_inspect_where"></include>
    </select>
    <select id="countInspectQty" resultType="com.nsy.api.wms.response.qa.StockinQaInspectCountQtyResponse">
        select
            ifnull(sum(detail.unqualified_count),0) as unqualifiedCount,
            ifnull(sum(detail.return_count),0) as returnCount,
            ifnull(SUM(p.inspect_count),0) as inspectTotalCount
        from stockin_qa_order o
        inner join stockin_qa_order_detail detail on detail.stockin_qa_order_id = o.stockin_qa_order_id
        inner join stockin_qa_order_process p on p.stockin_qa_order_id = o.stockin_qa_order_id and p.process_name = '质检稽查'  and p.status &lt;&gt; 'WAIT_INSPECT'
        <include refid="page_inspect_where"></include>
    </select>
    <select id="batchInspectList" resultType="com.nsy.api.wms.response.qa.StockinQaInspectBatchResponse">
        select o.stockin_qa_order_id,
               o.space_name,
        o.internal_box_code,
        o.spu,
        o.product_id,
        o.sku,
        o.skc,
        o.department,
        o.apply_department,
        o.inspect_status,
        detail.box_qty,
        detail.arrival_count,
        o.supplier_name,
        detail.test_total_count,
        o.unqualified_category,
        o.process_status,
        o.result,
        o.qc_user_name,
        o.complete_date,
        GROUP_CONCAT(DISTINCT oi.supplier_delivery_no) as supplierDeliveryNo,
        si.image_url,
        si.preview_image_url,
        si.thumbnail_image_url
        from stockin_qa_order o
        left join supplier s on s.supplier_id = o.supplier_id
        inner join stockin_qa_order_detail detail
        on detail.stockin_qa_order_id = o.stockin_qa_order_id
        inner join stockin_qa_order_item oi
        on o.stockin_qa_order_id = oi.stockin_qa_order_id
        inner join product_spec_info si
        on si.sku = o.sku
        where oi.supplier_delivery_no = #{request.supplierDeliveryNo}
          and o.inspect_status = 'WAIT_INSPECT'
        <if test="request.spu != null and request.spu !=''">
            AND o.spu = #{request.spu}
        </if>
        <if test="request.skc != null and request.skc !=''">
            AND o.skc = #{request.skc}
        </if>
        <if test="request.sku != null and request.sku !=''">
            AND o.sku = #{request.sku}
        </if>
        group by o.stockin_qa_order_id
    </select>
    <select id="pageWaitInspectTaskList" resultType="com.nsy.api.wms.response.qa.StockinQaInspectPageResponse">
        select
        o.stockin_qa_order_id,
        o.internal_box_code,
        o.product_id,
        o.sku,
        o.skc,
        o.department,
        o.apply_department,
        o.qc_user_name,
        o.complete_date,
        detail.box_qty,
        o.supplier_name,
        detail.direct_return_count,
        detail.test_total_count,
        detail.unqualified_count,
        detail.return_count,
        detail.concessions_count,
        detail.inspect_total_count,
        o.unqualified_category,
        o.inspect_status,
        o.inspect_result as result,
        o.inspect_complete_date,
        o.update_date,
        bs.space_name,
        detail.qa_inspect_date
        from stockin_qa_order o
        inner join stockin_qa_order_detail detail
        on detail.stockin_qa_order_id = o.stockin_qa_order_id
        left join bd_space bs on o.space_id = bs.space_id
        left join supplier s on s.supplier_id = o.supplier_id
        <include refid="page_wait_inspect_where"></include>
        order by o.update_date desc
    </select>
    
    <select id="pageWaitInspectCount" resultType="java.lang.Long">
        select count(o.stockin_qa_order_id)
        from stockin_qa_order o
        inner join stockin_qa_order_detail detail
        on detail.stockin_qa_order_id = o.stockin_qa_order_id
        <include refid="page_wait_inspect_where"></include>
    </select>

    <update id="clearAppointor">
        update stockin_qa_order
        set appointor = null
        where stockin_qa_order_id = #{stockinQaOrderId}
    </update>
    <update id="updateInspectStatusNull">
        update stockin_qa_order
        set inspect_status = null
        where stockin_qa_order_id = #{stockinQaOrderId}
    </update>
    
    <update id="updateWaitInspectStatus">
        update stockin_qa_order
        set inspect_status = 'WAIT_INSPECT',
            inspect_complete_date = null
        where stockin_qa_order_id = #{stockinQaOrderId}
    </update>

    <sql id="page_wait_inspect_where">
        <where>
            <if test="request.inspectResultIsNotNUll != null and request.inspectResultIsNotNUll == true">
                AND o.inspect_result is not null
            </if>

            <if test="request.internalBoxCode != null and request.internalBoxCode !=''">
                AND o.internal_box_code = #{request.internalBoxCode}
            </if>

            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and o.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and o.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierId != null">
                AND o.supplier_id =#{request.supplierId}
            </if>
            <if test="request.resultList != null and request.resultList.size() > 0 ">
                and o.inspect_result in
                <foreach collection="request.resultList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.processStatusList != null and request.processStatusList.size() > 0 ">
                and o.process_status in
                <foreach collection="request.processStatusList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.inspectStatusList != null and request.inspectStatusList.size() > 0 ">
                and o.inspect_status in
                <foreach collection="request.inspectStatusList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.spaceIdList != null and request.spaceIdList.size() > 0 ">
                and o.space_id in
                <foreach collection="request.spaceIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.unqualifiedCategory != null and request.unqualifiedCategory !=''">
                AND o.unqualified_category = #{request.unqualifiedCategory}
            </if>
            <if test="request.completeStartDate != null">
                and o.inspect_complete_date &gt;= #{request.completeStartDate}
            </if>
            <if test="request.completeEndDate != null">
                and o.inspect_complete_date &lt;= #{request.completeEndDate}
            </if>

            <if test="request.createStartDate != null">
                and detail.qa_inspect_date &gt;= #{request.createStartDate}
            </if>
            <if test="request.createEndDate != null">
                and detail.qa_inspect_date &lt;= #{request.createEndDate}
            </if>
        </where>
    </sql>

    <sql id="page_inspect_where">
        <where>
            <if test="request.inspectResultIsNotNUll != null and request.inspectResultIsNotNUll == true">
                AND o.inspect_result is not null
            </if>

            <if test="request.internalBoxCode != null and request.internalBoxCode !=''">
                AND o.internal_box_code = #{request.internalBoxCode}
            </if>

            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and o.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and o.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierId != null">
                AND o.supplier_id =#{request.supplierId}
            </if>
            <if test="request.resultList != null and request.resultList.size() > 0 ">
                and p.status in
                <foreach collection="request.resultList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.batchReturnFlag != null and request.batchReturnFlag == true">
                and (p.status != 'UNQUALIFIED' or (p.status = 'UNQUALIFIED' and detail.box_qty = detail.return_count))
            </if>
            <if test="request.someReturnFlag != null and request.someReturnFlag == true">
                and (p.status != 'UNQUALIFIED' or (p.status = 'UNQUALIFIED' and detail.box_qty &gt; detail.return_count))
            </if>
            <if test="request.processStatusList != null and request.processStatusList.size() > 0 ">
                and o.process_status in
                <foreach collection="request.processStatusList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.inspectStatusList != null and request.inspectStatusList.size() > 0 ">
                and o.inspect_status in
                <foreach collection="request.inspectStatusList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.spaceIdList != null and request.spaceIdList.size() > 0 ">
                and o.space_id in
                <foreach collection="request.spaceIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.unqualifiedCategory != null and request.unqualifiedCategory !=''">
                AND o.unqualified_category = #{request.unqualifiedCategory}
            </if>
            <if test="request.completeStartDate != null">
                and p.operate_date &gt;= #{request.completeStartDate}
            </if>
            <if test="request.completeEndDate != null">
                and p.operate_date &lt;= #{request.completeEndDate}
            </if>
            <if test="request.createStartDate != null">
                and p.create_date &gt;= #{request.createStartDate}
            </if>
            <if test="request.createEndDate != null">
                and p.create_date &lt;= #{request.createEndDate}
            </if>
        </where>
    </sql>

    <sql id="page_where">
        <where>
            <if test="request.internalBoxCode != null and request.internalBoxCode !=''">
                AND o.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.groupUserIdList != null and request.groupUserIdList.size() > 0 ">
                and o.user_id in
                <foreach collection="request.groupUserIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.categoryIdList != null and request.categoryIdList.size() > 0 ">
                and p.category_id in
                <foreach collection="request.categoryIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and o.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and o.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierId != null">
                AND o.supplier_id =#{request.supplierId}
            </if>
            <if test="request.resultList != null and request.resultList.size() > 0 ">
                and o.result in
                <foreach collection="request.resultList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.processStatusList != null and request.processStatusList.size() > 0 ">
                and o.process_status in
                <foreach collection="request.processStatusList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.skuTypeInfo != null and request.skuTypeInfo.size() > 0 ">
                AND EXISTS (
                select stockin_qa_order_id from stockin_qa_order_sku_type_info
                where stockin_qa_order_id = o.stockin_qa_order_id
                and sku_type in
                <foreach collection="request.skuTypeInfo" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="request.spaceName != null and request.spaceName !=''">
                AND o.space_name = #{request.spaceName}
            </if>

            <if test="request.packageName != null and request.packageName !=''">
                AND EXISTS (
                select stockin_qa_order_id from stockin_qa_order_item
                where stockin_qa_order_id = o.stockin_qa_order_id
                AND package_name = #{request.packageName}
                )
            </if>

            <if test="request.operator != null and request.operator !=''">
                AND o.qc_user_name = #{request.operator}
            </if>

            <if test="request.qcUserList != null and request.qcUserList.size() > 0 ">
                and o.qc_user_name in
                <foreach collection="request.qcUserList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spaceId != null">
                AND o.space_id = #{request.spaceId}
            </if>
            <if test="request.spaceIdList != null and request.spaceIdList.size() > 0 ">
                and o.space_id in
                <foreach collection="request.spaceIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.unqualifiedCategory != null and request.unqualifiedCategory !=''">
                AND o.unqualified_category = #{request.unqualifiedCategory}
            </if>

            <if test="request.applyDepartment != null and request.applyDepartment !=''">
                AND o.apply_department = #{request.applyDepartment}
            </if>

            <if test="request.department != null and request.department !=''">
                AND o.department = #{request.department}
            </if>

            <if test="request.isReturnApply != null and request.isReturnApply == 0">
                AND EXISTS (
                select stockin_qa_order_id from stockin_qa_order_item
                where stockin_qa_order_id = o.stockin_qa_order_id
                AND purchasing_apply_type != 6
                )
            </if>
            <if test="request.isReturnApply != null and request.isReturnApply == 1">
                AND EXISTS (
                select stockin_qa_order_id from stockin_qa_order_item
                where stockin_qa_order_id = o.stockin_qa_order_id
                AND purchasing_apply_type = 6
                )
            </if>

            <if test="request.startDate != null">
                and o.create_date &gt;= #{request.startDate}
            </if>
            <if test="request.endDate != null">
                and o.create_date &lt;= #{request.endDate}
            </if>

            <if test="request.updateStartDate != null">
                and o.update_date &gt;= #{request.updateStartDate}
            </if>
            <if test="request.updateEndDate != null">
                and o.update_date &lt;= #{request.updateEndDate}
            </if>
            <if test="request.stockinStartDate != null">
                and detail.stockin_date &gt;= #{request.stockinStartDate}
            </if>
            <if test="request.stockinEndDate != null">
                and detail.stockin_date &lt;= #{request.stockinEndDate}
            </if>


            <if test="request.completeStartDate != null">
                and o.complete_date &gt;= #{request.completeStartDate}
            </if>
            <if test="request.completeEndDate != null">
                and o.complete_date &lt;= #{request.completeEndDate}
            </if>
        </where>
    </sql>
</mapper>