package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-27 09:50:22
 */
@ApiModel(value = "StockoutLogisticsBatchPageResponse", description = "返回对象")
public class StockoutLogisticsBatchPageResponse {
    @ApiModelProperty("")
    private Integer id;

    /**
     * 地区
     */
    @ApiModelProperty("地区")
    private String location;

    @ApiModelProperty("关联id")
    private Integer referenceId;

    /**
     * 物流追踪波次
     */
    @ApiModelProperty("物流追踪波次")
    private String logisticsBatch;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    private String logisticsCompany;


    /**
     * 状态(待处理WAIT_DEAL、核对不通过DIFFERENCE、核对完成COMPLETION)
     */
    @ApiModelProperty("状态(待处理WAIT_DEAL、核对不通过DIFFERENCE、核对完成COMPLETION)")
    private String status;

    @ApiModelProperty("状态Str(待处理WAIT_DEAL、核对不通过DIFFERENCE、核对完成COMPLETION)")
    private String statusStr;

    /**
     * 差异类型(1待差异判断，2无差异，3重量差异，4单价差异，5重量和单价差异)
     */
    @ApiModelProperty("差异类型(1待差异判断，2无差异，3重量差异，4单价差异，5重量和单价差异)")
    private Integer differenceType;

    @ApiModelProperty("差异类型Str(1待差异判断，2无差异，3重量差异，4单价差异，5重量和单价差异)")
    private String differenceTypeStr;

    /**
     * 价格id
     */
    @ApiModelProperty("价格id")
    private Integer predictPriceId;

    @ApiModelProperty("预估单价")
    private BigDecimal predictPrice;

    @ApiModelProperty("预估总价")
    private BigDecimal predictTotalPrice;

    private Integer reviewResult;

    private String reviewResultCn;

    private Integer financialPushStatus;

    private String financialPushStatusCn;



    @ApiModelProperty("仓库称重总重量")
    private BigDecimal predictWeight;

    @ApiModelProperty("仓库称重计费总重量")
    private BigDecimal chargeWeight;

    /**
     * 是否当天(1是 0否)
     */
    @ApiModelProperty("是否当天(是当天 /否)")
    private Boolean exactDay;

    /**
     * 实际重量
     */
    @ApiModelProperty("实际重量")
    private BigDecimal realWeight;

    /**
     * 实际单价
     */
    @ApiModelProperty("实际单价")
    private BigDecimal realUnitPrice;

    /**
     * 实际总价
     */
    @ApiModelProperty("实际总价")
    private BigDecimal realPrice;

    /**
     * 税费
     */
    @ApiModelProperty("税费")
    private BigDecimal realTax;

    /**
     * 报关费用
     */
    @ApiModelProperty("报关费用")
    private BigDecimal realDeclarePrice;

    /**
     * 核对文件id
     */
    @ApiModelProperty("核对文件id")
    private Integer checkDocumentId;

    /**
     * 附件地址
     */
    @ApiModelProperty("附件地址")
    private String memoDocumentUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("Shipment详情")
    private List<StockoutLogisticsBatchShipmentResponse> shipmentList;


    // 运费差异
    @ApiModelProperty("运费差异")
    private BigDecimal diffPrice;

    // 重量差异
    @ApiModelProperty("重量差异")
    private BigDecimal diffWeight;

    @ApiModelProperty("核对文件名")
    private String checkDocumentName;


    public Integer getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(Integer reviewResult) {
        this.reviewResult = reviewResult;
    }

    public String getReviewResultCn() {
        return reviewResultCn;
    }

    public void setReviewResultCn(String reviewResultCn) {
        this.reviewResultCn = reviewResultCn;
    }

    public Integer getFinancialPushStatus() {
        return financialPushStatus;
    }

    public void setFinancialPushStatus(Integer financialPushStatus) {
        this.financialPushStatus = financialPushStatus;
    }

    public String getFinancialPushStatusCn() {
        return financialPushStatusCn;
    }

    public void setFinancialPushStatusCn(String financialPushStatusCn) {
        this.financialPushStatusCn = financialPushStatusCn;
    }

    public Integer getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Integer referenceId) {
        this.referenceId = referenceId;
    }

    public String getCheckDocumentName() {
        return checkDocumentName;
    }

    public void setCheckDocumentName(String checkDocumentName) {
        this.checkDocumentName = checkDocumentName;
    }

    public BigDecimal getPredictPrice() {
        return predictPrice;
    }

    public void setPredictPrice(BigDecimal predictPrice) {
        this.predictPrice = predictPrice;
    }

    public BigDecimal getPredictTotalPrice() {
        return predictTotalPrice;
    }

    public void setPredictTotalPrice(BigDecimal predictTotalPrice) {
        this.predictTotalPrice = predictTotalPrice;
    }

    public BigDecimal getDiffPrice() {
        return diffPrice;
    }

    public void setDiffPrice(BigDecimal diffPrice) {
        this.diffPrice = diffPrice;
    }

    public BigDecimal getDiffWeight() {
        return diffWeight;
    }

    public void setDiffWeight(BigDecimal diffWeight) {
        this.diffWeight = diffWeight;
    }

    public BigDecimal getPredictWeight() {
        return predictWeight;
    }

    public void setPredictWeight(BigDecimal predictWeight) {
        this.predictWeight = predictWeight;
    }

    public List<StockoutLogisticsBatchShipmentResponse> getShipmentList() {
        return shipmentList;
    }

    public void setShipmentList(List<StockoutLogisticsBatchShipmentResponse> shipmentList) {
        this.shipmentList = shipmentList;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getDifferenceTypeStr() {
        return differenceTypeStr;
    }

    public void setDifferenceTypeStr(String differenceTypeStr) {
        this.differenceTypeStr = differenceTypeStr;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLogisticsBatch() {
        return logisticsBatch;
    }

    public void setLogisticsBatch(String logisticsBatch) {
        this.logisticsBatch = logisticsBatch;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getDifferenceType() {
        return differenceType;
    }

    public void setDifferenceType(Integer differenceType) {
        this.differenceType = differenceType;
    }

    public Integer getPredictPriceId() {
        return predictPriceId;
    }

    public void setPredictPriceId(Integer predictPriceId) {
        this.predictPriceId = predictPriceId;
    }

    public Boolean getExactDay() {
        return exactDay;
    }

    public void setExactDay(Boolean exactDay) {
        this.exactDay = exactDay;
    }

    public BigDecimal getRealWeight() {
        return realWeight;
    }

    public void setRealWeight(BigDecimal realWeight) {
        this.realWeight = realWeight;
    }

    public BigDecimal getRealUnitPrice() {
        return realUnitPrice;
    }

    public void setRealUnitPrice(BigDecimal realUnitPrice) {
        this.realUnitPrice = realUnitPrice;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public BigDecimal getRealTax() {
        return realTax;
    }

    public void setRealTax(BigDecimal realTax) {
        this.realTax = realTax;
    }

    public BigDecimal getRealDeclarePrice() {
        return realDeclarePrice;
    }

    public void setRealDeclarePrice(BigDecimal realDeclarePrice) {
        this.realDeclarePrice = realDeclarePrice;
    }

    public Integer getCheckDocumentId() {
        return checkDocumentId;
    }

    public void setCheckDocumentId(Integer checkDocumentId) {
        this.checkDocumentId = checkDocumentId;
    }

    public String getMemoDocumentUrl() {
        return memoDocumentUrl;
    }

    public void setMemoDocumentUrl(String memoDocumentUrl) {
        this.memoDocumentUrl = memoDocumentUrl;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public BigDecimal getChargeWeight() {
        return chargeWeight;
    }

    public void setChargeWeight(BigDecimal chargeWeight) {
        this.chargeWeight = chargeWeight;
    }
}

