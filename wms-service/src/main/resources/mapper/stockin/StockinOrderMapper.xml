<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderMapper">
    <select id="pageSearchList" resultType="com.nsy.api.wms.domain.stockin.StockinOrderListInfo">
        select
        o.stockin_order_id as stockinOrderId,
        o.real_stockin_order_id as realStockinOrderId,
        o.stockin_order_no as stockinOrderNo,
        o.logistics_no as logisticsNo,
        t.supplier_delivery_box_code as supplierDeliveryBoxCode,
        taskItem.space_id as spaceId,
        sum(i.return_qty) as returnQty,
        sum(i.shelved_qty) as shelveQty,
        o.stockin_type as stockinType,
        ifnull(ps.supplier_name,o.supplier_name) as supplierName,
        o.status as status,
        t.box_index as boxIndex,
        t.supplier_delivery_no as supplierDeliveryNo,
        t.description as description,
        t.operator as operator,
        o.create_date as createDate,
        t.operate_end_date as operateEndDate,
        i.is_fba_quick as isFbaQuick,
        i.vacuum_flag as vacuumFlag,
        ps.receipt_place as receiptPlace,
        ps.remarks as remarks,
        taskItem.area_name as areaName,
        SUM(if(i.`status`='WAIT_RETURN',i.qty,0)) as waitReturnQty,
        SUM(if(i.`status`='WAIT_RETURN',i.return_qty,0)) as hasReturnQty,
        o.complete_shelved_date
        from stockin_order o
        LEFT JOIN (select
        vacuum_flag,is_fba_quick,shelved_qty,return_qty,status,qty,purchase_plan_no,branch_purchase_plan_no,stockin_order_id,task_item_id,internal_box_code,brand_name
        from stockin_order_item
        <where>
            <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                AND (purchase_plan_no =#{query.purchasePlanNo} or branch_purchase_plan_no =#{query.purchasePlanNo})
            </if>
            <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                AND internal_box_code =#{query.internalBoxCode}
            </if>
            <if test="query.isFbaQuick != null">
                and is_fba_quick = #{query.isFbaQuick}
            </if>
            <if test="query.vacuumFlag != null">
                and vacuum_flag = #{query.vacuumFlag}
            </if>
            <if test="query.brandName != null and query.brandName !=''">
                AND brand_name =#{query.brandName}
            </if>
        </where>
        ) i on o.stockin_order_id = i.stockin_order_id
        LEFT JOIN stockin_order_task t on o.task_id=t.task_id
        LEFT JOIN stockin_order_task_item taskItem on taskItem.task_item_id=i.task_item_id
        AND (i.purchase_plan_no is null or i.purchase_plan_no=taskItem.purchase_plan_no)
        LEFT JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        <include refid="pageCondition"></include>
        GROUP BY o.stockin_order_id
        order by o.stockin_order_id desc
    </select>

    <select id="pageStockinOrderItemList" resultType="com.nsy.api.wms.domain.stockin.StockinOrderDetailExport">
        select
        o.stockin_order_id as stockinOrderId,
        o.real_stockin_order_id as realStockinOrderId,
        o.stockin_order_no as stockinOrderNo,
        o.logistics_no as logisticsNo,
        o.supplier_delivery_box_code as supplierDeliveryBoxCode,
        min(s.space_name) as spaceName,
        sum(i.return_qty) as returnQty,
        o.stockin_type as stockinType,
        o.supplier_name as supplierName,
        o.status as status,
        sum(i.qty) as qty,
        t.box_index as boxIndex,
        i.purchase_plan_no as purchasePlanNo,
        t.supplier_delivery_no as supplierDeliveryNo,
        t.description as description,
        t.operator as operator,
        o.create_date as createDate,
        t.operate_end_date as operateEndDate,
        i.sku,
        o.complete_shelved_date
        from stockin_order_item i
        inner join stockin_order o on o.stockin_order_id = i.stockin_order_id
        LEFT JOIN stockin_order_task t on o.task_id=t.task_id
        LEFT JOIN stockin_order_task_item taskItem on taskItem.task_item_id=i.task_item_id AND (i.purchase_plan_no is
        null or i.purchase_plan_no=taskItem.purchase_plan_no)
        LEFT JOIN bd_space s on taskItem.space_id= s.space_id
        <include refid="itemCondition"></include>
        GROUP BY o.stockin_order_id,i.sku,i.purchase_plan_no
        order by o.stockin_order_id desc
    </select>

    <select id="pageSearchListCount" resultType="java.lang.Integer">
        select
        count(distinct o.stockin_order_id)
        from stockin_order o
        LEFT JOIN stockin_order_task t on o.task_id=t.task_id
        <where>
            and o.location = #{query.location}
            <if test="query.stockinOrderIds != null and query.stockinOrderIds.size() > 0">
                and o.stockin_order_id in
                <foreach collection="query.stockinOrderIds" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
            <if test="query.stockinOrderNo != null and query.stockinOrderNo !=''">
                AND o.stockin_order_no =#{query.stockinOrderNo}
            </if>
            <if test="query.hasBrand != null">
                and o.has_brand = #{query.hasBrand}
            </if>
            <if test="query.supplierId != null">
                AND o.supplier_id =#{query.supplierId}
            </if>
            <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                AND o.supplier_delivery_box_code =#{query.supplierDeliveryBoxCode}
            </if>
            <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                AND t.supplier_delivery_no =#{query.supplierDeliveryNo}
            </if>
            <if test="(query.areaNameList != null and query.areaNameList.size()>0)
                    or (query.spaceIds != null and query.spaceIds.size() > 0)">

                and exists(select taskItem.task_item_id from stockin_order_task_item taskItem
                inner join stockin_order_item oi
                on oi.task_item_id = taskItem.task_item_id
                where taskItem.task_id = o.task_id
                <if test="query.areaNameList != null and query.areaNameList.size()>0">
                    and taskItem.area_name in
                    <foreach collection="query.areaNameList" separator="," index="index" item="areaName" open="("
                             close=")">
                        #{areaName}
                    </foreach>
                </if>
                <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                    and taskItem.space_id in
                    <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                             close=")">
                        #{spaceId}
                    </foreach>
                </if>
                limit 1)
            </if>
            <if test="query.stockinTypeList != null and query.stockinTypeList.size()>0">
                and o.stockin_type in
                <foreach collection="query.stockinTypeList" separator="," index="index" item="stockinType" open="("
                         close=")">
                    #{stockinType}
                </foreach>
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                AND EXISTS (select ssi.spot_info_id from stockin_spot_info ssi where
                ssi.supplier_delivery_no=t.supplier_delivery_no and ssi.logistics_no = #{query.logisticsNo})
            </if>
            <if test="query.sku != null and query.sku !=''">
                AND o.stockin_order_id in (select distinct item.stockin_order_id from stockin_order_item item where
                item.sku like concat(#{query.sku},'%'))
            </if>

            <if test="query.completeShelvedStartDate != null">
                and o.complete_shelved_date &gt;= #{query.completeShelvedStartDate}
            </if>
            <if test="query.completeShelvedEndDate != null">
                and o.complete_shelved_date &lt;= #{query.completeShelvedEndDate}
            </if>
            <if test="(query.purchasePlanNo != null and query.purchasePlanNo !='')
                    or (query.internalBoxCode != null and query.internalBoxCode !='')
                    or (query.isFbaQuick != null)
                    or (query.vacuumFlag != null)
                    or (query.brandName != null and query.brandName !='')">

                and exists(select i.stockin_order_item_id from stockin_order_item i
                where i.stockin_order_id = o.stockin_order_id
                <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                    AND (i.purchase_plan_no =#{query.purchasePlanNo} or i.branch_purchase_plan_no
                    =#{query.purchasePlanNo})
                </if>
                <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                    AND i.internal_box_code =#{query.internalBoxCode}
                </if>
                <if test="query.isFbaQuick != null">
                    and i.is_fba_quick = #{query.isFbaQuick}
                </if>
                <if test="query.vacuumFlag != null">
                    and i.vacuum_flag = #{query.vacuumFlag}
                </if>
                <if test="query.brandName != null and query.brandName !=''">
                    and i.brand_name = #{query.brandName}
                </if>
                limit 1)
            </if>
            <if test="(query.remarks != null and query.remarks !='')
                    or (query.receiptPlace != null and query.receiptPlace !='')">

                and exists(select ps.platform_schedule_id from stock_platform_schedule ps
                        where t.platform_schedule_id = ps.platform_schedule_id
                        <if test="query.remarks != null and query.remarks !=''">
                            and ps.remarks like concat(#{query.remarks},'%')
                        </if>
                        <if test="query.receiptPlace != null and query.receiptPlace !=''">
                            and ps.receipt_place like concat(#{query.receiptPlace},'%')
                        </if>
                        limit 1)
            </if>
            <choose>
                <when test="query.unfinishedInThreeDay != null and query.unfinishedInThreeDay == true">
                    and o.create_date &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY) and o.status not in ('COMPLETED',
                    'RECEIVED')
                </when>
                <otherwise>
                    <if test="query.statusList != null and query.statusList.size()>0">
                        AND o.status in
                        <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                                 close=")">
                            #{status}
                        </foreach>
                    </if>

                    <if test="query.createStartDate != null">
                        and o.create_date &gt;= #{query.createStartDate}
                    </if>
                    <if test="query.createEndDate != null">
                        and o.create_date &lt;= #{query.createEndDate}
                    </if>
                </otherwise>
            </choose>
        </where>
    </select>

    <sql id="itemCondition">
        <where>
            and o.location = #{query.location}
            <if test="query.stockinOrderIds != null and query.stockinOrderIds.size() > 0">
                and o.stockin_order_id in
                <foreach collection="query.stockinOrderIds" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
            <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                AND (i.purchase_plan_no =#{query.purchasePlanNo} or i.branch_purchase_plan_no =#{query.purchasePlanNo})
            </if>
            <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                AND i.internal_box_code =#{query.internalBoxCode}
            </if>
            <if test="query.isFbaQuick != null">
                and i.is_fba_quick = #{query.isFbaQuick}
            </if>
            <if test="query.stockinOrderNo != null and query.stockinOrderNo !=''">
                AND o.stockin_order_no =#{query.stockinOrderNo}
            </if>
            <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                and taskItem.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
            <if test="query.supplierId != null">
                AND o.supplier_id =#{query.supplierId}
            </if>
            <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                AND t.supplier_delivery_box_code =#{query.supplierDeliveryBoxCode}
            </if>
            <if test="query.areaNameList != null and query.areaNameList.size()>0">
                and taskItem.area_name in
                <foreach collection="query.areaNameList" separator="," index="index" item="areaName" open="("
                         close=")">
                    #{areaName}
                </foreach>
            </if>
            <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                AND (i.purchase_plan_no =#{query.purchasePlanNo} or i.branch_purchase_plan_no =#{query.purchasePlanNo})
            </if>
            <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                AND i.internal_box_code =#{query.internalBoxCode}
            </if>
            <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                AND t.supplier_delivery_no =#{query.supplierDeliveryNo}
            </if>
            <if test="query.stockinTypeList != null and query.stockinTypeList.size()>0">
                and o.stockin_type in
                <foreach collection="query.stockinTypeList" separator="," index="index" item="stockinType" open="("
                         close=")">
                    #{stockinType}
                </foreach>
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                AND EXISTS (select ssi.spot_info_id from stockin_spot_info ssi where
                ssi.supplier_delivery_no=t.supplier_delivery_no and ssi.logistics_no = #{query.logisticsNo})
            </if>
            <if test="query.sku != null and query.sku !=''">
                AND o.stockin_order_id in (select distinct item.stockin_order_id from stockin_order_item item where
                item.sku like concat(#{query.sku},'%'))
            </if>

            <if test="query.completeShelvedStartDate != null">
                and o.complete_shelved_date &gt;= #{query.completeShelvedStartDate}
            </if>
            <if test="query.completeShelvedEndDate != null">
                and o.complete_shelved_date &lt;= #{query.completeShelvedEndDate}
            </if>
            <if test="query.isFbaQuick != null">
                and i.is_fba_quick = #{query.isFbaQuick}
            </if>
            <choose>
                <when test="query.unfinishedInThreeDay != null and query.unfinishedInThreeDay == true">
                    and o.create_date &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY) and o.status not in ('COMPLETED',
                    'RECEIVED')
                </when>
                <otherwise>
                    <if test="query.statusList != null and query.statusList.size()>0">
                        AND o.status in
                        <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                                 close=")">
                            #{status}
                        </foreach>
                    </if>

                    <if test="query.createStartDate != null">
                        and o.create_date &gt;= #{query.createStartDate}
                    </if>
                    <if test="query.createEndDate != null">
                        and o.create_date &lt;= #{query.createEndDate}
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>

    <sql id="pageCondition">
        <where>
            and o.location = #{query.location}
            <if test="query.stockinOrderIds != null and query.stockinOrderIds.size() > 0">
                and o.stockin_order_id in
                <foreach collection="query.stockinOrderIds" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
            <if test="query.stockinOrderNo != null and query.stockinOrderNo !=''">
                AND o.stockin_order_no =#{query.stockinOrderNo}
            </if>
            <if test="query.hasBrand != null">
                and o.has_brand = #{query.hasBrand}
            </if>
            <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                and taskItem.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
            <if test="query.supplierId != null">
                AND ps.supplier_id =#{query.supplierId}
            </if>
            <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                AND t.supplier_delivery_box_code =#{query.supplierDeliveryBoxCode}
            </if>
            <if test="query.areaNameList != null and query.areaNameList.size()>0">
                and taskItem.area_name in
                <foreach collection="query.areaNameList" separator="," index="index" item="areaName" open="("
                         close=")">
                    #{areaName}
                </foreach>
            </if>
            <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                AND (i.purchase_plan_no =#{query.purchasePlanNo} or i.branch_purchase_plan_no =#{query.purchasePlanNo})
            </if>
            <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                AND i.internal_box_code =#{query.internalBoxCode}
            </if>
            <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                AND t.supplier_delivery_no =#{query.supplierDeliveryNo}
            </if>
            <if test="query.stockinTypeList != null and query.stockinTypeList.size()>0">
                and o.stockin_type in
                <foreach collection="query.stockinTypeList" separator="," index="index" item="stockinType" open="("
                         close=")">
                    #{stockinType}
                </foreach>
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                AND EXISTS (select ssi.spot_info_id from stockin_spot_info ssi where
                ssi.supplier_delivery_no=t.supplier_delivery_no and ssi.logistics_no = #{query.logisticsNo})
            </if>
            <if test="query.sku != null and query.sku !=''">
                AND o.stockin_order_id in (select distinct item.stockin_order_id from stockin_order_item item where
                item.sku like concat(#{query.sku},'%'))
            </if>

            <if test="query.completeShelvedStartDate != null">
                and o.complete_shelved_date &gt;= #{query.completeShelvedStartDate}
            </if>
            <if test="query.completeShelvedEndDate != null">
                and o.complete_shelved_date &lt;= #{query.completeShelvedEndDate}
            </if>
            <if test="query.isFbaQuick != null">
                and i.is_fba_quick = #{query.isFbaQuick}
            </if>
            <if test="query.vacuumFlag != null">
                and i.vacuum_flag = #{query.vacuumFlag}
            </if>
            <if test="query.brandName != null and query.brandName !=''">
                and i.brand_name = #{query.brandName}
            </if>
            <if test="query.remarks != null and query.remarks !=''">
                and ps.remarks like concat(#{query.remarks},'%')
            </if>
            <if test="query.receiptPlace != null and query.receiptPlace !=''">
                and ps.receipt_place like concat(#{query.receiptPlace},'%')
            </if>
            <choose>
                <when test="query.unfinishedInThreeDay != null and query.unfinishedInThreeDay == true">
                    and o.create_date &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY) and o.status not in ('COMPLETED',
                    'RECEIVED')
                </when>
                <otherwise>
                    <if test="query.statusList != null and query.statusList.size()>0">
                        AND o.status in
                        <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                                 close=")">
                            #{status}
                        </foreach>
                    </if>

                    <if test="query.createStartDate != null">
                        and o.create_date &gt;= #{query.createStartDate}
                    </if>
                    <if test="query.createEndDate != null">
                        and o.create_date &lt;= #{query.createEndDate}
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>

    <select id="statusCount" resultType="com.nsy.api.wms.domain.stockin.StockinOrderStatusDTO">
        SELECT status,
               count(*) as qty
        FROM stockin_order
        where status != 'COMPLETED'
        GROUP BY status
    </select>

    <select id="searchByStockinOrderIdList" resultType="com.nsy.api.wms.response.stockin.StockinOrderInfoResponse">
        select
        o.stockin_order_id as stockinOrderId,
        o.stockin_order_no as stockinOrderNo,
        t.supplier_delivery_no as supplierDeliveryNo
        from stockin_order o
        LEFT JOIN stockin_order_task t on o.task_id=t.task_id
        <where>
            <if test="stockinOrderIdList != null and stockinOrderIdList.size()>0">
                AND o.stockin_order_id in
                <foreach collection="stockinOrderIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="pageSearchReceiptRecordList"
            resultType="com.nsy.api.wms.response.stockin.StockinReceiptRecordListResponse">
        select so.stockin_order_id,
        so.real_stockin_order_id,
        MAX(so.supplier_delivery_box_code) as supplierDeliveryBoxCode,
        MAX(so.stockin_order_no) as stockinOrderNo,
        case when MAX(so.stockin_type) = 'SPOT'
        then SUM(si.shipment_qty)
        else SUM(ti.shipmentQty)
        end as shipmentQty,
        MAX(so.status) as status,
        MAX(so.create_date) as createDate,
        MAX(so.update_date) as updateDate
        from stockin_order so
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN (
        select task_id,purchase_plan_no,
        SUM(expected_qty) as shipmentQty
        from stockin_order_task_item
        <where>
            <if test="query != null and query.purchaseNo != null and query.purchaseNo != ''">
                AND purchase_plan_no = #{query.purchaseNo}
            </if>
            <if test="query!=null and query.sku!=null and query.sku != '' ">
                and (sku like concat(#{query.sku}, '%')
                <if test="query.skuAutoMatchList != null and query.skuAutoMatchList.size() > 0">
                    OR
                    <foreach collection="query.skuAutoMatchList" item="sku" open="(" close=")" separator="OR">
                        sku like concat(#{sku}, '%')
                    </foreach>
                </if>
                )
            </if>
        </where>
        GROUP BY task_id,purchase_plan_no
        ) ti ON ti.task_id= t.task_id
        LEFT JOIN stockin_spot_info si ON si.supplier_delivery_no= t.supplier_delivery_no
        <where>
            <if test="query != null and query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode != ''">
                AND so.supplier_delivery_box_code = #{query.supplierDeliveryBoxCode}
            </if>
            <if test="query != null and query.location != null and query.location != ''">
                AND so.location = #{query.location}
            </if>
        </where>
        GROUP BY so.stockin_order_id
        order by so.stockin_order_id desc
    </select>

    <sql id="receiptListWhere">
            <if test="query != null and query.location != null and query.location != ''">
                AND so.location = #{query.location}
            </if>
            <if test="query != null and query.deliveryConfirmStatus != null and query.deliveryConfirmStatus != ''">
                AND so.delivery_confirm_status = #{query.deliveryConfirmStatus}
            </if>
            <if test="query != null and query.completeShelvedStartDate != null">
                and so.complete_shelved_date &gt;= #{query.completeShelvedStartDate}
            </if>
            <if test="query.completeShelvedEndDate != null">
                and so.complete_shelved_date &lt;= #{query.completeShelvedEndDate}
            </if>
            <if test="query!=null and query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                and t.supplier_delivery_no = #{query.supplierDeliveryNo}
            </if>
            <if test="query!=null and query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                and so.supplier_delivery_box_code = #{query.supplierDeliveryBoxCode}
            </if>
            <if test="query!=null and query.supplierId != null ">
                and ps.supplier_id = #{query.supplierId}
            </if>
            <if test="query!=null and query.purchaseUserName != null and query.purchaseUserName !=''">
                and ps.purchase_user_name = #{query.purchaseUserName}
            </if>
            <if test="query!=null and query.modelMerchandiserEmpName != null and query.modelMerchandiserEmpName !=''">
                and supplier.model_merchandiser_emp_name = #{query.modelMerchandiserEmpName}
            </if>
            <if test="query!=null and query.status != null and query.status != '' ">
                and so.status = #{query.status}
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                and so.status in
                <foreach collection="query.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
                and ba.area_id in
                <foreach collection="query.areaIdList" item="areaId" open="(" close=")" separator=",">
                    #{areaId}
                </foreach>
            </if>
            <if test="query!=null and query.deliveryDateStart!=null">
                and ps.delivery_date >= #{query.deliveryDateStart}
            </if>
            <if test="query!=null and query.deliveryDateEnd!=null">
                and ps.delivery_date &lt;= #{query.deliveryDateEnd}
            </if>
            <if test="query.selectIdList != null and query.selectIdList.size() > 0">
                and so.stockin_order_id in
                <foreach collection="query.selectIdList" item="ids" open="(" close=")" separator=",">
                    #{ids}
                </foreach>
            </if>
            <if test="query!=null and query.spaceId != null ">
                and ti.space_id = #{query.spaceId}
            </if>
            <if test="query!=null and query.purchaseNo != null and query.purchaseNo !=''">
                and ti.purchase_plan_no = #{query.purchaseNo}
            </if>
            <if test="query!=null and query.spu!=null and query.spu != '' ">
                and (pi.spu like concat(#{query.spu}, '%')
                <if test="query.spuAutoMatchList != null and query.spuAutoMatchList.size() > 0">
                    OR
                    <foreach collection="query.spuAutoMatchList" item="spu" open="(" close=")" separator="OR">
                        pi.spu like concat(#{spu}, '%')
                    </foreach>
                </if>
                )
            </if>
            <if test="query!=null and query.sku!=null and query.sku != '' ">
                and (ti.sku like concat(#{query.sku}, '%')
                <if test="query.skuAutoMatchList != null and query.skuAutoMatchList.size() > 0">
                    OR
                    <foreach collection="query.skuAutoMatchList" item="sku" open="(" close=")" separator="OR">
                        ti.sku like concat(#{sku}, '%')
                    </foreach>
                </if>
                )
            </if>
            <if test="query.isFbaQuick != null">
                and ti.is_fba_quick = #{query.isFbaQuick}
            </if>
            <if test="query.brandName != null  and query.brandName != ''">
                and ti.brand_name = #{query.brandName}
            </if>
        GROUP BY so.stockin_order_id
        <if test="query!=null and query.difference!=null ">
            <if test="query.difference == 1">
                HAVING SUM(ti.stockin_qty) != shipmentQty
            </if>
            <if test="query.difference == 0">
                HAVING SUM(ti.stockin_qty) = shipmentQty
            </if>
        </if>
    </sql>

    <select id="pageSearchReceiptList" resultType="com.nsy.api.wms.response.stockin.StockinReceiptListInfo">
        SELECT so.stockin_order_id as stockinOrderId,
        so.real_stockin_order_id as realStockinOrderId,
        so.order_type as orderType,
        so.stockin_order_no as stockinOrderNo,
        ps.platform_schedule_id as platform_schedule_id,
        t.supplier_delivery_no as supplierDeliveryNo,
        ps.plan_arrive_date as planArriveDate,
        so.supplier_name as supplierName,
        so.supplier_id as supplierId,
        so.supplier_delivery_box_code as supplierDeliveryBoxCode,
        ps.purchase_user_real_name as purchaseUserRealName,
        supplier.model_merchandiser_emp_name as modelMerchandiserEmpName,
        ps.delivery_date as deliveryDate,
        so.status as status,
        so.delivery_confirm_status deliveryConfirmStatus,
        t.box_index as boxIndex,
        ti.is_fba_quick as isFbaQuick,
        ti.brand_name as brandName,
        group_concat(distinct ti.area_name) as areaName,
        group_concat(distinct ti.purchase_plan_no) as purchaseNo,
        case when MAX(so.stockin_type) = 'SPOT'
        then SUM(si.shipment_qty)
        else SUM(ti.expected_qty)
        end as shipmentQty,
        so.create_date as createDate,
        so.update_date as updateDate,
        so.task_id
        FROM stockin_order so
        inner join supplier supplier on supplier.supplier_id = so.supplier_id
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
        <if test="query!=null and query.spu != null and query.spu !=''">
            INNER JOIN product_info pi ON pi.product_id = ti.product_id
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
        </if>
        LEFT JOIN stockin_spot_info si ON si.logistics_no= so.logistics_no
        where
            1=1
        <include refid="receiptListWhere"></include>
        order by so.stockin_order_id desc
    </select>


    <select id="pageSearchReceiptListCount" resultType="java.lang.Integer">
        select count(*) from(
        SELECT case when MAX(so.stockin_type) = 'SPOT'
        then SUM(si.shipment_qty)
        else SUM(ti.expected_qty)
        end as shipmentQty
        FROM stockin_order so
        inner join supplier supplier on supplier.supplier_id = so.supplier_id
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
        <if test="query!=null and query.spu != null and query.spu !=''">
            INNER JOIN product_info pi ON pi.product_id = ti.product_id
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
        </if>
        LEFT JOIN stockin_spot_info si ON si.logistics_no= so.logistics_no
        where
            1=1
        <include refid="receiptListWhere"></include>) t
    </select>


    <select id="getTabs" resultType="com.nsy.api.wms.response.base.StatusTabResponse">
        SELECT so.status, count(*) as num
        FROM stockin_order so
                 inner join supplier supplier on supplier.supplier_id = so.supplier_id
        WHERE so.status != 'COMPLETED'
        GROUP BY so.status
    </select>

    <select id="findTopByLogisticsNoIgnoreTenant" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        select *
        from stockin_order
        where logistics_no = #{logisticsNo}
    </select>
    <select id="listSearchReceiptItemList"
            resultType="com.nsy.api.wms.response.stockin.StockinReceiptItemListInfo">
        SELECT so.stockin_order_id,
        so.real_stockin_order_id,
        so.order_type,
        so.task_id,
        so.stockin_type,
        so.logistics_no,
        so.stockin_order_no,
        ps.platform_schedule_id,
        soi.stockin_order_item_id,
        so.supplier_delivery_box_code as supplierDeliveryBoxCode,
        t.supplier_delivery_no as supplierDeliveryNo,
        so.supplier_id as supplierId,
        so.supplier_name as supplierName,
        space.space_name,
        ps.purchase_user_real_name as purchaseUserRealName,
        ps.delivery_date as deliveryDate,
        soi.purchase_plan_no,
        soi.sku,
        pi.spu,
        soi.status,
        ifnull(sum(soi.qty),0) as receiveQty,
        ifnull(sum(soi.concessions_count),0) as concessionsQty,
        ifnull(sum(soi.return_qty),0) as returnedQty,
        ifnull(sum(soi.shelved_qty),0) as shelvedQty,
        t.description
        FROM stockin_order so
        inner join supplier supplier on supplier.supplier_id = so.supplier_id
        INNER JOIN stockin_order_item soi ON soi.stockin_order_id = so.stockin_order_id
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        INNER JOIN product_info pi ON pi.product_id = soi.product_id
        INNER JOIN bd_space space On space.space_id = soi.space_id
        <where>
            <if test="stockinOrderIdList != null and stockinOrderIdList.size() > 0">
                and so.stockin_order_id in
                <foreach collection="stockinOrderIdList" item="stockinOrderId" open="(" close=")" separator=",">
                    #{stockinOrderId}
                </foreach>
            </if>
        </where>
        GROUP BY so.stockin_order_id,soi.purchase_plan_no,soi.sku
    </select>
    <select id="searchByRealStockinOrderIdListIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        select * from stockin_order
        <where>
            <if test="stockinOrderIdList != null and stockinOrderIdList.size() > 0">
                real_stockin_order_id in
                <foreach collection="stockinOrderIdList" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="searchByStockinOrderIdListIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        select * from stockin_order
        <where>
            <if test="stockinOrderIdList != null and stockinOrderIdList.size() > 0">
                stockin_order_id in
                <foreach collection="stockinOrderIdList" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteByStockinOrderIdIgnoreTenant">
        delete
        from stockin_order
        where stockin_order_id = #{stockinOrderId}
    </delete>

    <update id="updateIgnoreTenant" parameterType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        UPDATE stockin_order
        SET status                = #{status},
            complete_shelved_date = #{completeShelvedDate},
            update_by=#{updateBy}
        WHERE stockin_order_id = #{stockinOrderId}
    </update>

    <select id="findTopByStockinOrderIdIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        select *
        from stockin_order
        where stockin_order_id = #{stockinOrderId}
    </select>

    <select id="searchByStockinTaskIdListIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        select * from stockin_order
        <where>
            <if test="stockinOrderTaskIdList != null and stockinOrderTaskIdList.size() > 0">
                task_id in
                <foreach collection="stockinOrderTaskIdList" separator="," index="index" item="stockinOrderTaskId"
                         open="("
                         close=")">
                    #{stockinOrderTaskId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findTopBySupplierDeliveryBoxCodeIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderEntity">
        select *
        from stockin_order
        where supplier_delivery_box_code = #{supplierDeliveryBoxCode}
    </select>
    <select id="countStockinOrderItemList" resultType="java.lang.Long">
        select count(*) from (
        select 1
        from stockin_order o
        LEFT JOIN (select
        is_fba_quick,return_qty,sku,status,qty,purchase_plan_no,branch_purchase_plan_no,stockin_order_id,task_item_id,internal_box_code
        from stockin_order_item
        <where>
            <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                AND (purchase_plan_no =#{query.purchasePlanNo} or branch_purchase_plan_no =#{query.purchasePlanNo})
            </if>
            <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                AND internal_box_code =#{query.internalBoxCode}
            </if>
            <if test="query.isFbaQuick != null">
                and is_fba_quick = #{query.isFbaQuick}
            </if>
        </where>
        ) i on o.stockin_order_id = i.stockin_order_id
        LEFT JOIN stockin_order_task t on o.task_id=t.task_id
        LEFT JOIN stockin_order_task_item taskItem on taskItem.task_item_id=i.task_item_id
        AND (i.purchase_plan_no is null or i.purchase_plan_no=taskItem.purchase_plan_no)
        LEFT JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        LEFT JOIN bd_space s on taskItem.space_id= s.space_id
        <where>
            and o.location = #{query.location}
            <if test="query.stockinOrderIds != null and query.stockinOrderIds.size() > 0">
                and o.stockin_order_id in
                <foreach collection="query.stockinOrderIds" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
            <if test="query.stockinOrderNo != null and query.stockinOrderNo !=''">
                AND o.stockin_order_no =#{query.stockinOrderNo}
            </if>
            <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                and taskItem.space_id in
                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                         close=")">
                    #{spaceId}
                </foreach>
            </if>
            <if test="query.supplierId != null">
                AND ps.supplier_id =#{query.supplierId}
            </if>
            <if test="query.supplierDeliveryBoxCode != null and query.supplierDeliveryBoxCode !=''">
                AND t.supplier_delivery_box_code =#{query.supplierDeliveryBoxCode}
            </if>
            <if test="query.purchasePlanNo != null and query.purchasePlanNo !=''">
                AND (i.purchase_plan_no =#{query.purchasePlanNo} or i.branch_purchase_plan_no =#{query.purchasePlanNo})
            </if>
            <if test="query.internalBoxCode != null and query.internalBoxCode !=''">
                AND i.internal_box_code =#{query.internalBoxCode}
            </if>
            <if test="query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
                AND t.supplier_delivery_no =#{query.supplierDeliveryNo}
            </if>
            <if test="query.stockinTypeList != null and query.stockinTypeList.size()>0">
                and o.stockin_type in
                <foreach collection="query.stockinTypeList" separator="," index="index" item="stockinType" open="("
                         close=")">
                    #{stockinType}
                </foreach>
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo !=''">
                AND EXISTS (select ssi.spot_info_id from stockin_spot_info ssi where
                ssi.supplier_delivery_no=t.supplier_delivery_no and ssi.logistics_no = #{query.logisticsNo})
            </if>
            <if test="query.sku != null and query.sku !=''">
                AND o.stockin_order_id in (select distinct item.stockin_order_id from stockin_order_item item where
                item.sku like concat(#{query.sku},'%'))
            </if>

            <if test="query.completeShelvedStartDate != null">
                and o.complete_shelved_date &gt;= #{query.completeShelvedStartDate}
            </if>
            <if test="query.completeShelvedEndDate != null">
                and o.complete_shelved_date &lt;= #{query.completeShelvedEndDate}
            </if>
            <if test="query.isFbaQuick != null">
                and i.is_fba_quick = #{query.isFbaQuick}
            </if>
            <choose>
                <when test="query.unfinishedInThreeDay != null and query.unfinishedInThreeDay == true">
                    and o.create_date &lt;= DATE_SUB(CURDATE(), INTERVAL 3 DAY) and o.status not in ('COMPLETED',
                    'RECEIVED')
                </when>
                <otherwise>
                    <if test="query.statusList != null and query.statusList.size()>0">
                        AND o.status in
                        <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                                 close=")">
                            #{status}
                        </foreach>
                    </if>

                    <if test="query.createStartDate != null">
                        and o.create_date &gt;= #{query.createStartDate}
                    </if>
                    <if test="query.createEndDate != null">
                        and o.create_date &lt;= #{query.createEndDate}
                    </if>
                </otherwise>
            </choose>
        </where>
        GROUP BY o.stockin_order_id,i.sku,i.purchase_plan_no
        ) t
    </select>

    <select id="getPlatformScheduleInfoByStockinOrderId"
            resultType="com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity">
        select s.* from stockin_order o
        inner join stockin_order_task t on t.task_id = o.task_id
        inner join stock_platform_schedule s on s.platform_schedule_id = t.platform_schedule_id
        where o.stockin_order_id = #{stockinOrderId}
    </select>

    <select id="findIdBySupplierDeliveryNo" resultType="java.lang.Integer">
        select o.stockin_order_id from stockin_order o
        inner join  stockin_order_task t
        on o.task_id = t.task_id
        where t.supplier_delivery_no = #{supplierDeliveryNo}
    </select>

    <select id="statisticsStockinOrderItemList" resultType="com.nsy.api.wms.domain.stockin.StockinOrderStatisticsResponse">
        select
        COALESCE(sum(i.return_qty), 0)  as returnQtyTotal,
        COALESCE(sum(i.qty), 0)  as qtyTotal,
        COALESCE(sum(i.shelved_qty), 0)  as shelvedQtyTotal
        from stockin_order_item i
        inner join stockin_order o on o.stockin_order_id = i.stockin_order_id
        LEFT JOIN stockin_order_task t on o.task_id=t.task_id
        LEFT JOIN stockin_order_task_item taskItem on taskItem.task_item_id=i.task_item_id AND (i.purchase_plan_no is
        null or i.purchase_plan_no=taskItem.purchase_plan_no)
        LEFT JOIN bd_space s on taskItem.space_id= s.space_id
        <include refid="itemCondition"></include>
    </select>

    <select id="searchStockinOrderNoByStockinOrderIdListIgnoreTenant" resultType="java.lang.String">
        select stockin_order_no from stockin_order
        <where>
            <if test="stockinOrderIdList != null and stockinOrderIdList.size() > 0">
                stockin_order_id in
                <foreach collection="stockinOrderIdList" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
        </where>

    </select>

    <select id="pageItemSearchReceiptList" resultType="com.nsy.api.wms.response.stockin.StockinReceiptListInfo">
        SELECT so.stockin_order_id as stockinOrderId,
        so.real_stockin_order_id as realStockinOrderId,
        so.order_type as orderType,
        so.supplier_id as supplierId
        FROM stockin_order so
        inner join supplier supplier on supplier.supplier_id = so.supplier_id
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
        <if test="query!=null and query.spu != null and query.spu !=''">
            INNER JOIN product_info pi ON pi.product_id = ti.product_id
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
        </if>
        LEFT JOIN stockin_spot_info si ON si.logistics_no= so.logistics_no
        where
        1=1
        <include refid="receiptListWhere"></include>
        order by so.stockin_order_id desc
    </select>

    <select id="statisticsShipmentQty" resultType="java.lang.Integer">
        select ifnull(sum(t.shipmentQty), 0) from (
            SELECT
            case when MAX(so.stockin_type) = 'SPOT'
            then SUM(si.shipment_qty)
            else SUM(ti.expected_qty)
            end as shipmentQty
            FROM stockin_order so
            inner join supplier supplier on supplier.supplier_id = so.supplier_id
            INNER JOIN stockin_order_task t on so.task_id=t.task_id
            INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
            INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
            <if test="query!=null and query.spu != null and query.spu !=''">
                INNER JOIN product_info pi ON pi.product_id = ti.product_id
            </if>
            <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
                LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
            </if>
            LEFT JOIN stockin_spot_info si ON si.logistics_no= so.logistics_no
            where
            1=1
            <include refid="receiptListWhere"></include>) t
    </select>


    <select id="statisticsShelveInfo" resultType="java.util.Map">
        select ifnull(sum(t.returnQty), 0) returnQty,
            ifnull(sum(t.receiveQty), 0) receiveQty,
            ifnull(sum(t.shelvedQty), 0) shelvedQty
        from (
            SELECT
            sum(IFNULL(soi.return_qty,0)) as returnQty,
            sum(soi.qty) as receiveQty,
            sum(soi.shelved_qty) as shelvedQty
            FROM stockin_order so
            inner join supplier supplier on supplier.supplier_id = so.supplier_id
            INNER JOIN stockin_order_task t on so.task_id=t.task_id
            INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
            INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
            INNER JOIN stockin_order_item soi on ti.task_item_id= soi.task_item_id
            <if test="query!=null and query.spu != null and query.spu !=''">
                INNER JOIN product_info pi ON pi.product_id = ti.product_id
            </if>
            <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
                LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
            </if>
            where
            1=1
            <include refid="receiptListWhere"></include>) t
    </select>

    <select id="statisticsWaitReturnQtyTotal" resultType="java.lang.Integer">
        select
            ifnull(sum(srp.return_qty), 0)
        from (
            select
                so.stockin_order_id,
                so.stockin_order_no
            from stockin_order so
            inner join supplier supplier on supplier.supplier_id = so.supplier_id
            INNER JOIN stockin_order_task t on so.task_id=t.task_id
            INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
            INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
            <if test="query!=null and query.spu != null and query.spu !=''">
                INNER JOIN product_info pi ON pi.product_id = ti.product_id
            </if>
            <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
                LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
            </if>
            where
            1=1
            <include refid="receiptListWhere"></include>
        ) t
        inner join stockin_return_product srp on t.stockin_order_no = srp.stockin_order_no
    </select>

    <select id="statisticsRealWaitReturnQtyTotal" resultType="java.lang.Integer">
        select
            ifnull(sum(srp.return_qty), 0)
        from (
            select
            so.stockin_order_id,
            so.stockin_order_no,
            so.real_stockin_order_id
            from stockin_order so
            inner join supplier supplier on supplier.supplier_id = so.supplier_id
            INNER JOIN stockin_order_task t on so.task_id=t.task_id
            INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
            INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
            <if test="query!=null and query.spu != null and query.spu !=''">
                INNER JOIN product_info pi ON pi.product_id = ti.product_id
            </if>
            <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
                LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
            </if>
            where
            so.real_stockin_order_id is not null and so.real_stockin_order_id > 0
            and 1=1
            <include refid="receiptListWhere"></include>
        ) t
        inner join stockin_order real_so on t.real_stockin_order_id = real_so.stockin_order_id
        inner join stockin_return_product srp on real_so.stockin_order_no = srp.stockin_order_no
    </select>

    <select id="statisticsReturnedQtyTotal" resultType="java.lang.Integer">
        select
        ifnull(sum(srpti.actual_return_qty), 0)
        from (
        select
        so.stockin_order_id,
        so.stockin_order_no
        from stockin_order so
        inner join supplier supplier on supplier.supplier_id = so.supplier_id
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
        <if test="query!=null and query.spu != null and query.spu !=''">
            INNER JOIN product_info pi ON pi.product_id = ti.product_id
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
        </if>
        where
        1=1
        <include refid="receiptListWhere"></include>
        ) t
        inner join stockin_return_product_task_item srpti on t.stockin_order_no = srpti.stockin_order_no
    </select>

    <select id="statisticsRealReturnedQtyTotal" resultType="java.lang.Integer">
        select
        ifnull(sum(srpti.actual_return_qty), 0)
        from (
        select
        so.stockin_order_id,
        so.stockin_order_no,
        so.real_stockin_order_id
        from stockin_order so
        inner join supplier supplier on supplier.supplier_id = so.supplier_id
        INNER JOIN stockin_order_task t on so.task_id=t.task_id
        INNER JOIN stock_platform_schedule ps on t.platform_schedule_id= ps.platform_schedule_id
        INNER JOIN stockin_order_task_item ti on ti.task_id= t.task_id
        <if test="query!=null and query.spu != null and query.spu !=''">
            INNER JOIN product_info pi ON pi.product_id = ti.product_id
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            LEFT JOIN bd_area ba ON ba.space_id = ti.space_id and ba.area_name = ti.area_name
        </if>
        where
        so.real_stockin_order_id is not null and so.real_stockin_order_id > 0
        and 1=1
        <include refid="receiptListWhere"></include>
        ) t
        inner join stockin_order real_so on t.real_stockin_order_id = real_so.stockin_order_id
        inner join stockin_return_product_task_item srpti on real_so.stockin_order_no = srpti.stockin_order_no
    </select>

</mapper>
