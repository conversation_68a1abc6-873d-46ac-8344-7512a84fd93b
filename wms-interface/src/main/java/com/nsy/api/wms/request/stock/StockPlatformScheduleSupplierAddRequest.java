package com.nsy.api.wms.request.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "StockPlatformScheduleSupplierAddRequest", description = "供应商系统请求request")
public class StockPlatformScheduleSupplierAddRequest implements Serializable {
    private static final long serialVersionUID = 8452721001837236208L;

    @NotNull
    @NotEmpty
    @Size(max = 100, message = "出库单申请数量超过大小,最多{max}")
    List<StockPlatformScheduleSupplier> platformScheduleSupplierList;

    Integer orderType;

    String location;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public List<StockPlatformScheduleSupplier> getPlatformScheduleSupplierList() {
        return platformScheduleSupplierList;
    }

    public void setPlatformScheduleSupplierList(List<StockPlatformScheduleSupplier> platformScheduleSupplierList) {
        this.platformScheduleSupplierList = platformScheduleSupplierList;
    }

    public static class StockPlatformScheduleSupplier {
        @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
        private String supplierDeliveryNo;

        @ApiModelProperty(value = "入库类型 FACTORY 工厂到货入库,SPOT 现货入库,CUSTOM 加工入库,ALLOT 仓间调拨入库", name = "stockinType")
        @NotEmpty
        @NotNull
        private StockinTypeEnum stockinType;

        @ApiModelProperty(value = "采购单类型", name = "orderType")
        private Integer orderType;

        @ApiModelProperty(value = "供应商地区", name = "supplierLocation")
        private String supplierLocation;

        @ApiModelProperty(value = "供应商id", name = "supplierId")
        private Integer supplierId;

        @ApiModelProperty(value = "供应商名称", name = "supplierName", required = true)
        private String supplierName;

        @ApiModelProperty(value = "采购员userName,例000040 ", name = "purchaseUserName")
        private String purchaseUserName;

        @ApiModelProperty(value = "采购员真实姓名,例张三", name = "purchaseUserRealName")
        private String purchaseUserRealName;

        @ApiModelProperty(value = "计划箱数", name = "planBoxNum", required = true)
        private int planBoxNum;

        @ApiModelProperty(value = "工厂发货时间", name = "deliveryDate", required = true)
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date deliveryDate;

        @ApiModelProperty(value = "车牌", name = "truckLicense")
        private String truckLicense;

        @ApiModelProperty(value = "车型", name = "truckType")
        private String truckType;

        @ApiModelProperty(value = "司机信息(姓名/联系方式)", name = "driver")
        private String driver;

        @ApiModelProperty(value = "收货地", name = "receiptPlace")
        private String receiptPlace;

        @ApiModelProperty(value = "直发备注", name = "remarks")
        private String remarks;

        @ApiModelProperty(value = "物流公司", name = "logisticsCompanyId")
        private Integer logisticsCompanyId;

        @ApiModelProperty(value = "物流公司名称", name = "logisticsCompanyName")
        private String logisticsCompanyName;

        @ApiModelProperty(value = "是否自动创建补货单", name = "isAutoCreateFbaReplenishment")
        private Integer isAutoCreateFbaReplenishment;

        @ApiModelProperty(value = "齐色齐码", name = "isHomogeneous")
        private Integer isHomogeneous;

        @ApiModelProperty(value = "发货类型：0-普通发货;1-排单发货;2-返工发货;3-直发发货;4-生产发货;5-快捷发货;", name = "deliveryType")
        private Integer deliveryType;

        @NotNull
        @NotEmpty
        @ApiModelProperty(value = "明细", name = "item")
        private List<Item> item;

        public Integer getDeliveryType() {
            return deliveryType;
        }

        public void setDeliveryType(Integer deliveryType) {
            this.deliveryType = deliveryType;
        }

        public StockinTypeEnum getStockinType() {
            return stockinType;
        }

        public void setStockinType(StockinTypeEnum stockinType) {
            this.stockinType = stockinType;
        }

        public String getSupplierDeliveryNo() {
            return supplierDeliveryNo;
        }

        public void setSupplierDeliveryNo(String supplierDeliveryNo) {
            this.supplierDeliveryNo = supplierDeliveryNo;
        }

        public Integer getOrderType() {
            return orderType;
        }

        public void setOrderType(Integer orderType) {
            this.orderType = orderType;
        }

        public String getSupplierLocation() {
            return supplierLocation;
        }

        public void setSupplierLocation(String supplierLocation) {
            this.supplierLocation = supplierLocation;
        }

        public Integer getSupplierId() {
            return supplierId;
        }

        public void setSupplierId(Integer supplierId) {
            this.supplierId = supplierId;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getPurchaseUserName() {
            return purchaseUserName;
        }

        public void setPurchaseUserName(String purchaseUserName) {
            this.purchaseUserName = purchaseUserName;
        }

        public String getPurchaseUserRealName() {
            return purchaseUserRealName;
        }

        public void setPurchaseUserRealName(String purchaseUserRealName) {
            this.purchaseUserRealName = purchaseUserRealName;
        }

        public int getPlanBoxNum() {
            return planBoxNum;
        }

        public void setPlanBoxNum(int planBoxNum) {
            this.planBoxNum = planBoxNum;
        }

        public Date getDeliveryDate() {
            return deliveryDate;
        }

        public void setDeliveryDate(Date deliveryDate) {
            this.deliveryDate = deliveryDate;
        }

        public String getTruckLicense() {
            return truckLicense;
        }

        public void setTruckLicense(String truckLicense) {
            this.truckLicense = truckLicense;
        }

        public String getTruckType() {
            return truckType;
        }

        public void setTruckType(String truckType) {
            this.truckType = truckType;
        }

        public String getDriver() {
            return driver;
        }

        public void setDriver(String driver) {
            this.driver = driver;
        }

        public List<Item> getItem() {
            return item;
        }

        public void setItem(List<Item> item) {
            this.item = item;
        }

        public String getReceiptPlace() {
            return receiptPlace;
        }

        public void setReceiptPlace(String receiptPlace) {
            this.receiptPlace = receiptPlace;
        }

        public String getRemarks() {
            return remarks;
        }

        public void setRemarks(String remarks) {
            this.remarks = remarks;
        }

        public Integer getLogisticsCompanyId() {
            return logisticsCompanyId;
        }

        public void setLogisticsCompanyId(Integer logisticsCompanyId) {
            this.logisticsCompanyId = logisticsCompanyId;
        }

        public String getLogisticsCompanyName() {
            return logisticsCompanyName;
        }

        public void setLogisticsCompanyName(String logisticsCompanyName) {
            this.logisticsCompanyName = logisticsCompanyName;
        }

        public Integer getIsAutoCreateFbaReplenishment() {
            return isAutoCreateFbaReplenishment;
        }

        public void setIsAutoCreateFbaReplenishment(Integer isAutoCreateFbaReplenishment) {
            this.isAutoCreateFbaReplenishment = isAutoCreateFbaReplenishment;
        }

        public Integer getIsHomogeneous() {
            return isHomogeneous;
        }

        public void setIsHomogeneous(Integer isHomogeneous) {
            this.isHomogeneous = isHomogeneous;
        }
    }

    public static class Item {
        @ApiModelProperty(value = "箱号", name = "supplierDeliveryBoxCode", required = true)
        private String supplierDeliveryBoxCode;

        @ApiModelProperty(value = "仓库id", name = "spaceId", required = true)
        private Integer spaceId;

        @ApiModelProperty(value = "仓库名称", name = "spaceName")
        private String spaceName;

        @ApiModelProperty(value = "第几箱", name = "boxIndex", required = true)
        private Integer boxIndex;

        @ApiModelProperty(value = "采购计划单号", name = "purchasePlanNo", required = true)
        private String purchasePlanNo;

        @ApiModelProperty(value = "订单号", name = "orderNo")
        private String orderNo;

        @ApiModelProperty(value = "店铺id", name = "storeId")
        private Integer storeId;

        @ApiModelProperty(value = "部门", name = "businessType")
        private String businessType;

        @ApiModelProperty(value = "采购申请单的信息", name = "purchaseApplyInfo")
        private String purchaseApplyInfo;

        @ApiModelProperty(value = "标签属性：S、A、新品首单、改版首单、正常、AMZN新", name = "labelAttributeNames")
        private String labelAttributeNames;

        /**
         * 采购申请类型，1:缺货订单申请，2:定制申请，3:FBA发货申请，4:采购申请,5:正常采购,6:退货返工,7:开发申请,8:分公司申请,9:现货补单,10:市场补单
         */
        private Integer purchasingApplyType;

        @ApiModelProperty(value = "批号", name = "batchCode")
        private String batchCode;

        @ApiModelProperty(value = "sku", name = "sku", required = true)
        private String sku;

        @ApiModelProperty(value = "业务sku", name = "sellerSku", required = true)
        private String sellerSku;

        @ApiModelProperty(value = "业务条形码", name = "sellerBarcode", required = true)
        private String sellerBarcode;

        @ApiModelProperty(value = "业务描述", name = "sellerTitle")
        private String sellerTitle;

        @ApiModelProperty(value = "数量", name = "qty", required = true)
        private Integer qty;

        @ApiModelProperty(value = "计划单备注", name = "planRemark")
        private String planRemark;

        @ApiModelProperty(value = "是否快进快出", name = "isFbaQuick")
        private Integer isFbaQuick;

        @ApiModelProperty(value = "工艺版本号", name = "workmanshipVersion")
        private String workmanshipVersion;

        @ApiModelProperty(value = "品牌名称", name = "brandName")
        private String brandName;

        @ApiModelProperty(value = "包装方式", name = "packageName")
        private String packageName;

        @ApiModelProperty(value = "公司采购首单", name = "purchaseSkcFirstOrder")
        private String purchaseSkcFirstOrder;

        @ApiModelProperty(value = "区域", name = "areaName")
        private String areaName;
        /**
         * 明细上添加供应商id、名称字段用于区分工厂出库单多个工厂一起发货
         */
        @ApiModelProperty(value = "供应商id", name = "supplierId")
        private Integer supplierId;

        @ApiModelProperty(value = "供应商名称", name = "supplierName", required = true)
        private String supplierName;

        @ApiModelProperty(value = "店铺平台ID", name = "storePlatformIds")
        private String storePlatformIds;

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public String getBrandName() {
            return brandName;
        }

        public void setBrandName(String brandName) {
            this.brandName = brandName;
        }

        public String getPurchaseSkcFirstOrder() {
            return purchaseSkcFirstOrder;
        }

        public void setPurchaseSkcFirstOrder(String purchaseSkcFirstOrder) {
            this.purchaseSkcFirstOrder = purchaseSkcFirstOrder;
        }

        public String getWorkmanshipVersion() {
            return workmanshipVersion;
        }

        public void setWorkmanshipVersion(String workmanshipVersion) {
            this.workmanshipVersion = workmanshipVersion;
        }

        public Integer getIsFbaQuick() {
            return isFbaQuick;
        }

        public void setIsFbaQuick(Integer isFbaQuick) {
            this.isFbaQuick = isFbaQuick;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public String getPlanRemark() {
            return planRemark;
        }

        public void setPlanRemark(String planRemark) {
            this.planRemark = planRemark;
        }

        public String getSupplierDeliveryBoxCode() {
            return supplierDeliveryBoxCode;
        }

        public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
            this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
        }

        public String getLabelAttributeNames() {
            return labelAttributeNames;
        }

        public void setLabelAttributeNames(String labelAttributeNames) {
            this.labelAttributeNames = labelAttributeNames;
        }

        public String getSellerSku() {
            return sellerSku;
        }

        public void setSellerSku(String sellerSku) {
            this.sellerSku = sellerSku;
        }

        public String getSellerBarcode() {
            return sellerBarcode;
        }

        public void setSellerBarcode(String sellerBarcode) {
            this.sellerBarcode = sellerBarcode;
        }

        public Integer getPurchasingApplyType() {
            return purchasingApplyType;
        }

        public void setPurchasingApplyType(Integer purchasingApplyType) {
            this.purchasingApplyType = purchasingApplyType;
        }

        public Integer getSpaceId() {
            return spaceId;
        }

        public void setSpaceId(Integer spaceId) {
            this.spaceId = spaceId;
        }

        public Integer getBoxIndex() {
            return boxIndex;
        }

        public void setBoxIndex(Integer boxIndex) {
            this.boxIndex = boxIndex;
        }

        public String getPurchasePlanNo() {
            return purchasePlanNo;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getPurchaseApplyInfo() {
            return purchaseApplyInfo;
        }

        public void setPurchaseApplyInfo(String purchaseApplyInfo) {
            this.purchaseApplyInfo = purchaseApplyInfo;
        }

        public void setPurchasePlanNo(String purchasePlanNo) {
            this.purchasePlanNo = purchasePlanNo;
        }

        public String getBatchCode() {
            return batchCode;
        }

        public void setBatchCode(String batchCode) {
            this.batchCode = batchCode;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public Integer getQty() {
            return qty;
        }

        public void setQty(Integer qty) {
            this.qty = qty;
        }

        public String getSpaceName() {
            return spaceName;
        }

        public void setSpaceName(String spaceName) {
            this.spaceName = spaceName;
        }

        public Integer getStoreId() {
            return storeId;
        }

        public void setStoreId(Integer storeId) {
            this.storeId = storeId;
        }


        public String getAreaName() {
            return areaName;
        }

        public void setAreaName(String areaName) {
            this.areaName = areaName;
        }

        public Integer getSupplierId() {
            return supplierId;
        }

        public void setSupplierId(Integer supplierId) {
            this.supplierId = supplierId;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getSellerTitle() {
            return sellerTitle;
        }

        public void setSellerTitle(String sellerTitle) {
            this.sellerTitle = sellerTitle;
        }

        public String getStorePlatformIds() {
            return storePlatformIds;
        }

        public void setStorePlatformIds(String storePlatformIds) {
            this.storePlatformIds = storePlatformIds;
        }
    }
}
