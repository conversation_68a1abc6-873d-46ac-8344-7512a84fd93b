package com.nsy.api.wms.enumeration.stockout;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;

public enum StockoutCustomsDeclareFormStatusEnum {
    // dic key : wms_declare_form_status
    WAIT_DEAL("待处理"),
    WAIT_AUDIT("待审核"),
    DEALT("已审核"),
    GENERATE_ORDER("已生成合同"),
    SUPPLIER_DONE("工厂已反馈"),
    COMPLETE("已完成");

    String name;

    StockoutCustomsDeclareFormStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getByName(String name) {
        if (Strings.isBlank(name)) return null;
        StockoutCustomsDeclareFormStatusEnum stockoutCustomsDeclareFormLogTypeEnum = Arrays.stream(values()).filter(instance -> name.equals(instance.name())).findFirst()
                .orElse(null);
        if (stockoutCustomsDeclareFormLogTypeEnum != null) {
            return stockoutCustomsDeclareFormLogTypeEnum.getName();
        } else {
            return "";
        }
    }
}
