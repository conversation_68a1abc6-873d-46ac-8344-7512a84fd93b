package com.nsy.wms.business.service.bd;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.domain.bd.BdPosition;
import com.nsy.api.wms.domain.bd.BdPositionPrint;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.domain.shared.SelectIntegerSortModel;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdChangLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderBusinessTypeEnum;
import com.nsy.api.wms.request.bd.BdPositionAddRequest;
import com.nsy.api.wms.request.bd.BdPositionBatchAddRequest;
import com.nsy.api.wms.request.bd.BdPositionEditAreaRequest;
import com.nsy.api.wms.request.bd.BdPositionListRequest;
import com.nsy.api.wms.request.bd.BdPositionSelectRequest;
import com.nsy.api.wms.request.bd.PositionCodePrintRequest;
import com.nsy.api.wms.request.stock.PDATransferInSelectRequest;
import com.nsy.api.wms.request.stock.StockListRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stock.PDATransferInSelectResponse;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.erp.ErpBaseApiService;
import com.nsy.wms.business.manage.erp.request.ErpAddPositionRequest;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.manage.user.upload.StockPositionImport;
import com.nsy.wms.business.service.bd.query.BdPositionQueryWrapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.lingxing.LingXingPushService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceAreaEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdAreaMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 * 库位
 */
@Service
public class BdPositionService extends ServiceImpl<BdPositionMapper, BdPositionEntity> implements IProcessUploadDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BdPositionService.class);
    @Autowired
    private BdPositionMapper bdPositionMapper;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private BdChangeLogService changeLogService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private BdSpaceAreaService spaceAreaService;
    @Autowired
    private BdAreaMapper bdAreaMapper;
    @Autowired
    private BdSpaceService spaceService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    private StockService stockService;
    @Autowired
    private LingXingPushService lingXingPushService;
    @Autowired
    private ErpBaseApiService erpBaseApiService;
    @Autowired
    private BdErpSpaceMappingService bdErpSpaceMappingService;
    @Autowired
    private OmsApiService omsApiService;
    @Resource
    private BdAreaService bdAreaService;

    /**
     * 新增库位
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.POSITION_NAME_SELECT, allEntries = true)
    public void saveBdPosition(BdPositionAddRequest request) {
        //如果是活动库位或OEM库位，需要保存归属组织
        validBusinessTypeAndStore(request);
        BdPositionEntity bdPositionEntity = buildSaveBdPosition(request);
        bdPositionMapper.insert(bdPositionEntity);
        String content = String.format("%s 新增库位:%s", loginInfoService.getName(), bdPositionEntity.getPositionName());
        changeLogService.addChangeLog(BdChangLogTypeEnum.POSITION, content, BdPositionEntity.class.getAnnotation(TableName.class).value());
        lingXingPushService.syncPositionCode(bdPositionEntity.getPositionCode());

        //活动库位和越库库位、店铺库位需要同步商通
        if (BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.STORE_POSITION.name().equals(bdPositionEntity.getPositionType())) {
            syncPositionToErp(bdPositionEntity);
        }
    }

    //同步库位信息给商通
    public void syncPositionToErp(BdPositionEntity bdPositionEntity) {
        ErpAddPositionRequest erpAddPositionRequest = new ErpAddPositionRequest();
        BdErpSpaceMappingEntity mappingEntity = bdErpSpaceMappingService.getEntityByAreaId(bdPositionEntity.getAreaId());
        BdSpaceAreaEntity bdSpaceAreaEntity = spaceAreaService.getById(bdPositionEntity.getSpaceAreaId());
        erpAddPositionRequest.setDeliveryLocation(bdPositionEntity.getLocation());
        erpAddPositionRequest.setEnableFlag(bdPositionEntity.getIsDeleted());
        erpAddPositionRequest.setGroupNum(bdSpaceAreaEntity.getSpaceAreaName());
        erpAddPositionRequest.setPositionCode(bdPositionEntity.getPositionCode());
        erpAddPositionRequest.setPositionName(bdPositionEntity.getPositionName());
        // 库位类型（0：发货库位，1：库存库位，2：虚拟库位  8:越库库位 9：店铺库位）
        erpAddPositionRequest.setPositionType(BdPositionTypeEnum.STORE_POSITION.name().equals(bdPositionEntity.getPositionType())
                ? 9 : BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType()) ? 8 : 0);
        erpAddPositionRequest.setSortOrder(0);
        erpAddPositionRequest.setSpaceId(mappingEntity.getErpSpaceId());
        erpAddPositionRequest.setSpaceName(mappingEntity.getErpSpaceName());
        erpBaseApiService.addFreightPosition(erpAddPositionRequest);
    }

    private void validBusinessTypeAndStore(BdPositionAddRequest request) {
        if (StringUtils.hasText(request.getBusinessType())) {
            //判断部门是否存在
            Map<String, String> businessUnitEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_BUSINESS_UNIT.getName());
            String businessType = businessUnitEnumMap.get(request.getBusinessType());
            if (!StringUtils.hasText(businessType)) {
                throw new BusinessServiceException(String.format("部门【%s】错误", request.getBusinessType()));
            }

        }

        if (BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(request.getPositionType())
                || BdPositionTypeEnum.CROSS_POSITION.name().equals(request.getPositionType())
                && SpaceAreaMapConstant.WmsArea.ACTIVITY_AREA.equals(request.getAreaName())) {
            if (!StringUtils.hasText(request.getBusinessType()))
                throw new BusinessServiceException(String.format("库位编码【%s】为活动库位，请填写部门", request.getPositionCode()));

            if ((StockoutOrderBusinessTypeEnum.B2C.getName().equals(request.getBusinessType())
                    || StockoutOrderBusinessTypeEnum.DOKOTOO.getName().equals(request.getBusinessType()))
                    && Objects.isNull(request.getStoreId())) {
                throw new BusinessServiceException(String.format("库位编码【%s】部门为B2C或dokotoo，请选择对应店铺", request.getPositionCode()));
            }
        }
    }

    private void validBusinessTypeAndStore(BdPositionEntity bdPositionEntity, BdAreaEntity bdAreaEntity) {

        if (BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType())
                && SpaceAreaMapConstant.WmsArea.ACTIVITY_AREA.equals(bdAreaEntity.getAreaName())
                && !StringUtils.hasText(bdPositionEntity.getBusinessType())) {
            throw new BusinessServiceException(String.format("越库库位【%s】没有设置归属部门，不允许迁移到活动区域，请先编辑设置", bdPositionEntity.getPositionCode()));
        }
    }

    private BdPositionEntity buildSaveBdPosition(BdPositionAddRequest request) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByPositionCode(request.getPositionCode());
        BdPositionEntity entity = bdPositionMapper.selectOne(wrapper);
        if (Objects.nonNull(entity)) {
            throw new BusinessServiceException(String.format("库位编码【%s】已使用, 请更换库位编码", request.getPositionCode()));
        }
        BdPositionEntity bdPositionEntity = new BdPositionEntity();
        BeanUtilsEx.copyProperties(request, bdPositionEntity, "quarter");
        buildQuarter(request, bdPositionEntity);
        bdPositionEntity.setCreateBy(loginInfoService.getName());
        bdPositionEntity.setUpdateBy(loginInfoService.getName());
        bdPositionEntity.setLocation(TenantContext.getTenant());

        return bdPositionEntity;
    }

    private void buildQuarter(BdPositionAddRequest request, BdPositionEntity bdPositionEntity) {
        Integer year = ObjectUtil.defaultIfNull(request.getYear(), 0);
        Integer quarter = ObjectUtil.defaultIfNull(request.getQuarter(), 0);
        if (0 != year && 0 == quarter || 0 == year && 0 != quarter) {
            throw new BusinessServiceException(String.format("库位编码【%s】年份和季度必须都填，或都不填", request.getPositionCode()));
        }
        if (0 == year) {
            bdPositionEntity.setQuarter("");
        } else {
            bdPositionEntity.setQuarter(year + "0" + quarter);
        }
    }

    /**
     * 批量修改库位
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.POSITION_NAME_SELECT, allEntries = true)
    public void batchSaveBdPosition(BdPositionBatchAddRequest requestList) {
        requestList.getData().forEach(this::saveBdPosition);
    }

    /**
     * 批量修改库位的区域和库区
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.POSITION_NAME_SELECT, allEntries = true)
    public void batchEditBdPosition(BdPositionEditAreaRequest request) {
        // 获取库位列表
        List<BdPositionEntity> positionEntityList = this.listByIds(request.getPositionId());
        if (CollectionUtils.isEmpty(positionEntityList)) {
            throw new BusinessServiceException("未找到库位，请确认！");
        }
        // 检查两组参数：(areaId, spaceAreaId)组和(year, quarter)组
        boolean hasAreaGroup = checkAreaGroupParams(request);
        boolean hasQuarterGroup = checkQuarterGroupParams(request);
        // 检查至少有一组参数必填
        if (!hasAreaGroup && !hasQuarterGroup) {
            throw new BusinessServiceException("区域和库区组(areaId, spaceAreaId)或年份季度组(year, quarter)至少有一组必填！");
        }
        // 处理区域和库区参数
        final BdAreaEntity bdAreaEntity;
        final BdSpaceAreaEntity bdSpaceAreaEntity;
        if (hasAreaGroup) {
            bdAreaEntity = getAndValidateArea(request.getAreaId());
            bdSpaceAreaEntity = getAndValidateSpaceArea(request.getSpaceAreaId());
        } else {
            bdAreaEntity = null;
            bdSpaceAreaEntity = null;
        }
        // 处理年份和季度
        final String quarter = hasQuarterGroup ? request.getYear() + "0" + request.getQuarter() : "";
        // 构建要更新的实体列表
        List<BdPositionEntity> updatedPositions = buildPositionsForUpdate(
                positionEntityList,
                bdAreaEntity,
                bdSpaceAreaEntity,
                hasAreaGroup,
                hasQuarterGroup,
                quarter
        );
        this.updateBatchById(updatedPositions);
        // 更新库存信息，仅在更新了区域信息时需要
        if (hasAreaGroup) {
            updateStock(request.getPositionId(), updatedPositions);
        }
        // 记录日志
        logBatchUpdate(positionEntityList, bdAreaEntity, bdSpaceAreaEntity, hasAreaGroup,
                hasQuarterGroup, request);
    }

    /**
     * 检查区域组参数
     */
    private boolean checkAreaGroupParams(BdPositionEditAreaRequest request) {
        if (ObjectUtil.isNull(request.getAreaId()) && ObjectUtil.isNull(request.getSpaceAreaId())) {
            return false;
        }

        if (ObjectUtil.isNull(request.getAreaId()) || ObjectUtil.isNull(request.getSpaceAreaId())) {
            throw new BusinessServiceException("区域ID和库区ID必须同时提供或同时不提供！");
        }

        return true;
    }

    /**
     * 检查季度组参数
     */
    private boolean checkQuarterGroupParams(BdPositionEditAreaRequest request) {
        if (StrUtil.isBlank(request.getYear()) && StrUtil.isBlank(request.getQuarter())) {
            return false;
        }

        if (StrUtil.isBlank(request.getYear()) || StrUtil.isBlank(request.getQuarter())) {
            throw new BusinessServiceException("年份和季度必须同时提供且不能为空值！");
        }

        return true;
    }

    /**
     * 获取并验证区域
     */
    private BdAreaEntity getAndValidateArea(Integer areaId) {
        BdAreaEntity areaEntity = bdAreaMapper.selectById(areaId);
        if (ObjectUtil.isNull(areaEntity)) {
            throw new BusinessServiceException("未找到区域，请确认！");
        }
        return areaEntity;
    }

    /**
     * 获取并验证库区
     */
    private BdSpaceAreaEntity getAndValidateSpaceArea(Integer spaceAreaId) {
        BdSpaceAreaEntity spaceAreaEntity = spaceAreaService.getById(spaceAreaId);
        if (ObjectUtil.isNull(spaceAreaEntity)) {
            throw new BusinessServiceException("未找到库区，请确认！");
        }
        return spaceAreaEntity;
    }

    /**
     * 构建要更新的实体列表
     */
    private List<BdPositionEntity> buildPositionsForUpdate(
            List<BdPositionEntity> positionEntityList,
            BdAreaEntity bdAreaEntity,
            BdSpaceAreaEntity bdSpaceAreaEntity,
            boolean hasAreaGroup,
            boolean hasQuarterGroup,
            String quarter) {

        return positionEntityList.stream().map(entity -> {
            if (hasAreaGroup) {
                validBusinessTypeAndStore(entity, bdAreaEntity);
            }

            BdPositionEntity bdPositionEntity = new BdPositionEntity();
            bdPositionEntity.setPositionId(entity.getPositionId());

            // 只在有区域组信息时设置
            if (hasAreaGroup) {
                bdPositionEntity.setAreaId(bdAreaEntity.getAreaId());
                bdPositionEntity.setAreaName(bdAreaEntity.getAreaName());
                bdPositionEntity.setSpaceAreaId(bdSpaceAreaEntity.getSpaceAreaId());
                bdPositionEntity.setSpaceAreaName(bdSpaceAreaEntity.getSpaceAreaName());
            }

            bdPositionEntity.setUpdateBy(loginInfoService.getName());

            // 只在有年份季度组信息时设置
            if (hasQuarterGroup) {
                bdPositionEntity.setQuarter(quarter);
            }

            return bdPositionEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 记录批量更新操作日志
     */
    private void logBatchUpdate(
            List<BdPositionEntity> positionEntityList,
            BdAreaEntity bdAreaEntity,
            BdSpaceAreaEntity bdSpaceAreaEntity,
            boolean hasAreaGroup,
            boolean hasQuarterGroup,
            BdPositionEditAreaRequest request) {
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append(loginInfoService.getName())
                .append(" 批量修改库位:")
                .append(positionEntityList.stream()
                        .map(BdPositionEntity::getPositionCode)
                        .collect(Collectors.joining(",")));
        if (hasAreaGroup) {
            contentBuilder.append("，区域：")
                    .append(bdAreaEntity.getAreaName())
                    .append("，库区：")
                    .append(bdSpaceAreaEntity.getSpaceAreaName());
        }
        if (hasQuarterGroup) {
            contentBuilder.append("，年份：").append(request.getYear())
                    .append("，季度：").append(request.getQuarter());
        }
        String content = contentBuilder.toString();
        content = content.substring(0, Math.min(content.length(), 450));
        String detail = JsonMapper.toJson(request);
        changeLogService.addChangeLog(BdChangLogTypeEnum.POSITION, content,
                BdPositionEntity.class.getAnnotation(TableName.class).value(), detail);
    }

    /**
     * 更新库存信息
     *
     * @param positionIdList
     * @param newPositionList
     */
    private void updateStock(List<Integer> positionIdList, List<BdPositionEntity> newPositionList) {
        Map<Integer, BdPositionEntity> positionMap = newPositionList.stream().collect(Collectors.toMap(BdPositionEntity::getPositionId, Function.identity()));

        List<StockEntity> stockList = stockService.list(new LambdaQueryWrapper<StockEntity>().in(StockEntity::getPositionId, positionIdList));
        stockList.forEach(stock -> {
            BdPositionEntity position = positionMap.get(stock.getPositionId());
            stock.setAreaId(position.getAreaId());
            stock.setAreaName(position.getAreaName());
            stock.setSpaceAreaId(position.getSpaceAreaId());
            stock.setSpaceAreaName(position.getSpaceAreaName());
        });

        stockService.updateBatchById(stockList);
    }

    /**
     * 修改库位
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.POSITION_NAME_SELECT, allEntries = true)
    public void updateBdPosition(Integer positionId, BdPositionAddRequest request) {
        BdPositionEntity bdPositionEntity = bdPositionMapper.selectById(positionId);
        if (bdPositionEntity == null) {
            throw new BusinessServiceException("没有找到对应库位记录");
        }
        validBusinessTypeAndStore(request);
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByPositionCode(request.getPositionCode());
        BdPositionEntity tempEntity = bdPositionMapper.selectOne(wrapper);
        if (Objects.nonNull(tempEntity) && !tempEntity.getPositionId().equals(bdPositionEntity.getPositionId())) {
            throw new BusinessServiceException(String.format("库位编码【%s】已使用, 请更换库位编码", request.getPositionCode()));
        }
        //若修改库位类型，需校验是否存在库存
        if (StringUtils.hasText(request.getPositionType())
                && !request.getPositionType().equals(bdPositionEntity.getPositionType())) {
            StockListRequest stockListRequest = new StockListRequest();
            stockListRequest.setPositionCode(bdPositionEntity.getPositionCode());
            Integer stock = stockService.getBaseMapper().stockStatistics(stockListRequest);
            if (stock != null && stock > 0)
                throw new BusinessServiceException("该库位上存在库存，无法修改库位类型！");
        }


        // 如果部门不是B2B或dokotoo 店铺ID设置为null
        if (!StockoutOrderBusinessTypeEnum.B2C.getName().equals(request.getBusinessType())
                && !StockoutOrderBusinessTypeEnum.DOKOTOO.getName().equals(request.getBusinessType())) {
            this.getBaseMapper().updateSetStoreIdNull(bdPositionEntity.getPositionId());
            //重新获取entity
            bdPositionEntity = bdPositionMapper.selectById(positionId);
        }

        BeanUtilsEx.copyProperties(request, bdPositionEntity, "quarter");
        bdPositionEntity.setBrandName(StringUtils.hasText(request.getBrandName()) ? request.getBrandName() : "");
        buildQuarter(request, bdPositionEntity);
        bdPositionEntity.setUpdateBy(loginInfoService.getName());

        String detail = JsonMapper.toJson(bdPositionEntity);
        bdPositionMapper.updateById(bdPositionEntity);
        String content = String.format("%s 修改库位:%s", loginInfoService.getName(), bdPositionEntity.getPositionName());
        changeLogService.addChangeLog(BdChangLogTypeEnum.POSITION, content, BdPositionEntity.class.getAnnotation(TableName.class).value(), detail);

        //活动库位和越库库位需要同步商通
        if (BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.CROSS_POSITION.name().equals(bdPositionEntity.getPositionType())
                || BdPositionTypeEnum.STORE_POSITION.name().equals(bdPositionEntity.getPositionType())) {
            syncPositionToErp(bdPositionEntity);
        }
    }

    /**
     * 启用/停用库位
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.POSITION_NAME_SELECT, allEntries = true)
    public void updateBdPositionStatus(Integer positionId, Integer isDeleted) {
        BdPositionEntity bdPositionEntity = bdPositionMapper.selectById(positionId);
        if (bdPositionEntity == null) {
            throw new BusinessServiceException("没有找到库位记录");
        }
        String status = isDeleted == 1 ? "停用" : "启用";
        if (isDeleted.equals(bdPositionEntity.getIsDeleted()))
            throw new BusinessServiceException(String.format("%s已是%s状态", bdPositionEntity.getPositionName(), status));

        //库位上还有库存禁止停用库位
        if (isDeleted == 1) {
            List<StockEntity> stockEntities = stockService.findByPositionCode(bdPositionEntity.getPositionCode());
            if (!CollectionUtils.isEmpty(stockEntities)
                    && stockEntities.stream().mapToInt(StockEntity::getStock).sum() > 0)
                throw new BusinessServiceException("库位上仍有库存，禁止停用！");

        }

        bdPositionEntity.setIsDeleted(isDeleted);
        bdPositionEntity.setUpdateBy(loginInfoService.getName());
        bdPositionMapper.updateById(bdPositionEntity);
        String content = String.format("%s %s%s", loginInfoService.getName(), status, bdPositionEntity.getPositionName());
        changeLogService.addChangeLog(BdChangLogTypeEnum.POSITION, content, BdPositionEntity.class.getAnnotation(TableName.class).value());
    }

    /*
     * 查询库位
     * */
    public BdPosition bdPositionById(Integer id) {
        BdPositionEntity bdPositionEntity = bdPositionMapper.selectById(id);
        if (Objects.isNull(bdPositionEntity)) {
            throw new BusinessServiceException("库位不存在");
        }
        BdPosition bdPosition = new BdPosition();
        BeanUtilsEx.copyProperties(bdPositionEntity, bdPosition, "quarter");
        if (StrUtil.isNotEmpty(bdPositionEntity.getQuarter())) {
            int year = Integer.parseInt(bdPositionEntity.getQuarter().substring(0, 4));
            int quarter = Integer.parseInt(bdPositionEntity.getQuarter().substring(4, 6));
            bdPosition.setYear(year);
            bdPosition.setQuarter(quarter);
        }
        return bdPosition;
    }

    /*
     * 通过编码查询库位
     * */
    public BdPosition bdPositionByPositionCode(String positionCode) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByPositionCode(positionCode);
        BdPositionEntity bdPositionEntity = bdPositionMapper.selectOne(wrapper);
        if (Objects.isNull(bdPositionEntity)) {
            throw new BusinessServiceException("库位不存在");
        }
        BdPosition bdPosition = new BdPosition();
        BeanUtilsEx.copyProperties(bdPositionEntity, bdPosition, "quarter");
        if (StrUtil.isNotEmpty(bdPositionEntity.getQuarter())) {
            int year = Integer.parseInt(bdPositionEntity.getQuarter().substring(0, 4));
            int quarter = Integer.parseInt(bdPositionEntity.getQuarter().substring(4, 6));
            bdPosition.setYear(year);
            bdPosition.setQuarter(quarter);
        }
        return bdPosition;
    }

    /*
     * 通过编码List查询库位list
     * */
    public List<BdPositionEntity> bdPositionByPositionCodeList(List<String> positionCodeList) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildWrapperBdPositionByPositionCodeList(positionCodeList);
        return this.list(wrapper);
    }

    /*
     * 查询库位列表
     * */
    public PageResponse<BdPosition> bdPositionByPage(BdPositionListRequest request) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionByBdPositionListRequest(request);
        Page<BdPositionEntity> page = bdPositionMapper.selectPage(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper);
        PageResponse<BdPosition> pageResponse = PageResponse.of(page.getTotal());
        List<BdPositionEntity> bdPositionEntityList = page.getRecords();
        Map<String, String> positionTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_POSITION_TYPE.getName());
        List<BdPosition> bdPositionList = bdPositionEntityList.stream().map(entity -> {
            BdPosition bdPosition = new BdPosition();
            BeanUtilsEx.copyProperties(entity, bdPosition, "quarter");
            if (StrUtil.isNotEmpty(entity.getQuarter())) {
                int year = Integer.parseInt(entity.getQuarter().substring(0, 4));
                int quarter = Integer.parseInt(entity.getQuarter().substring(4, 6));
                bdPosition.setYear(year);
                bdPosition.setQuarter(quarter);
            }
            bdPosition.setPositionTypeStr(positionTypeEnumMap.get(entity.getPositionType()));
            return bdPosition;
        }).collect(Collectors.toList());
        pageResponse.setContent(bdPositionList);
        return pageResponse;
    }

    public PageResponse<PDATransferInSelectResponse> transferInSelect(PDATransferInSelectRequest request) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = new LambdaQueryWrapper<BdPositionEntity>()
                .likeRight(BdPositionEntity::getPositionCode, request.getTransferInCode())
                .eq(BdPositionEntity::getIsDeleted, Boolean.FALSE);
        Page<BdPositionEntity> page = bdPositionMapper.selectPage(new Page<>(request.getPageIndex(), request.getPageSize()), wrapper);
        PageResponse<PDATransferInSelectResponse> pageResponse = PageResponse.of(page.getTotal());
        Map<String, String> positionTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_POSITION_TYPE.getName());
        List<PDATransferInSelectResponse> resultList = page.getRecords().stream().map(record -> {
            PDATransferInSelectResponse selectResponse = new PDATransferInSelectResponse();
            selectResponse.setTransferInCode(record.getPositionCode());
            selectResponse.setSpaceName(record.getSpaceName());
            selectResponse.setSpaceAreaName(record.getSpaceAreaName());
            selectResponse.setTransferInType(positionTypeEnumMap.get(record.getPositionType()));
            return selectResponse;
        }).collect(Collectors.toList());
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    /*
     * 根据库区查库位
     * */
    @Cacheable(value = CacheKeyConstant.POSITION_NAME_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant + '_' + #spaceAreaId")
    public List<SelectIntegerModel> getBdPositionNameSelect(Integer spaceAreaId) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionBySpaceAreaIdAndIsDeleted(spaceAreaId, IsDeletedConstant.NOT_DELETED);
        List<BdPositionEntity> bdPositionEntityList = bdPositionMapper.selectList(wrapper);
        return bdPositionEntityList.stream().map(entity -> new SelectIntegerModel(entity.getPositionId(), entity.getPositionName())).collect(Collectors.toList());
    }

    /*
     * 根据仓库,库位类型查库位
     * */
    @Cacheable(value = CacheKeyConstant.POSITION_NAME_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant + '_' + #spaceId")
    public List<SelectIntegerModel> getBdPositionNameSelectBySpaceIdAndPositionType(Integer spaceId, String positionType) {
        LambdaQueryWrapper<BdPositionEntity> queryWrapper = BdPositionQueryWrapper.buildBdPositionBySpaceIdAndPositionTypeAndIsDeleted(spaceId, positionType, IsDeletedConstant.NOT_DELETED);
        List<BdPositionEntity> positionEntityList = this.list(queryWrapper);
        return positionEntityList.stream().map(entity -> new SelectIntegerModel(entity.getPositionId(), entity.getPositionName())).collect(Collectors.toList());
    }

    /*
     * 根据仓库id校验库位是否存在
     * */
    public BdPosition verifyPosition(Integer spaceId, String positionCode) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionBySpaceIdAndPositionCodeAndIsDeleted(spaceId, positionCode, IsDeletedConstant.NOT_DELETED);
        BdPositionEntity entity = bdPositionMapper.selectOne(wrapper);
        if (entity == null) {
            LOGGER.error("仓库id:{}, 库位:{} 不存在", spaceId, positionCode);
            throw new BusinessServiceException("库位: " + positionCode + " 不存在");
        }
        BdPosition bdPosition = new BdPosition();
        BeanUtilsEx.copyProperties(entity, bdPosition, "quarter");
        if (StrUtil.isNotEmpty(entity.getQuarter())) {
            int year = Integer.parseInt(entity.getQuarter().substring(0, 4));
            int quarter = Integer.parseInt(entity.getQuarter().substring(4, 6));
            bdPosition.setYear(year);
            bdPosition.setQuarter(quarter);
        }
        return bdPosition;
    }

    /**
     * 停用库区下级库位
     */
    @CacheEvict(value = CacheKeyConstant.POSITION_NAME_SELECT, allEntries = true)
    public void deletePositionBySpaceAreaId(Integer spaceAreaId) {
        LambdaQueryWrapper<BdPositionEntity> wrapper = BdPositionQueryWrapper.buildBdPositionBySpaceAreaIdAndIsDeleted(spaceAreaId, IsDeletedConstant.NOT_DELETED);
        List<BdPositionEntity> bdPositionEntityList = bdPositionMapper.selectList(wrapper);
        for (BdPositionEntity positionEntity : bdPositionEntityList) {
            positionEntity.setIsDeleted(1);
            positionEntity.setUpdateBy(loginInfoService.getName());
            bdPositionMapper.updateById(positionEntity);
            String content = String.format("%s 停用 %s", loginInfoService.getName(), positionEntity.getPositionName());
            changeLogService.addChangeLog(BdChangLogTypeEnum.POSITION, content, BdPositionEntity.class.getAnnotation(TableName.class).value());
        }
    }

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_POSITION_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<StockPositionImport> stockPositionImportList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StockPositionImport.class);
        if (CollectionUtils.isEmpty(stockPositionImportList)) {
            return response;
        }
        List<StockPositionImport> errorList = new ArrayList<>();
        List<BdPositionEntity> positionList = new ArrayList<>();
        Map<String, String> positionTypeMap = enumConversionChineseUtils.inverseBaseConversionByType(DictionaryNameEnum.WMS_POSITION_TYPE.getName());
        List<SaStorePageInfoResponse> allStoreInfo = omsApiService.getAllStoreInfo();

        stockPositionImportList.forEach(stockPositionImport -> {
            try {
                //如果是活动库位或OEM库位，需要保存归属组织
                BdPositionAddRequest bdPositionAddRequest = this.buildBdPositionAddRequest(stockPositionImport, positionTypeMap);
                allStoreInfo.stream().filter(storeInfo -> storeInfo.getStoreName().equalsIgnoreCase(stockPositionImport.getStoreName()))
                        .findAny().ifPresent(storeInfo -> bdPositionAddRequest.setStoreId(storeInfo.getId()));

                validBusinessTypeAndStore(bdPositionAddRequest);
                BdPositionEntity bdPosition = this.buildSaveBdPosition(bdPositionAddRequest);
                positionList.add(bdPosition);
            } catch (BusinessServiceException e) {
                stockPositionImport.setErrorMsg(e.getMessage() + " " + stockPositionImport.getPositionCode());
                errorList.add(stockPositionImport);
                LOGGER.error(e.getMessage(), e);
            } catch (Exception e) {
                stockPositionImport.setErrorMsg("网络错误 " + stockPositionImport.getPositionCode());
                errorList.add(stockPositionImport);
                LOGGER.error(e.getMessage(), e);
            }
        });

        if (!errorList.isEmpty()) {
            String dataJsonStr = JsonMapper.toJson(errorList);
            LOGGER.error("导入失败 {} 条， 错误信息 {}", errorList.size(), errorList);
            response.setDataJsonStr(dataJsonStr);
            return response;
        }

        this.saveBatch(positionList);
        for (BdPositionEntity position : positionList) {
            String content = String.format("%s 新增库位:%s", loginInfoService.getName(), position.getPositionName());
            changeLogService.addChangeLog(BdChangLogTypeEnum.POSITION, content, BdPositionEntity.class.getAnnotation(TableName.class).value());
            lingXingPushService.syncPositionCode(position.getPositionCode());

            //活动库位和越库库位同步商通
            if (BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(position.getPositionType())
                    || BdPositionTypeEnum.CROSS_POSITION.name().equals(position.getPositionType())
                    || BdPositionTypeEnum.STORE_POSITION.name().equals(position.getPositionType())) {
                syncPositionToErp(position);
            }
        }

        return response;
    }

    private BdPositionAddRequest buildBdPositionAddRequest(StockPositionImport stockPositionImport, Map<String, String> positionTypeMap) {
        BdSpaceAreaEntity bdSpaceAreaEntity = spaceAreaService.getOne(new QueryWrapper<BdSpaceAreaEntity>()
                .eq("space_area_code", stockPositionImport.getSpaceAreaCode())
                .eq("is_deleted", Boolean.FALSE));
        LOGGER.info(TenantContext.getTenant());
        if (Objects.isNull(bdSpaceAreaEntity)) {
            throw new BusinessServiceException("所属库区不存在");
        }
        BdAreaEntity bdAreaEntity = bdAreaMapper.selectById(bdSpaceAreaEntity.getAreaId());
        if (Objects.isNull(bdAreaEntity) || !bdAreaEntity.getAreaName().equals(stockPositionImport.getAreaName())) {
            throw new BusinessServiceException("所属区域错误");
        }
        BdSpaceEntity bdSpaceEntity = spaceService.getById(bdSpaceAreaEntity.getSpaceId());
        if (Objects.isNull(bdSpaceEntity))
            throw new BusinessServiceException("所属仓库不存在");
        if (!bdSpaceEntity.getSpaceName().equals(stockPositionImport.getSpaceName())) {
            throw new BusinessServiceException("所属仓库错误");
        }
        String positionType = positionTypeMap.get(stockPositionImport.getPositionType());
        if (!StringUtils.hasText(positionType)) {
            throw new BusinessServiceException("库位类型错误");
        }
        BdPositionAddRequest bdPositionAddRequest = new BdPositionAddRequest();
        bdPositionAddRequest.setPositionCode(stockPositionImport.getPositionCode());
        bdPositionAddRequest.setPositionName(stockPositionImport.getPositionName());
        bdPositionAddRequest.setSpaceId(bdSpaceAreaEntity.getSpaceId());
        bdPositionAddRequest.setSpaceName(stockPositionImport.getSpaceName());
        bdPositionAddRequest.setAreaId(bdSpaceAreaEntity.getAreaId());
        bdPositionAddRequest.setAreaName(stockPositionImport.getAreaName());
        bdPositionAddRequest.setSpaceAreaName(bdSpaceAreaEntity.getSpaceAreaName());
        bdPositionAddRequest.setSpaceAreaId(bdSpaceAreaEntity.getSpaceAreaId());
        bdPositionAddRequest.setPositionType(positionType);
        bdPositionAddRequest.setLength(stockPositionImport.getLength());
        bdPositionAddRequest.setWidth(stockPositionImport.getWidth());
        bdPositionAddRequest.setHeight(stockPositionImport.getHeight());
        bdPositionAddRequest.setMaximumWeight(stockPositionImport.getMaximumWeight());
        bdPositionAddRequest.setMaximumVolume(stockPositionImport.getMaximumVolume());
        bdPositionAddRequest.setStoreName(stockPositionImport.getStoreName());
        bdPositionAddRequest.setBusinessType(stockPositionImport.getBusinessType());
        return bdPositionAddRequest;
    }


    public BdPositionEntity getPositionByCode(String positionCode) {
        return this.getOne(new QueryWrapper<BdPositionEntity>().lambda().eq(BdPositionEntity::getPositionCode, positionCode)
                .eq(BdPositionEntity::getIsDeleted, 0)
                .last(MybatisQueryConstant.QUERY_FIRST));
    }

    public BdPositionEntity getByPositionCode(String positionCode) {
        BdPositionEntity entity = getPositionByCode(positionCode);
        if (Objects.isNull(entity))
            throw new BusinessServiceException(String.format("库位 %s 不存在", positionCode));
        return entity;
    }

    /**
     * 获取仓库id发货库位
     */
    public BdPositionEntity getDeliverPosition(Integer spaceId) {
        LambdaQueryWrapper<BdPositionEntity> queryWrapper = BdPositionQueryWrapper.buildBdPositionBySpaceIdAndPositionTypeAndIsDeleted(spaceId, BdPositionTypeEnum.SHIPPING_POSITION.name(), IsDeletedConstant.NOT_DELETED);
        List<BdPositionEntity> positionEntityList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(positionEntityList)) {
            throw new BusinessServiceException(String.format("根据仓库id:%s 找不到发货库位", spaceId));
        }
        return positionEntityList.get(0);
    }

    /**
     * 获取库区库位数量
     */
    public Integer getPositionCount(Integer spaceAreaId) {
        return this.count(new QueryWrapper<BdPositionEntity>()
                .eq("space_area_id", spaceAreaId)
                .eq("is_deleted", 0));
    }

    /**
     * 根据 库区排序、库位排序，查找范围库位
     *
     * @param startPositionEntity 起始库位
     * @param endPositionEntity   结束库位
     */
    public List<BdPositionEntity> getBetweenPosition(BdPositionEntity startPositionEntity, BdPositionEntity endPositionEntity) {
        if (startPositionEntity.getSpaceAreaId().equals(endPositionEntity.getSpaceAreaId()))
            return this.list(Wrappers.<BdPositionEntity>lambdaQuery()
                    .eq(BdPositionEntity::getSpaceAreaId, startPositionEntity.getSpaceAreaId())
                    .between(BdPositionEntity::getSort, startPositionEntity.getSort(), endPositionEntity.getSort())
            );
        // 库区范围数据
        BdSpaceAreaEntity startSpaceAreaEntity = spaceAreaService.getById(startPositionEntity.getSpaceAreaId());
        BdSpaceAreaEntity endSpaceAreaEntity = spaceAreaService.getById(endPositionEntity.getSpaceAreaId());
        List<Integer> spaceAreaIdList = spaceAreaService.list(new QueryWrapper<BdSpaceAreaEntity>().lambda()
                        .between(BdSpaceAreaEntity::getSort, startSpaceAreaEntity.getSort(), endSpaceAreaEntity.getSort()))
                .stream().map(BdSpaceAreaEntity::getSpaceAreaId).collect(Collectors.toList());
        List<BdPositionEntity> result = new LinkedList<>();
        BdPositionEntity temp;
        List<BdPosition> sortList = bdPositionMapper.findSortListBySpaceAreaIds(spaceAreaIdList, null);
        boolean isStart = false;
        for (BdPosition position : sortList) {
            if (!isStart && !position.getPositionId().equals(startPositionEntity.getPositionId()))
                continue;
            isStart = true;
            temp = new BdPositionEntity();
            BeanUtils.copyProperties(position, temp);
            result.add(temp);
            if (position.getPositionId().equals(endPositionEntity.getPositionId()))
                return result;
        }
        return result;
    }

    public List<SelectIntegerSortModel> getSortNameList(BdPositionSelectRequest request) {
        List<BdPosition> sortList = bdPositionMapper.findSortListBySpaceAreaIds(request.getSpaceAreaIds(), request.getPositionTypes());
        List<SelectIntegerSortModel> result = new LinkedList<>();
        int index = 1;
        for (BdPosition position : sortList) {
            result.add(new SelectIntegerSortModel(position.getPositionId(), position.getPositionCode(), index));
            index++;
        }
        return result;
    }

    /**
     * 库位打印
     */
    public PrintListResponse printPosition(PositionCodePrintRequest idList) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.BDPOSITION_CODE.getTemplateName());
        List<BdPositionEntity> bdPositionEntities = listByIds(idList.getIdList());
        if (CollectionUtils.isEmpty(bdPositionEntities)) {
            throw new InvalidRequestException("请选择需要打印的库位信息");
        }
        if (this.baseMapper.selectBatchIds(idList.getIdList()).stream().anyMatch(item -> item.getIsDeleted() != 0)) {
            throw new InvalidRequestException("请选择已启用的库位信息");
        }
        List<String> list = new ArrayList<>();
        List<BdPositionPrint> printDetail = bdPositionMapper.getPrintDetail(idList);
        Map<String, String> spaceTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_POSITION_TYPE.getName());
        printDetail.forEach(i -> {
            i.setPositionTypeStr(spaceTypeEnumMap.get(i.getPositionType()));
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), i);
            list.add(transfer);
        });
        response.setHtmlList(list);
        response.setHtmlList(PrintTransferUtils.doubleTransfer(!idList.getDoublePrint(), response.getHtmlList(), templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 通过库位id和库位类型查找
     *
     * @param spaceId
     * @param positionType
     * @return
     */
    public BdPositionEntity findFirstBySpaceIdAndPositionType(Integer spaceId, String positionType) {
        return this.getOne(new LambdaQueryWrapper<BdPositionEntity>()
                .eq(BdPositionEntity::getSpaceId, spaceId)
                .eq(BdPositionEntity::getPositionType, positionType).orderByAsc(BdPositionEntity::getPositionId)
                .last("limit 1"));

    }

    public List<BdPositionEntity> findAllBySpaceIdAndPositionType(Integer spaceId, String positionType) {
        return this.list(new LambdaQueryWrapper<BdPositionEntity>()
                .eq(BdPositionEntity::getSpaceId, spaceId).eq(BdPositionEntity::getIsDeleted, 0)
                .eq(BdPositionEntity::getPositionType, positionType).orderByAsc(BdPositionEntity::getPositionId));
    }

    public BdPositionEntity findBySpaceIdAndPositionCode(Integer spaceId, String positionCode) {
        return this.getOne(new LambdaQueryWrapper<BdPositionEntity>()
                .eq(BdPositionEntity::getSpaceId, spaceId)
                .eq(BdPositionEntity::getPositionCode, positionCode)
                .eq(BdPositionEntity::getIsDeleted, 0)
                .last("limit 1"));
    }

    public List<BdPositionEntity> findInfoByPositionCode(List<String> positionCodeList, String location) {
        return this.baseMapper.findInfoByPositionCode(positionCodeList, location);
    }

    public BdPositionEntity getTopByAreaName(Integer spaceId, String areaName) {
        BdAreaEntity area = bdAreaService.getByAreaNameAndSpaceId(areaName, spaceId);

        BdPositionEntity one = getOne(Wrappers.<BdPositionEntity>lambdaQuery().eq(BdPositionEntity::getAreaId, area.getAreaId())
                .eq(BdPositionEntity::getIsDeleted, Boolean.FALSE).last("limit 1"));
        if (Objects.isNull(one)) {
            throw new BusinessServiceException(String.format("该区域下没有设置库位 %s", areaName));
        }
        return one;
    }

    @Transactional
    public void batchEditBdPositionSort(List<BdPosition> request) {
        request.forEach(item -> {
            this.getBaseMapper().updateSortByPositionCode(item.getPositionCode(), item.getSort());
        });
    }
}
