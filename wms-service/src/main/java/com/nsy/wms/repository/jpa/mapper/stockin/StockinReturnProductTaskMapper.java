package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockin.StockinQcReportDayExport;
import com.nsy.api.wms.domain.stockin.StockinReturnTaskPrint;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.ReturnRecordRequest;
import com.nsy.api.wms.request.stockin.StockinReturnOrderPageRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskListRequest;
import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.stockin.QueryReworkedReturnedRecordResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderPageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductSingleTaskInfo;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskInfoResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListCountResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListItemResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnTaskItemInfoListResponse;
import com.nsy.api.wms.response.stockin.TaskListCountResponse;
import com.nsy.permission.annatation.Permission;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface StockinReturnProductTaskMapper extends BaseMapper<StockinReturnProductTaskEntity> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockinReturnProductTaskListResponse> getTaskList(IPage page, @Param("request") StockinReturnProductTaskListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    StockinReturnProductTaskEntity getByIdIgnore(Integer taskId);

    IPage<StockinReturnProductTaskListItemResponse> getExportTaskListItem(IPage page, @Param("request") StockinReturnProductTaskListRequest request);


    StockinReturnProductTaskInfoResponse getTaskInfo(Integer taskId);

    List<StockinReturnProductSingleTaskInfo> getPdaTaskInfo(String positionCode);

    List<StockinReturnTaskPrint> printTask(@Param("request") IdListRequest request);

    List<TaskListCountResponse> countByStatus(@Param("query") TabCountRequest request);

    @Permission
    List<StockinReturnOrderPageResponse> returnOrderPageList(Page page, @Param("request") StockinReturnOrderPageRequest request);

    @Permission
    List<StockinReturnTaskItemInfoListResponse> queryReturnTaskItemList(Page page, @Param("request") StockinReturnOrderPageRequest request);

    @Permission
    List<StatusTabResponse> getTabs(@Param("statusList") List<String> statusList);

    List<StockinQcReportDayExport> queryQcReportDay(Date dateStart, Date dateEnd);

    /**
     * 根据skc和purchasePlanNo 查已退货返工记录
     *
     * @param request
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    IPage<QueryReworkedReturnedRecordResponse> queryReworkedReturnedRecord(IPage<QueryReworkedReturnedRecordResponse> page, @Param("request") ReturnRecordRequest request);

    /**
     * 列表统计
     *
     * @param request
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    StockinReturnProductTaskListCountResponse getTaskListCount(@Param("request") StockinReturnProductTaskListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinReturnProductTaskEntity> searchByRealReturnProductTaskIdListIgnoreTenant(@Param("returnProductTaskIdList") List<Integer> returnProductTaskIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinReturnProductTaskEntity> searchByReturnProductTaskIdListIgnoreTenant(@Param("returnProductTaskIdList") List<Integer> returnProductTaskIdList);

    @InterceptorIgnore(tenantLine = "true")
    Integer deleteByReturnProductTaskIdIgnoreTenant(@Param("returnProductTaskId") Integer returnProductTaskId);

    /**
     * 统计退货总件数
     *
     * @param request 查询条件
     * @return 退货总件数
     */
    @Permission
    Integer statisticsReturnTotalQty(@Param("request") StockinReturnOrderPageRequest request);
}




