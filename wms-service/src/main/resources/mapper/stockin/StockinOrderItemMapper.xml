<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderItemMapper">

    <select id="pageSearchStockinOrderItemList"
            resultType="com.nsy.api.wms.response.stockin.StockinOrderItemSkuListResponse">
        select
        ANY_VALUE(o.stockin_order_id) as stockinOrderId,
        ANY_VALUE(o.real_stockin_order_id) as realStockinOrderId,
        ANY_VALUE(ps.thumbnail_image_url) as thumbnailImageUrl,
        ANY_VALUE(ps.preview_image_url) as previewImageUrl,
        ANY_VALUE(ps.color) as color,
        ANY_VALUE(ps.size) as size,
        ANY_VALUE(p.spu) as spu,
        i.sku as sku,
        ANY_VALUE(ps.barcode) as barcode,
        i.purchase_plan_no as purchasePlanNo,
        i.branch_purchase_plan_no as branchPurchasePlanNo,
        ANY_VALUE(t.supplier_delivery_no) as supplierDeliveryNo,
        ANY_VALUE(ti.is_need_qa) as isNeedQa,
        ANY_VALUE(ti.shelve_space_area_name) as spaceAreaName,
        i.qty as stockinQty,
        i.return_qty as returnQty,
        i.shelved_qty as shelvedQty,
        ANY_VALUE(ti.qa_qty) as qaQty,
        i.internal_box_code as internal_box_code,
        i.status as status,
        i.unqualified_reason as unqualifiedReason,
        i.unqualified_category as unqualifiedCategory,
        srpti.actual_return_qty as actualReturnQty,
        o.supplier_delivery_box_code as supplier_delivery_box_code,
        i.is_fba_quick as isFbaQuick,
        i.brand_name as brandName,
        i.vacuum_flag as vacuumFlag,
        p.package_vacuum as packageVacuum,
        ti.store_id as storeId,
        if(i.`status`='WAIT_RETURN',i.qty,0) as waitReturnQty,
        ti.area_name as areaName
        from stockin_order_item i
        left join stockin_order o on i.stockin_order_id=o.stockin_order_id
        left join product_spec_info ps on i.spec_id=ps.spec_id
        left join product_info p on ps.product_id=p.product_id
        left join stockin_order_task t on o.task_id=t.task_id
        left join stockin_order_task_item ti on i.task_item_id=ti.task_item_id
        left join stockin_return_product_task_item srpti on i.sku=srpti.sku and
        srpti.stockin_order_no=o.stockin_order_no and srpti.status='RETURN_SUCCESS'
        where i.stockin_order_id = #{query.stockinOrderId}
        <if test="query.sku!=null and query.sku!=''">
            and i.sku like concat(#{query.sku}, '%')
        </if>
        <if test="query.brandName!=null and query.brandName!=''">
            and i.brand_name #{query.brandName}
        </if>
        GROUP BY i.sku,i.internal_box_code,i.purchase_plan_no,i.status
    </select>

    <select id="listQaInternalBox" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item soi
        left join stock_internal_box sib
        on soi.internal_box_code = sib.internal_box_code
        where soi.task_item_id in
        <foreach collection="taskItemList" separator="," index="index" item="taskItemId"
                 open="(" close=")">
            #{taskItemId}
        </foreach>
        and sib.internal_box_type = 'QA_BOX'
    </select>

    <select id="findQaBoxItem" resultType="java.lang.String">
        SELECT DISTINCT b.internal_box_code
        FROM stockin_order_item i
                 LEFT JOIN stockin_order o ON o.stockin_order_id = i.stockin_order_id
                 LEFT JOIN stock_internal_box b on i.internal_box_code = b.internal_box_code
        WHERE o.stockin_order_no = #{stockinOrderNo}
          AND i.purchase_plan_no = #{purchasePlanNo}
          AND i.sku = #{sku}
          and b.internal_box_type = 'QA_BOX';
    </select>

    <select id="findDiffBoxItem" resultType="com.nsy.api.wms.domain.stock.StockinOrderItemBean">
        select oi.*, b.internal_box_type
        from stockin_order_item oi
                 left join stock_internal_box b on oi.internal_box_code = b.internal_box_code
        where oi.stockin_order_id = #{stockinOrderId}
          and oi.sku = #{sku}
          and oi.internal_box_code != #{internalBoxCode}
          and oi.qty > 0
    </select>

    <select id="findAllByTaskItemId" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item oi
        left join stock_internal_box b on oi.internal_box_code = b.internal_box_code
        where oi.task_item_id in
        <foreach collection="taskItemIds" separator="," index="index" item="taskItemId"
                 open="(" close=")">
            #{taskItemId}
        </foreach>
        and b.internal_box_type = #{internalBoxType}
    </select>

    <select id="findAllByTaskItemIdAndBox" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item oi
        where oi.task_item_id in
        <foreach collection="taskItemIds" separator="," index="index" item="taskItemId"
                 open="(" close=")">
            #{taskItemId}
        </foreach>
        and oi.internal_box_code = #{internalBoxCode}
        and oi.sku = #{sku}
    </select>

    <select id="selectBySupplierDeliveryBoxCodeAndSku"
            resultType="com.nsy.wms.business.domain.bo.stockin.StockinOrderItemBo">
        SELECT soi.*,
               so.stockin_order_no
        FROM stockin_order_item soi
                 LEFT JOIN stockin_order so on soi.stockin_order_id = so.stockin_order_id
                 LEFT JOIN stockin_order_task_item soti ON soi.task_item_id = soti.task_item_id
                 LEFT JOIN stockin_order_task sot ON soti.task_id = sot.task_id

        WHERE sot.supplier_delivery_box_code = #{supplierDeliveryBox}
          AND soti.sku = #{sku}
    </select>

    <select id="findAllBySupplierDeliveryNoAndQaBox"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item oi
        left join stock_internal_box b on oi.internal_box_code = b.internal_box_code
        left join stockin_order_task_item ti on oi.task_item_id = ti.task_item_id
        left join stockin_order_task t on t.task_id = ti.task_id
        where t.supplier_delivery_no = #{supplierDeliveryNo}
        and b.internal_box_type = 'QA_BOX'
        <if test="skus != null and skus.size() > 0">
            and oi.sku in
            <foreach collection="skus" separator="," index="index" item="sku"
                     open="(" close=")">
                #{sku}
            </foreach>
        </if>
    </select>

    <select id="getIsPreQaBySupplierDeliveryNo" resultType="java.lang.String">
        select distinct o.stockin_order_no from stockin_order_item oi
        left join stock_internal_box b on oi.internal_box_code = b.internal_box_code
        left join stockin_order o on oi.stockin_order_id = o.stockin_order_id
        left join stockin_order_task t on t.task_id = o.task_id
        where b.internal_box_type = 'QA_BOX'
        and oi.qty >0
        and t.supplier_delivery_no in
        <foreach collection="supplierDeliveryNos" separator="," index="index" item="supplierDeliveryNo"
                 open="(" close=")">
            #{supplierDeliveryNo}
        </foreach>
    </select>

    <select id="sumQty" resultType="java.lang.Integer">
        SELECT sum(qty)
        FROM stockin_order_item
        WHERE create_date BETWEEN #{dateStart} and #{dateEnd}
    </select>

    <select id="pageSearchReceiptItemList" resultType="com.nsy.api.wms.response.stockin.StockReceiptItem">
        SELECT oi.sku,
        si.thumbnail_image_url,
        si.preview_image_url,
        oi.purchase_plan_no as purchaseNo,
        si.barcode,
        GROUP_CONCAT(DISTINCT oi.status) as status,
        GROUP_CONCAT(DISTINCT oi.internal_box_code) as internalBoxCode,
        oti.expected_qty as shipmentQty,
        ifnull(SUM(oi.qty),0) as receiveQty,
        ifnull(SUM(oi.return_qty),0) as returnQty,
        ifnull(SUM(oi.shelved_qty),0) as shelvedQty,
        oi.create_date,
        oi.update_date
        FROM stockin_order_task_item oti
        LEFT JOIN stockin_order_item oi On oti.task_item_id = oi.task_item_id
        LEFT JOIN product_spec_info si ON si.spec_id = oi.spec_id
        <where>
            <if test="query != null and query.stockinOrderId != null">
                and oi.stockin_order_id = #{query.stockinOrderId}
            </if>
            <if test="query != null and query.sku != null and query.sku != ''">
                and oi.sku like concat(#{query.sku}, '%')
            </if>
        </where>
        group by oti.task_item_id
        order by si.skc,si.sort
    </select>
    <select id="getReceiveBoxSkuStatus" resultType="java.lang.String">
        SELECT i.status
        FROM stock_internal_box_item i
        where i.internal_box_code = #{internalBoxCode}
          and i.sku = #{sku}
          and i.stock_in_order_no = #{stockInOrderNo}
          and i.purchase_plan_no = #{purchasePlanNo}
        limit 1
    </select>
    <select id="getQaBoxSkuStatus" resultType="java.lang.String">
        SELECT i.status
        FROM stockin_order_item i
                 LEFT JOIN stockin_order o ON o.stockin_order_id = i.stockin_order_id
                 LEFT JOIN stock_internal_box b on i.internal_box_code = b.internal_box_code
        WHERE i.sku = #{sku}
          AND i.internal_box_code = #{internalBoxCode}
          AND b.internal_box_type = 'QA_BOX'
        limit 1
    </select>

    <select id="getStockInOrderItemArrivalCount"
            resultType="com.nsy.api.wms.response.stockin.StockInOrderArrivalCountResponse">
        SELECT
        t.supplier_delivery_no as supplierDeliveryNo,
        GROUP_CONCAT(t.supplier_delivery_box_code) as supplierDeliveryBoxCode,
        item.sku as sku,
        sum(item.expected_qty) as qty
        FROM
        stockin_order_task_item item
        LEFT JOIN stockin_order_task t on item.task_id = t.task_id
        WHERE
        t.supplier_delivery_no in
        <foreach collection="supplierDeliveryNo" separator="," index="index" item="item" open="("
                 close=")">
            #{item}
        </foreach>
        GROUP BY t.supplier_delivery_no,item.sku
    </select>

    <select id="findAllByStockinOrderIdIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item
        <where>
            <if test="stockinOrderIdList != null and stockinOrderIdList.size() > 0">
                stockin_order_id in
                <foreach collection="stockinOrderIdList" separator="," index="index" item="stockinOrderId" open="("
                         close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findAllByStockinOrderItemIdIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item
        <where>
            <if test="stockinOrderItemIdList != null and stockinOrderItemIdList.size() > 0">
                stockin_order_item_id in
                <foreach collection="stockinOrderItemIdList" separator="," index="index" item="stockinOrderItemId"
                         open="("
                         close=")">
                    #{stockinOrderItemId}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteByStockinOrderIdIgnoreTenant">
        delete
        from stockin_order_item
        where stockin_order_id = #{stockinOrderId}
    </delete>

    <update id="updateIgnoreTenant" parameterType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        UPDATE stockin_order_item
        SET internal_box_code  = #{internalBoxCode},
            status             = #{status},
            qty                = #{qty},
            stockin_return_qty = #{stockinReturnQty},
            return_qty         = #{returnQty},
            concessions_count  = #{concessionsCount},
            unqualified_reason = #{unqualifiedReason},
            update_by          = #{updateBy}
        WHERE stockin_order_item_id = #{stockinOrderItemId}
    </update>

    <select id="findAllByPurchasePlanNosAndSkusIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item
        <where>
            <if test="query != null and query.location != null">
                and location = #{query.location}
            </if>
            <if test="query != null and query.orderNoList != null and query.orderNoList.size() > 0">
                and (purchase_plan_no in
                <foreach collection="query.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
                or branch_purchase_plan_no in
                <foreach collection="query.orderNoList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query != null and query.skuList != null and query.skuList.size() > 0">
                and sku in
                <foreach collection="query.skuList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryStockinOrderItemReceiveInfoIgnoreTenant"
            resultType="com.nsy.api.wms.response.stockin.QueryStockinOrderItemReceiveInfoResponse">
        select so.supplier_delivery_box_code as supplierDeliveryBoxCode, soi.purchase_plan_no as orderNo, soi.sku as
        sku, min(soi.create_date) as receiveDate from stockin_order_item soi
        inner join stockin_order so on so.stockin_order_id = soi.stockin_order_id
        <where>
            <if test="query.supplierDeliveryBoxCodeList != null and query.supplierDeliveryBoxCodeList.size() > 0">
                so.supplier_delivery_box_code in
                <foreach collection="query.supplierDeliveryBoxCodeList" separator="," index="index"
                         item="supplierDeliveryBoxCode" open="(" close=")">
                    #{supplierDeliveryBoxCode}
                </foreach>
            </if>
            <if test="query.orderNoList != null and query.orderNoList.size() > 0">
                and soi.purchase_plan_no in
                <foreach collection="query.orderNoList" separator="," index="index" item="orderNo" open="(" close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="query.skuList != null and query.skuList.size() > 0">
                and soi.sku in
                <foreach collection="query.skuList" separator="," index="index" item="sku" open="(" close=")">
                    #{sku}
                </foreach>
            </if>
        </where>
        group by so.supplier_delivery_box_code, soi.purchase_plan_no, soi.sku
    </select>

    <select id="statisticsByReceiptSummaryRequest"
            resultType="com.nsy.api.wms.response.stockin.StatisticsByStockinOrderIdResponse">
        SELECT soi.stockin_order_id,
        soi.purchase_plan_no,
        IFNULL(soi.return_qty,0) as returnQty,
        soi.qty as receiveQty,
        soi.shelved_qty as shelvedQty
        FROM stockin_order_item soi
        INNER JOIN stockin_order_task_item soti ON soti.task_item_id = soi.task_item_id
        <if test="request!=null and request.spu != null and request.spu !=''">
            INNER JOIN product_info p ON p.product_id = soi.product_id
        </if>
        <where>
            <if test="request!=null and request.stockinOrderIdList != null and request.stockinOrderIdList.size() > 0">
                and soi.stockin_order_id in
                <foreach collection="request.stockinOrderIdList" separator="," item="stockinOrderId" open="(" close=")">
                    #{stockinOrderId}
                </foreach>
            </if>
            <if test="request!=null and request.purchaseNo !=null and request.purchaseNo !=''">
                and soi.purchase_plan_no = #{request.purchaseNo}
            </if>
            <if test="request!=null and request.sku !=null and request.sku !=''">
                and soi.sku = #{request.sku}
            </if>
            <if test="request!=null and request.spu !=null and request.spu !=''">
                and p.spu = #{request.spu}
            </if>
            <if test="request!=null and request.spaceId !=null">
            </if>
        </where>
    </select>

    <select id="searchStockinOrderItemList"
            resultType="com.nsy.api.wms.response.stockin.StockinOrderItemCommonResponse">
        select
        ANY_VALUE(i.stockin_order_item_id) as stockinOrderItemId,
        ANY_VALUE(o.stockin_order_id) as stockinOrderId,
        ANY_VALUE(i.product_id) as productId,
        ANY_VALUE(i.spec_id) as specId,
        ANY_VALUE(o.task_id) as taskId,
        ANY_VALUE(ti.task_item_id) as taskItemId,
        i.sku as sku,
        i.qty as stockinQty,
        ANY_VALUE(p.delivery_date) as deliveryDate,
        ANY_VALUE(bp.space_id) as spaceId,
        ANY_VALUE(bp.space_name) as spaceName,
        ANY_VALUE(bp.area_name) as areaName,
        ti.store_id as storeId,
        i.purchase_plan_no as purchaseOrderNo,
        i.internal_box_code as internalBoxCode,
        ssti.position_code as positionCode,
        ssti.stockin_qty as shelvedStockinQty,
        ssti.returned_qty as shelvedReturnQty
        from stockin_order_item i
        left join stockin_order o on i.stockin_order_id=o.stockin_order_id
        left join stockin_order_task t on o.task_id=t.task_id
        left join stockin_order_task_item ti on i.task_item_id=ti.task_item_id
        left join stock_platform_schedule p on p.platform_schedule_id = t.platform_schedule_id
        left join stockin_shelve_task_item ssti ON o.stockin_order_id = ssti.source_id AND ssti.`sku` = i.sku
        left join bd_position bp ON ssti.position_code = bp.`position_code`
        <where>
            <if test="stockinOrderId!=null">
                and i.stockin_order_id = #{stockinOrderId} and i.status !='RETURNED'
                and ssti.stockin_qty &lt;&gt; ssti.returned_qty
            </if>
        </where>
        GROUP BY i.sku,i.internal_box_code,i.purchase_plan_no
    </select>

    <select id="searchStockinOrderItemListContainShelved"
            resultType="com.nsy.api.wms.response.stockin.StockinOrderItemCommonResponse">
        select
        ANY_VALUE(i.stockin_order_item_id) as stockinOrderItemId,
        ANY_VALUE(o.stockin_order_id) as stockinOrderId,
        ANY_VALUE(i.product_id) as productId,
        ANY_VALUE(i.spec_id) as specId,
        ANY_VALUE(o.task_id) as taskId,
        ANY_VALUE(ti.task_item_id) as taskItemId,
        i.sku as sku,
        i.qty as stockinQty,
        ANY_VALUE(p.delivery_date) as deliveryDate,
        ANY_VALUE(bp.space_id) as spaceId,
        ANY_VALUE(bp.space_name) as spaceName,
        ANY_VALUE(bp.area_name) as areaName,
        ti.store_id as storeId,
        i.purchase_plan_no as purchaseOrderNo,
        i.internal_box_code as internalBoxCode,
        ssti.position_code as positionCode,
        ssti.stockin_qty as shelvedStockinQty,
        ssti.returned_qty as shelvedReturnQty
        from stockin_order_item i
        left join stockin_order o on i.stockin_order_id=o.stockin_order_id
        left join stockin_order_task t on o.task_id=t.task_id
        left join stockin_order_task_item ti on i.task_item_id=ti.task_item_id
        left join stock_platform_schedule p on p.platform_schedule_id = t.platform_schedule_id
        left join stockin_shelve_task_item ssti ON o.stockin_order_id = ssti.source_id AND ssti.`sku` = i.sku
        left join bd_position bp ON ssti.position_code = bp.`position_code`
        <where>
            <if test="stockinOrderId!=null">
                and i.stockin_order_id = #{stockinOrderId} and i.status !='RETURNED'
                and (ssti.stockin_qty &lt;&gt; ssti.returned_qty or ssti.shelve_task_item_id is null )
            </if>
        </where>
        GROUP BY i.sku,i.internal_box_code,i.purchase_plan_no
    </select>


    <select id="getPurchaseCount" resultType="com.nsy.api.wms.response.external.QcPurchaseCount">
        select oi.qty,
        oi.return_qty as returnQty,
        oi.purchase_plan_no as purchasePlanNo,
        oi.sku as sku,
        ti.package_name as packageName,
        o.supplier_delivery_box_code as supplierDeliveryBoxCode,
        ti.purchasing_apply_type as purchasingApplyType
        from stockin_order_item oi
        INNER JOIN stockin_order o
        on o.stockin_order_id = oi.stockin_order_id
        INNER JOIN stockin_order_task_item ti
        on oi.task_item_id = ti.task_item_id
        where oi.stockin_order_id in
        <foreach collection="stockinOrderIds" separator="," index="index" item="stockinOrderId" open="("
                 close=")">
            #{stockinOrderId}
        </foreach>
        and oi.sku = #{sku}
        and oi.internal_box_code = #{internalBoxCode}
        <if test="purchasePlanNo != null and purchasePlanNo != ''">
            and oi.purchase_plan_no = #{purchasePlanNo}
        </if>
    </select>
    <select id="getCanEditPackage" resultType="com.nsy.api.wms.domain.stockin.StockinOrderItem">
        select status,sku,qty
        from stockin_order_item
        where purchase_plan_no = #{query.purchasePlanNo}
        and sku in
        <foreach collection="query.skuList" separator="," index="index" item="sku" open="("
                 close=")">
            #{sku}
        </foreach>
        and qty > 0
    </select>
    <select id="getConcessionItem" resultType="com.nsy.api.wms.domain.stockin.StockinOrderItem">

        select oi.stockin_order_item_id,
        oi.stockin_order_id,
        oi.sku,
        oi.qty,
        oi.`status`,
        oi.shelved_qty,
        oi.concessions_count,
        oi.purchase_plan_no,
        oi.create_date,
        max(qi.concession_price) as concessionPrice
        from stockin_qc_inbounds_item qi
        INNER JOIN stockin_order_item oi on qi.stockin_order_item_id = oi.stockin_order_item_id
        where qi.stockin_order_id in
        <foreach collection="stockinOrderIds" separator="," index="index" item="stockinOrderId" open="("
                 close=")">
            #{stockinOrderId}
        </foreach>
        and oi.concessions_count >0
        and oi.shelved_qty >0
        GROUP BY oi.stockin_order_item_id

    </select>
    <select id="getAreaName" resultType="java.lang.String">
        select distinct ti.area_name
        from stockin_order_item oi
        INNER JOIN stockin_order o
        on o.stockin_order_id = oi.stockin_order_id
        INNER JOIN stockin_order_task_item ti
        on oi.task_item_id = ti.task_item_id
        where o.stockin_order_no = #{stockinOrderNo}
        and oi.sku = #{sku}
        <if test="purchasePlanNo != null and purchasePlanNo != ''">
            and oi.purchase_plan_no = #{purchasePlanNo}
        </if>
        limit 1
    </select>

    <select id="getStockInPageItemInfo" resultType="com.nsy.api.wms.domain.stockin.StockInPageItemInfo">
        select stockin_order_id as stockinOrderId,
        IFNULL(SUM(qty),0) as qty,
        GROUP_CONCAT(DISTINCT(purchase_plan_no)) AS purchasePlanNo,
        GROUP_CONCAT(DISTINCT(branch_purchase_plan_no)) AS branchPurchasePlanNo
        from stockin_order_item
        where stockin_order_id in
        <foreach collection="stockInOrderIdList" separator="," index="index" item="stockinOrderId" open="(" close=")">
            #{stockinOrderId}
        </foreach>
        GROUP BY stockin_order_id
    </select>
    <select id="getAreaNameByShelveTask" resultType="java.lang.String">
        select distinct ti.area_name
        from stockin_order_item oi
        INNER JOIN stockin_order_task_item ti
        on oi.task_item_id = ti.task_item_id
        where oi.stockin_order_id in
        <foreach collection="sourceId" separator="," index="index" item="stockinOrderId" open="(" close=")">
            #{stockinOrderId}
        </foreach>
        and oi.sku = #{sku}
        and oi.internal_box_code = #{internalBoxCode}
    </select>
    <select id="searchQtyByReturnQtyaBatch"
            resultType="com.nsy.api.wms.response.stockin.ReturnProductSearchQtyResponse">
        select
        purchase_plan_no,
        sku,
        sum(return_qty) as returnCount
        from stockin_order_item
        where (purchase_plan_no, sku) in
        <foreach collection="requestList" separator="," item="info" open="(" close=")">
            (#{info.purchasePlanNo},#{info.sku})
        </foreach>
        GROUP BY purchase_plan_no, sku
    </select>
    <select id="findItemToEdit" resultType="com.nsy.api.wms.domain.stockin.StockinOrderTaskItem">
        select
        ti.task_item_id as taskItemId,
        ti.task_id as taskId,
        ti.expected_qty as expectedQty,
        ti.stockin_qty as scanQty,
        ti.barcode as barcode,
        (select ifnull(sum(qty),0) from stockin_order_item
        where task_item_id = ti.task_item_id
        and internal_box_code = #{internalBoxCode}) as qty,
        ti.sku as sku
        from stockin_order_task_item ti
        where ti.task_id in
        <foreach collection="taskIds" separator="," index="index" item="taskId" open="(" close=")">
            #{taskId}
        </foreach>
        and ti.spec_id = #{specId}
    </select>

    <select id="productStockinOrderTimeQuery"
            resultType="com.nsy.api.wms.response.stockin.StockinOrderTimeQueryResponse">
        SELECT b.product_id as productId,min(b.create_date) as firstStockinTime,max(b.create_date) as lastStockinTime
        FROM stockin_order a
        INNER JOIN stockin_order_item b
        ON a.stockin_order_id = b.stockin_order_id
        where a.stockin_type &lt;&gt; 'ALLOT' AND a.order_type = 0
        and b.product_id in
        <foreach collection="productIdList" separator="," index="index" item="productId" open="(" close=")">
            #{productId}
        </foreach>
        GROUP BY b.product_id
    </select>
    <select id="queryCheckOrShelve" resultType="com.nsy.api.wms.response.stockin.CheckOrShelveResponse">

        select
        sku as sku,
        if(oi.`status` in ('WAIT_SHELVE','WAIT_QC') and not EXISTS (select 1 from stockin_qc_inbounds_item where
        stockin_order_item_id = oi.stockin_order_item_id limit 1),true,false) as checkOrShelve
        from
        stockin_order_item oi
        where oi.purchase_plan_no = #{request.orderNo}
        and oi.sku in
        <foreach collection="request.skuList" separator="," index="index" item="sku" open="(" close=")">
            #{sku}
        </foreach>
        GROUP BY oi.sku;
    </select>
    <select id="selectZeroQtyItemIdBySupplierDeliveryNo" resultType="java.lang.Integer">
        select distinct oi.stockin_order_item_id
        from stockin_order_item oi
                 inner join stockin_order_task_item ti
                            on ti.task_item_id = oi.task_item_id
                 inner join stockin_order_task t
                            on t.task_id = ti.task_id
        where t.supplier_delivery_no = #{supplierDeliveryNo}
          and oi.qty = 0
    </select>

    <select id="getSpaceIdByCondition" resultType="java.lang.Integer">
        select oi.space_id from stockin_order_item oi
        inner join stockin_order o on o.stockin_order_id = oi.stockin_order_id
        where oi.purchase_plan_no = #{purchasePlanNo} and oi.sku =#{sku}
        and o.stockin_order_no = #{stockinOrderNo} and oi.space_id is not null limit 1
    </select>

    <select id="sumArrivalCount" resultType="java.lang.Integer">
        SELECT
        ifnull(sum(oi.qty),0) as qty
        FROM
        stockin_order_task t
        inner join stockin_order o
        on t.task_id = o.task_id
        inner join stockin_order_item oi
        on oi.stockin_order_id = o.stockin_order_id
        and oi.sku = #{sku}
        WHERE
        t.supplier_delivery_no in
        <foreach collection="supplierDeliveryNoList" separator="," index="index" item="item" open="("
                 close=")">
            #{item}
        </foreach>
    </select>
    
    <select id="calculateReturnRange" resultType="java.lang.Double">
        SELECT SUM(return_qty) / SUM(qty) AS return_rate 
        FROM stockin_order_item
        WHERE sku = #{sku}
        AND qty > 0
    </select>
    
    <select id="getPackageNameInfo" resultType="com.nsy.api.wms.response.stockin.StockinPackageNameResponse">
        select
        soi.sku,
        soti.package_name,
        sum(soi.qty) as qty
        from stockin_order so
        inner join stockin_order_item soi on soi.stockin_order_id = so.stockin_order_id
        inner join stockin_order_task_item soti on soti.task_item_id = soi.task_item_id
        inner join stockin_order_task sot on sot.task_id = soti.task_id
        where soi.internal_box_code = #{internalBoxCode}
        and sot.supplier_delivery_no = #{supplierDeliveryNo}
        <if test="sku!=null and sku!=''">
            and soti.sku = #{sku}
        </if>
        group by soi.sku,soti.package_name
    </select>
    <select id="findOneEverStockin" resultType="com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity">
        select * from stockin_order_item oi
        inner join stockin_order o
        on o.stockin_order_id = oi.stockin_order_id
        where oi.sku = #{sku}
        and o.supplier_id = #{supplierId}
        limit 1

    </select>
    <select id="getPurchaseOrderRequest"
            resultType="com.nsy.wms.business.manage.scm.request.PurchaseOrderRequestItem">
        select
        ti.create_date as arrivalTime,
        (select max(concession_price) from stockin_qc_inbounds_item where stockin_order_item_id =oi.stockin_order_item_id) as concessionPrice,
        ifnull(oi.concessions_count,0) as concessionQty,
        ps.delivery_date as deliveryDate,
        ti.expected_qty as deliveryQty,
        oi.unqualified_category as unqualifiedCategory,
        ti.package_name as packageName,
        ti.purchase_plan_no as purchaseOrderPlanNo,
        oi.create_date as receiveDate,
        ifnull(oi.shelved_qty,0) as receiveQty,
        oi.create_by as receiver,
        oi.stockin_order_id as receivingId,
        oi.stockin_order_item_id as receivingItemId,
        ifnull(oi.return_qty,0) as returnQty,
        ti.sku as sku,
        oi.internal_box_code
        from stockin_order_task_item ti
        inner join stockin_order_task t
        on ti.task_id = t.task_id
        inner join stockin_order o
        on o.task_id = t.task_id
        inner join stock_platform_schedule ps
        on ps.platform_schedule_id = t.platform_schedule_id
        left join stockin_order_item oi
        on oi.task_item_id = ti.task_item_id

        where o.stockin_order_id = #{stockinOrderId}

    </select>
</mapper>
