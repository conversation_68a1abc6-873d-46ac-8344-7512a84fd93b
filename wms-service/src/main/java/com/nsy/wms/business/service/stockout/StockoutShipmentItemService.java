package com.nsy.wms.business.service.stockout;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.domain.stock.StockInternalBoxOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTask;
import com.nsy.api.wms.domain.stockout.StockoutShipmentBarcodeSku;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentModel;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockoutScanTaskDetailEnum;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ReplenishOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemOpListRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemOpRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemSearchByStockoutOrderRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemSearchRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderBoxPositionResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stock.StockTransferTrackingService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper;
import com.nsy.wms.utils.BarCodeUtils;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 装箱清单明细
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockoutShipmentItemService extends ServiceImpl<StockoutShipmentItemMapper, StockoutShipmentItemEntity> {
    @Autowired
    private ProductSpecInfoService specInfoService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    private StockoutShipmentService stockoutShipmentService;
    @Autowired
    private StockoutShipmentAmazonRelationService amazonRelationService;
    @Autowired
    PrintTemplateService printTemplateService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    BdPositionService positionService;
    @Autowired
    StockService stockService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutShipmentErpPickingBoxService shipmentErpPickingBoxService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    StockoutOrderScanTaskItemService scanTaskItemService;
    @Autowired
    StockoutBatchSplitTaskService splitTaskService;
    @Autowired
    StockoutBatchSplitTaskItemService splitTaskItemService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutOrderShipService stockoutOrderShipService;
    @Autowired
    MessageProducer producer;
    @Autowired
    ApplicationContext context;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockoutShipmentPackTaskService packTaskService;
    @Autowired
    StockoutShipmentPasteTaskService pasteTaskService;
    @Autowired
    StockTransferTrackingService transferTrackingService;


    public PageResponse<StockoutShipmentItemInfo> searchList(StockoutShipmentItemSearchRequest request) {
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
        StockoutShipmentEntity stockoutShipmentEntity = stockoutShipmentService.list(wrapper.eq(StockoutShipmentEntity::getShipmentBoxCode, request.getShipmentBoxCode())).get(0);
        PageResponse<StockoutShipmentItemInfo> pageResponse = new PageResponse<>();
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getShipmentId, stockoutShipmentEntity.getShipmentId());
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        Page<StockoutShipmentItemEntity> page = page(new Page<>(request.getPageIndex(), request.getPageSize()), queryWrapper);
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(page.getRecords().stream().map(item -> {
            StockoutShipmentItemInfo itemInfo = new StockoutShipmentItemInfo();
            BeanUtilsEx.copyProperties(item, itemInfo);
            if (item.getSpecId() == null) {
                return itemInfo;
            }
            StockoutOrderItemEntity one = stockoutOrderItemService.getById(item.getStockoutOrderItemId());
            if (one != null) {
                itemInfo.setSellerBarcode(one.getSellerBarcode());
                itemInfo.setSellerSku(one.getSellerSku());
            }
            ProductSpecInfoEntity specInfo = specInfoService.findTopBySpecId(item.getSpecId());
            itemInfo.setBarcode(specInfo.getBarcode());
            itemInfo.setProductName(productInfoService.findTopByProductId(specInfo.getProductId()).getProductName());
            return itemInfo;
        }).collect(Collectors.toList()));
        return pageResponse;
    }


    // 查找装箱清单中的orders，注意可能为null
    public List<String> getOrderNosByShipmentIds(List<Integer> shipmentIds) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentItemEntity::getShipmentId, shipmentIds);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList());
    }

    // 查找装箱清单中的orders，注意可能为null
    public List<String> getLogisticsNoByOrderNos(List<String> orderNoList) {
        return this.baseMapper.getLogisticsNoByOrderNos(orderNoList);
    }

    public List<StockoutShipmentItemEntity> findByShipmentId(Integer shipmentId) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getShipmentId, shipmentId);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public StockoutShipmentItemEntity findByShipmentIdAndStockoutOrderItemId(Integer shipmentId, Integer stockoutOrderItemId) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getShipmentId, shipmentId);
        queryWrapper.eq(StockoutShipmentItemEntity::getStockoutOrderItemId, stockoutOrderItemId);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0)
            .last("limit 1");
        return getOne(queryWrapper);
    }

    // 只有同步erp装箱清单才能调用此方法(查出包括被删除的sku)
    public List<StockoutShipmentItemEntity> findAllByShipmentId(Integer shipmentId) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getShipmentId, shipmentId);
        return list(queryWrapper);
    }

    public List<StockoutShipmentItemEntity> findByShipmentIdOrderByCreateTime(Integer shipmentId) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getShipmentId, shipmentId);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        queryWrapper.orderByAsc(StockoutShipmentItemEntity::getUpdateDate);
        return list(queryWrapper);
    }

    public List<StockoutShipmentItemEntity> findByShipmentIdsOrderByCreateTime(List<Integer> shipmentIdList) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentItemEntity::getShipmentId, shipmentIdList);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        queryWrapper.orderByAsc(StockoutShipmentItemEntity::getUpdateDate);
        return list(queryWrapper);
    }

    public List<StockoutShipmentItemEntity> findByShipmentIdList(List<Integer> shipmentIdList) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentItemEntity::getShipmentId, shipmentIdList);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public StockoutShipmentItemEntity findTopForUpdateTransfer(Integer shipmentId, String sku, String orderNo, String stockoutOrderNo, Integer stockoutOrderItemId) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        queryWrapper.eq(StockoutShipmentItemEntity::getShipmentId, shipmentId);
        queryWrapper.eq(StockoutShipmentItemEntity::getSku, sku);
        queryWrapper.eq(StockoutShipmentItemEntity::getOrderNo, orderNo);
        queryWrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo);
        queryWrapper.eq(stockoutOrderItemId != null, StockoutShipmentItemEntity::getStockoutOrderItemId, stockoutOrderItemId);
        return this.getOne(queryWrapper);
    }

    public List<Integer> getShipmentIdByOrderNos(String orderNo) {
        List<StockoutShipmentItemEntity> list = list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
            .eq(StockoutShipmentItemEntity::getOrderNo, orderNo).eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList());
    }

    public List<String> getStockoutOrderNoByShipmentId(Integer shipmentId) {
        List<StockoutShipmentItemEntity> list = list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
            .select(StockoutShipmentItemEntity::getStockoutOrderNo)
            .eq(StockoutShipmentItemEntity::getShipmentId, shipmentId)
            .eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
    }

    public List<StockoutShipmentEntity> getShipmentByStockoutOrderNo(String stockoutOrderNo) {
        List<StockoutShipmentItemEntity> list = list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
            .eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo).eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return stockoutShipmentService.listByIds(list.stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList()));
    }

    /**
     * 装箱打印条码：双排打印，若总数量为奇数，最后一个放左边，右边
     */
    public PrintListResponse printBarcode(IdListRequest idListRequest) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.STOCKOUT_SHIPMENT_BARCODE.getTemplateName());
        List<StockoutShipmentItemEntity> shipmentItemEntityList = this.findByShipmentIdList(idListRequest.getIdList());
        if (CollectionUtils.isEmpty(shipmentItemEntityList)) {
            return response;
        }
        // 获取所有的sku
        List<String> list = new ArrayList<>();
        for (StockoutShipmentItemEntity shipmentItemEntity : shipmentItemEntityList) {
            ProductSpecInfoEntity specInfoEntity = specInfoService.findTopBySku(shipmentItemEntity.getSku());
            if (Objects.isNull(specInfoEntity)) {
                throw new BusinessServiceException(String.format("找不到【%s】的商品信息", shipmentItemEntity.getSku()));
            }
            int size = shipmentItemEntity.getQty();
            StockoutShipmentBarcodeSku barcodeSku = new StockoutShipmentBarcodeSku();
            BeanUtilsEx.copyProperties(specInfoEntity, barcodeSku);
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), barcodeSku);
            for (int i = 0; i < size; i++) {
                list.add(transfer);
            }
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(Boolean.FALSE, list, templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 打印B2B条码
     */
    public PrintListResponse printB2bBarcode(IdListRequest idListRequest) {
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.STOCKOUT_SHIPMENT_B2B_BARCODE.getTemplateName());
        List<StockoutShipmentItemEntity> allShipmentItemEntityList = this.findByShipmentIdList(idListRequest.getIdList());
        if (CollectionUtils.isEmpty(allShipmentItemEntityList))
            throw new BusinessServiceException("装箱记录不存在");
        List<String> htmlList = new ArrayList<>();
        PrintListResponse response = new PrintListResponse();
        allShipmentItemEntityList.forEach(shipmentItemEntity -> {
            StockoutOrderItemEntity item = stockoutOrderItemService.getById(shipmentItemEntity.getStockoutOrderItemId());
            if (item == null) {
                throw new BusinessServiceException("找不到出库单明细");
            }
            if (!org.springframework.util.StringUtils.hasText(item.getSellerBarcode())) {
                response.setErrorPrintMsg(response.getErrorPrintMsg() + item.getSku() + "此sku未导入业务条码;");
                return;
            }
            String transfer = StockoutBuilding.buildFbaHtml(item, templateEntity);
            Map<String, String> map = new HashMap<>();
            ProductSpecInfoEntity productSpecInfoEntity = specInfoService.findTopBySpecId(shipmentItemEntity.getSpecId());
            map.put("size", productSpecInfoEntity.getSize());
            if (StringUtils.hasText(item.getSellerSku())) {
                ProductSpecInfoEntity sellerSkuInfo = specInfoService.findTopBySku(item.getSellerSku());
                if (sellerSkuInfo != null) {
                    map.put("sellSkc", sellerSkuInfo.getSkc());
                    map.put("sellSize", sellerSkuInfo.getSize());
                }
            }
            if (StringUtils.hasText(item.getSellerBarcode())) {
                map.put("sellBarcodeImg", BarCodeUtils.getBarCodeBase64(item.getSellerBarcode()));
            }
            transfer = PrintTransferUtils.transfer(transfer, map);
            for (int i = 0; i < shipmentItemEntity.getQty(); i++) {
                htmlList.add(transfer);
            }
        });
        response.setHtmlList(htmlList);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public void getLastShipmentBoxCode(StockoutOrderEntity stockoutOrderEntity, StockoutOrderScanTask scanTask) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderEntity.getStockoutOrderNo());
        wrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        wrapper.orderByDesc(StockoutShipmentItemEntity::getCreateDate);
        List<StockoutShipmentItemEntity> itemList = list(wrapper);
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        StockoutShipmentEntity one = stockoutShipmentService.getById(itemList.get(0).getShipmentId());
        if (one == null || !StockoutShipmentStatusEnum.PACKING.name().equals(one.getStatus())) {
            return;
        }
        if (this.count(new QueryWrapper<StockoutShipmentItemEntity>().lambda().eq(StockoutShipmentItemEntity::getShipmentId, one.getShipmentId())) > 0)
            scanTask.setIsEmpty(0);
        else
            scanTask.setIsEmpty(1);
        scanTask.setBoxCode(one.getShipmentBoxCode());
    }

    /**
     * 装箱完成后 部分商品取消发货
     * 1.仅允许状态为装箱中、装箱完成的装箱清单进行修改
     * 2.sku发货库位-  撤货箱+
     * 3.更新装箱清单明细 记录删除操作日志
     * 4.更新复核|分拣任务（无复核任务）的已扫描数
     * 5.出库单明细不做更新 但记录删除的操作日志
     * 6.校验当前箱子内是否还有sku 若无 更新箱子状态为装箱中
     * 7.同步erp装箱清单
     * 8.装箱完成的撤货，需要重新调用拣货单完成
     */
    @Transactional
    public void deleteShipmentItemAndSyncErp(StockoutShipmentItemOpRequest request) {
        context.getBean(this.getClass()).deleteShipmentItemLock(request);
        StockoutShipmentItemEntity itemEntity = this.getById(request.getShipmentItemId());
        List<String> pushOrderNoList = transferTrackingService.getPushOrderNo(Collections.singletonList(itemEntity.getOrderNo()));
        if (!CollectionUtils.isEmpty(pushOrderNoList)) {
            throw new BusinessServiceException(String.format("当前订单【%s】已推送海外仓无法撤货", String.join(",", pushOrderNoList)));
        }
        StockoutShipmentEntity shipmentEntity = stockoutShipmentService.getById(itemEntity.getShipmentId());
        if (shipmentEntity.getShipmentBoxCode().startsWith("Pack-"))
            throw new BusinessServiceException("Pack箱子无法调拨");
        // 7.同步erp装箱清单
        shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(shipmentEntity);
        // 8.装箱完成的撤货，需要重新调用拣货单完成
        if (StockoutShipmentStatusEnum.PACKING_END.name().equals(shipmentEntity.getStatus())) {
            stockoutOrderShipService.finishErpPartialPick(Collections.singletonList(itemEntity.getStockoutOrderNo()));
        }
    }

    @JLock(keyConstant = "deleteShipmentItem", lockKey = "#request.shipmentItemId")
    public void deleteShipmentItemLock(StockoutShipmentItemOpRequest request) {
        context.getBean(this.getClass()).deleteShipmentItem(request);
    }

    /**
     * 装箱完成后 部分商品取消发货
     * 1.仅允许状态为装箱中、装箱完成的装箱清单进行修改
     * 2.sku发货库位-  撤货箱+
     * 3.更新装箱清单明细 记录删除操作日志
     * 4.更新复核|分拣任务（无复核任务）的已扫描数
     * 5.出库单明细不做更新 但记录删除的操作日志
     * 6.校验当前箱子内是否还有sku 若无 更新箱子状态为装箱中
     */
    @Transactional
    public void deleteShipmentItem(StockoutShipmentItemOpRequest request) {
        StockoutShipmentItemEntity itemEntity = this.getById(request.getShipmentItemId());
        if (itemEntity == null || itemEntity.getIsDeleted().equals(1))
            throw new BusinessServiceException("未找到当前装箱清单明细记录，请确认！");
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(itemEntity.getStockoutOrderNo());
        StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(itemEntity.getStockoutOrderItemId());
        // 1.仅允许状态为装箱中、装箱完成的装箱清单进行修改
        StockoutShipmentEntity shipmentEntity = stockoutShipmentService.getById(itemEntity.getShipmentId());
        if (StockoutOrderStatusEnum.READY_DELIVERY.getIndex() > StockoutOrderStatusEnum.valueOf(stockoutOrderEntity.getStatus()).getIndex())
            throw new BusinessServiceException("请先完成分拣、复核任务！");
        if (!StockoutShipmentStatusEnum.PACKING.name().equals(shipmentEntity.getStatus()) && !StockoutShipmentStatusEnum.PACKING_END.name().equals(shipmentEntity.getStatus()))
            throw new BusinessServiceException("仅允许状态为装箱中、装箱完成的装箱清单进行修改");
        if (ReplenishOrderStatusEnum.DEAL.name().equalsIgnoreCase(shipmentEntity.getReplenishOrderStatus()))
            throw new BusinessServiceException(String.format("箱子%s已生成FBA补货单，无法操作", shipmentEntity.getShipmentBoxCode()));
        // 先分仓后装箱模式，装箱清单申请完箱贴允许撤货
        if (StrUtil.equalsAny(shipmentEntity.getFbaLabelStatus(), FbaLabelStatusEnum.APPLYING.name(), FbaLabelStatusEnum.COMPLETE.name())
            && !FbaReplenishTypeEnum.SPLIT_SHIPMENT.getName().equals(shipmentEntity.getFbaReplenishType()))
            throw new BusinessServiceException(String.format("箱子%s已经申请箱贴，无法操作", shipmentEntity.getShipmentBoxCode()));
        // 2.sku发货库位-  撤货箱+ (复核完成)
        updateShippingPositionStock(stockoutOrderEntity, itemEntity.getSku(), request.getQty());
        updateWithDrawalBoxItem(stockoutOrderItemEntity, stockoutOrderEntity, itemEntity, request.getQty());
        // 3.更新装箱清单明细
        if (request.getQty() > itemEntity.getQty())
            throw new BusinessServiceException("装箱清单明细数量不足扣减，请确认！");
        itemEntity.setQty(itemEntity.getQty() - request.getQty());
        if (itemEntity.getQty().equals(0))
            itemEntity.setIsDeleted(1);
        itemEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(itemEntity);
        producer.sendMessage(KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME, KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC, Key.of(itemEntity.getSku()),
            new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), StockoutBuilding.detailMessage(itemEntity, -request.getQty(), shipmentEntity.getShipmentBoxCode(), StockoutScanTaskDetailEnum.WITHDRAW.name())));
        // 4.更新复核|分拣任务（无复核任务）的已扫描数
        updateScanQty(itemEntity, request.getQty());
        // 5.出库单明细不做更新 更新发货数 记录删除的操作日志
        String content = String.format("装箱清单【%s】，【%s】撤货【%s】件", shipmentEntity.getShipmentBoxCode(), itemEntity.getSku(), request.getQty());
        stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.SHIPMENT_WITHDRAWAL, content);
        if (stockoutOrderItemEntity.getShipmentQty() >= request.getQty()) {
            stockoutOrderItemEntity.setScanQty(stockoutOrderItemEntity.getScanQty() - request.getQty());
            stockoutOrderItemEntity.setShipmentQty(stockoutOrderItemEntity.getShipmentQty() - request.getQty());
            stockoutOrderItemEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderItemService.updateById(stockoutOrderItemEntity);
        }
        // 6.校验当前箱子内是否还有sku 若无 更新箱子为已删除
        Integer itemCount = this.count(new QueryWrapper<StockoutShipmentItemEntity>().lambda().eq(StockoutShipmentItemEntity::getShipmentId, shipmentEntity.getShipmentId())
            .eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (itemCount.equals(0)) {
            shipmentEntity.setIsDeleted(1);
            shipmentEntity.setUpdateBy(loginInfoService.getName());
            stockoutShipmentService.updateById(shipmentEntity);
            amazonRelationService.remove(new LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity>()
                    .eq(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentEntity.getShipmentId()));
        }
        //更新打包任务贴码任务
        packTaskService.updateQty(shipmentEntity.getShipmentId());
        pasteTaskService.updateQty(shipmentEntity.getShipmentId());
    }

    /**
     * 更新发货库位库存【复核未完成进行撤货，需要扣除拣货箱库存】
     */
    private void updateShippingPositionStock(StockoutOrderEntity stockoutOrderEntity, String sku, Integer qty) {
        //        StockoutOrderScanTaskEntity scanTaskEntity = scanTaskService.getByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        //        if (scanTaskEntity != null && !scanTaskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.REVIEWED.name())) {
        //            StockoutBatchOrderEntity batchOrderEntity = batchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        //            if (batchOrderEntity == null)
        //                throw new BusinessServiceException(String.format("出库单【%s】未找到波次", stockoutOrderEntity.getStockoutOrderNo()));
        //            // 拣货箱明细
        //            List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
        //                    .eq(StockInternalBoxItemEntity::getBatchId, batchOrderEntity.getBatchId()));
        //            StockInternalBoxItemEntity boxItemEntity = internalBoxItemEntityList.stream().filter(o -> o.getSku().equals(sku)).findFirst().orElse(null);
        //            if (boxItemEntity != null) {
        //                stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItemEntity, qty, StockChangeLogTypeEnum.STOCKOUT_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
        //                return;
        //            }
        //        }

        BdPositionEntity position = positionService.getDeliverPosition(stockoutOrderEntity.getSpaceId());
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(sku);
        stockUpdateRequest.setPositionCode(position.getPositionCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.STOCKOUT_WITHDRAWAL);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setQty(-qty);
        stockUpdateRequest.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        stockService.updateStock(stockUpdateRequest);
    }

    /**
     * 更新撤货箱库存
     */
    private void updateWithDrawalBoxItem(StockoutOrderItemEntity stockoutOrderItemEntity, StockoutOrderEntity stockoutOrderEntity, StockoutShipmentItemEntity itemEntity, Integer qty) {
        // 根据工作区域+内部箱类型查询撤货箱
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getWithdrawalByWorkspace(stockoutOrderEntity.getWorkspace(), stockoutOrderEntity.getSpaceId());
        // 撤货箱状态改为装箱中
        if (!stockInternalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.PACKING.name())) {
            stockInternalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.PACKING.name());
        }
        ProductSpecInfoEntity productSpecInfoEntity = specInfoService.findTopBySku(itemEntity.getSku());
        if (productSpecInfoEntity == null)
            throw new BusinessServiceException("商品条码不存在");
        LambdaQueryWrapper<StockInternalBoxItemEntity> stockInternalBoxItemEntityLambdaQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
            .eq(StockInternalBoxItemEntity::getInternalBoxCode, stockInternalBoxEntity.getInternalBoxCode())
            .eq(StockInternalBoxItemEntity::getSku, itemEntity.getSku())
            .eq(StockInternalBoxItemEntity::getSourcePositionCode, stockoutOrderItemEntity.getPositionCode())
            .eq(StockInternalBoxItemEntity::getBatchId, itemEntity.getBatchId());
        StockInternalBoxItemEntity stockInternalBoxItemEntity = stockInternalBoxItemService.getOne(stockInternalBoxItemEntityLambdaQueryWrapper);
        Integer areaId = positionService.getPositionByCode(stockoutOrderItemEntity.getPositionCode()).getAreaId();
        if (stockInternalBoxItemEntity == null) {
            stockInternalBoxItemEntity = StockoutBuilding.buildBoxItem(stockoutOrderEntity.getSpaceId(), stockInternalBoxEntity, productSpecInfoEntity, itemEntity.getBatchId());
            stockInternalBoxItemEntity.setSourcePositionCode(stockoutOrderItemEntity.getPositionCode());
            stockInternalBoxItemEntity.setSourceAreaId(areaId);
        }
        // 撤货箱明细增加
        stockInternalBoxItemService.addStockInternalBoxItemQty(stockInternalBoxItemEntity, qty, StockChangeLogTypeEnum.STOCKOUT_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, new StockInternalBoxOrderInfo(areaId, stockoutOrderItemEntity.getPositionCode()));
    }

    /**
     * 更新复核|分拣任务（无复核任务）的已扫描数
     */
    private void updateScanQty(StockoutShipmentItemEntity shipmentItemEntity, Integer qty) {
        StockoutOrderScanTaskEntity scanTaskEntity = scanTaskService.getByStockoutOrderNo(shipmentItemEntity.getStockoutOrderNo());
        if (scanTaskEntity != null) {
            StockoutOrderScanTaskItemEntity scanTaskItemEntity = scanTaskItemService.getListByTaskId(scanTaskEntity.getTaskId()).stream()
                .filter(o -> o.getOrderItemId().equals(shipmentItemEntity.getOrderItemId()) && o.getScanQty() > 0)
                .findFirst().orElse(null);
            if (scanTaskItemEntity != null) {
                scanTaskItemEntity.setScanQty(scanTaskItemEntity.getScanQty() - qty);
                scanTaskItemEntity.setShipmentQty(scanTaskItemEntity.getShipmentQty() - qty);
                scanTaskItemEntity.setUpdateBy(loginInfoService.getName());
                scanTaskItemEntity.setLackQty(scanTaskItemEntity.getLackQty() + qty);
                scanTaskItemEntity.setIsLack(1);
                scanTaskItemService.updateById(scanTaskItemEntity);
            }
        } else {
            StockoutBatchSplitTaskEntity splitTaskEntity = splitTaskService.getOne(new QueryWrapper<StockoutBatchSplitTaskEntity>().lambda()
                .eq(StockoutBatchSplitTaskEntity::getBatchId, shipmentItemEntity.getBatchId())
                .last(MybatisQueryConstant.QUERY_FIRST));
            StockoutBatchSplitTaskItemEntity splitTaskItemEntity = splitTaskItemService.getByTaskIdOrderByTaskId(splitTaskEntity.getTaskId())
                .stream().filter(o -> o.getStockoutOrderItemId() != null && o.getStockoutOrderItemId().equals(shipmentItemEntity.getStockoutOrderItemId()))
                .findFirst().orElse(null);
            if (splitTaskItemEntity != null) {
                splitTaskItemEntity.setScanQty(splitTaskItemEntity.getScanQty() - qty);
                splitTaskItemEntity.setUpdateBy(loginInfoService.getName());
                splitTaskItemEntity.setLackQty(splitTaskItemEntity.getLackQty() + qty);
                splitTaskItemEntity.setIsLack(1);
                splitTaskItemService.updateById(splitTaskItemEntity);
            }
        }
    }

    public List<StockoutShipmentModel> getShipmentsByBoxCode(IdListRequest request) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentItemEntity::getShipmentId, request.getIdList());
        wrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> itemList = list(wrapper);
        if (!CollectionUtils.isEmpty(itemList)) {
            List<String> collect = itemList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).collect(Collectors.toList());
            wrapper.clear();
            wrapper.in(StockoutShipmentItemEntity::getStockoutOrderNo, collect);
            List<StockoutShipmentItemEntity> itemNoList = list(wrapper);
            request.getIdList().addAll(itemNoList.stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList()));
        }
        List<StockoutShipmentEntity> shipmentEntityList = stockoutShipmentService.listByIds(request.getIdList());
        return shipmentEntityList.stream().map(shipment -> {
            StockoutShipmentModel model = new StockoutShipmentModel();
            BeanUtils.copyProperties(shipment, model);
            return model;
        }).collect(Collectors.toList());
    }

    public List<StockoutShipmentEntity> findByStockoutOrderNo(List<String> stockoutOrderNo) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo);
        wrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Integer> collect = list.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        return stockoutShipmentService.listByIds(collect);
    }

    public List<StockoutShipmentEntity> findByStockoutOrderNo(String stockoutOrderNo) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo);
        wrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Integer> collect = list.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        return stockoutShipmentService.listByIds(collect);
    }

    /**
     * 圆通公司统计重量
     *
     * <AUTHOR>
     * 2022-01-08
     */
    public void countWeight(String logisticsCompany, List<Integer> shipmentIds) {
        if (!LogisticsCompanyConstant.YUAN_TONG.equals(logisticsCompany)) {
            return;
        }
        // 重量有值的话，就不再去回填
        List<StockoutShipmentEntity> shipmentEntityList = stockoutShipmentService.listByIds(shipmentIds);
        String name = loginInfoService.getName();
        for (StockoutShipmentEntity shipment : shipmentEntityList) {
            // 有货代，自提
            if (shipment.getWeight() != null || StringUtils.hasText(shipment.getForwarderChannel())) {
                continue;
            }
            shipment.setWeight(BigDecimal.ZERO);
            List<StockoutShipmentItemEntity> itemList = findByShipmentId(shipment.getShipmentId());
            itemList.forEach(item -> {
                ProductSpecInfoEntity spec = specInfoService.findTopBySku(item.getSku());
                BigDecimal weight = spec.getActualWeight() == null ? spec.getWeight() == null ? BigDecimal.ZERO : spec.getWeight() : spec.getActualWeight();
                shipment.setWeight(shipment.getWeight().add(weight.multiply(new BigDecimal(item.getQty()))));
            });
            // 除1000 单位转换
            shipment.setWeight(shipment.getWeight().divide(new BigDecimal(1000), BigDecimal.ROUND_DOWN));
            shipment.setUpdateBy(name);
            stockoutShipmentService.updateById(shipment);
        }
    }

    public void validAllShipmentConfirm(String stockoutOrderNo, String boxCodeIn) {
        vaildShipmentStoreAndLogistics(stockoutOrderNo, boxCodeIn);

        List<StockoutShipmentEntity> shipments = getShipmentByStockoutOrderNo(stockoutOrderNo);
        if (CollectionUtils.isEmpty(shipments)) {
            return;
        }
        List<StockoutShipmentEntity> packingBox = shipments.stream().filter(shipment -> Objects.equals(StockoutShipmentStatusEnum.PACKING.name(), shipment.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(packingBox)) {
            String boxCode = packingBox.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.joining(","));
            // 如果输入的箱子是装箱中的, 则可以继续装箱
            if (!(StringUtils.hasText(boxCodeIn) && boxCode.contains(boxCodeIn))) {
                throw new BusinessServiceException("存在未完成的箱子,箱号[" + boxCode + "]");
            }
        }
    }

    public void vaildShipmentStoreAndLogistics(String stockoutOrderNo, String boxCodeIn) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);

        if (!StringUtils.hasText(boxCodeIn) || !StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(stockoutOrderEntity.getWorkspace()))
            return;

        StockoutShipmentEntity topByShipmentBoxCode = stockoutShipmentService.findTopByShipmentBoxCode(boxCodeIn);
        //判断FBA补货类型
        FbaReplenishTypeEnum fbaReplenishType = org.springframework.util.StringUtils.hasText(stockoutOrderEntity.getFbaReplenishType()) ? FbaReplenishTypeEnum.getByName(stockoutOrderEntity.getFbaReplenishType()) : FbaReplenishTypeEnum.NORMAL;
        FbaReplenishTypeEnum fbaReplenishTypeBox = org.springframework.util.StringUtils.hasText(topByShipmentBoxCode.getFbaReplenishType()) ? FbaReplenishTypeEnum.getByName(topByShipmentBoxCode.getFbaReplenishType()) : FbaReplenishTypeEnum.NORMAL;

        if (!fbaReplenishType.name().equals(fbaReplenishTypeBox.name()))
            throw new BusinessServiceException(String.format("【%s】的出库单不能装进【%s】的箱子", fbaReplenishType.getName(), fbaReplenishTypeBox.getName()));

        //标准件和大号件不能混装
        if (StringUtils.hasText(topByShipmentBoxCode.getProductSizeSegment())
            && StringUtils.hasText(stockoutOrderEntity.getProductSizeSegment())
            && !topByShipmentBoxCode.getProductSizeSegment().equals(stockoutOrderEntity.getProductSizeSegment()))
            throw new BusinessServiceException("标准件和大号件不能混装！");

        //宓思：相同店铺和相同物流公司的可以装在同一箱
        if (LocationEnum.MISI.name().equals(TenantContext.getTenant())) {
            List<String> stockoutOrderNos = this.getStockoutOrderNoByShipmentId(topByShipmentBoxCode.getShipmentId());
            StockoutOrderEntity stockoutOrderEntity2 = null;
            if (!CollectionUtils.isEmpty(stockoutOrderNos))
                stockoutOrderEntity2 = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNos.get(0));

            if (Objects.nonNull(stockoutOrderEntity2)
                && (!stockoutOrderEntity.getStoreId().equals(stockoutOrderEntity2.getStoreId())
                || !stockoutOrderEntity.getLogisticsCompany().equals(stockoutOrderEntity2.getLogisticsCompany())))
                throw new BusinessServiceException("不同店铺和物流公司的出库单不允许装在一起！");
        }
    }

    public List<StockoutShipmentItemEntity> findByOrderNo(String orderNo) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getOrderNo, orderNo);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public List<StockoutShipmentItemEntity> findByOrderNos(List<String> orderNoList) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentItemEntity::getOrderNo, orderNoList);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public List<StockoutShipmentItemEntity> findItemByStockoutOrderNo(String stockoutOrderNo) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public List<StockoutShipmentItemEntity> findItemByStockoutOrderNo(List<String> stockoutOrderNo) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo);
        queryWrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    /**
     * 按出库单撤货-装箱清单明细
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutShipmentItemDetail> searchListByStockoutOrder(StockoutShipmentItemSearchByStockoutOrderRequest request) {

        PageResponse<StockoutShipmentItemDetail> pageResponse = new PageResponse<>();
        if (!StringUtils.hasText(request.getStockoutOrderNo())) {
            throw new BusinessServiceException("出库单号不能为空");
        }
        IPage<StockoutShipmentItemDetail> page = this.baseMapper.searchListByStockoutOrder(new Page(request.getPageIndex(),
            request.getPageSize()), request);
        pageResponse.setContent(page.getRecords());
        pageResponse.setTotalCount(page.getTotal());

        return pageResponse;
    }

    /**
     * 装箱完成后 部分商品取消发货 (批量)
     * 1.仅允许状态为装箱中、装箱完成的装箱清单进行修改
     * 2.sku发货库位-  撤货箱+
     * 3.更新装箱清单明细 记录删除操作日志
     * 4.更新复核|分拣任务（无复核任务）的已扫描数
     * 5.出库单明细不做更新 但记录删除的操作日志
     * 6.校验当前箱子内是否还有sku 若无 更新箱子状态为装箱中
     * 7.同步erp装箱清单
     * 8.装箱完成的撤货，需要重新调用拣货单完成
     */
    @Transactional
    public void batchDeleteShipmentItem(StockoutShipmentItemOpListRequest request) {
        if (request.getItemOpList().isEmpty())
            return;

        //1-6 撤货
        for (StockoutShipmentItemOpRequest itemOpRequest : request.getItemOpList()) {
            context.getBean(this.getClass()).deleteShipmentItemLock(itemOpRequest);
        }

        // 7.同步erp装箱清单
        List<Integer> shipmentItemIdList = request.getItemOpList().stream().map(StockoutShipmentItemOpRequest::getShipmentItemId).collect(Collectors.toList());
        List<StockoutShipmentItemEntity> stockoutShipmentItemList = shipmentItemService.listByIds(shipmentItemIdList);
        List<String> pushOrderNoList = transferTrackingService.getPushOrderNo(stockoutShipmentItemList.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(pushOrderNoList)) {
            throw new BusinessServiceException(String.format("当前订单【%s】已推送海外仓无法撤货", String.join(",", pushOrderNoList)));
        }
        List<Integer> shipmentIdList = stockoutShipmentItemList.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        if (shipmentIdList.isEmpty())
            return;
        List<StockoutShipmentEntity> stockoutShipmentList = stockoutShipmentService.listByIds(shipmentIdList);
        if (stockoutShipmentList.stream().anyMatch(it -> it.getShipmentBoxCode().startsWith("Pack-"))) {
            throw new BusinessServiceException("包含Pack箱子，无法撤货");
        }
        Map<Integer, List<StockoutShipmentItemEntity>> shipmentItemMap = stockoutShipmentItemList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId));
        shipmentItemMap.entrySet().forEach(entry -> {
            int shipmentId = entry.getKey();
            StockoutShipmentEntity stockoutShipment = stockoutShipmentList.stream().filter(temp -> shipmentId == temp.getShipmentId()).findFirst().orElse(null);
            shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(stockoutShipment);
        });
        // 8.装箱完成的撤货，需要重新调用拣货单完成
        List<Integer> packedShipmentIdList = stockoutShipmentList.stream().filter(o -> StockoutShipmentStatusEnum.PACKING_END.name().equals(o.getStatus()))
            .map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());
        List<String> stockoutOrderNos = stockoutShipmentItemList.stream().filter(o -> packedShipmentIdList.contains(o.getShipmentId())).map(StockoutShipmentItemEntity::getStockoutOrderNo)
            .distinct().collect(Collectors.toList());
        stockoutOrderShipService.finishErpPartialPick(stockoutOrderNos);
    }

    /**
     * 根据出库单号判断装箱清单是否已发货
     *
     * @param stockoutOrderNo
     * @return
     */
    public Boolean validStockoutOrderShipment(String stockoutOrderNo) {
        return this.baseMapper.getShippedCountByStockoutOrderNo(stockoutOrderNo) > 0;
    }

    /**
     * 装箱清单根据区域分组,拼接成 "装箱清单号-erp区域id"格式，如果再转换表找不到对应erp区域则直接返回装箱清单编号
     *
     * @param shipmentBoxCode
     * @return
     */
    public List<String> groupByWithSpace(String shipmentBoxCode) {
        List<String> shipmentBySpaceList = this.baseMapper.groupByWithSpace(shipmentBoxCode);
        if (CollectionUtils.isEmpty(shipmentBySpaceList)) {
            return Collections.singletonList(shipmentBoxCode);
        }
        return shipmentBySpaceList;
    }

    public List<Integer> getShipmentErpSpace(String shipmentBoxCode) {
        List<Integer> shipmentErpSpaceList = this.baseMapper.getShipmentErpSpace(shipmentBoxCode);
        if (CollectionUtils.isEmpty(shipmentErpSpaceList)) {
            return Collections.emptyList();
        }
        return shipmentErpSpaceList;
    }

    /**
     * 查找指定区域的装箱清单信息
     *
     * @param shipmentId
     * @param erpSpaceId
     * @return
     */
    public List<StockoutShipmentItemEntity> findByShipmentIdAndErpSpaceId(Integer shipmentId, Integer erpSpaceId) {
        return this.baseMapper.findByShipmentIdAndErpSpaceId(shipmentId, erpSpaceId);
    }

    public Boolean validStockoutType(Integer shipmentId, StockoutPickingTypeEnum pickingTypeEnum) {
        List<String> pickingTypeList = this.baseMapper.getPickTypeByShipmentId(shipmentId);
        if (!CollectionUtils.isEmpty(pickingTypeList) && pickingTypeEnum.name().equals(pickingTypeList.get(0))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public List<String> getSkuByShipmentId(Integer shipmentId) {
        return this.baseMapper.getSkuByShipmentId(shipmentId);
    }

    public List<String> getStockoutOrderNoByShipmentBoxCode(List<String> shipmentBoxCodeList) {
        return this.baseMapper.getStocktoutOrderByShipment(shipmentBoxCodeList);
    }

    /**
     * 获取当前箱数
     *
     * @param stockoutOrderNo
     * @return
     */
    public Integer getShipmentCountByStockoutOrderNo(String stockoutOrderNo) {
        return this.baseMapper.getShipmentCountByStockoutOrderNo(stockoutOrderNo);
    }

    public Integer sumQty(Integer shipmentId) {
        List<StockoutShipmentItemEntity> shipmentItemList = findByShipmentId(shipmentId);
        if (CollectionUtils.isEmpty(shipmentItemList)) {
            throw new BusinessServiceException(String.format("装箱明细为空 %s", shipmentId));
        }

        return shipmentItemList.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum();
    }

    /**
     * 获取箱子位置信息
     *
     * @param orderNo
     * @return
     */
    public StockoutOrderBoxPositionResponse getOrderInfoByOrderNo(String orderNo) {
        StockoutOrderBoxPositionResponse positionInfo = this.getBaseMapper().getOrderInfoByOrderNo(orderNo);
        if (Objects.isNull(positionInfo)) {
            throw new BusinessServiceException("查询不到订单数据!");
        }
        return positionInfo;
    }

    public StockoutShipmentItemEntity findTopByShipmentIdAndSku(Integer shipmentId, String sku) {
        LambdaQueryWrapper<StockoutShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentItemEntity::getShipmentId, shipmentId);
        queryWrapper.eq(StockoutShipmentItemEntity::getSku, sku).last("limit 1");
        return getOne(queryWrapper);
    }

    /**
     * 校验每个箱子，其装箱内容（每个sku及其对应的qty）必须以5的倍数存在相同的箱子
     *
     * <AUTHOR>
     * 2025-05-29
     */
    public void vaildShipmentByOrderNos(String orderNo) {
        List<StockoutShipmentItemEntity> list = list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
                .eq(StockoutShipmentItemEntity::getOrderNo, orderNo).eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("订单号" + orderNo + "找不到对应的装箱清单记录");
        }

        List<Integer> shipmentIds = list.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = stockoutShipmentService.listByIds(shipmentIds);

        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("订单号" + orderNo + "找不到对应的装箱清单");
        }

        // 按装箱ID分组装箱明细
        Map<Integer, List<StockoutShipmentItemEntity>> shipmentItemMap = list.stream()
                .collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId));

        // 创建装箱内容签名到箱子数量的映射
        Map<String, Integer> contentSignatureCountMap = new HashMap<>();

        // 为每个箱子生成内容签名
        for (StockoutShipmentEntity shipment : shipmentList) {
            List<StockoutShipmentItemEntity> items = shipmentItemMap.get(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            // 按SKU聚合数量，然后生成装箱内容签名
            Map<String, Integer> skuQtyMap = items.stream()
                    .collect(Collectors.groupingBy(
                            StockoutShipmentItemEntity::getSku,
                            Collectors.summingInt(StockoutShipmentItemEntity::getQty)
                    ));

            // 按sku排序后拼接sku:总数量生成签名
            String contentSignature = skuQtyMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> entry.getKey() + ":" + entry.getValue())
                    .collect(Collectors.joining(","));

            // 统计相同内容签名的箱子数量
            contentSignatureCountMap.merge(contentSignature, 1, Integer::sum);
        }

        // 校验每种内容签名的箱子数量必须是5的倍数
        for (Map.Entry<String, Integer> entry : contentSignatureCountMap.entrySet()) {
            String contentSignature = entry.getKey();
            Integer count = entry.getValue();

            if (count % 5 != 0) {
                throw new BusinessServiceException(String.format("订单号%s的装箱内容[%s]的箱子数量为%d，不是5的倍数，请检查装箱配置",
                        orderNo, contentSignature, count));
            }
        }
    }
}
