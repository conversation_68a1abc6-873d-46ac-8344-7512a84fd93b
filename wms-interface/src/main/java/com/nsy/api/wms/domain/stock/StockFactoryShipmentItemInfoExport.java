package com.nsy.api.wms.domain.stock;

import com.nsy.api.core.apicore.annotation.NsyExcelProperty;

public class StockFactoryShipmentItemInfoExport {
    @NsyExcelProperty(value = "工厂出库单号", index = 0)
    private String supplierDeliveryNo;
    @NsyExcelProperty(value = "供应商编码", index = 1)
    private String supplierName;
    @NsyExcelProperty(value = "仓库", index = 2)
    private String spaceName;
    @NsyExcelProperty(value = "下单采购员", index = 3)
    private String purchaseName;
    @NsyExcelProperty(value = "跟单员", index = 4)
    private String supplierBuyerName;
    @NsyExcelProperty(value = "跟单小组", index = 5)
    private String departmentName;
    @NsyExcelProperty(value = "生产部门", index = 6)
    private String parentDepartmentName;
    @NsyExcelProperty(value = "出库单状态", index = 7)
    private String stockinStatusStr;
    @NsyExcelProperty(value = "发货日期", index = 8)
    private String deliveryDate;
    @NsyExcelProperty(value = "合同货期", index = 9)
    private String originExpectDeliveryDate;
    @NsyExcelProperty(value = "到仓时间", index = 10)
    private String auditDate;
    @NsyExcelProperty(value = "商品编码", index = 11)
    private String spu;
    @NsyExcelProperty(value = "颜色编码", index = 12)
    private String skc;
    @NsyExcelProperty(value = "规格编码", index = 13)
    private String sku;
    @NsyExcelProperty(value = "sellerSku", index = 14)
    private String sellerSku;
    @NsyExcelProperty(value = "出库箱码", index = 15)
    private String supplierDeliveryBoxCode;
    @NsyExcelProperty(value = "采购单号", index = 16)
    private String purchaseNo;
    @NsyExcelProperty(value = "下单数", index = 17)
    private Integer purchaseQty;
    @NsyExcelProperty(value = "可裁数", index = 18)
    private Integer activeQty;
    @NsyExcelProperty(value = "待发货数", index = 19)
    private Integer unDeliveryQty;
    @NsyExcelProperty(value = "出库件数", index = 20)
    private Integer shipmentQty;
    @NsyExcelProperty(value = "收货件数", index = 21)
    private Integer receiveQty;
    @NsyExcelProperty(value = "待收货件数", index = 22)
    private Integer pendingReceiveQty;
    @NsyExcelProperty(value = "需退货数", index = 23)
    private Integer returnQty;
    @NsyExcelProperty(value = "上架件数", index = 24)
    private Integer shelvedQty;
    @NsyExcelProperty(value = "直发备注", index = 25)
    private String remarks;
    @NsyExcelProperty(value = "明细状态", index = 26)
    private String itemStatusStr;
    @NsyExcelProperty(value = "创建时间", index = 27)
    private String createDate;
    @NsyExcelProperty(value = "更新时间", index = 28)
    private String updateDate;


    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public Integer getShipmentQty() {
        return shipmentQty;
    }

    public void setShipmentQty(Integer shipmentQty) {
        this.shipmentQty = shipmentQty;
    }

    public Integer getReceiveQty() {
        return receiveQty;
    }

    public void setReceiveQty(Integer receiveQty) {
        this.receiveQty = receiveQty;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public Integer getShelvedQty() {
        return shelvedQty;
    }

    public void setShelvedQty(Integer shelvedQty) {
        this.shelvedQty = shelvedQty;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getPurchaseName() {
        return purchaseName;
    }

    public void setPurchaseName(String purchaseName) {
        this.purchaseName = purchaseName;
    }

    public String getSupplierBuyerName() {
        return supplierBuyerName;
    }

    public void setSupplierBuyerName(String supplierBuyerName) {
        this.supplierBuyerName = supplierBuyerName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getParentDepartmentName() {
        return parentDepartmentName;
    }

    public void setParentDepartmentName(String parentDepartmentName) {
        this.parentDepartmentName = parentDepartmentName;
    }

    public Integer getPurchaseQty() {
        return purchaseQty;
    }

    public void setPurchaseQty(Integer purchaseQty) {
        this.purchaseQty = purchaseQty;
    }

    public Integer getActiveQty() {
        return activeQty;
    }

    public void setActiveQty(Integer activeQty) {
        this.activeQty = activeQty;
    }

    public Integer getUnDeliveryQty() {
        return unDeliveryQty;
    }

    public void setUnDeliveryQty(Integer unDeliveryQty) {
        this.unDeliveryQty = unDeliveryQty;
    }

    public String getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(String auditDate) {
        this.auditDate = auditDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getOriginExpectDeliveryDate() {
        return originExpectDeliveryDate;
    }

    public void setOriginExpectDeliveryDate(String originExpectDeliveryDate) {
        this.originExpectDeliveryDate = originExpectDeliveryDate;
    }

    public String getStockinStatusStr() {
        return stockinStatusStr;
    }

    public void setStockinStatusStr(String stockinStatusStr) {
        this.stockinStatusStr = stockinStatusStr;
    }

    public Integer getPendingReceiveQty() {
        return pendingReceiveQty;
    }

    public void setPendingReceiveQty(Integer pendingReceiveQty) {
        this.pendingReceiveQty = pendingReceiveQty;
    }

    public String getItemStatusStr() {
        return itemStatusStr;
    }

    public void setItemStatusStr(String itemStatusStr) {
        this.itemStatusStr = itemStatusStr;
    }
}
