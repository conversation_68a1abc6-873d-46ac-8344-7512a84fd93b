package com.nsy.wms.controller.product;

import com.nsy.wms.business.service.bd.BdTagService;
import com.nsy.wms.business.service.product.ProductCategoryService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.repository.entity.product.ProductCategoryEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller层
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "商品分类接口")
@RestController
public class ProductCategoryController extends BaseController {

    @Autowired
    ProductCategoryService productCategoryService;
    @Autowired
    BdTagService bdTagService;

    @ApiOperation(value = "所有父节点", produces = "application/json")
    @RequestMapping(value = "/product-category/parent-list", method = RequestMethod.GET)
    @ResponseBody
    public List<ProductCategoryEntity> findParentList(@RequestParam Integer categoryId) {
        return productCategoryService.findAllParentList(categoryId);
    }

    @ApiOperation(value = "所有子节点", produces = "application/json")
    @RequestMapping(value = "/product-category/son-list", method = RequestMethod.GET)
    @ResponseBody
    public List<ProductCategoryEntity> findSonList(@RequestParam Integer categoryId) {
        return productCategoryService.findAllSonList(categoryId);
    }


    @ApiOperation(value = "检查是否配置先进先出", produces = "application/json")
    @RequestMapping(value = "/product-category/check-stock-fifo", method = RequestMethod.GET)
    @ResponseBody
    public Boolean checkStockFifo(@RequestParam Integer spaceId, @RequestParam String sku) {
        return bdTagService.checkStockFifo(sku);
    }
}
