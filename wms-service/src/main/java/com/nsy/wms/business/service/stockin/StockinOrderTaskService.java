package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ExceptionConstants;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.product.ProductInfo;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.StockSupplierDeliveryBoxCodePrint;
import com.nsy.api.wms.domain.stockin.StockinOrderTask;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskDetailPrint;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItem;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItemDetail;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskPrint;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockPlatformScheduleStockinStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferStatusEnum;
import com.nsy.api.wms.enumeration.stockin.QcReceiveEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.enumeration.stockout.VacuumEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxFullRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse;
import com.nsy.api.wms.response.stockin.StatusStatisticsResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTaskBaseInfoResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTaskListResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockin.StockinQaTaskUpdateBo;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpGeneratePurchaseReceivingOrderRequest;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.BdQaRuleService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.product.ProductWmsCategoryService;
import com.nsy.wms.business.service.product.ProductWmsCategorySpaceService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleLogService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stock.StockTransferCrossSpaceService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.business.service.stockin.building.StockinBuilding;
import com.nsy.wms.business.service.stockin.valid.StockinOrderTaskValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.bd.BdStockinRuleEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductWmsCategoryEntity;
import com.nsy.wms.repository.entity.product.ProductWmsCategorySpaceEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stock.StockTransferCrossSpaceEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinSpotInfoEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdStockinRuleMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderTaskMapper;
import com.nsy.wms.utils.BarCodeUtils;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import freemarker.template.Template;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinOrderTaskService extends ServiceImpl<StockinOrderTaskMapper, StockinOrderTaskEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinOrderTaskService.class);
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockPlatformScheduleItemService platformScheduleItemService;
    @Autowired
    StockPlatformScheduleService platformScheduleService;
    @Autowired
    ErpApiService erpApiService;
    @Inject
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinScanLogService stockinScanLogService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    LoginInfoService loginInfoService;
    @Inject
    BdSpaceService spaceService;
    @Autowired
    BdStockinRuleMapper bdStockinRuleMapper;
    @Autowired
    ProductWmsCategorySpaceService productWmsCategorySpaceService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockTransferCrossSpaceService stockTransferCrossSpaceService;
    @Autowired
    StockinSpotInfoService stockinSpotInfoService;
    @Autowired
    StockPlatformScheduleLogService platformSchedulerLogService;
    @Autowired
    BdSystemParameterService parameterService;
    @Autowired
    BdQaRuleService bdQaRuleService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;
    @Autowired
    StockPlatformScheduleService stockPlatformScheduleService;
    @Resource
    ProductWmsCategoryService productWmsCategoryService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private BrandCommonService brandCommonService;

    // 根据入库规则生成相应的入库任务
    @Transactional
    @JLock(keyConstant = "generateStockinOrderTask", lockKey = "#platformScheduleEntity.platformScheduleId")
    public void generateStockinOrderTask(StockPlatformScheduleEntity platformScheduleEntity, String userName) {
        if (this.count(new LambdaQueryWrapper<StockinOrderTaskEntity>().eq(StockinOrderTaskEntity::getPlatformScheduleId, platformScheduleEntity.getPlatformScheduleId())) > 0) {
            LOGGER.info("当前月台调度任务已生成入库任务不在重复处理,月台任务id{}", platformScheduleEntity.getPlatformScheduleId());
            return;
        }
        LambdaQueryWrapper<BdStockinRuleEntity> queryWrapper = new LambdaQueryWrapper<BdStockinRuleEntity>().eq(BdStockinRuleEntity::getLocation, platformScheduleEntity.getLocation())
                .eq(BdStockinRuleEntity::getStockinType, platformScheduleEntity.getPlatformScheduleType()).eq(BdStockinRuleEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        BdStockinRuleEntity stockinRuleEntity = bdStockinRuleMapper.selectOne(queryWrapper);
        // case1.没有规则或入库质检类型为先入库后质检，则插入入库任务
        if (Objects.isNull(stockinRuleEntity) || QcReceiveEnum.STOCKIN_QC.name().equals(stockinRuleEntity.getQcReceive())) {
            List<StockPlatformScheduleItemEntity> platformScheduleItemEntityList = platformScheduleItemService.listByPlatformScheduleId(platformScheduleEntity.getPlatformScheduleId());
            Map<String, List<StockPlatformScheduleItemEntity>> map = platformScheduleItemEntityList.stream().collect(Collectors.groupingBy(StockPlatformScheduleItemEntity::getSupplierDeliveryBoxCode));
            map.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey)).forEach(entry -> {
                List<StockPlatformScheduleItemEntity> platformScheduleItemList = entry.getValue();
                StockinOrderTaskEntity orderTaskEntity = new StockinOrderTaskEntity();
                orderTaskEntity.setSupplierDeliveryNo(platformScheduleEntity.getSupplierDeliveryNo());
                orderTaskEntity.setLocation(platformScheduleEntity.getLocation());
                orderTaskEntity.setStockinType(platformScheduleEntity.getPlatformScheduleType());
                orderTaskEntity.setPlatformScheduleId(platformScheduleEntity.getPlatformScheduleId());
                orderTaskEntity.setPurchaseUserId(platformScheduleEntity.getPurchaseUserId());
                orderTaskEntity.setExpectedQty(platformScheduleItemList.stream().mapToInt(StockPlatformScheduleItemEntity::getQty).sum());
                orderTaskEntity.setStatus(StockinOrderTaskStatusEnum.PENDING.name());
                orderTaskEntity.setBoxIndex(platformScheduleItemList.get(0).getBoxIndex());
                orderTaskEntity.setHasBrand(platformScheduleItemList.stream().anyMatch(o -> StringUtils.hasText(o.getBrandName())) ? 1 : 0);
                orderTaskEntity.setSupplierDeliveryBoxCode(entry.getKey());
                orderTaskEntity.setSupplierDeliveryBarcode(platformScheduleItemList.get(0).getSupplierDeliveryBarcode());
                if (!Objects.isNull(stockinRuleEntity)) {
                    orderTaskEntity.setStockinMethod(stockinRuleEntity.getStockinMethod());
                    orderTaskEntity.setQcShelve(stockinRuleEntity.getQcShelve());
                    orderTaskEntity.setWeightProcess(stockinRuleEntity.getWeightProcess());
                }
                orderTaskEntity.setCreateBy(userName);
                this.save(orderTaskEntity);
                stockinScanLogService.addScanLog("月台审核工厂出库单后下发入库任务", orderTaskEntity, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_CREATE);
                Map<Integer, List<ProductInfo>> productInfoMap = productInfoService.getBaseMapper().getVacuumInfo(platformScheduleItemList.stream().map(StockPlatformScheduleItemEntity::getProductId).collect(Collectors.toList()))
                        .stream().collect(Collectors.groupingBy(ProductInfo::getProductId));
                List<StockinOrderTaskItemEntity> orderTaskItemEntityList = platformScheduleItemList.stream().map(entity -> {
                    StockinOrderTaskItemEntity orderTaskItemEntity = getStockinOrderTaskItemEntity(orderTaskEntity, entity, productInfoMap);
                    //查询质检规则
                    bdQaRuleService.setQaQty(orderTaskEntity, platformScheduleEntity.getSupplierId(), orderTaskItemEntity);
                    return orderTaskItemEntity;
                }).collect(Collectors.toList());
                stockinOrderTaskItemService.saveBatch(orderTaskItemEntityList);
            });
        }
        platformSchedulerLogService.addLog(platformScheduleEntity.getPlatformScheduleId(), StockPlatformScheduleLogTypeEnum.GENERATE_STOCKIN_TASK.getLogType(), "月台根据入库规则，生成入库任务");
    }

    @NotNull
    private StockinOrderTaskItemEntity getStockinOrderTaskItemEntity(StockinOrderTaskEntity orderTaskEntity, StockPlatformScheduleItemEntity entity, Map<Integer, List<ProductInfo>> productInfoEntityMap) {
        StockinOrderTaskItemEntity orderTaskItemEntity = new StockinOrderTaskItemEntity();
        BeanUtilsEx.copyProperties(entity, orderTaskItemEntity, "id", "createDate", "createBy", "updateDate", "updateBy", "version");
        orderTaskItemEntity.setTaskId(orderTaskEntity.getTaskId());
        orderTaskItemEntity.setExpectedQty(entity.getQty());
        orderTaskItemEntity.setIsFbaQuick(entity.getIsFbaQuick());
        orderTaskItemEntity.setBrandName(entity.getBrandName());
        stockinOrderTaskItemService.recommendShelveSpaceArea(orderTaskEntity, entity.getSpaceId(), orderTaskItemEntity);
        orderTaskItemEntity.setCreateBy(loginInfoService.getName());

        List<ProductInfo> productInfos = productInfoEntityMap.get(entity.getProductId());
        //真空标志 标记逻辑：（is_fba_quick |  first_order_label | purchaseSkcFirstOrder | B2C专卖） & （product_info.package_vacuum）
        boolean isVacuum = !CollectionUtils.isEmpty(productInfos)
                && StringUtils.hasText(productInfos.get(0).getPackageVacuum())
                && !VacuumEnum.NO_VACUUM.getValue().equalsIgnoreCase(productInfos.get(0).getPackageVacuum());
        //亚马逊专卖 （B2C专卖）
        boolean isAmazonMonopoly = !CollectionUtils.isEmpty(productInfos) && StockConstant.ENABLE.equals(productInfos.get(0).getIsAmazonMonopoly());
        if (isVacuum && (isAmazonMonopoly || StockConstant.ENABLE.equals(entity.getIsFbaQuick()) || StringConstant.FIRST_ORDER_LABEL.equals(entity.getFirstOrderLabel()) || StringConstant.FIRST_ORDER_LABEL.equals(entity.getPurchaseSkcFirstOrder())))
            orderTaskItemEntity.setVacuumFlag(StockConstant.ENABLE);

        // 如果是快进快出 && 品牌不为空 ，查询对应的品牌区域
        if (orderTaskItemEntity.getIsFbaQuick() == 1 && StringUtils.hasText(entity.getBrandName())) {
            String areaNameByBrandName = brandCommonService.getAreaNameByBrandName(entity.getBrandName());
            if (StringUtils.hasText(areaNameByBrandName))
                orderTaskItemEntity.setAreaName(areaNameByBrandName);
        }

        return orderTaskItemEntity;
    }

    // 根据productId获取库区
    public String getSpaceAreaNameByProduct(Integer productId) {
        ProductInfoEntity productInfoEntity = productInfoService.findTopByProductId(productId);
        if (Objects.isNull(productInfoEntity)) return null;
        ProductWmsCategoryEntity wmsCategory = productWmsCategoryService.findByCategoryId(productInfoEntity.getCategoryId());
        if (Objects.isNull(wmsCategory)) return null;
        ProductWmsCategorySpaceEntity wmsCategorySpaceEntity = productWmsCategorySpaceService.getByWmsCategoryId(wmsCategory.getWmsCategoryId());
        return Objects.isNull(wmsCategorySpaceEntity) ? null : wmsCategorySpaceEntity.getSpaceAreaName();
    }

    @Transactional
    @JLock(keyConstant = "getBySupplierDeliveryBoxCode", lockKey = "#supplierDeliveryBoxCode")
    public StockinOrderTask getBySupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity entity = findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null) throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        if (StockinTypeEnum.SPOT.name().equals(entity.getStockinType()))
            throw new BusinessServiceException("现货商品请使用现货入库确认！");
        StockinOrderTask stockinOrderTask = getStockinOrderTask(entity, FormNoTypeEnum.STOCKIN_ORDER_NO_CGRK, Boolean.FALSE);
        BdSystemParameterEntity bdSystemParameterEntity = parameterService.getByKey(BdSystemParameterEnum.WMS_SIZE_SORTED.getKey());
        if (bdSystemParameterEntity == null) {
            return stockinOrderTask;
        }
        StockinBuilding.taskItemOrderBySku(stockinOrderTask, bdSystemParameterEntity);
        return stockinOrderTask;
    }

    @Transactional
    public List<StockinOrderTask> getByLogisticsNo(String logisticsNo) {
        List<StockinSpotInfoEntity> spotInfoEntityList = stockinSpotInfoService.list(new LambdaQueryWrapper<StockinSpotInfoEntity>().eq(StockinSpotInfoEntity::getLogisticsNo, logisticsNo));
        if (CollectionUtils.isEmpty(spotInfoEntityList)) {
            throw new BusinessServiceException("该物流单号没有签收记录");
        }
        List<String> supplierDeliveryNoList = spotInfoEntityList.stream().map(StockinSpotInfoEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
        List<StockinOrderTaskEntity> list = this.list(new QueryWrapper<StockinOrderTaskEntity>().lambda()
                .in(StockinOrderTaskEntity::getSupplierDeliveryNo, supplierDeliveryNoList));
        if (CollectionUtils.isEmpty(spotInfoEntityList)) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        }
        ArrayList<StockinOrderTask> objects = new ArrayList<>();
        list.stream().forEach(entity -> {
            if (!StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus())) {
                objects.add(getStockinOrderTask(entity, FormNoTypeEnum.STOCKIN_ORDER_NO_XHRK, Boolean.FALSE));
            }
        });
        if (CollectionUtils.isEmpty(objects)) {
            throw new BusinessServiceException("该物流单下的入库任务已经完成收货");
        }
        return objects;
    }

    @Transactional
    public StockinOrderTask getStockinOrderTask(StockinOrderTaskEntity entity, FormNoTypeEnum stockinOrderNoType, Boolean isQa) {
        if (StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus()))
            throw new BusinessServiceException("出库箱码已经完成收货");
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId());
        if (CollectionUtils.isEmpty(itemEntityList))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK_ITEM);
        List<ProductSpecInfoEntity> specInfoEntityList = productSpecInfoService.findAllBySpecIdIn(itemEntityList.stream()
                .map(StockinOrderTaskItemEntity::getSpecId).collect(Collectors.toList()));
        HashMap<Integer, ProductSpecInfoEntity> specMap = new HashMap<>();
        specInfoEntityList.stream().forEach(productSpecInfoEntity -> specMap.put(productSpecInfoEntity.getSpecId(), productSpecInfoEntity));
        //生成入库单
        StockinOrderEntity orderEntity = stockinOrderService.create(entity, stockinOrderNoType);
        List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.listFromStockin(isQa,
                itemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()));
        Map<String, List<StockinOrderItemEntity>> collect = stockinOrderItemEntityList.stream().collect(Collectors.groupingBy(StockinOrderItemEntity::getSku));
        Map<String, List<StockinOrderTaskItemEntity>> taskItemEntityMap = itemEntityList.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku));
        List<StockinOrderTaskItem> itemList = stockinOrderTaskItemService.getStockinOrderTaskItems(specMap, collect, taskItemEntityMap, entity.getHasBrand());
        itemList.addAll(stockinOrderItemService.addItemList(orderEntity));
        StockinOrderTask stockinOrderTask = new StockinOrderTask();
        BeanUtilsEx.copyProperties(entity, stockinOrderTask);
        stockinOrderTask.setStockinOrderTaskItemList(itemList);
        stockinOrderTask.setNotice(getSupplierNameTotice(entity));
        //生成扫描日志
        if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) {
            stockinScanLogService.addScanLog(isQa ? StockinScanLogTypeEnum.STOCKIN_QA_SCAN_LOG_CONTENT_RECEIVE : StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_CONTENT_RECEIVE,
                    entity, orderEntity, isQa ? StockinScanLogTypeEnum.STOCKIN_QA_SCAN_LOG_TYPE_START_RECEIVE : StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_START_RECEIVE);
        }
        if (!StockinOrderTaskStatusEnum.RECEIVING.name().equals(entity.getStatus())) {
            //更新出库任务状态
            entity.setOperator(loginInfoService.getName());
            entity.setOperateStartDate(new Date());
            entity.setStatus(StockinOrderTaskStatusEnum.RECEIVING.name());
            entity.setIsPushQc(isQa ? 1 : 0);
            entity.setUpdateBy(loginInfoService.getName());
            this.updateById(entity);
        }
        platformScheduleService.updateStockinStatus(entity.getPlatformScheduleId(), StockPlatformScheduleStockinStatusEnum.INBOUNDING);
        platformSchedulerLogService.addLog(entity.getPlatformScheduleId(), StockPlatformScheduleLogTypeEnum.START_RECEIVING.getLogType(), String.format("月台下的出库箱码【%s】开始收货，生成入库单【%s】", stockinOrderTask.getSupplierDeliveryBoxCode(), orderEntity.getStockinOrderNo()));
        return stockinOrderTask;
    }

    // 生成入库单-日志记录
    public void createStockinOrderAddStockinOrderLog(StockinOrderEntity orderEntity, StockinOrderTaskEntity entity) {
        String content;
        if (StockinTypeEnum.SPOT.name().equals(entity.getStockinType())) {
            String logisticsNos = stockinSpotInfoService.listBySupplierDeliveryNo(entity.getSupplierDeliveryNo()).stream().map(StockinSpotInfoEntity::getLogisticsNo).collect(Collectors.joining(StringConstant.COMMA));
            content = String.format("【%s】扫描物流单号【%s】 生成入库单",
                    enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName(), entity.getStockinType()), logisticsNos);
        } else {
            content = String.format("【%s】扫描出库箱码【%s】 生成入库单",
                    enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName(), entity.getStockinType()), entity.getSupplierDeliveryBoxCode());
        }
        stcokinOrderTimeService.addTime(orderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.CREATE_STOCKIN_ORDER);
        stockinOrderLogService.addLog(orderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.CREATE_STOCKIN_ORDER.getName(), content);
        //修改调拨单状态
        if (StockinTypeEnum.ALLOT.name().equals(entity.getStockinType())) {
            String transferNo = entity.getSupplierDeliveryNo();
            StockTransferCrossSpaceEntity byTransferNo = stockTransferCrossSpaceService.findByTransferNo(transferNo);
            Optional.ofNullable(byTransferNo).ifPresent(spaceEntity -> {
                spaceEntity.setStatus(StockTransferStatusEnum.INBOUNDING.name());
                stockTransferCrossSpaceService.updateById(spaceEntity);
            });
        }
    }

    public void generatePurchaseReceivingOrder(String supplierDeliveryBoxCode, Boolean checkUnCollect, Date receiveDate, String stockinType) {
        StockinOrderTaskEntity taskEntity = this.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        //仓间调拨不生成接收单
        if (StockinTypeEnum.ALLOT.name().equals(taskEntity.getStockinType()))
            return;
        StockPlatformScheduleEntity bySupplierDeliveryNo = stockPlatformScheduleService.getBySupplierDeliveryNo(taskEntity.getSupplierDeliveryNo());
        ErpGeneratePurchaseReceivingOrderRequest request = new ErpGeneratePurchaseReceivingOrderRequest();
        request.setLocation(TenantContext.getTenant());
        request.setOperator(loginInfoService.getName());
        request.setCheckUnCollect(checkUnCollect ? 1 : 0);
        request.setSupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        request.setReceiveDate(receiveDate);
        request.setStockinType(stockinType);
        if (Objects.nonNull(bySupplierDeliveryNo)) request.setAuditDate(bySupplierDeliveryNo.getAuditDate());

        //由于多发入库的工厂出库单是scm异步生成，故而需要延后调用
        boolean isDelay = supplierDeliveryBoxCode.contains(StockConstant.STOCKIN_ORDER_NO_DFRK);
        erpApiService.asyncGeneratePurchaseReceivingOrder(request, isDelay);
    }

    public List<String> getPromptInformation(Integer taskId, String barcode) {
        StockinOrderTaskEntity taskEntity = this.getById(taskId);
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(barcode, Collections.singletonList(taskId));
        if (StockinTypeEnum.SPOT.name().equals(taskEntity.getStockinType()))
            return getSpotPromptInformation(taskId, productSpecInfoEntity.getBarcode());
        ArrayList<String> strings = new ArrayList<>(10);
        if (Objects.isNull(taskEntity))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndBarcode(taskId, productSpecInfoEntity.getBarcode());
        if (CollectionUtils.isEmpty(itemEntityList)) {
            strings.add("该商品不属于该工厂出库单");
            return strings;
        }
        StockinOrderTaskItemEntity itemEntity = itemEntityList.get(0);
        StockPlatformScheduleEntity platformScheduleEntity = platformScheduleService.getById(taskEntity.getPlatformScheduleId());
        String message1 = itemEntityList.stream().anyMatch(taskItemEntity -> taskItemEntity.getIsNeedQa().equals(1)) ? "商品需要质检" : "商品不需要质检";
        strings.add(message1.concat(String.format(",到货属于%s到货", platformScheduleEntity.getSupplierName())));
        String shelveSpaceAreaName = itemEntity.getShelveSpaceAreaName();
        if (StringUtils.hasText(shelveSpaceAreaName)) {
            BdSpaceEntity spaceEntity = spaceService.getById(itemEntity.getSpaceId());
            strings.add(String.format("商品预上架区：%s,%s,%s", spaceEntity.getSpaceName(), itemEntity.getAreaName(), shelveSpaceAreaName));
        }
        for (int i = 1; i <= itemEntityList.size(); i++) {
            StockinOrderTaskItemEntity itemEntity1 = itemEntityList.get(i - 1);
            strings.add(String.format("申请部门%s：%s,%s|申请计划单号：%s", i, itemEntity1.getPurchaseApplyInfo(),
                    platformScheduleEntity.getPurchaseUserRealName(), itemEntity1.getPurchasePlanNo()));
        }
        if (productSpecInfoEntity.getActualWeight() == null || productSpecInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0) {
            List<StockinSpotInfoEntity> infoEntityList = stockinSpotInfoService.listBySupplierDeliveryNo(taskEntity.getSupplierDeliveryNo());
            if (!CollectionUtils.isEmpty(infoEntityList)) strings.add("入库时需要称重");
        }
        String businessType = platformScheduleItemService.list(new LambdaQueryWrapper<StockPlatformScheduleItemEntity>().select(StockPlatformScheduleItemEntity::getBusinessType)
                        .eq(StockPlatformScheduleItemEntity::getPlatformScheduleId, taskEntity.getPlatformScheduleId())
                        .eq(StockPlatformScheduleItemEntity::getSupplierDeliveryBoxCode, taskEntity.getSupplierDeliveryBoxCode())
                        .eq(StockPlatformScheduleItemEntity::getSku, productSpecInfoEntity.getSku()))
                .stream().map(StockPlatformScheduleItemEntity::getBusinessType).distinct().collect(Collectors.joining(","));
        if (StringUtils.hasText(businessType)) strings.add("申请部门：" + businessType);
        if (itemEntityList.stream().anyMatch(taskItem -> StockConstant.ENABLE.equals(taskItem.getVacuumFlag()))) {
            ProductInfoEntity productInfoEntity = productInfoService.findTopByProductId(productSpecInfoEntity.getProductId());
            strings.add(VacuumEnum.getPcNoticeByKey(productInfoEntity.getPackageVacuum()) + "包装");
        }
        if (itemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains("OEM首单")))
            strings.add("OEM首单");
        if (itemEntityList.stream().anyMatch(item -> Objects.nonNull(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains("ODM首单")))
            strings.add("ODM首单");
        return strings;
    }

    public List<String> getSpotPromptInformation(Integer taskId, String barcode) {
        ArrayList<String> strings = new ArrayList<>(10);
        StockinOrderTaskEntity taskEntity = this.getById(taskId);
        if (Objects.isNull(taskEntity))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        List<StockinOrderTaskItemEntity> itemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndBarcode(taskId, barcode);
        if (CollectionUtils.isEmpty(itemEntityList)) {
            strings.add("该商品不属于该工厂出库单");
            return strings;
        }
        StockinOrderTaskItemEntity itemEntity = itemEntityList.get(0);
        StockinOrderEntity stockinOrderEntity = stockinOrderService.getByTaskId(taskEntity.getTaskId());
        String message1 = itemEntityList.stream().anyMatch(taskItemEntity -> taskItemEntity.getIsNeedQa().equals(1)) ? "商品需要质检" : "商品不需要质检";
        strings.add(message1.concat(String.format(",到货属于%s到货", stockinOrderEntity.getSupplierName())));
        String shelveSpaceAreaName = itemEntity.getShelveSpaceAreaName();
        if (StringUtils.hasText(shelveSpaceAreaName)) {
            BdSpaceEntity spaceEntity = spaceService.getById(itemEntity.getSpaceId());
            strings.add(String.format("商品预上架区：%s,%s,%s", spaceEntity.getSpaceName(), itemEntity.getAreaName(), shelveSpaceAreaName));
        }
        for (int i = 1; i <= itemEntityList.size(); i++) {
            StockinOrderTaskItemEntity itemEntity1 = itemEntityList.get(i - 1);
            strings.add(String.format("申请计划单号：%s", itemEntity1.getPurchasePlanNo()));
        }
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopBySku(itemEntity.getSku());
        if (specInfoEntity.getActualWeight() == null || specInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0) {
            List<StockinSpotInfoEntity> infoEntityList = stockinSpotInfoService.listBySupplierDeliveryNo(taskEntity.getSupplierDeliveryNo());
            if (!CollectionUtils.isEmpty(infoEntityList)) {
                strings.add("入库时需要称重");
            }
        }
        return strings;
    }

    public String getSupplierNameTotice(StockinOrderTaskEntity taskEntity) {
        StockPlatformScheduleEntity platformScheduleEntity = platformScheduleService.getById(taskEntity.getPlatformScheduleId());
        return String.format("到货属于%s到货", platformScheduleEntity.getSupplierName());
    }

    @Transactional
    public void editSkuQty(Integer taskId, StockinOrderTaskItemDetail request) {
        StockinOrderTaskValid.validateStockinOrderTaskItemDetail(request);
        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(request.getInternalBoxCode());
        StockinOrderTaskEntity taskEntity = this.getById(taskId);
        if (Objects.isNull(taskEntity))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskEntity.getTaskId());
        if (Objects.isNull(stockinOrderEntity)) throw new BusinessServiceException("请先录入该数据");
        List<StockinOrderItemEntity> orderItemEntityList = stockinOrderItemService.findAllByStockinOrderIdAndSkuAndInternalBoxCode(stockinOrderEntity.getStockinOrderId(), request.getSku(), request.getInternalBoxCode());
        if (CollectionUtils.isEmpty(orderItemEntityList))
            throw new BusinessServiceException("请先录入该数据");
        //没有修改数量则直接返回
        if (request.getQty().equals(orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum()))
            return;
        if (IsDeletedConstant.DELETED.equals(internalBox.getIsDeleted()))
            throw new BusinessServiceException("箱子已被删除，无法操作");
        if (StockInternalBoxStatusEnum.SHELVING.name().equals(internalBox.getStatus()) || StockInternalBoxStatusEnum.QC_PROCESSING.name().equals(internalBox.getStatus()) || StockInternalBoxStatusEnum.RETURNING.name().equals(internalBox.getStatus())) {
            throw new BusinessServiceException(String.format("该内部箱状态为：%s，禁止修改数量！", enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_STATUS.getName(), internalBox.getStatus())));
        }
        if (orderItemEntityList.stream().anyMatch(item -> StockinOrderItemStatusEnum.WAIT_DEAL.name().equals(item.getStatus()))) {
            throw new BusinessServiceException(String.format("该内部箱sku【%s】质检处理中，禁止修改数量！", request.getSku()));
        }
        if (orderItemEntityList.size() == 1 && Objects.isNull(orderItemEntityList.get(0).getTaskItemId())) {
            StockinOrderItemEntity orderItemEntity = orderItemEntityList.get(0);
            this.editQty(orderItemEntity, request);
            //生成日志
            String content = String.format("SKU【%s】原收货%s件，修改收货为%s件，绑定内部箱号%s", orderItemEntity.getSku(),
                    orderItemEntity.getQty(), request.getQty(), orderItemEntity.getInternalBoxCode());
            stockinScanLogService.addScanLog(content, taskEntity, stockinOrderEntity, orderItemEntity, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_RECEIVING_EDIT);
        } else {
            List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.list(new QueryWrapper<StockinOrderTaskItemEntity>().lambda()
                    .eq(StockinOrderTaskItemEntity::getTaskId, taskEntity.getTaskId())
                    .eq(StockinOrderTaskItemEntity::getSku, request.getSku()));
            validQty(taskItemEntityList, orderItemEntityList, request);
            dealQty(stockinOrderEntity, orderItemEntityList, taskItemEntityList, request.getQty(), request.getInternalBoxCode());
            //生成日志
            String content = String.format("SKU【%s】预收货%s件，原收货%s件，修改收货为%s件，需质检%s件，绑定内部箱号%s",
                    request.getSku(), taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum(),
                    taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getStockinQty).sum(), request.getQty(),
                    taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getQaQty).sum(), request.getInternalBoxCode());
            stockinScanLogService.addScanLog(content, taskEntity, stockinOrderEntity, request.getQty(), StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_RECEIVING_EDIT);
        }
        //满箱确认
        StockinInternalBoxFullRequest stockinInternalBoxFullRequest = new StockinInternalBoxFullRequest();
        stockinInternalBoxFullRequest.setTaskId(taskId);
        stockInternalBoxService.fullInternalBox(request.getInternalBoxCode(), stockinInternalBoxFullRequest);

        //生成或更新质检任务
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_TASK_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_TASK_TOPIC,
                Key.of(internalBox.getInternalBoxCode() + "_" + request.getSku()),
                new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(),
                        new StockinQaTaskUpdateBo(internalBox.getInternalBoxCode(), request.getSku())));
    }

    private void validQty(List<StockinOrderTaskItemEntity> stockinOrderTaskItemEntityList, List<StockinOrderItemEntity> orderItemEntityList, StockinOrderTaskItemDetail detail) {
        int expectedQty = stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
        List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.findAllByTaskItemIdIn(stockinOrderTaskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()));
        int stockinQty = stockinOrderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
        int internalBoxQty = orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
        if (detail.getQty() - internalBoxQty + stockinQty > expectedQty) {
            throw new BusinessServiceException("收货数不能大于预收货数！");
        }
    }

    private void dealQty(StockinOrderEntity stockinOrderEntity, List<StockinOrderItemEntity> orderItemEntityList, List<StockinOrderTaskItemEntity> taskItemEntityList, int qty, String internalBoxCode) {
        Map<Integer, List<StockinOrderItemEntity>> orderItemMap = orderItemEntityList.stream().collect(Collectors.groupingBy(StockinOrderItemEntity::getTaskItemId));
        //现有数量
        int stockQty = orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
        int qtyEdit = qty - stockQty;
        if (qtyEdit > 0) {
            taskItemEntityList.sort(Comparator.comparing(entity -> entity.getStockinQty() - entity.getExpectedQty()));
            for (int i = 0; qtyEdit > 0 && i < taskItemEntityList.size(); i++) {
                StockinOrderTaskItemEntity taskItemEntity = taskItemEntityList.get(i);
                //是否为最后一个
                boolean isLast = i == taskItemEntityList.size() - 1;
                List<StockinOrderItemEntity> orderItemEntityList1 = orderItemMap.get(taskItemEntity.getTaskItemId());
                StockinOrderItemEntity orderItemEntity = getStockinOrderItemEntity(stockinOrderEntity, internalBoxCode, taskItemEntity, orderItemEntityList1);
                int stockinQty = Objects.isNull(taskItemEntity.getStockinQty()) ? 0 : taskItemEntity.getStockinQty();
                //还可改变的数量
                int canLoadQty = taskItemEntity.getExpectedQty() - stockinQty;
                //当该taskItem不可改变 且不是最后一个,就continue
                if (!isLast && canLoadQty <= 0) continue;
                //实际改变的数量
                int loadQty = isLast ? qtyEdit : Math.min(qtyEdit, canLoadQty);
                StockinOrderTaskItemDetail detail = new StockinOrderTaskItemDetail();
                detail.setInternalBoxCode(orderItemEntity.getInternalBoxCode());
                detail.setSku(orderItemEntity.getSku());
                detail.setQty(orderItemEntity.getQty() + loadQty);
                this.editQty(orderItemEntity, detail);
                qtyEdit = qtyEdit - canLoadQty;
                //数据回填给taskItem
                taskItemEntity.setStockinQty(stockinQty + loadQty);
                taskItemEntity.setUpdateBy(loginInfoService.getName());
                stockinOrderTaskItemService.updateById(taskItemEntity);
            }
        } else if (qtyEdit < 0) {
            //减少的算法更复杂点，重写个方法
            Map<Integer, Integer> reduceOrderItem = stockinOrderTaskItemService.findReduceOrderItem(orderItemEntityList, taskItemEntityList, qtyEdit);
            stockinOrderTaskItemService.reduceOrderItem(taskItemEntityList, internalBoxCode, orderItemMap, reduceOrderItem);
        }
    }

    public StockinOrderItemEntity getStockinOrderItemEntity(StockinOrderEntity stockinOrderEntity, String internalBoxCode, StockinOrderTaskItemEntity taskItemEntity, List<StockinOrderItemEntity> orderItemEntityList1) {
        StockinOrderItemEntity orderItemEntity;
        if (CollectionUtils.isEmpty(orderItemEntityList1)) {
            orderItemEntity = stockinOrderItemService.createItem(internalBoxCode, taskItemEntity, stockinOrderEntity);
        } else {
            Optional<StockinOrderItemEntity> any = orderItemEntityList1.stream()
                    .filter(entity -> internalBoxCode.equals(entity.getInternalBoxCode())).findAny();
            if (any.isPresent()) {
                orderItemEntity = any.get();
            } else {
                orderItemEntity = stockinOrderItemService.createItem(internalBoxCode, taskItemEntity, stockinOrderEntity);
            }
        }
        return orderItemEntity;
    }


    /**
     * 修改入库单明细、 内部箱明细、库存
     *
     * @param stockinOrderItemEntity
     * @param request
     */
    @Transactional
    public void editQty(StockinOrderItemEntity stockinOrderItemEntity, StockinOrderTaskItemDetail request) {
        //修改内部箱明细
        Integer qty = request.getQty() - stockinOrderItemEntity.getQty();
        if (qty.equals(0)) return;
        StockinOrderEntity orderEntity = stockinOrderService.getById(stockinOrderItemEntity.getStockinOrderId());
        stockInternalBoxService.editQty(qty, orderEntity, stockinOrderItemEntity);
        //如果是0 则删除明细
        if (request.getQty() == 0) {
            stockinOrderItemService.removeById(stockinOrderItemEntity);
        } else {
            stockinOrderItemEntity.setQty(request.getQty());
            stockinOrderItemEntity.setUpdateBy(loginInfoService.getName());
            stockinOrderItemService.updateById(stockinOrderItemEntity);
        }
    }

    public StockinOrderTaskEntity findTopBySupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        return this.getOne(new QueryWrapper<StockinOrderTaskEntity>().lambda()
                .eq(StockinOrderTaskEntity::getSupplierDeliveryBarcode, supplierDeliveryBoxCode)
                .or()
                .eq(StockinOrderTaskEntity::getSupplierDeliveryBoxCode, supplierDeliveryBoxCode)
                .last("limit 1"));
    }

    public List<StockinOrderTaskEntity> findAllBySupplierDeliveryNo(String supplierDeliveryNo) {
        return this.list(new QueryWrapper<StockinOrderTaskEntity>().lambda().eq(StockinOrderTaskEntity::getSupplierDeliveryNo, supplierDeliveryNo));
    }

    public List<StockinOrderTaskEntity> findAllBySupplierDeliveryNoList(List<String> supplierDeliveryNoList) {
        return this.list(new QueryWrapper<StockinOrderTaskEntity>().lambda().in(StockinOrderTaskEntity::getSupplierDeliveryNo, supplierDeliveryNoList));
    }

    public StatusStatisticsResponse statusStatistics() {
        StatusStatisticsResponse response = new StatusStatisticsResponse();
        response.setStatusStatisticsList(this.baseMapper.statusStatistics());
        return response;
    }

    public PageResponse<StockinOrderTaskListResponse> pageList(StockinOrderTaskListRequest request) {
        Page<StockinOrderTaskListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        List<StockinOrderTaskListResponse> pageList = this.baseMapper.pageList(page, request);
        buildStockinQty(pageList);
        Map<String, String> stockinTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName());
        Map<String, String> stockinTaskTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TASK_TYPE.getName());
        pageList.forEach(stockinOrderTaskListResponse -> {
            stockinOrderTaskListResponse.setStockinType(stockinTypeEnumMap.get(stockinOrderTaskListResponse.getStockinType()));
            stockinOrderTaskListResponse.setStatus(stockinTaskTypeEnumMap.get(stockinOrderTaskListResponse.getStatus()));
            //现货重新获取
            StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(stockinOrderTaskListResponse.getTaskId());
            if (Objects.nonNull(stockinOrderEntity) && StockinTypeEnum.SPOT.name().equals(stockinOrderEntity.getStockinType())) {
                stockinOrderTaskListResponse.setLogisticsNo(stockinOrderEntity.getLogisticsNo());
                stockinOrderTaskListResponse.setSupplierName(stockinOrderEntity.getSupplierName());
                stockinOrderTaskListResponse.setStockinType(StockinTypeEnum.SPOT.getType());
            }
            if (StockConstant.ENABLE.equals(stockinOrderTaskListResponse.getIsODM()) || StockConstant.ENABLE.equals(stockinOrderTaskListResponse.getIsOEM())) {
                stockinOrderTaskListResponse.setFirstLabel(StockConstant.ENABLE.equals(stockinOrderTaskListResponse.getIsODM()) ? StringConstant.FIRST_ORDER_LABEL_ODM : StringConstant.FIRST_ORDER_LABEL_OEM);
            }
            stockinOrderTaskListResponse.setReceiptPlace(enumConversionChineseUtils.getLocationValue(stockinOrderTaskListResponse.getReceiptPlace()));
        });
        PageResponse<StockinOrderTaskListResponse> response = new PageResponse<>();
        response.setContent(pageList);
        response.setTotalCount(this.baseMapper.pageListCount(request));
        return response;
    }

    /**
     * 实际收货数
     *
     * @param responseList
     */
    private void buildStockinQty(List<StockinOrderTaskListResponse> responseList) {
        if (responseList.isEmpty()) return;
        List<Integer> taskIdList = responseList.stream().map(StockinOrderTaskListResponse::getTaskId).collect(Collectors.toList());
        List<StockinOrderTaskItemEntity> taskItemList = stockinOrderTaskItemService.findAllByTaskIdList(taskIdList);
        Map<Integer, List<StockinOrderTaskItemEntity>> itemMap = taskItemList.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getTaskId));
        responseList.forEach(response -> {
            List<StockinOrderTaskItemEntity> itemList = itemMap.get(response.getTaskId());
            if (Objects.isNull(itemList) || itemList.isEmpty()) return;
            response.setStockinQty(itemList
                    .stream()
                    .filter(temp -> !ObjectUtils.isEmpty(temp.getStockinQty()))
                    .mapToInt(StockinOrderTaskItemEntity::getStockinQty)
                    .sum());
        });
    }

    public StockinOrderTaskBaseInfoResponse baseInfoBoxCode(String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity stockinOrderTaskEntity = this.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (Objects.isNull(stockinOrderTaskEntity))
            throw new BusinessServiceException("未找到出库箱码对应的入库任务！");
        return this.baseInfo(stockinOrderTaskEntity.getTaskId());
    }

    public StockinOrderTaskBaseInfoResponse baseInfo(Integer taskId) {
        StockinOrderTaskBaseInfoResponse response = this.baseMapper.baseInfo(taskId);
        response.setStockinTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName(), response.getStockinType()));
        response.setStockinMethodStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_METHOD.getName(), response.getStockinMethod()));
        response.setQcShelveStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_QC_SHELVE_TYPE.getName(), response.getQcShelve()));
        response.setWeightProcessStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_WEIGHT_ORDER.getName(), response.getWeightProcess()));
        response.setStatusStr(StockinOrderTaskStatusEnum.getByName(response.getStatus()));
        //现货重新获取
        StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskId);
        if (Objects.nonNull(stockinOrderEntity) && StockinTypeEnum.SPOT.name().equals(stockinOrderEntity.getStockinType())) {
            response.setSupplierName(stockinOrderEntity.getSupplierName());
            response.setStockinType(StockinTypeEnum.SPOT.name());
            response.setStockinTypeStr(StockinTypeEnum.SPOT.getType());
        }
        return response;
    }

    public StockInternalBox getInternalBox(String internalBoxCode) {
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        if (boxEntity == null)
            throw new BusinessServiceException(String.format("未找到内部箱号为【%s】的内部箱!", internalBoxCode));
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            throw new BusinessServiceException("箱子已被删除，请确认！");
        if (!StockInternalBoxTypeEnum.RECEIVE_BOX.name().equals(boxEntity.getInternalBoxType()))
            throw new BusinessServiceException("该箱子不是收货箱，请扫描收货箱！");
        String status = boxEntity.getStatus();
        List<String> statusList = Arrays.asList(StockInternalBoxStatusEnum.EMPTY.name(), StockInternalBoxStatusEnum.PACKING.name(), StockInternalBoxStatusEnum.WAIT_QC.name(),
                StockInternalBoxStatusEnum.WAIT_SHELVE.name(), StockInternalBoxStatusEnum.QC_COMPLETED.name(),
                StockInternalBoxStatusEnum.WAIT_RETURN.name());
        if (!statusList.contains(status)) {
            throw new BusinessServiceException(String.format("该类型【%s】的内部箱，不允许接收", StockInternalBoxStatusEnum.valueOf(status).getStatus()));
        }
        StockInternalBox stockInternalBox = new StockInternalBox();
        BeanUtilsEx.copyProperties(boxEntity, stockInternalBox);
        return stockInternalBox;
    }

    @Transactional
    public PrintListResponse printSupplierDeliveryBoxCode(IdListRequest idListRequest, Boolean singlePrint) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.SUPPLIER_DELIVERY_NO.getTemplateName());
        LambdaQueryWrapper<StockinOrderTaskEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockinOrderTaskEntity::getTaskId, idListRequest.getIdList());
        wrapper.orderByDesc(StockinOrderTaskEntity::getSupplierDeliveryNo).orderByDesc(StockinOrderTaskEntity::getBoxIndex);
        List<StockinOrderTaskEntity> stockinOrderTaskEntityList = this.list(wrapper);
        List<String> list = stockinOrderTaskEntityList.stream().map(entity -> {
            StockPlatformScheduleEntity stockPlatformScheduleEntity = platformScheduleService.getById(entity.getPlatformScheduleId());
            List<StockinOrderTaskItemEntity> allByTaskId = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId());
            StockSupplierDeliveryBoxCodePrint print = new StockSupplierDeliveryBoxCodePrint();
            BeanUtilsEx.copyProperties(stockPlatformScheduleEntity, print);
            String supplierName = stockPlatformScheduleEntity.getSupplierName();
            if (StringUtils.hasText(supplierName) && supplierName.indexOf('-') > 0) {
                print.setSupplierName(supplierName.substring(0, supplierName.lastIndexOf('-')));
            }
            print.setBoxIndex(entity.getBoxIndex());
            print.setCheck(allByTaskId.stream().anyMatch(item -> item.getIsNeedQa().equals(1)) ? "(检)" : "");
            print.setSupplierDeliveryBoxCode(entity.getSupplierDeliveryBoxCode());
            print.setSupplierDeliveryBarcode(StringUtils.hasText(entity.getSupplierDeliveryBarcode()) ? entity.getSupplierDeliveryBarcode() : entity.getSupplierDeliveryBoxCode());
            return PrintTransferUtils.transfer(templateEntity.getContent(), print);
        }).collect(Collectors.toList());
        response.setHtmlList(PrintTransferUtils.doubleTransfer(singlePrint, list, templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public PrintListResponse printDetail(IdListRequest idList) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new LinkedList<>());
        List<StockinOrderTaskEntity> taskEntities = listByIds(idList.getIdList());
        if (CollectionUtils.isEmpty(taskEntities)) throw new InvalidRequestException("请选择需要打印的单据");
        Map<String, Object> map = new HashMap<>(32);
        Map<String, String> stockinTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName());

        for (StockinOrderTaskEntity task : taskEntities) {
            StockinOrderTaskPrint dto = new StockinOrderTaskPrint();
            BeanUtils.copyProperties(task, dto);
            dto.setStockinTypeLabel(stockinTypeEnumMap.get(task.getStockinType()));
            StockPlatformScheduleEntity sc = platformScheduleService.getById(task.getPlatformScheduleId());
            dto.setSupplierName(sc.getSupplierName());
            map.put("taskInfo", dto);
            LambdaQueryWrapper<StockinOrderTaskItemEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StockinOrderTaskItemEntity::getTaskId, task.getTaskId());
            List<StockinOrderTaskItemEntity> taskItemList = stockinOrderTaskItemService.list(wrapper);
            List<StockinOrderTaskDetailPrint> list = new LinkedList<>();
            int i = 1;
            for (StockinOrderTaskItemEntity item : taskItemList) {
                StockinOrderTaskDetailPrint detailPrint = new StockinOrderTaskDetailPrint();
                detailPrint.setIndex(i);
                BeanUtils.copyProperties(item, detailPrint);
                detailPrint.setIsNeedQaLabel(item.getIsNeedQa() == 1 ? "是" : "否");
                list.add(detailPrint);
                i++;
            }
            map.put("detailList", list);
            map.put("nowDate", DateFormatUtils.format(new Date(), "yyyy/MM/dd  HH:mm:ss"));
            map.put("barCodeImage", BarCodeUtils.getBarCodeBase64(task.getSupplierDeliveryBoxCode()));
            Template template = FreeMarkerTemplateUtils.getTemplate("StockInTaskFirstPage.ftl");
            String html = FreeMarkerTemplateUtils.renderTemplate(template, map);
            response.getHtmlList().add(html);
        }
        return response;
    }

    public List<StatisticsByPlatformScheduleIdResponse> statisticsByPlatformScheduleId(List<Integer> platformScheduleIdList) {
        if (CollectionUtils.isEmpty(platformScheduleIdList)) return new ArrayList<>();
        return getBaseMapper().statisticsByPlatformScheduleId(platformScheduleIdList);
    }

    /**
     * 根据出库箱码查询工厂出库单号
     *
     * @param supplierDeliveryBoxCodeList
     * @return
     */
    public List<String> getSupplierDeliveryNoByBoxCode(List<String> supplierDeliveryBoxCodeList) {
        List<StockinOrderTaskEntity> taskEntities = this.list(new LambdaQueryWrapper<StockinOrderTaskEntity>()
                .select(StockinOrderTaskEntity::getSupplierDeliveryNo)
                .in(StockinOrderTaskEntity::getSupplierDeliveryBoxCode, supplierDeliveryBoxCodeList));
        if (CollectionUtils.isEmpty(taskEntities)) {
            return Collections.emptyList();
        }
        return taskEntities.stream().map(StockinOrderTaskEntity::getSupplierDeliveryNo).collect(Collectors.toList());
    }

    /**
     * 根据采购计划单号找到对应的工厂出库单号
     *
     * @param purchasePlanNo
     * @return
     */
    public List<String> getSupplierDeliveryByPurchasePlanNo(String purchasePlanNo) {
        return this.baseMapper.getSupplierDeliveryNoFromPurchasePlanNo(purchasePlanNo);
    }
}
