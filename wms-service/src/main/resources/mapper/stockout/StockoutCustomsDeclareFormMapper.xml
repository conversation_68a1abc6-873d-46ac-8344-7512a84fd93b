<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareFormMapper">
    <sql id="pageWhere">
        <if test="request.systemMark != null and request.systemMark !=''">
            and scdf.system_mark = #{request.systemMark}
        </if>
        <if test="request.declareDocumentNo != null and request.declareDocumentNo.size() > 0">
            and scdf.declare_document_no in
            <foreach collection="request.declareDocumentNo" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.protocolNo != null and request.protocolNo.size() > 0">
            and scdf.protocol_no in
            <foreach collection="request.protocolNo" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.supplierId != null and request.supplierId.size() > 0">
            and scdf.supplier_id in
            <foreach collection="request.supplierId" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.status != null and request.status.size() > 0">
            and scdf.status in
            <foreach collection="request.status" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.companyId != null">
            and scdf.company_id = #{request.companyId}
        </if>
        <if test="request.declareFormId != null and request.declareFormId.size() > 0">
            and scdf.declare_form_id in
            <foreach collection="request.declareFormId" separator="," index="index" item="item" open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.stockoutDateBegin != null">
            and scdf.stockout_date &gt;= #{request.stockoutDateBegin}
        </if>
        <if test="request.stockoutDateEnd != null">
            and scdf.stockout_date &lt;= #{request.stockoutDateEnd}
        </if>
        <if test="request.shouldAuditItem != null and request.shouldAuditItem">
            and scdf.should_audit_item = 1
        </if>
        <if test="request.shouldAuditItem != null and !request.shouldAuditItem">
            and scdf.should_audit_item = 0
        </if>
        <if test="request.shouldChooseOtherSupplier != null and request.shouldChooseOtherSupplier">
            and scdf.should_choose_other_supplier = 1
        </if>
        <if test="request.shouldChooseOtherSupplier != null and !request.shouldChooseOtherSupplier">
            and scdf.should_choose_other_supplier = 0
        </if>
        <if test="request.exchangeRateNull != null and request.exchangeRateNull">
            and scdf.exchange_rate = 0
        </if>
        <if test="request.exchangeRateNull != null and !request.exchangeRateNull">
            and scdf.exchange_rate != 0
        </if>
        <if test="request.createDateStart != null">
            and scdf.create_date &gt;= #{request.createDateStart}
        </if>
        <if test="request.createDateEnd != null">
            and scdf.create_date &lt;= #{request.createDateEnd}
        </if>
        <if test="request.gNo != null and request.gNo !=''">
            and scdf.g_no = #{request.gNo}
        </if>
        <if test="request.declareContractNo != null and request.declareContractNo !=''">
            and scdf.declare_contract_no = #{request.declareContractNo}
        </if>
        <if test="request.matchDateStart != null">
            and scdf.match_date &gt;= #{request.matchDateStart}
        </if>
        <if test="request.matchDateEnd != null">
            and scdf.match_date &lt;= #{request.matchDateEnd}
        </if>
    </sql>
    <select id="countByStatus" resultType="com.nsy.api.wms.response.stockout.StatusCountResponse">
        SELECT status,
               count(*) as qty
        FROM stockout_customs_declare_form
        GROUP BY status
    </select>
    <select id="findDeliveryDate" resultType="java.util.Date">
        SELECT
            stockout_shipment.delivery_date
        FROM
            stockout_shipment_amazon_relation stockout_shipment_amazon_relation
                LEFT JOIN stockout_shipment stockout_shipment ON stockout_shipment.shipment_id = stockout_shipment_amazon_relation.shipment_id
        WHERE
            stockout_shipment_amazon_relation.fba_shipment_id = #{fbaShipmentId}
        ORDER BY
            stockout_shipment.delivery_date DESC
            LIMIT 1
    </select>
    <select id="exportFormItem" resultType="com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormItemExport">
        SELECT
        scdf.declare_document_no,
        scdf.g_no,
        scdf.supplier_name,
        scdfi.input_qty,
        scdfi.input_price,
        scdfi.tax_inclusive_unit_price,
        scdfi.tax_inclusive_price,
        scdfi.tax_price,
        scdfi.input_invoice_code,
        scdfi.input_invoice_no,
        scdfi.invoice_date,
        scdf.declare_contract_no as mainDeclareContractNo,
        scdfi.declare_contract_no,
        scdf.contract_signed_date
        FROM
        stockout_customs_declare_form_item scdfi
            LEFT JOIN stockout_customs_declare_form scdf ON scdf.declare_form_id = scdfi.declare_form_id
        <where>
            <include refid="pageWhere"></include>
            and scdfi.is_deleted = 0
        </where>
        ORDER BY
            scdf.declare_form_id,
            scdf.g_no
    </select>
    <select id="countExportFormItem" resultType="java.lang.Long">
        SELECT count(*)
        FROM
        stockout_customs_declare_form_item scdfi
        LEFT JOIN stockout_customs_declare_form scdf ON scdfi.declare_form_id = scdf.declare_form_id
        <where>
            <include refid="pageWhere"></include>
            and scdfi.is_deleted = 0
        </where>
    </select>

    <select id="exportForm" resultType="com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormExport">
        SELECT
            scdf.customer,
            scdd.destination,
            DATE_FORMAT(scdf.export_invoice_date, '%Y-%m-%d') export_invoice_date,
            scdf.export_invoice_no,
            DATE_FORMAT(scdf.export_date, '%Y-%m-%d') export_date,
            DATE_FORMAT(scdf.d_date, '%Y-%m-%d') d_date,
            scdf.declare_document_no,
            scdf.protocol_no,
            scdf.export_port,
            scdf.g_no,
            scdf.g_code,
            scdf.g_name,
            scdf.declare_element,
            scdf.g_unit,
            scdf.g_qty,
            scdf.cf_price,
            scdf.apportioned_freight,
            scdf.fob_price,
            scdf.exchange_rate,
            scdf.fob_price_cny,
            scdf.box_qty,
            scdf.rough_weight,
            DATE_FORMAT(scdf.stockout_date, '%Y-%m-%d') stockout_date,
            DATE_FORMAT(scdf.stockin_date, '%Y-%m-%d') stockin_date,
            GROUP_CONCAT(scdfi.input_invoice_no SEPARATOR '/') input_invoice_no,
            GROUP_CONCAT(scdfi.input_invoice_code SEPARATOR '/') input_invoice_code,
            GROUP_CONCAT(DATE_FORMAT(scdfi.invoice_date, '%Y-%m-%d') SEPARATOR '/') invoice_date,
            scdf.supplier_name,
            GROUP_CONCAT(scdfi.input_price SEPARATOR '/') input_price,
            GROUP_CONCAT(scdfi.tax_price SEPARATOR '/') tax_price,
            scdf.declare_contract_no,
            DATE_FORMAT(scdf.contract_signed_date, '%Y-%m-%d') contract_signed_date,
            scdf.declare_batch,
            scdf.tax_inclusive_unit_price,
            scdf.tax_inclusive_price,
            scdf.supplier_id,
            scdf.match_date,
            DATE_FORMAT(scdf.logistics_invoice_date, '%Y-%m-%d') logisticsInvoiceDate,
            scdf.logistics_invoice_number,
            scdf.logistics_company,
            DATE_FORMAT(scdf.document_signing_date, '%Y-%m-%d') documentSigningDate
        FROM
            stockout_customs_declare_form scdf
                LEFT JOIN stockout_customs_declare_document scdd ON scdf.protocol_no = scdd.declare_document_no
                AND scdd.is_deleted = 0
                left join stockout_customs_declare_form_item scdfi on scdf.declare_form_id = scdfi.declare_form_id  AND scdfi.is_deleted = 0
        <where>
            <include refid="pageWhere"></include>
        </where>
        group by scdf.declare_form_id
    </select>

    <select id="pageForm" resultType="com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormResult">
        SELECT
        scdf.*
        FROM
        stockout_customs_declare_form scdf
        <where>
            <include refid="pageWhere"></include>
        </where>
        order by
            scdf.declare_document_no,
            CAST(scdf.g_no AS UNSIGNED)
    </select>

    <select id="getUnGenerateAEOList" resultType="java.lang.Integer">
        SELECT
            DISTINCT scdf.declare_form_id
        FROM
            stockout_customs_declare_form scdf
                LEFT JOIN stockout_customs_declare_document_aggregated_item scddai ON scdf.protocol_no = scddai.declare_document_no
                AND scdf.g_no = scddai.g_no
                LEFT JOIN stockout_customs_declare_document_item scddi ON scddi.declare_document_aggregated_item_id = scddai.declare_document_aggregated_item_id
                LEFT JOIN stockout_customs_declare_customer_order_item scdcoi ON scdcoi.reference_id = scddi.declare_document_item_id
        WHERE
            scdcoi.id IS NULL
    </select>
</mapper>
