package com.nsy.wms.business.manage.user.upload;

import java.math.BigDecimal;

/**
 * 物流批次导入
 *
 * <AUTHOR>
 * 2021-08-24
 */
public class StockoutLogisticsBatchImport {

    private String logisticsBatch;

    private String fbaShipmentId;

    private BigDecimal realWeight;

    // 物流总价
    private BigDecimal realPrice;

    // 物流单价
    private BigDecimal realUnitPrice;

    // 税费
    private BigDecimal realCustomsPrice;

    // 报关费用
    private BigDecimal realTaxPrice;

    /**
     * 错误信息
     */
    private String errorMsg;

    public BigDecimal getRealCustomsPrice() {
        return realCustomsPrice;
    }

    public void setRealCustomsPrice(BigDecimal realCustomsPrice) {
        this.realCustomsPrice = realCustomsPrice;
    }

    public BigDecimal getRealTaxPrice() {
        return realTaxPrice;
    }

    public void setRealTaxPrice(BigDecimal realTaxPrice) {
        this.realTaxPrice = realTaxPrice;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getLogisticsBatch() {
        return logisticsBatch;
    }

    public void setLogisticsBatch(String logisticsBatch) {
        this.logisticsBatch = logisticsBatch;
    }

    public BigDecimal getRealWeight() {
        return realWeight;
    }

    public void setRealWeight(BigDecimal realWeight) {
        this.realWeight = realWeight;
    }

    public BigDecimal getRealUnitPrice() {
        return realUnitPrice;
    }

    public void setRealUnitPrice(BigDecimal realUnitPrice) {
        this.realUnitPrice = realUnitPrice;
    }

}
