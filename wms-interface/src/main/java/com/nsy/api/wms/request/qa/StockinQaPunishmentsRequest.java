package com.nsy.api.wms.request.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-05-20 9:29
 */
public class StockinQaPunishmentsRequest {

    /**
     * 质检单id
     */
    @ApiModelProperty("质检单id")
    private Integer stockinQaOrderId;

    /**
     * 奖惩日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("罚款日期")
    private Date rewardAndPunishmentsDate;

    /**
     * 奖惩原因
     */
    @ApiModelProperty("罚款原因")
    private String rewardAndPunishmentsReason;

    /**
     * 数量(仅作为数据备注，不参与任何计算)
     */
    @ApiModelProperty("罚款件数")
    private Integer rewardAndPunishmentsCount;
    /**
     * 结算金额
     */
    @ApiModelProperty("罚款金额")
    private BigDecimal confirmTotalMoney;

    @ApiModelProperty("备注")
    private String remark;

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public Date getRewardAndPunishmentsDate() {
        return rewardAndPunishmentsDate;
    }

    public void setRewardAndPunishmentsDate(Date rewardAndPunishmentsDate) {
        this.rewardAndPunishmentsDate = rewardAndPunishmentsDate;
    }

    public String getRewardAndPunishmentsReason() {
        return rewardAndPunishmentsReason;
    }

    public void setRewardAndPunishmentsReason(String rewardAndPunishmentsReason) {
        this.rewardAndPunishmentsReason = rewardAndPunishmentsReason;
    }

    public Integer getRewardAndPunishmentsCount() {
        return rewardAndPunishmentsCount;
    }

    public void setRewardAndPunishmentsCount(Integer rewardAndPunishmentsCount) {
        this.rewardAndPunishmentsCount = rewardAndPunishmentsCount;
    }

    public BigDecimal getConfirmTotalMoney() {
        return confirmTotalMoney;
    }

    public void setConfirmTotalMoney(BigDecimal confirmTotalMoney) {
        this.confirmTotalMoney = confirmTotalMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
