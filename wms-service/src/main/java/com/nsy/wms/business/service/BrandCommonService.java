package com.nsy.wms.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.bd.BdErpSpaceMapping;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 品牌相关的公共服务类
 * @author: caishaohui
 * @time: 2024/3/13 14:12
 */
@Service
public class BrandCommonService {
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;

    /**
     * 通过品牌名获取配置
     *
     * @param brandName
     * @return
     */
    public BdErpSpaceMappingEntity getByBrandName(String brandName) {
        List<BdErpSpaceMappingEntity> brandSpace = bdErpSpaceMappingService.getBrandSpaceEntityList();
        return brandSpace.stream().filter(item -> item.getBrandName().equals(brandName)).findAny().orElse(null);
    }

    /**
     * 通过区域名获取品牌名
     *
     * @param areaName
     * @return
     */
    public String getBrandNameByAreaName(String areaName) {
        if (!StringUtils.hasText(areaName))
            return areaName;

        BdErpSpaceMappingEntity mappingEntity = bdErpSpaceMappingService.getOne(new LambdaQueryWrapper<BdErpSpaceMappingEntity>()
                .eq(BdErpSpaceMappingEntity::getAreaName, areaName)
                .last("limit 1"));
        return Objects.nonNull(mappingEntity) ? mappingEntity.getBrandName() : null;
    }


    /**
     * 通过区域id 获取品牌名
     *
     * @param areaId
     * @return
     */
    public String getBrandNameByAreaId(Integer areaId) {
        if (Objects.isNull(areaId))
            return null;

        BdErpSpaceMappingEntity mappingEntity = bdErpSpaceMappingService.getEntityByAreaId(areaId);
        return Objects.nonNull(mappingEntity) ? mappingEntity.getBrandName() : null;
    }

    /**
     * 获取品牌仓配置map
     * key = 统一库位
     * value = 品牌仓配置
     *
     * @return
     */
    public Map<String, BdErpSpaceMapping> getCommonPositionCodeMap() {
        List<BdErpSpaceMapping> brandSpace = bdErpSpaceMappingService.getBrandSpace();
        if (CollectionUtils.isEmpty(brandSpace))
            return Collections.EMPTY_MAP;
        return brandSpace.stream().collect(Collectors.toMap(BdErpSpaceMapping::getCommonPositionCode, item -> item));
    }

    /**
     * 获取品牌仓标签List
     *
     * @return
     */
    public List<String> getBrandTagNameList() {
        return bdErpSpaceMappingService.getBrandSpaceEntityList().stream().map(BdErpSpaceMappingEntity::getBrandTagName).collect(Collectors.toList());
    }

    /**
     * 获取品牌区域List
     *
     * @return
     */
    public List<String> getBrandAreaNameList() {
        return bdErpSpaceMappingService.getBrandSpaceEntityList().stream().map(BdErpSpaceMappingEntity::getAreaName).collect(Collectors.toList());
    }

    /**
     * 获取品牌标签下拉框
     *
     * @return
     */
    public List<SelectModel> getBrandLabel() {
        return bdErpSpaceMappingService.getBrandSpace().stream().map(item -> new SelectModel(item.getBrandTagName(), item.getBrandTagName())).collect(Collectors.toList());
    }


    public String getAreaNameByBrandName(String brandName) {
        if (!StringUtils.hasText(brandName))
            return null;

        BdErpSpaceMappingEntity mappingEntity = bdErpSpaceMappingService.getOne(new LambdaQueryWrapper<BdErpSpaceMappingEntity>()
                .eq(BdErpSpaceMappingEntity::getBrandName, brandName)
                .last("limit 1"));
        return Objects.nonNull(mappingEntity) ? mappingEntity.getAreaName() : null;
    }
}
