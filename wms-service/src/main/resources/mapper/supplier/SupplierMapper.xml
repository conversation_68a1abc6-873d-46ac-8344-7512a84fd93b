<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.supplier.SupplierMapper">
    <select id="getBySupplierId" resultType="com.nsy.wms.repository.entity.supplier.SupplierEntity">
        select *
        from supplier
        where supplier_id = #{supplierId}
    </select>
    
    <select id="getSupplierDepartmentInfo" resultType="com.nsy.business.base.utils.SelectModel">
        select distinct affiliate_dept_id as value,affiliate_dept_name as label
        from supplier
        where affiliate_dept_name &lt;&gt; '' and affiliate_dept_id is not null
    </select>


</mapper>
