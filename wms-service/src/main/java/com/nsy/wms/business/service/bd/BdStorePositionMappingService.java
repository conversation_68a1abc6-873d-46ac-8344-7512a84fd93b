package com.nsy.wms.business.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.bd.BdPositionInfo;
import com.nsy.api.wms.domain.bd.BdStorePositionMappingModel;
import com.nsy.api.wms.domain.stockin.RecommendParamsInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.request.bd.BdStorePositionMappingSaveRequest;
import com.nsy.api.wms.request.bd.BdStorePositionMappingSearchRequest;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdStorePositionMappingResponse;
import com.nsy.api.wms.response.bd.StoreConfigResponse;
import com.nsy.wms.business.manage.erp.ErpStockoutApiService;
import com.nsy.wms.business.manage.erp.request.StockPositionMappingRequest;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStoreDetailResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdStorePositionMappingEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdStorePositionMappingMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 店铺库位映射
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class BdStorePositionMappingService extends ServiceImpl<BdStorePositionMappingMapper, BdStorePositionMappingEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BdStorePositionMappingService.class);

    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private BdPositionService positionService;
    @Autowired
    private ErpStockoutApiService erpStockoutApiService;
    @Autowired
    private OmsApiService omsApiService;
    @Autowired
    private StockService stockService;
    @Autowired
    private BdAreaService areaService;

    public BdStorePositionMappingResponse get(Integer id) {
        BdStorePositionMappingResponse bdStorePositionMappingResponse = new BdStorePositionMappingResponse();
        BdStorePositionMappingModel bdStorePositionMapping = new BdStorePositionMappingModel();
        BdStorePositionMappingEntity bdStorePositionMappingEntity = getById(id);
        if (bdStorePositionMappingEntity == null) {
            throw new BusinessServiceException("店铺库位映射-找不到" + id + "id记录");
        }
        BeanUtilsEx.copyProperties(bdStorePositionMappingEntity, bdStorePositionMapping);
        bdStorePositionMappingResponse.setBdStorePositionMapping(bdStorePositionMapping);
        return bdStorePositionMappingResponse;
    }

    public StoreConfigResponse getStoreInfo(Integer id) {
        SaStoreDetailResponse storeInfo = omsApiService.getStoreInfoByStoreId(id);
        StoreConfigResponse response = new StoreConfigResponse();
        response.setBusinessType(storeInfo.getDepartment());
        response.setStoreName(storeInfo.getStoreName());
        response.setAccountProperties1(storeInfo.getExtendedAttributes0ne());
        response.setAccountProperties2(storeInfo.getExtendedAttributesTwo());
        response.setAccountProperties3(storeInfo.getExtendedAttributesThree());
        return response;
    }

    public PageResponse<BdStorePositionMappingModel> getPageList(BdStorePositionMappingSearchRequest request) {
        PageResponse<BdStorePositionMappingModel> pageResponse = new PageResponse<>();
        LambdaQueryWrapper<BdStorePositionMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(request.getPositionCode()), BdStorePositionMappingEntity::getPositionCode, request.getPositionCode())
                .eq(request.getStoreId() != null, BdStorePositionMappingEntity::getStoreId, request.getStoreId())
                .eq(StringUtils.hasText(request.getBusinessType()), BdStorePositionMappingEntity::getBusinessType, request.getBusinessType())
                .in(!CollectionUtils.isEmpty(request.getBusinessTypeList()), BdStorePositionMappingEntity::getBusinessType, request.getBusinessTypeList())
                .in(!CollectionUtils.isEmpty(request.getStoreIdList()), BdStorePositionMappingEntity::getStoreId, request.getStoreIdList());
        queryWrapper.orderByDesc(BdStorePositionMappingEntity::getId);
        Page<BdStorePositionMappingEntity> page = page(new Page<>(request.getPageIndex(), request.getPageSize()), queryWrapper);
        List<BdStorePositionMappingEntity> records = page.getRecords();
        List<BdStorePositionMappingModel> resultList = records.stream().map(entity -> {
            BdStorePositionMappingModel model = new BdStorePositionMappingModel();
            BeanUtilsEx.copyProperties(entity, model);
            if (StringUtils.hasText(model.getBusinessType())) {
                model.setBusinessTypeLabel(model.getBusinessType());
            }
            return model;
        }).collect(Collectors.toList());
        pageResponse.setContent(resultList);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    @Transactional
    public void createBdStorePositionMapping(BdStorePositionMappingSaveRequest request) {
        BdStorePositionMappingEntity bdStorePositionMappingEntity = new BdStorePositionMappingEntity();
        BdPositionEntity positionByCode = positionService.getPositionByCode(request.getPositionCode());
        if (positionByCode == null) {
            throw new BusinessServiceException("无法在仓库中找到库位编号，请核对：" + request.getPositionCode());
        }
        if (!positionByCode.getAreaName().contains("活动") && !BdPositionTypeEnum.STORE_POSITION.name().equals(positionByCode.getPositionType())) {
            throw new BusinessServiceException("该库位不属于活动区域或店铺库位，请核对：" + request.getPositionCode());
        }
        //校验：店铺库位只允许绑定一个店铺
        if (BdPositionTypeEnum.STORE_POSITION.name().equals(positionByCode.getPositionType())
                && count(new LambdaQueryWrapper<BdStorePositionMappingEntity>()
                .eq(BdStorePositionMappingEntity::getPositionCode, request.getPositionCode())) > 0) {
            throw new BusinessServiceException("该库位已绑定店铺，请勿重复添加");
        }

        BeanUtilsEx.copyProperties(request, bdStorePositionMappingEntity, "id");
        bdStorePositionMappingEntity.setCreateBy(loginInfoService.getName());
        if (!StringUtils.hasText(bdStorePositionMappingEntity.getBusinessType())) {
            throw new BusinessServiceException("部门不能为空，请核对");
        }


        validRepeat(bdStorePositionMappingEntity);
        save(bdStorePositionMappingEntity);
        if (bdStorePositionMappingEntity.getStoreId() != null) {
            StockPositionMappingRequest erpRequest = new StockPositionMappingRequest();
            erpRequest.setPositionCode(bdStorePositionMappingEntity.getPositionCode());
            erpRequest.setDisAInfoId(bdStorePositionMappingEntity.getStoreId());
            erpRequest.setOperator(loginInfoService.getName());
            erpRequest.setDeliveryLocation(TenantContext.getTenant());
            try {
                erpStockoutApiService.addStorePositionMapping(erpRequest);
            } catch (Exception e) {
                if (!(StringUtils.hasText(e.getMessage()) && e.getMessage().contains("该店铺和库位已经存在映射")))
                    throw e;
            }
        }
    }

    private void validRepeat(BdStorePositionMappingEntity entity) {
        if (!StringUtils.hasText(entity.getBusinessType()) && entity.getStoreId() == null) {
            throw new BusinessServiceException("请选择店铺来绑定库位");
        }
        if (entity.getStoreId() != null) {
            LambdaQueryWrapper<BdStorePositionMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BdStorePositionMappingEntity::getPositionCode, entity.getPositionCode())
                    .eq(BdStorePositionMappingEntity::getStoreId, entity.getStoreId());
            List<BdStorePositionMappingEntity> list = list(queryWrapper);
            if (!CollectionUtils.isEmpty(list)) {
                throw new BusinessServiceException("已存在对应的店铺和库位，请勿重复添加");
            }
        }
    }

    @Transactional
    public void updateBdStorePositionMapping(BdStorePositionMappingSaveRequest request) {
        if (request.getId() == null) {
            throw new BusinessServiceException("id不能为空");
        }
        BdStorePositionMappingEntity entity = getById(request.getId());
        BdPositionEntity positionByCode = positionService.getPositionByCode(request.getPositionCode());
        if (positionByCode == null) {
            throw new BusinessServiceException("无法在仓库中找到库位编号，请核对：" + request.getPositionCode());
        }
        if (!positionByCode.getAreaName().contains("活动")) {
            throw new BusinessServiceException("该库位不属于活动，请核对：" + request.getPositionCode());
        }
        boolean validRepeat = false;
        if (!request.getPositionCode().equals(entity.getPositionCode()) || !request.getStoreId().equals(entity.getStoreId())) {
            validRepeat = true;
        }
        BeanUtilsEx.copyProperties(request, entity);
        entity.setUpdateBy(loginInfoService.getName());
        if (validRepeat) {
            validRepeat(entity);
        }
        updateById(entity);
    }

    @Transactional
    public void deleteByIds(IdListRequest request) {
        request.getIdList().forEach(this::deleteById);
    }

    private void deleteById(Integer id) {
        BdStorePositionMappingEntity entity = this.getById(id);
        if (entity.getStoreId() != null) {
            StockPositionMappingRequest request = new StockPositionMappingRequest();
            request.setPositionCode(entity.getPositionCode());
            request.setDisAInfoId(entity.getStoreId());
            request.setOperator(loginInfoService.getName());
            request.setDeliveryLocation(entity.getLocation());
            erpStockoutApiService.deleteStorePositionMapping(request);
        }
        this.removeById(id);
    }

    public List<BdPositionInfo> matchStorePosition(RecommendParamsInfo matchInfo) {
        LOGGER.info("开始匹配店铺库位：{}", JsonMapper.toJson(matchInfo));
        if (matchInfo.getAreaId() == null)
            return Collections.emptyList();
        // 没有店铺，且没有部门
        if ((matchInfo.getStoreId() == null || matchInfo.getStoreId() == 0) && !StringUtils.hasText(matchInfo.getBusinessType()))
            return Collections.emptyList();
        BdAreaEntity areaEntity = areaService.getById(matchInfo.getAreaId());
        // 不是活动区域的，不用匹配
        if (areaEntity == null || !StringUtils.hasText(areaEntity.getAreaName()) || !areaEntity.getAreaName().contains("活动"))
            return Collections.emptyList();
        // 先匹配店铺，后部门，如果都没就返回空
        List<BdStorePositionMappingEntity> list = new ArrayList<>();
        LambdaQueryWrapper<BdStorePositionMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (matchInfo.getStoreId() != null && matchInfo.getStoreId() != 0) {
            queryWrapper.eq(BdStorePositionMappingEntity::getStoreId, matchInfo.getStoreId());
            list = list(queryWrapper);
        }
        if (CollectionUtils.isEmpty(list) && StringUtils.hasText(matchInfo.getBusinessType())) {
            queryWrapper.clear();
            queryWrapper.eq(BdStorePositionMappingEntity::getBusinessType, matchInfo.getBusinessType());
            list = list(queryWrapper);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<BdPositionInfo> result = new ArrayList<>();
        Set<String> positionSet = new HashSet<>();
        list.forEach(entity -> {
            BdPositionEntity positionByCode = positionService.getPositionByCode(entity.getPositionCode());
            if (positionByCode != null && !positionSet.contains(positionByCode.getPositionCode())) {
                LambdaQueryWrapper<StockEntity> stockWrapper = new LambdaQueryWrapper<>();
                stockWrapper.eq(StockEntity::getSpecId, matchInfo.getSpecId());
                stockWrapper.eq(StockEntity::getPositionCode, positionByCode.getPositionCode());
                List<StockEntity> stockEntities = stockService.list(stockWrapper);
                BdPositionInfo positionInfo = new BdPositionInfo();
                BeanUtilsEx.copyProperties(positionByCode, positionInfo);
                positionInfo.setSpecId(matchInfo.getSpecId());
                positionInfo.setStock(CollectionUtils.isEmpty(stockEntities) || stockEntities.get(0).getStock() == null ? Integer.valueOf(0) : stockEntities.get(0).getStock());
                result.add(positionInfo);
                positionSet.add(positionByCode.getPositionCode());
            }
        });
        return result;
    }


    public List<String> listPositionByStoreId(Integer storeId) {
        return this.list(new LambdaQueryWrapper<BdStorePositionMappingEntity>()
                        .select(BdStorePositionMappingEntity::getPositionCode)
                        .eq(BdStorePositionMappingEntity::getStoreId, storeId)
                        .eq(BdStorePositionMappingEntity::getIsDeleted, 0))
                .stream().map(BdStorePositionMappingEntity::getPositionCode).distinct().collect(Collectors.toList());
    }

    /**
     * 获取库位对应的店铺信息
     *
     * @param positionCode
     * @return
     */
    public Integer getStoreIdByPositionCode(String positionCode) {
        BdStorePositionMappingEntity mappingInfo = this.getOne(new LambdaQueryWrapper<BdStorePositionMappingEntity>()
                .eq(BdStorePositionMappingEntity::getPositionCode, positionCode)
                .eq(BdStorePositionMappingEntity::getIsDeleted, 0)
                .last("limit 1"));
        if (Objects.isNull(mappingInfo)) {
            return null;
        }
        return mappingInfo.getStoreId();
    }
}
