package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTask;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItem;
import com.nsy.api.wms.enumeration.ProductSizeSegmentEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderScanTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingScanningStationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskTypeEnum;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdTagMappingEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutReceiverInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderScanTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutReceiverInfoMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper;
import com.nsy.wms.utils.WmsDateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StockoutOrderScanCheckInfoService {

    @Autowired
    StockoutOrderLackService stockoutOrderLackService;
    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    StockoutOrderScanTaskItemMapper scanTaskItemMapper;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    StockoutOrderScanLogService scanLogService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutReceiverInfoService receiverInfoService;
    @Autowired
    StockoutReceiverInfoMapper receiverInfoMapper;
    @Autowired
    StockoutShipmentItemMapper shipmentItemMapper;
    @Resource
    BdTagMappingService bdTagMappingService;
    @Resource
    StockoutOrderTemuExtendService stockoutOrderTemuExtendService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    StockoutVasTaskService stockoutVasTaskService;


    public StockoutOrderScanTask getStockoutOrderScanTask(StockoutOrderScanTaskEntity taskEntity, StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> stockoutOrderItemEntityList) {
        List<StockoutOrderScanTaskItem> itemList = scanTaskItemMapper.searchTaskItem(taskEntity.getTaskId());
        if (itemList.isEmpty())
            throw new BusinessServiceException("未找到对应的复核任务sku信息");
        List<String> orderNoList = stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.toList());
        StockoutOrderScanTask scanTask = new StockoutOrderScanTask();
        buildBaseInfo(taskEntity, stockoutOrderEntity, orderNoList, scanTask);
        Set<String> transparencySet = new HashSet<>(32);
        List<String> skuList = new ArrayList<>();
        stockoutOrderItemEntityList.forEach(i -> {
            if (stockoutOrderItemService.validTransparency(stockoutOrderEntity.getStockoutOrderNo(), Collections.singletonList(i)))
                transparencySet.add(i.getOrderItemId());
            skuList.add(i.getSku());
        });
        scanTask.setIsTransparency(!CollectionUtils.isEmpty(transparencySet));
        scanTask.setIsLabelOrder(bdTagMappingService.validWithLabelOrder(stockoutOrderEntity.getStockoutOrderNo()));
        Map<String, List<String>> skuTagMap = stockoutOrderEntity.getWorkspace().contains("FBA") ? bdTagMappingService.getProductTagBySkus(skuList) : Collections.emptyMap();
        Map<String, String> vasTaskMap = stockoutVasTaskService.getVasTaskMap(Collections.singletonList(stockoutOrderEntity.getStockoutOrderNo()));
        itemList.forEach(it -> {
            it.setProductTag(skuTagMap.getOrDefault(it.getSku(), Collections.emptyList()));
            it.setIsTransparency(transparencySet.contains(it.getOrderItemId()));
            it.setVasType(vasTaskMap.getOrDefault(it.getSku(), ""));
        });
        // 增值类型
        buildVasInfo(itemList, stockoutOrderEntity.getStockoutOrderId());
        // 对明细进行排序
        this.sortItemList(itemList, stockoutOrderEntity);
        scanTask.setScanTaskItemList(itemList);
        scanTask.setPlatformName(stockoutOrderEntity.getPlatformName());
        scanTask.setOriginType(stockoutOrderEntity.getOriginType());
        // 待复核 -》复核中
        updateTaskReviewing(taskEntity, stockoutOrderEntity);

        // 是否有合并发货的出库单
//        scanTask.setNotice(getMergeStockOutOrderInfo(stockoutOrderEntity));
        getMergeStockOutOrderInfo(stockoutOrderEntity, scanTask);
        shipmentItemService.getLastShipmentBoxCode(stockoutOrderEntity, scanTask);

        //赋值品牌信息
        List<BdTagMappingEntity> mappingEntityList = bdTagMappingService.getBrandTageByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (!CollectionUtils.isEmpty(mappingEntityList)) scanTask.setBrandName(mappingEntityList.get(0).getTagName());
        //组装标签信息
        this.buildLabelInfo(scanTask);

        StockoutBatchOrderEntity stockoutBatchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        scanTask.setOverTime(WmsDateUtils.dateDiffMinutesAndSeconds(stockoutBatchOrderEntity.getCreateDate().getTime(), System.currentTimeMillis()));
        return scanTask;
    }

    public void updateTaskReviewing(StockoutOrderScanTaskEntity taskEntity, StockoutOrderEntity stockoutOrderEntity) {
        if (taskEntity.getStatus().equals(StockoutOrderScanTaskStatusEnum.WAIT_REVIEW.name())) {
            taskEntity.setStatus(StockoutOrderScanTaskStatusEnum.REVIEWING.name());
            taskEntity.setUpdateBy(loginInfoService.getName());
            taskEntity.setOperateStartDate(new Date());
            taskEntity.setOperator(loginInfoService.getName());
            scanTaskService.updateById(taskEntity);
            // 日志记录
            scanLogService.addScanLog(taskEntity.getTaskId(), "开始复核", "开始复核装箱", loginInfoService.getName());
            // 出库单日志 不允许回退状态
            stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.OUTBOUNDING.name());
            stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.OUTBOUNDING, String.format("开始扫描核单，扫描台【%s】", StockoutSortingScanningStationEnum.getNameBy(taskEntity.getScanType())));
        }
    }

    /**
     * 对明细进行排序
     * 当存在多个相同sku明细时，根据拣货箱数量（通过来源库位判断），多的往前排
     *
     * @param itemList
     * @param stockoutOrderEntity
     * @return
     */
    public void sortItemList(List<StockoutOrderScanTaskItem> itemList, StockoutOrderEntity stockoutOrderEntity) {
        StockoutBatchOrderEntity batchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (batchOrderEntity == null)
            throw new BusinessServiceException(String.format("出库单【%s】未找到波次", stockoutOrderEntity.getStockoutOrderNo()));
        StockoutBatchEntity stockoutBatch = stockoutBatchService.getStockoutBatchById(batchOrderEntity.getBatchId());
        List<Integer> batchIds = new LinkedList<>();
        batchIds.add(batchOrderEntity.getBatchId());
        if (stockoutBatch.getMergeBatchId() != null)
            batchIds.add(stockoutBatch.getMergeBatchId());
        Map<String, List<StockoutOrderScanTaskItem>> collect = itemList.stream().collect(Collectors.groupingBy(StockoutOrderScanTaskItem::getSku));
        collect.values().forEach(list -> {
            if (list.size() > 1 && Objects.nonNull(list.get(0).getStockoutOrderItemId())) {
                // 拣货箱明细
                List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchIdsAndSku(batchIds, StockInternalBoxTypeEnum.PICKING_BOX.name(), list.get(0).getSku());
                list.forEach(item -> {
                    StockoutOrderItemEntity orderItemEntity = stockoutOrderItemService.getOne(new LambdaQueryWrapper<StockoutOrderItemEntity>()
                            .select(StockoutOrderItemEntity::getPositionCode, StockoutOrderItemEntity::getIsNeedProcess)
                            .eq(StockoutOrderItemEntity::getStockoutOrderItemId, item.getStockoutOrderItemId()));
                    List<StockInternalBoxItemEntity> boxItemEntities;
                    if (StockConstant.ENABLE.equals(orderItemEntity.getIsNeedProcess())) {
                        //加工取来源为加工发货库位明细
                        boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> ProcessConstant.AFTER_PROCESS_POSITION_CODE.equals(boxItem.getSourcePositionCode())).collect(Collectors.toList());
                    } else {
                        //非加工款按区域取箱内数
                        BdPositionEntity positionByCode = bdPositionService.getPositionByCode(orderItemEntity.getPositionCode());
                        boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> positionByCode.getAreaId().equals(boxItem.getSourceAreaId())).collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(boxItemEntities))
                        //赋值箱内数
                        item.setBoxQty(boxItemEntities.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum());

                });
            }
        });
        //按照箱内数量由大到小排列
        itemList.sort(Comparator.comparing(StockoutOrderScanTaskItem::getSku).thenComparing(StockoutOrderScanTaskItem::getBoxQty).reversed());
    }

    public void buildVasInfo(List<StockoutOrderScanTaskItem> itemList, Integer stockoutOrderId) {
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderId)
                .stream().filter(o -> StringUtils.hasText(o.getVasType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderItemEntityList)) {
            return;
        }
        itemList.forEach(item -> {
            StockoutOrderItemEntity curItem = stockoutOrderItemEntityList.stream().filter(o -> o.getOrderItemId().equals(item.getOrderItemId())).findFirst().orElse(null);
            if (curItem != null) {
                item.setVasType(StockoutVasTaskTypeEnum.getVasTypeStr(curItem.getVasType()));
            }
        });
    }

    private void buildBaseInfo(StockoutOrderScanTaskEntity taskEntity, StockoutOrderEntity stockoutOrderEntity, List<String> orderNoList, StockoutOrderScanTask scanTask) {
        BeanUtils.copyProperties(taskEntity, scanTask);
        scanTask.setDescription(stockoutOrderEntity.getDescription());
        scanTask.setPickingType(stockoutOrderEntity.getPickingType());
        scanTask.setOrderNos(StringUtils.join(orderNoList, '，'));
        scanTask.setHasPack(stockoutOrderEntity.getHasPack());
        scanTask.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
        scanTask.setStockoutType(stockoutOrderEntity.getStockoutType());
        scanTask.setProductSizeSegment(ProductSizeSegmentEnum.getCn(stockoutOrderEntity.getProductSizeSegment()));
    }

    private void getMergeStockOutOrderInfo(StockoutOrderEntity stockoutOrderEntity, StockoutOrderScanTask scanTask) {
        if (stockoutOrderEntity.getLatestDeliveryDate() == null)
            return;
        // 当前出库单的收货信息
        StockoutReceiverInfoEntity receiverInfoEntity = receiverInfoService.getOne(new QueryWrapper<StockoutReceiverInfoEntity>().lambda()
                .eq(StockoutReceiverInfoEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId()));
        if (receiverInfoEntity == null) {
            return;
        }
        // 查找相同收货地址，并且预发货日期相同
        List<String> orderNoList = receiverInfoMapper.searchMergeOrderNoList(receiverInfoEntity.getReceiverInfoMd5(), stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderEntity.getLatestDeliveryDate());
        if (orderNoList.isEmpty())
            return;
        List<String> shipmentBoxCodeList = shipmentItemMapper.searchShipmentBoxCodeList(stockoutOrderEntity.getStockoutOrderNo(), orderNoList);
        scanTask.setSameReceiverOrder(String.format("需要同时发货的订单，订单号【%s】，若需要合并装箱请扫描箱子编号：【%s】", StringUtils.join(orderNoList, ','), StringUtils.join(shipmentBoxCodeList, ',')));
    }

    private void buildLabelInfo(StockoutOrderScanTask scanTask) {
        List<String> labelList = new ArrayList<>();
        if (scanTask.getIsTransparency()) {
            labelList.add("亚马逊透明计划");
        }
        if (scanTask.getIsLabelOrder()) {
            labelList.add("有标单");
        }
        if (StringUtils.hasText(scanTask.getBrandName())) {
            labelList.add(scanTask.getBrandName());
        }
        if (StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(scanTask.getStockoutType()) && 65 == scanTask.getSpaceId()) {
            if (scanTask.getHasPack()) {
                labelList.add("谷仓海外仓PACK");
            } else {
                labelList.add("谷仓海外仓普");
            }
        }
        if (!CollectionUtils.isEmpty(scanTask.getScanTaskItemList()) && 1 == scanTask.getScanTaskItemList().get(0).getIsFirstOrderByStore()) {
            labelList.add("首单");
        }
        scanTask.setLabelList(labelList);
    }
}
