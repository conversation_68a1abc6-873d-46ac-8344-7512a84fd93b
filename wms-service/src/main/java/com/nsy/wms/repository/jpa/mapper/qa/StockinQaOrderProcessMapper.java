package com.nsy.wms.repository.jpa.mapper.qa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * @author: caish<PERSON><PERSON>
 * @version: v1.0
 * @description: 质检单流程表Mapper
 * @date: 2024-11-18 15:58
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinQaOrderProcessMapper extends BaseMapper<StockinQaOrderProcessEntity> {

    List<StockinQaOrderProcessEntity> getProcessInfoByInfo(@Param("qaOrderIdList") List<Integer> qaOrderIdList, @Param("processName") String processName);
}
