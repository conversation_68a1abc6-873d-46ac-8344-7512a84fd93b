
package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: caishao<PERSON>
 * @time: 2024/11/18 11:42
 */
public class StockinQaOrderPageExport {

    /**
     * stockinQaOrderId
     */
    @ApiModelProperty("质检单Id")
    private Integer stockinQaOrderId;
    /**
     * 内部箱号
     */
    @ApiModelProperty("内部箱号")
    private String internalBoxCode;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String spu;
    /**
     * skc
     */
    @ApiModelProperty("颜色编码")
    private String skc;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String sku;

    @ApiModelProperty("商品标签")
    private String skuType;


    @ApiModelProperty("箱内数量")
    private Integer boxQty;

    @ApiModelProperty("到货数量")
    private Integer arrivalCount;

    /**
     * 质检数量
     */
    @ApiModelProperty("质检数量")
    private Integer testTotalCount;
    /**
     * 质检不合格件数
     */
    @ApiModelProperty("质检不合格件数")
    private Integer unqualifiedCount;
    /**
     * 合格件数
     */
    @ApiModelProperty("合格件数")
    private Integer qualifiedCount;

    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    private Integer returnCount;
    /**
     * 让步接收数
     */
    @ApiModelProperty("让步接收数")
    private Integer concessionsCount;

    /**
     * 不合格原因归类
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;
    /**
     * 质检不合格的问题描述
     */
    @ApiModelProperty("质检不合格的问题描述")
    private String unqualifiedQuestion;
    /**
     * 不合格原因
     */
    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;


    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("次要不合格的问题描述")
    private String unqualifiedQuestionSecondary;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("次要不合格原因归类")
    private String unqualifiedCategorySecondary;

    @ApiModelProperty("次要不合格原因")
    private String unqualifiedReasonSecondary;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    private String processStatus;

    @ApiModelProperty("质检流程状态")
    private String processStatusStr;

    private String result;

    @ApiModelProperty("质检结果")
    private String resultStr;

    @ApiModelProperty("质检人员")
    private String qcUserName;

    @ApiModelProperty("初审人员")
    private String firstAuditUserName;

    @ApiModelProperty("复审人员")
    private String secondAuditUserName;

    @ApiModelProperty("跟单QC")
    private String lastQcUserRealName;

    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stockinDate;

    @ApiModelProperty(value = "质检开始时间", name = "qcStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date qcStartDate;

    @ApiModelProperty(value = "质检完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeDate;

    @ApiModelProperty(value = "初审完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstAuditEndDate;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;

    /**
     * 商品图片地址
     */
    @ApiModelProperty(value = "商品图片地址", name = "imageUrl")
    private String imageUrl;
    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    /**
     * 预览图地址
     */
    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    @ApiModelProperty(value = "采购部门", name = "department")
    private String department;

    @ApiModelProperty(value = "品类1", name = "categoryOne")
    private String categoryOne;
    @ApiModelProperty(value = "品类2", name = "categoryTwo")
    private String categoryTwo;
    @ApiModelProperty(value = "品类3", name = "categoryThree")
    private String categoryThree;


    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public Integer getQualifiedCount() {
        return qualifiedCount;
    }

    public void setQualifiedCount(Integer qualifiedCount) {
        this.qualifiedCount = qualifiedCount;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSkuType() {
        return skuType;
    }

    public void setSkuType(String skuType) {
        this.skuType = skuType;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getTestTotalCount() {
        return testTotalCount;
    }

    public void setTestTotalCount(Integer testTotalCount) {
        this.testTotalCount = testTotalCount;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getProcessStatusStr() {
        return processStatusStr;
    }

    public void setProcessStatusStr(String processStatusStr) {
        this.processStatusStr = processStatusStr;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getResultStr() {
        return resultStr;
    }

    public void setResultStr(String resultStr) {
        this.resultStr = resultStr;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public String getFirstAuditUserName() {
        return firstAuditUserName;
    }

    public void setFirstAuditUserName(String firstAuditUserName) {
        this.firstAuditUserName = firstAuditUserName;
    }

    public String getSecondAuditUserName() {
        return secondAuditUserName;
    }

    public void setSecondAuditUserName(String secondAuditUserName) {
        this.secondAuditUserName = secondAuditUserName;
    }

    public String getLastQcUserRealName() {
        return lastQcUserRealName;
    }

    public void setLastQcUserRealName(String lastQcUserRealName) {
        this.lastQcUserRealName = lastQcUserRealName;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getCategoryOne() {
        return categoryOne;
    }

    public void setCategoryOne(String categoryOne) {
        this.categoryOne = categoryOne;
    }

    public String getCategoryTwo() {
        return categoryTwo;
    }

    public void setCategoryTwo(String categoryTwo) {
        this.categoryTwo = categoryTwo;
    }

    public String getCategoryThree() {
        return categoryThree;
    }

    public void setCategoryThree(String categoryThree) {
        this.categoryThree = categoryThree;
    }

    public String getUnqualifiedQuestionSecondary() {
        return unqualifiedQuestionSecondary;
    }

    public void setUnqualifiedQuestionSecondary(String unqualifiedQuestionSecondary) {
        this.unqualifiedQuestionSecondary = unqualifiedQuestionSecondary;
    }

    public String getUnqualifiedCategorySecondary() {
        return unqualifiedCategorySecondary;
    }

    public void setUnqualifiedCategorySecondary(String unqualifiedCategorySecondary) {
        this.unqualifiedCategorySecondary = unqualifiedCategorySecondary;
    }

    public String getUnqualifiedReasonSecondary() {
        return unqualifiedReasonSecondary;
    }

    public void setUnqualifiedReasonSecondary(String unqualifiedReasonSecondary) {
        this.unqualifiedReasonSecondary = unqualifiedReasonSecondary;
    }

    public Date getQcStartDate() {
        return qcStartDate;
    }

    public void setQcStartDate(Date qcStartDate) {
        this.qcStartDate = qcStartDate;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public Date getFirstAuditEndDate() {
        return firstAuditEndDate;
    }

    public void setFirstAuditEndDate(Date firstAuditEndDate) {
        this.firstAuditEndDate = firstAuditEndDate;
    }
}
