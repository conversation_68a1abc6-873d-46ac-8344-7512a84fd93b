package com.nsy.api.wms.enumeration;

/**
 * 命名规则：服务名_业务名
 */
public enum QuartzUploadQueueTypeEnum {
    // 仓库系统-库位导入
    WMS_POSITION_IMPORT,
    // 仓库系统-商品报关导入
    WMS_PRODUCT_DECLARE,
    // 仓库系统-商品报关导入维护
    WMS_PRODUCT_DECLARE_MAINTAIN,
    // 仓库系统-装箱清单-导入物流单号
    WMS_STOCKOUT_SHIPMENT_LOGISTICS_NO_IMPORT,
    // 仓库系统-装箱清单-更新物流单号
    WMS_STOCKOUT_SHIPMENT_LOGISTICS_NO_UPDATE_IMPORT,
    // 仓库系统-装箱清单-越库装箱导入
    WMS_STOCKOUT_SHIPMENT_OUT_IMPORT,
    // 仓库系统-上架规则-分类导入
    WMS_BD_SHELVE_RULE_PRODUCT_CATEGORY,
    // 仓库系统-客户商品
    WMS_PRODUCT_STORE_SKU_IMPORT,
    // 仓库系统-退货单-导入
    WMS_RETURN_PRODUCT_ORDER_IMPORT,
    // 仓库系统-库内调拨-普通调拨导入
    WMS_STOCK_GENERAL_ALLOCATION_IMPORT,
    // 仓库系统-库内调拨-清库调拨导入
    WMS_STOCK_CLEARING_ALLOCATION_IMPORT,
    // 仓间调拨-仓间调拨计划导入
    WMS_STOCK_TRANSFER_CROSS_SPACE_IMPORT,
    // 仓间调拨-清仓出库计划导入
    WMS_STOCK_TRANSFER_CLEAR_IMPORT,
    // 仓库系统-上架任务清单上传
    WMS_STOCKIN_SHELVE_TASK_ITEM_IMPORT,
    // 仓库系统-导入临时盘点任务
    WMS_TEMPORARY_TAKE_STOCK_TASK,
    // 惠州仓上架
    WMS_STOCKOUT_HUI_ZHOU_SPACE_SHELVED,
    // 报关单据列表-导入更新
    WMS_CUSTOMS_DECLARE_DOCUMENT_UPDATE,
    // 批次费用核对
    WMS_LOGISTICS_BATCH,
    // 批次费用核对-外部订单
    WMS_LOGISTICS_BATCH_OTHER_ORDER,
    // 批次费用核对-导入单价
    WMS_LOGISTICS_BATCH_PRICE,
    // tag商品导入
    WMS_TAG_PRODUCT_IMPORT,
    // 仓库系统-店铺库位映射
    WMS_STORE_POSITION_MAPPING_IMPORT,
    // 仓库系统-盘点单-导入
    WMS_STORE_TAKE_ORDER_IMPORT,
    //通用分拣上传
    WMS_UNIVERSAL_SORTING_TASK_IMPORT,
    // 仓内调拨 - 导入
    WMS_STOCK_TRANSFER_IN_SPACE_IMPORT,
    // wms-关单匹配供应商导入
    WMS_DECLARE_FORM_MATCH_SUPPLIER_IMPORT,
    // 装箱导入
    WMS_STOCKOUT_SHIPMENT_IMPORT,
    //关单冲库存
    WMS_CUSTOMS_DECLARE_FORM_ITEM,
    //进项发票信息批量导入
    WMS_CUSTOMS_DECLARE_FORM_ITEM_INVOICE,
    //物流发票信息批量导入
    WMS_CUSTOMS_DECLARE_FORM_LOGISTICS_INVOICE,
    //沃尔玛1p订单导入
    WALMART_FIRST_PARTY_ORDER_IMPORT,
    //自动上架规则导入
    WMS_BD_STOCKIN_AUTO_SHELVE_RULE_IMPORT,
    // 仓库系统-亚马逊装箱清单-导入物流单号
    WMS_AMAZON_SHIPMENT_LOGISTICS_NO_IMPORT,
    //申报批次导入
    WMS_CUSTOMS_DECLARE_FORM_DECLARE_BATCH,
    //出口发票导入
    WMS_CUSTOMS_DECLARE_FORM_EXPORT_INVOICE
}
