package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.repository.entity.qa.StockinQaProductSampleSkuTypeEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaProductSampleSkuTypeMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/2 15:59
 */
@Service
public class StockinQaProductSampleSkuTypeService extends ServiceImpl<StockinQaProductSampleSkuTypeMapper, StockinQaProductSampleSkuTypeEntity> {
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;

    public void saveLabelInfo(String interBoxCode, String sku, Integer recordId) {
        //获取所有采购标签
        //查询标签
        List<String> itemTypes = stockInternalBoxItemService.getLabelAttributeNames(interBoxCode, sku);
        if (CollectionUtils.isEmpty(itemTypes)) {
            return;
        }
        List<StockinQaProductSampleSkuTypeEntity> skuTypeInfoEntityList = new ArrayList<>();
        itemTypes.forEach(detail -> {
            if (this.existItem(detail, recordId)) {
                return;
            }
            StockinQaProductSampleSkuTypeEntity entity = new StockinQaProductSampleSkuTypeEntity();
            entity.setRecordId(recordId);
            entity.setLocation(TenantContext.getTenant());
            entity.setCreateBy(loginInfoService.getName());
            entity.setUpdateBy(loginInfoService.getName());
            entity.setSkuType(detail);
            skuTypeInfoEntityList.add(entity);
        });
        this.saveBatch(skuTypeInfoEntityList);
    }

    public boolean existItem(String skuType, Integer recordId) {
        LambdaQueryWrapper<StockinQaProductSampleSkuTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaProductSampleSkuTypeEntity::getSkuType, skuType);
        queryWrapper.eq(StockinQaProductSampleSkuTypeEntity::getRecordId, recordId);
        return this.count(queryWrapper) > 0;
    }

    public Map<Integer, List<String>> getMapByQcRoutingInspectId(List<Integer> recordIdList) {
        if (CollectionUtils.isEmpty(recordIdList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<StockinQaProductSampleSkuTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockinQaProductSampleSkuTypeEntity::getRecordId, recordIdList);
        List<StockinQaProductSampleSkuTypeEntity> skuTypeInfoList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(skuTypeInfoList)) {
            return Collections.emptyMap();
        }
        return skuTypeInfoList.stream().collect(Collectors.groupingBy(
            StockinQaProductSampleSkuTypeEntity::getRecordId,
            Collectors.mapping(StockinQaProductSampleSkuTypeEntity::getSkuType, Collectors.toList())
        ));
    }

    /**
     * 校验是否新款
     *
     * @param interBoxCode
     * @param sku
     * @return
     */
    public boolean validNewOrder(String interBoxCode, String sku) {
        //查询标签
        //List<String> itemTypes = stockInternalBoxItemService.getLabelAttributeNames(interBoxCode, sku);
        //查询标签
        return stockInternalBoxItemService.getBaseMapper().countNewStockinItem(interBoxCode, sku) > 0;
        //包含新品首单 或 新款标签
        //return StockinUtils.isNew(itemTypes, isFirstOrderLabel);
    }
}
