<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockMapper">
    <select id="pageSearchStock" resultType="com.nsy.api.wms.domain.stock.Stock">

        select
        s.stock_id AS stockId,
        s.sku AS sku,
        s.spec_id AS specId,
        s.product_id AS productId,
        s.space_name AS spaceName,
        s.space_area_name AS spaceAreaName,
        s.position_code AS positionCode,
        s.internal_box_id AS internalBoxId,
        s.stock AS stock,
        s.is_lock AS `lock`,
        s.description AS description
        <include refid="stockWhere"></include>
        order by ${sortSql}
        limit #{pageIndex},#{pageSize}

    </select>

    <select id="pageSearchStockExport" resultType="com.nsy.api.wms.domain.stock.Stock">
        SELECT
        ss.stock_id AS stockId,
        ss.sku AS sku,
        ss.spec_id AS specId,
        ss.product_id AS productId,
        ss.space_name AS spaceName,
        ss.space_area_name AS spaceAreaName,
        ss.position_code AS positionCode,
        ss.internal_box_id AS internalBoxId,
        ss.stock AS stock,
        ss.is_lock AS `lock`,
        ss.description AS description,
        bp.position_type AS positionType
        FROM
        stock ss
        inner join(
        select s.stock_id
        <include refid="stockWhere"></include>
        order by ${sortSql}
        limit #{pageIndex},#{pageSize}
        ) b on ss.stock_id = b.stock_id
        left join bd_position bp on ss.position_id = bp.position_id
    </select>

    <select id="stockCount" resultType="java.lang.Integer">
        SELECT
        COUNT( s.stock_id )
        <include refid="stockWhere"></include>
    </select>

    <select id="stockStatistics" resultType="java.lang.Integer">
        SELECT
        SUM( s.stock ) AS stock
        <include refid="stockWhere"></include>
    </select>

    <sql id="stockWhere">
        from stock s
        <if test="query!=null and query.positionType!= null and query.positionType!= ''">
            left join bd_position bp on s.position_id = bp.position_id
        </if>
        <if test="query!=null and query.internalBoxType!= null and query.internalBoxType!= ''">
            left join stock_internal_box sib on sib.internal_box_id = s.internal_box_id
        </if>
        <if test="query.spuList!= null and query.spuList.size() > 0 or query.productName!= null and query.productName!= ''">
            left join product_info pi on s.product_id = pi.product_id and pi.is_deleted = 0
        </if>
        <where>
            <if test="query!=null and query.specIds != null and query.specIds.size() > 0 ">
                and s.spec_id in
                <foreach collection="query.specIds" separator="," index="index" item="specId" open="(" close=")">
                    #{specId}
                </foreach>
            </if>
            <if test="query!=null and query.productIds != null and query.productIds.size() > 0 ">
                and s.product_id in
                <foreach collection="query.productIds" separator="," index="index" item="productId" open="(" close=")">
                    #{productId}
                </foreach>
            </if>
            <if test="query!=null and query.internalBoxId!= null and query.internalBoxId!=''">
                and s.internal_box_id = #{query.internalBoxId}
            </if>
            <if test="query!=null and query.spaceId!= null and query.spaceId!=''">
                and s.space_id = #{query.spaceId}
            </if>
            <if test="query!=null and query.spaceAreaId!= null and query.spaceAreaId!=''">
                and s.space_area_id = #{query.spaceAreaId}
            </if>
            <if test="query!=null and query.positionCode!= null and query.positionCode!=''">
                and s.position_code like concat(#{query.positionCode}, '%')
            </if>
            <if test="query!=null and query.exactPositionCode!= null and query.exactPositionCode!=''">
                and s.position_code = #{query.exactPositionCode}
            </if>
            <if test="query!=null and query.minStock!= null">
                and s.stock &gt;= #{query.minStock}
            </if>
            <if test="query.isHideZeroStock != null and query.isHideZeroStock == 1">
                and s.stock != 0
            </if>
            <if test="query!=null and query.lock != null ">
                and s.is_lock = #{query.lock}
            </if>
            <if test="query!=null and query.maxStock!= null">
                and s.stock &lt;= #{query.maxStock}
            </if>
            <if test="query!=null and query.isFuzzy == 1 and query.sku!= null and query.sku!= ''">
                and s.sku like concat(#{query.sku}, '%')
            </if>
            <if test="query!=null and query.isFuzzy == 0 and query.sku!= null and query.sku!= ''">
                and s.sku = #{query.sku}
            </if>
            <if test="query!=null and query.internalBoxType!= null and query.internalBoxType!= ''">
                and sib.internal_box_type = #{query.internalBoxType}
            </if>
            <if test="query!=null and query.positionAttribute!= null and query.positionAttribute!= ''">
                <if test="query.positionAttribute == 'position'">
                    and s.internal_box_id is null
                </if>
                <if test="query.positionAttribute == 'internal_box'">
                    and s.position_code is null
                </if>
            </if>
            <if test="query!=null and query.positionType!= null and query.positionType!= ''">
                and bp.position_type = #{query.positionType}
            </if>
            <if test="query!=null and query.skuList != null and query.skuList.size() > 0 ">
                and s.sku in
                <foreach collection="query.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.spuList != null and query.spuList.size() > 0 ">
                and pi.spu in
                <foreach collection="query.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query!=null and query.productName!= null and query.productName!= ''">
                and pi.product_name like concat(#{query.productName}, '%')
            </if>
        </where>
    </sql>

    <select id="stockSkuPositionInfo" resultType="com.nsy.api.wms.domain.stock.StockSkuPositionInfo">
        SELECT
        s.stock_id AS stockId,
        s.spec_id AS specId,
        s.position_id AS positionId,
        s.stock AS stock,
        p.position_code AS positionCode,
        p.position_type AS positionType,
        p.sort AS positionSort,
        sa.sort AS spaceAreaSort,
        sa.space_area_id as spaceAreaId,
        sa.space_area_name as spaceAreaName,
        sa.area_id as areaId,
        0 as prematchQty,
        p.quarter,
        s.product_id,
        s.space_id,
        p.min_stock,
        s.sku,
        s.is_lock isLock
        FROM
        stock s
        LEFT JOIN bd_position p ON p.position_id = s.position_id
        LEFT JOIN bd_space_area sa ON sa.space_area_id = p.space_area_id
        <where>
            and s.position_id > 0
            <if test="specIdList!=null and specIdList.size() > 0 ">
                and s.spec_id in
                <foreach collection="specIdList" separator="," index="index" item="specId" open="(" close=")">
                    #{specId}
                </foreach>
            </if>
            <if test="spaceId!= null">
                and s.space_id = #{spaceId}
            </if>
            <if test="areaId != null">
                and s.area_id = #{areaId}
            </if>
            <if test="positionCodeList!=null and positionCodeList.size() > 0 ">
                and s.position_code in
                <foreach collection="positionCodeList" separator="," index="index" item="positionCode" open="("
                         close=")">
                    #{positionCode}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findForPDASearch" resultType="com.nsy.api.wms.domain.stock.Stock">
        SELECT
        s.stock_id AS stockId,
        spec_info.image_url AS imageUrl,
        p_info.spu AS spu,
        p_info.product_name AS productName,
        spec_info.sku AS sku,
        sp.space_name AS spaceName,
        sp_area.space_area_name AS spaceAreaName,
        p.position_code AS positionCode,
        s.stock AS stock,
        p.position_type as positionType
        FROM stock s
        LEFT JOIN product_info p_info ON p_info.product_id = s.product_id
        LEFT JOIN product_spec_info spec_info ON spec_info.spec_id = s.spec_id
        LEFT JOIN bd_space sp ON sp.space_id = s.space_id
        LEFT JOIN bd_space_area sp_area ON sp_area.space_area_id = s.space_area_id
        LEFT JOIN bd_position p ON p.position_id = s.position_id
        <where>
            <if test="spu!= null and spu!=''">
                and p_info.spu = #{spu}
            </if>
            <if test="sku!= null and sku!=''">
                and spec_info.sku = #{sku}
            </if>
            <if test="skc!= null and skc!=''">
                and spec_info.skc = #{skc}
                and s.internal_box_id is null
            </if>
            <if test="spaceId!= null">
                and s.space_id = #{spaceId}
            </if>
            <if test="positionType!=null and positionType.size() > 0 ">
                and p.position_type in
                <foreach collection="positionType" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findForPDASearchInternalBoxStock" resultType="com.nsy.api.wms.domain.stock.Stock">
        SELECT
        s.stock_id AS stockId,
        spec_info.image_url AS imageUrl,
        p_info.spu AS spu,
        p_info.product_name AS productName,
        spec_info.sku AS sku,
        sp.space_name AS spaceName,
        sp_area.space_area_name AS spaceAreaName,
        s.stock AS stock,
        b.internal_box_code as internalBoxCode,
        b.internal_box_type as internalBoxType
        FROM stock s
        LEFT JOIN product_info p_info ON p_info.product_id = s.product_id
        LEFT JOIN product_spec_info spec_info ON spec_info.spec_id = s.spec_id
        LEFT JOIN bd_space sp ON sp.space_id = s.space_id
        LEFT JOIN bd_space_area sp_area ON sp_area.space_area_id = s.space_area_id
        LEFT JOIN stock_internal_box b on b.internal_box_id = s.internal_box_id
        <where>
            <if test="sku!= null and sku!=''">
                and spec_info.sku = #{sku}
            </if>
            <if test="skc!= null and skc!=''">
                and spec_info.skc = #{skc}
            </if>
            <if test="spaceId!= null">
                and s.space_id = #{spaceId}
            </if>
            and s.internal_box_id is not null
        </where>
    </select>
    <select id="exceptionStockStatistics" resultType="java.lang.Integer">
        SELECT SUM(s.stock) AS stock
        FROM stock s
                 JOIN bd_position p ON s.position_id = p.position_id AND p.position_type = 'EXCEPTION_POSITION'
        where s.space_id = #{spaceId}
          and s.spec_id = #{specId}
    </select>
    <select id="exceptionPositionList" resultType="com.nsy.api.wms.response.stock.ExceptionPositionListResponse">
        SELECT
        s.stock_id,
        s.space_name,
        pi.spu,
        ps.sku,
        ps.color,
        ps.size,
        s.stock,
        s.create_by,
        s.create_date,
        s.update_by,
        s.update_date,
        p.position_code
        FROM
        stock s
        JOIN bd_position p ON s.position_id = p.position_id AND p.is_deleted = 0 AND p.position_type =
        'EXCEPTION_POSITION'
        JOIN product_spec_info ps ON s.spec_id = ps.spec_id
        JOIN product_info pi ON s.product_id = pi.product_id
        where
        s.stock &lt;&gt; 0
        <choose>
            <when test="request.idList != null and request.idList.size() > 0 ">
                and s.stock_id in
                <foreach collection="request.idList" separator="," index="index" item="id" open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <if test="request.spaceId != null">
                    and s.space_id = #{request.spaceId}
                </if>
                <if test="request.sku!=null and request.sku!=''">
                    and s.sku = #{request.sku}
                </if>
                <if test="request.updateStartDate != null and request.updateStartDate != ''">
                    and s.update_date &gt;= #{request.updateStartDate}
                </if>
                <if test="request.updateEndDate != null and request.updateEndDate != ''">
                    and s.update_date &lt; #{request.updateEndDate}
                </if>
            </otherwise>
        </choose>
        order by s.stock_id desc
    </select>

    <select id="countExceptionPositionList" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        stock s
        JOIN bd_position p ON s.position_id = p.position_id AND p.is_deleted = 0 AND p.position_type =
        'EXCEPTION_POSITION'
        where
        s.stock &lt;&gt; 0
        <choose>
            <when test="request.idList != null and request.idList.size() > 0 ">
                and s.stock_id in
                <foreach collection="request.idList" separator="," index="index" item="id" open="(" close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                <if test="request.spaceId != null">
                    and s.space_id = #{request.spaceId}
                </if>
                <if test="request.sku!=null and request.sku!=''">
                    and s.sku = #{request.sku}
                </if>
                <if test="request.updateStartDate != null and request.updateStartDate != ''">
                    and s.update_date &gt;= #{request.updateStartDate}
                </if>
                <if test="request.updateEndDate != null and request.updateEndDate != ''">
                    and s.update_date &lt; #{request.updateEndDate}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="pageStockTrackingByRequest" resultType="com.nsy.api.wms.response.stock.StockTrackingIdResponse">
        select s.space_id as spaceId,s.spec_id as specId
        from stock s
        <if test="request!=null and request.spu != null and request.spu !=''">
            left join product_info pi on s.product_id = pi.product_id
        </if>
        <where>
            <if test="request!=null and request.spaceId != null and request.spaceId !=''">
                and s.space_id = #{request.spaceId}
            </if>
            <if test="request!=null and request.sku != null and request.sku !=''">
                and s.sku like concat(#{request.sku}, '%')
            </if>
            <if test="request!=null and request.spu != null and request.spu !=''">
                and pi.spu like concat(#{request.spu}, '%')
            </if>
            <if test="request!=null and request.skuList != null and request.skuList.size() > 0 ">
                and s.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY s.space_id,s.spec_id
        order by s.space_id desc
    </select>


    <select id="getStockTrackingByRequest" resultType="com.nsy.api.wms.response.stock.StockTrackingResponse">
        select sum(s.stock)                                                      as stock,
               sum(s.stock)                                                      as spaceStock,
               sum(if(s.position_id is null and b.internal_box_type = 'RECEIVE_BOX', s.stock,
                      0))                                                        as receiveBoxStock,
               sum(if(bp.position_type = 'SPARE_POSITION', s.stock, 0))          as sparePositionStock,
               sum(if(bp.position_type = 'SPARE_POSITION', spi.prematch_qty, 0)) as sparePositionPrematchQty,
               sum(if(bp.position_type = 'STOCK_POSITION', s.stock, 0))          as stockPositionStock,
               sum(if(bp.position_type = 'STOCK_POSITION', spi.prematch_qty, 0)) as stockPositionPrematchQty,
               sum(if(bp.position_type = 'EXCEPTION_POSITION', s.stock, 0))      as exceptionPositionStock,
               sum(if(bp.position_type = 'RETURN_POSITION', s.stock, 0))         as returnPositionStock,
               sum(if(bp.position_type = 'SHIPPING_POSITION', s.stock, 0))       as shippingPositionStock,
               sum(if(s.position_id is null and b.internal_box_type = 'PICKING_BOX', s.stock,
                      0))                                                        as pickingBoxStock,
               sum(if(s.position_id is null and b.internal_box_type = 'TRANSFER_BOX', s.stock,
                      0))                                                        as transferBoxStock,
               sum(if(s.position_id is null and b.internal_box_type = 'WITHDRAWAL_BOX', s.stock,
                      0))                                                        as withdrawalBoxStock,
               sum(if(s.position_id is null and b.internal_box_type = 'RETURN_BOX', s.stock,
                      0))                                                        as returnBoxStock

        from stock s
                 LEFT JOIN bd_position bp
                           on s.position_id = bp.position_id
                 LEFT JOIN stock_internal_box b
                           on s.internal_box_id = b.internal_box_id
                 LEFT JOIN
             (select position_id, sum(prematch_qty) prematch_qty
              from stock_prematch_info t
              where t.space_id = #{spaceId}
                and t.spec_id = #{specId}
              GROUP BY t.position_id) spi
             on spi.position_id = s.position_id
        where s.space_id = #{spaceId}
          and s.spec_id = #{specId}
        group by s.spec_id

    </select>

    <select id="countStockTrackingByRequest" resultType="com.nsy.api.wms.response.stock.StockTrackingCountResponse">
        select
        sum(s.stock) as stock,
        sum(spi.prematch_qty) as prematchQty,
        sum(if(s.position_id is null and b.internal_box_type = 'RECEIVE_BOX',s.stock,0)) as receiveBoxStock,
        sum(if(bp.position_type = 'SPARE_POSITION',s.stock,0)) as sparePositionStock,
        sum(if(bp.position_type = 'SPARE_POSITION',spi.prematch_qty,0)) as sparePositionPrematchQty,
        sum(if(bp.position_type = 'STOCK_POSITION',s.stock,0)) as stockPositionStock,
        sum(if(bp.position_type = 'STOCK_POSITION',spi.prematch_qty,0)) as stockPositionPrematchQty,
        sum(if(bp.position_type = 'EXCEPTION_POSITION',s.stock,0)) as exceptionPositionStock,
        sum(if(bp.position_type = 'RETURN_POSITION',s.stock,0)) as returnPositionStock,
        sum(if(bp.position_type = 'SHIPPING_POSITION',s.stock,0)) as shippingPositionStock,
        sum(if(s.position_id is null and b.internal_box_type = 'PICKING_BOX',s.stock,0)) as pickingBoxStock,
        sum(if(s.position_id is null and b.internal_box_type = 'TRANSFER_BOX',s.stock,0)) as transferBoxStock,
        sum(if(s.position_id is null and b.internal_box_type = 'WITHDRAWAL_BOX',s.stock,0)) as withdrawalBoxStock,
        sum(if(s.position_id is null and b.internal_box_type = 'RETURN_BOX',s.stock,0)) as returnBoxStock
        from stock s
        LEFT JOIN product_info p
        on s.product_id = p.product_id
        LEFT JOIN product_spec_info ps
        on s.spec_id = ps.spec_id
        LEFT JOIN bd_position bp
        on s.position_id = bp.position_id
        LEFT JOIN stock_internal_box b
        on s.internal_box_id = b.internal_box_id
        LEFT JOIN
        (select space_id,product_id,spec_id,sku,position_id,sum(prematch_qty) prematch_qty
        from stock_prematch_info
        GROUP BY space_id,product_id,spec_id,sku,position_id) spi
        on (spi.space_id = s.space_id and spi.spec_id = s.spec_id and spi.position_id = s.position_id)
        <where>
            <if test="request!=null and request.spaceId != null and request.spaceId !=''">
                and s.space_id = #{request.spaceId}
            </if>
            <if test="request!=null and request.spu != null and request.spu !=''">
                and p.spu = #{request.spu}
            </if>
            <if test="request!=null and request.productName != null and request.productName !=''">
                and p.product_name = #{request.productName}
            </if>
            <if test="request!=null and request.sku != null and request.sku !=''">
                and ps.sku = #{request.sku}
            </if>
        </where>
    </select>
    <select id="queryStockIdByNotConfirmCheckOrder" resultType="java.lang.Integer">
        SELECT
        s.stock_id
        FROM
        stock_difference_check_order c
        join stock_difference_check_order_item ci on c.check_order_id = ci.check_order_id
        join stock s on ci.position_id = s.position_id and ci.spec_id = s.spec_id
        WHERE c.`status` != 'CONFIRMED'
        <if test="request.idList != null and request.idList.size() > 0 ">
            and s.stock_id in
            <foreach collection="request.idList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryCheckOrderSourceData"
            resultType="com.nsy.wms.repository.entity.stock.StockDifferenceCheckOrderItemEntity">
        SELECT
        s.location,
        s.space_id,
        s.space_name,
        s.space_area_id,
        s.space_area_name,
        s.area_id,
        s.area_name,
        s.product_id,
        pi.product_name,
        s.spec_id,
        s.sku,
        s.position_id,
        s.position_code
        FROM
        stock s
        JOIN bd_position p ON s.position_id = p.position_id AND p.position_type = 'EXCEPTION_POSITION' AND p.is_deleted
        = 0
        JOIN product_info pi ON s.product_id = pi.product_id
        <where>
            <if test="stockIdList != null and stockIdList.size() > 0 ">
                and s.stock_id in
                <foreach collection="stockIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="pageClearSkuList" resultType="com.nsy.api.wms.response.stock.StockTransferClearSkuResponse">
        select
        ps.thumbnail_image_url as thumbnailImageUrl ,ps.preview_image_url as previewImageUrl,
        ps.color,ps.size,ps.barcode,ps.sku,ps.spec_id specId,t.qty,
        sa.earliest_in_date as earliestInDate,sa.latest_out_date latestOutDate,
        to_days(now()) - to_days(sa.earliest_in_date)as stockAgeDays
        from
        (select s.spec_id,sum(stock)-IFNULL(sum(prematch_qty),0) as qty
        from stock s
        LEFT JOIN (SELECT SKU,position_id,sum(prematch_qty) prematch_qty FROM stock_prematch_info
        GROUP BY SKU,position_id) spi
        on s.position_id = spi.position_id and s.sku = spi.SKU
        where s.position_id is not null
        and (select position_type from bd_position where position_id = s.position_id ) in
        ('STOCK_POSITION','SPARE_POSITION','OEM_POSITION','ACTIVITY_POSITION')
        <if test="request!=null and request.spaceId != null and request.spaceId !=''">
            and s.space_id = #{request.spaceId}
        </if>
        GROUP BY s.spec_id) t
        LEFT JOIN product_spec_info ps
        on ps.spec_id = t.spec_id
        LEFT JOIN product_info p
        on ps.product_id = p.product_id
        LEFT JOIN stock_age_statistics sa
        on ps.sku = sa.sku
        where
        t.qty > 0
        <if test="request.spu != null and request.spu !=''">
            and p.spu = #{request.spu}
        </if>
        <if test="request.sku != null and request.sku !=''">
            and ps.sku = #{request.sku}
        </if>
        <if test="request.earliestInStartDate != null and request.earliestInStartDate !=''">
            and sa.earliest_in_date >= #{request.earliestInStartDate}
        </if>
        <if test="request.earliestInEndDate != null and request.earliestInEndDate !=''">
            and sa.earliest_in_date &lt;= #{request.earliestInEndDate}
        </if>
        <if test="request.latestOutStartDate != null and request.latestOutStartDate !=''">
            and sa.latest_out_date >= #{request.latestOutStartDate}
        </if>
        <if test="request.latestOutEndDate != null and request.latestOutEndDate !=''">
            and sa.latest_out_date &lt;= #{request.latestOutEndDate}
        </if>
        order by stockAgeDays desc


    </select>

    <select id="getPositionBySpecId" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        select p.position_id, p.position_code
        from stock s
                 left JOIN bd_position p
                           on p.position_id = s.position_id
        where s.position_id is not null
          and s.spec_id = #{specId}
          and p.position_type in ('STOCK_POSITION', 'SPARE_POSITION', 'OEM_POSITION', 'ACTIVITY_POSITION')
        limit 1
    </select>
    <select id="queryStockBySpaceIdAndSku" resultType="java.lang.Integer">
        SELECT sum(a.stock) - sum(IFNULL(b.prematch_qty, 0)) stock
        FROM (
                 SELECT s.stock,
                        s.position_id
                 FROM stock s
                          JOIN bd_position p ON s.position_id = p.position_id
                     AND (p.position_type = 'STOCK_POSITION' OR p.position_type = 'SPARE_POSITION' OR
                          p.position_type = 'OEM_POSITION' OR p.position_type = 'ACTIVITY_POSITION')
                          JOIN product_spec_info si ON s.spec_id = si.spec_id
                 WHERE s.stock > 0
                   AND si.sku = #{sku}
                   AND s.space_id = #{spaceId}
             ) a
                 LEFT JOIN (
            SELECT s.position_id,
                   SUM(s.prematch_qty) prematch_qty
            FROM stock_prematch_info s
            WHERE s.prematch_qty > 0
              AND s.sku = #{sku}
              AND s.space_id = #{spaceId}
            GROUP BY s.position_id
        ) b ON a.position_id = b.position_id
        WHERE a.stock > b.prematch_qty
           or b.position_id is null
    </select>

    <select id="listClearSku" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        SELECT
        spec_id as specId,sum(stock)-IFNULL(sum(prematch_qty),0) as stock
        FROM
        stock s
        JOIN bd_position p
        ON s.position_id = p.position_id
        LEFT JOIN (SELECT SKU,position_id,sum(prematch_qty) prematch_qty FROM stock_prematch_info
        GROUP BY SKU,position_id) spi
        on s.position_id = spi.position_id and s.sku = spi.SKU
        WHERE
        (p.position_type = 'STOCK_POSITION' or p.position_type = 'SPARE_POSITION' OR p.position_type = 'OEM_POSITION' OR
        p.position_type = 'ACTIVITY_POSITION')
        and s.space_id = #{request.spaceId}
        and s.spec_id in
        <foreach collection="request.specIdList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        group by spec_id
    </select>
    <select id="queryStockByAreaIds" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        SELECT
        s.*
        FROM
        stock s
        JOIN bd_position p ON s.position_id = p.position_id
        AND (p.position_type = 'STOCK_POSITION' OR p.position_type = 'SPARE_POSITION' OR p.position_type =
        'OEM_POSITION' OR p.position_type = 'ACTIVITY_POSITION')
        AND p.area_id in
        <foreach collection="areaIds" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        WHERE
        s.stock > 0 and s.position_id is not null
    </select>
    <select id="queryStockBySpaceAreaIds" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        SELECT
        s.*
        FROM
        stock s
        JOIN bd_position p ON s.position_id = p.position_id
        AND (p.position_type = 'STOCK_POSITION' OR p.position_type = 'SPARE_POSITION' OR p.position_type =
        'OEM_POSITION' OR p.position_type = 'ACTIVITY_POSITION')
        AND p.space_area_id in
        <foreach collection="spaceAreaIds" item="spaceAreaId" separator="," open="(" close=")">
            #{spaceAreaId}
        </foreach>
        WHERE
        s.stock > 0 and s.position_id is not null
    </select>
    <select id="queryStockBySpecIds" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        SELECT
        s.*
        FROM
        stock s
        JOIN bd_position p ON s.position_id = p.position_id
        AND (p.position_type = 'STOCK_POSITION' OR p.position_type = 'SPARE_POSITION' OR p.position_type =
        'OEM_POSITION' OR p.position_type = 'ACTIVITY_POSITION')
        WHERE
        s.stock > 0
        and s.spec_id in
        <foreach collection="specIds" item="specId" separator="," open="(" close=")">
            #{specId}
        </foreach>
        and s.position_id is not null

    </select>

    <select id="findMaxStockByPositionTypeAndArea" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        SELECT
        s.*
        FROM
        stock s
        LEFT JOIN bd_position bp ON s.position_id = bp.position_id
        WHERE
        bp.position_type IN
        <foreach collection="positionTypeList" item="positionType" separator="," open="(" close=")">
            #{positionType}
        </foreach>
        <if test="areaName != null and areaName !=''">
            AND s.area_name = #{areaName}
        </if>
        AND s.sku = #{sku}
        AND s.is_lock = 0
        ORDER BY
        s.stock DESC
        LIMIT 1
    </select>

    <select id="findReturnPositionStockBySku" resultType="java.lang.Integer">
        SELECT sum(s.stock)
        FROM stock s
                 LEFT JOIN bd_position bp ON s.position_id = bp.position_id
        WHERE bp.position_type = 'RETURN_POSITION'
          AND s.sku = #{sku}
    </select>

    <select id="findStockInStockPositionByAreaIdAndSku" resultType="com.nsy.wms.repository.entity.stock.StockEntity">
        SELECT s.*
        FROM stock s
                 LEFT JOIN bd_position bp ON s.position_code = bp.position_code
        WHERE s.sku = #{sku}
          and bp.position_type = "STOCK_POSITION"
          and s.area_id = #{areaId}
          and s.stock &gt; 0
          and bp.quarter != ''
        ORDER BY bp.quarter ASC, s.stock desc;
    </select>

    <select id="findStockBdPositionCategoryList"
            resultType="com.nsy.wms.business.domain.bo.stock.StockBdPositionCategoryBo">
        SELECT
        s.sku,
        s.stock,
        bp.position_code,
        bp.position_id,
        bp.min_stock,
        pc.category_id
        FROM
        stock s
        LEFT JOIN bd_position bp ON s.position_code = bp.position_code
        LEFT JOIN product_spec_info psi ON s.spec_id = psi.spec_id
        LEFT JOIN product_info pi ON s.product_id = pi.product_id
        LEFT JOIN product_category pc ON pi.category_id = pc.category_id
        WHERE
        bp.position_type = "SPARE_POSITION"
        and s.space_id = #{spaceId}
        and bp.min_stock &gt; 0
        and s.stock &lt; bp.min_stock
        <if test="categoryIdList != null and categoryIdList.size() > 0 ">
            and pc.category_id in
            <foreach collection="categoryIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="skc != null and skc !=''">
            and psi.skc = #{skc}
        </if>
    </select>

    <!--    <select id="getEffectivePositionStock" resultType="java.lang.Integer">
            SELECT ifnull(sum(wmsStock.qty),0)
            FROM (
                     SELECT s.stock                                                         qty,
                            IF
                                (s.position_id > 0, bp.position_code, ib.internal_box_code) sCode,
                            IF
                                (s.position_id > 0, bp.position_type, ib.internal_box_type) sType
                     FROM nsy_wms.stock s
                              LEFT JOIN nsy_wms.bd_position bp ON bp.position_id = s.position_id
                              LEFT JOIN nsy_wms.stock_internal_box ib ON ib.internal_box_id = s.internal_box_id
                     WHERE  s.sku = #{sku}
                       AND s.stock > 0
                       AND s.space_id = #{spaceId}
                 ) wmsStock
            WHERE wmsStock.sType != 'EXCEPTION_POSITION'
              AND wmsStock.sType != 'RECEIVE_BOX'
              AND wmsStock.sType != 'QA_BOX'
              AND wmsStock.sType != 'PROCESS_DEMAGE_POSITION'
              AND wmsStock.sType != 'RETURN_DEMAGE_POSITION'
              AND wmsStock.sType != 'RETURN_POSITION'
              AND wmsStock.sType != 'BORROW_RETURN_BOX'
              AND wmsStock.sType != 'RETURN_BOX'

        </select>-->

    <select id="getEffectivePositionStockQuanzhouTaishang" resultType="java.lang.Integer">
        SELECT ifnull(sum(wmsStock.qty), 0) as qty
        FROM (
                 SELECT s.stock qty
                 FROM nsy_wms.stock s
                          INNER JOIN nsy_wms.bd_position bp ON bp.position_id = s.position_id
                 WHERE s.sku = #{sku}
                   and s.space_id = #{spaceId}
                   and s.area_id not in (5, 6, 25, 33,68,75,76,77)
                   AND s.stock > 0
                   AND bp.position_type != 'EXCEPTION_POSITION'
                   AND bp.position_type != 'PROCESS_DEMAGE_POSITION'
                   AND bp.position_type != 'RETURN_DEMAGE_POSITION'
                   AND bp.position_type != 'RETURN_POSITION'
                   AND bp.position_type != 'SHIPPING_POSITION'
                 UNION ALL

                 SELECT s.stock - ifnull(t.qty, 0) as qty
                 FROM nsy_wms.stock s
                          LEFT JOIN nsy_wms.bd_position bp ON bp.position_id = s.position_id
                          LEFT JOIN (
                     select stock_id, sum(qty) as qty
                     from stock_shipping_position
                     where sku = #{sku}
                       and source_area_id in (5, 6, 25, 33,68,75,76,77)
                     GROUP BY stock_id
                 ) t on s.stock_id = t.stock_id
                 where s.sku = #{sku}
                   and s.position_id is not null
                   and s.space_id = #{spaceId}
                   and s.area_id not in (5, 6, 25, 33,68,75,76,77)
                   AND s.stock > 0
                   AND bp.position_type = 'SHIPPING_POSITION'

                 UNION ALL
                 SELECT bi.qty qty
                 FROM stock_internal_box_item bi
                          LEFT JOIN stock_internal_box ib ON ib.internal_box_id = bi.internal_box_id
                 WHERE bi.sku = #{sku}
                   and bi.qty > 0
                   and bi.source_area_id is not null
                   and bi.source_area_id not in (-1, -2, -3, 5, 6, 33, 25,68,75,76,77)
                   AND ib.internal_box_type != 'RECEIVE_BOX'
                   AND ib.internal_box_type != 'QA_BOX'
                   AND ib.internal_box_type != 'BORROW_RETURN_BOX'
                   AND ib.internal_box_type != 'RETURN_BOX'
             ) wmsStock

    </select>


    <select id="getEffectivePositionStock" resultType="java.lang.Integer">
        SELECT ifnull(sum(wmsStock.qty), 0) as qty
        FROM (
                 SELECT s.stock qty
                 FROM nsy_wms.stock s
                          INNER JOIN nsy_wms.bd_position bp ON bp.position_id = s.position_id
                 WHERE s.sku = #{sku}
                   and s.space_id = #{spaceId}
                   and s.area_id = #{areaId}
                   AND s.stock > 0
                   AND bp.position_type != 'EXCEPTION_POSITION'
                   AND bp.position_type != 'PROCESS_DEMAGE_POSITION'
                   AND bp.position_type != 'RETURN_DEMAGE_POSITION'
                   AND bp.position_type != 'RETURN_POSITION'
                   AND bp.position_type != 'SHIPPING_POSITION'
                   AND bp.position_type != 'ACTIVITY_POSITION'
                    AND bp.position_type != 'STORE_POSITION'
                 UNION ALL

                 SELECT if(t.qty > s.stock, s.stock, t.qty) as qty
                 FROM nsy_wms.stock s
                          inner JOIN (
                     select sp.stock_id, ifnull(sum(sp.qty), 0) as qty
                     from stock_shipping_position sp
                     left join bd_position  bp on sp.source_position_code = bp.position_code
                     where sp.sku = #{sku}
                       and sp.source_area_id = #{areaId}
                        and bp.position_type != 'ACTIVITY_POSITION'
                            AND bp.position_type != 'STORE_POSITION'
                     GROUP BY sp.stock_id
                 ) t on s.stock_id = t.stock_id
                          LEFT JOIN nsy_wms.bd_position bp ON bp.position_id = s.position_id
                 where s.sku = #{sku}
                   and s.position_id is not null
                   and s.space_id = #{spaceId}
                   AND s.stock > 0
                   AND bp.position_type = 'SHIPPING_POSITION'


                 UNION ALL
                 SELECT bi.qty qty
                 FROM stock_internal_box_item bi
                          LEFT JOIN stock_internal_box ib ON ib.internal_box_id = bi.internal_box_id
                          left join bd_position  bp on bi.source_position_code = bp.position_code
                 WHERE bi.sku = #{sku}
                   and bi.qty > 0
                   and bi.source_area_id is not null
                   and bi.source_area_id = #{areaId}
                   and bp.position_type != 'ACTIVITY_POSITION'
                   AND bp.position_type != 'STORE_POSITION'
                   AND ib.internal_box_type != 'RECEIVE_BOX'
                   AND ib.internal_box_type != 'QA_BOX'
                   AND ib.internal_box_type != 'BORROW_RETURN_BOX'
                   AND ib.internal_box_type != 'RETURN_BOX'
             ) wmsStock

    </select>

    <select id="sumStockInStockPositionByAreaIdAndSku" resultType="java.lang.Integer">
        SELECT
            IFNULL(sum( s.stock ), 0)
        FROM
            stock s
                LEFT JOIN bd_position bp ON s.position_id = bp.position_id
        WHERE
            bp.position_type = 'STOCK_POSITION'
            AND s.area_id = #{areaId}
            AND s.stock > 0
            AND s.sku = #{sku}
            AND s.is_lock = 0
    </select>

    <select id="findEnoughByPositionCode" resultType="com.nsy.api.wms.response.stockin.StockinReturnProductTaskScanFromStockPositionItemResponse">
        SELECT
            s.stock_id,
            psi.image_url,
            psi.thumbnail_image_url,
            psi.preview_image_url,
            psi.barcode,
            s.sku,
            s.stock qty,
            s.create_date
        FROM
            stock s
                LEFT JOIN product_spec_info psi ON s.spec_id = psi.spec_id
        WHERE
            s.position_code = #{positionCode}
          and s.stock > 0
        order by s.create_date
    </select>
    <select id="getActivityPositionStock" resultType="java.lang.Integer">
        SELECT ifnull(sum(wmsStock.qty), 0) as qty
        FROM (
                 SELECT s.stock qty
                 FROM nsy_wms.stock s
                 WHERE s.sku = #{sku}
                   and s.position_code = #{positionCode}
                   AND s.stock > 0
                 UNION ALL

                 SELECT if(t.qty > s.stock, s.stock, t.qty) as qty
                 FROM nsy_wms.stock s
                          inner JOIN (
                     select sp.stock_id, ifnull(sum(sp.qty), 0) as qty
                     from stock_shipping_position sp
                     where sp.sku = #{sku}
                       and sp.source_position_code = #{positionCode}
                     GROUP BY sp.stock_id
                 ) t on s.stock_id = t.stock_id
                          LEFT JOIN nsy_wms.bd_position bp ON bp.position_id = s.position_id
                 where s.sku = #{sku}
                   and s.position_id is not null
                   and s.space_id = #{spaceId}
                   AND s.stock > 0
                   AND bp.position_type = 'SHIPPING_POSITION'


                 UNION ALL
                 SELECT bi.qty qty
                 FROM stock_internal_box_item bi
                          LEFT JOIN stock_internal_box ib ON ib.internal_box_id = bi.internal_box_id
                 WHERE bi.sku = #{sku}
                   and bi.qty > 0
                   and bi.source_area_id is not null
                   and bi.source_position_code = #{positionCode}
                   AND ib.internal_box_type != 'RECEIVE_BOX'
                   AND ib.internal_box_type != 'QA_BOX'
                   AND ib.internal_box_type != 'BORROW_RETURN_BOX'
                   AND ib.internal_box_type != 'RETURN_BOX'
             ) wmsStock
    </select>
</mapper>
