package com.nsy.wms.business.service.stockout.valid;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.ReplenishOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPackingListStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentPackStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentPackItemSkuInfoRequest;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutTransparencyCodeService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentPackEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentPackItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutTransparencyCodeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HXD
 * 2022/9/16
 **/
@Service
public class StockoutShipmentValid {

    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;

    public void validTransferBox(StockoutShipmentEntity originBox, StockoutShipmentEntity targetBox, List<StockoutShipmentItemEntity> shipmentItemEntities, StockoutShipmentItemSkuInfoRequest request) {
        if (originBox.getStatus().equalsIgnoreCase(StockoutPackingListStatusEnum.SHIPPED.name())) {
            List<String> stockoutOrderNos = shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
            List<StockoutOrderEntity> stockoutOrderEntities = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos)
                .stream().filter(o -> StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equals(o.getWorkspace())).collect(Collectors.toList());
            if (stockoutOrderEntities.isEmpty() || stockoutOrderNos.size() != stockoutOrderEntities.size()) {
                throw new BusinessServiceException("箱号已经发货，无法调拨");
            }
        }
        List<StockoutTransparencyCodeEntity> transparencyCodeEntities = transparencyCodeService.findByStockouOrderNo(shipmentItemEntities.get(0).getStockoutOrderNo());
        if (!CollectionUtils.isEmpty(transparencyCodeEntities) && !Objects.equals(originBox.getStatus(), StockoutShipmentStatusEnum.PACKING_END.name())) {
            throw new BusinessServiceException("该箱子需要先确认装箱，再进行调拨！");
        }
        if (originBox.getShipmentBoxCode().startsWith("Pack-"))
            throw new BusinessServiceException("Pack箱子无法调拨");
        if (targetBox.getStatus().equalsIgnoreCase(StockoutPackingListStatusEnum.SHIPPED.name()))
            throw new BusinessServiceException("箱号已经发货，无法调拨");
        if (targetBox.getShipmentBoxCode().startsWith("Pack-"))
            throw new BusinessServiceException("Pack箱子无法调拨");
        if (CollectionUtils.isEmpty(shipmentItemEntities))
            throw new BusinessServiceException("箱子[" + request.getOriginShipmentBoxCode() + "]为空箱");

        //已申请箱贴，不允许调拨
        if (StringUtils.hasText(originBox.getFbaLabelStatus())
            && !FbaLabelStatusEnum.WAIT.name().equals(originBox.getFbaLabelStatus())
            && !FbaLabelStatusEnum.INIT.name().equals(originBox.getFbaLabelStatus())
            && !FbaLabelStatusEnum.CANCELED.name().equals(originBox.getFbaLabelStatus()))
            throw new BusinessServiceException(String.format("箱子%s已开始申请箱贴，无法调拨", originBox.getShipmentBoxCode()));
        if (StringUtils.hasText(targetBox.getFbaLabelStatus())
            && !FbaLabelStatusEnum.WAIT.name().equals(targetBox.getFbaLabelStatus())
            && !FbaLabelStatusEnum.INIT.name().equals(targetBox.getFbaLabelStatus())
            && !FbaLabelStatusEnum.CANCELED.name().equals(targetBox.getFbaLabelStatus()))
            throw new BusinessServiceException(String.format("箱子%s已开始申请箱贴，无法调拨", targetBox.getShipmentBoxCode()));


        // 已经创建FBA补货单的箱子不允许调拨
        String originBoxReplenishOrderStatus = originBox.getReplenishOrderStatus();
        String targetBoxReplenishOrderStatus = targetBox.getReplenishOrderStatus();
        if (ReplenishOrderStatusEnum.DEAL.name().equalsIgnoreCase(originBoxReplenishOrderStatus))
            throw new BusinessServiceException(String.format("箱子%s已生成FBA补货单，不允许调拨", originBox.getShipmentBoxCode()));
        if (ReplenishOrderStatusEnum.DEAL.name().equalsIgnoreCase(targetBoxReplenishOrderStatus))
            throw new BusinessServiceException(String.format("箱子%s已生成FBA补货单，不允许调拨", targetBox.getShipmentBoxCode()));

        // FBA新流程和旧流程的箱子不允许互相调拨
        if (!originBox.getFbaReplenishType().equalsIgnoreCase(targetBox.getFbaReplenishType()))
            throw new BusinessServiceException("不同FBA补货类型不允许相互调拨");

        // 包装规格不同箱子质检不允许调拨
        if (StringUtils.hasText(originBox.getProductSizeSegment())
            && StringUtils.hasText(targetBox.getProductSizeSegment())
            && !originBox.getProductSizeSegment().equalsIgnoreCase(targetBox.getProductSizeSegment()))
            throw new BusinessServiceException("包装规格不同箱子质检不允许调拨");
    }

    public void validTransfiferPackBox(StockoutShipmentPackEntity originBox, StockoutShipmentPackEntity targetBox, List<StockoutShipmentPackItemEntity> shipmentItemEntities, StockoutShipmentPackItemSkuInfoRequest request) {
        if (originBox.getShipmentBoxCode().startsWith("Pack-"))
            throw new BusinessServiceException("Pack箱子无法调拨");
        if (CollectionUtils.isEmpty(shipmentItemEntities))
            throw new BusinessServiceException("箱子[" + request.getOriginShipmentBoxCode() + "]为空箱");
        if (StockoutShipmentPackStatusEnum.SYNCED.name().equalsIgnoreCase(targetBox.getStatus())
            || StockoutShipmentPackStatusEnum.SYNCED.name().equalsIgnoreCase(originBox.getStatus())) {
            throw new BusinessServiceException("Pack箱子已同步，无法调拨");
        }
    }

    public void validSameBoxIndex(StockoutShipmentEntity shipment, Integer boxIndex) {
        if (boxIndex == null) {
            return;
        }
        if (!Objects.equals(shipment.getBoxIndex(), boxIndex)) {
            List<String> stockoutOrderNo = shipmentItemService.getStockoutOrderNoByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(stockoutOrderNo)) {
                return;
            }
            //判断FBA补货类型
            FbaReplenishTypeEnum fbaReplenishType = StringUtils.hasText(shipment.getFbaReplenishType()) ? FbaReplenishTypeEnum.getByName(shipment.getFbaReplenishType()) : FbaReplenishTypeEnum.NORMAL;

            //补货计划 - 补货单已经生成不允许再修改箱序
            if (FbaReplenishTypeEnum.REPLENISH.name().equalsIgnoreCase(fbaReplenishType.name())
                && ReplenishOrderStatusEnum.DEAL.name().equalsIgnoreCase(shipment.getReplenishOrderStatus()))
                throw new BusinessServiceException("箱子已成生FBA补货单，不允许修改箱序");

            if (FbaReplenishTypeEnum.REPLENISH.name().equalsIgnoreCase(fbaReplenishType.name()))
                return;

            Integer sameBoxIndex = shipmentItemService.getBaseMapper().validSameBoxIndex(stockoutOrderNo, shipment.getShipmentId(), boxIndex);
            if (sameBoxIndex > 0) {
                throw new BusinessServiceException("存在相同的箱子序号，请重新填写");
            }
        }
    }

    public void validShipmentConfirmRequest(StockoutShipmentConfirmUpdateRequest request) {
        //查询装箱清单的所有出库单号与请求参数对比
        List<String> stockoutOrderNos = shipmentItemService.list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
                .select(StockoutShipmentItemEntity::getStockoutOrderNo)
                .in(StockoutShipmentItemEntity::getShipmentId, request.getShipmentIds())
                .gt(StockoutShipmentItemEntity::getQty, 0)
                .eq(StockoutShipmentItemEntity::getIsDeleted, 0))
            .stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderNos))
            throw new BusinessServiceException("装箱清单已更新，请重新扫描！");
        // 装箱清单有，请求参数没有
        if (stockoutOrderNos.stream().filter(no -> !request.getStockoutOrderNos().contains(no)).count() > 0)
            throw new BusinessServiceException("装箱清单已更新，请重新扫描！");
        // 装箱清单没有，请求参数有
        if (request.getStockoutOrderNos().stream().filter(no -> !stockoutOrderNos.contains(no)).count() > 0)
            throw new BusinessServiceException("装箱清单已更新，请重新扫描！");
    }
}
