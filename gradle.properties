# 开发手工切换成对应的项目分支
PROJECT_ENV=dev
# newshiying
# 正式仓库
REPOSITORY_URL_RELEASE=https://packages.aliyun.com/maven/repository/2015093-release-I4hJZq/
# 正式用户（只读）
RELEASE_NEXUS_USER=62ac61abdbd131f866efd423
# 正式用户密码（只读）
RELEASE_NEXUS_PASS=d]tES(=7oMIv

# nsy-test
# 开发快照仓库
REPOSITORY_URL_SNAPSHOT_DEV=https://packages.aliyun.com/maven/repository/2279189-snapshot-XsMf4f
# 开发快照用户（只读）
SNAPSHOT_NEXUS_USER=639c48614f6a9c17e2b48961
# 开发快照用户密码（只读）
SNAPSHOT_NEXUS_PASS=7dVMj4xD)NYg
# uat
#REPOSITORY_URL_SNAPSHOT_DEV=https://packages.aliyun.com/maven/repository/2015093-snapshot-kqGwKY
#SNAPSHOT_NEXUS_USER=62ac61abdbd131f866efd423
## 正式用户密码（只读）
#SNAPSHOT_NEXUS_PASS=d]tES(=7oMIv

org.gradle.jvmargs=-Xmx6144m -XX:MaxPermSize=2048m
spotbugs.jvmargs=-Xmx4096m
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true