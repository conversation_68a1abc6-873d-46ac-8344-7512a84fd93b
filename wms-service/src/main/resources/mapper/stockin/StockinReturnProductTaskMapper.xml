<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskMapper">

    <resultMap id="BaseResultMap" type="com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity">
        <id property="returnProductTaskId" column="return_product_task_id" jdbcType="INTEGER"/>
        <result property="spaceId" column="space_id" jdbcType="INTEGER"/>
        <result property="spaceName" column="space_name" jdbcType="VARCHAR"/>
        <result property="supplierId" column="supplier_id" jdbcType="INTEGER"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="positionId" column="position_id" jdbcType="INTEGER"/>
        <result property="positionCode" column="position_code" jdbcType="VARCHAR"/>
        <result property="spaceAreaId" column="space_area_id" jdbcType="INTEGER"/>
        <result property="spaceAreaName" column="space_area_name" jdbcType="VARCHAR"/>
        <result property="returnMethod" column="return_method" jdbcType="VARCHAR"/>
        <result property="logisticsNo" column="logistics_no" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="operateStartDate" column="operate_start_date" jdbcType="TIMESTAMP"/>
        <result property="operateEndDate" column="operate_end_date" jdbcType="TIMESTAMP"/>
        <result property="location" column="location" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        return_product_task_id,space_id,space_name,
        supplier_id,supplier_name,status,
        position_id,position_code,space_area_id,
        space_area_name,return_method,logistics_no,
        remark,operator_user,operator_date,
        return_date,location,create_date,
        create_by,update_date,update_by
    </sql>
    <sql id="stockinReturnOrderPageRequest">
        <where>
            <if test="request.returnId != null">
                and t.return_product_task_id = #{request.returnId}
            </if>
            <if test="request.spaceId != null">
                and t.space_id = #{request.spaceId}
            </if>
            <if test="request.supplierId != null">
                and t.supplier_id = #{request.supplierId}
            </if>
            <if test="request.purchaseUserName != null and request.purchaseUserName != ''">
                and t.purchaser_user_name = #{request.purchaseUserName}
            </if>
            <if test="request.returnDateStart != null">
                and t.operate_start_date >= #{request.returnDateStart}
            </if>
            <if test="request.returnDateEnd != null">
                and t.operate_start_date &lt;= #{request.returnDateEnd}
            </if>
            <if test="request.reworkStatus != null and request.reworkStatus != ''">
                and t.rework_status = #{request.reworkStatus}
            </if>
            <if test="request.deliveryDateStart != null">
                and t.delivery_date >= #{request.deliveryDateStart}
            </if>
            <if test="request.deliveryDateEnd != null">
                and t.delivery_date &lt;= #{request.deliveryDateEnd}
            </if>
            <if test="request.returnType != null and request.returnType != ''">
                and t.return_type = #{request.returnType}
            </if>
            <if test="request.returnNature != null and request.returnNature != ''">
                and t.return_nature = #{request.returnNature}
            </if>
            <if test="request.status != null and request.status != ''">
                and t.status = #{request.status}
            </if>
            <if test="request.deliveryType != null and request.deliveryType != ''">
                and t.deliveryType = #{request.deliveryType}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                and t.status in
                <foreach collection="request.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="request.logisticsNo != null and request.logisticsNo != ''">
                and t.logistics_no = #{request.logisticsNo}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and (i.sku like concat(#{request.sku}, '%')
                <if test="request.skuAutoMatchList != null and request.skuAutoMatchList.size() > 0">
                    OR <foreach collection="request.skuAutoMatchList" item="sku" open="(" close=")" separator="OR">
                    i.sku like concat(#{sku}, '%')
                </foreach>
                </if>)
            </if>
            <if test="request.selectIdList != null and request.selectIdList.size() > 0">
                and t.return_product_task_id in
                <foreach collection="request.selectIdList" item="ids" open="(" close=")" separator=",">
                    #{ids}
                </foreach>
            </if>
            <if test="request.returnTaskItemIdList != null and request.returnTaskItemIdList.size() > 0">
                and i.id in
                <foreach collection="request.returnTaskItemIdList" item="returnTaskItemId" open="(" close=")" separator=",">
                    #{returnTaskItemId}
                </foreach>
            </if>
            <if test="request.returnIdList != null and request.returnIdList.size() > 0">
                and t.return_product_task_id in
                <foreach collection="request.returnIdList" item="returnId" open="(" close=")" separator=",">
                    #{returnId}
                </foreach>
            </if>
            <if test="request.unfinishedInFiveDays != null ">
                <if test="request.unfinishedInFiveDays == 1">
                    and t.delivery_date &lt;= DATE_SUB(CURDATE(), INTERVAL 5 DAY) and t.status = 'IN_TRANSIT'
                </if>
                <if test="request.unfinishedInFiveDays == 0">
                    and (t.delivery_date > DATE_SUB(CURDATE(), INTERVAL 5 DAY) or t.status != 'IN_TRANSIT')
                </if>
            </if>
        </where>
    </sql>
    <sql id="taskListWhere">
        <where>
			and t.location = #{request.location}
            <if test="request.returnProductTaskId != null">
                and t.return_product_task_id = #{request.returnProductTaskId}
            </if>
            <if test="request.spaceId != null">
                and t.space_id = #{request.spaceId}
            </if>
            <if test="request.supplierId != null">
                and t.supplier_id = #{request.supplierId}
            </if>
            <if test="request.returnNature != null and request.returnNature != ''">
                and return_nature = #{request.returnNature}
            </if>
            <if test="request.positionCode != null and request.positionCode != ''">
                and t.position_code = #{request.positionCode}
            </if>
            <if test="request.operator != null and request.operator != ''">
                and t.operator like concat(#{request.operator},'%')
            </if>
            <if test="request.sku != null and request.sku != ''">
                and item.sku like concat(#{request.sku}, '%')
            </if>
            <if test="request.operateStartDate != null">
                and t.create_date &gt;= #{request.operateStartDate}
            </if>
            <if test="request.operateEndDate != null">
                and t.create_date &lt;= #{request.operateEndDate}
            </if>
            <if test="request.logisticsNo != null and request.logisticsNo != ''">
                and t.logistics_no = #{request.logisticsNo}
            </if>
            <if test="request.logisticsCompany != null and request.logisticsCompany != ''">
                and t.logistics_company = #{request.logisticsCompany}
            </if>
            <if test="request.freightCarrier != null">
                and t.freight_carrier = #{request.freightCarrier}
            </if>
            <if test="request.handleMethod != null">
                and t.handle_method = #{request.handleMethod}
            </if>
            <if test="request.purchasePlanNo != null and request.purchasePlanNo != ''">
                and item.purchase_plan_no = #{request.purchasePlanNo}
            </if>
            <if test="request.selectIdList != null and request.selectIdList.size() > 0">
                and t.return_product_task_id in
                <foreach collection="request.selectIdList" item="ids" open="(" close=")" separator=",">
                    #{ids}
                </foreach>
            </if>
            <choose>
                <when test="request.unfinishedInFiveDays != null and request.unfinishedInFiveDays == true">
                    and t.delivery_date &lt;= DATE_SUB(CURDATE(), INTERVAL 5 DAY) and t.status = 'IN_TRANSIT'
                </when>
                <otherwise>
                    <if test="request.status != null and request.status != ''">
                        and t.status = #{request.status}
                    </if>
                    <if test="request.deliveryStartDate != null">
                        and t.delivery_date &gt;= #{request.deliveryStartDate}
                    </if>
                    <if test="request.deliveryEndDate != null">
                        and t.delivery_date &lt;= #{request.deliveryEndDate}
                    </if>
                </otherwise>
            </choose>
        </where>
    </sql>
    <select id="getTaskList" resultType="com.nsy.api.wms.response.stockin.StockinReturnProductTaskListResponse">
        SELECT
        t.return_product_task_id,
        t.real_return_product_task_id,
        min(t.space_id) as space_id,
        min(t.space_name) as space_name,
        min(t.supplier_id) as supplier_id,
        min(t.supplier_name) as supplier_name,
        min(t.`status`) as status,
        min(t.position_id) as position_id,
        min(t.position_code) as position_code,
        min(t.space_area_id) as space_area_id,
        min(t.space_area_name) as space_area_name,
        min(t.return_method) as return_method,
        min(t.return_nature) as return_nature,
        min(t.logistics_no) as logistics_no,
        min(t.remark) as remark,
        min(t.operator) as operator,
        min(t.operate_start_date) as operate_start_date,
        min(t.operate_end_date) as operate_end_date,
        min(t.location) as location,
        min(t.version) as version,
        min(t.create_date) as create_date,
        min(t.create_by) as create_by,
        min(t.update_date) as update_date,
        min(t.update_by) as update_by,
        sum(item.wait_return_qty) as wait_return_qty,
        sum(item.actual_return_qty) as actual_return_qty,
        sum(item.lack_product_qty) as lack_product_qty,
        t.delivery_date,
        t.shipping_address,
        item.sku,
        t.logistics_company,
        t.freight_carrier,
        t.handle_method,
        t.delivery_type,
        t.attachment_url
        FROM
        stockin_return_product_task AS t
        LEFT JOIN stockin_return_product_task_item AS item ON t.return_product_task_id = item.return_product_task_id
        <include refid="taskListWhere"></include>
        GROUP BY
        t.return_product_task_id
        order by t.return_product_task_id desc
    </select>


    <select id="getExportTaskListItem" resultType="com.nsy.api.wms.response.stockin.StockinReturnProductTaskListItemResponse">
        SELECT
        distinct
        t.return_product_task_id,
        t.supplier_name,
        t.`status`,
        t.logistics_no,
        t.logistics_company,
        t.space_name,
        t.space_area_name,
        t.position_code,
        item.id,
        item.sku,
        item.actual_return_qty,
        item.supplier_delivery_no,
        item.purchase_plan_no,
        item.stockin_order_no,
        p.label_name,
        t.return_nature,
        t.freight_carrier,
        t.handle_method,
        t.operator,
        t.operate_start_date,
        t.create_by,
        t.create_date,
        t.remark,
        t.delivery_date,
        item.unqualified_category,
        ba.area_name as sourceAreaName,
        item.packing_method,
        item.version_no as versionNo
        FROM
        stockin_return_product_task AS t
        LEFT JOIN stockin_return_product_task_item AS item ON t.return_product_task_id = item.return_product_task_id
        LEFT JOIN product_label as p on p.product_id = item.product_id and (p.label_id in (4,5,7,8)) and p.is_delete = 0
        LEFT JOIN bd_area ba on item.source_area_id = ba.area_id
        <include refid="taskListWhere"></include>
    </select>

    <select id="getTaskInfo"
            resultType="com.nsy.api.wms.response.stockin.StockinReturnProductTaskInfoResponse">
        SELECT
        t.return_product_task_id,
        t.space_id as space_id,
        t.space_name as space_name,
        t.supplier_id as supplier_id,
        t.supplier_name as supplier_name,
        t.`status` as status,
        t.position_id as position_id,
        t.position_code as position_code,
        t.space_area_id as space_area_id,
        t.space_area_name as space_area_name,
        t.operator as operator,
        t.operate_start_date as operate_start_date,
        t.operate_end_date as operate_end_date,
        t.create_date as create_date,
        t.create_by as create_by,
        t.logistics_no,
        t.return_nature,
        t.delivery_date
        FROM
        stockin_return_product_task t
        where
        t.return_product_task_id = #{taskId}
    </select>

    <select id="getPdaTaskInfo"
            resultType="com.nsy.api.wms.response.stockin.StockinReturnProductSingleTaskInfo">
        SELECT
            t.return_product_task_id,
            min(t.return_nature) as return_nature,
            min(t.supplier_id) as supplier_id,
            min(t.supplier_name) as supplier_name,
            sum(item.wait_return_qty) as return_total_qty
        FROM
            stockin_return_product_task AS t
        LEFT JOIN stockin_return_product_task_item AS item ON t.return_product_task_id = item.return_product_task_id
        where
            t.position_code = #{request.positionCode}
            and
            t.status in ('ALREADY_GENERATE', 'RETURNING')
        group by
            t.return_product_task_id
    </select>

    <select id="printTask" resultType="com.nsy.api.wms.domain.stockin.StockinReturnTaskPrint">
        SELECT
            t.return_product_task_id,
            t.supplier_name,
            t.return_method,
            t.create_date,
            item.actual_return_qty
        FROM
        stockin_return_product_task AS t
        JOIN stockin_return_product_task_item AS item ON t.return_product_task_id = item.return_product_task_id
        where t.return_product_task_id in
        <foreach collection="request.idList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="countByStatus" resultType="com.nsy.api.wms.response.stockin.TaskListCountResponse">
        SELECT
        status,count(*) as qty
        FROM
        stockin_return_product_task
        <where>
            <if test="query.createStartDate!=null">
                and create_date &gt;= #{query.createStartDate}
            </if>
            <if test="query.createEndDate!=null">
                and create_date &lt;= #{query.createEndDate}
            </if>
        </where>
        GROUP BY status
    </select>

    <select id="returnOrderPageList" resultType="com.nsy.api.wms.response.stockin.StockinReturnOrderPageResponse">
        select t.return_product_task_id as returnTaskId,
        t.supplier_id,
        t.supplier_name,
        t.purchaser_real_name,
        t.return_type,
        t.return_nature,
        t.space_name,
        t.operate_start_date as returnDate,
        t.delivery_date as deliveryDate,
        t.logistics_no,
        t.logistics_company,
        t.return_reason,
        t.status,
        t.rework_status,
        t.operator,
        t.create_date,
        t.attachment_url,
        t.delivery_type
        FROM stockin_return_product_task t
        inner join supplier supplier on supplier.supplier_id = t.supplier_id
        LEFT JOIN stockin_return_product_task_item i ON i.return_product_task_id = t.return_product_task_id
        <include refid="stockinReturnOrderPageRequest"></include>
        GROUP BY t.return_product_task_id
        ORDER By t.return_product_task_id DESC
    </select>

    <select id="getTabs" resultType="com.nsy.api.wms.response.base.StatusTabResponse">
        select t.status,count(t.status) as num
        FROM stockin_return_product_task t
        inner join supplier supplier on supplier.supplier_id = t.supplier_id
        <where>
            <if test="statusList != null and statusList.size() > 0">
                t.status in
                <foreach collection="statusList" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
        </where>
        GROUP BY status
    </select>

    <select id="queryQcReportDay" resultType="com.nsy.api.wms.domain.stockin.StockinQcReportDayExport">
        SELECT
            t.operator,sum(i.actual_return_qty) returnStockoutQty
        FROM
            stockin_return_product_task t
        INNER JOIN stockin_return_product_task_item i on t.return_product_task_id = i.return_product_task_id
        where t.operate_start_date BETWEEN #{dateStart} and #{dateEnd}
        and t.status in ('IN_TRANSIT','RETURN_SUCCESS')
        GROUP BY operator
    </select>

    <select id="queryReworkedReturnedRecord" resultType="com.nsy.api.wms.response.stockin.QueryReworkedReturnedRecordResponse">
        SELECT t.return_product_task_id,
               ti.supplier_delivery_no,
               ti.supplier_delivery_box_code,
               ti.sku,
               SUM(ti.wait_return_qty) as requireReturnQty,
               SUM(ti.actual_return_qty) as actualReturnQty,
               ti.status,
               t.delivery_date as operateEndDate,
               t.logistics_no,
               t.logistics_company,
               t.operator
        FROM stockin_return_product_task t
        INNER JOIN stockin_return_product_task_item ti ON t.return_product_task_id = ti.return_product_task_id
        INNER JOIN product_spec_info ps ON ps.spec_id = ti.spec_id
        <where>
            <if test="request.purchasePlanNo != null and request.purchasePlanNo != ''">
                and ti.purchase_plan_no = #{request.purchasePlanNo}
            </if>
            <if test="request.stockinOrderNo != null and request.stockinOrderNo != ''">
                and ti.stockin_order_no = #{request.stockinOrderNo}
            </if>
            <if test="request.skc != null and request.skc != ''">
                and ps.skc = #{request.skc}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and ti.sku = #{request.sku}
            </if>
            <if test="request.location != null and request.location != ''">
                and ti.location = #{request.location}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                and ti.status in
                <foreach collection="request.statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
        </where>
        GROUP BY t.return_product_task_id,ti.supplier_delivery_box_code,ti.supplier_delivery_no,ti.sku,ti.status
    </select>

    <select id="getTaskListCount" resultType="com.nsy.api.wms.response.stockin.StockinReturnProductTaskListCountResponse">
        SELECT
        ifnull(sum(item.actual_return_qty), 0) as actual_return_qty
        FROM
        stockin_return_product_task AS t
        LEFT JOIN stockin_return_product_task_item AS item ON t.return_product_task_id = item.return_product_task_id
        <include refid="taskListWhere"></include>
    </select>

    <select id="queryReturnTaskItemList"
            resultType="com.nsy.api.wms.response.stockin.StockinReturnTaskItemInfoListResponse">
        SELECT
            distinct
            t.return_product_task_id as returnTaskId,
            t.real_return_product_task_id as realReturnTaskId,
            i.id as taskItemId,
            t.supplier_id,
            supplier.supplier_code,
            t.return_type,
            t.return_nature,
            t.space_name,
            t.purchaser_real_name,
            t.operate_start_date as returnDate,
            t.delivery_date,
            i.sku,
            pi.spu,
            psi.skc,
            i.purchase_plan_no,
            i.supplier_delivery_no,
            i.supplier_delivery_box_code,
            i.stockin_order_no,
            i.actual_return_qty as returnQty,
            t.logistics_no,
            t.logistics_company,
            i.unqualified_reason as returnReason,
            i.unqualified_category as unqualifiedCategory,
            t.status,
            t.operator,
            i.create_date,
            p.label_name,
            t.remark,
            sps.audit_date,
            srpl.create_date as receiveDate,
            t.freight_carrier
        FROM stockin_return_product_task t
        inner join supplier supplier on supplier.supplier_id = t.supplier_id
        INNER JOIN stockin_return_product_task_item i ON i.return_product_task_id = t.return_product_task_id
        INNER JOIN product_info pi on pi.product_id = i.product_id
        INNER JOIN product_spec_info psi on psi.sku = i.sku
        LEFT JOIN stock_platform_schedule sps on sps.supplier_delivery_no = i.supplier_delivery_no
        LEFT JOIN stockin_return_product_log srpl on srpl.return_product_task_id = i.return_product_task_id and srpl.type = '返工退货收货' and srpl.content like concat('%',i.sku,'%')
        LEFT JOIN product_label as p on p.product_id = i.product_id and (p.label_id in (4,5,7,8)) and p.is_delete = 0
        <include refid="stockinReturnOrderPageRequest"></include>
        group by taskItemId
    </select>

    <select id="searchByRealReturnProductTaskIdListIgnoreTenant" resultType="com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity">
        select * from stockin_return_product_task
        <where>
            <if test="returnProductTaskIdList != null and returnProductTaskIdList.size() > 0">
                real_return_product_task_id in
                <foreach collection="returnProductTaskIdList" separator="," index="index" item="returnProductTaskId" open="("
                         close=")">
                    #{returnProductTaskId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="searchByReturnProductTaskIdListIgnoreTenant" resultType="com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity">
        select * from stockin_return_product_task
        <where>
            <if test="returnProductTaskIdList != null and returnProductTaskIdList.size() > 0">
                return_product_task_id in
                <foreach collection="returnProductTaskIdList" separator="," index="index" item="returnProductTaskId" open="("
                         close=")">
                    #{returnProductTaskId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="statisticsReturnTotalQty" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(i.actual_return_qty), 0) as returnTotalQty
        FROM
            stockin_return_product_task t
            INNER JOIN stockin_return_product_task_item i ON i.return_product_task_id = t.return_product_task_id
            INNER JOIN supplier supplier ON supplier.supplier_id = t.supplier_id
        <include refid="stockinReturnOrderPageRequest"></include>
    </select>
    <select id="getByIdIgnore"
            resultType="com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity">
        select * from stockin_return_product_task where return_product_task_id = #{taskId}
    </select>

    <delete id="deleteByReturnProductTaskIdIgnoreTenant">
        delete from stockin_return_product_task where return_product_task_id = #{returnProductTaskId}
    </delete>
</mapper>
