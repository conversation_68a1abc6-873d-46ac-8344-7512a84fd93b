package com.nsy.wms.business.service.download.stockin;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinReturnOrderPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.stockin.StockinReturnOrderPageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnTaskItemInfoListResponse;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderDeliveryItemInfoDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.response.PurchaseReturnItemNewOrderNoResponse;
import com.nsy.wms.business.manage.supplier.response.QcInboundsResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserDepartmentInfoResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.internal.scm.ReturnOrderService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinReturnTaskItemService implements IDownloadService {
    @Autowired
    private StockinReturnProductTaskService returnProductTaskService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private ReturnOrderService returnOrderService;
    @Autowired
    private SupplierApiService supplierApiService;
    @Autowired
    private GcApiService gcApiService;
    @Autowired
    private UserApiService userApiService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_RETURN_ORDER_ITEM_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        StockinReturnOrderPageRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinReturnOrderPageRequest.class);
        Page<StockinReturnOrderPageResponse> page = new Page<>(downloadRequest.getPageIndex(), downloadRequest.getPageSize());
        returnOrderService.buildStockinReturnOrderPageRequest(request);
        page.setSearchCount(false);
        List<StockinReturnTaskItemInfoListResponse> responseList = returnProductTaskService.getBaseMapper().queryReturnTaskItemList(page, request);

        List<Integer> taskIdList = responseList.stream().filter(t -> StockinReturnNatureEnum.REFUND_RETURN.name().equals(t.getReturnNature()))
            .map(t -> t.getRealReturnTaskId() != null && t.getRealReturnTaskId() > 0 ? t.getRealReturnTaskId() : t.getReturnTaskId()).distinct().collect(Collectors.toList());
        Map<Integer, List<PurchaseReturnItemNewOrderNoResponse.Item>> newPurchaseNoMap = gcApiService.getNewPurchaseNo(taskIdList).stream().collect(Collectors.groupingBy(PurchaseReturnItemNewOrderNoResponse.Item::getWmsReturnTaskId));
        List<String> purchaseList = responseList.stream().filter(detail -> StringUtils.hasText(detail.getPurchasePlanNo())).map(StockinReturnTaskItemInfoListResponse::getPurchasePlanNo).distinct().collect(Collectors.toList());
        Map<String, List<PurchaseOrderDeliveryItemInfoDto>> purchaseInfoMap = this.buildPurchaseInfoList(purchaseList);
        List<SupplierDto> supplierList = scmApiService.getSupplierInfoList(responseList.stream().map(StockinReturnTaskItemInfoListResponse::getSupplierId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<String, String> stockinSupplyReturnStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_SUPPLY_RETURN_STATUS.getName());
        Map<String, String> stockinReturnTaskTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_RETURN_TASK_TYPE.getName());
        Map<String, String> stockinReturnNatureEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_RETURN_NATURE.getName());
        Map<String, String> freightCarrierEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_FREIGHT_CARRIER.getName());
        responseList.forEach(response -> {
            if (StockinReturnNatureEnum.REFUND_RETURN.name().equals(response.getReturnNature())) {
                List<PurchaseReturnItemNewOrderNoResponse.Item> items = newPurchaseNoMap.get(response.getRealReturnTaskId() != null && response.getRealReturnTaskId() > 0 ? response.getRealReturnTaskId() : response.getReturnTaskId());
                if (!CollectionUtils.isEmpty(items))
                    response.setPurchasePlanNo(items.get(0).getNewOrderNo());
            }
            response.setStatusStr(stockinSupplyReturnStatusEnumMap.get(response.getStatus()));
            response.setReturnTypeStr(stockinReturnTaskTypeEnumMap.get(response.getReturnType()));
            response.setReturnNatureStr(stockinReturnNatureEnumMap.get(response.getReturnNature()));
            response.setFreightCarrierStr(ObjectUtils.isEmpty(response.getFreightCarrier()) ? "" : freightCarrierEnumMap.get(String.valueOf(response.getFreightCarrier())));
            response.setSupplierName(supplierList.stream().filter(t -> Objects.equals(t.getSupplierId(), response.getSupplierId())).findFirst().map(SupplierDto::getSupplierName).orElse(com.alibaba.nacos.api.utils.StringUtils.EMPTY));
            if (!purchaseInfoMap.containsKey(response.getPurchasePlanNo())) {
                return;
            }
            PurchaseOrderDeliveryItemInfoDto purchaseItemInfoDto = purchaseInfoMap.get(response.getPurchasePlanNo()).stream().filter(detail -> response.getSku().equalsIgnoreCase(detail.getSku())).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(purchaseItemInfoDto)) {
                return;
            }
            response.setDepartmentName(purchaseItemInfoDto.getDepartmentName());
            response.setParentDepartmentName(purchaseItemInfoDto.getParentDepartmentName());
            response.setSupplierBuyerName(purchaseItemInfoDto.getSupplierBuyerName());
            response.setPurchaseName(purchaseItemInfoDto.getPurchaseName());
            response.setUnqualifiedQuestions(purchaseItemInfoDto.getUnqualifiedQuestions());
        });
        DownloadResponse response = new DownloadResponse();
        response.setDataJsonStr(JsonMapper.toJson(responseList));
        response.setTotalCount((long) (responseList.size() >= downloadRequest.getPageSize()
            ? (downloadRequest.getPageIndex() + 1) * downloadRequest.getPageSize()
            : (downloadRequest.getPageIndex() - 1) * downloadRequest.getPageSize() + responseList.size()));
        return response;
    }

    private Map<String, List<PurchaseOrderDeliveryItemInfoDto>> buildPurchaseInfoList(List<String> purchaseNoList) {
        if (CollUtil.isEmpty(purchaseNoList)) {
            return Collections.emptyMap();
        }
        List<PurchaseOrderDeliveryItemInfoDto> supplierSpecProductPurchaseList = scmApiService.getSupplierSpecProductPurchaseList(purchaseNoList);
        if (CollUtil.isEmpty(supplierSpecProductPurchaseList)) {
            return Collections.emptyMap();
        }
        Map<String, String> supplierReasonMap = new HashMap<>();
        List<QcInboundsResponse> inboundInfoByPurchaseNumbers = supplierApiService.getInboundInfoByPurchaseNumbers(purchaseNoList);
        if (!CollectionUtils.isEmpty(inboundInfoByPurchaseNumbers)) {
            supplierReasonMap = inboundInfoByPurchaseNumbers.stream().filter(QcInboundsResponse::hasText)
                .collect(Collectors.toMap(item -> String.format("%s_%s", item.getPurchaseNumbers(), item.getSku()), QcInboundsResponse::getUnqualifiedQuestion, this::mergeValues));
        }
        Map<Integer, SysUserDepartmentInfoResponse> userDepartmentMap = new HashMap<>();
        List<Integer> userIdList = supplierSpecProductPurchaseList.stream().map(PurchaseOrderDeliveryItemInfoDto::getSupplierBuyerId).distinct().collect(Collectors.toList());
        if (!CollUtil.isEmpty(userIdList)) {
            userDepartmentMap = userApiService.getSupplierSpecProductPurchaseList(userIdList).stream().collect(Collectors.toMap(SysUserDepartmentInfoResponse::getUserId, Function.identity(), (k1, k2) -> k2));
        }
        for (PurchaseOrderDeliveryItemInfoDto detail : supplierSpecProductPurchaseList) {
            detail.setUnqualifiedQuestions(supplierReasonMap.get(detail.getPurchaseOrderNo() + "_" + detail.getSku()));
            if (!userDepartmentMap.containsKey(detail.getSupplierBuyerId())) {
                continue;
            }
            detail.setDepartmentName(userDepartmentMap.get(detail.getSupplierBuyerId()).getDepartmentName());
            detail.setParentDepartmentName(userDepartmentMap.get(detail.getSupplierBuyerId()).getParentDepartmentName());
        }
        return supplierSpecProductPurchaseList.stream().collect(Collectors.groupingBy(PurchaseOrderDeliveryItemInfoDto::getPurchaseOrderNo));
    }

    private String mergeValues(String oldValue, String newValue) {
        return oldValue + "|" + newValue;
    }
}
