package com.nsy.wms.business.manage.thirdparty;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.request.base.PageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.common.SauPlatformAuthConfigResponse;
import com.nsy.api.wms.response.temu.SauPddConfigResponse;
import com.nsy.wms.business.manage.thirdparty.request.BdBrandStoreListRequest;
import com.nsy.wms.business.manage.thirdparty.request.PlatformAuthConfigRequest;
import com.nsy.wms.business.manage.thirdparty.request.SaStoreInfoQueryRequest;
import com.nsy.wms.business.manage.thirdparty.response.BdPlatformResponse;
import com.nsy.wms.business.manage.thirdparty.response.SaStoreBrandResponse;
import com.nsy.wms.business.manage.thirdparty.response.SaStoreDetailResponse;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.manage.thirdparty.response.SaStoreStaffingResponse;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class OmsApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OmsApiService.class);

    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.oms}")
    private String omsServiceUrl;
    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 获取拼多多鉴权信息
     *
     * @param storeId
     * @return
     */
    public SauPddConfigResponse getPddAuthByStoreId(Integer storeId) {
        String uri = String.format("%s/sau-pdd-inter-config/get-by-store-id/%s", omsServiceUrl, storeId);
        ResponseEntity<SauPddConfigResponse> response = this.restTemplate.getForEntity(uri, SauPddConfigResponse.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取通用鉴权信息
     *
     * @param storeId
     * @return
     */
    public SauPlatformAuthConfigResponse getPlatformConfigByStoreId(Integer storeId) {
        String uri = String.format("%s/sau-platform-config/get-by-store-id/%s", omsServiceUrl, storeId);
        ResponseEntity<SauPlatformAuthConfigResponse> response = this.restTemplate.getForEntity(uri, SauPlatformAuthConfigResponse.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 获取店铺信息
     *
     * @param storeId
     * @return
     */
    public SaStoreDetailResponse getStoreInfoById(Integer storeId) {
        String uri = String.format("%s/sa-store/info/%s", omsServiceUrl, storeId);
        ResponseEntity<SaStoreDetailResponse> response = this.restTemplate.getForEntity(uri, SaStoreDetailResponse.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    public void updateStoreAuthInfo(PlatformAuthConfigRequest request) {
        String uri = String.format("%s/sau-platform-config/update-store-auth-info", omsServiceUrl);
        try {
            this.restTemplate.postForObject(uri, request, String.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
    }


    /**
     * 迁移商通的旧接口/User/GetStoreInfoById
     *
     * @param storeId
     * @return
     */
    public SaStoreDetailResponse getStoreInfoByStoreId(Integer storeId) {
        String uri = String.format("%s/store-feign/get-store-by-id?id=%s", omsServiceUrl, storeId);
        ResponseEntity<SaStoreDetailResponse> response = this.restTemplate.getForEntity(uri, SaStoreDetailResponse.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 迁移商通的旧接口/User/GetStoreInfoById
     *
     * @param request
     * @return
     */
    public SaStoreDetailResponse getStoreInfoByQuery(SaStoreInfoQueryRequest request) {
        String uri = String.format("%s/store-feign/get-store-list", omsServiceUrl);
        ResponseEntity<SaStoreDetailResponse> response = this.restTemplate.getForEntity(uri, SaStoreDetailResponse.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    public List<Integer> getStoreStaffingById(Integer storeId) {
        List<Integer> result = new ArrayList<>();
        if (storeId == null) {
            return result;
        }
        String uri = String.format("%s/store-feign/get-store-staffing/%s", omsServiceUrl, storeId);
        String responseEntity = this.restTemplate.getForObject(uri, String.class);
        List<SaStoreStaffingResponse> responseList = JsonMapper.jsonStringToObjectArray(responseEntity, SaStoreStaffingResponse.class);
        if (CollectionUtils.isEmpty(responseList)) {
            return result;
        }
        responseList.forEach(resp -> {
            if (Objects.equals(resp.getStatus(), 1)) {
                result.add(resp.getStaffingId());
            }
        });
        return result;
    }

    /**
     * 获取店铺品牌信息
     *
     * @param storeId
     * @return
     */
    public String getStoreBrandInfo(Integer storeId) {
        if (storeId == null || storeId <= 0) {
            return null;
        }
        BdBrandStoreListRequest request = new BdBrandStoreListRequest();
        request.setStoreIds(Collections.singletonList(storeId));
        String uri = String.format("%s/bd-brand-store/list-brand-store", omsServiceUrl, storeId);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, request, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                SaStoreBrandResponse saStoreResponse = objectMapper.readValue(response.getBody(), SaStoreBrandResponse.class);
                if (ObjectUtils.isEmpty(saStoreResponse) || CollectionUtils.isEmpty(saStoreResponse.getBdBrandStores())) {
                    return null;
                }
                return saStoreResponse.getBdBrandStores().get(0).getBrandName();
            } catch (IOException e) {
                LOGGER.error("获取店铺品牌失败", e);
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 店铺下拉
     *
     * @return
     */
    @Cacheable(value = CacheKeyConstant.WMS_STORE_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public List<SelectModel> getStoreSelect() {
        return getAllStoreInfo().stream().map(item -> {
            SelectModel selectModel = new SelectModel();
            selectModel.setLabel(item.getStoreName());
            selectModel.setValue(item.getId().toString());
            selectModel.setBusinessType(item.getDepartment());
            return selectModel;
        }).collect(Collectors.toList());
    }

    @Cacheable(value = CacheKeyConstant.WMS_ALL_PLATFORM, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public List<BdPlatformResponse> getAllPlatformInfo() {
        String uri = String.format("%s/bd-platfrom/list", omsServiceUrl);
        ResponseEntity<String> response = this.restTemplate.getForEntity(uri, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                return objectMapper.readValue(response.getBody(), new BdPlatformInfoResponseReference());
            } catch (IOException e) {
                LOGGER.error("获取平台列表失败", e);
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 迁移商通的旧接口 /User/GetStorePageLis
     *
     * @return
     */
    @Cacheable(value = CacheKeyConstant.WMS_ALL_STORE, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public List<SaStorePageInfoResponse> getAllStoreInfo() {
        String uri = String.format("%s/store-feign/get-store-page-list", omsServiceUrl);
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageIndex(1);
        pageRequest.setPageSize(10000);
        ResponseEntity<String> response = this.restTemplate.postForEntity(uri, pageRequest, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                PageResponse<SaStorePageInfoResponse> pageResponse = objectMapper.readValue(response.getBody(), new SaStorePageInfoResponseReference());
                if (ObjectUtils.isEmpty(pageResponse) || CollectionUtils.isEmpty(pageResponse.getContent())) {
                    return Collections.emptyList();
                }
                return pageResponse.getContent();
            } catch (IOException e) {
                LOGGER.error("获取店铺列表失败", e);
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    public void backFbtLabel(String orderNo) {
        String uri = String.format("%s/inbound-plan/back-to-allocated-for-wms/%s", omsServiceUrl, orderNo);
        this.restTemplate.getForObject(uri, Void.class);
    }

    private static final class SaStorePageInfoResponseReference extends TypeReference<PageResponse<SaStorePageInfoResponse>> {
    }

    private static final class BdPlatformInfoResponseReference extends TypeReference<ArrayList<BdPlatformResponse>> {
    }

}
