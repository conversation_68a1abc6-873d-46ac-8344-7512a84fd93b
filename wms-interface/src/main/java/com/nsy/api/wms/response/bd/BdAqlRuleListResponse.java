package com.nsy.api.wms.response.bd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel("AQL规则列表响应")
public class BdAqlRuleListResponse {

    @ApiModelProperty("AQL规则ID")
    private Integer aqlRuleId;

    @ApiModelProperty("地区")
    private String location;

    @ApiModelProperty("区间从")
    private Integer rangeFrom;

    @ApiModelProperty("区间到")
    private Integer rangeTo;

    @ApiModelProperty("最少可接受严重件数")
    private Integer minAcceptQty;

    @ApiModelProperty("仓库id")
    private Integer spaceId;

    @ApiModelProperty("仓库名称")
    private String spaceName;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @ApiModelProperty("更新人")
    private String updateBy;

    public Integer getAqlRuleId() {
        return aqlRuleId;
    }

    public void setAqlRuleId(Integer aqlRuleId) {
        this.aqlRuleId = aqlRuleId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getRangeFrom() {
        return rangeFrom;
    }

    public void setRangeFrom(Integer rangeFrom) {
        this.rangeFrom = rangeFrom;
    }

    public Integer getRangeTo() {
        return rangeTo;
    }

    public void setRangeTo(Integer rangeTo) {
        this.rangeTo = rangeTo;
    }

    public Integer getMinAcceptQty() {
        return minAcceptQty;
    }

    public void setMinAcceptQty(Integer minAcceptQty) {
        this.minAcceptQty = minAcceptQty;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
} 