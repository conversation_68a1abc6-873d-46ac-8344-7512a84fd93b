package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareContractGenerateRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareContractSetUrlRequest;
import com.nsy.wms.business.domain.bo.esign.ESignFlowCreateByFileResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowKeywordPosResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowSignUrlResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutAEOAsynMsgBo;
import com.nsy.wms.business.manage.scm.response.SupplierTaxGetSampleDatailDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.service.bd.BdCompanyService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.esign.ESignService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdCompanyEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关单合同操作
 */
@Service
public class StockoutCustomsDeclareContractOpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareContractOpService.class);

    @Autowired
    StockoutCustomsDeclareContractService contractService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutCustomsDeclareContractLogService contractLogService;
    @Autowired
    StockoutCustomsDeclareFormService formService;
    @Autowired
    SupplierApiService supplierApiService;
    @Autowired
    ESignService eSignService;
    @Resource
    StockoutCustomsDeclareAEOService declareAEOService;
    @Resource
    BdCompanyService bdCompanyService;
    @Resource
    StockoutCustomsDeclareFormLogService formLogService;
    @Resource
    StockoutCustomsDeclareFormItemService formItemService;
    @Resource
    StockoutCustomsDeclareAEOService aeoService;

    /**
     * 公司确认
     * <p>
     * 1.判断合同是否待签署 且 合同未确认 且 工厂已签署
     * 2.判断是否审核通过
     * 3.是
     * * * 修改合同已确认字段 判断合同是否已完成
     * 4.否
     * * * a.合同删除链接 清空是否签署等字段，合同状态变成【待审核】
     * * * b.通知supplier
     * * * c.合同解约
     *
     * @param request
     */
//    @Transactional
//    public void companyConfirm(StockoutCustomsDeclareConstractCompanyConfirmRequest request) {
//        StockoutCustomsDeclareContractEntity contract = contractService.findById(request.getDeclareContractId());
//        //1.判断合同是否待签署 且 合同未确认 且 工厂已签署
//        validContract(contract);
//        if (request.getIsPass()) {
//            contract.setHasConfirmContract(Boolean.TRUE);
//            contract.setUpdateBy(loginInfoService.getName());
//            contractService.updateById(contract);
//            contractLogService.addLog(contract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.CONFIRM, "合同确认：通过");
//            //修改合同已确认字段 判断合同是否已完成
//            checkContractComplete(contract.getDeclareContractId());
//        } else {
//            resetContract(contract.getDeclareContractId(), request.getCompanyRejectReason(), ContractResetTypeEnum.CONFIRM, ContractSignerEnum.COMPANY, loginInfoService.getName());
//        }
//    }

//    /**
//     * 合同重置
//     * <p>
//     * * * * a.合同删除链接 清空是否签署等字段，合同状态变成【待审核】
//     * * * * b.通知supplier
//     * * * * c.合同解约
//     *
//     * @param contractId
//     * @param reason
//     * @param resetType
//     */
//    public void resetContract(Integer contractId, String reason, ContractResetTypeEnum resetType, ContractSignerEnum signer, String operator) {
//        StockoutCustomsDeclareContractEntity contract = contractService.findById(contractId);
//        //合同删除链接 清空是否签署等字段，合同状态变成【待审核】
//        contract.setCompanySignUrl("");
//        contract.setSupplierSignUrl("");
//        contract.setPreviewUrl("");
//        contract.setAuditDate(null);
//        contract.setSupplierSignDate(null);
//        contract.setCompanySignDate(null);
//        contract.setHasConfirmContract(Boolean.FALSE);
//        contract.setHasSupplierSignContract(Boolean.FALSE);
//        contract.setHasCompanySignContract(Boolean.FALSE);
//        contract.setStatus(StockoutCustomsDeclareContractStatusEnum.WAIT_AUDIT.name());
//        contract.setUpdateBy(loginInfoService.getName());
//        contractService.updateById(contract);
//        if (ContractResetTypeEnum.CONFIRM.name.equals(resetType.name)) {
//            contractLogService.addLog(contract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.CONFIRM, "合同确认：不通过，原因：" + reason);
//        } else {
//            contractLogService.addLog(contract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.ESIGN_REJECT, String.format("%s拒签，原因：%s", signer.getName(), reason));
//        }
//
//        //通知supplier
//        StockoutCustomsDeclareContractWmsAuditRequest contractAuditRequest = new StockoutCustomsDeclareContractWmsAuditRequest();
//        contractAuditRequest.setStatus("COMPANY_NO_PASS");
//        contractAuditRequest.setOperator(operator);
//        contractAuditRequest.setCompanyRejectReason(reason);
//        StockoutCustomsDeclareContractWmsAuditOneRequest contractWmsAuditOneRequest = new StockoutCustomsDeclareContractWmsAuditOneRequest();
//        contractWmsAuditOneRequest.setDeclareContractId(contractId);
//        contractWmsAuditOneRequest.setDeclareForm(contractService.buildFormWmsRejectOneRequest(contractId));
//        contractAuditRequest.setDeclareContract(Collections.singletonList(contractWmsAuditOneRequest));
//        supplierApiService.syncCustomsContractReject(contractAuditRequest);
//
//        //合同解约
//        if (ContractResetTypeEnum.CONFIRM.name.equals(resetType.name)) {
//            eSignService.revoke(contract.getSignFlowId());
//            declareAEOService.removeAEO(contract);
//        }
//    }

    /**
     * 验证合同状态
     * 判断合同是否待签署 且 合同未确认 且 工厂已签署
     *
     * @param contract
     */
//    private void validContract(StockoutCustomsDeclareContractEntity contract) {
//        if (!StockoutCustomsDeclareContractStatusEnum.WAIT_SIGN.name().equals(contract.getStatus())) {
//            throw new BusinessServiceException(String.format("合同【%s】不是【待签署】状态", contract.getDeclareContractNo()));
//        }
//        if (contract.getHasConfirmContract()) {
//            throw new BusinessServiceException(String.format("合同【%s】已确认", contract.getDeclareContractNo()));
//        }
//        if (!contract.getHasSupplierSignContract()) {
//            throw new BusinessServiceException(String.format("合同【%s】工厂未签署", contract.getDeclareContractNo()));
//        }
//    }

    /**
     * 判断合同工是否已完成
     * <p>
     * 1.【公司是否签署】等于”是“      【工厂是否签署】等于”是“    【合同是否确认】等于”是“      三个条件都满足 -> 采购合同状态更新成已完成
     * 2. 合同已完成 判断关单是否已完成
     */
    public void checkContractComplete(Integer contractId) {
        StockoutCustomsDeclareContractEntity contract = contractService.findById(contractId);
        //1.【公司是否签署】等于”是“      【工厂是否签署】等于”是“    条件都满足 -> 采购合同状态更新成已完成
        //合同确认，则工厂肯定签署，因此只需要判断合同是否确认
        if (contract.getHasCompanySignContract() && contract.getHasSupplierSignContract()) {
            contract.setStatus(StockoutCustomsDeclareContractStatusEnum.COMPLETE.name());
            contract.setUpdateBy(loginInfoService.getName());
            contractService.updateById(contract);
            contractLogService.addLog(contract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.COMPLETE, "合同已完成");
            //2. 合同已完成 判断关单是否已完成
            List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractId(contractId);
            formList.forEach(form -> formService.checkComplete(form.getDeclareFormId()));
        }
    }


    /**
     * 生成合同
     * 4.初始化合同流程 获得 SignFlowId
     * 5.获取签署链接 和 预览链接
     * 6.回填 签署链接 预览链接 SignFlowId
     */
    public void generateEsignContract(List<Integer> declareContractIdList) {
        LOGGER.info("正在处理报关合同 {} ", declareContractIdList.stream().map(Object::toString).collect(Collectors.joining()));
        List<StockoutCustomsDeclareContractEntity> contractList = contractService.listByIds(declareContractIdList);
        contractList.forEach(contract -> {
            if (!StringUtils.hasText(contract.getFileId())) {
                throw new BusinessServiceException(String.format("合同【%s】未上传e签宝", contract.getDeclareContractNo()));
            }
        });
        LOGGER.info("当前FileId {} ", contractList.stream().map(StockoutCustomsDeclareContractEntity::getFileId).collect(Collectors.joining()));
        //4.初始化合同流程 获得 SignFlowId
        StockoutCustomsDeclareContractEntity firstContract = contractList.get(0);
        SupplierTaxGetSampleDatailDto supplierInfo = contractService.getSupplierInfo(firstContract.getSupplierId(), "IN_COOPERATION");
        String signFlowId = initContract(contractList, supplierInfo.getInvoiceCompanyName());
        LOGGER.info("当前SignFlowId {} ", signFlowId);
        contractList.forEach(declareContract -> {
            //记录日志
            contractLogService.addLog(declareContract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.ESIGN_GENERATE_CONTRACT, "在线合同生成");

            //获取签署链接
            ESignFlowSignUrlResponse supplierSignUrlResponse = eSignService.getContractUrl(ESignService.ContractUrlTypeEnum.SIGN, supplierInfo.getInvoiceCompanyName(), declareContract.getSupplierAgentPhone(), signFlowId);
            ESignFlowSignUrlResponse companySignUrlResponse = eSignService.getContractUrl(ESignService.ContractUrlTypeEnum.SIGN, declareContract.getCompanyName(), declareContract.getCompanyAgentPhone(), signFlowId);
            //回填 签署链接  SignFlowId
            contractService.update(new LambdaUpdateWrapper<StockoutCustomsDeclareContractEntity>()
                    .set(StockoutCustomsDeclareContractEntity::getSignFlowId, signFlowId)
                    .set(!Objects.isNull(companySignUrlResponse), StockoutCustomsDeclareContractEntity::getCompanySignUrl, companySignUrlResponse.getUrl())
                    .set(!Objects.isNull(supplierSignUrlResponse), StockoutCustomsDeclareContractEntity::getSupplierSignUrl, supplierSignUrlResponse.getUrl())
                    .eq(StockoutCustomsDeclareContractEntity::getDeclareContractId, declareContract.getDeclareContractId()));

            //生成AEO单据
            aeoService.sendAsynOpMsg(new StockoutAEOAsynMsgBo(StockoutAEOAsynMsgBo.OperationType.SAVE_BY_CONTRACT, declareContract.getDeclareContractId(), declareContract.getDeclareContractNo()));
        });
    }

    /**
     * 初始化合同流程
     *
     * @return
     */
    public String initContract(List<StockoutCustomsDeclareContractEntity> declareContractList, String supplierCompanyName) {
        String seal = "{\"fileId\":\"%s\",\"normalSignFieldConfig\":{\"autoSign\":false,\"freeMode\":false,\"movableSignField\":false,\"signFieldPosition\":{\"positionPage\":\"%s\",\"positionX\":%s,\"positionY\":%s},\"signFieldStyle\":1}}";
        String pageSeal = "{\"fileId\":\"%s\",\"normalSignFieldConfig\":{\"signFieldStyle\":2,\"signFieldPosition\":{\"acrossPageMode\":\"ALL\",\"positionY\":%s}}}";
        String docs = "{\"fileId\":\"%s\",\"neededPwd\":false}";

        List<String> docsList = new ArrayList<>();
        List<String> companySealList = new ArrayList<>();
        List<String> supplierSealList = new ArrayList<>();

        declareContractList.forEach(contract -> {
            Map<String, ESignFlowKeywordPosResponse> keywordPosMap = eSignService.getKeywordPos(contract.getFileId(), "供方盖章,需方盖章");
            ESignFlowKeywordPosResponse supplierPos = keywordPosMap.get("供方盖章");
            ESignFlowKeywordPosResponse companyPos = keywordPosMap.get("需方盖章");

            //页数>1 盖骑缝
            Integer pageCount = eSignService.getFiles(contract.getFileId()).getFileTotalPageCount();

            docsList.add(String.format(docs, contract.getFileId()));
            companySealList.add(String.format(seal, contract.getFileId(), companyPos.getPageNum(), companyPos.getPositionX().add(BigDecimal.valueOf(150)), companyPos.getPositionY().subtract(BigDecimal.valueOf(25))));
            if (pageCount > 1)
                companySealList.add(String.format(pageSeal, contract.getFileId(), 600));
            supplierSealList.add(String.format(seal, contract.getFileId(), supplierPos.getPageNum(), supplierPos.getPositionX().add(BigDecimal.valueOf(150)), supplierPos.getPositionY().subtract(BigDecimal.valueOf(25))));
            if (pageCount > 1)
                supplierSealList.add(String.format(pageSeal, contract.getFileId(), 400));
        });

        String docsStr = String.join(",", docsList);
        String companySealStr = String.join(",", companySealList);
        String supplierSealStr = String.join(",", supplierSealList);

        StockoutCustomsDeclareContractEntity firstContract = declareContractList.get(0);

        //工厂信息
        String bodyJson = "{\"docs\":[%s],\"signers\":[{\"orgSignerInfo\":{\"orgName\":\"%s\",\"transactorInfo\":{\"psnAccount\":\"%s\"}},\"signerType\":1,\"signFields\":[%s]},{\"orgSignerInfo\":{\"orgName\":\"%s\",\"transactorInfo\":{\"psnAccount\":\"%s\"}},\"signerType\":1,\"signFields\":[%s]}],\"signFlowConfig\":{\"autoFinish\":true,\"autoStart\":true,\"signConfig\":{\"showBatchDropSealButton\":true},\"signFlowTitle\":\"%s\",\"notifyUrl\":\"%s\",\"noticeConfig\":{\"noticeTypes\":1}}}";
        bodyJson = String.format(bodyJson, docsStr, firstContract.getCompanyName(), firstContract.getCompanyAgentPhone(), companySealStr,
                supplierCompanyName, firstContract.getSupplierAgentPhone(), supplierSealStr, fetchContractName(firstContract), eSignService.getNotifyUrl());
        ESignFlowCreateByFileResponse createByFileResponse = eSignService.initContract(bodyJson);
        return createByFileResponse.getSignFlowId();
    }

    /**
     * 获取合同名称
     *
     * @param contract
     * @return
     */
    private String fetchContractName(StockoutCustomsDeclareContractEntity contract) {
        String yearMonth = DateUtil.format(contract.getCreateDate(), "yyyy.MM");
        return String.format("%s月%s合同", yearMonth, contract.getSupplierName());
    }


    /**
     * 手动生成合同
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "manualGenerateContract", lockKey = "manualGenerateContract")
    public void manualGenerateContract(StockoutCustomsDeclareContractGenerateRequest request) {
        if (CollectionUtil.isEmpty(request.getFormIdList())) {
            throw new BusinessServiceException("未选择关单");
        }
        List<StockoutCustomsDeclareFormEntity> formList = formService.listByIds(request.getFormIdList());
        formList.forEach(form -> {
            if (!StockoutCustomsDeclareFormStatusEnum.DEALT.name().equals(form.getStatus())) {
                throw new BusinessServiceException(String.format("协议号 %s 项号 %s 非已处理", form.getProtocolNo(), form.getgNo()));
            }
            if (!StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name().equals(form.getSystemMark())) {
                throw new BusinessServiceException(String.format("协议号 %s 项号 %s 非手动冲库存", form.getProtocolNo(), form.getgNo()));
            }
            if (StrUtil.isEmpty(form.getDeclareContractNo())) {
                throw new BusinessServiceException(String.format("协议号 %s 项号 %s 合同号为空", form.getProtocolNo(), form.getgNo()));
            }
        });
        //获取所有采购合同
        List<String> contractNoList = formList.stream().map(StockoutCustomsDeclareFormEntity::getDeclareContractNo).distinct().collect(Collectors.toList());
        //重新获取一次关单列表，防止忘记勾选
        Map<String, List<StockoutCustomsDeclareFormEntity>> formMap = CollectionUtil.split(contractNoList, 200).stream()
                .map(splitList -> formService.findListByContractNoList(splitList))
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(StockoutCustomsDeclareFormEntity::getDeclareContractNo));
        //手动生成合同
        formMap.values().forEach(splitFormList -> manualGenerateContract(splitFormList, request));
    }

    /**
     * 手动生成合同
     *
     * @param formList
     * @param request
     */
    private void manualGenerateContract(List<StockoutCustomsDeclareFormEntity> formList, StockoutCustomsDeclareContractGenerateRequest request) {
        //获取交货日期
        StockoutCustomsDeclareFormEntity firstForm = formList.get(0);
        //生成合同
        StockoutCustomsDeclareContractEntity contractEntity = new StockoutCustomsDeclareContractEntity();
        contractEntity.setDeclareContractNo(firstForm.getDeclareContractNo());
        contractEntity.setStatus(StockoutCustomsDeclareContractStatusEnum.COMPLETE.name());
        BdCompanyEntity bdCompanyEntity = bdCompanyService.findByCompanyName("福建新时颖电子商务有限公司");
        contractEntity.setCompanyId(bdCompanyEntity.getCompanyId());
        contractEntity.setCompanyName(bdCompanyEntity.getCompanyName());
        contractEntity.setSupplierId(firstForm.getSupplierId());
        contractEntity.setSupplierName(firstForm.getSupplierName());
        contractEntity.setQty(formList.stream().mapToInt(StockoutCustomsDeclareFormEntity::getgQty).sum());
        BigDecimal totalInputPrice = formList.stream().map(form -> formItemService.totalInputPrice(form.getDeclareFormId()))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        contractEntity.setInputPrice(totalInputPrice);
        contractEntity.setAuditDate(request.getAuditDate());
        contractEntity.setCompanySignDate(request.getCompanySignDate());
        contractEntity.setSupplierSignDate(request.getSupplierSignDate());
        contractEntity.setHasCompanySignContract(Boolean.TRUE);
        contractEntity.setHasSupplierSignContract(Boolean.TRUE);
        contractEntity.setHasConfirmContract(Boolean.TRUE);
        contractService.save(contractEntity);

        //关单更新已完成
        formList.forEach(form -> {
            form.setDeclareContractId(contractEntity.getDeclareContractId());
            form.setStatus(StockoutCustomsDeclareFormStatusEnum.COMPLETE.name());
            form.setUpdateBy(loginInfoService.getName());
            formLogService.addLog(form.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.DONE, "手动创建合同，状态变更【已完成】");
        });
        formService.updateBatchById(formList);
        //记录合同日志
        contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.SET_URL, "设置合同地址");
        //生成AEO
        //生成AEO单据
        aeoService.sendAsynOpMsg(new StockoutAEOAsynMsgBo(StockoutAEOAsynMsgBo.OperationType.SAVE_BY_CONTRACT, contractEntity.getDeclareContractId(), contractEntity.getDeclareContractNo()));
    }

    /**
     * 合同设置地址
     *
     * @param request
     */
    @Transactional
    public void contractSetUrl(StockoutCustomsDeclareContractSetUrlRequest request) {
        StockoutCustomsDeclareContractEntity contract = contractService.getById(request.getContractId());
        if (Objects.isNull(contract))
            throw new BusinessServiceException("合同不存在");
        contract.setContractUrl(request.getUrl());
        contract.setUpdateBy(loginInfoService.getName());
        contractService.updateById(contract);

        contractLogService.addLog(contract.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.GENERATE_CONTRACT, "手动生成合同");

    }


    /**
     * 合同重置类型
     */
    public enum ContractResetTypeEnum {
        CONFIRM(1, "用户确认"),
        ESIGN_REJECT(2, "E签宝拒签");

        ContractResetTypeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private String name;

        private Integer value;

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

    }

    /**
     * 合同签约方
     */
    public enum ContractSignerEnum {
        COMPANY(1, "需方"),
        SUPPLIER(2, "供方");

        ContractSignerEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        private String name;

        private Integer value;

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }

    }
}
