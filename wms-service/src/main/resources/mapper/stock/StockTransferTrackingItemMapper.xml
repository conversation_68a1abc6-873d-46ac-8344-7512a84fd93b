<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockTransferTrackingItemMapper">
    <select id="queryOnTheWayStock" resultType="com.nsy.api.wms.response.stock.StockTransferTrackingSkuResponse$SpaceInfo">
        SELECT
            stti.space_id AS spaceId,
            SUM(stti.stock_out_qty) AS onTheWayStock
        FROM
            stock_transfer_tracking_item stti
        INNER JOIN stock_transfer_tracking stt ON stti.tracking_id = stt.id
        WHERE
            stti.sku = #{sku}
            AND stt.status = 'TRANSPORT'
            <if test="spaceIdList != null and spaceIdList.size() > 0">
                AND stti.space_id IN
                <foreach collection="spaceIdList" item="spaceId" open="(" separator="," close=")">
                    #{spaceId}
                </foreach>
            </if>
        GROUP BY
            stti.space_id
    </select>

    <select id="queryTransferTrackingDetail" resultType="com.nsy.api.wms.response.stock.StockTransferTrackingDetailResponse">
        SELECT
            stti.sku,
            stt.order_no AS orderNo,
            so.store_name AS storeName,
            stti.stock_out_qty AS shipQty,
            bs.space_name AS targetSpaceName,
            stt.logistics_company AS logisticsCompany,
            so.delivery_date AS shipDate,
            stti.space_id AS spaceId
        FROM
            stock_transfer_tracking_item stti
        INNER JOIN stock_transfer_tracking stt ON stti.tracking_id = stt.id
        LEFT JOIN bd_space bs ON stti.space_id = bs.space_id
        LEFT JOIN stockout_order_item soi ON stt.order_no = soi.order_no
        LEFT JOIN stockout_order so ON soi.stockout_order_id = so.stockout_order_id
        WHERE
            stt.status = 'TRANSPORT'
            AND so.status = 'DELIVERED'
            <if test="skuList != null and skuList.size() > 0">
                AND stti.sku IN
                <foreach collection="skuList" item="sku" open="(" separator="," close=")">
                    #{sku}
                </foreach>
            </if>
        GROUP BY
            stti.tracking_item_id
        ORDER BY
            stti.sku,
            so.delivery_date DESC
    </select>
</mapper>
