package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.NoneDataException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.domain.stockout.ShipmentTransferSku;
import com.nsy.api.wms.domain.stockout.SkuDTO;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemSku;
import com.nsy.api.wms.domain.stockout.StockoutShipmentModel;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.common.IsEnum;
import com.nsy.api.wms.enumeration.stock.StockoutScanTaskDetailEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPackingListStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutOrderLogisticsCompanyRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentAddRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSkuPageRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutConfirmShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderScanTaskValidateResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentLogisticsRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.amazon.AmazonApiService;
import com.nsy.wms.business.service.bd.BdBoxSpecificationsService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockMaterialCodeGeneralService;
import com.nsy.wms.business.service.stock.StockTransferTrackingService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.building.StockoutShipmentBuilding;
import com.nsy.wms.business.service.stockout.valid.StockoutShipmentValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockMaterialCodeGenerateEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutReceiverInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 装箱清单
 *
 * <AUTHOR>
 */
@Service
public class StockoutShipmentService extends ServiceImpl<StockoutShipmentMapper, StockoutShipmentEntity> {
    @Autowired
    private StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    private ProductSpecInfoService specInfoService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private BdSpaceService spaceService;
    @Autowired
    private ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentErpPickingBoxService shipmentErpPickingBoxService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    StockoutOrderScanLogService scanLogService;
    @Autowired
    StockoutOrderScanCheckService checkService;
    @Autowired
    StockoutOrderItemMapper stockoutOrderItemMapper;
    @Autowired
    StockoutShipmentItemMapper stockoutShipmentItemMapper;
    @Autowired
    StockMaterialCodeGeneralService codeGeneralService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutReceiverInfoService receiverInfoService;
    @Autowired
    StockoutShipmentConfirmService confirmService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutShipmentCustomsService stockoutShipmentCustomsService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    MessageProducer producer;
    @Autowired
    StockoutShipmentValid shipmentValid;
    @Autowired
    StockoutShipmentPackService packService;
    @Autowired
    StockoutOrderTemuExtendInfoService temuExtendInfoService;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    StockoutFaireShipmentService stockoutFaireShipmentService;
    @Autowired
    AmazonApiService amazonApiService;
    @Autowired
    private BdPositionService bdPositionService;
    @Autowired
    BdBoxSpecificationsService specificationsService;
    @Autowired
    StockoutShipmentPackTaskService packTaskService;
    @Autowired
    StockoutShipmentPasteTaskService pasteTaskService;
    @Autowired
    StockTransferTrackingService transferTrackingService;

    public List<StockoutShipmentEntity> findByShipmentIdsList(List<Integer> shipmentIdList) {
        LambdaQueryWrapper<StockoutShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentEntity::getShipmentId, shipmentIdList);
        queryWrapper.eq(StockoutShipmentEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public List<StockoutShipmentEntity> findByShipmentIdsListOrderByIndex(List<Integer> shipmentIdList) {
        LambdaQueryWrapper<StockoutShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentEntity::getShipmentId, shipmentIdList);
        queryWrapper.eq(StockoutShipmentEntity::getIsDeleted, 0);
        queryWrapper.orderByAsc(StockoutShipmentEntity::getBoxIndex);
        return list(queryWrapper);
    }

    public List<StockoutShipmentEntity> findByLogisticsNo(String logisticsNo) {
        LambdaQueryWrapper<StockoutShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutShipmentEntity::getIsDeleted, 0);
        queryWrapper.eq(StockoutShipmentEntity::getLogisticsNo, logisticsNo);
        return list(queryWrapper);
    }

    public Set<Integer> findByShipmentIdsListReturnId(List<Integer> shipmentIdList) {
        LambdaQueryWrapper<StockoutShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentEntity::getShipmentId, shipmentIdList);
        queryWrapper.eq(StockoutShipmentEntity::getIsDeleted, 0);
        queryWrapper.select(StockoutShipmentEntity::getShipmentId);
        List<StockoutShipmentEntity> stockoutShipmentEntities = this.getBaseMapper().selectList(queryWrapper);
        return stockoutShipmentEntities.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toSet());
    }

    public StockoutShipmentResponse get(Integer id) {
        StockoutShipmentResponse stockoutShipmentResponse = new StockoutShipmentResponse();
        StockoutShipmentModel stockoutShipment = new StockoutShipmentModel();
        StockoutShipmentEntity stockoutShipmentEntity = getById(id);
        if (stockoutShipmentEntity == null)
            throw new BusinessServiceException("装箱清单-找不到" + id + "id记录");
        BeanUtils.copyProperties(stockoutShipmentEntity, stockoutShipment);
        stockoutShipment.setStatus(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKING_LIST_STATUS.getName(), stockoutShipment.getStatus()));
        stockoutShipment.setBoxSizeText(specificationsService.getValueByInfo(stockoutShipment.getBoxSize()));
        stockoutShipment.setPackTypeLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKAGING_TYPE.getName(), stockoutShipment.getPackType()));
        stockoutShipmentResponse.setStockoutShipment(stockoutShipment);
        return stockoutShipmentResponse;
    }

    public PageResponse<StockoutShipmentSearchResult> searchList(StockoutShipmentSearchRequest request) {
        StockoutBuilding.buildRequest(request);
        PageResponse<StockoutShipmentSearchResult> pageResponse = new PageResponse<>();
        pageResponse.setContent(new ArrayList<>());
        Page<StockoutShipmentSearchResult> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        //存在订单号、波次号、出库单号，先查询明细
        try {
            queryItemPutShipmentId(request);
        } catch (NoneDataException e) {
            pageResponse.setTotalCount(0);
            return pageResponse;
        }
        IPage<Integer> pageResult = stockoutShipmentMapper.pageSearchShipmentIds(page, request);
        pageResponse.setTotalCount(stockoutShipmentMapper.pageSearchShipmentIdsCount(request));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return pageResponse;
        }
        List<StockoutShipmentItemEntity> shipmentItems = shipmentItemService.findByShipmentIdList(pageResult.getRecords());
        Map<Integer, String> spaceMap = spaceService.getNameSelect().stream().collect(Collectors.toMap(SelectIntegerModel::getValue, SelectIntegerModel::getLabel));
        // 每个装箱对应的明细
        Map<Integer, List<StockoutShipmentItemEntity>> itemMap = shipmentItems.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId));
        Map<String, String> pickingListStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKING_LIST_STATUS.getName());
        Map<String, String> boxStandardsEnumMap = specificationsService.getBdBoxSpecificationsMap();
        pageResult.getRecords().forEach(shipmentId -> {
            StockoutShipmentEntity shipment = getById(shipmentId);
            StockoutShipmentSearchResult item = new StockoutShipmentSearchResult();
            BeanUtilsEx.copyProperties(shipment, item);
            item.setStatus(pickingListStatusEnumMap.get(item.getStatus()));
            item.setBoxSize(boxStandardsEnumMap.get(item.getBoxSize()));
            List<StockoutShipmentItemEntity> itemEntityList = itemMap.get(shipmentId);
            if (!CollectionUtils.isEmpty(itemEntityList)) {
                // 不是空箱子
                List<String> stockoutOrderNos = itemEntityList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
                List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos);
                // 组装出库单信息
                item.setStockoutOrderNoList(stockoutOrderNos);
                item.setOrderNoList(StockoutBuilding.shipmentOrderList(itemEntityList));
                item.setStoreName(stockoutOrderList.stream().map(StockoutOrderEntity::getStoreName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(StringConstant.COMMA)));
                item.setBatchId(itemEntityList.stream().map(it -> it.getBatchId() == null ? "" : it.getBatchId().toString()).filter(StringUtils::hasText).distinct().collect(Collectors.joining(StringConstant.COMMA)));
                item.setSpaceName(stockoutOrderList.stream().map(it -> spaceMap.getOrDefault(it.getSpaceId(), "")).distinct().filter(StringUtils::hasText).collect(Collectors.joining(StringConstant.COMMA)));
                item.setWorkspace(stockoutOrderList.stream().map(it -> StockoutOrderWorkSpaceEnum.getNameBy(it.getWorkspace())).distinct().filter(StringUtils::hasText).collect(Collectors.joining(StringConstant.COMMA)));
                item.setSpaceAreaName(itemEntityList.stream().map(StockoutShipmentItemEntity::getSpaceAreaName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
            }
            item.setBoxSkuAmount(itemEntityList.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum());
            pageResponse.getContent().add(item);
        });
        buildReceiverNameList(pageResponse);
        return pageResponse;
    }

    private void queryItemPutShipmentId(StockoutShipmentSearchRequest request) throws NoneDataException {
        //存在订单号、波次号、出库单号，先查询明细
        if (StringUtils.hasText(request.getOrderNo()) || !CollectionUtils.isEmpty(request.getOrderNos())
                || StringUtils.hasText(request.getStockoutOrderNo()) || !CollectionUtils.isEmpty(request.getStockoutNos())
                || Objects.nonNull(request.getStockoutBatchId())) {
            List<Integer> shipmentIdList = request.getShipmentIdList();
            if (CollectionUtils.isEmpty(shipmentIdList)) {
                shipmentIdList = new ArrayList<>();
            }
            List<Integer> shipmentIds = shipmentItemService.getBaseMapper().queryItemPutShipmentId(request);
            if (CollectionUtils.isEmpty(shipmentIds)) {
                throw new NoneDataException();
            }

            shipmentIdList.addAll(shipmentIds);
            request.setShipmentIdList(shipmentIdList);
        }

    }

    private void buildReceiverNameList(PageResponse<StockoutShipmentSearchResult> pageResponse) {
        List<String> stockoutOrderNoList = pageResponse.getContent().stream().map(StockoutShipmentSearchResult::getStockoutOrderNoList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (stockoutOrderNoList.isEmpty()) return;
        Map<String, StockoutReceiverInfoEntity> receiverInfoMap = stockoutReceiverInfoService.getByStockoutOrderNos(stockoutOrderNoList).stream().collect(Collectors.toMap(StockoutReceiverInfoEntity::getStockoutOrderNo, Function.identity()));
        pageResponse.getContent().forEach(item -> {
            if (Objects.isNull(item.getStockoutOrderNoList()) || item.getStockoutOrderNoList().isEmpty()) return;
            for (String stockoutOrderNo : item.getStockoutOrderNoList()) {
                StockoutReceiverInfoEntity receiverInfo = receiverInfoMap.get(stockoutOrderNo);
                if (Objects.isNull(receiverInfo)) return;
                item.getReceiverNameSet().add(receiverInfo.getReceiverName());
            }
        });
    }


    public StockoutShipmentResponse getByShipmentBoxCode(String shipmentBoxCode) {
        StockoutShipmentResponse stockoutShipmentResponse = new StockoutShipmentResponse();
        StockoutShipmentModel stockoutShipment = new StockoutShipmentModel();
        StockoutShipmentEntity stockoutShipmentEntity = findTopByShipmentBoxCode(shipmentBoxCode);
        BeanUtils.copyProperties(stockoutShipmentEntity, stockoutShipment);
        List<String> orderNoList = shipmentItemService.getOrderNosByShipmentIds(Collections.singletonList(stockoutShipmentEntity.getShipmentId()));
        stockoutShipment.setPackTypeLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKAGING_TYPE.getName(), stockoutShipment.getPackType()));
//        stockoutShipment.setPackTypeLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKAGING_TYPE.getName(), stockoutShipment.getPackType()));
        StringBuilder orderNoStr = new StringBuilder();
        for (String item : orderNoList) {
            if (item != null)
                orderNoStr.append(',').append(item);
        }
        if (StringUtils.hasText(orderNoStr.toString())) {
            stockoutShipment.setOrderNoStr(orderNoStr.toString().replaceFirst(",", ""));
        }
        stockoutShipmentResponse.setStockoutShipment(stockoutShipment);
        return stockoutShipmentResponse;
    }

    public List<StockoutShipmentEntity> getByLogisticNo(String logisticsNo) {
        return this.list(new LambdaQueryWrapper<StockoutShipmentEntity>().eq(StockoutShipmentEntity::getLogisticsNo, logisticsNo)
                .eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED));
    }

    @Transactional
    @JLock(keyConstant = "updateStockoutShipment", lockKey = "#request.shipmentBoxCode")
    public void updateStockoutShipment(StockoutShipmentUpdateRequest request) {
        request.setLogisticsNo(StrUtil.trim(request.getLogisticsNo()));
        StockoutShipmentEntity shipment = findTopByShipmentBoxCode(request.getShipmentBoxCode());
        String oldCompany = shipment.getLogisticsCompany();
        // 校验相同boxIndex
        shipmentValid.validSameBoxIndex(shipment, request.getBoxIndex());
        BeanUtils.copyProperties(request, shipment);
        shipment.setUpdateBy(loginInfoService.getName());
        this.updateById(shipment);
        if (request.getForwarderChannel() == null) {
            LambdaUpdateWrapper<StockoutShipmentEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(StockoutShipmentEntity::getShipmentId, shipment.getShipmentId())
                    .set(StockoutShipmentEntity::getForwarderChannel, request.getForwarderChannel());
            this.update(updateWrapper);
        }
        List<StockoutShipmentItemEntity> shipmentItemEntities = shipmentItemService.findByShipmentId(shipment.getShipmentId());
        if (shipmentItemEntities.isEmpty())
            throw new BusinessServiceException("当前箱子为空箱，无法操作，请确认");
        // 异步同步erp装箱清单
        shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(shipment);
        if (StockoutShipmentStatusEnum.SHIPPED.name().equalsIgnoreCase(shipment.getStatus()))
            shipmentErpPickingBoxService.sendErpPickingBoxShippedSync(shipment);
        if (!Objects.equals(oldCompany, request.getLogisticsCompany())) {
            List<String> stockoutOrder = shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).collect(Collectors.toList());
            List<StockoutOrderEntity> byStockoutOrderNoList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrder);
            stockoutOrderLogService.addShipmentChangeLog(request.getLogisticsCompany(), byStockoutOrderNoList, Collections.singletonList(shipment.getShipmentId()));
            byStockoutOrderNoList.forEach(orderEntity -> {
                if (StrUtil.equals(orderEntity.getWorkspace(), StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name())) {
                    orderEntity.setLogisticsCompany(request.getLogisticsCompany());
                    stockoutOrderService.updateById(orderEntity);
                }
            });
        }
    }

    public StockoutShipmentEntity findTopByShipmentBoxCode(String shipmentBoxCode) {
        StockoutShipmentEntity one = this.getOne(new QueryWrapper<StockoutShipmentEntity>().lambda()
                .eq(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCode).eq(StockoutShipmentEntity::getIsDeleted, 0)
                .last("limit 1"));
        if (Objects.isNull(one))
            throw new BusinessServiceException("未找到" + shipmentBoxCode + "的装箱记录");
        return one;
    }

    /**
     * 确认装箱
     * step1：装箱清单新增 | 更新
     * step2：复核任务装箱数更新
     * step3：装箱清单明细新增
     * step6：出库单发货数 = 订单数，自动完成复核扫描
     */
    @Transactional
    public StockoutConfirmShipmentResponse addShipment(String stockoutOrderNo, StockoutShipmentAddRequest request) {
        StockoutConfirmShipmentResponse response = new StockoutConfirmShipmentResponse();
        StockoutOrderScanTaskValidateResponse validateResponse = checkService.validateScanTask(stockoutOrderNo);
        // step1：装箱清单 更新
        StockoutShipmentEntity stockoutShipmentEntity = buildShipmentEntity(request, validateResponse.getStockoutOrderInfo());
        // 异步同步erp装箱清单
        shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(stockoutShipmentEntity);
        // step2：复核任务装箱数更新
        StockoutOrderScanTaskEntity taskEntity = new StockoutOrderScanTaskEntity();
        taskEntity.setTaskId(validateResponse.getScanTask().getTaskId());
        taskEntity.setTotalBoxQty(request.getBoxIndex());
        taskEntity.setUpdateBy(loginInfoService.getName());
        scanTaskService.updateById(taskEntity);
        // step2.1: 透明计划 箱子内预配T code
        transparencyCodeService.buildShipmentInfo(stockoutShipmentEntity, stockoutOrderNo);
        // step3：出库单发货数 = 订单数，自动完成复核扫描
        int unShippedCount = (int) stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().lambda().eq(StockoutOrderItemEntity::getStockoutOrderId, validateResponse.getStockoutOrderInfo().getStockoutOrderId()))
                .stream().filter(o -> !o.getQty().equals(o.getShipmentQty())).count();
        if (unShippedCount <= 0) {
            checkService.completeScan(stockoutOrderNo, null);
            response.setCompleteScan(Boolean.TRUE);
            if (StockoutOrderTypeEnum.PROCESS_DELIVERY.name().equals(validateResponse.getStockoutOrderInfo().getStockoutType()))
                response.setProcessWork(Boolean.TRUE);
        }
        response.setStockoutType(validateResponse.getStockoutOrderInfo().getStockoutType());
        scanLogService.addScanLog(taskEntity.getTaskId(), "确认装箱", String.format("箱子【%s】装箱完成，同步erp。%s", stockoutShipmentEntity.getShipmentBoxCode(), unShippedCount <= 0 ? "任务下所有的sku都已完成" : ""), loginInfoService.getName());
        response.setStockoutOrderInfo(validateResponse.getStockoutOrderInfo());
        return response;
    }

    /**
     * 获取装箱信息
     */
    public StockoutShipmentInfo getShipmentInfo(String stockoutOrderNo, String shipmentBoxCode) {
        StockoutShipmentInfo result = new StockoutShipmentInfo();
        StockoutOrderScanTaskEntity taskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda()
                .eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderNo));
        if (taskEntity == null)
            throw new BusinessServiceException("复核任务不存在");
        shipmentItemService.validAllShipmentConfirm(stockoutOrderNo, shipmentBoxCode);
        List<String> orderNoList = stockoutOrderItemMapper.searchOrderNo(stockoutOrderNo);
        // 如果箱子不唯一，则判断箱子状态（非发货箱子） / 发货地址
        StockoutShipmentEntity shipmentEntity = getOne(new QueryWrapper<StockoutShipmentEntity>().lambda().eq(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCode));
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (shipmentEntity != null) {
            if (shipmentEntity.getStatus().equals(StockoutShipmentStatusEnum.SHIPPED.name()))
                throw new BusinessServiceException("箱子编号已发货，请确定");
            //
            validSameReceiver(shipmentEntity, stockoutOrder);
        } else {
            // 如果箱子唯一，新增装箱
            StockMaterialCodeGenerateEntity codeGenerateEntity = codeGeneralService.getOne(new QueryWrapper<StockMaterialCodeGenerateEntity>().lambda()
                    .eq(StockMaterialCodeGenerateEntity::getBoxCode, shipmentBoxCode));
            if (codeGenerateEntity == null)
                throw new BusinessServiceException("当前箱子不在物料箱子中，请申请箱子领用");
            shipmentEntity = StockoutBuilding.buildSortShipment(stockoutOrder, loginInfoService.getName(), shipmentBoxCode);
            shipmentEntity.setBoxSize(codeGenerateEntity.getMaterialSize());
            // 第几个箱子
            LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo)
                    .eq(StockoutShipmentItemEntity::getIsDeleted, 0);
            List<StockoutShipmentItemEntity> list = shipmentItemService.list(wrapper);
            if (!CollectionUtils.isEmpty(list)) {
                List<Integer> collect = list.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
                StockoutShipmentEntity shipment = getOne(new LambdaQueryWrapper<StockoutShipmentEntity>()
                        .in(StockoutShipmentEntity::getShipmentId, collect).orderByDesc(StockoutShipmentEntity::getBoxIndex).last("limit 1"));
                shipmentEntity.setBoxIndex(shipment == null || shipment.getBoxIndex() == null ? 1 : shipment.getBoxIndex() + 1);
            }
            save(shipmentEntity);
        }
        result.setBoxIndex(shipmentEntity.getBoxIndex());
        result.setBoxSize(shipmentEntity.getBoxSize());
        result.setWeight(shipmentEntity.getWeight());
        result.setVolumeWeight(shipmentEntity.getVolumeWeight());
        result.setRemark(shipmentEntity.getRemark());
        result.setShipmentBoxCode(shipmentBoxCode);
        orderNoList.addAll(stockoutShipmentItemMapper.searchOrderNo(shipmentBoxCode));
        result.setOrderNos(StringUtils.join(orderNoList, ','));
        result.setSkuDTOS(buildScanShipmentSku(shipmentEntity.getShipmentId()));
        if (shipmentItemService.count(new QueryWrapper<StockoutShipmentItemEntity>().lambda().eq(StockoutShipmentItemEntity::getShipmentId, shipmentEntity.getShipmentId())) > 0)
            result.setIsEmpty(0);
        else
            result.setIsEmpty(1);
        return result;
    }

    private List<SkuDTO> buildScanShipmentSku(Integer shipmentId) {
        List<StockoutShipmentItemEntity> list = shipmentItemService.list(new QueryWrapper<StockoutShipmentItemEntity>().lambda()
                .eq(StockoutShipmentItemEntity::getShipmentId, shipmentId).eq(StockoutShipmentItemEntity::getIsDeleted, IsEnum.IS_NOT.getCode()));
        return list.stream().map(item -> {
            SkuDTO dto = new SkuDTO();
            dto.setShipmentItemId(item.getShipmentItemId());
            dto.setStockoutOrderItemId(item.getStockoutOrderItemId());
            dto.setSku(item.getSku());
            dto.setQty(item.getQty());
            return dto;
        }).collect(Collectors.toList());
    }

    private void validSameReceiver(StockoutShipmentEntity shipmentEntity, StockoutOrderEntity stockoutOrder) {
        List<StockoutShipmentItemEntity> shipmentItems = shipmentItemService.findByShipmentId(shipmentEntity.getShipmentId());
        if (CollectionUtils.isEmpty(shipmentItems)) {
            return;
        }

        if (StrUtil.equals(stockoutOrder.getWorkspace(), StockoutOrderWorkSpaceEnum.FBA_AREA.name())) {
            List<StockoutOrderEntity> stockoutOrderNoList = stockoutOrderService.getByStockoutOrderNoList(shipmentItems.stream()
                    .map(StockoutShipmentItemEntity::getStockoutOrderNo).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(stockoutOrderNoList)) {
                return;
            }
            if (stockoutOrderNoList.stream().anyMatch(it -> !it.getStoreId().equals(stockoutOrder.getStoreId()) || !StrUtil.equals(it.getLogisticsCompany(), stockoutOrder.getLogisticsCompany()))) {
                throw new BusinessServiceException("不同店铺/不同物流的出库单不能装在同一个箱子！");
            }
        } else {
            List<StockoutReceiverInfoEntity> receivers = receiverInfoService.getByStockoutOrderNos(shipmentItems.stream()
                    .map(StockoutShipmentItemEntity::getStockoutOrderNo).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(receivers)) {
                return;
            }
            StockoutReceiverInfoEntity receiver = receiverInfoService.getByStockoutOrderId(stockoutOrder.getStockoutOrderId());
            if (receivers.stream().anyMatch(item -> !Objects.equals(item.getReceiverInfo(), receiver.getReceiverInfo()))) {
                throw new BusinessServiceException("不同地址的出库单不允许装在同一箱");
            }
        }
    }

    /**
     * 根据出库单号 + 箱子编号（判断是否有数据），获取sku信息
     */
    public PageResponse<StockoutShipmentItemSku> searchSkuList(String stockoutOrderNo, StockoutShipmentSkuPageRequest request) {
        PageResponse<StockoutShipmentItemSku> pageResponse = new PageResponse<>();
        if (!StringUtils.hasText(stockoutOrderNo)) {
            throw new BusinessServiceException("出库单号不能为空");
        }
        IPage<StockoutShipmentItemSku> scanTaskItemSku = stockoutShipmentItemMapper.searchShipmentItemSku(new Page(request.getPageIndex(),
                request.getPageSize()), request.getShipmentBoxCode());
        pageResponse.setContent(scanTaskItemSku.getRecords());
        pageResponse.setTotalCount(scanTaskItemSku.getTotal());
        return pageResponse;
    }

    private StockoutShipmentEntity buildShipmentEntity(StockoutShipmentAddRequest request, StockoutOrderInfo stockoutOrderInfo) {
        if (!StringUtils.hasText(request.getShipmentBoxCode())) {
            throw new BusinessServiceException("请输入箱子进行装箱");
        }
        // 物料编码生成表改为已使用
        StockMaterialCodeGenerateEntity codeGenerateEntity = codeGeneralService.getOne(new QueryWrapper<StockMaterialCodeGenerateEntity>().lambda()
                .eq(StockMaterialCodeGenerateEntity::getBoxCode, request.getShipmentBoxCode())
                .eq(StockMaterialCodeGenerateEntity::getIsUsed, 0));
        if (codeGenerateEntity != null) {
            codeGenerateEntity.setIsUsed(1);
            codeGenerateEntity.setUpdateBy(loginInfoService.getName());
            codeGeneralService.updateById(codeGenerateEntity);
        }
        StockoutShipmentEntity stockoutShipmentEntity = getOne(new QueryWrapper<StockoutShipmentEntity>().lambda()
                .eq(StockoutShipmentEntity::getShipmentBoxCode, request.getShipmentBoxCode()).last("limit 1"));
        if (stockoutShipmentEntity == null) {
            throw new BusinessServiceException(request.getShipmentBoxCode() + "未找到对应的装箱清单，请核对");
        }
        shipmentValid.validSameBoxIndex(stockoutShipmentEntity, request.getBoxIndex());
        stockoutShipmentEntity.setUpdateBy(loginInfoService.getName());
        stockoutShipmentEntity.setBoxIndex(request.getBoxIndex());
        stockoutShipmentEntity.setBoxSize(request.getBoxSize());
        if (!Objects.isNull(request.getWeight())) {
            stockoutShipmentEntity.setWeight(request.getWeight());
        } else { //拼多多填预估重量
            setWeight(stockoutShipmentEntity, stockoutOrderInfo.getStockoutOrderNo());
        }
        stockoutShipmentEntity.setVolumeWeight(request.getVolumeWeight());
        if (StockoutShipmentStatusEnum.PACKING.name().equals(stockoutShipmentEntity.getStatus()))
            stockoutShipmentCustomsService.generateCustomsOrder(Lists.newArrayList(stockoutShipmentEntity.getShipmentId()));
        stockoutShipmentEntity.setStatus(StockoutShipmentStatusEnum.PACKING_END.name());
        stockoutShipmentEntity.setRemark(request.getRemark());
        stockoutShipmentEntity.setIsDeleted(IsEnum.IS_NOT.getCode());
        stockoutShipmentEntity.setShipmentDate(new Date());
        stockoutShipmentEntity.setCustomsDeclareType(stockoutOrderInfo.getCustomerDeclareTypeStr());
        if (StringUtils.hasText(stockoutOrderInfo.getLogisticsCompany()) && !StringUtils.hasText(stockoutShipmentEntity.getLogisticsCompany())) {
            stockoutShipmentEntity.setLogisticsCompany(stockoutOrderInfo.getLogisticsCompany());
        }
        if (StringUtils.hasText(stockoutOrderInfo.getLogisticsNo()) && !StringUtils.hasText(stockoutShipmentEntity.getLogisticsNo())) {
            stockoutShipmentEntity.setLogisticsNo(stockoutOrderInfo.getLogisticsNo());
        }
        stockoutShipmentEntity.setStockoutType(stockoutOrderInfo.getStockoutType());
        stockoutShipmentEntity.setPlatformName(stockoutOrderInfo.getPlatformName());
        updateById(stockoutShipmentEntity);
        //生成报关订单
        stockoutShipmentCustomsService.generateCustomsOrder(stockoutShipmentEntity.getShipmentId());
        //生成贴码任务和打包任务
        packTaskService.add(stockoutShipmentEntity.getShipmentId());
        pasteTaskService.add(stockoutShipmentEntity.getShipmentId());
        return stockoutShipmentEntity;
    }

    private void setWeight(StockoutShipmentEntity stockoutShipmentEntity, String stockoutOrderNo) {
        if (!temuExtendInfoService.validTemuOrder(stockoutOrderNo)) return;

        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findByShipmentId(stockoutShipmentEntity.getShipmentId());
        if (shipmentItemList.isEmpty())
            return;

        List<ProductSpecInfoEntity> allBySkuIn = specInfoService.findAllBySkuIn(shipmentItemList.stream().map(StockoutShipmentItemEntity::getSku).distinct().collect(Collectors.toList()));
        BigDecimal weight = StockoutBuilding.getPayWeightByShipment(shipmentItemList, allBySkuIn);
        stockoutShipmentEntity.setWeight(weight);
    }

    public StockoutShipmentItemEntity buildByStockoutOrderItemAndQty(StockoutOrderItemInfo stockoutOrderItemEntity, Integer qty, String stockoutOrderNo, Integer shipmentId) {
        StockoutBatchOrderEntity batchOrderEntity = batchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderItemEntity.getStockoutOrderId());
        StockoutShipmentItemEntity entity = StockoutBuilding.buildByStockoutOrderItemAndQty(stockoutOrderItemEntity, qty, stockoutOrderNo, shipmentId, batchOrderEntity);
        if (StringUtils.hasText(stockoutOrderItemEntity.getPositionCode())) {
            BdPositionEntity positionByCode = bdPositionService.getPositionByCode(stockoutOrderItemEntity.getPositionCode());
            entity.setSpaceAreaId(positionByCode.getSpaceAreaId());
            entity.setSpaceAreaName(positionByCode.getSpaceAreaName());
        }

        entity.setCreateBy(loginInfoService.getName());
        return entity;
    }

    public StockoutShipmentItemSkuInfoResponse getSkuListByShipmentBoxCode(String shipmentBoxCode, Integer isOriginBox) {
        StockoutShipmentEntity shipmentEntity = this.getOne(new QueryWrapper<StockoutShipmentEntity>().lambda().eq(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCode).last("limit 1"));
        if (shipmentEntity == null)
            throw new BusinessServiceException(isOriginBox == 1 ? "源箱号不存在" : "目的箱号不存在");
        if (shipmentEntity.getShipmentBoxCode().startsWith("Pack-"))
            throw new BusinessServiceException("Pack箱子无法调拨");
        StockoutShipmentItemSkuInfoResponse itemSkuInfo = new StockoutShipmentItemSkuInfoResponse();
        itemSkuInfo.setBoxIndex(shipmentEntity.getBoxIndex());
        if (isOriginBox == null || isOriginBox != 1) {
            return itemSkuInfo;
        }
        List<StockoutShipmentItemEntity> shipmentItemEntities = shipmentItemService.findByShipmentId(shipmentEntity.getShipmentId());
        if (CollectionUtils.isEmpty(shipmentItemEntities)) throw new BusinessServiceException("请选择不是空箱的箱子");
        if (shipmentEntity.getStatus().equalsIgnoreCase(StockoutPackingListStatusEnum.SHIPPED.name())) {
            List<String> stockoutOrderNos = shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
            List<StockoutOrderEntity> stockoutOrderEntities = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos)
                    .stream().filter(o -> StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equals(o.getWorkspace())).collect(Collectors.toList());
            if (stockoutOrderEntities.isEmpty() || stockoutOrderNos.size() != stockoutOrderEntities.size()) {
                throw new BusinessServiceException("箱号已经发货，无法调拨");
            }
        }
        List<String> skuList = shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getSku).collect(Collectors.toList());
        List<ProductSpecInfoEntity> specInfoEntities = specInfoService.findAllBySkuIn(skuList);
        Map<String, ProductSpecInfoEntity> specMap = specInfoEntities.stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, t -> t));
        List<ShipmentTransferSku> skuInfoList = shipmentItemEntities.stream().map(item -> {
            ShipmentTransferSku transferSku = new ShipmentTransferSku();
            ProductSpecInfoEntity spec = specMap.get(item.getSku());
            if (Objects.isNull(spec))
                throw new BusinessServiceException("未找到箱子中sku[" + item.getSku() + "]对应的商品记录");
            BeanUtils.copyProperties(item, transferSku);
            transferSku.setBarcode(spec.getBarcode());
            ProductInfoEntity productInfo = productInfoService.findTopByProductId(spec.getProductId());
            transferSku.setProductName(productInfo.getProductName());
            transferSku.setSpu(productInfo.getSpu());
            transferSku.setSize(spec.getSize());
            transferSku.setColor(spec.getColor());
            StockoutOrderEntity one = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getStockoutOrderNo, item.getStockoutOrderNo()));
            if (Objects.isNull(one) || Objects.isNull(one.getStoreId()))
                return transferSku;
            transferSku.setStoreBarcode(productStoreSkuMappingService.getStoreBarcode(one.getStoreId(), spec.getSku()));
            return transferSku;
        }).collect(Collectors.toList());
        itemSkuInfo.setSkuList(skuInfoList);
        return itemSkuInfo;
    }

    @Transactional
    @JLock(keyConstant = "transferShipmentBox", lockKey = "#request.originShipmentBoxCode")
    public void transferShipmentBox(StockoutShipmentItemSkuInfoRequest request) {
        StockoutShipmentEntity originBox = findTopByShipmentBoxCode(request.getOriginShipmentBoxCode());
        StockoutShipmentEntity targetBox = findTopByShipmentBoxCode(request.getTargetShipmentBoxCode());
        List<StockoutShipmentItemEntity> shipmentItemEntities = shipmentItemService.findByShipmentId(originBox.getShipmentId());
        List<String> pushOrderNoList = transferTrackingService.getPushOrderNo(shipmentItemEntities.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(pushOrderNoList)) {
            throw new BusinessServiceException(String.format("当前订单【%s】已推送海外仓无法调拨", String.join(",", pushOrderNoList)));
        }
        shipmentValid.validTransferBox(originBox, targetBox, shipmentItemEntities, request);
        // 同步.net装箱清单
        List<StockoutShipmentItemEntity> originItemList = new LinkedList<>();
        List<StockoutShipmentItemEntity> targetItemList = new LinkedList<>();
        for (ShipmentTransferSku transferSku : request.getSkuList()) {
            if (transferSku.getAllocateNum() == 0) continue; // 没有要调拨的数量
            StockoutShipmentItemEntity topForUpdateTransfer = shipmentItemService.findTopForUpdateTransfer(originBox.getShipmentId(), transferSku.getSku(), transferSku.getOrderNo(), transferSku.getStockoutOrderNo(), transferSku.getStockoutOrderItemId());
            if (topForUpdateTransfer == null)
                throw new BusinessServiceException(transferSku.getSku() + "在原箱子找不到");
            // 记录调拨日志
            scanLogService.addTransferShipmentLogOut(topForUpdateTransfer, transferSku.getQty(), originBox.getShipmentBoxCode());
            topForUpdateTransfer.setQty(transferSku.getQty());
            originItemList.add(topForUpdateTransfer);
            StockoutShipmentItemEntity shipmentItem = shipmentItemService.getById(transferSku.getShipmentItemId());
            if (shipmentItem.getQty() != transferSku.getQty() + transferSku.getAllocateNum())
                throw new BusinessServiceException(shipmentItem.getSku() + "此sku调拨数量不正确，请到箱子确认！");
            // 整批货被调拨
            if (transferSku.getQty() == 0) {
                producer.sendMessage(KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME, KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC, Key.of(shipmentItem.getSku()), new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), StockoutBuilding.detailMessage(shipmentItem, -shipmentItem.getQty(), originBox.getShipmentBoxCode(), StockoutScanTaskDetailEnum.TRANSFER.name())));
                shipmentItem.setUpdateBy(loginInfoService.getName());
                shipmentItem.setQty(0);
                shipmentItem.setIsDeleted(IsDeletedConstant.DELETED);
                shipmentItemService.updateById(shipmentItem);
            } else {
                // 部分调拨
                StockoutShipmentItemEntity entity = new StockoutShipmentItemEntity();
                StockoutShipmentItemEntity shipmentItemOrigin = shipmentItemService.getById(transferSku.getShipmentItemId());
                producer.sendMessage(KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME, KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC, Key.of(shipmentItemOrigin.getSku()), new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), StockoutBuilding.detailMessage(shipmentItemOrigin, transferSku.getQty() - shipmentItemOrigin.getQty(), originBox.getShipmentBoxCode(), StockoutScanTaskDetailEnum.TRANSFER.name())));
                entity.setShipmentItemId(transferSku.getShipmentItemId());
                entity.setQty(transferSku.getQty());
                shipmentItemService.updateById(entity);
            }
            targetItemList.add(transferTonNewBox(transferSku, targetBox, shipmentItem));
        }
        if (!originItemList.isEmpty() && !targetItemList.isEmpty()) {
            if (Objects.equals(StockoutShipmentStatusEnum.PACKING.name(), targetBox.getStatus())) {
                targetBox.setStatus(StockoutShipmentStatusEnum.PACKING_END.name());
                //生成报关订单
                stockoutShipmentCustomsService.generateCustomsOrder(targetBox.getShipmentId());
                packTaskService.add(targetBox.getShipmentId());
                pasteTaskService.add(targetBox.getShipmentId());
            }
            originBox.setUpdateBy(loginInfoService.getName());
            updateById(originBox);
            targetBox.setUpdateBy(loginInfoService.getName());
            targetBox.setStockoutType(originBox.getStockoutType());
            targetBox.setPlatformName(originBox.getPlatformName());
            if (Objects.equals(StockoutShipmentStatusEnum.SHIPPED.name(), originBox.getStatus()))
                targetBox.setStatus(StockoutShipmentStatusEnum.SHIPPED.name());
            updateById(targetBox);
            //更新打包和贴码任务
            originItemList.stream().map(StockoutShipmentItemEntity::getShipmentId).forEach(shipmentId -> {
                packTaskService.updateQty(shipmentId);
                pasteTaskService.updateQty(shipmentId);
            });
            targetItemList.stream().map(StockoutShipmentItemEntity::getShipmentId).forEach(shipmentId -> {
                packTaskService.updateQty(shipmentId);
                pasteTaskService.updateQty(shipmentId);
            });
            shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(originBox);
            shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(targetBox);
        }
    }

    // 查询目的箱号是否有同批号的，如果没有就新增，如果有就叠加
    private StockoutShipmentItemEntity transferTonNewBox(ShipmentTransferSku transferSku, StockoutShipmentEntity targetBox, StockoutShipmentItemEntity shipmentItem) {
        StockoutShipmentItemEntity updateEntity = shipmentItemService.findTopForUpdateTransfer(targetBox.getShipmentId(), transferSku.getSku(), transferSku.getOrderNo(), transferSku.getStockoutOrderNo(), transferSku.getStockoutOrderItemId());
        if (updateEntity == null) {
            updateEntity = new StockoutShipmentItemEntity();
            BeanUtils.copyProperties(transferSku, updateEntity, "shipmentItemId");
            updateEntity.setSellerSku(shipmentItem.getSellerSku());
            updateEntity.setSellerBarcode(shipmentItem.getSellerBarcode());
            updateEntity.setSpaceAreaId(shipmentItem.getSpaceAreaId());
            updateEntity.setSpaceAreaName(shipmentItem.getSpaceAreaName());
            updateEntity.setShipmentId(targetBox.getShipmentId());
            updateEntity.setQty(transferSku.getAllocateNum());
            updateEntity.setCreateBy(loginInfoService.getName());
            //设置波次号
            StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(transferSku.getStockoutOrderNo());
            StockoutBatchOrderEntity stockoutBatchOrder = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrder.getStockoutOrderId());
            updateEntity.setBatchId(stockoutBatchOrder.getBatchId());
        } else {
            updateEntity.setQty(updateEntity.getQty() + transferSku.getAllocateNum());
        }
        updateEntity.setUpdateBy(loginInfoService.getName());
        shipmentItemService.saveOrUpdate(updateEntity);
        producer.sendMessage(KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME, KafkaConstant.STOCKOUT_SCAN_SHIPMENT_TOPIC, Key.of(updateEntity.getSku()),
                new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), StockoutBuilding.detailMessage(updateEntity, transferSku.getAllocateNum(), targetBox.getShipmentBoxCode(), StockoutScanTaskDetailEnum.TRANSFER.name())));
        // 记录调拨日志
        scanLogService.addTransferShipmentLogInto(updateEntity, transferSku.getAllocateNum(), targetBox.getShipmentBoxCode());
        transparencyCodeService.transferCode(shipmentItem, transferSku.getAllocateNum(), updateEntity);
        return updateEntity;
    }

    public void deleteEmptyBox(StockoutShipmentRequest request) {
        List<String> shipmentBoxCodeList = request.getShipmentBoxCodeList();
        if (CollectionUtils.isEmpty(shipmentBoxCodeList)) throw new BusinessServiceException("请选择要删除的箱子");
        List<StockoutShipmentEntity> shipmentEntityList = list(new LambdaQueryWrapper<StockoutShipmentEntity>().in(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCodeList));
        shipmentEntityList.forEach(item -> {
            List<StockoutShipmentItemEntity> box = shipmentItemService.findByShipmentId(item.getShipmentId());
            if (!CollectionUtils.isEmpty(box))
                throw new BusinessServiceException(item.getShipmentBoxCode() + "此箱子不是空箱");
            stockoutShipmentMapper.deleteById(item.getShipmentId());
            stockoutFaireShipmentService.deleteByShipmentId(item.getShipmentId());
        });
    }

    @Transactional
    @JLock(keyConstant = "changeLogistics", lockKey = "#request.shipmentBoxCodeList[0]")
    public void changeLogistics(StockoutShipmentLogisticsRequest request) {
        Validator.isValid(request.getLogisticsCompany(), StringUtils::hasText, "请选择物流公司");
        List<String> shipmentBoxCodeList = request.getShipmentBoxCodeList();
        if (CollectionUtils.isEmpty(shipmentBoxCodeList)) throw new BusinessServiceException("请选择要修改的箱子");
        List<StockoutShipmentEntity> shipmentEntityList = list(new LambdaQueryWrapper<StockoutShipmentEntity>().in(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCodeList));
        List<StockoutShipmentItemEntity> itemList = new ArrayList<>();
        shipmentEntityList.forEach(item -> {
            List<StockoutShipmentItemEntity> currentItems = shipmentItemService.findByShipmentId(item.getShipmentId());
            if (CollectionUtils.isEmpty(currentItems)) {
                // 是空箱，直接改
                item.setLogisticsCompany(request.getLogisticsCompany());
                item.setLogisticsNo("");
                item.setUpdateBy(loginInfoService.getName());
                updateById(item);
            } else {
                itemList.addAll(currentItems);
            }
        });
        if (itemList.isEmpty())
            return;
        List<String> orderNos = itemList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
        List<StockoutOrderEntity> byStockoutOrderNoList = stockoutOrderService.getByStockoutOrderNoList(orderNos);
        List<Integer> orderIds = byStockoutOrderNoList.stream().map(StockoutOrderEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
        StockoutOrderLogisticsCompanyRequest orderRequest = new StockoutOrderLogisticsCompanyRequest();
        orderRequest.setStockoutOrderIds(orderIds);
        orderRequest.setLogisticsCompany(request.getLogisticsCompany());
        stockoutOrderLogService.addShipmentChangeLog(request.getLogisticsCompany(), byStockoutOrderNoList, itemList.stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList()));
        stockoutOrderService.updateOutOrderLogisticsCompany(orderRequest);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void selfGetLogistics(StockoutShipmentLogisticsRequest request) {
        Validator.isValid(request.getLogisticsCompany(), StringUtils::hasText, "请选择货代渠道");
        List<String> shipmentBoxCodeList = request.getShipmentBoxCodeList();
        if (CollectionUtils.isEmpty(shipmentBoxCodeList)) throw new BusinessServiceException("请选择要修改的箱子");
        List<StockoutShipmentEntity> shipmentEntityList = list(new LambdaQueryWrapper<StockoutShipmentEntity>().in(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCodeList));
        if (CollectionUtils.isEmpty(shipmentEntityList))
            throw new BusinessServiceException("未找到对应的箱子，请核对");
        List<Integer> shipmentIds = shipmentEntityList.stream().map(StockoutShipmentEntity::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentItemEntity> shipmentItems = shipmentItemService.findByShipmentIdList(shipmentIds);
        List<String> collect = shipmentItems.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
        List<StockoutOrderEntity> orderList = stockoutOrderService.getByStockoutOrderNoList(collect);
        orderList.stream().filter(StockoutOrderEntity::getHasPack).forEach(it -> packService.validDeliveryBox(it));
        List<String> stockoutOrderEntityFilter = orderList.stream().filter(stockoutOrderNo -> StockoutOrderStatusEnum.CANCELLING.name().equals(stockoutOrderNo.getStatus()))
                .map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stockoutOrderEntityFilter)) {
            throw new BusinessServiceException(String.format("以下出库单状态为取消中请撤货，点击确定取消！出库单:%s", stockoutOrderEntityFilter.stream().distinct().collect(Collectors.joining(","))));
        }
        shipmentEntityList.forEach(item -> {
            if (!StockoutShipmentStatusEnum.PACKING_END.name().equals(item.getStatus()))
                throw new BusinessServiceException(item.getShipmentBoxCode() + "不是装箱完成状态，请重新选择");
            item.setForwarderChannel(request.getLogisticsCompany());
            item.setLogisticsCompany(request.getLogisticsCompany());
            if (StringUtils.hasText(request.getLogisticsNo())) item.setLogisticsNo(request.getLogisticsNo());
            updateById(item);
        });
        shipmentEntityList.forEach(it -> shipmentErpPickingBoxService.sendErpPickingBoxSyncRequest(it));
        StockoutShipmentConfirmUpdateRequest req = StockoutShipmentBuilding.buildConfirmDeliveryRequest(request, shipmentIds, collect);
        confirmService.updateShipmentConfirm(req);
    }


    public StockoutShipmentEntity getEntityByShipmentBoxCode(String shipmentBoxCode) {
        return this.getOne(new QueryWrapper<StockoutShipmentEntity>().lambda().eq(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCode).last("limit 1"));
    }

    public List<StockoutShipmentEntity> getEntityByShipmentBoxCodeList(List<String> shipmentBoxCode) {
        return this.list(new QueryWrapper<StockoutShipmentEntity>().lambda().in(StockoutShipmentEntity::getShipmentBoxCode, shipmentBoxCode).eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED));
    }

    @Transactional
    public StockoutShipmentModel createShipmentBoxByUser(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        StockoutShipmentModel model = new StockoutShipmentModel();
        if (StockoutOrderTypeEnum.PROCESS_DELIVERY.name().equals(stockoutOrder.getStockoutType())) {
            int count = this.count(new QueryWrapper<StockoutShipmentEntity>().lambda()
                    .like(StockoutShipmentEntity::getShipmentBoxCode, stockoutOrderNo + "-%"));
            count = count + 1;
            model.setShipmentBoxCode(stockoutOrderNo + "-" + count);
        } else {
            model.setShipmentBoxCode(FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKOUT_SHIPMENT));
        }
        StockoutShipmentEntity entity = StockoutBuilding.buildSortShipment(stockoutOrder, loginInfoService.getName(), model.getShipmentBoxCode());
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> list = shipmentItemService.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> collect = list.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
            StockoutShipmentEntity shipment = getOne(new LambdaQueryWrapper<StockoutShipmentEntity>()
                    .in(StockoutShipmentEntity::getShipmentId, collect).orderByDesc(StockoutShipmentEntity::getBoxIndex).last("limit 1"));
            entity.setBoxIndex(shipment == null || shipment.getBoxIndex() == null ? 1 : shipment.getBoxIndex() + 1);
        }
        entity.setStockoutType(stockoutOrder.getStockoutType());
        entity.setPlatformName(stockoutOrder.getPlatformName());
        save(entity);
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    public StockoutShipmentEntity getByShipmentId(Integer shipmentId) {
        StockoutShipmentEntity shipment = getById(shipmentId);
        if (Objects.isNull(shipment))
            throw new BusinessServiceException(String.format("该装箱清单不存在 %s", shipmentId));
        if (1 == shipment.getIsDeleted())
            throw new BusinessServiceException(String.format("该装箱清单已删除 %s", shipmentId));
        return shipment;
    }
}
