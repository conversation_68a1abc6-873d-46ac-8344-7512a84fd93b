package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareContractStartRescindedRequest;
import com.nsy.api.wms.response.base.NoticeMsgResponse;
import com.nsy.wms.business.domain.bo.esign.ESignFlowInitiateRescissionResponse;
import com.nsy.wms.business.domain.bo.esign.ESignOrganizationsIdentityInfoResponse;
import com.nsy.wms.business.domain.bo.esign.ESignPersonsIdentityInfoResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutAEOAsynMsgBo;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.response.SupplierTaxGetSampleDatailDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractWmsAuditOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractWmsAuditRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectOneDetailRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsRejectRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.esign.ESignService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 关单合同
 */
@Service
public class StockoutCustomsDeclareContractCancelService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareContractCancelService.class);
    @Resource
    ESignService eSignService;
    @Resource
    StockoutCustomsDeclareFormService formService;
    @Resource
    StockoutCustomsDeclareContractLogService contractLogService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    SupplierApiService supplierApiService;
    @Resource
    StockoutCustomsDeclareFormItemService formItemService;
    @Resource
    StockoutCustomsDeclareFormRejectService formRejectService;
    @Resource
    StockoutCustomsDeclareAEOService declareAEOService;
    @Resource
    StockoutCustomsDeclareContractService contractService;
    @Autowired
    private ScmApiService scmApiService;

    /**
     * 拒签
     *
     * @param signFlowId
     */
    public void refuse(String signFlowId) {
        List<StockoutCustomsDeclareContractEntity> contractList = contractService.getBySignFlowId(signFlowId);
        contractList.forEach(contractEntity -> {
            try {
                SpringUtil.getBean(StockoutCustomsDeclareContractCancelService.class).cancel(contractEntity, StockoutCustomsDeclareContractStatusEnum.REFUSE, Boolean.TRUE);
            } catch (Exception e) {
                LOGGER.error(String.format("%s 合同拒签失败 %s", contractEntity.getDeclareContractNo(), e.getMessage()), e);
            }
        });
    }


    /**
     * 合同取消
     *
     * @param contractEntity
     * @param status
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void cancel(StockoutCustomsDeclareContractEntity contractEntity, StockoutCustomsDeclareContractStatusEnum status, Boolean shouldRejectForm) {
        // 1. 合同变成已解约或者已撤销
        if (StrUtil.isEmpty(contractEntity.getSignFlowId())) {
            throw new BusinessServiceException("e签宝未创建");
        }
        contractEntity.setStatus(status.name());
        contractService.updateById(contractEntity);
        contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.CANCEL, status.getName());
        //2. AEO单据删除
        declareAEOService.sendAsynOpMsg(new StockoutAEOAsynMsgBo(StockoutAEOAsynMsgBo.OperationType.REMOVE_BY_CONTRACT, contractEntity.getDeclareContractId(), contractEntity.getDeclareContractNo()));

        //3. 供应商 删除合同
        StockoutCustomsDeclareContractWmsAuditRequest contractAuditRequest = new StockoutCustomsDeclareContractWmsAuditRequest();
        contractAuditRequest.setStatus("COMPANY_NO_PASS");
        contractAuditRequest.setOperator(loginInfoService.getName());
        contractAuditRequest.setCompanyRejectReason(status.getName());
        StockoutCustomsDeclareContractWmsAuditOneRequest contractWmsAuditOneRequest = new StockoutCustomsDeclareContractWmsAuditOneRequest();
        contractWmsAuditOneRequest.setDeclareContractId(contractEntity.getDeclareContractId());
        contractWmsAuditOneRequest.setDeclareContractNo(contractEntity.getDeclareContractNo());
        contractAuditRequest.setDeclareContract(Collections.singletonList(contractWmsAuditOneRequest));
        supplierApiService.syncCustomsContractReject(contractAuditRequest);
        scmApiService.rejectCustomsContract(contractAuditRequest);


        //4. 判断是否回退关单
        if (shouldRejectForm) {
            List<StockoutCustomsDeclareFormWmsRejectOneRequest> oneRequestList = buildFormWmsRejectOneRequest(contractEntity.getDeclareContractId());

            //关单回退
            List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
            formList.forEach(temp -> formRejectService.rejectForm(temp.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.CONTRACT_CANCEL, status.getName()));

            //供应商删除关单
            StockoutCustomsDeclareFormWmsRejectRequest formRejectRequest = new StockoutCustomsDeclareFormWmsRejectRequest();
            formRejectRequest.setAuditNoPassReason(status.getName());
            formRejectRequest.setOperator(loginInfoService.getName());
            formRejectRequest.setDeclareForm(oneRequestList);
            supplierApiService.customsDeclareFormReject(formRejectRequest);
        } else {
            //关单回退
            List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
            formList.forEach(temp -> formRejectService.revokeForm(temp.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.CONTRACT_CANCEL, status.getName()));
        }
    }

    /**
     * 构建请求参数
     *
     * @param contractId
     * @return
     */
    public List<StockoutCustomsDeclareFormWmsRejectOneRequest> buildFormWmsRejectOneRequest(Integer contractId) {
        List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractId(contractId);
        if (formList.isEmpty()) throw new BusinessServiceException(String.format("合同【%s】的关单为空", contractId));
        return formList.stream().map(form -> {
            StockoutCustomsDeclareFormWmsRejectOneRequest rejectOneRequest = new StockoutCustomsDeclareFormWmsRejectOneRequest();
            rejectOneRequest.setDeclareFormId(form.getDeclareFormId());
            List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemListNoManual(form.getDeclareFormId());
            if (formItemList.isEmpty())
                throw new BusinessServiceException(String.format("关单【%s】明细为空", form.getDeclareFormId()));
            List<StockoutCustomsDeclareFormWmsRejectOneDetailRequest> detailList = formItemList.stream().map(formItem -> {
                StockoutCustomsDeclareFormWmsRejectOneDetailRequest detailRequest = new StockoutCustomsDeclareFormWmsRejectOneDetailRequest();
                detailRequest.setDeclareFormItemId(formItem.getDeclareFormItemId());
                detailRequest.setTaxItemId(formItem.getTaxItemId());
                return detailRequest;
            }).collect(Collectors.toList());
            rejectOneRequest.setDeclareFormItem(detailList);
            return rejectOneRequest;
        }).collect(Collectors.toList());
    }

    /**
     * 确认撤销
     *
     * @param contractId
     * @return
     */
    public NoticeMsgResponse checkRevoke(Integer contractId) {
        StockoutCustomsDeclareContractEntity contract = contractService.getByDeclareContractId(contractId);
        if (!StockoutCustomsDeclareContractStatusEnum.WAIT_SIGN.name().equals(contract.getStatus())) {
            throw new BusinessServiceException(String.format("合同 %s 非签署中，没办法撤回", contract.getDeclareContractNo()));
        }
        List<StockoutCustomsDeclareContractEntity> contractList = contractService.getBySignFlowId(contract.getSignFlowId());
        NoticeMsgResponse response = new NoticeMsgResponse();
        if (contractList.size() > 1) {
            response.setIsShowMsg(Boolean.TRUE);
            response.setMsg(String.format("同属该流程的合同有 %s，确认是否一起撤销?", contractList.stream().map(StockoutCustomsDeclareContractEntity::getDeclareContractNo).collect(Collectors.joining("，"))));
        }
        return response;
    }

    /**
     * 撤销
     *
     * @param contractId
     */
    @Transactional
    public void revoke(Integer contractId) {
        StockoutCustomsDeclareContractEntity contract = contractService.getByDeclareContractId(contractId);
        if (!StockoutCustomsDeclareContractStatusEnum.WAIT_SIGN.name().equals(contract.getStatus())) {
            throw new BusinessServiceException(String.format("合同 %s 非签署中，没办法撤回", contract.getDeclareContractNo()));
        }
        List<StockoutCustomsDeclareContractEntity> contractList = contractService.getBySignFlowId(contract.getSignFlowId());
        contractList.forEach(contractEntity -> {
            try {
                SpringUtil.getBean(StockoutCustomsDeclareContractCancelService.class).cancel(contractEntity, StockoutCustomsDeclareContractStatusEnum.REVOKE, Boolean.FALSE);
            } catch (Exception e) {
                LOGGER.error(String.format("%s 合同撤销失败 %s", contractEntity.getDeclareContractNo(), e.getMessage()), e);
            }
        });
        //e签宝撤回流程
        eSignService.revoke(contract.getSignFlowId());
    }

    /**
     * 开始解约
     *
     * @param request
     */
    @Transactional
    public void startRescinded(StockoutCustomsDeclareContractStartRescindedRequest request) {
        StockoutCustomsDeclareContractEntity contractEntity = contractService.getByDeclareContractId(request.getContractId());
        if (!StockoutCustomsDeclareContractStatusEnum.COMPLETE.name().equals(contractEntity.getStatus())) {
            throw new BusinessServiceException(String.format("合同 %s 非签署完成，无法解约", contractEntity.getDeclareContractNo()));
        }
        contractEntity.setStatus(StockoutCustomsDeclareContractStatusEnum.RESCINDING.name());
        contractService.updateById(contractEntity);

        ESignOrganizationsIdentityInfoResponse organizationsIdentityInfoResponse = eSignService.organizationsIdentityInfo(contractEntity.getCompanyName());
        ESignPersonsIdentityInfoResponse personsIdentityInfoResponse = eSignService.personsIdentityInfo(contractEntity.getCompanyAgentPhone());
        //调用e签宝解约
        SupplierTaxGetSampleDatailDto supplierInfo = contractService.getSupplierInfo(contractEntity.getSupplierId(), "IN_COOPERATION");
        String bodyJson = "{\"rescindReason\":\"%s\",\"rescindFileList\":[\"%s\"],\"rescissionInitiator\":{\"orgInitiator\":{\"orgId\":\"%s\",\"transactor\":{\"psnId\":\"%s\"}}},\"signFlowConfig\":{\"notifyUrl\":\"%s\"},\"orgSignerTransactor\":[{\"orgName\":\"%s\",\"transactorInfo\":{\"psnAccount\":\"%s\"}}]}";
        bodyJson = String.format(bodyJson, request.getRescindValue(), contractEntity.getFileId(), organizationsIdentityInfoResponse.getOrgId(), personsIdentityInfoResponse.getPsnId(), eSignService.getNotifyUrl(),
                supplierInfo.getInvoiceCompanyName(), contractEntity.getSupplierAgentPhone());
        ESignFlowInitiateRescissionResponse initiateRescissionResponse = eSignService.initiateRescission(contractEntity.getSignFlowId(), bodyJson);


        contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.CANCEL, String.format("开启解约流程，流程id %s", initiateRescissionResponse.getSignFlowId()));
    }

    /**
     * 解约
     *
     * @param jObj
     */
    public void rescinded(JSONObject jObj) {
        //完成解约的文件ID列表
        List<String> rescissionFileIds = jObj.getJSONArray("rescissionFileIds").toList(String.class);
        rescissionFileIds.forEach(fileId -> {
            StockoutCustomsDeclareContractEntity contractEntity = contractService.getByFileId(fileId);
            if (!StockoutCustomsDeclareContractStatusEnum.RESCINDING.name().equals(contractEntity.getStatus())) {
                throw new BusinessServiceException(String.format("合同 %s 非解约中，无法解约成功", contractEntity.getDeclareContractNo()));
            }

            try {
                //合同解约
                SpringUtil.getBean(StockoutCustomsDeclareContractCancelService.class).cancel(contractEntity, StockoutCustomsDeclareContractStatusEnum.RESCINDED, Boolean.TRUE);
            } catch (Exception e) {
                LOGGER.error(String.format("%s 合同解约失败 %s", contractEntity.getDeclareContractNo(), e.getMessage()), e);
            }
        });
    }

    /**
     * 拒签
     *
     * @param signFlowId
     */
    public void overdue(String signFlowId) {
        List<StockoutCustomsDeclareContractEntity> contractList = contractService.getBySignFlowId(signFlowId);
        contractList.forEach(contractEntity -> {
            try {
                SpringUtil.getBean(StockoutCustomsDeclareContractCancelService.class).cancel(contractEntity, StockoutCustomsDeclareContractStatusEnum.OVERDUE, Boolean.TRUE);
            } catch (Exception e) {
                LOGGER.error(String.format("%s 合同操作过期失败 %s", contractEntity.getDeclareContractNo(), e.getMessage()), e);
            }
        });
    }

    /**
     * 冲库存删除合同
     * 专门用于冲库存生成的合同删除，直接物理删除合同记录
     *
     * @param contractId 合同ID
     */
    @Transactional
    public void cancelInventoryContract(Integer contractId) {
        // 1. 获取合同信息
        StockoutCustomsDeclareContractEntity contractEntity = contractService.getByDeclareContractId(contractId);

        // 2. 验证合同状态
        if (!StockoutCustomsDeclareContractStatusEnum.COMPLETE.name().equals(contractEntity.getStatus())) {
            throw new BusinessServiceException(String.format("合同 %s 非签署完成状态，无法删除", contractEntity.getDeclareContractNo()));
        }

        // 3. 获取关联的关单，验证是否为冲库存合同
        List<StockoutCustomsDeclareFormEntity> formList = formService.findListByContractNo(contractEntity.getDeclareContractNo());
        if (formList.isEmpty()) {
            throw new BusinessServiceException(String.format("合同 %s 没有关联的关单", contractEntity.getDeclareContractNo()));
        }

        // 4. 验证是否为冲库存合同（检查关单的系统标记）
        boolean isInventoryContract = formList.stream()
                .anyMatch(form -> StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name().equals(form.getSystemMark()));

        if (!isInventoryContract) {
            throw new BusinessServiceException(String.format("合同 %s 不是冲库存合同，无法使用此接口删除", contractEntity.getDeclareContractNo()));
        }

        // 5. 记录合同删除日志（在删除前记录）
        contractLogService.addLog(contractEntity.getDeclareContractId(), StockoutCustomsDeclareContractLogTypeEnum.CANCEL, "冲库存合同物理删除");

        // 6. 删除冲库存手动导入的明细记录
        formList.forEach(form -> {
            formItemService.deleteManualItems(form.getDeclareFormId());
        });

        // 7. 更新关联关单状态为待处理，清空合同关联信息和冲库存导入字段
        formList.forEach(form -> {
            // 基本状态和合同信息
            form.setStatus(StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name());
            form.setDeclareContractId(null);
            form.setDeclareContractNo(null);
            form.setContractSignedDate(null);
            form.setContractGenerateDate(null);
            form.setStockinDate(null);
            form.setHasSupplierSignContract(Boolean.FALSE);
            form.setHasInvoiceFill(Boolean.FALSE);
            form.setHasAuditPassItem(Boolean.FALSE);

            // 清空冲库存导入时设置的字段
            form.setSystemMark(""); // 清空系统标记，使用空字符串确保数据库更新
            form.setMatchDate(null); // 清空匹配时间  
            form.setSupplierId(null); // 清空供应商信息
            form.setSupplierName(null);
            form.setTotalManualInputQty(null); // 清空手动拆分总进项数量

            form.setUpdateBy(loginInfoService.getName());
        });

        // 8. 批量更新关单
        formService.updateBatchById(formList);

        // 9. 记录关单操作日志
        StockoutCustomsDeclareFormLogService formLogService = SpringUtil.getBean(StockoutCustomsDeclareFormLogService.class);
        formList.forEach(form -> {
            formLogService.addLog(form.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.CONTRACT_CANCEL,
                    "冲库存合同删除，清空导入字段，关单状态变更为【待处理】");
        });

        // 10. 删除AEO单据
        declareAEOService.sendAsynOpMsg(new StockoutAEOAsynMsgBo(StockoutAEOAsynMsgBo.OperationType.REMOVE_BY_CONTRACT,
                contractEntity.getDeclareContractId(), contractEntity.getDeclareContractNo()));

        // 11. 物理删除合同记录
        contractService.removeById(contractEntity.getDeclareContractId());

        LOGGER.info("冲库存合同删除成功，合同号：{}，关联关单数量：{}，已清空冲库存导入字段", contractEntity.getDeclareContractNo(), formList.size());
    }

    /**
     * 批量冲库存删除合同
     * 专门用于批量删除冲库存生成的合同，直接物理删除合同记录
     *
     * @param contractIdList 合同ID列表
     */
    public void batchCancelInventoryContract(List<Integer> contractIdList) {
        if (CollectionUtil.isEmpty(contractIdList)) {
            throw new BusinessServiceException("合同ID列表不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (Integer contractId : contractIdList) {
            try {
                // 调用单个删除方法，利用现有的业务逻辑和验证
                SpringUtil.getBean(StockoutCustomsDeclareContractCancelService.class).cancelInventoryContract(contractId);
                successCount++;
            } catch (Exception e) {
                failCount++;
                String errorMsg = String.format("合同ID[%s]删除失败：%s", contractId, e.getMessage());
                errorMessages.add(errorMsg);
                LOGGER.error(errorMsg, e);
            }
        }

        String resultMessage = String.format("批量删除完成，总数：%d，成功：%d，失败：%d",
                contractIdList.size(), successCount, failCount);

        if (failCount > 0) {
            String allErrors = String.join("；", errorMessages);
            throw new BusinessServiceException(resultMessage + "。失败详情：" + allErrors);
        }

        LOGGER.info(resultMessage);
    }
}
