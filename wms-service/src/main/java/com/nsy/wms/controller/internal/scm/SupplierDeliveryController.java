package com.nsy.wms.controller.internal.scm;

import com.nsy.api.wms.domain.stock.StockPlatformSchedulePageCount;
import com.nsy.api.wms.request.bd.PageShippingItemListRequest;
import com.nsy.api.wms.request.bd.PlatformScheduleSetArriveDateRequest;
import com.nsy.api.wms.request.stock.StockPlatformSchedulePageSearchRequest;
import com.nsy.api.wms.request.stock.StockinOrderTaskSetNeedQaRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockPlatformSchedulePageResponse;
import com.nsy.api.wms.response.stock.StockPlatformScheduleStatisticsResponse;
import com.nsy.api.wms.response.stock.StockPlatformScheduleStatusProcessResponse;
import com.nsy.api.wms.response.stock.StockShippingItemPageListResponse;
import com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse;
import com.nsy.wms.business.service.internal.scm.SupplierDeliveryService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Api(tags = "提供给scm,出库发货列表相关接口")
public class SupplierDeliveryController extends BaseController {

    @Autowired
    SupplierDeliveryService stockoutDeliveryService;

    @ApiOperation(value = "工厂发货列表-scm-统计", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/shipping-count", method = RequestMethod.POST)
    @ResponseBody
    public StockPlatformSchedulePageCount countShippingList(@RequestBody StockPlatformSchedulePageSearchRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockoutDeliveryService.countSearchShippingList(request);
    }

    @ApiOperation(value = "工厂发货列表-scm-统计箱数和发货数", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/count-task-qty", method = RequestMethod.POST)
    @ResponseBody
    public StatisticsByPlatformScheduleIdResponse statisticsSearchShippingTaskQty(@RequestBody StockPlatformSchedulePageSearchRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockoutDeliveryService.statisticsSearchShippingTaskQty(request);
    }

    @ApiOperation(value = "工厂发货列表-scm-统计收货数、退货数、上架数", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/count-order-qty", method = RequestMethod.POST)
    @ResponseBody
    public StatisticsByPlatformScheduleIdResponse statisticsSearchShippingOrderQty(@RequestBody StockPlatformSchedulePageSearchRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockoutDeliveryService.statisticsSearchShippingOrderQty(request);
    }

    @ApiOperation(value = "工厂发货列表统计-scm", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/shipping-list/statistics", method = RequestMethod.POST)
    @ResponseBody
    public StockPlatformScheduleStatisticsResponse statisticsShippingList(@RequestBody StockPlatformSchedulePageSearchRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockoutDeliveryService.statisticsShippingList(request);
    }

    @ApiOperation(value = "工厂发货列表-scm", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/shipping-list", method = RequestMethod.POST)
    public PageResponse<StockPlatformSchedulePageResponse> pageShippingList(@RequestBody StockPlatformSchedulePageSearchRequest request) {
        request.buildSkuAutoMatchList(request.getSku());
        request.buildSpuAutoMatchList(request.getSpu());
        return stockoutDeliveryService.pageSearchShippingList(request);
    }

    @ApiOperation(value = "工厂发货详情列表-scm", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/shipping-item-list", method = RequestMethod.POST)
    public StockShippingItemPageListResponse pageShippingItemList(@Valid @RequestBody PageShippingItemListRequest request) {
        return stockoutDeliveryService.pageShippingItemList(request);
    }

    @ApiOperation(value = "工厂发货状态机过程-scm", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/status-process", method = RequestMethod.GET)
    public StockPlatformScheduleStatusProcessResponse getPlatformScheduleStatusProcess(@RequestParam("platformScheduleId") Integer platformScheduleId) {
        return stockoutDeliveryService.getPlatformScheduleStatusProcess(platformScheduleId);
    }

    @ApiOperation(value = "工厂发货-设置到货日期-scm", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/arrive-date", method = RequestMethod.PUT)
    public void setArriveDate(@Valid @RequestBody PlatformScheduleSetArriveDateRequest request) {
        stockoutDeliveryService.setArriveDate(request);
    }

    @ApiOperation(value = "出库发货详情-设置质检-scm", produces = "application/json")
    @RequestMapping(value = "/platform-schedule/need-qa", method = RequestMethod.PUT)
    public void setNeedQa(@Valid @RequestBody StockinOrderTaskSetNeedQaRequest request) {
        stockoutDeliveryService.setNeedQa(request);
    }
}
