package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.Attach;
import com.nsy.api.wms.domain.product.ProductFabricTypeInfo;
import com.nsy.api.wms.domain.product.ProductInfo;
import com.nsy.api.wms.domain.product.ProductRequirementInfo;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stock.StockInternalBoxRoutingInspectionInfo;
import com.nsy.api.wms.domain.stockin.ModifySkuReceiveQtyInfo;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.enumeration.QcInboundsResultStatusEnum;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.qa.DevelopImageType;
import com.nsy.api.wms.enumeration.qa.InboundsRequiredRoutingInspectedCheckStatusEnum;
import com.nsy.api.wms.enumeration.qa.PurchaseOrderItemTypeEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaTaskStatusEnum;
import com.nsy.api.wms.enumeration.stock.PurchaseOrderTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.qa.BarcodeRequest;
import com.nsy.api.wms.request.qa.RoutingInspectItemSaveRequest;
import com.nsy.api.wms.request.qa.StockinQaRoutingInspectPageRequest;
import com.nsy.api.wms.request.qa.StockinQaRoutingInspectStatusQueryRequest;
import com.nsy.api.wms.request.qa.StockinQaRoutingInspectUpdateRequest;
import com.nsy.api.wms.request.stock.StockInternalBoxCodeRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.product.ProductDetail;
import com.nsy.api.wms.response.qa.ApiCommonResponse;
import com.nsy.api.wms.response.qa.GetScanBarcodeSearchQaInspectionInfoResponse;
import com.nsy.api.wms.response.qa.ProductTechnologyResponse;
import com.nsy.api.wms.response.qa.QcInboundsRoutingInspectedListResponse;
import com.nsy.api.wms.response.qa.QcInboundsRoutingInspectedResponse;
import com.nsy.api.wms.response.qa.StockinQaRoutingInspectDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaRoutingInspectPageResponse;
import com.nsy.api.wms.response.qa.StockinQaRoutingInspectStatusQueryResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxRoutingInspectionResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.dto.stockin.StockinQaRoutingInspectSyncDTO;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.product.ProductApiService;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.ProductDevelopAttachInspectionRequest;
import com.nsy.wms.business.manage.scm.request.ProductDevelopAttachRequest;
import com.nsy.wms.business.manage.scm.response.AttachDto;
import com.nsy.wms.business.manage.scm.response.BdPurchaseOrderLabelDto;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.response.PurchaseOrderItemResponse;
import com.nsy.wms.business.manage.user.response.BdDictionaryItem;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.business.service.supplier.SupplierService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaRoutingInspectEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaRoutingInspectMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 巡检表业务实现
 * @date: 2024-11-13 10:09
 */
@Service
public class StockinQaRoutingInspectService extends ServiceImpl<StockinQaRoutingInspectMapper, StockinQaRoutingInspectEntity> implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaRoutingInspectService.class);

    @Autowired
    private StockinQcService stockinQcService;
    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private SupplierApiService supplierApiService;
    @Autowired
    private GcApiService gcApiService;
    @Autowired
    private PurchaseOrderLabelService purchaseOrderLabelService;
    @Autowired
    private ProductApiService productApiService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinQaOrderService stockinQaOrderService;
    @Autowired
    private StockinQaTaskService stockinQaTaskService;
    @Autowired
    private StockinQaRoutingInspectItemService qaRoutingInspectItemService;
    @Autowired
    private ProductSpecInfoService specInfoService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private StockinQaRoutingInspectSkuTypeInfoService qaRoutingInspectSkuTypeInfoService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private StockinQaProductSampleSkuTypeService sampleSkuTypeService;

    /**
     * 获取待巡检列表
     * 1.如果箱号则直接找对应的数据
     * 2.如果为出库单号则返回对应箱子数据给前端选择
     *
     * @param barcodeRequest
     * @return
     */
    public ApiCommonResponse waitQcRoutingInspectedList(BarcodeRequest barcodeRequest) {
        StockInternalBox internalBoxInfo = null;
        try {
            internalBoxInfo = stockinQcService.getInternalBoxByQc(barcodeRequest.getBoxBarcode());
        } catch (BusinessServiceException e) {
            //查找工厂入库单
            List<ModifySkuReceiveQtyInfo> internalBoxCodeList = stockInternalBoxService.getBaseMapper().searchBoxBySupplierDeliveryNo(barcodeRequest.getBoxBarcode());
            if (CollectionUtils.isEmpty(internalBoxCodeList)) {
                throw e;
            }
            if (internalBoxCodeList.size() == 1) {
                barcodeRequest.setBoxBarcode(internalBoxCodeList.get(0).getInternalBoxCode());
            } else {
                //如果工厂出库单下存在多个箱子,返回给前端多个箱子让用户选择
                return ApiCommonResponse.create(internalBoxCodeList, "selectBox");
            }
        }
        if (Objects.nonNull(internalBoxInfo) && !TenantContext.getTenant().equals(internalBoxInfo.getLocation())) {
            throw new BusinessServiceException("请勿扫描其他区域的箱子！");
        }
        try {
            StockInternalBoxCodeRequest request = new StockInternalBoxCodeRequest();
            request.setBoxBarcode(barcodeRequest.getBoxBarcode());
            request.setStatus(barcodeRequest.getStatus());
            StockInternalBoxRoutingInspectionResponse boxDetailList = stockInternalBoxItemService.getRoutingInspectionListGroup(request);
            List<QcInboundsRoutingInspectedListResponse> responseList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(boxDetailList.getBoxDetailList())) {
                responseList = buildResponseList(boxDetailList);
            }
            return ApiCommonResponse.create(responseList);
        } catch (Exception exception) {
            LOGGER.error("waitQcRoutingInspectedList error, cause ={}", exception.getMessage(), exception);
            return ApiCommonResponse.create(exception, "fail");
        }
    }

    /**
     * 组装返回信息
     *
     * @param boxDetailList
     * @return
     */
    private List<QcInboundsRoutingInspectedListResponse> buildResponseList(StockInternalBoxRoutingInspectionResponse boxDetailList) {
        List<QcInboundsRoutingInspectedResponse> itemList = new ArrayList<>();
        List<QcInboundsRoutingInspectedListResponse> responseList = new ArrayList<>();
        //获取所有采购标签
        List<BdPurchaseOrderLabelDto> allBdPurchaseOrderLabel = scmApiService.getAllBdPurchaseOrderLabel();
        boxDetailList.getBoxDetailList().forEach(entity -> {
            QcInboundsRoutingInspectedResponse routingInspectedResponse = new QcInboundsRoutingInspectedResponse();
            BeanUtils.copyProperties(entity, routingInspectedResponse);
            //能查出来就是待处理
            StockinQaRoutingInspectStatusQueryRequest queryRequest = new StockinQaRoutingInspectStatusQueryRequest();
            BeanUtils.copyProperties(entity, queryRequest);
            StockinQaRoutingInspectStatusQueryResponse checkStatusByInfo = supplierApiService.getCheckStatusByInfo(queryRequest);
            routingInspectedResponse.setCheckStatus(Objects.isNull(checkStatusByInfo) ? null : checkStatusByInfo.getCheckStatus());

            //赋值对应的标签信息
            ProductSpecInfo specInfo = productSpecInfoService.getBySku(entity.getSku());
            if (specInfo != null && specInfo.getProductId() != null) {
                routingInspectedResponse.setProductId(specInfo.getProductId());
                //赋值标签信息
                routingInspectedResponse.setLabel(getRoutingInspectedResponseLabel(entity, allBdPurchaseOrderLabel));
                //根据 productId 是否独立站专款 isShopifySpecial
                ProductRequirementInfo productDetail = productApiService.getProductRequirement(specInfo.getProductId());
                if (productDetail != null && productDetail.getIsShopifySpecial() != null) {
                    routingInspectedResponse.setIsShopifySpecial(productDetail.getIsShopifySpecial());
                }
            }
            itemList.add(routingInspectedResponse);
        });
        //组成嵌套同款(即同parent-sku)为一组
        if (CollectionUtils.isNotEmpty(itemList)) {
            responseList = buildProductSkuList(itemList);
        }
        return responseList;
    }

    /**
     * 组装标签信息
     *
     * @param entity
     * @param allBdPurchaseOrderLabel
     * @return
     */
    public String getRoutingInspectedResponseLabel(StockInternalBoxRoutingInspectionInfo entity, List<BdPurchaseOrderLabelDto> allBdPurchaseOrderLabel) {
        List<PurchaseOrderItemResponse> productOrderItemList = gcApiService.getPurchaseOrderItemByInfo(Collections.singletonList(entity.getPurchaseNumber()), entity.getSku());
        if (CollectionUtils.isEmpty(productOrderItemList))
            return null;
        if (productOrderItemList.stream().anyMatch(e -> PurchaseOrderTypeEnum.RETURN_APPLY.getIntValue().equals(e.getSource()))) {
            return "返";
        }
        String itemTypes = productOrderItemList.stream().map(PurchaseOrderItemResponse::getItemType).distinct().collect(Collectors.joining(","));
        String label = purchaseOrderLabelService.buildItemType(itemTypes, allBdPurchaseOrderLabel);
        if (Objects.equals(PurchaseOrderItemTypeEnum.SUPPLIER_FIRST_ORDER.getValue(), label)) {
            return "新品";
        }
        if (Objects.equals(PurchaseOrderItemTypeEnum.SUPPLIER_REDESIGN_FIRST_ORDER.getValue(), label)) {
            return "改版";
        }
        return label;
    }

    /**
     * 同款(即同product-sku)为一组
     *
     * @param itemList
     * @return
     */
    private List<QcInboundsRoutingInspectedListResponse> buildProductSkuList(List<QcInboundsRoutingInspectedResponse> itemList) {
        List<QcInboundsRoutingInspectedListResponse> responseList = new ArrayList<>();
        itemList.forEach(item -> {
            QcInboundsRoutingInspectedListResponse qcInboundsRoutingInspectedListResponse = new QcInboundsRoutingInspectedListResponse();
            BeanUtils.copyProperties(item, qcInboundsRoutingInspectedListResponse);
            String parentSkuStr = item.getProductSku();
            qcInboundsRoutingInspectedListResponse.setIsNew(sampleSkuTypeService.validNewOrder(item.getBoxBarcode(), item.getSku()) ? 1 : 0);
            if (StringUtils.hasText(parentSkuStr)) {
                List<QcInboundsRoutingInspectedResponse> list = new ArrayList<>();
                itemList.forEach(boxItem -> {
                    String parentSkuStrNext = boxItem.getProductSku();
                    if (StringUtils.hasText(parentSkuStrNext) && parentSkuStr.equals(parentSkuStrNext)) {
                        list.add(boxItem);
                    }
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    qcInboundsRoutingInspectedListResponse.setProductSkuList(list);
                }
            }
            responseList.add(qcInboundsRoutingInspectedListResponse);
        });
        return responseList;
    }

    /**
     * 获取巡检产品 - 成分
     */
    public ProductDetail getProductProductFabricType(Integer productId) {
        ProductDetail productDetail = new ProductDetail();
        List<ProductFabricTypeInfo> list = productApiService.productFabricTypeInfos(Lists.newArrayList(productId));
        if (!list.isEmpty()) {
            productDetail.setFabricType(list.get(0).getFabricType());
        }
        return productDetail;
    }

    /**
     * 获取商品工艺单
     */
    public List<ProductTechnologyResponse> listQcInboundsProductAttach(Integer productId) {
        List<ProductTechnologyResponse> productTechnologyList = new ArrayList<>();
        ProductDevelopAttachRequest request = new ProductDevelopAttachRequest();
        request.setProductId(productId);
        request.setType(DevelopImageType.DESIGN);
        List<AttachDto> developAttachList = scmApiService.getDevelopAttachList(request);
        if (!CollectionUtils.isEmpty(developAttachList)) {
            productTechnologyList = developAttachList.stream().map(developImage -> {
                ProductTechnologyResponse productTechnology = new ProductTechnologyResponse();
                productTechnology.setName(developImage.getOriginName());
                productTechnology.setProductTechnologyUrl(developImage.getUrl());
                return productTechnology;
            }).collect(Collectors.toList());
        }
        return productTechnologyList;
    }

    @Transactional
    public String routingInspectPushShelve(String boxBarcode) {
        stockInternalBoxService.changeStockInternalBoxStatus(boxBarcode, StockInternalBoxStatusEnum.WAIT_SHELVE.name());
        stockinOrderService.routingInSpectShelveChangeStatus(boxBarcode, StockInternalBoxStatusEnum.WAIT_SHELVE.name());
        return "推送上架成功";
    }


    /**
     * 步骤一：新增对应的巡检表信息
     * 步骤二：如果未推送wms则推送完成对应的直接结果
     *
     * @param item
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @JLock(keyConstant = "saveRoutingInspect", lockKey = "#item.boxBarcode + '-' + #item.purchaseNumber + '-' + #item.receiveOrder + '-' + #item.sku")
    public ApiCommonResponse saveRoutingInspectItemNew(RoutingInspectItemSaveRequest item) {
        StockinQaRoutingInspectEntity routingInspectItemEntity = saveRoutingInspectItem(item);
        if (Objects.isNull(routingInspectItemEntity.getHadPush()) || routingInspectItemEntity.getHadPush() == 0) {
            routingInspectItemEntity.setHadPush(1);
            routingInspectItemEntity.setUpdateBy(loginInfoService.getName());
            QcInboundsMessage message = this.buildQcInboundsMessage(routingInspectItemEntity, item);
            stockinQcService.qcComplete(message);
            this.updateById(routingInspectItemEntity);
        }
        return ApiCommonResponse.create("新增成功");
    }

    //更新质检状态checkStatus
    @Transactional
    @JLock(keyConstant = "saveRoutingInspect", lockKey = "#request.boxBarcode + '-' + #request.purchaseNumber + '-' + #request.receiveOrder + '-' + #request.sku")
    public ApiCommonResponse changeInspectStatusByRoutingCheckoutStatus(RoutingInspectItemSaveRequest request) {
        changeRoutingInspectStatus(request);
        saveOrUpdateQaTask(request);
        return ApiCommonResponse.create("更新成功");
    }

    //批量更新质检状态checkStatus
    @Transactional
    public ApiCommonResponse batchChangeInspectStatusByRoutingCheckoutStatus(List<RoutingInspectItemSaveRequest> request) {
        if (CollectionUtils.isNotEmpty(request)) {
            request.forEach(detail -> {
                changeRoutingInspectStatus(detail);
                saveOrUpdateQaTask(detail);
            });
        }
        return ApiCommonResponse.create("更新成功");
    }

    /**
     * 更新巡检状态checkStatus
     */
    public void changeRoutingInspectStatus(RoutingInspectItemSaveRequest request) {
        String realName = loginInfoService.getName();
        StockinQaRoutingInspectEntity routingInspectEntity = new StockinQaRoutingInspectEntity();

        BeanUtilsEx.copyProperties(request, routingInspectEntity);
        routingInspectEntity.setInternalBoxCode(request.getBoxBarcode());
        routingInspectEntity.setCreateBy(loginInfoService.getName());
        routingInspectEntity.setUpdateBy(loginInfoService.getName());
        routingInspectEntity.setLocation(TenantContext.getTenant());
        routingInspectEntity.setUpdateBy(realName);
        StockinQaRoutingInspectEntity routingInspectItemEntityOld = this.getBaseMapper().findCheckStatusByInfo(
            request.getBoxBarcode(), request.getPurchaseNumber(),
            CollUtil.newArrayList(request.getReceiveOrder().split(",")), request.getSku());
        if (Objects.nonNull(routingInspectItemEntityOld)) {
            BeanUtils.copyProperties(routingInspectItemEntityOld, routingInspectEntity);
        } else {
            routingInspectEntity.setCreateBy(realName);
        }
        if (Objects.isNull(routingInspectEntity.getArrivalCount()) || routingInspectEntity.getArrivalCount() == 0) {
            routingInspectEntity.setArrivalCount(this.getArrivalCount(request.getBoxBarcode(), request.getSkuBarcode(), request.getSku()));
        }
        String statusDesc = getRoutingInspectCheckStatusDesc(Integer.valueOf(request.getCheckStatus()), routingInspectEntity.getCancelCount(), routingInspectEntity.getArrivalCount());
        if (StringUtils.hasText(statusDesc)) {
            routingInspectEntity.setCheckStatus(statusDesc);
        }
        if (routingInspectEntity.getSupplierId() != null) {
            String departName = this.getErpDept(routingInspectEntity.getSupplierId());
            if (StringUtils.hasText(departName)) {
                routingInspectEntity.setDepartment(departName);
            } else
                routingInspectEntity.setDepartment("Ka");
        }
        routingInspectEntity.setQcUserName(loginInfoService.getUserName());
        routingInspectEntity.setQcUserRealName(realName);
        this.saveOrUpdate(routingInspectEntity);
        qaRoutingInspectItemService.saveInspectItemInfo(request.getBoxBarcode(), request.getSku(), routingInspectEntity.getId());
        qaRoutingInspectSkuTypeInfoService.saveLabelInfo(request.getPurchaseNumber(), request.getSku(), routingInspectEntity.getId());
    }

    /**
     * 新增或更新巡检信息
     * 步骤一：判断处于让步接收中状态，是则不允许进行，否则继续
     * 步骤二：生成对应的巡检任务
     * 步骤三：判断是否存在对应的质检任务，如果没有则生成
     */
    public StockinQaRoutingInspectEntity saveRoutingInspectItem(RoutingInspectItemSaveRequest request) {
        if (stockinQaOrderService.validProcessingByInfo(request.getBoxBarcode(), request.getSku())) {
            throw new BusinessServiceException(String.format("【%s】让步接收处理中，不允许巡检", request.getSku()));
        }
        StockinQaRoutingInspectEntity routingInspectEntity = new StockinQaRoutingInspectEntity();
        BeanUtilsEx.copyProperties(request, routingInspectEntity);
        routingInspectEntity.setHadPush(0);
        routingInspectEntity.setInternalBoxCode(request.getBoxBarcode());
        routingInspectEntity.setCreateBy(loginInfoService.getName());
        routingInspectEntity.setUpdateBy(loginInfoService.getName());
        routingInspectEntity.setUserId(loginInfoService.getUserId());
        routingInspectEntity.setLocation(TenantContext.getTenant());
        if (!CollectionUtils.isEmpty(request.getInspectItemImgList())) {
            StringBuilder imgUrlStringBuilder = new StringBuilder();
            for (String imgUrl : request.getInspectItemImgList()) {
                if (request.getInspectItemImgList().size() == 1) {
                    imgUrlStringBuilder.append(imgUrl);
                } else {
                    imgUrlStringBuilder.append(imgUrl).append(',');
                }
            }
            String imgUrlStr = imgUrlStringBuilder.toString();
            if (StringUtils.hasText(imgUrlStr)) {
                routingInspectEntity.setRoutingInspectImgUrl(imgUrlStr);
            }
        } else {
            routingInspectEntity.setRoutingInspectImgUrl(null);
        }
        if (Objects.isNull(routingInspectEntity.getArrivalCount()) || routingInspectEntity.getArrivalCount() == 0) {
            routingInspectEntity.setArrivalCount(this.getArrivalCount(request.getBoxBarcode(), request.getSkuBarcode(), request.getSku()));
        }
        //转换成对应的巡检状态
        String statusDesc = getRoutingInspectCheckStatusDesc(Integer.valueOf(request.getCheckStatus()), routingInspectEntity.getCancelCount(), routingInspectEntity.getArrivalCount());
        if (StringUtils.hasText(statusDesc)) {
            routingInspectEntity.setCheckStatus(statusDesc);
        }
        //更新或生成质检任务信息
        saveOrUpdateQaTask(request);
        //赋值对应的供应商部门信息
        if (routingInspectEntity.getSupplierId() != null) {
            String departName = this.getErpDept(routingInspectEntity.getSupplierId());
            if (StringUtils.hasText(departName)) {
                routingInspectEntity.setDepartment(departName);
            } else
                routingInspectEntity.setDepartment("Ka");
        }
        routingInspectEntity.setQcUserName(loginInfoService.getName());
        routingInspectEntity.setQcUserRealName(loginInfoService.getName());
        this.buildSkcInfo(routingInspectEntity);
        this.saveOrUpdate(routingInspectEntity);
        //同步旧系统
        StockinQaRoutingInspectSyncDTO syncDTO = new StockinQaRoutingInspectSyncDTO(routingInspectEntity.getId());
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_ROUTING_INSPECT_SYNC_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_ROUTING_INSPECT_SYNC_TOPIC,
            Key.of(TenantContext.getTenant() + "_" + syncDTO.getId()), new LocationWrapperMessage(TenantContext.getTenant(), syncDTO));
        //保存明细信息
        qaRoutingInspectItemService.saveInspectItemInfo(request.getBoxBarcode(), request.getSku(), routingInspectEntity.getId());
        //保存标签信息
        qaRoutingInspectSkuTypeInfoService.saveLabelInfo(request.getPurchaseNumber(), request.getSku(), routingInspectEntity.getId());
        return routingInspectEntity;
    }

    /**
     * 更新质检任务信息，如果不存在质检任务则生成对应的质检任务
     *
     * @param request
     */
    public void saveOrUpdateQaTask(RoutingInspectItemSaveRequest request) {
        StockinQaTaskEntity qaTaskInfo = stockinQaTaskService.getQaTaskByInfo(request.getBoxBarcode(), request.getSku());
        if (ObjectUtils.isEmpty(qaTaskInfo)) {
            // 新增质检任务
            stockinQaTaskService.generateOrUpdateQaTask(request.getBoxBarcode(), request.getSku());
            qaTaskInfo = stockinQaTaskService.getQaTaskByInfo(request.getBoxBarcode(), request.getSku());
        }
        //更新质检任务的状态
        String statusDesc = getInspectCheckStatusDesc(Integer.valueOf(request.getCheckStatus()));
        if (StringUtils.hasText(statusDesc)) {
            qaTaskInfo.setCheckStatus(statusDesc);
        }
        //更新质检任务
        if (ObjectUtils.isNotEmpty(qaTaskInfo)) {
            qaTaskInfo.setUpdateBy(loginInfoService.getName());
            stockinQaTaskService.updateById(qaTaskInfo);
            //同步旧质检系统
            if (Lists.newArrayList(StockinQaTaskStatusEnum.QC_COMPLETED.name(), StockinQaTaskStatusEnum.CANCELLED.name()).contains(qaTaskInfo.getCheckStatus())) {
                supplierApiService.syncStockinQaTask(request.getBoxBarcode(), qaTaskInfo.getSku());
            }
        }
    }

    /**
     * 修改质检任务状态 by checkStatus
     */
    String getInspectCheckStatusDesc(Integer status) {
        String checkStatusStr = "";
        if (status == 1) {
            // 巡检状态推送QA,质检状态为待质检
            checkStatusStr = StockinQaTaskStatusEnum.PENDING_QC.name();
        } else if (status == 2 || status == 3) {
            // 巡检状态已推上架,质检状态为已质检
            checkStatusStr = StockinQaTaskStatusEnum.QC_COMPLETED.name();
        } else if (status == 4) {
            // 巡检状态已退货,质检状态为已质检
            checkStatusStr = StockinQaTaskStatusEnum.CANCELLED.name();
        }
        return checkStatusStr;
    }

    /**
     * 修改巡检状态 by checkStatus
     */
    String getRoutingInspectCheckStatusDesc(Integer status, Integer cancelCount, Integer arrivalCount) {
        String checkStatusStr = "";
        if (status == 1) {
            // 巡检状态已推送QA
            checkStatusStr = InboundsRequiredRoutingInspectedCheckStatusEnum.PUSH_QUALITY_INSPECTION.getDesc();
        } else if (status == 2) {
            // 巡检状态推送上架
            checkStatusStr = InboundsRequiredRoutingInspectedCheckStatusEnum.PUSH_ON_SALE.getDesc();
        } else if (status == 3) {
            if (cancelCount >= arrivalCount) {
                // 巡检状态已退货
                checkStatusStr = InboundsRequiredRoutingInspectedCheckStatusEnum.RETURN_ALL_BACK_ROUTING_INSPECTED.getDesc();
            } else {
                // 巡检状态已退货
                checkStatusStr = InboundsRequiredRoutingInspectedCheckStatusEnum.RETURN_BACK_ROUTING_INSPECTED.getDesc();
            }
        } else if (status == 4) {
            // 忽略
            checkStatusStr = InboundsRequiredRoutingInspectedCheckStatusEnum.IGNORE_ROUTING_INSPECTED.getDesc();
        }
        return checkStatusStr;
    }

    /**
     * 设置到货数量
     *
     * <AUTHOR>
     * 2021-06-18
     */
    private Integer getArrivalCount(String boxBarcode, String skuBarcode, String sku) {
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.getInfoBySkuBarcode(StringUtils.hasText(skuBarcode) ? skuBarcode : sku);
        if (Objects.isNull(specInfoEntity)) {
            return 0;
        }
        //赋值内部箱对应的入库单的到货数
        return stockInternalBoxItemService.getArriveCountByInfo(boxBarcode, specInfoEntity.getSku());
    }

    private QcInboundsMessage buildQcInboundsMessage(StockinQaRoutingInspectEntity item, RoutingInspectItemSaveRequest saveRequest) {
        QcInboundsMessage qcInboundsMessage = new QcInboundsMessage();
        BeanUtilsEx.copyProperties(item, qcInboundsMessage);
        qcInboundsMessage.setGoodsToRefundCount(item.getCancelCount());
        qcInboundsMessage.setPurchaseNumbers(saveRequest.getPurchaseNumber());
        qcInboundsMessage.setReceiveOrderNos(saveRequest.getReceiveOrder());
        qcInboundsMessage.setTestTotalCount(item.getQcCount());
        qcInboundsMessage.setUnqualifiedCount(item.getCancelCount());
        qcInboundsMessage.setResult(getStatusDesc(item));
        qcInboundsMessage.setQcInboundsId(item.getId());
        qcInboundsMessage.setQaType(StockChangeLogTypeEnum.RETURN_ROUTING_INSPECTED);
        qcInboundsMessage.setIsQcRoutingInspect(1);
        qcInboundsMessage.setBoxBarcode(item.getInternalBoxCode());
        qcInboundsMessage.setIsWmsQa(1);
        return qcInboundsMessage;
    }

    /**
     * 将巡检的状态转化成PC质检的状态
     */
    private String getStatusDesc(StockinQaRoutingInspectEntity item) {
        String status = item.getCheckStatus();
        String checkStatusStr = "";
        if (InboundsRequiredRoutingInspectedCheckStatusEnum.PUSH_ON_SALE.getDesc().equals(status)) {
            checkStatusStr = QcInboundsResultStatusEnum.PUT_ON.getDesc();
        } else if (Lists.newArrayList(InboundsRequiredRoutingInspectedCheckStatusEnum.RETURN_BACK_ROUTING_INSPECTED.getDesc(),
            InboundsRequiredRoutingInspectedCheckStatusEnum.RETURN_ALL_BACK_ROUTING_INSPECTED.getDesc()).contains(status)) {
            checkStatusStr = item.getQcCount() != null && item.getQcCount().equals(item.getCancelCount()) ? QcInboundsResultStatusEnum.FACTORY_REWORK.getDesc() : QcInboundsResultStatusEnum.RECEIVE_BUT_SOME_RETURN.getDesc();
        }
        return checkStatusStr;
    }

    private void buildSkcInfo(StockinQaRoutingInspectEntity routingInspectEntity) {
        if (Objects.isNull(routingInspectEntity) || !StringUtils.hasText(routingInspectEntity.getSku())) {
            return;
        }
        ProductSpecInfo specInfo = specInfoService.getBySku(routingInspectEntity.getSku());
        routingInspectEntity.setSkc(specInfo.getSkc());
        ProductInfo productInfo = productInfoService.getByProductId(specInfo.getProductId());
        routingInspectEntity.setSpu(productInfo.getSpu());
        routingInspectEntity.setProductId(specInfo.getProductId());
    }

    /**
     * 获取部门映射
     *
     * @param supplierId
     * @return
     */
    private String getErpDept(Integer supplierId) {
        SupplierEntity entity = supplierService.getBySupplierId(supplierId);
        if (Objects.isNull(entity) || Objects.isNull(entity.getAffiliateDeptId())) {
            LOGGER.error("TopicSupplierSaveConsumer getErpDept ignore, empty deptId");
            return null;
        }
        String parentKey = DictionaryNameEnum.SCM_DOCKING_DEPARTMENT.getName();
        try {
            enumConversionChineseUtils.getBdDictionaryItemMapByDictionaryName(parentKey);
            return Optional.ofNullable(enumConversionChineseUtils.getBdDictionaryItemMapByDictionaryName(parentKey)
                    .get(String.format("%s#%s", parentKey, String.valueOf(entity.getAffiliateDeptId()))))
                .map(BdDictionaryItem::getAttribute)
                .orElse(null);
        } catch (RuntimeException e) {
            LOGGER.error("TopicSupplierSaveConsumer getErpDept error, cause= {}", e.getMessage(), e);
        }
        return null;
    }

    public PageResponse<StockinQaRoutingInspectPageResponse> pageList(StockinQaRoutingInspectPageRequest request) {
        PageResponse<StockinQaRoutingInspectPageResponse> response = new PageResponse<>();

        Page<StockinQaRoutingInspectPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinQaRoutingInspectPageResponse> pageList = this.getBaseMapper().pageList(page, request);
        List<StockinQaRoutingInspectPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setContent(Collections.emptyList());
            response.setTotalCount(0L);
            return response;
        }
        Map<Integer, List<String>> skuTypeMap = qaRoutingInspectSkuTypeInfoService.getMapByQcRoutingInspectId(pageList.getRecords().stream().map(StockinQaRoutingInspectPageResponse::getId).collect(Collectors.toList()));
        records.forEach(detail -> {
            if (CollectionUtils.isEmpty(skuTypeMap.get(detail.getId()))) {
                return;
            }
            detail.setSkuTypeStr(String.join(",", skuTypeMap.get(detail.getId())));
            detail.setSkuTypeList(skuTypeMap.get(detail.getId()));
        });
        response.setContent(records);
        response.setTotalCount(page.getTotal());
        return response;
    }

    public StockinQaRoutingInspectDetailResponse findDetail(Integer id) {
        return this.getBaseMapper().findDetail(id);
    }

    public void updateRoutingInspectInfo(StockinQaRoutingInspectUpdateRequest request) {
        StockinQaRoutingInspectEntity routingInspectEntity = new StockinQaRoutingInspectEntity();
        routingInspectEntity.setId(request.getId());
        routingInspectEntity.setCheckStatus(request.getCheckStatus());
        routingInspectEntity.setItemCraftResult(request.getItemCraftResult());
        routingInspectEntity.setItemPartResult(request.getItemPartResult());
        routingInspectEntity.setItemRemark(request.getItemRemark());
        routingInspectEntity.setUnqualifiedCount(Objects.nonNull(request.getUnqualifiedCount()) ? request.getUnqualifiedCount() : 0);
        routingInspectEntity.setUnqualifiedCategory(Objects.nonNull(request.getUnqualifiedCategory()) ? request.getUnqualifiedCategory() : "");
        routingInspectEntity.setUnqualifiedReason(Objects.nonNull(request.getUnqualifiedReason()) ? request.getUnqualifiedReason() : "");
        if (request.getRoutingInspectImgUrl() != null && !request.getRoutingInspectImgUrl().isEmpty()) {
            routingInspectEntity.setRoutingInspectImgUrl(String.join(",", request.getRoutingInspectImgUrl()));
        } else {
            routingInspectEntity.setRoutingInspectImgUrl("");
        }
        this.updateById(routingInspectEntity);
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_QA_ROUTING_INSPECT_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinQaRoutingInspectPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinQaRoutingInspectPageRequest.class);
        // 设置每次的查询数量
        Page<StockinQaRoutingInspectPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinQaRoutingInspectPageResponse> pageResponse = this.baseMapper.pageList(page, downloadRequest);
        response.setTotalCount(pageResponse.getTotal());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getRecords()));
        return response;
    }

    /**
     * 根据信息获取巡检状态
     *
     * @param request
     * @return
     */
    public StockinQaRoutingInspectStatusQueryResponse getCheckStatusByInfo(StockinQaRoutingInspectStatusQueryRequest request) {
        StockinQaRoutingInspectEntity info = this.getBaseMapper().findCheckStatusByInfo(request.getBoxBarcode(), request.getPurchaseNumber(),
            Collections.singletonList(request.getReceiveOrder()), request.getSku());
        if (Objects.isNull(info)) {
            return null;
        }
        StockinQaRoutingInspectStatusQueryResponse response = new StockinQaRoutingInspectStatusQueryResponse();
        response.setCheckStatus(this.convertCheckStatus(info.getCheckStatus()));
        return response;
    }

    /**
     * 转成旧系统状态
     *
     * @param checkStatus
     * @return
     */
    public String convertCheckStatus(String checkStatus) {
        switch (InboundsRequiredRoutingInspectedCheckStatusEnum.getEnumByDesc(checkStatus)) {
            case PUSH_ON_SALE:
                return "已推送上架";
            case PUSH_QUALITY_INSPECTION:
                return "已推送QA质检";
            case RETURN_BACK_ROUTING_INSPECTED:
            case RETURN_ALL_BACK_ROUTING_INSPECTED:
                return "退货";
            default:
                return "已忽略";
        }
    }

    /**
     * 根据商品条形码获取QA巡检信息
     *
     * @param barcode
     * @return
     */
    public GetScanBarcodeSearchQaInspectionInfoResponse getScanBarcodeSearchQaInspectionInfo(String barcode) {
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.getInfoBySkuBarcode(barcode);
        if (Objects.isNull(specInfoEntity)) {
            throw new BusinessServiceException("未找到商品规格");
        }

        ProductDevelopAttachInspectionRequest request = new ProductDevelopAttachInspectionRequest();
        ProductInfoEntity topByProductId = productInfoService.findTopByProductId(specInfoEntity.getProductId());
        request.setProductId(topByProductId.getErpProductId());
        request.setType("design");
        List<Attach> productDevelopAttachList = productApiService.getProductDevelopAttachList(request);

        GetScanBarcodeSearchQaInspectionInfoResponse response = new GetScanBarcodeSearchQaInspectionInfoResponse();
        response.setProductTechnologyList(productDevelopAttachList);

        response.setSkc(specInfoEntity.getSkc());

        // 根据skc获取商品图片列表
        List<String> collect = productSpecInfoService.list(new LambdaQueryWrapper<ProductSpecInfoEntity>()
                        .select(ProductSpecInfoEntity::getPreviewImageUrl)
                        .eq(ProductSpecInfoEntity::getSkc, specInfoEntity.getSkc()))
                .stream().map(ProductSpecInfoEntity::getPreviewImageUrl).collect(Collectors.toList());

        response.setImgUrls(collect);
        response.setImgUrl(specInfoEntity.getPreviewImageUrl());


        return response;

    }
}
