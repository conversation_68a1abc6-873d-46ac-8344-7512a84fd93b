package com.nsy.wms.business.service.stockout;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.StockoutVasTaskDownloadList;
import com.nsy.api.wms.domain.stockout.StockoutVasTaskList;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskCreateEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutVasTaskTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutVasTaskListRequest;
import com.nsy.api.wms.request.stockout.StockoutVasTaskOpRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskCountResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskDetailResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskListResponse;
import com.nsy.wms.business.domain.dto.stockout.StockoutStorePositionVasTaskDTO;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStoreDetailResponse;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdStorePositionMappingService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutValueAddServiceTaskEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutValueAddServiceTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StockoutVasTaskService extends ServiceImpl<StockoutValueAddServiceTaskMapper, StockoutValueAddServiceTaskEntity> implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutVasTaskService.class);

    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutVasTaskItemService taskItemService;
    @Autowired
    StockoutVasTaskLogService taskLogService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    OmsApiService omsApiService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    BdStorePositionMappingService storePositionMappingService;

    /**
     * 创建增值任务
     *
     * @param stockoutOrderId
     */
    @Transactional(rollbackFor = Exception.class)
    public void createByStockoutOrderId(Integer stockoutOrderId, StockoutVasTaskCreateEnum stockoutVasTaskCreateEnum) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(stockoutOrderId);
        //获取增值服务
        List<StockoutOrderItemEntity> vasTypeList = this.getStockoutOrderVasInfo(stockoutOrderEntity);
        if (CollectionUtils.isEmpty(vasTypeList)) {
            return;
        }
        // 任务
        StockoutValueAddServiceTaskEntity taskEntity = new StockoutValueAddServiceTaskEntity();
        BeanUtils.copyProperties(stockoutOrderEntity, taskEntity);
        taskEntity.setStatus(StockoutVasTaskStatusEnum.WAIT.name());
        taskEntity.setCreateBy(loginInfoService.getName());
        taskEntity.setVasQty(vasTypeList.stream().mapToInt(StockoutOrderItemEntity::getQty).sum());
        taskEntity.setVasType(vasTypeList.stream().map(StockoutOrderItemEntity::getVasType).findFirst().orElse(""));
        //增值服务单价
        BigDecimal unitPrice = this.calculateValueAddCost(StockoutVasTaskTypeEnum.getVasTypeStr(taskEntity.getVasType()));
        taskEntity.setVasPrice(unitPrice.multiply(new BigDecimal(taskEntity.getVasQty())));
        this.save(taskEntity);
        // 明细
        taskItemService.createByStockoutItems(vasTypeList, taskEntity.getId());
        // 日志
        taskLogService.addLog(taskEntity.getId(), StockoutVasTaskLogTypeEnum.INIT, String.format("%s，生成增值任务", stockoutVasTaskCreateEnum.getDesc()));
    }

    private List<StockoutOrderItemEntity> getStockoutOrderVasInfo(StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        //是否为亚马逊首单增值服务类型，则固定为增值服务类型为换包装
        BdSystemParameterEntity systemParameter = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_AMAZON_FIRST_ORDER_ADD_VALUE.getKey());
        Boolean supportAmazonFistOrder = Boolean.FALSE;
        //无配置默认支持泉州
        if (Objects.isNull(systemParameter) && LocationEnum.QUANZHOU.name().equalsIgnoreCase(stockoutOrderEntity.getLocation())) {
            supportAmazonFistOrder = Boolean.TRUE;
        } else if (Objects.nonNull(systemParameter) && StockConstant.TRUE.equalsIgnoreCase(systemParameter.getConfigValue())) {
            supportAmazonFistOrder = Boolean.TRUE;
        }
        //增值类型集合  亚马逊首单只支持配置地区，如果没配置则默认支持泉州地区
        List<StockoutOrderItemEntity> vasTypeList = new ArrayList<>();
        if (stockoutOrderItemEntityList.stream().anyMatch(o -> StringUtils.hasText(o.getVasType()))) {
            vasTypeList.addAll(stockoutOrderItemEntityList.stream().filter(o -> StringUtils.hasText(o.getVasType())).collect(Collectors.toList()));
        } else if (supportAmazonFistOrder && StockoutOrderPlatformEnum.AMAZON.getName().equals(stockoutOrderEntity.getPlatformName())
                && stockoutOrderItemEntityList.stream().anyMatch(o -> 1 == o.getIsFirstOrderByStore())) {
            //明细表增值类型为空且平台不为亚马逊则返回
            vasTypeList.addAll(stockoutOrderItemEntityList.stream().filter(detail -> {
                if (1 == detail.getIsFirstOrderByStore()) {
                    detail.setVasType(StockoutVasTaskTypeEnum.CH_PACK.name());
                    return true;
                }
                return false;
            }).collect(Collectors.toList()));
        }
        //获取融合标库存的增值服务,并合并到当前增值任务中
        return this.combineSotrePositionVasTaskList(vasTypeList, stockoutOrderEntity, stockoutOrderItemEntityList);
    }

    /**
     * 列表查询
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutVasTaskListResponse> getListByRequest(StockoutVasTaskListRequest request) {
        PageResponse<StockoutVasTaskListResponse> pageResponse = new PageResponse<>();
        Page<StockoutVasTaskList> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutVasTaskList> pageResult = this.baseMapper.pageSearchList(page, request);

        List<StockoutVasTaskListResponse> recordList = pageResult.getRecords().stream().map(projection -> {
            StockoutVasTaskListResponse response = new StockoutVasTaskListResponse();
            BeanUtils.copyProperties(projection, response);
            response.setWorkspaceStr(StockoutOrderWorkSpaceEnum.getNameBy(projection.getWorkspace()));
            response.setBusinessTypeStr(projection.getBusinessType());
            response.setStatusStr(StockoutVasTaskStatusEnum.getNameBy(projection.getStatus()));
            // 增值类型处理
            response.setVasTypeStr(StockoutVasTaskTypeEnum.getVasTypeStr(projection.getVasType()));
            // 订单号处理
            response.setOrderNos(taskItemService.getOrderNoByTaskId(projection.getId()));
            return response;
        }).collect(Collectors.toList());

        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(recordList);
        return pageResponse;
    }

    /**
     * 增值任务详情
     *
     * @param taskId
     * @return
     */
    public StockoutVasTaskDetailResponse getDetailById(Integer taskId) {
        StockoutVasTaskDetailResponse response = new StockoutVasTaskDetailResponse();
        StockoutValueAddServiceTaskEntity taskEntity = this.getById(taskId);
        BeanUtils.copyProperties(taskEntity, response);
        response.setWorkspaceStr(StockoutOrderWorkSpaceEnum.getNameBy(taskEntity.getWorkspace()));
        response.setBusinessTypeStr(taskEntity.getBusinessType());
        // 订单号处理
        response.setOrderNos(taskItemService.getOrderNoByTaskId(taskId));
        // 订单备注
        response.setRemark(stockoutOrderService.getByStockoutOrderNo(taskEntity.getStockoutOrderNo()).getDescription());
        return response;
    }

    /**
     * 修改增值类型
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateVasType(StockoutVasTaskOpRequest request) {
        if (CollectionUtils.isEmpty(request.getVasTypeList())) {
            throw new BusinessServiceException("请选择增值类型");
        }
        if (CollectionUtils.isEmpty(request.getIdList())) {
            throw new BusinessServiceException("请选择任务id");
        }
        String vasType = String.join(",", request.getVasTypeList());
        BigDecimal unitPrice = this.calculateValueAddCost(StockoutVasTaskTypeEnum.getVasTypeStr(vasType));
        List<StockoutValueAddServiceTaskEntity> taskEntityList = this.list(new QueryWrapper<StockoutValueAddServiceTaskEntity>()
                .lambda().in(StockoutValueAddServiceTaskEntity::getId, request.getIdList()));
        for (StockoutValueAddServiceTaskEntity taskEntity : taskEntityList) {
            String oldType = taskEntity.getVasType();
            taskEntity.setVasType(vasType);
            taskEntity.setVasPrice(unitPrice.multiply(new BigDecimal(taskEntity.getVasQty())));
            taskEntity.setUpdateBy(loginInfoService.getName());
            // 明细
            taskItemService.updateVasType(taskEntity.getId(), vasType);
            // 日志
            taskLogService.addLog(taskEntity.getId(), StockoutVasTaskLogTypeEnum.UPDATE, String.format("增值类型由原来的【%s】改成【%s】", StockoutVasTaskTypeEnum.getVasTypeStr(oldType), StockoutVasTaskTypeEnum.getVasTypeStr(vasType)));
        }
        this.updateBatchById(taskEntityList);
    }


    /**
     * 增值完成
     *
     * @param request
     */
    public void finishVas(StockoutVasTaskOpRequest request) {
        if (CollectionUtils.isEmpty(request.getIdList())) {
            throw new BusinessServiceException("请选择任务id");
        }
        List<StockoutValueAddServiceTaskEntity> taskEntityList = this.list(new QueryWrapper<StockoutValueAddServiceTaskEntity>()
                .lambda().in(StockoutValueAddServiceTaskEntity::getId, request.getIdList()));
        for (StockoutValueAddServiceTaskEntity taskEntity : taskEntityList) {
            taskEntity.setStatus(StockoutVasTaskStatusEnum.DONE.name());
            taskEntity.setUpdateBy(loginInfoService.getName());
            // 明细
            taskItemService.updateVasStatus(taskEntity.getId(), StockoutVasTaskStatusEnum.DONE.name());
            // 日志
            taskLogService.addLog(taskEntity.getId(), StockoutVasTaskLogTypeEnum.FINISH, "完成增值任务");
        }
        this.updateBatchById(taskEntityList);
    }

    /**
     * 根据出库单号完成增值任务
     *
     * @param stockoutOrderNo
     */
    public void finishVasByStockoutOrderNo(String stockoutOrderNo) {
        StockoutValueAddServiceTaskEntity taskEntity = this.getOne(new QueryWrapper<StockoutValueAddServiceTaskEntity>().lambda()
                .eq(StockoutValueAddServiceTaskEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutValueAddServiceTaskEntity::getVasType, StockoutVasTaskTypeEnum.INSPECTION.name())
                .ne(StockoutValueAddServiceTaskEntity::getStatus, StockoutVasTaskStatusEnum.CANCELED.name())
                .last(MybatisQueryConstant.QUERY_FIRST));
        //不存在增值任务.或增值任务已完成
        if (ObjectUtils.isEmpty(taskEntity) || StockoutVasTaskStatusEnum.DONE.name().equals(taskEntity.getStatus())) {
            return;
        }
        taskEntity.setStatus(StockoutVasTaskStatusEnum.DONE.name());
        taskEntity.setUpdateBy(loginInfoService.getName());
        // 明细
        taskItemService.updateVasStatus(taskEntity.getId(), StockoutVasTaskStatusEnum.DONE.name());
        // 日志
        taskLogService.addLog(taskEntity.getId(), StockoutVasTaskLogTypeEnum.FINISH, "全检完成自动完结增值任务");
        this.updateById(taskEntity);
    }

    /**
     * 增值任务取消
     *
     * @param stockoutOrderNo
     */
    public void cancelByStockoutOrder(String stockoutOrderNo) {
        StockoutValueAddServiceTaskEntity taskEntity = this.getOne(new QueryWrapper<StockoutValueAddServiceTaskEntity>().lambda()
                .eq(StockoutValueAddServiceTaskEntity::getStockoutOrderNo, stockoutOrderNo)
                .ne(StockoutValueAddServiceTaskEntity::getStatus, StockoutVasTaskStatusEnum.CANCELED.name())
                .last(MybatisQueryConstant.QUERY_FIRST));
        if (taskEntity == null) {
            return;
        }
        taskEntity.setStatus(StockoutVasTaskStatusEnum.CANCELED.name());
        taskEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(taskEntity);
        // 明细
        taskItemService.updateVasStatus(taskEntity.getId(), StockoutVasTaskStatusEnum.CANCELED.name());
        // 日志
        taskLogService.addLog(taskEntity.getId(), StockoutVasTaskLogTypeEnum.CANCEL, "增值任务对应的出库单取消");
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_VAS_TASK_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockoutVasTaskListRequest taskListRequest = JSONObject.parseObject(request.getRequestContent(), StockoutVasTaskListRequest.class);
        taskListRequest.setPageIndex(request.getPageIndex());
        taskListRequest.setPageSize(request.getPageSize());
        PageResponse<StockoutVasTaskListResponse> vasTaskListResponsePageResponse = this.getListByRequest(taskListRequest);

        List<StockoutVasTaskDownloadList> result = vasTaskListResponsePageResponse.getContent().stream().map(projection -> {
            StockoutVasTaskDownloadList item = new StockoutVasTaskDownloadList();
            BeanUtils.copyProperties(projection, item);
            item.setCreateDate(DateUtils.format(projection.getCreateDate(), DateUtils.DATE_FORMAT_DATE4));
            item.setOrderNo(String.join(",", projection.getOrderNos()));
            return item;
        }).collect(Collectors.toList());

        response.setTotalCount(vasTaskListResponsePageResponse.getTotalCount());
        response.setDataJsonStr(JSON.toJSONString(result));
        return response;
    }

    private BigDecimal calculateValueAddCost(String vasTypeStr) {
        if (!StringUtils.hasText(vasTypeStr)) {
            return BigDecimal.ZERO;
        }
        String configValue = bdSystemParameterService.findConfigValue(BdSystemParameterEnum.WMS_VALUE_ADD_PRICE_LIST);
        if (!StringUtils.hasText(configValue)) {
            LOGGER.info("未配置价格列表信息!");
            return BigDecimal.ZERO;
        }
        //价格集合，含多种增值服务组合价格
        Map<String, BigDecimal> priceListMap = JSONObject.parseObject(configValue, Map.class);
        if (Objects.isNull(priceListMap)) {
            return BigDecimal.ZERO;
        }
        List<String> vasTypeList = Arrays.asList(vasTypeStr.trim().split(","));
        //对集合进行排序
        Collections.sort(vasTypeList);
        List<String> priceList;
        //单种增值服务的价格，用于后续找不到组合价格后的后续计算
        Map<String, BigDecimal> pricesSimpleListMap = new HashMap<>();
        for (Map.Entry<String, BigDecimal> entry : priceListMap.entrySet()) {
            priceList = Arrays.asList(entry.getKey().split(","));
            if (priceList.size() == 1) {
                pricesSimpleListMap.put(entry.getKey(), entry.getValue());
            }
            if (priceList.size() != vasTypeList.size()) {
                continue;
            }
            Collections.sort(priceList);
            //两个排序后的集合进行比较
            if (vasTypeList.equals(priceList)) {
                return entry.getValue();
            }
        }
        BigDecimal unitPrice = BigDecimal.ZERO;
        for (String varType : vasTypeList) {
            if (Objects.nonNull(pricesSimpleListMap.get(varType))) {
                unitPrice = unitPrice.add(pricesSimpleListMap.get(varType));
            }
        }
        return unitPrice;
    }

    public StockoutVasTaskCountResponse vatTaskStatistics(StockoutVasTaskListRequest request) {
        return this.baseMapper.pageStatistics(request);
    }

    /**
     * 生成对应的增值任务
     */
    private List<StockoutOrderItemEntity> combineSotrePositionVasTaskList(List<StockoutOrderItemEntity> vasTypeList, StockoutOrderEntity stockoutOrderEntity,
                                                                          List<StockoutOrderItemEntity> stockoutOrderItemEntityList) {
        int storeId = this.convertFbaStoreId(stockoutOrderEntity.getStoreId());
        //获取店铺品牌信息
        String brandName = omsApiService.getStoreBrandInfo(storeId);
        for (StockoutOrderItemEntity orderItemEntity : stockoutOrderItemEntityList) {
            try {
                // 1. 获取库位信息
                BdPositionEntity positionInfo = bdPositionService.getByPositionCode(orderItemEntity.getPositionCode());
                if (!BdPositionTypeEnum.STORE_POSITION.name().equals(positionInfo.getPositionType())) {
                    continue;
                }
                // 2. 获取库位关联的店铺ID
                Integer positionCodeStoreId = storePositionMappingService.getStoreIdByPositionCode(positionInfo.getPositionCode());
                if (Objects.nonNull(positionCodeStoreId) && positionCodeStoreId.equals(storeId)) {
                    continue;
                }
                // 3. 获取库位店铺品牌信息
                String positionBrandName = omsApiService.getStoreBrandInfo(positionCodeStoreId);
                LOGGER.info("符合条需要生成融合标增值服务");
                // 4. 处理增值任务
                if (StringUtils.hasText(brandName) && (!StringUtils.hasText(positionBrandName) || !brandName.equals(positionBrandName))) {
                    LOGGER.info("符合条件需要生成增值服务剪吊牌换条码,预配的库位为 {} ,出库单号为 {} ,规格编码为 {}", positionInfo.getPositionCode(), stockoutOrderEntity.getStockoutOrderNo(), orderItemEntity.getSku());
                    dealStoreVasTask(orderItemEntity, vasTypeList, String.join(",", StockoutVasTaskTypeEnum.CUTTING_TAGS.name(), StockoutVasTaskTypeEnum.PASTING_BARCODE.name()));
                } else {
                    LOGGER.info("符合条件需要生成增值任务换条码,预配的库位为 {} ,出库单号为 {} ,规格编码为 {}", positionInfo.getPositionCode(), stockoutOrderEntity.getStockoutOrderNo(), orderItemEntity.getSku());
                    dealStoreVasTask(orderItemEntity, vasTypeList, StockoutVasTaskTypeEnum.PASTING_BARCODE.name());
                }
            } catch (Exception e) {
                LOGGER.error("店铺增值服务校验失败，出库单明细id为 {} ,规格编码为 {}", orderItemEntity.getStockoutOrderItemId(), orderItemEntity.getSku(), e);
            }
        }
        return vasTypeList;
    }

    /**
     * 处理店铺增值任务
     *
     * @param stockoutOrderItem
     * @param vasTypeList
     * @param vasType
     */
    private void dealStoreVasTask(StockoutOrderItemEntity stockoutOrderItem, List<StockoutOrderItemEntity> vasTypeList, String vasType) {
        //查找需要生成增值任务的出库单明细，如果原来有增值任务则合并，没有就赋值融合标的
        StockoutOrderItemEntity orderItemEntity = vasTypeList.stream().filter(item -> item.getStockoutOrderItemId().equals(stockoutOrderItem.getStockoutOrderItemId())).findFirst().orElse(null);
        //如果原先没有增值任务赋值增值任务
        if (Objects.isNull(orderItemEntity)) {
            //赋值增值任务
            stockoutOrderItem.setVasType(vasType);
            vasTypeList.add(stockoutOrderItem);
            return;
        }
        orderItemEntity.setVasType(this.combineVasType(orderItemEntity.getVasType(), vasType));
    }
    
    /**
     * 合并增值服务
     *
     * @param vasType
     * @param storeVasType
     * @return
     */
    private String combineVasType(String vasType, String storeVasType) {
        if (!StringUtils.hasText(vasType)) {
            return storeVasType;
        }
        if (!StringUtils.hasText(storeVasType)) {
            return vasType;
        }

        Set<String> combinedSet = new LinkedHashSet<>();
        Collections.addAll(combinedSet, vasType.split(","));
        Collections.addAll(combinedSet, storeVasType.split(","));

        return String.join(",", combinedSet);
    }

    public Map<String, String> getVasTaskMap(List<String> stockoutOrderNoList) {
        List<StockoutStorePositionVasTaskDTO> vasTypeInfoList = this.getBaseMapper().getVasTypeInfo(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(vasTypeInfoList)) {
            return Collections.emptyMap();
        }
        return vasTypeInfoList.stream().collect(Collectors.toMap(StockoutStorePositionVasTaskDTO::getSku, item -> convertVasType(item.getVasType()), (existing, replacement) -> existing));
    }

    private String convertVasType(String vasType) {
        if (!StringUtils.hasText(vasType)) {
            return "";
        }
        List<String> vasTypeList = Arrays.asList(vasType.split(","));
        Set<String> uniqueSet = new LinkedHashSet<>(vasTypeList);
        // 直接转换为枚举显示名
        return uniqueSet.stream().map(StockoutVasTaskTypeEnum::getNameBy).filter(Objects::nonNull).collect(Collectors.joining(","));
    }

    /**
     * 转换FBA店铺转换
     *
     * @param storeId
     * @return
     */
    private int convertFbaStoreId(Integer storeId) {
        SaStoreDetailResponse storeInfo = omsApiService.getStoreInfoById(storeId);
        if (Objects.isNull(storeInfo)) {
            return 0;
        }
        return storeInfo.getErpStoreName().endsWith("FBA") ? storeInfo.getAssociatedStoreId() : storeInfo.getId();
    }

    /**
     * 获取店铺增值任务map
     *
     * @param stockoutOrderNoList
     * @return
     */
    public Map<String, String> getSotrePositionVasTaskMapByNo(List<String> stockoutOrderNoList) {
        if (CollectionUtils.isEmpty(stockoutOrderNoList)) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>();

        for (String stockoutOrderNo : stockoutOrderNoList) {
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.findByStockoutOrderNo(stockoutOrderNo);
            // 合并当前订单的增值服务信息到总结果中
            List<StockoutOrderItemEntity> stockoutOrderVasInfoList = this.getStockoutOrderVasInfo(stockoutOrderEntity);
            if (CollectionUtils.isEmpty(stockoutOrderVasInfoList)) {
                continue;
            }
            Map<String, String> currentMap = stockoutOrderVasInfoList.stream().collect(Collectors.toMap(StockoutOrderItemEntity::getSku, item -> convertVasType(item.getVasType()), (existing, replacement) -> existing));
            if (CollectionUtils.isEmpty(currentMap)) {
                continue;
            }
            result.putAll(currentMap);
        }
        return result;
    }

    public Map<String, String> getSotrePositionVasTaskMapById(List<Integer> stockoutOrderIdList) {
        if (CollectionUtils.isEmpty(stockoutOrderIdList)) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>();

        for (Integer stockoutOrderId : stockoutOrderIdList) {
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderId(stockoutOrderId);
            // 合并当前订单的增值服务信息到总结果中
            List<StockoutOrderItemEntity> stockoutOrderVasInfoList = this.getStockoutOrderVasInfo(stockoutOrderEntity);
            if (CollectionUtils.isEmpty(stockoutOrderVasInfoList)) {
                continue;
            }
            Map<String, String> currentMap = stockoutOrderVasInfoList.stream().collect(Collectors.toMap(StockoutOrderItemEntity::getSku, item -> convertVasType(item.getVasType()), (existing, replacement) -> existing));
            if (CollectionUtils.isEmpty(currentMap)) {
                continue;
            }
            result.putAll(currentMap);
        }
        return result;
    }
}
