package com.nsy.api.wms.domain.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 关单(StockoutCustomsDeclareForm)
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
@ApiModel(value = "StockoutCustomsDeclareFormResult", description = "关单")
public class StockoutCustomsDeclareFormResult {

    @ApiModelProperty("主键ID")
    private Integer declareFormId;

    //地区
    @ApiModelProperty("地区")
    private String location;

    //系统标识
    @ApiModelProperty("系统标识")
    private String systemMark;

    @ApiModelProperty("系统标识Str")
    private String systemMarkStr;

    //报关单号
    @ApiModelProperty("报关单号")
    private String declareDocumentNo;

    //协议号
    @ApiModelProperty("协议号")
    private String protocolNo;

    //合同id
    @ApiModelProperty("合同id")
    private Integer declareContractId;

    //合同号
    @ApiModelProperty("合同号")
    private String declareContractNo;

    //公司id
    @ApiModelProperty("公司id")
    private Integer companyId;

    //公司名称
    @ApiModelProperty("公司名称")
    private String companyName;

    //状态
    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态Str")
    private String statusStr;

    //供应商id
    @ApiModelProperty("供应商id")
    private Integer supplierId;

    //供应商名称
    @ApiModelProperty("供应商名称")
    private String supplierName;

    //出库口岸
    @ApiModelProperty("出库口岸")
    private String exportPort;

    //出库口岸
    @ApiModelProperty("出库口岸code")
    private String exportPortCode;

    //海关编码
    @ApiModelProperty("海关编码")
    private String hsCode;

    //商品编码
    @ApiModelProperty("商品编码")
    private String gCode;

    //项号
    @ApiModelProperty("项号")
    private String gNo;

    //商品名称
    @ApiModelProperty("商品名称")
    private String gName;

    //单位
    @ApiModelProperty("单位")
    private String gUnit;

    //单位
    @ApiModelProperty("单位Code")
    private String unitCode;

    //出口数量
    @ApiModelProperty("出口数量")
    private Integer gQty;

    //申报要素
    @ApiModelProperty("申报要素")
    private String declareElement;

    //C&F金额
    @ApiModelProperty("C&F金额")
    private BigDecimal cfPrice;

    //含税单价
    @ApiModelProperty("含税单价")
    private BigDecimal taxInclusiveUnitPrice;

    //含税金额
    @ApiModelProperty("含税金额")
    private BigDecimal taxInclusivePrice;

    //分摊运费
    @ApiModelProperty("分摊运费")
    private BigDecimal apportionedFreight;

    //fob总价
    @ApiModelProperty("fob总价")
    private BigDecimal fobPrice;

    //fob总价(人民币)
    @ApiModelProperty("fob总价(人民币)")
    private BigDecimal fobPriceCny;

    //金额
    @ApiModelProperty("金额")
    private BigDecimal price;

    //汇率
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    //进项比例
    @ApiModelProperty("进项比例")
    private BigDecimal inputRate;

    //箱数
    @ApiModelProperty("箱数")
    private Integer boxQty;

//    //出库时间
//    @ApiModelProperty("出库时间")
//    private Date outboundDate;

    //客户
    @ApiModelProperty("客户")
    private String customer;

    //出库时间
    @ApiModelProperty("出库时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stockoutDate;

    //入库时间
    @ApiModelProperty("入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stockinDate;

    //合同签订时间
    @ApiModelProperty("合同签订时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractSignedDate;

    //出口日期
    @ApiModelProperty("出口日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date exportDate;

    //毛重
    @ApiModelProperty("毛重")
    private BigDecimal roughWeight;

    //申报批次
    @ApiModelProperty("申报批次")
    private String declareBatch;

    //出口发票号
    @ApiModelProperty("出口发票号")
    private String exportInvoiceNo;

    //出口开票日期
    @ApiModelProperty("出口开票日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date exportInvoiceDate;

    //是否工厂签署合同 0-否 1-是
    @ApiModelProperty("是否工厂签署合同 0-否 1-是")
    private Boolean hasSupplierSignContract;

    //是否填写发票 0-否 1-是
    private Boolean hasInvoiceFill;
    //审核不同过原因
    private String auditNoPassReason;
    //是否另选供应商 0-否 1-是
    private Boolean shouldChooseOtherSupplier;

    //是否需要审核进项明细 0-否 1-是
    @ApiModelProperty("是否需要审核进项明细")
    private Boolean shouldAuditItem;
    //商品类别ID
    private Integer wmsCategoryId;
    //包含父级分类
    private String allWmsCategory;
    //手动拆分总进项数量
    private Integer totalManualInputQty;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateDate;

    private String updateBy;

    private String createBy;

    //匹配时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date matchDate;

    //物流发票开票时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date logisticsInvoiceDate;
    //物流发票号
    private String logisticsInvoiceNumber;

    @ApiModelProperty("物流商")
    private String logisticsCompany;

    //报关单据签约时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date documentSigningDate;

    public Date getMatchDate() {
        return matchDate;
    }

    public void setMatchDate(Date matchDate) {
        this.matchDate = matchDate;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getExportPortCode() {
        return exportPortCode;
    }

    public void setExportPortCode(String exportPortCode) {
        this.exportPortCode = exportPortCode;
    }

    public String getSystemMarkStr() {
        return systemMarkStr;
    }

    public void setSystemMarkStr(String systemMarkStr) {
        this.systemMarkStr = systemMarkStr;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getDeclareFormId() {
        return declareFormId;
    }

    public void setDeclareFormId(Integer declareFormId) {
        this.declareFormId = declareFormId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSystemMark() {
        return systemMark;
    }

    public void setSystemMark(String systemMark) {
        this.systemMark = systemMark;
    }

    public String getDeclareDocumentNo() {
        return declareDocumentNo;
    }

    public void setDeclareDocumentNo(String declareDocumentNo) {
        this.declareDocumentNo = declareDocumentNo;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public Integer getDeclareContractId() {
        return declareContractId;
    }

    public void setDeclareContractId(Integer declareContractId) {
        this.declareContractId = declareContractId;
    }

    public String getDeclareContractNo() {
        return declareContractNo;
    }

    public void setDeclareContractNo(String declareContractNo) {
        this.declareContractNo = declareContractNo;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getExportPort() {
        return exportPort;
    }

    public void setExportPort(String exportPort) {
        this.exportPort = exportPort;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public String getgCode() {
        return gCode;
    }

    public void setgCode(String gCode) {
        this.gCode = gCode;
    }

    public String getgNo() {
        return gNo;
    }

    public void setgNo(String gNo) {
        this.gNo = gNo;
    }

    public String getgName() {
        return gName;
    }

    public void setgName(String gName) {
        this.gName = gName;
    }

    public String getgUnit() {
        return gUnit;
    }

    public void setgUnit(String gUnit) {
        this.gUnit = gUnit;
    }

    public Integer getgQty() {
        return gQty;
    }

    public void setgQty(Integer gQty) {
        this.gQty = gQty;
    }

    public String getDeclareElement() {
        return declareElement;
    }

    public void setDeclareElement(String declareElement) {
        this.declareElement = declareElement;
    }

    public BigDecimal getCfPrice() {
        return cfPrice;
    }

    public void setCfPrice(BigDecimal cfPrice) {
        this.cfPrice = cfPrice;
    }

    public BigDecimal getTaxInclusiveUnitPrice() {
        return taxInclusiveUnitPrice;
    }

    public void setTaxInclusiveUnitPrice(BigDecimal taxInclusiveUnitPrice) {
        this.taxInclusiveUnitPrice = taxInclusiveUnitPrice;
    }

    public BigDecimal getTaxInclusivePrice() {
        return taxInclusivePrice;
    }

    public void setTaxInclusivePrice(BigDecimal taxInclusivePrice) {
        this.taxInclusivePrice = taxInclusivePrice;
    }

    public BigDecimal getApportionedFreight() {
        return apportionedFreight;
    }

    public void setApportionedFreight(BigDecimal apportionedFreight) {
        this.apportionedFreight = apportionedFreight;
    }

    public BigDecimal getFobPrice() {
        return fobPrice;
    }

    public void setFobPrice(BigDecimal fobPrice) {
        this.fobPrice = fobPrice;
    }

    public BigDecimal getFobPriceCny() {
        return fobPriceCny;
    }

    public void setFobPriceCny(BigDecimal fobPriceCny) {
        this.fobPriceCny = fobPriceCny;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getInputRate() {
        return inputRate;
    }

    public void setInputRate(BigDecimal inputRate) {
        this.inputRate = inputRate;
    }

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public Date getStockoutDate() {
        return stockoutDate;
    }

    public void setStockoutDate(Date stockoutDate) {
        this.stockoutDate = stockoutDate;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public Date getContractSignedDate() {
        return contractSignedDate;
    }

    public void setContractSignedDate(Date contractSignedDate) {
        this.contractSignedDate = contractSignedDate;
    }

    public Date getExportDate() {
        return exportDate;
    }

    public void setExportDate(Date exportDate) {
        this.exportDate = exportDate;
    }

    public BigDecimal getRoughWeight() {
        return roughWeight;
    }

    public void setRoughWeight(BigDecimal roughWeight) {
        this.roughWeight = roughWeight;
    }

    public String getDeclareBatch() {
        return declareBatch;
    }

    public void setDeclareBatch(String declareBatch) {
        this.declareBatch = declareBatch;
    }

    public String getExportInvoiceNo() {
        return exportInvoiceNo;
    }

    public void setExportInvoiceNo(String exportInvoiceNo) {
        this.exportInvoiceNo = exportInvoiceNo;
    }

    public Date getExportInvoiceDate() {
        return exportInvoiceDate;
    }

    public void setExportInvoiceDate(Date exportInvoiceDate) {
        this.exportInvoiceDate = exportInvoiceDate;
    }

    public Boolean getHasSupplierSignContract() {
        return hasSupplierSignContract;
    }

    public void setHasSupplierSignContract(Boolean hasSupplierSignContract) {
        this.hasSupplierSignContract = hasSupplierSignContract;
    }

    public Boolean getHasInvoiceFill() {
        return hasInvoiceFill;
    }

    public void setHasInvoiceFill(Boolean hasInvoiceFill) {
        this.hasInvoiceFill = hasInvoiceFill;
    }

    public String getAuditNoPassReason() {
        return auditNoPassReason;
    }

    public void setAuditNoPassReason(String auditNoPassReason) {
        this.auditNoPassReason = auditNoPassReason;
    }

    public Boolean getShouldChooseOtherSupplier() {
        return shouldChooseOtherSupplier;
    }

    public void setShouldChooseOtherSupplier(Boolean shouldChooseOtherSupplier) {
        this.shouldChooseOtherSupplier = shouldChooseOtherSupplier;
    }

    public Integer getWmsCategoryId() {
        return wmsCategoryId;
    }

    public void setWmsCategoryId(Integer wmsCategoryId) {
        this.wmsCategoryId = wmsCategoryId;
    }

    public Integer getTotalManualInputQty() {
        return totalManualInputQty;
    }

    public void setTotalManualInputQty(Integer totalManualInputQty) {
        this.totalManualInputQty = totalManualInputQty;
    }

    public Boolean getShouldAuditItem() {
        return shouldAuditItem;
    }

    public void setShouldAuditItem(Boolean shouldAuditItem) {
        this.shouldAuditItem = shouldAuditItem;
    }

    public String getAllWmsCategory() {
        return allWmsCategory;
    }

    public void setAllWmsCategory(String allWmsCategory) {
        this.allWmsCategory = allWmsCategory;
    }

    public Date getLogisticsInvoiceDate() {
        return logisticsInvoiceDate;
    }

    public void setLogisticsInvoiceDate(Date logisticsInvoiceDate) {
        this.logisticsInvoiceDate = logisticsInvoiceDate;
    }

    public String getLogisticsInvoiceNumber() {
        return logisticsInvoiceNumber;
    }

    public void setLogisticsInvoiceNumber(String logisticsInvoiceNumber) {
        this.logisticsInvoiceNumber = logisticsInvoiceNumber;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Date getDocumentSigningDate() {
        return documentSigningDate;
    }

    public void setDocumentSigningDate(Date documentSigningDate) {
        this.documentSigningDate = documentSigningDate;
    }
}

