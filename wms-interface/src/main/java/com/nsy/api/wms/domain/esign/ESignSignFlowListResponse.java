package com.nsy.api.wms.domain.esign;

import java.util.List;

/**
 * 查询集成方企业流程列表响应参数
 */
public class ESignSignFlowListResponse {
    private Integer total;
    private List<SignFlowInfo> signFlowInfos;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<SignFlowInfo> getSignFlowInfos() {
        return signFlowInfos;
    }

    public void setSignFlowInfos(List<SignFlowInfo> signFlowInfos) {
        this.signFlowInfos = signFlowInfos;
    }

    // 内部类型全部放在类末尾
    public static class SignFlowInfo {
        private String signFlowId;
        private Long signFlowStartTime;
        private Long signFlowEndTime;
        private String signFlowTitle;
        private Integer signFlowStatus;
        private Integer rescissionStatus;
        private SignFlowInitiator signFlowInitiator;
        private List<Signer> signers;

        public String getSignFlowId() {
            return signFlowId;
        }

        public void setSignFlowId(String signFlowId) {
            this.signFlowId = signFlowId;
        }

        public Long getSignFlowStartTime() {
            return signFlowStartTime;
        }

        public void setSignFlowStartTime(Long signFlowStartTime) {
            this.signFlowStartTime = signFlowStartTime;
        }

        public Long getSignFlowEndTime() {
            return signFlowEndTime;
        }

        public void setSignFlowEndTime(Long signFlowEndTime) {
            this.signFlowEndTime = signFlowEndTime;
        }

        public String getSignFlowTitle() {
            return signFlowTitle;
        }

        public void setSignFlowTitle(String signFlowTitle) {
            this.signFlowTitle = signFlowTitle;
        }

        public Integer getSignFlowStatus() {
            return signFlowStatus;
        }

        public void setSignFlowStatus(Integer signFlowStatus) {
            this.signFlowStatus = signFlowStatus;
        }

        public Integer getRescissionStatus() {
            return rescissionStatus;
        }

        public void setRescissionStatus(Integer rescissionStatus) {
            this.rescissionStatus = rescissionStatus;
        }

        public SignFlowInitiator getSignFlowInitiator() {
            return signFlowInitiator;
        }

        public void setSignFlowInitiator(SignFlowInitiator signFlowInitiator) {
            this.signFlowInitiator = signFlowInitiator;
        }

        public List<Signer> getSigners() {
            return signers;
        }

        public void setSigners(List<Signer> signers) {
            this.signers = signers;
        }

        public static class SignFlowInitiator {
            private PersonInitiator psnInitiator;
            private OrgInitiator orgInitiator;

            public PersonInitiator getPsnInitiator() {
                return psnInitiator;
            }

            public void setPsnInitiator(PersonInitiator psnInitiator) {
                this.psnInitiator = psnInitiator;
            }

            public OrgInitiator getOrgInitiator() {
                return orgInitiator;
            }

            public void setOrgInitiator(OrgInitiator orgInitiator) {
                this.orgInitiator = orgInitiator;
            }
        }

        public static class PersonInitiator {
            private Account psnAccount;
            private String psnId;

            public Account getPsnAccount() {
                return psnAccount;
            }

            public void setPsnAccount(Account psnAccount) {
                this.psnAccount = psnAccount;
            }

            public String getPsnId() {
                return psnId;
            }

            public void setPsnId(String psnId) {
                this.psnId = psnId;
            }
        }

        public static class OrgInitiator {
            private String orgId;
            private String orgName;
            private Transactor transactor;

            public String getOrgId() {
                return orgId;
            }

            public void setOrgId(String orgId) {
                this.orgId = orgId;
            }

            public String getOrgName() {
                return orgName;
            }

            public void setOrgName(String orgName) {
                this.orgName = orgName;
            }

            public Transactor getTransactor() {
                return transactor;
            }

            public void setTransactor(Transactor transactor) {
                this.transactor = transactor;
            }
        }

        public static class Signer {
            private PersonSigner psnSigner;
            private OrgSigner orgSigner;

            public PersonSigner getPsnSigner() {
                return psnSigner;
            }

            public void setPsnSigner(PersonSigner psnSigner) {
                this.psnSigner = psnSigner;
            }

            public OrgSigner getOrgSigner() {
                return orgSigner;
            }

            public void setOrgSigner(OrgSigner orgSigner) {
                this.orgSigner = orgSigner;
            }
        }

        public static class PersonSigner {
            private String psnId;
            private Account psnAccount;

            public String getPsnId() {
                return psnId;
            }

            public void setPsnId(String psnId) {
                this.psnId = psnId;
            }

            public Account getPsnAccount() {
                return psnAccount;
            }

            public void setPsnAccount(Account psnAccount) {
                this.psnAccount = psnAccount;
            }
        }

        public static class OrgSigner {
            private String orgId;
            private String orgName;
            private Transactor transactor;

            public String getOrgId() {
                return orgId;
            }

            public void setOrgId(String orgId) {
                this.orgId = orgId;
            }

            public String getOrgName() {
                return orgName;
            }

            public void setOrgName(String orgName) {
                this.orgName = orgName;
            }

            public Transactor getTransactor() {
                return transactor;
            }

            public void setTransactor(Transactor transactor) {
                this.transactor = transactor;
            }
        }

        public static class Transactor {
            private Account psnAccount;
            private String psnId;

            public Account getPsnAccount() {
                return psnAccount;
            }

            public void setPsnAccount(Account psnAccount) {
                this.psnAccount = psnAccount;
            }

            public String getPsnId() {
                return psnId;
            }

            public void setPsnId(String psnId) {
                this.psnId = psnId;
            }
        }

        public static class Account {
            private String accountMobile;
            private String accountEmail;

            public String getAccountMobile() {
                return accountMobile;
            }

            public void setAccountMobile(String accountMobile) {
                this.accountMobile = accountMobile;
            }

            public String getAccountEmail() {
                return accountEmail;
            }

            public void setAccountEmail(String accountEmail) {
                this.accountEmail = accountEmail;
            }
        }
    }
} 