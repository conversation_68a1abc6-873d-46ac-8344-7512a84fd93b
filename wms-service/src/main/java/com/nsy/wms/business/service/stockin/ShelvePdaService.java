package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.domain.stockin.StockinOrderItemShelvedQty;
import com.nsy.api.wms.domain.stockin.StockinShelveSyncInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.enumeration.StockinShelveEventEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.StockinUpShelveByLackBatchRequest;
import com.nsy.api.wms.request.stockin.UpShelveByExceptionRequest;
import com.nsy.api.wms.request.stockin.UpShelveRequest;
import com.nsy.api.wms.request.stockin.WholeBoxUpShelveRequest;
import com.nsy.api.wms.response.stockin.UpShelvesResponse;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackReceivedRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockCenterCommonService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxItemQueryWrapper;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.business.service.stockin.building.StockinBuilding;
import com.nsy.wms.business.service.stockin.query.StockinShelveTaskItemQueryWrapper;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 * pda上架
 */
@Service
public class ShelvePdaService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShelvePdaService.class);
    @Autowired
    private StockInternalBoxMapper stockInternalBoxMapper;
    @Autowired
    private StockInternalBoxItemMapper stockInternalBoxItemMapper;
    @Autowired
    private StockinShelveTaskMapper shelveTaskMapper;
    @Inject
    StockinShelveTaskItemMapper stockinShelveTaskItemMapper;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockinOrderLogService stockinOrderLogService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockService stockService;
    @Autowired
    private StockinShelveTaskService shelveTaskService;
    @Autowired
    private StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockinShelveLogService stockinShelveLogService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinShelveTaskOpService shelveTaskOpService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    StockinQcService stockinQcService;
    @Autowired
    ApplicationContext context;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinWholeBoxUpShelveService stockinWholeBoxUpShelveService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockinShelveTaskUpdateService stockinShelveTaskUpdateService;
    @Autowired
    StockinShelveTaskOpService stockinShelveTaskOpService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockCenterCommonService stockCenterCommonService;

    private static final int IS_CEPTION_SHELVE = 1;

    /**
     * 普通上架
     */
    @Transactional
    @JLock(keyConstant = "upShelve", lockKey = "#request.internalBoxCode + '-' + #request.sku")
    public UpShelvesResponse upShelve(UpShelveRequest request) {
        UpShelvesResponse upShelvesResponse = new UpShelvesResponse();
        shelveTaskService.checkShelvePosition(request.getPosition());
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(request.getSku());
        if (productSpecInfoEntity == null || productSpecInfoEntity.getSpecId() == null)
            throw new BusinessServiceException("找不到商品信息");
        List<StockinShelveTaskItemInfo> allItemList = stockinShelveTaskItemMapper.searchItemListByInternalBoxCodeAndStatus(request.getInternalBoxCode(), Lists.newArrayList(StockinShelveTaskStatusEnum.PENDING.name(), StockinShelveTaskStatusEnum.SHELVING.name()));
        if (CollectionUtils.isEmpty(allItemList)) throw new BusinessServiceException("找不到可上架的上架任务明细");
        List<StockinShelveTaskItemInfo> filterSpecIdItemList = allItemList.stream().filter(m -> m.getSpecId().equals(productSpecInfoEntity.getSpecId())).collect(Collectors.toList());
        List<StockinShelveTaskItemInfo> taskItemEntityList = filterSpecIdItemList.stream().filter(item -> item.getStockinQty().compareTo(item.getReturnedQty() + item.getShelvedQty()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            return buildResponse(IS_CEPTION_SHELVE, "找不到上架任务明细 " + productSpecInfoEntity.getSku() + " 未上架的商品");
        }
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemService.getByInternalBoxCodeAndsku(request.getInternalBoxCode(), request.getSku());
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList)) {
            return buildResponse(IS_CEPTION_SHELVE, "内部箱不存在 " + request.getSku() + " 的商品");
        }
        List<StockInternalBoxItemEntity> collect = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() > 0 && !StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect))
            return buildResponse(IS_CEPTION_SHELVE, String.format("内部箱【%s】存在状态为【%s】的SKU，无法上架！", request.getInternalBoxCode(), enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), collect.get(0).getStatus())));
        StockinShelveSyncInfo syncInfo = buildUpdateStockShelveTaskItem(taskItemEntityList, request, stockInternalBoxItemEntityList);
        UpShelveRequest positionStockUpdateRequest = new UpShelveRequest();
        BeanUtilsEx.copyProperties(request, positionStockUpdateRequest);
        stockinShelveTaskUpdateService.shevleUpdatePositionStock(syncInfo, positionStockUpdateRequest, stockInternalBoxItemEntityList);
        skuCompleteShelveShelveLog(taskItemEntityList.get(0).getShelveTaskId(), productSpecInfoEntity.getSpecId());
        // 重新获取箱内总库存
        List<StockinShelveTaskItemEntity> itemList = stockinShelveTaskItemMapper.selectList(shelveTaskService.buildWrapperShelveTaskItemByTaskId(taskItemEntityList.get(0).getShelveTaskId()));
        int internalBoxAllStock = shelveTaskService.shelveTaskInternalBoxAllNum(itemList);
        upShelvesResponse.setAllStock(internalBoxAllStock);
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getByInternalBoxCodeAndStatus(request.getInternalBoxCode(), StockInternalBoxStatusEnum.SHELVING.name());
        if (stockInternalBoxEntity != null)
            upShelvesResponse.setInternalBoxStatus(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_STATUS.getName(), stockInternalBoxEntity.getStatus()));
        upShelvesResponse.setMessage("上架成功");
        //更新质检任务
        stockinQcService.upShelveUpdateQcTask(stockInternalBoxItemEntityList);
        // 更新erp库存，返接收数
        if (!CollectionUtils.isEmpty(syncInfo.getInfoItemList()) && (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(taskItemEntityList.get(0).getTaskType())
                || StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name().equals(taskItemEntityList.get(0).getTaskType())))
            pushShelveInfoToErp(syncInfo);
        try {
            // 入库单对应的入库任务是否收货完成 && 上架任务所有明细 入库数 <= 上架数 + 退货数 => 完成上架任务
            context.getBean(StockinShelveTaskOpService.class).checkTaskComplete(taskItemEntityList.get(0).getShelveTaskId());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return upShelvesResponse;
    }

    private void skuCompleteShelveShelveLog(Integer taskId, Integer specId) {
        List<StockinShelveTaskItemEntity> list = stockinShelveTaskItemService.getByShelveTaskIdAndSpecId(taskId, specId);
        List<StockinShelveTaskItemEntity> filterList = list.stream().filter(m -> m.getStockinQty().compareTo(m.getShelvedQty() + m.getReturnedQty()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            int stockinQty = list.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
            int returnQty = list.stream().mapToInt(StockinShelveTaskItemEntity::getReturnedQty).sum();
            int pendingQty = stockinQty - returnQty;
            String content = String.format("SKU【%s】 待上架数 %s件完成", list.get(0).getSku(), pendingQty);
            stockinShelveLogService.addShelveLog(StockinShelveEventEnum.UP_SHELVE_NUMBER, list.get(0).getShelveTaskId(), content);
        }
    }

    // 内部箱上架完成入库单日志记录
    public void internalBoxCompleteShelveStockinOrderLog(String internalBoxCode) {
        LOGGER.info("-------内部箱:【 {}】 上架完成记录入库单日志记录---------", internalBoxCode);
        List<StockinShelveTaskItemInfo> allItemList = stockinShelveTaskItemMapper.searchItemListByInternalBoxCodeAndStatus(internalBoxCode, Lists.newArrayList(StockinShelveTaskStatusEnum.SHELVED.name()));
        List<StockinOrderLogEntity> logEntityList = new ArrayList<>();
        Map<Integer, List<StockinShelveTaskItemInfo>> mapGroup = allItemList.stream().collect(Collectors.groupingBy(StockinShelveTaskItemInfo::getSourceId));
        mapGroup.forEach((stockinOrderId, itemList) -> {
            List<StockinShelveTaskItemInfo> filterLackItemList = itemList.stream().filter(item -> item.getStockinQty().compareTo(item.getShelvedQty() + item.getReturnedQty()) > 0).collect(Collectors.toList());
            int lackQty = 0;
            if (!CollectionUtils.isEmpty(filterLackItemList)) {
                Integer pendingQty = filterLackItemList.stream().mapToInt(StockinShelveTaskItemInfo::getStockinQty).sum();
                Integer shelvedQty = filterLackItemList.stream().mapToInt(StockinShelveTaskItemInfo::getShelvedQty).sum();
                Integer returnedQty = filterLackItemList.stream().mapToInt(StockinShelveTaskItemInfo::getReturnedQty).sum();
                lackQty = pendingQty - shelvedQty - returnedQty;
            }
            Integer totalPendingQty = itemList.stream().mapToInt(StockinShelveTaskItemInfo::getStockinQty).sum();
            Integer totalShelvedQty = itemList.stream().mapToInt(StockinShelveTaskItemInfo::getShelvedQty).sum();
            String content = String.format("内部箱号【%s】,上架完成,待上架 %s 件，实际上架 %s 件，缺货 %s 件", internalBoxCode, totalPendingQty, totalShelvedQty, lackQty);
            StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(stockinOrderId, StockinOrderLogTypeEnum.INTERNAL_BOX_COMPLETE_SHELVE.getName(), content);
            logEntityList.add(logEntity);
        });
        stockinOrderLogService.saveBatch(logEntityList);
    }

    // 更新上架任务明细状态
    void buildUpdateShelveTaskItemStatus(StockinShelveTaskItemEntity entity, Integer qty) {
        if (entity.getReturnedQty() != null) {
            if (entity.getShelvedQty() != null && entity.getStockinQty().compareTo(entity.getShelvedQty() + entity.getReturnedQty() + qty) <= 0) {
                entity.setStatus(StockInternalBoxStatusEnum.SHELVED.name());
                //上架明细完成更新入库单明细状态
                stockinOrderItemService.buildUpdateShelveTaskItemStatus(entity, qty);
            }
            if (entity.getShelvedQty() != null && entity.getStockinQty().compareTo(entity.getShelvedQty() + entity.getReturnedQty() + qty) > 0) {
                entity.setStatus(StockInternalBoxStatusEnum.SHELVING.name());
            }
        } else {
            if (entity.getShelvedQty() != null && entity.getStockinQty().compareTo(entity.getShelvedQty() + qty) <= 0) {
                entity.setStatus(StockInternalBoxStatusEnum.SHELVED.name());
                stockinOrderItemService.buildUpdateShelveTaskItemStatus(entity, qty);
            }
            if (entity.getShelvedQty() != null && entity.getStockinQty().compareTo(entity.getShelvedQty() + qty) > 0) {
                entity.setStatus(StockInternalBoxStatusEnum.SHELVING.name());
            }
        }
    }

    // 更新上架任务明细上架数(包含同一个sku,同一个上架任务，多条任务明细的情况)
    private StockinShelveSyncInfo buildUpdateStockShelveTaskItem(List<StockinShelveTaskItemInfo> taskItemEntityList, UpShelveRequest request, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        Integer shelvedQty = request.getQty();
        int size = taskItemEntityList.size();
        int sum = stockInternalBoxItemEntityList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
        if (shelvedQty > sum)
            throw new BusinessServiceException(String.format("箱内只有%s件，请确认", sum));
        int totalPendingQty = 0;
        List<StockinShelveSyncInfo.SyncInfoItem> syncInfoItemList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            StockinShelveTaskItemEntity item = new StockinShelveTaskItemEntity();
            BeanUtilsEx.copyProperties(taskItemEntityList.get(i), item);
            int pendingShelveQty = taskItemEntityList.get(i).getStockinQty() - item.getReturnedQty() - item.getShelvedQty();
            String positionCodeStr = buildShelvePosition(item.getShelvedQty(), item.getPositionCode(), request.getPosition());
            totalPendingQty += pendingShelveQty;
            StockinShelveSyncInfo.SyncInfoItem syncInfoItem = new StockinShelveSyncInfo.SyncInfoItem();
            BeanUtilsEx.copyProperties(taskItemEntityList.get(i), syncInfoItem);
            if (pendingShelveQty >= shelvedQty) {
                Integer oldShelvedQty = item.getShelvedQty();
                buildUpdateShelveTaskItemStatus(item, shelvedQty);
                syncInfoItem.setShelvedQty(shelvedQty);
                stockinShelveTaskItemService.changeShelveTaskItem(item, oldShelvedQty + shelvedQty, positionCodeStr);
                syncInfoItemList.add(syncInfoItem);
                break;
            } else {
                if (i == size - 1) {
                    Integer oldShelvedQty = item.getShelvedQty();
                    buildUpdateShelveTaskItemStatus(item, shelvedQty);
                    syncInfoItem.setShelvedQty(shelvedQty);
                    stockinShelveTaskItemService.changeShelveTaskItem(item, oldShelvedQty + shelvedQty, positionCodeStr);
                } else {
                    Integer totalShelveQty = item.getStockinQty() - item.getReturnedQty();
                    shelvedQty = shelvedQty - pendingShelveQty;
                    buildUpdateShelveTaskItemStatus(item, totalShelveQty);
                    syncInfoItem.setShelvedQty(totalShelveQty - item.getShelvedQty());
                    stockinShelveTaskItemService.changeShelveTaskItem(item, totalShelveQty, positionCodeStr);
                }
            }
            syncInfoItemList.add(syncInfoItem);
        }
        StockinShelveSyncInfo syncInfo = new StockinShelveSyncInfo();
        syncInfo.setPositionCode(request.getPosition());
        syncInfo.setInfoItemList(syncInfoItemList);
        if (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(taskItemEntityList.get(0).getTaskType())) {
            buildScanShelveStockinOrderLog(taskItemEntityList.get(0).getSourceId(), totalPendingQty, request);
        }
        return syncInfo;
    }

    // 上架库位，同一个sku
    private String buildShelvePosition(Integer shelveQty, String oldPositionCode, String positionCode) {
        if (!StringUtils.hasText(oldPositionCode)) {
            return positionCode;
        } else if (shelveQty == 0) {
            return positionCode;
        } else {
            Set<String> positionCodeSet = Arrays.stream(oldPositionCode.split(",")).map(String::trim).collect(Collectors.toSet());
            positionCodeSet.add(positionCode);
            return String.join(",", positionCodeSet);
        }
    }

    // 扫描上架入库单日志记录
    private void buildScanShelveStockinOrderLog(Integer sourceId, Integer pendingQty, UpShelveRequest request) {
        String content = String.format("内部箱号【%s】 SKU 【%s】,待上架 %s 件，上架到库位 【%s】 %s 件", request.getInternalBoxCode(), request.getSku(), pendingQty, request.getPosition(), request.getQty());
        stockinOrderLogService.addLog(sourceId, StockinOrderLogTypeEnum.SCAN_SHELVE.getName(), content);
    }

    private UpShelvesResponse buildResponse(int isExceptionShelve, String message) {
        UpShelvesResponse upShelvesResponse = new UpShelvesResponse();
        upShelvesResponse.setIsExceptionShelve(isExceptionShelve);
        upShelvesResponse.setMessage(message);
        return upShelvesResponse;
    }

    /**
     * 整箱上架
     */
    @Transactional
    public void wholeBoxUpShelve(WholeBoxUpShelveRequest request) {
        shelveTaskService.checkShelvePosition(request.getPosition());
        // 检验上架任务,修改状态
        StockinShelveTaskEntity shelveTaskEntity = checkShelveTask(request.getInternalBoxCode(), StockinShelveTaskStatusEnum.SHELVING.name(), StockinShelveTaskStatusEnum.SHELVED.name());
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemMapper.selectList(StockInternalBoxItemQueryWrapper.buildWrapperByInternalBoxCode(request.getInternalBoxCode()));
        wholeBoxUpShelveValidBoxItem(request, stockInternalBoxItemEntityList);
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxMapper.selectOne(StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(request.getInternalBoxCode()));
        if (stockInternalBoxEntity == null)
            throw new BusinessServiceException("找不到内部箱号: " + request.getInternalBoxCode());
        Integer pendingTotalQty = stockInternalBoxItemEntityList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
        if (!pendingTotalQty.equals(request.getQty()))
            throw new BusinessServiceException("内部箱数量不符，无法完成整箱装箱，请确定");

        // 检查库位是否被锁定
        for (StockInternalBoxItemEntity item : stockInternalBoxItemEntityList) {
            StockEntity stockEntity = stockService.getSkuPositionStock(item.getSku(), request.getPosition());
            if (stockEntity != null && stockEntity.getLock()) {
                throw new BusinessServiceException("该库位已被锁定，无法上架");
            }
        }

        // 箱子库存--  库位库存++
        Map<Integer, List<StockInternalBoxItemEntity>> mapGroupSpecId = stockInternalBoxItemEntityList.stream().collect(Collectors.groupingBy(StockInternalBoxItemEntity::getSpecId));
        List<Integer> itemIds = new LinkedList<>();
        mapGroupSpecId.forEach((specId, boxItemList) -> {
            boxItemList.forEach(item -> itemIds.add(item.getInternalBoxItemId()));
            buildStockUpdateByInternalBox(boxItemList);
            buildStockUpdateByPositionCode(boxItemList, request.getPosition());
        });
        stockInternalBoxItemService.removeByIds(itemIds);
        // 更新内部箱状态为:空箱
        LOGGER.info("{}，整箱上架更新内部箱 {} 为空箱", loginInfoService.getName(), stockInternalBoxEntity.getInternalBoxCode());
        stockInternalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.EMPTY.name());
        List<StockinShelveTaskItemEntity> shelveTaskItemEntityList = stockinShelveTaskItemMapper.selectList(StockinShelveTaskItemQueryWrapper.wrapperTaskItemByTaskId(shelveTaskEntity.getShelveTaskId()));
        List<StockinShelveTaskItemEntity> feedBackSkuList = new ArrayList<>();
        shelveTaskItemEntityList.forEach(item -> {
            addFeedBackSkuList(feedBackSkuList, item);
            StockinBuilding.setShelveTaskItem(item, request.getPosition(), loginInfoService.getName());
            stockinOrderItemService.buildUpdateShelveTaskItemStatus(item, 0);
        });
        stockinShelveTaskItemService.updateBatchById(shelveTaskItemEntityList);
        // 上架信息同步到erp，回填接收数，出库单计划单状态
        if (!CollectionUtils.isEmpty(feedBackSkuList) && (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(shelveTaskEntity.getTaskType())
                || StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name().equals(shelveTaskEntity.getTaskType())))
            pushShelveInfoToErp(StockinBuilding.buildShelveSyncInfo(request.getPosition(), feedBackSkuList));
        if (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(shelveTaskEntity.getTaskType())) {
            buildFullBoxCompleteShelveStockinOrderLog(shelveTaskEntity.getShelveTaskId(), request.getInternalBoxCode(), request.getPosition());
            //过滤出入库任务未完成的
            List<StockinShelveTaskItemEntity> filterShelveTaskItemEntityList = stockinWholeBoxUpShelveService.filterStockinOrderTaskReceived(shelveTaskItemEntityList);
            if (!filterShelveTaskItemEntityList.isEmpty()) {
                // 整箱上架完成回写入库单状态
                shelveTaskService.shelveCompleteChangStockinOrderStatus(filterShelveTaskItemEntityList);
            }
            //活动仓到货通知业务
            shelveTaskOpService.activitySpaceNoticeUser(shelveTaskItemEntityList);
            // this.notifyProcess(StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name(), shelveTaskItemEntityList, Collections.emptyMap());
        } else if (StockinShelveTaskTypeEnum.TRANSFER_SHELVE.name().equals(shelveTaskEntity.getTaskType())) {
            // 更新内部箱调拨任务及明细状态->已完成
            shelveTaskService.shelveCompleteChangeTransferBoxTask(shelveTaskItemEntityList);
        } else if (StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name().equals(shelveTaskEntity.getTaskType())) {
            List<StockinShelveTaskItemEntity> filterShelveTaskItemEntityList = stockinWholeBoxUpShelveService.filterStockinOrderTaskReceived(shelveTaskItemEntityList);
            if (!filterShelveTaskItemEntityList.isEmpty())
                shelveTaskService.shelveCompleteChangSpaceCrossStockinOrderStatus(shelveTaskItemEntityList);
        }
        //更新质检任务
        stockinQcService.upShelveUpdateQcTask(stockInternalBoxItemEntityList);
    }

    private void wholeBoxUpShelveValidBoxItem(WholeBoxUpShelveRequest request, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList)) {
            LOGGER.error("根据内部箱号: {} 查询内部箱明细为空", request.getInternalBoxCode());
            throw new BusinessServiceException("内部箱数量不符，无法完成整箱装箱，请确定");
        }
        List<StockInternalBoxItemEntity> collect = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() > 0 && !StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            String format = String.format("内部箱【%s】存在状态为【%s】的SKU，无法完成整箱装箱，请确定", request.getInternalBoxCode(), enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), collect.get(0).getStatus()));
            LOGGER.error(format);
            throw new BusinessServiceException(format);
        }
    }

    private void addFeedBackSkuList(List<StockinShelveTaskItemEntity> feedBackSkuList, StockinShelveTaskItemEntity item) {
        if (item.getShelvedQty() + item.getReturnedQty() < item.getStockinQty()) {
            StockinShelveTaskItemEntity shelveTaskItemEntity = new StockinShelveTaskItemEntity();
            BeanUtilsEx.copyProperties(item, shelveTaskItemEntity);
            shelveTaskItemEntity.setShelvedQty(item.getStockinQty() - item.getShelvedQty() - item.getReturnedQty());
            feedBackSkuList.add(shelveTaskItemEntity);
        }
    }

    private StockinShelveTaskEntity checkShelveTask(String internalBoxCode, String oldStatus, String newStatus) {
        StockinShelveTaskEntity shelveTaskEntity = shelveTaskMapper.selectOne(shelveTaskService.wrapperTaskByInternalBoxCodeAndStatus(internalBoxCode, oldStatus));
        if (Objects.isNull(shelveTaskEntity)) {
            LOGGER.error("找不到内部箱：【 {}】的 {}上架任务", internalBoxCode, enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_SHELVE_TASK_STATUS.getName(), oldStatus));
            throw new BusinessServiceException(String.format("找不到内部箱：【%s】的【%s】上架任务", internalBoxCode, enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_SHELVE_TASK_STATUS.getName(), oldStatus)));
        }
        shelveTaskService.changeStockinShelveTaskStatus(shelveTaskEntity, newStatus);
        return shelveTaskEntity;
    }

    private void buildStockUpdateByInternalBox(List<StockInternalBoxItemEntity> internalBoxItemList) {
        for (StockInternalBoxItemEntity item : internalBoxItemList) {
            StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
            BeanUtilsEx.copyProperties(item, stockUpdateRequest);
            stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
            stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
            stockUpdateRequest.setStockInOrderNo(item.getStockInOrderNo());
            stockUpdateRequest.setTakeTaskId(item.getTransferInternalBoxTaskId());
            stockUpdateRequest.setPurchasePlanNo(item.getPurchasePlanNo());
            stockUpdateRequest.setQty(-item.getQty());
            stockService.updateStock(stockUpdateRequest);
        }
    }

    private void buildStockUpdateByPositionCode(List<StockInternalBoxItemEntity> internalBoxItemList, String positionCode) {
        for (StockInternalBoxItemEntity item : internalBoxItemList) {
            StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
            BeanUtilsEx.copyProperties(item, stockUpdateRequest, "internalBoxId", "internalBoxCode");
            stockUpdateRequest.setPositionCode(positionCode);
            stockUpdateRequest.setPurchasePlanNo(item.getPurchasePlanNo());
            stockUpdateRequest.setStockInOrderNo(item.getStockInOrderNo());
            stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
            stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
            stockService.updateStock(stockUpdateRequest);
        }
    }

    // 满箱上架完成入库单日志记录
    private void buildFullBoxCompleteShelveStockinOrderLog(Integer shelveTaskId, String internalBoxCode, String positionCode) {
        List<StockinShelveTaskItemEntity> shelveTaskItemshelvedList = stockinShelveTaskItemService.getByShelveTaskId(shelveTaskId);
        List<StockinShelveTaskItemEntity> filterShelveTaskItemshelvedList = shelveTaskItemshelvedList.stream().filter(item -> !item.getStockinQty().equals(item.getShelvedQty() + item.getReturnedQty())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(shelveTaskItemshelvedList) && CollectionUtils.isEmpty(filterShelveTaskItemshelvedList)) {
            Integer pendingQty = shelveTaskItemshelvedList.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
            Integer shelvedQty = shelveTaskItemshelvedList.stream().mapToInt(StockinShelveTaskItemEntity::getShelvedQty).sum();
            String content = String.format("内部箱号【%s】 满箱上架,待上架 %s 件，实际上架 %s 件，上架库位 【%s】", internalBoxCode, pendingQty, shelvedQty, positionCode);
            stockinOrderLogService.addLog(shelveTaskItemshelvedList.get(0).getSourceId(), StockinOrderLogTypeEnum.WHOLE_BOX_SHELVE.getName(), content);
        }
    }

    /**
     * 异常上架
     */
    @Transactional
    public void upShelveByException(UpShelveByExceptionRequest request) {
    /*    UpShelveRequest upShelveRequest = new UpShelveRequest();
        BeanUtilsEx.copyProperties(request, upShelveRequest);
        upShelveRequest.setQty(request.getQty());*/
        LOGGER.error("关闭异常上架: {} , {} ", request.getInternalBoxCode(), request.getSku());
        throw new BusinessServiceException("暂不允许异常上架，请确认上架任务，若商品多余，请使用盘点。");
  /*      ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(request.getSku());
        if (productSpecInfoEntity == null || productSpecInfoEntity.getSpecId() == null)
            throw new BusinessServiceException("不存在此条码的商品信息");
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxMapper.selectOne(StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(request.getInternalBoxCode()));
        if (stockInternalBoxEntity == null) {
            LOGGER.error("找不到内部箱号: {} ", request.getInternalBoxCode());
            throw new BusinessServiceException("找不到内部箱: " + request.getInternalBoxCode());
        }*/

/*        StockEntity stockEntity = stockService.getBySpecIdAndInternalBoxIdAndPositionIdIsNull(productSpecInfoEntity.getSpecId(), stockInternalBoxEntity.getInternalBoxId());
        stockinShelveTaskUpdateService.shevleUpdatePositionStock(upShelveRequest, null); // 1.更新stock库位库存
        if (stockEntity != null) { // 2.更新stock中内部箱库存
            int changeQty = request.getQty();
            if (stockEntity.getStock().compareTo(request.getQty()) < 0) {
                changeQty = stockEntity.getStock();
            }
            stockService.updateStock(buildUpdateInternalBoxStock(upShelveRequest, changeQty));
        }
        buildExceptionShelveStockinOrderLog(request.getInternalBoxCode(), request.getSku(), request.getPosition(), request.getQty());*/
        //stockPDATakeStockTaskService.exceptionShelveSyncToErp(exceptionPosition, request);
    }

    /**
     * 批量确认缺货
     */
    @Transactional
    public void upShelveByLackConfirmBatch(StockinUpShelveByLackBatchRequest request) {
        for (String internalBoxCode : request.getInternalBoxCodeList()) {
            context.getBean(this.getClass()).upShelveByLackConfirm(internalBoxCode, Boolean.TRUE);
        }
    }

    /**
     * 确认缺货
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void upShelveByLackConfirm(String internalBoxCode, Boolean isBatch) {
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemMapper.searchByInternalBoxCodeAndStatus(internalBoxCode, null);
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList))
            throw new BusinessServiceException("找不到对应内部箱明细记录");
        lackConfirmValidBoxItem(internalBoxCode, stockInternalBoxItemEntityList);
        StockinShelveTaskEntity stockinShelveTaskEntity = shelveTaskMapper.selectOne(shelveTaskService.wrapperTaskByInternalBoxCodeAndStatus(internalBoxCode, null).ne(StockinShelveTaskEntity::getStatus, StockinShelveTaskStatusEnum.SHELVED.name()));
        if (stockinShelveTaskEntity == null) throw new BusinessServiceException("找不到对应上架任务记录");
        if (StockinShelveTaskStatusEnum.SHELVED.name().equals(stockinShelveTaskEntity.getStatus()))
            throw new BusinessServiceException("上架任务已完成！");
        List<StockInternalBoxItemEntity> filterList = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() != null || item.getQty() != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) throw new BusinessServiceException("找不到对应内部箱明细记录");
        //更新内部箱库存和异常库位库存
        stockInternalBoxItemService.updateStock(filterList);
        List<Integer> specIdList = filterList.stream().map(StockInternalBoxItemEntity::getSpecId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<StockinShelveTaskItemEntity> queryWrapper = StockinShelveTaskItemQueryWrapper.wrapperTaskItemByTaskIdAndSpecIdList(stockinShelveTaskEntity.getShelveTaskId(), specIdList);
        List<StockinShelveTaskItemEntity> stockinShelveTaskItemEntityList = stockinShelveTaskItemMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(stockinShelveTaskItemEntityList))
            throw new BusinessServiceException("找不到上架任务明细");
        List<StockinShelveTaskItemEntity> stockinShelveTaskItemEntities = stockinShelveTaskItemEntityList.stream().map(item -> {
            StockinShelveTaskItemEntity stockinShelveTaskItemEntity = new StockinShelveTaskItemEntity();
            stockinShelveTaskItemEntity.setStatus(StockInternalBoxStatusEnum.SHELVED.name());
            stockinShelveTaskItemEntity.setUpdateBy(loginInfoService.getName());
            if (!isBatch && !StringUtils.hasText(item.getOperator())) {
                stockinShelveTaskItemEntity.setOperator(loginInfoService.getName());
                stockinShelveTaskItemEntity.setOperatorDate(new Date());
            }
            stockinShelveTaskItemEntity.setShelveTaskItemId(item.getShelveTaskItemId());
            return stockinShelveTaskItemEntity;
        }).collect(Collectors.toList());
        stockinShelveTaskItemService.updateBatchById(stockinShelveTaskItemEntities);
        LOGGER.info("{},确认缺货更新内部箱  {} 为空箱", loginInfoService.getName(), internalBoxCode);
        stockInternalBoxService.changeStockInternalBoxStatus(internalBoxCode, StockInternalBoxStatusEnum.EMPTY.name());
        shelveTaskService.changeStockinShelveTaskStatus(stockinShelveTaskEntity, StockInternalBoxStatusEnum.SHELVED.name());
        // 缺货记录入库单日志
        List<StockinShelveTaskItemEntity> shelveTaskItemList = stockinShelveTaskItemService.getByShelveTaskId(stockinShelveTaskEntity.getShelveTaskId());
        if (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            stockinOrderLogService.buildShelveLackStockinOrderLog(internalBoxCode, shelveTaskItemList);
            // 同步erp （缺货数不用返回接收单）
            // pushShelveInfoToErp(buildShelveSyncInfo(filterList.get(0).getSpaceId(), stockinShelveTaskItemEntityList));
            // 确认缺货上架完成回写入库单状态
            shelveTaskService.shelveCompleteChangStockinOrderStatus(shelveTaskItemList);
            //活动仓到货通知业务
            shelveTaskOpService.activitySpaceNoticeUser(shelveTaskItemList);
        } else if (StockinShelveTaskTypeEnum.TRANSFER_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            // 更新内部箱调拨任务及明细状态->已完成
            shelveTaskService.shelveCompleteChangeTransferBoxTask(shelveTaskItemList);
        } else if (StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            shelveTaskService.shelveCompleteChangSpaceCrossStockinOrderStatus(shelveTaskItemList);
        }
        // 缺货上架任务日志
        lackConfirmAddShelveLog(stockinShelveTaskEntity.getShelveTaskId(), internalBoxCode, shelveTaskItemList);
    }

    private void lackConfirmValidBoxItem(String internalBoxCode, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        List<StockInternalBoxItemEntity> collect = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() > 0 && !StockinOrderItemStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            String format = String.format("内部箱【%s】存在状态为【%s】的SKU，无法确认缺货，请确定", internalBoxCode, enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), collect.get(0).getStatus()));
            LOGGER.error(format);
            throw new BusinessServiceException(format);
        }
    }

    // 确认缺货上架日志
    private void lackConfirmAddShelveLog(Integer shelveTaskId, String internalBoxCode, List<StockinShelveTaskItemEntity> shelveTaskItemList) {
        int totalStockinQty = shelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
        int totalShelveQty = shelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getShelvedQty).sum();
        List<StockinShelveTaskItemEntity> filterShelveTaskItemList = shelveTaskItemList.stream().filter(m ->
                m.getStockinQty().compareTo(m.getShelvedQty() + m.getReturnedQty()) > 0).collect(Collectors.toList());
        int lackQty = 0;
        if (!CollectionUtils.isEmpty(filterShelveTaskItemList)) {
            int lackStockinQty = filterShelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
            int lackShelveQty = filterShelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getShelvedQty).sum();
            int lackReturnQty = filterShelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getReturnedQty).sum();
            lackQty = lackStockinQty - lackShelveQty - lackReturnQty;
        }
        String content = String.format("上架任务完成，待上架 %s件，实际上架 %s件，缺货 %s件，内部箱号 %s释放", totalStockinQty, totalShelveQty, lackQty, internalBoxCode);
        stockinShelveLogService.addShelveLog(StockinShelveEventEnum.COMPLETE_SHELVE_TASK, shelveTaskId, content);
        stockinShelveTaskOpService.sendReplenishOrderMessage(shelveTaskId, TenantContext.getTenant());
    }

    // 更新stock异常库位库存
    public StockUpdateRequest buildUpdateExceptionStock(UpShelveRequest request, int exceptionQty) {
        StockUpdateRequest positionStockUpdateRequest = new StockUpdateRequest();
        positionStockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.SHELVE_EXCEPTION_LEVELLING);
        positionStockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
        positionStockUpdateRequest.setQty(exceptionQty);
        positionStockUpdateRequest.setSku(request.getSku());
        positionStockUpdateRequest.setPositionCode(request.getPosition());
        return positionStockUpdateRequest;
    }

    /**
     * 更新stock 库存中内部箱库存,更新内部箱明细sku数
     */
    public StockUpdateRequest buildUpdateInternalBoxStock(UpShelveRequest request, int changeQty) {
        StockUpdateRequest internalBoxStockUpdateRequest = new StockUpdateRequest();
        internalBoxStockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
        internalBoxStockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
        internalBoxStockUpdateRequest.setQty(-changeQty);
        internalBoxStockUpdateRequest.setSku(request.getSku());
        internalBoxStockUpdateRequest.setInternalBoxCode(request.getInternalBoxCode());
        return internalBoxStockUpdateRequest;
    }

    /**
     * 完成上架任务--更新上架任务/内部箱状态
     */
    @Transactional
    public UpShelvesResponse changeCompleteShelveTask(String internalBoxCode) {
        UpShelvesResponse upShelvesResponse = new UpShelvesResponse();
        StockinShelveTaskEntity stockinShelveTaskEntity = shelveTaskMapper.selectOne(shelveTaskService.wrapperTaskByInternalBoxCodeAndStatus(internalBoxCode, StockinShelveTaskStatusEnum.SHELVING.name()));
        if (stockinShelveTaskEntity == null)
            throw new BusinessServiceException(String.format("内部箱号【%s】 对应未完成的上架任务未找到!", internalBoxCode));
        if (StockinShelveTaskStatusEnum.SHELVED.name().equals(stockinShelveTaskEntity.getStatus()))
            throw new BusinessServiceException(String.format("内部箱号【%s】 上架任务已完成，请不要重复操作!", internalBoxCode));
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxMapper.selectOne(StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode));
        if (stockInternalBoxEntity == null) {
            LOGGER.error("resource internalBoxCode={} find stockInternalBoxEntity not found", internalBoxCode);
            throw new BusinessServiceException(String.format("内部箱号【%s】 未找到!", internalBoxCode));
        }
        List<StockinShelveTaskItemEntity> shelveTaskItemEntityList = stockinShelveTaskItemMapper.selectList(StockinShelveTaskItemQueryWrapper.wrapperTaskItemByTaskId(stockinShelveTaskEntity.getShelveTaskId()));
        if (CollectionUtils.isEmpty(shelveTaskItemEntityList)) {
            LOGGER.error("resource internalBoxCode={} find shelveTaskItemEntityList not found", internalBoxCode);
            throw new BusinessServiceException("上架任务明细记录未找到!");
        }
        List<StockinShelveTaskItemEntity> filterShelveTaskItemList = shelveTaskItemEntityList.stream().filter(item ->
                item.getStockinQty().compareTo(item.getShelvedQty() + item.getReturnedQty()) > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterShelveTaskItemList)) {
            upShelvesResponse.setMessage("有商品缺货，请确定该内部箱已经全部上架完成!");
            upShelvesResponse.setIsExceptionShelve(1);
            return upShelvesResponse;
        }
        stockinShelveTaskItemService.batchChangeShelveTaskItemStatus(shelveTaskItemEntityList, StockinShelveTaskStatusEnum.SHELVED.name());
        // 内部箱上架完成日志
        if (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            internalBoxCompleteShelveStockinOrderLog(internalBoxCode);
        }
        shelveTaskService.changeStockinShelveTaskStatus(stockinShelveTaskEntity, StockinShelveTaskStatusEnum.SHELVED.name());
        LOGGER.info(" {},完成上架任务changeCompleteShelveTask更新内部箱 {},为空箱", loginInfoService.getName(), stockInternalBoxEntity.getInternalBoxCode());
        stockInternalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.EMPTY.name());
        // 若是入库上架,更新入库单状态
        if (StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            shelveCompleteChangeStockinOrderLog(stockinShelveTaskEntity, shelveTaskItemEntityList);
            // 回写入库单状态
            shelveTaskService.shelveCompleteChangStockinOrderStatus(shelveTaskItemEntityList);
            //活动仓到货通知业务
            shelveTaskOpService.activitySpaceNoticeUser(shelveTaskItemEntityList);
        }
        // 若是调拨上架,更新内部箱调拨任务状态
        if (StockinShelveTaskTypeEnum.TRANSFER_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            shelveTaskService.shelveCompleteChangeTransferBoxTask(shelveTaskItemEntityList);
        } else if (StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType()))
            shelveTaskService.shelveCompleteChangSpaceCrossStockinOrderStatus(shelveTaskItemEntityList);
        int stockinQty = shelveTaskItemEntityList.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
        int returnedQty = shelveTaskItemEntityList.stream().mapToInt(StockinShelveTaskItemEntity::getReturnedQty).sum();
        int shelvedQty = shelveTaskItemEntityList.stream().mapToInt(StockinShelveTaskItemEntity::getShelvedQty).sum();
        int pendingQty = stockinQty - returnedQty;
        String content = String.format("上架任务完成，待上架:%s件,实际上架:%s件,内部箱箱号:%s 释放", pendingQty, shelvedQty, internalBoxCode);
        stockinShelveLogService.addShelveLog(StockinShelveEventEnum.COMPLETE_SHELVE_TASK, stockinShelveTaskEntity.getShelveTaskId(), content);
        //判断是否创建补货单
        stockinShelveTaskOpService.sendReplenishOrderMessage(stockinShelveTaskEntity.getShelveTaskId(), stockinShelveTaskEntity.getLocation());
        upShelvesResponse.setMessage("内部箱上架完成");
        //通知加工任务
        //this.notifyProcess(stockinShelveTaskEntity.getTaskType(), shelveTaskItemEntityList, Collections.emptyMap());
        return upShelvesResponse;
    }

    // 边上架边同步到商通，库存、接收数(入库上架才同步)
    private void pushShelveInfoToErp(StockinShelveSyncInfo syncInfo) {
        if (CollectionUtils.isEmpty(syncInfo.getInfoItemList()))
            throw new BusinessServiceException("上架明细为空,推商通失败");
        List<Integer> shelveTaskItemIdList = syncInfo.getInfoItemList().stream().map(StockinShelveSyncInfo.SyncInfoItem::getShelveTaskItemId).collect(Collectors.toList());
        List<StockinShelveTaskItemEntity> shelveTaskItemEntityList = stockinShelveTaskItemService.listByIds(shelveTaskItemIdList);
        List<Integer> shelveTaskIdList = shelveTaskItemEntityList.stream().map(StockinShelveTaskItemEntity::getShelveTaskId).distinct().collect(Collectors.toList());
        List<StockinShelveTaskEntity> shelveTaskEntityList = shelveTaskService.listByIds(shelveTaskIdList);
        List<Integer> stockinOrderIdList = shelveTaskItemEntityList.stream().map(StockinShelveTaskItemEntity::getSourceId).distinct().collect(Collectors.toList());
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.listByIds(stockinOrderIdList);
        Map<String, List<StockinOrderItemEntity>> orderItemEntityMap = stockinOrderItemService.findAllByStockinOrderIdList(stockinOrderIdList).stream()
                .collect(Collectors.groupingBy(t -> String.format("%s##%s##%s##%s", t.getStockinOrderId(), t.getPurchasePlanNo(), t.getSku(), t.getInternalBoxCode())));
        LOGGER.info(JsonMapper.toJson(orderItemEntityMap));
        shelveTaskItemEntityList.stream().collect(Collectors.groupingBy(StockinShelveTaskItemEntity::getSourceId))
                .forEach((stockinOrderId, list) -> {
                    StockinShelveTaskEntity shelveTaskEntity = shelveTaskEntityList.stream().filter(t -> t.getShelveTaskId().equals(list.get(0).getShelveTaskId())).findFirst().orElseThrow(() -> new BusinessServiceException("找不到上架任务"));
                    StockinOrderEntity stockinOrderEntity = stockinOrderEntityList.stream().filter(t -> t.getStockinOrderId().equals(stockinOrderId)).findFirst().orElseThrow(() -> new BusinessServiceException("找不到入库单"));
                    List<ErpFeedbackReceivedRequest.ErpFeedbackReceivedItem> erpFeedbackReceivedItemList = list.stream().map(taskItem -> {
                        StockinShelveSyncInfo.SyncInfoItem syncInfoItem = syncInfo.getInfoItemList().stream().filter(t -> t.getShelveTaskItemId().equals(taskItem.getShelveTaskItemId())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("【%s】入库任务明细不能存在", taskItem.getShelveTaskItemId())));
                        StockinOrderItemEntity stockinOrderItemEntity = orderItemEntityMap.getOrDefault(String.format("%s##%s##%s##%s", stockinOrderId, taskItem.getPurchasePlanNo(), taskItem.getSku(), shelveTaskEntity.getInternalBoxCode()), Lists.newArrayList()).stream().min(Comparator.comparing(StockinOrderItemEntity::getCreateDate)).orElse(new StockinOrderItemEntity());
                        LOGGER.info(JsonMapper.toJson(stockinOrderItemEntity));
                        ErpFeedbackReceivedRequest.ErpFeedbackReceivedItem erpFeedbackReceivedItem = new ErpFeedbackReceivedRequest.ErpFeedbackReceivedItem();
                        erpFeedbackReceivedItem.setSku(taskItem.getSku());
                        erpFeedbackReceivedItem.setPurchasePlanNo(taskItem.getPurchasePlanNo());
                        erpFeedbackReceivedItem.setShelvedQty(syncInfoItem.getShelvedQty());
                        erpFeedbackReceivedItem.setReceiveDate(stockinOrderItemEntity.getCreateDate());
                        erpFeedbackReceivedItem.setStockinOrderItemId(stockinOrderItemEntity.getStockinOrderItemId());
                        return erpFeedbackReceivedItem;
                    }).collect(Collectors.toList());
                    ErpFeedbackReceivedRequest request = new ErpFeedbackReceivedRequest();
                    request.setOperator(loginInfoService.getName());
                    request.setLocation(TenantContext.getTenant());
                    request.setSupplierDeliveryBoxCode(stockinOrderEntity.getSupplierDeliveryBoxCode());
                    request.setFeedbackReceivedItemList(erpFeedbackReceivedItemList);
                    request.setPositionCode(stockCenterCommonService.changePositionCode(syncInfo.getPositionCode()));
                    request.setIsAllot(shelveTaskEntity.getTaskType().equals(StockinShelveTaskTypeEnum.SPACE_TRANSFER_SHELVE.name()) ? 1 : 0);
                    erpApiService.feedbackReceived(request, syncInfo.getPositionCode(), stockinOrderEntity.getStockinType());
                    //回填给入库单明细
                    pushStockinOrderItem(erpFeedbackReceivedItemList, stockinOrderId);
                });
    }

    private void pushStockinOrderItem(List<ErpFeedbackReceivedRequest.ErpFeedbackReceivedItem> erpFeedbackReceivedItemList, Integer stockinOrderId) {
        StockinOrderItemShelvedQty stockinOrderItemShelvedQty = new StockinOrderItemShelvedQty();
        List<StockinOrderItemShelvedQty.ShelveInfoItem> collect = erpFeedbackReceivedItemList.stream().map(item -> new StockinOrderItemShelvedQty.ShelveInfoItem(item.getStockinOrderItemId(), item.getShelvedQty())).collect(Collectors.toList());
        stockinOrderItemShelvedQty.setShelveInfoItemList(collect);
        stockinOrderItemShelvedQty.setStockinOrderId(stockinOrderId);
        //messageProducer.sendMessage(KafkaConstant.STOCKIN_SHELVED_UPDATE_ORDER_TOPIC_NAME, KafkaConstant.STOCKIN_SHELVED_UPDATE_ORDER_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockinOrderItemShelvedQty));
        //由于回填包装数需要这一步先执行，改为同步
        stockinOrderItemService.shelvedUpdateOrderItem(stockinOrderItemShelvedQty);
    }

    // 构造整箱上架同步erp request
    public void shelveCompleteChangeStockinOrderLog(StockinShelveTaskEntity stockinShelveTaskEntity, List<StockinShelveTaskItemEntity> shelveTaskItemEntityList) {
        List<Integer> stockinOrderIdList = shelveTaskItemEntityList.stream().map(StockinShelveTaskItemEntity::getSourceId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockinOrderIdList))
            throw new BusinessServiceException("上架任务完成时，上架任务明细找不到入库单id信息");
        List<StockinOrderLogEntity> stockinOrderLogEntityList = new ArrayList<>();
        stockinOrderIdList.forEach(stockinOrderId -> {
            String content = String.format("内部箱【%s】上架任务完成", stockinShelveTaskEntity.getInternalBoxCode());
            StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(stockinOrderId, StockinOrderLogTypeEnum.COMPLETE_SHELVE_TASK.getName(), content);
            stockinOrderLogEntityList.add(logEntity);
        });
        // 记录入库单日志
        if (!CollectionUtils.isEmpty(stockinOrderLogEntityList)) {
            stockinOrderLogService.saveBatch(stockinOrderLogEntityList);
        }
    }
}
