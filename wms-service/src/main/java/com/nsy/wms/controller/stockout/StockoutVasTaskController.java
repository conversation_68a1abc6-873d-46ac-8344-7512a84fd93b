package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.request.stockout.StockoutVasTaskItemListRequest;
import com.nsy.api.wms.request.stockout.StockoutVasTaskListRequest;
import com.nsy.api.wms.request.stockout.StockoutVasTaskLogListRequest;
import com.nsy.api.wms.request.stockout.StockoutVasTaskOpRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskCountResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskDetailResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskItemListResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskListResponse;
import com.nsy.api.wms.response.stockout.StockoutVasTaskLogListResponse;
import com.nsy.wms.business.service.stockout.StockoutVasTaskItemService;
import com.nsy.wms.business.service.stockout.StockoutVasTaskLogService;
import com.nsy.wms.business.service.stockout.StockoutVasTaskService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "增值任务接口")
public class StockoutVasTaskController extends BaseController {

    @Autowired
    StockoutVasTaskService taskService;
    @Autowired
    StockoutVasTaskItemService taskItemService;
    @Autowired
    StockoutVasTaskLogService taskLogService;

    @ApiOperation(value = "增值任务列表", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutVasTaskListResponse> getList(@RequestBody StockoutVasTaskListRequest request) {
        return taskService.getListByRequest(request);
    }

    @ApiOperation(value = "增值任务详情", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/{taskId}", method = RequestMethod.GET)
    @ResponseBody
    public StockoutVasTaskDetailResponse getDetailByTaskId(@PathVariable Integer taskId) {
        return taskService.getDetailById(taskId);
    }

    @ApiOperation(value = "增值任务明细列表", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/item-list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutVasTaskItemListResponse> getItemList(@RequestBody StockoutVasTaskItemListRequest request) {
        return taskItemService.getListByRequest(request);
    }

    @ApiOperation(value = "增值任务日志列表", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/log-list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutVasTaskLogListResponse> getLogList(@RequestBody StockoutVasTaskLogListRequest request) {
        return taskLogService.getListByRequest(request);
    }

    @ApiOperation(value = "增值任务修改类型", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/vas-type", method = RequestMethod.POST)
    public void updateVasType(@RequestBody StockoutVasTaskOpRequest request) {
        taskService.updateVasType(request);
    }

    @ApiOperation(value = "增值完成", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/finish-vas", method = RequestMethod.POST)
    public void finishVas(@RequestBody StockoutVasTaskOpRequest request) {
        taskService.finishVas(request);
    }
    
    @ApiOperation(value = "增值任务统计", produces = "application/json")
    @RequestMapping(value = "/stockout-vas-task/qty/statistics", method = RequestMethod.POST)
    public StockoutVasTaskCountResponse vatTaskStatistics(@RequestBody StockoutVasTaskListRequest request) {
        return taskService.vatTaskStatistics(request);
    }
}
