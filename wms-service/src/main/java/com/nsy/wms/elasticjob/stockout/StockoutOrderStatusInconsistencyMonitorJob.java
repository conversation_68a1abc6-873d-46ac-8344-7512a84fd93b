package com.nsy.wms.elasticjob.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.erp.ErpOrderStateNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpGetTradeStatusRequest;
import com.nsy.wms.business.manage.erp.response.ErpGetTradeStatusResponse;
import com.nsy.wms.business.manage.notify.NotifyApiService;
import com.nsy.wms.business.manage.notify.request.DingTalkRobotWebhookMarkdownSendRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderItemMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 出库单状态不一致监控
 * 监控WMS状态为"获取异常"但ERP状态为"待发货"的出库单
 */
@Component
public class StockoutOrderStatusInconsistencyMonitorJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderStatusInconsistencyMonitorJob.class);

    public static final String JOB_NAME = "StockoutOrderStatusInconsistencyMonitorJob";

    // 支持的地区列表
    private static final List<String> LOCATIONS = Arrays.asList(
            "QUANZHOU", "XIAMEN", "GUANGZHOU", "TAILI", "MISI", "WEIYUE"
    );

    @Autowired
    private StockoutOrderService stockoutOrderService;

    @Autowired
    private StockoutOrderItemMapper stockoutOrderItemMapper;

    @Autowired
    private ErpApiService erpApiService;

    @Autowired
    private NotifyApiService notifyApiService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockoutOrderStatusInconsistencyMonitorJob start");
        LoginInfoService.setName(JOB_NAME);

        try {
            // 存储所有地区的不一致订单信息
            Map<String, List<InconsistentOrderInfo>> locationInconsistentOrders = new HashMap<>();

            LOGGER.info("将处理所有地区: {}", String.join(", ", LOCATIONS));

            // 循环处理每个地区
            for (String location : LOCATIONS) {
                List<InconsistentOrderInfo> inconsistentOrders = processLocation(location);

                if (!CollectionUtils.isEmpty(inconsistentOrders)) {
                    locationInconsistentOrders.put(location, inconsistentOrders);
                }
            }

            // 如果有不一致数据，发送合并通知
            if (!locationInconsistentOrders.isEmpty()) {
                sendDingDingNotification(locationInconsistentOrders);
            } else {
                LOGGER.info("未找到任何地区的状态不一致数据");
            }
        } catch (Exception e) {
            LOGGER.error("出库单状态不一致监控Job发生异常: {}", e.getMessage(), e);
        } finally {
            LoginInfoService.removeName();
            LOGGER.info("StockoutOrderStatusInconsistencyMonitorJob end");
        }
    }

    /**
     * 处理单个地区的数据
     *
     * @return 返回不一致的订单信息列表
     */
    private List<InconsistentOrderInfo> processLocation(String location) {
        List<InconsistentOrderInfo> inconsistentOrders = new ArrayList<>();

        try {
            // 设置租户上下文
            TenantContext.setTenant(location);
            LOGGER.info("正在处理地区: {}", location);

            // 查询状态不一致的订单
            inconsistentOrders = findInconsistentOrders(location);
        } catch (Exception e) {
            LOGGER.error("处理地区{}时出现异常: {}", location, e.getMessage(), e);
        } finally {
            TenantContext.clear();
        }

        return inconsistentOrders;
    }

    /**
     * 查找状态不一致的订单
     */
    private List<InconsistentOrderInfo> findInconsistentOrders(String location) {
        // 查询WMS状态为"获取异常"的出库单
        LambdaQueryWrapper<StockoutOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name());
        List<StockoutOrderEntity> logisticsGetFailOrders = stockoutOrderService.list(queryWrapper);

        if (CollectionUtils.isEmpty(logisticsGetFailOrders)) {
            LOGGER.info("地区 {} 没有获取异常状态的出库单", location);
            return new ArrayList<>();
        }

        LOGGER.info("地区 {} 获取异常状态的出库单数量: {}", location, logisticsGetFailOrders.size());

        // 获取所有出库单的订单号
        List<String> allOrderNoList = new ArrayList<>();
        Map<String, List<StockoutOrderEntity>> orderNoToStockoutOrders = new HashMap<>();
        for (StockoutOrderEntity order : logisticsGetFailOrders) {
            // 从出库单明细中获取订单号
            List<String> orderNoList = stockoutOrderItemMapper.getOrderNoByStockoutOrderId(order.getStockoutOrderId());
            if (!CollectionUtils.isEmpty(orderNoList)) {
                allOrderNoList.addAll(orderNoList);

                for (String orderNo : orderNoList) {
                    if (!orderNoToStockoutOrders.containsKey(orderNo)) {
                        orderNoToStockoutOrders.put(orderNo, new ArrayList<>());
                    }
                    orderNoToStockoutOrders.get(orderNo).add(order);
                }
            }
        }

        if (CollectionUtils.isEmpty(allOrderNoList)) {
            LOGGER.info("地区 {} 没有找到出库单对应的订单号", location);
            return new ArrayList<>();
        }

        LOGGER.info("地区 {} 获取异常状态的出库单对应的订单号数量: {}", location, allOrderNoList.size());

        // 调用ERP接口查询订单状态
        ErpGetTradeStatusRequest request = new ErpGetTradeStatusRequest(allOrderNoList);
        List<ErpGetTradeStatusResponse.TradeStatusInfo> tradeStatusInfoList;
        try {
            tradeStatusInfoList = erpApiService.getTradeStatus(request);
        } catch (Exception e) {
            LOGGER.error("调用ERP接口查询订单状态失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(tradeStatusInfoList)) {
            LOGGER.info("地区 {} 没有从ERP获取到订单状态信息", location);
            return new ArrayList<>();
        }

        // 筛选出WMS状态为"获取异常"但ERP状态为"待发货"的订单
        List<InconsistentOrderInfo> inconsistentOrders = new ArrayList<>();


        // 检查每个ERP订单状态
        for (ErpGetTradeStatusResponse.TradeStatusInfo statusInfo : tradeStatusInfoList) {
            // 如果ERP状态是"待发货"
            if (ErpOrderStateNameEnum.PENDING_SHIPMENT.getName().equals(statusInfo.getOrderStateName())) {
                String orderNo = statusInfo.getTid();
                // 查找对应的出库单
                List<StockoutOrderEntity> relatedOrders = orderNoToStockoutOrders.get(orderNo);
                if (!CollectionUtils.isEmpty(relatedOrders)) {
                    for (StockoutOrderEntity order : relatedOrders) {
                        // 找到状态不一致的订单
                        InconsistentOrderInfo inconsistentOrder = new InconsistentOrderInfo();
                        inconsistentOrder.setOrderNo(orderNo); // 使用真实订单号
                        inconsistentOrder.setStockoutOrderNo(order.getStockoutOrderNo()); // 添加出库单号
                        inconsistentOrder.setWmsStatus(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.getName());
                        inconsistentOrder.setErpStatus(statusInfo.getOrderStateName());
                        inconsistentOrders.add(inconsistentOrder);
                    }
                }
            }
        }

        LOGGER.info("地区 {} 状态不一致的出库单数量: {}", location, inconsistentOrders.size());
        return inconsistentOrders;
    }

    /**
     * 发送钉钉通知
     */
    private void sendDingDingNotification(Map<String, List<InconsistentOrderInfo>> locationInconsistentOrders) {
        DingTalkRobotWebhookMarkdownSendRequest request = new DingTalkRobotWebhookMarkdownSendRequest();
        request.setMsgType("markdown");

        // 初始化足够大的StringBuilder以避免扩容
        StringBuilder contentBuilder = new StringBuilder(1024);
        contentBuilder.append("## 【出库单状态不一致监控】\n\n"
                + "### WMS状态为【获取异常】但ERP状态为【待发货】的出库单：\n\n"
                + "| **地区** | **订单号** | **出库单号** | **WMS状态** | **ERP状态** |\n"
                + "| --- | --- | --- | --- | --- |\n");

        int grandTotal = 0;

        // 按地区处理不一致订单
        for (Map.Entry<String, List<InconsistentOrderInfo>> entry : locationInconsistentOrders.entrySet()) {
            String location = entry.getKey();
            String locationName = LocationEnum.getNameBy(location);
            if (locationName == null) {
                locationName = location;
            }

            List<InconsistentOrderInfo> inconsistentOrders = entry.getValue();
            grandTotal += inconsistentOrders.size();

            for (InconsistentOrderInfo order : inconsistentOrders) {
                contentBuilder.append("| **").append(locationName).append("** | ")
                        .append(order.getOrderNo()).append(" | ")
                        .append(order.getStockoutOrderNo()).append(" | ")
                        .append(order.getWmsStatus()).append(" | ")
                        .append(order.getErpStatus()).append(" |\n");
            }
        }

        contentBuilder.append("\n## 总计: ").append(grandTotal).append("条\n\n");

        request.setMarkdown(new DingTalkRobotWebhookMarkdownSendRequest.Markdown(
                "仓库提示：出库单状态不一致监控", contentBuilder.toString()));

        // 发送钉钉通知
        notifyApiService.sendWebhookStockCheckMarkdownMessage(request);
        LOGGER.info("已发送出库单状态不一致监控通知，共{}条", grandTotal);
    }

    /**
     * 不一致订单信息类
     */
    private static class InconsistentOrderInfo {
        private String orderNo; // 订单号
        private String stockoutOrderNo; // 出库单号
        private String wmsStatus;
        private String erpStatus;

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getStockoutOrderNo() {
            return stockoutOrderNo;
        }

        public void setStockoutOrderNo(String stockoutOrderNo) {
            this.stockoutOrderNo = stockoutOrderNo;
        }

        public String getWmsStatus() {
            return wmsStatus;
        }

        public void setWmsStatus(String wmsStatus) {
            this.wmsStatus = wmsStatus;
        }

        public String getErpStatus() {
            return erpStatus;
        }

        public void setErpStatus(String erpStatus) {
            this.erpStatus = erpStatus;
        }
    }
}
