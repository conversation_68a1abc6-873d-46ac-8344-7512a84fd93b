package com.nsy.wms.business.manage.thirdparty.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2023/9/12 10:32
 */
public class SaStoreDetailResponse {

    @ApiModelProperty("店铺id")
    private Integer id;

    @ApiModelProperty("店铺名称")
    private String storeName;

    /**
     * 关联店铺id
     */
    @ApiModelProperty("关联店铺id")
    private Integer associatedStoreId;
    /**
     * 业绩归属
     */
    @ApiModelProperty("业绩归属")
    private String achievementAttribution;

    /**
     * 部门(数据字典名称)
     */
    @ApiModelProperty("二级部门(数据字典名称)")
    private String secondDepartment;

    @ApiModelProperty("分销二级平台名称(数据字典id)")
    private String secondDepartmentName;

    @ApiModelProperty("部门(数据字典名称)")
    private String department;

    /**
     * 部门id(数据字典id)
     */
    @ApiModelProperty("部门id(数据字典id)")
    private Integer departmentId;

    @ApiModelProperty("店铺名称")
    private String erpStoreName;

    @ApiModelProperty("扩展属性1")
    private String extendedAttributes0ne;

    @ApiModelProperty("扩展属性2")
    private String extendedAttributesTwo;

    @ApiModelProperty("扩展属性3")
    private String extendedAttributesThree;

    @ApiModelProperty("所属平台")
    private String platformName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAchievementAttribution() {
        return achievementAttribution;
    }

    public void setAchievementAttribution(String achievementAttribution) {
        this.achievementAttribution = achievementAttribution;
    }

    public String getSecondDepartment() {
        return secondDepartment;
    }

    public void setSecondDepartment(String secondDepartment) {
        this.secondDepartment = secondDepartment;
    }

    public String getSecondDepartmentName() {
        return secondDepartmentName;
    }

    public void setSecondDepartmentName(String secondDepartmentName) {
        this.secondDepartmentName = secondDepartmentName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getErpStoreName() {
        return erpStoreName;
    }

    public void setErpStoreName(String erpStoreName) {
        this.erpStoreName = erpStoreName;
    }

    public String getExtendedAttributes0ne() {
        return extendedAttributes0ne;
    }

    public void setExtendedAttributes0ne(String extendedAttributes0ne) {
        this.extendedAttributes0ne = extendedAttributes0ne;
    }

    public String getExtendedAttributesTwo() {
        return extendedAttributesTwo;
    }

    public void setExtendedAttributesTwo(String extendedAttributesTwo) {
        this.extendedAttributesTwo = extendedAttributesTwo;
    }

    public String getExtendedAttributesThree() {
        return extendedAttributesThree;
    }

    public void setExtendedAttributesThree(String extendedAttributesThree) {
        this.extendedAttributesThree = extendedAttributesThree;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Integer getAssociatedStoreId() {
        return associatedStoreId;
    }

    public void setAssociatedStoreId(Integer associatedStoreId) {
        this.associatedStoreId = associatedStoreId;
    }
}
