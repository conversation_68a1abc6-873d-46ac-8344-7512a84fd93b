package com.nsy.wms.repository.jpa.mapper.supplier;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.business.base.utils.SelectModel;
import com.nsy.wms.repository.entity.supplier.SupplierEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 供应商 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
@Mapper
public interface SupplierMapper extends BaseMapper<SupplierEntity> {


    @InterceptorIgnore(tenantLine = "true")
    SupplierEntity getBySupplierId(Integer supplierId);
    
    List<SelectModel> getSupplierDepartmentInfo();
}
