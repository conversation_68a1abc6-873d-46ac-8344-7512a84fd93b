<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockPlatformScheduleMapper">

    <select id="pageSearchShippingList" resultType="com.nsy.api.wms.domain.stock.StockPlatformSchedulePageSearchInfo">
        SELECT
        sps.platform_schedule_id,
        MAX(sps.platform_schedule_type) as platformScheduleType,
        MAX(sps.supplier_delivery_no) as supplierDeliveryNo,
        MAX(sps.supplier_id) as supplierId,
        MAX(sps.supplier_name) as supplierName,
        MAX(sps.purchase_user_real_name) as purchaseUserRealName,
        MAX(supplier.model_merchandiser_emp_name) as modelMerchandiserEmpName,
        MAX(sps.purchase_model) as purchaseModel,
        MAX(sps.delivery_date) as deliveryDate,
        MAX(sps.plan_arrive_date) as planArriveDate,
        MAX(sps.plan_box_num) as planBoxNum,
        MAX(sps.stockin_status) as stockinStatus,
        MAX(sps.status) as appointmentStatus,
        MAX(sps.platform_name) as platformName,
        MAX(spsi.is_fba_quick) as isFbaQuick,
        MAX(spsi.brand_name) as brandName,
        MAX(sps.schedule_start_date) as scheduleStartDate,
        MAX(sps.schedule_end_date) as scheduleEndDate,
        MAX(sps.audit_date) as auditDate,
        MAX(sps.create_date) as createDate,
        MAX(sps.update_date) as updateDate,
        MAX(supplier.supplier_code) as supplierCode,
        MAX(sps.remarks) as remarks
        FROM stock_platform_schedule sps
        inner join supplier supplier on supplier.supplier_id = sps.supplier_id
        INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
        <include refid="searchShippingList"></include>
        GROUP BY sps.platform_schedule_id
        ORDER BY sps.platform_schedule_id desc
    </select>

    <select id="countSearchShippingListTotal" resultType="java.lang.Integer">
        SELECT
        count(distinct sps.platform_schedule_id)
        FROM stock_platform_schedule sps
        inner join supplier supplier on supplier.supplier_id = sps.supplier_id
        INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
        <include refid="searchShippingList"></include>
    </select>

    <select id="statisticsSearchShippingTaskQty"
            resultType="com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse">
        SELECT count(DISTINCT spsi.supplier_delivery_box_code) AS planBoxNum,
        IFNULL(sum(spsi.qty), 0) AS shipmentQty
        FROM stock_platform_schedule sps
        inner join supplier supplier on supplier.supplier_id = sps.supplier_id
        INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
        <include refid="searchShippingList"></include>
    </select>

    <select id="statisticsSearchShippingOrderQty"
            resultType="com.nsy.api.wms.response.stockin.StatisticsByPlatformScheduleIdResponse">
        SELECT
        IFNULL(sum(soi.return_qty), 0) AS returnQty,
        IFNULL(sum(soi.shelved_qty), 0) AS shelvedQty,
        IFNULL(sum(soi.qty), 0) AS receiveQty
        FROM
        (
        SELECT DISTINCT sps.supplier_delivery_no
        FROM stock_platform_schedule sps
        inner join supplier supplier on supplier.supplier_id = sps.supplier_id
        INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
        <include refid="searchShippingList"></include>
        ) as t
        INNER JOIN stockin_order_task sot ON sot.supplier_delivery_no = t.supplier_delivery_no
        INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
        LEFT JOIN stockin_order_item soi ON soi.task_item_id = soti.task_item_id
        AND soi.real_stockin_order_item_id = 0
    </select>


    <sql id="searchShippingList">
        <if test="query.spu != null and query.spu !=''">
            INNER JOIN product_info pi ON pi.product_id = spsi.product_id
        </if>
        <if test="query!=null and query.logisticsNo != null and query.logisticsNo !=''">
            LEFT JOIN stockin_spot_info spi ON spi.supplier_delivery_no = sps.supplier_delivery_no
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            LEFT JOIN bd_area ba ON ba.space_id = spsi.space_id and ba.area_name = spsi.area_name
        </if>
        /* 泉州需要看到分公司找总公司采购的单*/
        where (sps.location = #{location}
        <if test="location == 'QUANZHOU'">
            or sps.order_type = 1
        </if>
        )
        <if test="query!=null and query.supplierDeliveryNo != null and query.supplierDeliveryNo !=''">
            and sps.supplier_delivery_no = #{query.supplierDeliveryNo}
        </if>
        <if test="query!=null and query.supplierId != null ">
            and sps.supplier_id = #{query.supplierId}
        </if>
        <if test="query!=null and query.purchaseUserName != null and query.purchaseUserName !=''">
            and sps.purchase_user_name = #{query.purchaseUserName}
        </if>
        <if test="query!=null and query.modelMerchandiserEmpName != null and query.modelMerchandiserEmpName !=''">
            and supplier.model_merchandiser_emp_name = #{query.modelMerchandiserEmpName}
        </if>
        <if test="query!=null and query.spaceId != null ">
            and spsi.space_id = #{query.spaceId}
        </if>
        <if test="query!=null and query.purchaseNo != null and query.purchaseNo !=''">
            and spsi.purchase_plan_no = #{query.purchaseNo}
        </if>
        <if test="query!=null and query.logisticsNo != null and query.logisticsNo !=''">
            and spi.logistics_no = #{query.logisticsNo}
        </if>
        <if test="query!=null and query.appointmentStatus != null and query.appointmentStatus != '' ">
            and sps.status = #{query.appointmentStatus}
        </if>
        <if test="query!=null and query.stockinStatus != null and query.stockinStatus != '' ">
            and sps.stockin_status = #{query.stockinStatus}
        </if>
        <if test="query.stockinStatusList != null and query.stockinStatusList.size() > 0">
            and sps.stockin_status in
            <foreach collection="query.stockinStatusList" item="stockinStatus" open="(" close=")" separator=",">
                #{stockinStatus}
            </foreach>
        </if>
        <if test="query!=null and query.areaIdList != null and query.areaIdList.size() > 0">
            and ba.area_id in
            <foreach collection="query.areaIdList" item="areaId" open="(" close=")" separator=",">
                #{areaId}
            </foreach>
        </if>
        <if test="query!=null and query.deliveryDateStart!=null">
            and sps.delivery_date >= DATE_FORMAT(#{query.deliveryDateStart},'%Y-%m-%d')
        </if>
        <if test="query!=null and query.deliveryDateEnd!=null">
            and sps.delivery_date &lt;= DATE_FORMAT(#{query.deliveryDateEnd},'%Y-%m-%d')
        </if>
        <if test="query!=null and query.spu!=null and query.spu != '' ">
            and (pi.spu like concat(#{query.spu}, '%')
            <if test="query.spuAutoMatchList != null and query.spuAutoMatchList.size() > 0">
                OR
                <foreach collection="query.spuAutoMatchList" item="spu" open="(" close=")" separator="OR">
                    pi.spu like concat(#{spu}, '%')
                </foreach>
            </if>
            )
        </if>
        <if test="query!=null and query.sku!=null and query.sku != '' ">
            and (spsi.sku like concat(#{query.sku}, '%')
            <if test="query.skuAutoMatchList != null and query.skuAutoMatchList.size() > 0">
                OR
                <foreach collection="query.skuAutoMatchList" item="sku" open="(" close=")" separator="OR">
                    spsi.sku like concat(#{sku}, '%')
                </foreach>
            </if>
            )
        </if>
        <if test="query!=null and query.purchaseModel!=null and query.purchaseModel != '' ">
            and sps.purchase_model = #{query.purchaseModel}
        </if>
        <if test="query != null and query.selectIdList!=null and query.selectIdList.size() > 0 ">
            and sps.platform_schedule_id in
            <foreach collection="query.selectIdList" separator="," index="index" item="platformScheduleId" open="("
                     close=")">
                #{platformScheduleId}
            </foreach>
        </if>
        <if test="query!=null and query.isFbaQuick != null">
            and spsi.is_fba_quick = #{query.isFbaQuick}
        </if>
        <if test="query!=null and query.brandName != null and query.brandName !=''">
            and spsi.brand_name = #{query.brandName}
        </if>
    </sql>

    <select id="countSearchShippingList" resultType="com.nsy.api.wms.domain.stock.StockPlatformSchedulePageCount">
        SELECT
        count(DISTINCT sps.supplier_delivery_no) as orderQty,
        count(DISTINCT spsi.purchase_plan_no) as purchaseQty,
        count(DISTINCT spi.skc) as skcCount,
        sum(spsi.qty) as qty,
        count(DISTINCT spsi.supplier_delivery_box_code) as boxQty
        FROM stock_platform_schedule sps
        inner join supplier supplier on supplier.supplier_id = sps.supplier_id
        INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
        INNER JOIN product_spec_info spi ON spi.spec_id = spsi.spec_id
        <include refid="searchShippingList"></include>
    </select>

    <select id="pageSearchShippingItemList"
            resultType="com.nsy.api.wms.response.stock.StockShippingItemPageList">
        select soti.task_item_id,
        sot.supplier_delivery_box_code,
        MAX(soti.sku) as sku,
        MAX(soti.purchase_plan_no) as purchaseNo,
        MAX(so.stockin_order_id) as stockinOrderId,
        MAX(so.status) as status,
        MAX(sot.box_index) as boxIndex,
        MAX(soti.is_need_qa) as isNeedQA,
        MAX(soti.expected_qty) as shipment_qty,
        MAX(soti.stockin_qty) as receiveQty,
        IFNULL(sum(soi.return_qty),0) as returnQty,
        IFNULL(sum(soi.shelved_qty),0) as shelvedQty,
        MAX(sot.create_date) as createDate,
        MAX(sot.update_date) as updateDate
        from stockin_order_task sot
        INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
        LEFT JOIN stockin_order so ON so.task_id = sot.task_id and so.real_stockin_order_id = 0
        LEFT JOIN stockin_order_item soi ON soti.task_item_id = soi.task_item_id and soi.real_stockin_order_item_id = 0
        where sot.platform_schedule_id = #{query.platformScheduleId}
        <if test="query != null and query.sku != null and query.sku != ''">
            and soti.sku like concat(#{query.sku}, '%')
        </if>
        GROUP BY sot.supplier_delivery_box_code,soti.task_item_id
    </select>

    <select id="pageSearchShippingItemListNoTask"
            resultType="com.nsy.api.wms.response.stock.StockShippingItemPageList">
        select spsi.supplier_delivery_box_code,
        spsi.sku as sku,
        spsi.purchase_plan_no as purchaseNo,
        '仓库未到货' as status,
        spsi.box_index as boxIndex,
        spsi.qty as shipment_qty,
        0 as receiveQty,
        0 as returnQty,
        0 as shelvedQty,
        spsi.create_date as createDate,
        spsi.update_date as updateDate
        from stock_platform_schedule sps
        INNER JOIN stock_platform_schedule_item spsi ON sps.platform_schedule_id = spsi.platform_schedule_id
        where sps.platform_schedule_id = #{query.platformScheduleId}
        <if test="query != null and query.sku != null and query.sku != ''">
            and spsi.sku like concat(#{query.sku}, '%')
        </if>
    </select>

    <select id="getTabs" resultType="com.nsy.api.wms.response.base.StatusTabResponse">
        select sps.stockin_status status,
        count(sps.stockin_status) num
        FROM stock_platform_schedule sps
        inner join supplier supplier on supplier.supplier_id = sps.supplier_id
        <where>
            <if test="stockinStatusList!=null and stockinStatusList.size() > 0 ">
                and sps.stockin_status in
                <foreach collection="stockinStatusList" separator="," index="index" item="stockinStatus" open="("
                         close=")">
                    #{stockinStatus}
                </foreach>
            </if>
        </where>
        GROUP BY sps.stockin_status
    </select>

    <select id="queryDeliveryQtyGroupBySupplierDeliveryBoxCode"
            resultType="com.nsy.api.wms.domain.stock.StockDeliveryQtyBySupplierDeliveryBoxCodeInfo">
        select psi.supplier_delivery_box_code,
        sum(psi.qty) as shipmentQty,
        MAX(ps.delivery_date) as deliveryDate,
        MAX(ps.platform_schedule_id) as platformScheduleId,
        MAX(ps.audit_date) as auditDate
        from stock_platform_schedule ps
        INNER JOIN stock_platform_schedule_item psi On psi.platform_schedule_id = ps.platform_schedule_id
        <where>
            <if test="request.purchasePlanNo !=null and request.purchasePlanNo !=''">
                and psi.purchase_plan_no = #{request.purchasePlanNo}
            </if>
            <if test="request.sku !=null and request.sku !=''">
                and psi.sku = #{request.sku}
            </if>
        </where>
        GROUP BY psi.supplier_delivery_box_code
    </select>
    <select id="queryAllInTransitInfo" resultType="com.nsy.api.wms.response.stockin.QueryAllInTransitInfoResponse">
        SELECT tb1.purchase_plan_no,tb1.sku,SUM(tb1.inTransitQty + tb1.actual_return_qty ) as inTransitQty
        FROM (
        SELECT i.purchase_plan_no,i.sku,SUM(i.qty) as inTransitQty, 0 as actual_return_qty
        FROM stock_platform_schedule s
        INNER JOIN stock_platform_schedule_item i ON i.platform_schedule_id = s.platform_schedule_id
        WHERE s.status IN ('APPOINTMENT_PENDING', 'NO_APPOINTMENT')
        GROUP BY i.purchase_plan_no,i.sku
        UNION ALL
        SELECT si.purchase_plan_no, si.sku, SUM(si.`expected_qty` - si.`stockin_qty` - si.`stockin_return_qty`) AS inTransitQty, 0 AS actual_return_qty FROM stockin_order_task s
        INNER JOIN stockin_order_task_item si ON s.`task_id` = si.`task_id`
        WHERE s.`status` IN ('PENDING', 'RECEIVING') AND si.`purchase_plan_no` != '' AND si.expected_qty > si.stockin_qty + si.stockin_return_qty
        GROUP BY si.purchase_plan_no, si.sku
        UNION ALL
        SELECT i.`purchase_plan_no`,i.`sku`,0 as inTransitQty,SUM(i.`actual_return_qty`) AS actual_return_qty
        FROM `stockin_return_product_task` a
        INNER JOIN `stockin_return_product_task_item` i ON a.`return_product_task_id`=i.`return_product_task_id`
        WHERE a.`return_nature`='REWORK_RETURN' AND a.`status` IN ('READY_DELIVERY','IN_TRANSIT') AND
        i.`actual_return_qty`>0
        GROUP BY i.`purchase_plan_no`,i.`sku`)tb1
        GROUP BY tb1.purchase_plan_no, tb1.sku
    </select>

    <select id="findById" resultType="com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity">
        select *
        from stock_platform_schedule
        where platform_schedule_id = #{platformScheduleId}
    </select>
    <select id="pagePlatformScheduleList"
            resultType="com.nsy.api.wms.response.stockin.StockinPlatformScheduleListResponse">
        select
        s.platform_schedule_id,
        s.supplier_delivery_no,
        s.supplier_name,
        si.space_name,
        s.`status`,
        s.audit_by,
        s.audit_date,
        s.remarks,
        s.create_date as createDate,
        sum(si.qty) as expectedQty,
        GROUP_CONCAT(distinct p.category_name) categoryNames,
        s.plan_box_num as boxNum,
        s.audit_description as auditDescription
        from stock_platform_schedule s
        INNER JOIN stock_platform_schedule_item si on s.platform_schedule_id = si.platform_schedule_id
        INNER JOIN product_info p on p.product_id = si.product_id
        <include refid="platformScheduleListWhere"></include>
        group by s.platform_schedule_id
        order by s.create_date desc
    </select>
    <select id="countPlatformScheduleList" resultType="java.lang.Integer">
        select
        count(distinct s.platform_schedule_id)
        from stock_platform_schedule s
        INNER JOIN stock_platform_schedule_item si ON s.platform_schedule_id = si.platform_schedule_id
        <include refid="platformScheduleListWhere"></include>
    </select>
    <sql id="platformScheduleListWhere">
        <where>
            s.purchase_model &lt;&gt; 'SPOT'
            <if test="request.spaceId !=null">
                and si.space_id = #{request.spaceId}
            </if>
            <if test="request.supplierDeliveryNo != null and request.supplierDeliveryNo !=''">
                and s.supplier_delivery_no = #{request.supplierDeliveryNo}
            </if>
            <if test="request.supplierId != null">
                and s.supplier_id = #{request.supplierId}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                and s.status in
                <foreach collection="request.statusList" separator="," item="status" open="(" close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.platformScheduleIdList != null and request.platformScheduleIdList.size() > 0">
                and s.platform_schedule_id in
                <foreach collection="request.platformScheduleIdList" separator="," item="detail" open="(" close=")">
                    #{detail}
                </foreach>
            </if>
            <if test="request.createStartDate != null">
                and s.create_date >= #{request.createStartDate}
            </if>
            <if test="request.createEndDate != null">
                and s.create_date &lt;= #{request.createEndDate}
            </if>
            <if test="request.auditStartDate != null">
                and s.audit_date >= #{request.auditStartDate}
            </if>
            <if test="request.auditEndDate != null">
                and s.audit_date &lt;= #{request.auditEndDate}
            </if>
            <if test="request.overTenDayUnAudit != null and request.overTenDayUnAudit == true ">
                and s.create_date &lt;= DATE_SUB(NOW(), INTERVAL 10 DAY)
            </if>
        </where>
    </sql>
    <select id="searchShippingItemListDownLoad"
            resultType="com.nsy.api.wms.response.stock.StockShippingItemPageList">
        select soti.task_item_id,
               sot.supplier_delivery_box_code,
               MAX(soti.sku)                  as sku,
               MAX(soti.purchase_plan_no)     as purchaseNo,
               MAX(so.stockin_order_id)       as stockinOrderId,
               MAX(so.status)                  as status,
               MAX(sot.box_index)              as boxIndex,
               MAX(soti.is_need_qa)            as isNeedQA,
               MAX(soti.expected_qty)          as shipment_qty,
               MAX(soti.stockin_qty)           as receiveQty,
               IFNULL(sum(soi.shelved_qty), 0) as shelvedQty,
               IFNULL(sum(soi.return_qty), 0)  as returnQty,
               MAX(sps.audit_date)             as auditDate,
               MAX(sot.create_date)            as createDate,
               MAX(sot.update_date)            as updateDate,
               si.skc                          as skc,
               soti.seller_sku                 as sellerSku,
               pi.spu                          as spu,
               MAX(sps.remarks)                as remarks
        from stockin_order_task sot
                 INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
                 inner join stock_platform_schedule sps on sps.platform_schedule_id = sot.platform_schedule_id
                 LEFT JOIN stockin_order so ON so.task_id = sot.task_id and so.real_stockin_order_id = 0
                 LEFT JOIN stockin_order_item soi
                           ON soti.task_item_id = soi.task_item_id and soi.real_stockin_order_item_id = 0
                 left join product_spec_info si on si.spec_id = soti.spec_id
                 left join product_info pi on pi.product_id = si.product_id
        where sot.platform_schedule_id = #{query.platformScheduleId}
        GROUP BY sot.supplier_delivery_box_code, soti.task_item_id

    </select>
    <select id="searchShippingItemListNoTaskDownLoad"
            resultType="com.nsy.api.wms.response.stock.StockShippingItemPageList">
        select spsi.supplier_delivery_box_code,
               spsi.sku              as sku,
               spsi.purchase_plan_no as purchaseNo,
               '仓库未到货'               as status,
               spsi.box_index        as boxIndex,
               spsi.qty              as shipment_qty,
               0                     as receiveQty,
               0                     as returnQty,
               0                     as shelvedQty,
               spsi.create_date      as createDate,
               spsi.update_date      as updateDate,
               si.skc                as skc,
               spsi.seller_sku       as sellerSku,
               pi.spu                as spu,
               sps.remarks           as remarks
        from stock_platform_schedule sps
                 INNER JOIN stock_platform_schedule_item spsi ON sps.platform_schedule_id = spsi.platform_schedule_id
                 left join product_spec_info si on si.spec_id = spsi.spec_id
                 left join product_info pi on pi.product_id = si.product_id
        where sps.platform_schedule_id = #{query.platformScheduleId}
    </select>

    <select id="getStockNameBySupplierDeliveryNo" resultType="java.lang.String">
        select spsi.space_name
        from stock_platform_schedule sps
                 INNER JOIN stock_platform_schedule_item spsi ON sps.platform_schedule_id = spsi.platform_schedule_id
        where sps.supplier_delivery_no = #{supplierDeliveryNo}
        limit 1
    </select>
    <select id="queryPlatformScheduleReceiveInfo"
            resultType="com.nsy.api.wms.response.stock.QueryStockPlatformScheduleReceiveInfoResponse">
        select sps.supplier_delivery_no as supplierDeliveryNo, sps.audit_date as arrivalTime
        from stock_platform_schedule sps
        <where>
            <if test="query.supplierDeliveryNoList != null and query.supplierDeliveryNoList.size() > 0">
                sps.supplier_delivery_no in
                <foreach collection="query.supplierDeliveryNoList" separator="," index="index" item="supplierDeliveryNo"
                         open="(" close=")">
                    #{supplierDeliveryNo}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getPlatformScheduleInfo" resultType="com.nsy.api.wms.response.stockin.StockinPlatformScheduleListResponse">
        select
        s.platform_schedule_id,
        s.supplier_delivery_no,
        s.supplier_name,
        si.space_name,
        s.`status`,
        s.audit_by,
        s.audit_date,
        s.remarks,
        s.create_date as createDate,
        sum(si.qty) as expectedQty,
        count(DISTINCT si.supplier_delivery_box_code) as boxNum,
        s.audit_description as auditDescription
        from stock_platform_schedule s
        INNER JOIN stock_platform_schedule_item si on s.platform_schedule_id = si.platform_schedule_id
        where s.supplier_delivery_no =  #{supplierDeliveryNo}
        group by s.platform_schedule_id
    </select>
    <select id="queryStockinPurchaseReport"
            resultType="com.nsy.api.wms.response.stockin.QueryStockinPurchaseReportItem">

        select
        si.purchase_plan_no as purchasePlanNo,
        si.sku,
        max(s.audit_date) as lastArrivalTime,
        sum(si.qty) as qty,
        IFNULL(sum(t.waitQcQty),0) as waitQcQty,
        IFNULL(sum(t.qcProcessingQty),0) as qcProcessingQty,
        IFNULL(sum(t.waitShelve),0) as waitShelveQty,
        IFNULL(sum(t.shelvedQty),0) as shelvedQty,
        IFNULL(sum(t.returnQty),0) as returnQty,
        IFNULL(sum(t.stockinQty),0) as stockinQty,
        IFNULL(sum(return_product.return_qty),0) as returnPositionQty,
        IFNULL(sum(product_task.task_return_qty),0) as taskReturnQty
        from stock_platform_schedule s
        INNER JOIN stock_platform_schedule_item si
        on s.platform_schedule_id = si.platform_schedule_id
        left join
        (
        select
        t.`status` as tstatus,
        o.supplier_delivery_box_code,
        oi.purchase_plan_no,oi.sku,
        sum(if(oi.`status` = 'WAIT_QC',oi.qty,0)) as waitQcQty,
        sum(if(oi.`status` = 'QC_PROCESSING',oi.qty,0)) as qcProcessingQty,
        sum(if(oi.`status` = 'WAIT_SHELVE',oi.qty - oi.shelved_qty - oi.return_qty,0)) as waitShelve,
        sum(oi.shelved_qty) as shelvedQty,
        sum(oi.return_qty) as returnQty,
        sum(oi.qty) as stockinQty
        from stockin_order_item oi
        INNER JOIN stockin_order o
        on o.stockin_order_id = oi.stockin_order_id
        INNER JOIN stockin_order_task t
        on t.task_id = o.task_id
        where oi.purchase_plan_no in
        <foreach collection="purchasePlanNos" separator="," index="index" item="purchasePlanNo"
                 open="(" close=")">
            #{purchasePlanNo}
        </foreach>

        GROUP BY o.supplier_delivery_box_code,oi.purchase_plan_no,oi.sku
        ) t
        on t.supplier_delivery_box_code = si.supplier_delivery_box_code
        and t.purchase_plan_no = si.purchase_plan_no
        and t.sku = si.sku
        left join
        (
        select p.supplier_delivery_box_code,p.purchase_plan_no,p.sku,sum(p.return_qty) as return_qty from
        stockin_return_product p
        where p.purchase_plan_no in
        <foreach collection="purchasePlanNos" separator="," index="index" item="purchasePlanNo"
                 open="(" close=")">
            #{purchasePlanNo}
        </foreach>

        GROUP BY p.supplier_delivery_box_code,p.purchase_plan_no,p.sku
        ) return_product
        on return_product.supplier_delivery_box_code = si.supplier_delivery_box_code
        and return_product.purchase_plan_no = si.purchase_plan_no
        and return_product.sku = si.sku

        left join
        (
        select ti.supplier_delivery_box_code,ti.purchase_plan_no,ti.sku,sum(actual_return_qty) task_return_qty
        from stockin_return_product_task_item ti
        INNER JOIN stockin_return_product_task t
        on t.return_product_task_id = ti.return_product_task_id
        where t.`status` in ('IN_TRANSIT','READY_DELIVERY')
        and ti.purchase_plan_no in
        <foreach collection="purchasePlanNos" separator="," index="index" item="purchasePlanNo"
                 open="(" close=")">
            #{purchasePlanNo}
        </foreach>

        GROUP BY ti.supplier_delivery_box_code,ti.purchase_plan_no,ti.sku
        ) product_task
        on product_task.supplier_delivery_box_code = si.supplier_delivery_box_code
        and product_task.purchase_plan_no = si.purchase_plan_no
        and product_task.sku = si.sku

        where s.`status` = 'AUDITED'
        and si.purchase_plan_no in
        <foreach collection="purchasePlanNos" separator="," index="index" item="purchasePlanNo"
                 open="(" close=")">
            #{purchasePlanNo}
        </foreach>

        GROUP BY si.purchase_plan_no,si.sku


    </select>
    <select id="countUncompletedOrder" resultType="java.lang.Integer">
        select count(*) from stockin_order_task t
        left join stockin_order o
        on t.task_id = o.task_id
        where t.supplier_delivery_no = #{supplierDeliveryNo}
          and (o.status != 'COMPLETED' or o.`status` is null)
    </select>

    <select id="getMaxId" resultType="java.lang.Integer">
        select max(platform_schedule_id) from stock_platform_schedule
    </select>
    <select id="listSupplierDeliveryNoBetweenIdRange" resultType="java.lang.String">
        select supplier_delivery_no
        from stock_platform_schedule
        where platform_schedule_id between  #{idStart} and #{idEnd}
        and stockin_status != 'COMPLETED'
    </select>
    <select id="getDeliveryDateBysetSupplierDeliveryNo" resultType="java.util.Date">
        select max(delivery_date) from stock_platform_schedule
        where supplier_delivery_no in
        <foreach collection="supplierDeliveryNos" separator="," index="index" item="supplierDeliveryNo"
                 open="(" close=")">
            #{supplierDeliveryNo}
        </foreach>
    </select>

    <select id="statisticsQty" resultType="com.nsy.api.wms.response.stockin.StockinPlatformScheduleStatisticResponse">
        select
        sum(t.expectedQty) as expectedQtyTotal,
        sum(t.boxNum) as boxNumTotal
        from
        (
            select
            sum(si.qty) as expectedQty,
            s.plan_box_num as boxNum
            from stock_platform_schedule s
            INNER JOIN stock_platform_schedule_item si on s.platform_schedule_id = si.platform_schedule_id
            <include refid="platformScheduleListWhere"></include>
            group by s.platform_schedule_id
        ) t
    </select>

    <select id="getPlatformIdByCondition" resultType="java.lang.Integer">
        select
        distinct s.platform_schedule_id
        from stock_platform_schedule s
        INNER JOIN stock_platform_schedule_item si on s.platform_schedule_id = si.platform_schedule_id
        INNER JOIN product_info p on p.product_id = si.product_id
        <include refid="platformScheduleListWhere"></include>
        <trim prefix="AND" prefixOverrides="AND ">
            <if test="request.supplierDeliveryBoxCode != null and request.supplierDeliveryBoxCode !=''">
                and si.supplier_delivery_box_code = #{request.supplierDeliveryBoxCode}
            </if>
        </trim>
        <trim prefix="AND" prefixOverrides="AND ">
            <if test="request.skuList != null and request.skuList.size() > 0">
                and si.sku  in
                <foreach collection="request.skuList" separator="," item="detail" open="(" close=")">
                    #{detail}
                </foreach>
            </if>
        </trim>
        <trim prefix="AND" prefixOverrides="AND ">
            <if test="request.spuList != null and request.spuList.size() > 0">
                and p.spu  in
                <foreach collection="request.spuList" separator="," item="detail" open="(" close=")">
                    #{detail}
                </foreach>
            </if>
        </trim>
    </select>

    <update id="updateRemarkInfo">
        update stock_platform_schedule set remarks =  #{request.remark}
        where supplier_delivery_no =  #{request.supplierDeliveryNo}
    </update>
    <update id="updateStockinStatus">
        update stock_platform_schedule set stockin_status = #{stockinStatus}
        where supplier_delivery_no in
        <foreach collection="supplierDeliveryNos" separator="," index="index" item="supplierDeliveryNo"
                 open="(" close=")">
            #{supplierDeliveryNo}
        </foreach>

    </update>

    <select id="statisticsShippingBoxAndQty" resultType="com.nsy.wms.business.domain.bo.stock.StockPlatformScheduleStatisticsBo">
        SELECT
            COUNT(DISTINCT spsi.supplier_delivery_box_code) AS boxQtyTotal,
            IFNULL(SUM(spsi.qty), 0) AS shipmentQtyTotal
        FROM stock_platform_schedule sps
        INNER JOIN supplier supplier ON supplier.supplier_id = sps.supplier_id
        INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
        <include refid="searchShippingList"></include>
    </select>

    <select id="statisticsReceiveAndShelvedAndReturnQty" resultType="com.nsy.wms.business.domain.bo.stock.StockPlatformScheduleStatisticsBo">
        SELECT
            IFNULL( SUM( soi.qty ), 0 ) AS receiveQtyTotal,
            IFNULL( SUM( soi.shelved_qty ), 0 ) AS shelvedQtyTotal,
            IFNULL( SUM( soi.return_qty ), 0 ) AS waitReturnQtyTotal
        FROM
            (
            SELECT DISTINCT
            sps.supplier_delivery_no,
            spsi.supplier_id
            FROM
            stock_platform_schedule sps
            INNER JOIN supplier supplier ON supplier.supplier_id = sps.supplier_id
            INNER JOIN stock_platform_schedule_item spsi ON spsi.platform_schedule_id = sps.platform_schedule_id
            <include refid="searchShippingList"></include>
            ) AS t
        INNER JOIN stockin_order_task sot ON sot.supplier_delivery_no = t.supplier_delivery_no
        INNER JOIN stockin_order_task_item soti ON soti.task_id = sot.task_id
        LEFT JOIN stockin_order_item soi ON soi.task_item_id = soti.task_item_id
        AND soi.real_stockin_order_item_id = 0
        INNER JOIN supplier supplier ON supplier.supplier_id = t.supplier_id
    </select>
</mapper>
