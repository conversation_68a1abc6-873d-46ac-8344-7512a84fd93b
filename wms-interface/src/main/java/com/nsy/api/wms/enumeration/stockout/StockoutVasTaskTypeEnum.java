package com.nsy.api.wms.enumeration.stockout;

import com.nsy.api.core.apicore.util.StringUtils;

import java.util.Arrays;

public enum StockoutVasTaskTypeEnum {

    PASTING_BARCODE("贴条码", "不换包装，仅在原包装装上粘贴或覆盖原有条码或其他标签一张"),
    HANG_TAG("挂吊牌", "将客户指定吊牌挂到衣服上（领标，水流标等处）使用穿线，打枪等方式，并包回原包装"),
    CH_PACK("换包装", "拆除原包装，不更改折叠方式，使用指定包装袋更换"),
    CH_TAG("改标/唛", "需使用针车，进行产品唛头，水洗标或尺码标进行更改一处，并包装回原来包装袋"),
    INSPECTION("全检", "按客户要求。对订单货品进行逐件拆包检查"),
    VACUUM("真空", "按客户要求，对商品包装进行真空处理"),
    CUTTING_TAGS("剪吊牌/领标", "剪吊牌/领标");

    String name;
    String des;

    public String getName() {
        return name;
    }

    public String getDes() {
        return des;
    }

    StockoutVasTaskTypeEnum(String name, String des) {
        this.name = name;
        this.des = des;
    }

    public static String getNameBy(String name) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(name))
                .findAny()
                .map(StockoutVasTaskTypeEnum::getName)
                .orElse(PASTING_BARCODE.getName());
    }

    public static String getDesBy(String name) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(name))
                .findAny()
                .map(StockoutVasTaskTypeEnum::getDes)
                .orElse(PASTING_BARCODE.getDes());
    }

    public static String getVasTypeStr(String vasType) {
        StringBuilder result = new StringBuilder();
        String[] strs = vasType.split(",");
        for (String str : strs) {
            result.append(',').append(StockoutVasTaskTypeEnum.getNameBy(str));
        }
        if (StringUtils.hasText(result.toString())) {
            return result.toString().replaceFirst(",", "");
        } else {
            return "";
        }
    }
}
