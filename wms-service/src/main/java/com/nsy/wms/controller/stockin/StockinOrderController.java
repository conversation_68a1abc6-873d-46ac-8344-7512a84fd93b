package com.nsy.wms.controller.stockin;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stockin.StockinOrderStatisticsResponse;
import com.nsy.api.wms.feign.StockinOrderItemClient;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.CanEditPackageRequest;
import com.nsy.api.wms.request.stockin.CheckOrShelveRequest;
import com.nsy.api.wms.request.stockin.QueryStockinOrderItemReceiveInfoRequest;
import com.nsy.api.wms.request.stockin.QueryStockinOrderItemSummaryRequest;
import com.nsy.api.wms.request.stockin.StockInOrderArrivalCountRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemListRequest;
import com.nsy.api.wms.request.stockin.StockinOrderListRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.external.FeignClientResponse;
import com.nsy.api.wms.response.stockin.CanEditPackageResponse;
import com.nsy.api.wms.response.stockin.CheckOrShelveResponse;
import com.nsy.api.wms.response.stockin.QueryStockinOrderItemReceiveInfoResponse;
import com.nsy.api.wms.response.stockin.QueryStockinOrderItemSummaryResponse;
import com.nsy.api.wms.response.stockin.QueryStockinPurchaseReportResponse;
import com.nsy.api.wms.response.stockin.StockInOrderArrivalCountListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderBoxListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderInfoResponse;
import com.nsy.api.wms.response.stockin.StockinOrderItemSkuListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderStatusCountResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTimeQueryResponse;
import com.nsy.api.wms.response.stockin.StockinReceiptShelveDateResponse;
import com.nsy.wms.business.service.business.BusinessBaseService;
import com.nsy.wms.business.service.stockin.StcokinOrderTimeService;
import com.nsy.wms.business.service.stockin.StockinOrderInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderItemPackageService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinPurchaseOrderService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.utils.JsonMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * Controller层
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "入库单接口")
@RestController
public class StockinOrderController extends BaseController implements StockinOrderItemClient {

    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    StockinOrderInfoService stockinOrderInfoService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;
    @Autowired
    StockinOrderItemPackageService stockinOrderItemPackageService;
    @Autowired
    BusinessBaseService businessBaseService;
    @Autowired
    StockinPurchaseOrderService stockinPurchaseOrderService;


    @ApiOperation(value = "入库单列表", produces = "application/json")
    @RequestMapping(value = "/stockin-order/list", method = RequestMethod.POST)
    public PageResponse<StockinOrderListResponse> getStockinOrderList(@RequestBody StockinOrderListRequest request) {
        return stockinOrderService.getStockinOrderListByRequest(request);
    }

    @ApiOperation(value = "入库单列表", produces = "application/json")
    @RequestMapping(value = "/stockin-order/list/statistics", method = RequestMethod.POST)
    public StockinOrderStatisticsResponse statisticsStockinOrderItemList(@RequestBody StockinOrderListRequest request) {
        return stockinOrderService.statisticsStockinOrderItemList(request);
    }

    @ApiOperation(value = "入库单状态对应数量", produces = "application/json")
    @RequestMapping(value = "/stockin-order/status-count", method = RequestMethod.GET)
    public StockinOrderStatusCountResponse getStockinOrderStatusCount() {
        return stockinOrderService.getStatusCount();
    }

    @ApiOperation(value = "入库单信息", produces = "application/json")
    @RequestMapping(value = "/stockin-order-info/{stockinOrderNo}", method = RequestMethod.GET)
    public StockinOrderInfoResponse getStockinOrderInfo(@PathVariable String stockinOrderNo) {
        return stockinOrderInfoService.getStockinOrderInfoByStockinOrderNo(stockinOrderNo);
    }

    @Override
    public List<StockinReceiptShelveDateResponse> firstStockinShelveDate(List<Integer> productIds) {
        return stockinOrderInfoService.firstStockinShelveDate(productIds);
    }

    @ApiOperation(value = "入库单详情sku信息列表", produces = "application/json")
    @RequestMapping(value = "/stockin-order-sku/list", method = RequestMethod.POST)
    public PageResponse<StockinOrderItemSkuListResponse> getShelveSplitTaskItemList(@RequestBody StockinOrderItemListRequest request) {
        return stockinOrderItemService.getStockinStockinOrderItemSkuList(request);
    }

    @ApiOperation(value = "入库单详情-内部箱信息列表", produces = "application/json")
    @RequestMapping(value = "/stockin-order-box/list", method = RequestMethod.POST)
    public PageResponse<StockinOrderBoxListResponse> getBoxList(@RequestBody StockinOrderItemListRequest request) {
        return stockinOrderItemService.getBoxList(request);
    }

    @Override
    public List<QueryStockinOrderItemSummaryResponse> queryStockinOrderItemSummary(QueryStockinOrderItemSummaryRequest request) {
        return stockinOrderItemService.queryStockinOrderItemSummary(request);
    }

    @Override
    public FeignClientResponse queryStockinOrderItemReceiveInfo(QueryStockinOrderItemReceiveInfoRequest request) {
        FeignClientResponse response = new FeignClientResponse();
        try {
            List<QueryStockinOrderItemReceiveInfoResponse> queryStockinOrderItemReceiveInfoResponses = stockinOrderItemService.queryStockinOrderItemReceiveInfo(request);
            response.setData(JsonMapper.toJson(queryStockinOrderItemReceiveInfoResponses));
            response.setStatus(200);
        } catch (BusinessServiceException e) {
            response.setMsg(e.getMessage());
            response.setStatus(500);
        }
        return response;
    }

    @Override
    public List<CanEditPackageResponse> canEditPackage(@RequestBody @Valid CanEditPackageRequest request) {
        return stockinOrderItemPackageService.canEditPackage(request);
    }

    @Override
    public List<StockinOrderTimeQueryResponse> productStockinOrderTimeQuery(@RequestBody List<Integer> productIdList) {
        return stockinOrderItemService.productStockinOrderTimeQuery(productIdList);
    }

    @Override
    public List<CheckOrShelveResponse> queryCheckOrShelve(@Valid CheckOrShelveRequest request) {
        return stockinOrderItemService.queryCheckOrShelve(request);
    }

    @Override
    public List<QueryStockinPurchaseReportResponse> queryStockinPurchaseReport(@Valid StringListRequest request) {
        return stockinPurchaseOrderService.queryStockinPurchaseReport(request);
    }

    @ApiOperation(value = "入库单上架中修改成核对（手动操作）", produces = "application/json", hidden = true)
    @RequestMapping(value = "/stockin-order/auto-check-confirm", method = RequestMethod.POST)
    public void updateStockinOrder(@RequestBody IdListRequest request) {
        stockinShelveTaskService.updateStockinOrder(request.getIdList(), true);
    }

    @ApiOperation(value = "工厂出库单到货数量统计", produces = "application/json")
    @RequestMapping(value = "/stockin-order-item/arrival-count", method = RequestMethod.POST)
    public StockInOrderArrivalCountListResponse getArriveCount(@RequestBody StockInOrderArrivalCountRequest request) {
        return stockinOrderItemService.getStockInOrderItemArrivalCount(request);
    }

    @ApiOperation(value = "入库单时间表修复（手动操作）", produces = "application/json", hidden = true)
    @RequestMapping(value = "/stockin-order-time/repair", method = RequestMethod.POST)
    public void stockinOrderTimeRepair(@RequestBody IdListRequest request) {
        stcokinOrderTimeService.repairData(request);
    }
}
