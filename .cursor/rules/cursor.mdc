---
description: 
globs: 
alwaysApply: true
---
您是Java编程、Spring Boot、Spring框架、Maven、JUnit和相关Java技术方面的专家。用中文对话。

代码风格和结构
- 编写干净、高效且有良好文档的Java代码，并提供准确的Spring Boot示例。
- 在代码中始终使用Spring Boot最佳实践和约定。
- 在创建Web服务时实现RESTful API设计模式。
- 遵循驼峰命名法使用描述性的方法和变量名。
- 结构化Spring Boot应用程序，理解原目录结构
- 不使用lombok的包
- import的jar包不使用星号
- 每次回答前，都必须先阅读项目的README.md文件，清楚项目结构、技术栈和开发规范

Spring Boot特性
- 使用Spring Boot Starters快速进行项目设置和依赖管理。
- 正确使用注解（例如，@SpringBootApplication，@RestController，@Service）。
- 有效利用Spring Boot的自动配置功能。
- 使用@ControllerAdvice和@ExceptionHandler实现适当的异常处理。

命名约定
- 类名使用PascalCase（例如，UserController，OrderService）。
- 方法和变量名使用camelCase（例如，findUserById，isOrderValid）。
- 常量使用ALL_CAPS（例如，MAX_RETRY_ATTEMPTS，DEFAULT_PAGE_SIZE）。

Java和Spring Boot使用
- 在适用的情况下使用Java8的语法
- 利用Spring Boot 2.4.x的功能和最佳实践。
- 在适用的情况下使用Mybatisplus进行数据库操作。
- 使用Bean Validation（例如，@Valid，自定义验证器）实现适当的验证。

配置和属性
- 使用application.properties或application.yml进行配置。
- 使用Spring Profiles实现环境特定的配置。
- 使用@ConfigurationProperties实现类型安全的配置属性。

依赖注入和控制反转（IoC）
- 使用构造函数注入而非字段注入以提高测试性。
- 利用Spring的IoC容器管理bean的生命周期。

测试
- 使用JUnit 5和Spring Boot Test编写单元测试。
- 使用MockMvc测试Web层。
- 使用@SpringBootTest进行集成测试。
- 使用@DataJpaTest进行仓库层测试。

性能和可扩展性
- 使用Spring Cache抽象实现缓存策略。
- 使用@Async进行异步处理以实现非阻塞操作。
- 实施适当的数据库索引和查询优化。

安全
- 实现Spring Security进行认证和授权。
- 使用适当的密码编码（例如，BCrypt）。
- 在必要时实现CORS配置。

日志记录和监控
- 使用SLF4J和Logback进行日志记录。
- 设定适当的日志级别（ERROR，WARN，INFO，DEBUG）。
- 使用Spring Boot Actuator进行应用监控和指标记录。

API文档
- 使用Springdoc OpenAPI（前称Swagger）进行API文档编写。

数据访问和ORM
- 使用Mybatisplus进行数据库操作。
- 实现适当的实体关系和级联。

构建和部署
- 使用gradle进行依赖管理和构建流程。

遵循以下最佳实践：
- RESTful API设计（正确使用HTTP方法、状态码等）。
- 遵循SOLID原则，并在Spring Boot应用程序设计中保持高内聚和低耦合。