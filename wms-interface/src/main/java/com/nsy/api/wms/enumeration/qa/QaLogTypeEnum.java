package com.nsy.api.wms.enumeration.qa;

public enum QaLogTypeEnum {

    START_QA("生成质检单"),
    QA_PROCESS("质检流程操作"),
    QA_SUBMIT("初检完成"),
    FIRST_AUDIT("质检初审"),
    TRIGGER_INSPECT("触发稽查"),
    SECOND_AUDIT("质检复审"),
    COMPLETE("质检完成"),

    INSPECT("质检稽查"),
    ADD_RETURN_QUANTITY("增加退货数量"),
    REDUCE_RETURN_QUANTITY("减少退货数量"),
    DELETED("取消质检"),

    BACK_TO_INCOMPLETE("返回未完成状态"),
    AUDIT_END("初审完成"),
    AUDIT("审核"),
    TWICE_AUDIT("复核"),
    AUDIT_REJECT("复核不通过"),
    CLAIM("领取"),
    ASSIGN("转派"),
    PUNISHMENTS("发起罚款");

    private String value;

    QaLogTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
