package com.nsy.wms.business.service.qa;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.qa.StockinQaBoxItemInfo;
import com.nsy.api.wms.enumeration.qa.StockinQaTaskStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.response.qa.StockinWaitQaTaskPageResponse;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinQcBuildService;
import com.nsy.wms.business.service.stockin.StockinQcInboundsService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaProductSampleRecordEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskItemEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaTaskMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检任务表业务实现
 * @date: 2024-11-13 10:09
 */
@Service
public class StockinQaTaskService extends ServiceImpl<StockinQaTaskMapper, StockinQaTaskEntity> {

    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private StockinQaTaskItemService stockinQaTaskItemService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private StockinQcService stockinQcService;
    @Autowired
    private StockinQaProductSampleRecordService stockinQaProductSampleRecordService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinQcBuildService stockinQcBuildService;
    @Autowired
    private StockinQaProductSampleSkuTypeService sampleSkuTypeService;
    @Autowired
    private SupplierApiService supplierApiService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockinQcInboundsService stockinQcInboundsService;
    @Autowired
    private StockinQaOrderSkuTypeInfoService skuTypeInfoService;

    public void generateOrUpdateQaTask(String internalBoxCode, String sku) {
        SpringUtil.getBean(StockinQaTaskService.class).generateOrUpdateQaTask(internalBoxCode, sku, false);
    }

    /**
     * 生成质检任务 or 更新待质检的质检任务
     * 一.取出箱子明细，过滤不需要生成质检任务的明细
     * 二.取出待质检/质检中的质检任务，与箱子明细比对
     * 1.不存在：生成质检任务
     * 2.已存在：质检中的任务不做改动
     * 3.比对相同入库单+采购单号的明细数量
     * <p>
     * 如果收货调整把整个箱子货移走则删除质检任务
     *
     * @param internalBoxCode
     */
    @Transactional
    public void generateOrUpdateQaTask(String internalBoxCode, String sku, boolean deleteTaskFlag) {
        StockInternalBoxEntity internalBoxEntity = stockInternalBoxService.findByInternalBoxCode(internalBoxCode);
        //一.取出箱子明细，过滤不需要生成质检任务的明细
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.getByInternalBoxCodeAndsku(internalBoxCode, sku);
        boxItemEntityList = stockinQcService.validQcBox(internalBoxEntity, boxItemEntityList);

        //二.取出待质检/质检中的质检任务
        StockinQaTaskEntity taskEntity = this.getOne(new LambdaQueryWrapper<StockinQaTaskEntity>()
            .select(StockinQaTaskEntity::getTaskId, StockinQaTaskEntity::getInternalBoxCode,
                StockinQaTaskEntity::getSku, StockinQaTaskEntity::getCheckStatus,
                StockinQaTaskEntity::getSupplierId, StockinQaTaskEntity::getSpaceId)
            .eq(StockinQaTaskEntity::getInternalBoxCode, internalBoxEntity.getInternalBoxCode())
            .eq(StockinQaTaskEntity::getSku, sku)
            .eq(StockinQaTaskEntity::getCheckStatus, StockinQaTaskStatusEnum.PENDING_QC.name())
            .last("limit 1"));

        if (CollectionUtils.isEmpty(boxItemEntityList)) {
            //如果没有任务不做处理
            if (Objects.isNull(taskEntity)) {
                return;
            }
            // 收货调整把整个箱子货移走则删除质检任务，如果任务存在，判断是否为关闭或漏检上架
            if (deleteTaskFlag) {
                this.removeById(taskEntity.getTaskId());
            } else {
                this.closeTQaTask(taskEntity);
            }
            return;
        }

        //与箱子明细比对
        compareBoxItemAndQaTask(internalBoxEntity, boxItemEntityList, taskEntity);
    }

    /**
     * 关闭质检任务
     *
     * @param taskEntity
     */
    private void closeTQaTask(StockinQaTaskEntity taskEntity) {
        Boolean isUpShelved = this.getBaseMapper().isUpShelved(taskEntity.getTaskId());
        StockinQaTaskEntity stockinQaTaskEntity = new StockinQaTaskEntity();
        stockinQaTaskEntity.setTaskId(taskEntity.getTaskId());
        stockinQaTaskEntity.setUpdateBy(loginInfoService.getName());
        stockinQaTaskEntity.setCheckStatus(isUpShelved ? StockinQaTaskStatusEnum.MISSED_DETECTION.name() : StockinQaTaskStatusEnum.CANCELLED.name());
        this.updateById(stockinQaTaskEntity);
    }

    /**
     * 二.取出待质检/质检中的质检任务，与箱子明细比对
     * 1.不存在：生成质检任务
     * 2.已存在：质检中的任务不做改动
     * 3.比对相同入库单+采购单号的明细数量
     *
     * @param boxItemList
     * @param taskEntity
     */
    private void compareBoxItemAndQaTask(StockInternalBoxEntity internalBoxEntity, List<StockInternalBoxItemEntity> boxItemList, StockinQaTaskEntity taskEntity) {
        //1.不存在：生成质检任务
        if (Objects.isNull(taskEntity)) {
            buildQaTask(internalBoxEntity, boxItemList);
            return;
        }
        List<StockinQaTaskItemEntity> taskItemList = stockinQaTaskItemService.list(new LambdaQueryWrapper<StockinQaTaskItemEntity>()
            .select(StockinQaTaskItemEntity::getStockinOrderItemId, StockinQaTaskItemEntity::getTaskId,
                StockinQaTaskItemEntity::getStockinOrderNo, StockinQaTaskItemEntity::getQty,
                StockinQaTaskItemEntity::getTaskItemId)
            .eq(StockinQaTaskItemEntity::getTaskId, taskEntity.getTaskId()));

        //2.已存在：质检中的任务不做改动
        if (StockinQaTaskStatusEnum.QC_PROCESSING.name().equalsIgnoreCase(taskEntity.getCheckStatus()))
            return;

        //3.比对相同入库单+采购单号的明细数量
        List<StockinQaTaskItemEntity> saveList = new LinkedList<>();
        List<StockinQaTaskItemEntity> updateList = new LinkedList<>();

        List<StockinQaBoxItemInfo> stockinQaBoxItemInfos = this.getBaseMapper().queryBoxItemInfo(boxItemList.stream().map(StockInternalBoxItemEntity::getInternalBoxItemId).collect(Collectors.toList()));
        Map<Integer, StockinQaTaskItemEntity> taskItemMap = taskItemList.stream().collect(Collectors.toMap(StockinQaTaskItemEntity::getStockinOrderItemId, Function.identity()));
        for (StockinQaBoxItemInfo boxItemInfo : stockinQaBoxItemInfos) {
            StockinQaTaskItemEntity stockinQaTaskItemEntity = taskItemMap.get(boxItemInfo.getStockinOrderItemId());
            //不存在则新增
            if (Objects.isNull(stockinQaTaskItemEntity)) {
                saveList.add(buildQaTaskItem(taskEntity, boxItemInfo));
                continue;
            }
            if (!stockinQaTaskItemEntity.getQty().equals(boxItemInfo.getQty())) {
                StockinQaTaskItemEntity qaTaskItemEntity = new StockinQaTaskItemEntity();
                qaTaskItemEntity.setTaskItemId(stockinQaTaskItemEntity.getTaskItemId());
                qaTaskItemEntity.setQty(boxItemInfo.getQty());
                qaTaskItemEntity.setUpdateBy(loginInfoService.getName());
                updateList.add(qaTaskItemEntity);
            }
        }

        //取出任务明细不存在箱子明细的数据
        List<Integer> stockinOrderItems = stockinQaBoxItemInfos.stream().map(StockinQaBoxItemInfo::getStockinOrderItemId).collect(Collectors.toList());
        List<Integer> removeList = taskItemList.stream().filter(item -> !stockinOrderItems.contains(item.getStockinOrderItemId())).map(StockinQaTaskItemEntity::getTaskItemId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(saveList))
            stockinQaTaskItemService.saveBatch(saveList);
        if (!CollectionUtils.isEmpty(updateList))
            stockinQaTaskItemService.updateBatchById(updateList);
        if (!CollectionUtils.isEmpty(removeList))
            stockinQaTaskItemService.removeByIds(removeList);

        //重置主任务的状态和数量
        int stockinQty = stockinQaBoxItemInfos.stream().mapToInt(StockinQaBoxItemInfo::getQty).sum();
        if (stockinQty > 0) {
            //根据明细重新赋值任务
            resetQaTaskEntity(internalBoxEntity, stockinQaBoxItemInfos, taskEntity);
            taskEntity.setUpdateBy(loginInfoService.getName());
        } else {
            taskEntity.setCheckStatus(StockinQaTaskStatusEnum.CANCELLED.name());
        }
        this.updateById(taskEntity);

    }

    //根据内部箱明细生成质检任务
    public StockinQaTaskEntity buildQaTask(StockInternalBoxEntity internalBoxEntity, List<StockInternalBoxItemEntity> boxItemList) {
        List<StockinQaBoxItemInfo> stockinQaBoxItemInfos = this.getBaseMapper().queryBoxItemInfo(boxItemList.stream().map(StockInternalBoxItemEntity::getInternalBoxItemId).collect(Collectors.toList()));
        StockinQaBoxItemInfo stockinQaBoxItemInfo = stockinQaBoxItemInfos.get(0);

        StockinQaTaskEntity qaTaskEntity = new StockinQaTaskEntity();
        BeanUtils.copyProperties(stockinQaBoxItemInfo, qaTaskEntity);

        qaTaskEntity.setCheckStatus(StockinQaTaskStatusEnum.PENDING_QC.name());
        qaTaskEntity.setLocation(TenantContext.getTenant());

        qaTaskEntity.setIsPushQa(0);
        qaTaskEntity.setStockinDate(new Date());
        qaTaskEntity.setCreateBy(loginInfoService.getName());

        //根据明细重新赋值任务
        resetQaTaskEntity(internalBoxEntity, stockinQaBoxItemInfos, qaTaskEntity);

        this.save(qaTaskEntity);
        //生成标签信息
        skuTypeInfoService.taskBuildSkuType(qaTaskEntity, stockinQaBoxItemInfos);

        List<StockinQaTaskItemEntity> itemEntityList = stockinQaBoxItemInfos.stream().map(item -> buildQaTaskItem(qaTaskEntity, item)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(itemEntityList))
            stockinQaTaskItemService.saveBatch(itemEntityList);

        return qaTaskEntity;

    }

    //根据明细重新赋值任务
    private void resetQaTaskEntity(StockInternalBoxEntity internalBoxEntity, List<StockinQaBoxItemInfo> stockinQaBoxItemInfos, StockinQaTaskEntity qaTaskEntity) {
        //是否需要新款质检（产前样质检）  是新款且相同spu未做过质检
        qaTaskEntity.setIsNew(stockinQaBoxItemInfos.stream().anyMatch(item -> StringConstant.FIRST_ORDER_LABEL.equals(item.getFirstOrderLabel())) ? 1 : 0);
        if (qaTaskEntity.getIsNew() == 1) {
            int count = stockinQaProductSampleRecordService.count(new LambdaQueryWrapper<StockinQaProductSampleRecordEntity>()
                .eq(StockinQaProductSampleRecordEntity::getProductId, qaTaskEntity.getProductId())
                .eq(StockinQaProductSampleRecordEntity::getResult, 1));
            qaTaskEntity.setNeedProductSample(count > 0 ? 0 : 1);
        }
        String labelAttributeNames = stockinQaBoxItemInfos.stream().map(StockinQaBoxItemInfo::getLabelAttributeNames).filter(StringUtils::hasText).collect(Collectors.joining(","));
        if (StringUtils.hasText(labelAttributeNames) && !labelAttributeNames.contains("null")) {
            qaTaskEntity.setLabelAttributeNames(Arrays.stream(labelAttributeNames
                .split(",")).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        }

        Integer boxQty = stockinQaBoxItemInfos.stream().mapToInt(StockinQaBoxItemInfo::getQty).sum();
        qaTaskEntity.setQaQty(StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBoxEntity.getInternalBoxType()) ? boxQty : stockinQcBuildService.getQaQty(qaTaskEntity.getSpaceId(), qaTaskEntity.getSupplierId(), boxQty));
        List<String> supplierDeliveryNoList = stockinQaBoxItemInfos.stream().map(StockinQaBoxItemInfo::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
        qaTaskEntity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, qaTaskEntity.getSku()));
    }

    @NotNull
    private StockinQaTaskItemEntity buildQaTaskItem(StockinQaTaskEntity qaTaskEntity, StockinQaBoxItemInfo item) {
        StockinQaTaskItemEntity itemEntity = new StockinQaTaskItemEntity();
        BeanUtils.copyProperties(item, itemEntity);
        itemEntity.setLocation(TenantContext.getTenant());
        if (Objects.nonNull(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains(StringConstant.FIRST_ORDER_LABEL_OEM))
            itemEntity.setFirstLabel(StringConstant.FIRST_ORDER_LABEL_OEM);
        if (Objects.nonNull(item.getLabelAttributeNames()) && item.getLabelAttributeNames().contains(StringConstant.FIRST_ORDER_LABEL_ODM))
            itemEntity.setFirstLabel(StringConstant.FIRST_ORDER_LABEL_ODM);

        itemEntity.setTaskId(qaTaskEntity.getTaskId());
        itemEntity.setCreateBy(loginInfoService.getName());
        return itemEntity;
    }

    public StockinQaTaskEntity getQaTaskByInfo(String internalBoxCode, String sku) {
        return this.getBaseMapper().getQaTaskByInfo(internalBoxCode, sku);
    }

    public List<StockinQaBoxItemInfo> queryBoxItemInfo(List<Integer> internalBoxItemIds) {
        return this.getBaseMapper().queryBoxItemInfo(internalBoxItemIds);
    }

    /**
     * 查询内部箱对应的待质检数据,并过滤掉非新款数据
     *
     * @param internalBoxCode
     * @return
     */
    public List<StockinWaitQaTaskPageResponse> getWaitQaTaskExcludeNotNew(String internalBoxCode) {
        //内部箱对应的待质检数据
        List<StockinWaitQaTaskPageResponse> waitQaTaskList = this.getBaseMapper().listWaitQaListNew(internalBoxCode);
        if (CollectionUtils.isEmpty(waitQaTaskList)) {
            return Collections.emptyList();
        }
        //去除了非新款的待质检数据
        List<StockinWaitQaTaskPageResponse> response = new ArrayList<>(waitQaTaskList.size());
        waitQaTaskList.forEach(detail -> {
            //非新款
            if (!sampleSkuTypeService.validNewOrder(internalBoxCode, detail.getSku())) {
                return;
            }
            response.add(detail);
        });
        return response;
    }

    /**
     * 完结入库质检任务
     *
     * @param stockinQaOrderEntity
     */
    @Transactional
    public void completeTask(StockinQaOrderEntity stockinQaOrderEntity) {
        StockinQaTaskEntity taskEntity = new StockinQaTaskEntity();
        taskEntity.setTaskId(stockinQaOrderEntity.getTaskId());
        taskEntity.setCheckStatus(StockinQaTaskStatusEnum.QC_COMPLETED.name());
        taskEntity.setOperator(loginInfoService.getName());
        taskEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(taskEntity);

        supplierApiService.syncStockinQaTask(stockinQaOrderEntity.getInternalBoxCode(), stockinQaOrderEntity.getSku());
    }

    /**
     * 自动完结质检任务
     *
     * @param taskId
     */
    @Transactional
    public void autoComplete(Integer taskId) {
        StockinQaTaskEntity taskEntity = this.getById(taskId);
        StockinQaTaskStatusEnum statusEnum = StockinQaTaskStatusEnum.CANCELLED;
        //需判断是否漏检上架
        if (isMissedDetection(taskEntity))
            statusEnum = StockinQaTaskStatusEnum.MISSED_DETECTION;

        StockinQaTaskEntity qaTaskEntity = new StockinQaTaskEntity();
        qaTaskEntity.setTaskId(taskId);
        qaTaskEntity.setUpdateBy(loginInfoService.getName());
        qaTaskEntity.setCheckStatus(statusEnum.name());
        this.updateById(qaTaskEntity);
    }

    private boolean isMissedDetection(StockinQaTaskEntity taskEntity) {
        //是否存在质检记录
        Integer count = stockinQcInboundsService.countByInternalBoxCodeAndsku(taskEntity.getInternalBoxCode(), taskEntity.getSku());
        if (count == 0) {
            //查询是否存在上架记录
            Integer shelvedQty = stockinQaTaskItemService.getBaseMapper().sumShelveQtyByTaskId(taskEntity.getTaskId());
            return shelvedQty > 0;
        }
        return false;
    }
}
