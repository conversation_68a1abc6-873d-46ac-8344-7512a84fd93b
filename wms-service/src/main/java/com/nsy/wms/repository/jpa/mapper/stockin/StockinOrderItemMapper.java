package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stock.StockinOrderItemBean;
import com.nsy.api.wms.domain.stockin.StockInPageItemInfo;
import com.nsy.api.wms.domain.stockin.StockinOrderItem;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItem;
import com.nsy.api.wms.request.stockin.CanEditPackageRequest;
import com.nsy.api.wms.request.stockin.CheckOrShelveRequest;
import com.nsy.api.wms.request.stockin.QueryStockinOrderItemReceiveInfoRequest;
import com.nsy.api.wms.request.stockin.QueryStockinOrderItemSummaryRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptItemListRequest;
import com.nsy.api.wms.request.stockin.StockinReceiptSummaryRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductQtyRequest;
import com.nsy.api.wms.response.external.QcPurchaseCount;
import com.nsy.api.wms.response.stockin.CheckOrShelveResponse;
import com.nsy.api.wms.response.stockin.QueryStockinOrderItemReceiveInfoResponse;
import com.nsy.api.wms.response.stockin.ReturnProductSearchQtyResponse;
import com.nsy.api.wms.response.stockin.StatisticsByStockinOrderIdResponse;
import com.nsy.api.wms.response.stockin.StockInOrderArrivalCountResponse;
import com.nsy.api.wms.response.stockin.StockReceiptItem;
import com.nsy.api.wms.response.stockin.StockinOrderItemCommonResponse;
import com.nsy.api.wms.response.stockin.StockinOrderItemSkuListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTimeQueryResponse;
import com.nsy.api.wms.response.stockin.StockinPackageNameResponse;
import com.nsy.wms.business.domain.bo.stockin.StockinOrderItemBo;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderRequestItem;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 入库单明细Mapper
 * @date: 2021-07-26 15:37
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinOrderItemMapper extends BaseMapper<StockinOrderItemEntity> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockinOrderItemSkuListResponse> pageSearchStockinOrderItemList(IPage page, @Param("query") StockinOrderItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StatisticsByStockinOrderIdResponse> statisticsByReceiptSummaryRequest(@Param("request") StockinReceiptSummaryRequest request);

    List<StockinOrderItemEntity> listQaInternalBox(@Param("taskItemList") List<Integer> taskItemList);

    //查询入库单中是否存在装入质检箱的商品，返回质检箱号
    List<String> findQaBoxItem(String stockinOrderNo, String purchasePlanNo, String sku);

    //查询其他箱子的入库单明细
    List<StockinOrderItemBean> findDiffBoxItem(Integer stockinOrderId, String sku, String internalBoxCode);

    //根据入库任务明细ID和箱子类型查询入库单明细
    List<StockinOrderItemEntity> findAllByTaskItemId(List<Integer> taskItemIds, String internalBoxType);

    // 根据入库任务明细ID和箱号 sku查询入库单明细
    List<StockinOrderItemEntity> findAllByTaskItemIdAndBox(List<Integer> taskItemIds, String internalBoxCode, String sku);

    /**
     * 通过出库箱号和sku查找item列表
     *
     * @param supplierDeliveryBox
     * @param sku
     * @return
     */
    List<StockinOrderItemBo> selectBySupplierDeliveryBoxCodeAndSku(String supplierDeliveryBox, String sku);

    //查询工厂出库单下是否存在装入质检箱的商品
    List<StockinOrderItemEntity> findAllBySupplierDeliveryNoAndQaBox(String supplierDeliveryNo, List<String> skus);

    //查询工厂出库单下是否存在装入质检箱的商品
    List<String> getIsPreQaBySupplierDeliveryNo(List<String> supplierDeliveryNos);

    Integer sumQty(Date dateStart, Date dateEnd);

    @InterceptorIgnore(tenantLine = "true")
    List<StockReceiptItem> pageSearchReceiptItemList(IPage page, @Param("query") StockinReceiptItemListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockReceiptItem> pageSearchReceiptItemList(@Param("query") StockinReceiptItemListRequest request);

    String getReceiveBoxSkuStatus(String internalBoxCode, String sku, String stockInOrderNo, String purchasePlanNo);

    String getQaBoxSkuStatus(String internalBoxCode, String sku);

    List<StockInOrderArrivalCountResponse> getStockInOrderItemArrivalCount(List<String> supplierDeliveryNo);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderItemEntity> findAllByStockinOrderIdIgnoreTenant(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderItemEntity> findAllByStockinOrderItemIdIgnoreTenant(@Param("stockinOrderItemIdList") List<Integer> stockinOrderItemIdList);

    @InterceptorIgnore(tenantLine = "true")
    Integer deleteByStockinOrderIdIgnoreTenant(@Param("stockinOrderId") Integer stockinOrderId);

    @InterceptorIgnore(tenantLine = "true")
    void updateIgnoreTenant(StockinOrderItemEntity stockinOrderItemEntity);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderItemEntity> findAllByPurchasePlanNosAndSkusIgnoreTenant(@Param("query") QueryStockinOrderItemSummaryRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<QueryStockinOrderItemReceiveInfoResponse> queryStockinOrderItemReceiveInfoIgnoreTenant(@Param("query") QueryStockinOrderItemReceiveInfoRequest request);

    List<StockinOrderItemCommonResponse> searchStockinOrderItemList(@Param("stockinOrderId") Integer stockinOrderId);

    /**
     * 包含查询不到上架任务得-- 工厂直发
     *
     * @param stockinOrderId
     * @return
     */
    List<StockinOrderItemCommonResponse> searchStockinOrderItemListContainShelved(@Param("stockinOrderId") Integer stockinOrderId);

    List<QcPurchaseCount> getPurchaseCount(List<Integer> stockinOrderIds, String internalBoxCode, String sku, String purchasePlanNo);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderItem> getCanEditPackage(@Param("query") CanEditPackageRequest request);

    List<StockinOrderItem> getConcessionItem(@Param("stockinOrderIds") List<Integer> stockinOrderIds);

    String getAreaName(String stockinOrderNo, String purchasePlanNo, String sku);

    List<StockInPageItemInfo> getStockInPageItemInfo(@Param("stockInOrderIdList") List<Integer> stockInOrderIdList);

    List<String> getAreaNameByShelveTask(List<Integer> sourceId, String sku, String internalBoxCode);

    List<ReturnProductSearchQtyResponse> searchQtyByReturnQtyaBatch(List<StockinReturnProductQtyRequest.StockinReturnProductQtyItem> requestList);

    //入库修改数量-查询入库单明细
    List<StockinOrderTaskItem> findItemToEdit(List<Integer> taskIds, Integer specId, String internalBoxCode);

    List<StockinOrderTimeQueryResponse> productStockinOrderTimeQuery(List<Integer> productIdList);

    List<CheckOrShelveResponse> queryCheckOrShelve(@Param("request") CheckOrShelveRequest request);

    List<Integer> selectZeroQtyItemIdBySupplierDeliveryNo(String supplierDeliveryNo);

    Integer getSpaceIdByCondition(@Param("stockinOrderNo") String stockinOrderNo, @Param("purchasePlanNo") String purchasePlanNo, @Param("sku") String sku);

    Integer sumArrivalCount(@Param("supplierDeliveryNoList") List<String> supplierDeliveryNoList, @Param("sku") String sku);

    /**
     * 计算退货率
     *
     * @param sku
     * @return
     */
    Double calculateReturnRange(@Param("sku") String sku);


    /**
     * 获取包装方式信息
     *
     * @param supplierDeliveryNo
     * @param internalBoxCode
     * @param sku
     * @return
     */
    List<StockinPackageNameResponse> getPackageNameInfo(@Param("supplierDeliveryNo") String supplierDeliveryNo, @Param("internalBoxCode") String internalBoxCode, @Param("sku") String sku);

    StockinOrderItemEntity findOneEverStockin(@Param("supplierId") Integer supplierId, @Param("sku") String sku);

    List<PurchaseOrderRequestItem> getPurchaseOrderRequest(@Param("stockinOrderId") Integer stockinOrderId);
}
