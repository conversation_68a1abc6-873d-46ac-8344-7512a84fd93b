package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockin.StockinPackageQty;
import com.nsy.api.wms.domain.stockin.StockinSupplierBoxSku;
import com.nsy.api.wms.request.stockin.QueryPurchaseOrderInfoBySkuRequest;
import com.nsy.api.wms.request.stockin.QueryPurchaseOrderInfoRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskItemListRequest;
import com.nsy.api.wms.response.stockin.QueryPurchaseOrderEarliestReceiptDateResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTaskItemListResponse;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 入库任务表Mapper
 * @date: 2021-07-30 17:12
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinOrderTaskItemMapper extends BaseMapper<StockinOrderTaskItemEntity> {
    List<StockinOrderTaskItemListResponse> pageList(Page page, @Param("request") StockinOrderTaskItemListRequest request);

    List<QueryPurchaseOrderEarliestReceiptDateResponse> queryEarliestReceiptDateByPurchasePlanNo(@Param("request") QueryPurchaseOrderInfoRequest request);

    List<QueryPurchaseOrderEarliestReceiptDateResponse> queryEarliestReceiptDateByPurchasePlanNoAndSku(@Param("request") List<QueryPurchaseOrderInfoBySkuRequest> requests);

    List<StockinSupplierBoxSku> findBoxQtyBySupplierDeliveryNo(String supplierDeliveryNo);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderTaskItemEntity> findAllByTaskIdListIgnoreTenant(@Param("taskIdList") List<Integer> taskIdList);

    List<StockinPackageQty> getPackageQty(@Param("taskId") Integer taskId);

    int findQtyBySupplierDeliveryBoxCodeAndSku(String supplierDeliveryBoxCode, String sku);

    List<String> findSkuListByPurchasePlanNo(String purchasePlanNo);

    boolean isReturnDeliveryType(List<Integer> taskIdList);
}
