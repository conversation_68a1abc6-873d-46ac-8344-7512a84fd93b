package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdQaSopEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.qa.QaLogTypeEnum;
import com.nsy.api.wms.enumeration.qa.QaProcessEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaInspectStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderInspectResultEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderStatusEnum;
import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectBatchRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectDetailRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectPageRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateFirstAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectBatchResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectCountQtyResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectPageResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectResultDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderPageResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderProcess;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderImgEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderItemEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderUnqualifiedEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.WmsDateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述： 质检稽查相关服务
 *
 * <AUTHOR>
 * @date 2025/2/28 16:09
 */
@Service
public class StockinQaInspectService implements IDownloadService {

    @Autowired
    private StockinQaOrderService stockinQaOrderService;
    @Autowired
    private StockinQaOrderOperateService stockinQaOrderOperateService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinQaOrderLogService stockinQaOrderLogService;
    @Autowired
    private StockinQaOrderDetailService stockinQaOrderDetailService;
    @Autowired
    private StockinQaOrderProcessService stockinQaOrderProcessService;
    @Autowired
    private StockinQaOrderUnqualifiedService stockinQaOrderUnqualifiedService;
    @Autowired
    private StockinQcService stockinQcService;
    @Autowired
    private StockinQaTaskService stockinQaTaskService;
    @Autowired
    private StockinQaOrderItemService stockinQaOrderItemService;
    @Autowired
    private StockinQaOrderSkuTypeInfoService stockinQaOrderSkuTypeInfoService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockinQaOrderImgService stockinQaOrderImgService;
    @Autowired
    private StockinQaOrderSearchService stockinQaOrderSearchService;
    @Autowired
    private ApplicationContext context;

    //sop质检合格触发稽查
    @Transactional
    public void stockinQaSopCompleteInspect(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateRequest request, int directReturnCount, int totalDirectReturnCount, String defectCountContent) {
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.INSPECT_AUDIT);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setInspectStatus(StockinQaInspectStatusEnum.WAIT_INSPECT.name());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockinQaOrderService.updateById(orderEntity);
        //设置状态以及重置完成时间
        stockinQaOrderService.getBaseMapper().updateWaitInspectStatus(stockinQaOrderEntity.getStockinQaOrderId());
        stockinQaOrderOperateService.sopCompleteDealData(stockinQaOrderEntity, request, directReturnCount, totalDirectReturnCount);
        //更新稽查时间
        stockinQaOrderDetailService.update(new LambdaUpdateWrapper<StockinQaOrderDetailEntity>()
            .set(StockinQaOrderDetailEntity::getQaInspectDate, new Date())
            .eq(StockinQaOrderDetailEntity::getStockinQaOrderId, stockinQaOrderEntity.getStockinQaOrderId()));


        //记录日志
        String content = String.format("【%s】操作SOP质检完成%s，触发稽查规则，进入稽查流程", loginInfoService.getName(), defectCountContent);
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.TRIGGER_INSPECT, content);
        //生成一条状态是待稽查的流程信息
        this.buildInspectProcessInfo(stockinQaOrderEntity);
    }

    //初审触发稽查
    @Transactional
    public void firstAuditInspect(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateFirstAuditRequest request) {
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.INSPECT_AUDIT);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setInspectStatus(StockinQaInspectStatusEnum.WAIT_INSPECT.name());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockinQaOrderService.updateById(orderEntity);
        //设置状态以及重置完成时间
        stockinQaOrderService.getBaseMapper().updateWaitInspectStatus(stockinQaOrderEntity.getStockinQaOrderId());

        //更新稽查时间
        stockinQaOrderDetailService.update(new LambdaUpdateWrapper<StockinQaOrderDetailEntity>()
            .set(StockinQaOrderDetailEntity::getQaInspectDate, new Date())
            .eq(StockinQaOrderDetailEntity::getStockinQaOrderId, stockinQaOrderEntity.getStockinQaOrderId()));

        //记录日志
        String content = String.format("【%s】操作初审完成，触发稽查规则，进入稽查流程", loginInfoService.getName());
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.TRIGGER_INSPECT, content);
        //生成一条状态是待稽查的流程信息
        this.buildInspectProcessInfo(stockinQaOrderEntity);
    }

    /**
     * 质检稽查
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "inspectCommitResult", lockKey = "#request.stockinQaOrderId")
    public void inspectCommitResult(StockinQaOperateInspectRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到稽查任务");
        if (!StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("该稽查任务不是待稽查状态");
        }
        if (!StockinQaInspectStatusEnum.WAIT_INSPECT.name().equals(stockinQaOrderEntity.getInspectStatus())) {
            throw new BusinessServiceException("该稽查任务不是待稽查状态");
        }

        if (request.getUnqualifiedCount() > request.getInspectTotalCount()) {
            throw new BusinessServiceException("不合格件数不能大于稽查件数!");
        }
        StockinQaOrderDetailEntity qaOrderDetailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        //生成流程信息
        StockinQaOrderProcessEntity processEntity = this.updateInspectStatus(request, stockinQaOrderEntity, qaOrderDetailEntity);

        if (qaOrderDetailEntity.getDirectReturnCount() + request.getReturnCount() > qaOrderDetailEntity.getBoxQty()) {
            throw new BusinessServiceException("直接退货数 + 退货数不能大于箱内数!");
        }

        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.completeOrderDetailByInspectCommitResult(stockinQaOrderEntity, request);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setUpdateBy(loginInfoService.getName());

        //如果存在不合格原因，需要新增
        if (StringUtils.hasText(request.getUnqualifiedCategory())) {
            orderEntity.setUnqualifiedCategory(request.getUnqualifiedCategory());

            stockinQaOrderUnqualifiedService.saveUnqualifiedInspectCommitResult(processEntity, request);
            if (Objects.nonNull(stockinQaOrderEntity.getUnqualifiedCategory())
                && !stockinQaOrderEntity.getUnqualifiedCategory().equals(request.getUnqualifiedCategory()))
                stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.INSPECT,
                    String.format("%s将不合格归类由【%s】修改为【%s】", loginInfoService.getName(),
                        stockinQaOrderEntity.getUnqualifiedCategory(), request.getUnqualifiedCategory()));
        }

        //图片不为空则保存
        if (!CollectionUtils.isEmpty(request.getImageList())) {
            stockinQaOrderImgService.saveImageList(processEntity, request.getImageList());
        }

        //判断是否返回未完成
        if (request.getIsComplete().equals(0)) {
            orderEntity.setInspectResult(StockinQaOrderInspectResultEnum.BACK_QA.name());
            orderEntity.setInspectCompleteDate(new Date());
            stockinQaOrderService.updateById(orderEntity);
            stockinQaOrderOperateService.backToIncomplete(request.getStockinQaOrderId());
            //记录日志
            String content = String.format("【%s】操作质检稽查，返回未完成", loginInfoService.getName(),
                detailEntity.getUnqualifiedCount() <= 0 ? "" : String.format("，不合格数：%s，退货数：%s",
                    detailEntity.getUnqualifiedCount(), detailEntity.getReturnCount()));
            stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.INSPECT, content);
            return;
        }


        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.COMPLETED);


        StockinQaOrderStatusEnum statusEnum = stockinQaOrderOperateService.getStatusEnumByDetail(detailEntity);
        orderEntity.setCompleteDate(new Date());
        orderEntity.setInspectStatus(StockinQaInspectStatusEnum.INSPECT_COMPLETE.name());
        orderEntity.setInspectCompleteDate(new Date());
        orderEntity.setResult(statusEnum.name());
        orderEntity.setInspectResult(statusEnum.name());
        //记录日志
        String content = String.format("【%s】操作质检稽查完成，质检结果为【%s】%s", loginInfoService.getName(), statusEnum.getStatus(),
            detailEntity.getUnqualifiedCount() <= 0 ? "" : String.format("，不合格数：%s，退货数：%s",
                detailEntity.getUnqualifiedCount(), detailEntity.getReturnCount()));
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.INSPECT, content);
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.COMPLETE, String.format("%s完成质检单", loginInfoService.getName()));
        stockinQaOrderService.updateById(orderEntity);


        //反馈入库单
        QcInboundsMessage qcInboundsMessage = stockinQaOrderOperateService.buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), request.getReturnCount());
        stockinQcService.qcComplete(qcInboundsMessage);
        //完结质检任务
        stockinQaTaskService.completeTask(stockinQaOrderEntity);
    }

    /**
     * 生成待稽查状态的稽查流程信息
     * @param stockinQaOrderEntity
     * @return
     */
    private StockinQaOrderProcessEntity buildInspectProcessInfo(StockinQaOrderEntity stockinQaOrderEntity) {
        //新增一条复审的流程记录
        StockinQaOrderProcessEntity lastProcessEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
                .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderEntity.getStockinQaOrderId())
                .orderByDesc(StockinQaOrderProcessEntity::getSort)
                .last("limit 1"));
        StockinQaOrderProcessEntity processEntity = new StockinQaOrderProcessEntity();
        processEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        processEntity.setProcessName(StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus());
        processEntity.setLocation(stockinQaOrderEntity.getLocation());
        processEntity.setSort(lastProcessEntity.getSort() + 1);
        processEntity.setCreateBy(loginInfoService.getName());
        processEntity.setStatus(QaProcessEnum.WAIT_INSPECT.name());
        stockinQaOrderProcessService.save(processEntity);
        return processEntity;
    }

    /**
     * 修改流程状态为对应的状态，如果没有待稽查状态的流程则创建一条（旧数据）
     * @param request
     * @param stockinQaOrderEntity
     * @param qaOrderDetailEntity
     * @return
     */
    public StockinQaOrderProcessEntity updateInspectStatus(StockinQaOperateInspectRequest request, StockinQaOrderEntity stockinQaOrderEntity, StockinQaOrderDetailEntity qaOrderDetailEntity) {
        StockinQaOrderProcessEntity waitInspectProcessEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
                .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, request.getStockinQaOrderId())
                .eq(StockinQaOrderProcessEntity::getStatus, QaProcessEnum.WAIT_INSPECT.name())
                .orderByDesc(StockinQaOrderProcessEntity::getSort)
                .last("limit 1"));
        //没有待稽查状态的流程，属于旧数据直接生成一条新的流程记录
        if (Objects.isNull(waitInspectProcessEntity)) {
            waitInspectProcessEntity = this.buildInspectProcessInfo(stockinQaOrderEntity);
        }
        BeanUtils.copyProperties(request, waitInspectProcessEntity);
        waitInspectProcessEntity.setStatus(request.getIsComplete().equals(0) ? QaProcessEnum.BACK_QA.name()
                : request.getReturnCount() > 0 ? QaProcessEnum.UNQUALIFIED.name() : QaProcessEnum.QUALIFIED.name());
        waitInspectProcessEntity.setOperator(loginInfoService.getName());
        waitInspectProcessEntity.setUpdateBy(loginInfoService.getName());
        waitInspectProcessEntity.setInspectCount(request.getInspectTotalCount());
        waitInspectProcessEntity.setQaCount(qaOrderDetailEntity.getTestTotalCount());
        waitInspectProcessEntity.setOperateDate(new Date());
        waitInspectProcessEntity.setUpdateDate(new Date());
        stockinQaOrderProcessService.updateById(waitInspectProcessEntity);
        return waitInspectProcessEntity;
    }

    public PageResponse<StockinQaInspectPageResponse> pageInspectTaskList(StockinQaInspectPageRequest request) {
        PageResponse<StockinQaInspectPageResponse> response = new PageResponse<>();
        Page<StockinQaInspectPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        if (CollectionUtils.isEmpty(request.getInspectStatusList()))
            request.setInspectStatusList(Lists.newArrayList(StockinQaInspectStatusEnum.WAIT_INSPECT.name(), StockinQaInspectStatusEnum.INSPECT_COMPLETE.name()));

        IPage<StockinQaInspectPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageWaitInspectTaskList(page, request);
        List<StockinQaInspectPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setContent(pageList.getRecords());
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> stockinQaOrderIds = records.stream().map(StockinQaInspectPageResponse::getStockinQaOrderId).collect(Collectors.toList());

        Map<Integer, List<StockinQaOrderItemEntity>> itemMap = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>().select(StockinQaOrderItemEntity::getStockinQaOrderId, StockinQaOrderItemEntity::getSupplierDeliveryNo).in(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderItemEntity::getStockinQaOrderId));
        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>().select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId).in(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId));

        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());
        Map<String, String> inspectStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_STATUS.getName());
        Map<String, String> inspectResultMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_RESULT.getName());

        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(records.stream().map(StockinQaInspectPageResponse::getSku).collect(Collectors.toList())).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));

        records.stream().forEach(entity -> {
            StockinQaOrderProcessEntity processEntity = stockinQaOrderProcessService.getOneByStockinQaOrderIdAndProcessName(entity.getStockinQaOrderId(), StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus());
            if (StockinQaInspectStatusEnum.INSPECT_COMPLETE.name().equals(entity.getInspectStatus()) && Objects.nonNull(processEntity)) {
                entity.setInspectUserName(processEntity.getOperator());
            }

            List<StockinQaOrderItemEntity> taskItemEntityList = itemMap.get(entity.getStockinQaOrderId());
            entity.setSupplierDeliveryNo(taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
            entity.setProcessStatusStr(processStatusMap.get(entity.getProcessStatus()));
            entity.setResultStr(inspectResultMap.get(entity.getResult()));
            entity.setInspectStatusStr(inspectStatusMap.get(entity.getInspectStatus()));

            List<String> supplierDeliveryNoList = taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            entity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, entity.getSku()));

            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(entity.getSku());
            entity.setImageUrl(productSpecInfoEntity.getImageUrl());
            entity.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            entity.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());

            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }
        });

        response.setContent(pageList.getRecords());
        response.setTotalCount(stockinQaOrderService.getBaseMapper().pageWaitInspectCount(request));
        return response;
    }

    public PageResponse<StockinQaInspectPageResponse> pageInspectResultList(StockinQaInspectPageRequest request) {
        PageResponse<StockinQaInspectPageResponse> response = new PageResponse<>();

        Page<StockinQaInspectPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);

        request.setInspectResultIsNotNUll(Boolean.TRUE);
        this.buildInspectStatusQueryCondition(request);
        IPage<StockinQaInspectPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageInspectTaskList(page, request);
        List<StockinQaInspectPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setContent(pageList.getRecords());
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> stockinQaOrderIds = records.stream().map(StockinQaInspectPageResponse::getStockinQaOrderId).collect(Collectors.toList());

        Map<Integer, List<StockinQaOrderItemEntity>> itemMap = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>().select(StockinQaOrderItemEntity::getStockinQaOrderId, StockinQaOrderItemEntity::getSupplierDeliveryNo).in(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderItemEntity::getStockinQaOrderId));
        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>().select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId).in(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId));

        Map<String, String> inspectResultMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_RESULT.getName());
        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());
        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(records.stream().map(StockinQaInspectPageResponse::getSku).collect(Collectors.toList())).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));

        records.stream().forEach(entity -> {
            entity.setUnqualifiedCategory(stockinQaOrderUnqualifiedService.getInspectUnqualifiedCategory(entity.getStockinQaOrderId(), entity.getStockinQaOrderProcessId()));
            List<StockinQaOrderItemEntity> taskItemEntityList = itemMap.get(entity.getStockinQaOrderId());
            entity.setSupplierDeliveryNo(taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
            entity.setProcessStatusStr(processStatusMap.get(entity.getProcessStatus()));
            entity.setResultStr(this.convertInspectStatus(entity, inspectResultMap));
            List<String> supplierDeliveryNoList = taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            entity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, entity.getSku()));

            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(entity.getSku());

            entity.setImageUrl(productSpecInfoEntity.getImageUrl());
            entity.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            entity.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());

            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }

        });

        response.setContent(pageList.getRecords());
        response.setTotalCount(stockinQaOrderService.getBaseMapper().pageInspectCount(request));
        return response;
    }

    public StockinQaInspectResultDetailResponse getInspectDetail(StockinQaInspectDetailRequest request) {
        //校验用户查看的权利
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getBaseMapper().getByIdPermission(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单或无权查看");
        StockinQaInspectResultDetailResponse response = new StockinQaInspectResultDetailResponse();
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(request.getStockinQaOrderId());
        BeanUtils.copyProperties(stockinQaOrderEntity, response, "unqualifiedCategory");
        BeanUtils.copyProperties(detailEntity, response);

        StockinQaTaskEntity taskEntity = stockinQaTaskService.getById(stockinQaOrderEntity.getTaskId());
        response.setQaQty(taskEntity.getQaQty());

        response.setProcessStatusStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName(), stockinQaOrderEntity.getProcessStatus()));
        response.setResultStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_RESULT.getName(), stockinQaOrderEntity.getResult()));

        List<StockinQaOrderItemEntity> itemEntityList = stockinQaOrderItemService.findByStockinQaOrderId(request.getStockinQaOrderId());

        response.setPurchasePlanNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        response.setSupplierDeliveryBoxCode(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(",")));
        response.setSupplierDeliveryNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));

        List<String> supplierDeliveryNoList = itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
        response.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, stockinQaOrderEntity.getSku()));

        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(stockinQaOrderEntity.getSku());

        response.setImageUrl(productSpecInfoEntity.getImageUrl());
        response.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
        response.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());

        response.setStockinQaProcessId(request.getStockinQaOrderProcessId());
        buildUnqualifiedReasonInspect(request.getStockinQaOrderId(), response);


        StockinQaOrderProcessEntity processEntity = stockinQaOrderProcessService.getById(request.getStockinQaOrderProcessId());
        if (Objects.nonNull(processEntity)) {
            response.setUnqualifiedCount(processEntity.getUnqualifiedCount());
            response.setReturnCount(processEntity.getReturnCount());
            response.setInspectTotalCount(processEntity.getInspectCount());
            response.setInspectUserName(processEntity.getCreateBy());
            response.setAttachmentUrl(processEntity.getAttachmentUrl());
            response.setRemark(processEntity.getRemark());

            List<StockinQaOrderImgEntity> stockinQaOrderImgEntities = stockinQaOrderImgService.getInspectByProcessInfo(request.getStockinQaOrderId(), request.getStockinQaOrderProcessId());
            if (!CollectionUtils.isEmpty(stockinQaOrderImgEntities))
                response.setImageList(stockinQaOrderImgEntities.stream().map(StockinQaOrderImgEntity::getImgUrl).collect(Collectors.toList()));
        }

        return response;
    }

    private void buildUnqualifiedReasonInspect(Integer stockinQaOrderId, StockinQaInspectResultDetailResponse response) {
        //兼容旧数据，查询未删除的且流程id=0（旧数据流程的id都为0）
        List<StockinQaOrderUnqualifiedEntity> unqualifiedEntityList = stockinQaOrderUnqualifiedService.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                .eq(StockinQaOrderUnqualifiedEntity::getIsDeleted, 0)
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, 0)
                .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
        //如果找不到带流程信息去找,即新数据
        if (CollectionUtils.isEmpty(unqualifiedEntityList)) {
            unqualifiedEntityList = stockinQaOrderUnqualifiedService.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                    .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                    .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, response.getStockinQaProcessId())
                    .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
        }
        //都找不到即没有
        if (CollectionUtils.isEmpty(unqualifiedEntityList)) return;
        StockinQaOrderUnqualifiedEntity unqualifiedEntity = unqualifiedEntityList.get(0);
        response.setUnqualifiedCategory(unqualifiedEntity.getUnqualifiedCategory());
        response.setUnqualifiedQuestion(unqualifiedEntity.getUnqualifiedQuestion());
        response.setUnqualifiedReason(unqualifiedEntity.getUnqualifiedReason());

        StockinQaOrderUnqualifiedEntity secondaryUnqualifiedEntity = null;
        if (unqualifiedEntityList.size() > 1)
            secondaryUnqualifiedEntity = unqualifiedEntityList.get(1);
        if (Objects.nonNull(secondaryUnqualifiedEntity) && secondaryUnqualifiedEntity.getProcessName().equals(unqualifiedEntity.getProcessName())) {
            response.setUnqualifiedCategorySecondary(secondaryUnqualifiedEntity.getUnqualifiedCategory());
            response.setUnqualifiedQuestionSecondary(secondaryUnqualifiedEntity.getUnqualifiedQuestion());
            response.setUnqualifiedReasonSecondary(secondaryUnqualifiedEntity.getUnqualifiedReason());
        }
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_INSPECT_TASK_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        DownloadResponse response = new DownloadResponse();

        Page<StockinQaOrderPageResponse> page = new Page<>(downloadRequest.getPageIndex(), downloadRequest.getPageSize());
        page.setSearchCount(false);
        StockinQaInspectPageRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinQaInspectPageRequest.class);

        if (CollectionUtils.isEmpty(request.getInspectStatusList()))
            request.setInspectStatusList(Lists.newArrayList(StockinQaInspectStatusEnum.WAIT_INSPECT.name(), StockinQaInspectStatusEnum.INSPECT_COMPLETE.name()));

        IPage<StockinQaInspectPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageWaitInspectTaskList(page, request);
        List<StockinQaInspectPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setDataJsonStr(JsonMapper.toJson(records));
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> stockinQaOrderIds = records.stream().map(StockinQaInspectPageResponse::getStockinQaOrderId).collect(Collectors.toList());

        Map<Integer, List<StockinQaOrderItemEntity>> itemMap = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>().select(StockinQaOrderItemEntity::getStockinQaOrderId, StockinQaOrderItemEntity::getSupplierDeliveryNo).in(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderItemEntity::getStockinQaOrderId));
        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>().select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId).in(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId));

        Map<String, String> statusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_RESULT.getName());
        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());
        Map<String, String> inspectStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_STATUS.getName());

        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(records.stream().map(StockinQaInspectPageResponse::getSku).collect(Collectors.toList())).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));

        records.stream().forEach(entity -> {
            StockinQaOrderProcessEntity processEntity = stockinQaOrderProcessService.getOneByStockinQaOrderIdAndProcessName(entity.getStockinQaOrderId(), StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus());
            if (StockinQaInspectStatusEnum.INSPECT_COMPLETE.name().equals(entity.getInspectStatus()) && Objects.nonNull(processEntity)) {
                entity.setInspectUserName(processEntity.getOperator());
            }
            List<StockinQaOrderItemEntity> taskItemEntityList = itemMap.get(entity.getStockinQaOrderId());
            entity.setSupplierDeliveryNo(taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
            entity.setProcessStatusStr(processStatusMap.get(entity.getProcessStatus()));
            entity.setResultStr(statusMap.get(entity.getResult()));
            entity.setInspectStatusStr(inspectStatusMap.get(entity.getInspectStatus()));

            List<String> supplierDeliveryNoList = taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            entity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, entity.getSku()));

            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(entity.getSku());

            entity.setImageUrl(productSpecInfoEntity.getImageUrl());
            entity.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            entity.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());

            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }

        });

        response.setTotalCount((long) (records.size() >= downloadRequest.getPageSize() ? (downloadRequest.getPageIndex() + 1) * downloadRequest.getPageSize() : (downloadRequest.getPageIndex() - 1) * downloadRequest.getPageSize() + records.size()));
        response.setDataJsonStr(JsonMapper.toJson(records));
        return response;
    }

    public StockinQaInspectCountQtyResponse countInspectQty(StockinQaInspectPageRequest request) {
        StockinQaInspectCountQtyResponse response = new StockinQaInspectCountQtyResponse();
        request.setInspectResultIsNotNUll(Boolean.TRUE);
        if (Objects.nonNull(request.getCompleteStartDate()) && Objects.nonNull(request.getCompleteEndDate())) {
            //按七天一次查询
            List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getCompleteStartDate(), request.getCompleteEndDate(), 7);
            Integer unqualifiedCount = 0;
            Integer returnCount = 0;
            Integer inspectTotalCount = 0;
            for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
                request.setCompleteStartDate(dateRangeRequest.getStartDate());
                request.setCompleteEndDate(dateRangeRequest.getEndDate());
                StockinQaInspectCountQtyResponse countQty = stockinQaOrderService.getBaseMapper().countInspectQty(request);
                unqualifiedCount += countQty.getUnqualifiedCount();
                returnCount += countQty.getReturnCount();
                inspectTotalCount += countQty.getInspectTotalCount();
            }
            response.setUnqualifiedCount(unqualifiedCount);
            response.setReturnCount(returnCount);
            response.setInspectTotalCount(inspectTotalCount);
            return response;
        } else {
            response = stockinQaOrderService.getBaseMapper().countInspectQty(request);
        }

        return response;
    }

    public List<StockinQaInspectBatchResponse> batchInspectList(StockinQaInspectBatchRequest request) {
        List<StockinQaInspectBatchResponse> skcList = stockinQaOrderService.getBaseMapper().batchInspectList(request);
        if (CollectionUtils.isEmpty(skcList)) {
            return new LinkedList<>();
        }
        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());
        Map<String, String> inspectStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_STATUS.getName());

        skcList.forEach(entity -> {
            List<StockinQaOrderProcessEntity> processEntityList = stockinQaOrderProcessService.listByStockinQaOrderId(entity.getStockinQaOrderId());
            Map<String, StockinQaOrderProcessEntity> collect = processEntityList.stream().collect(Collectors.toMap(StockinQaOrderProcessEntity::getProcessName, Function.identity()));
            entity.setProcessStatusStr(processStatusMap.get(entity.getProcessStatus()));
            entity.setInspectStatusStr(inspectStatusMap.get(entity.getInspectStatus()));

            StockinQaOrderProcessEntity processEntity = collect.get(BdQaSopEnum.QC_RESULT.getProcessName());
            StockinQaOrderProcess sopProcess = new StockinQaOrderProcess();
            BeanUtils.copyProperties(processEntity, sopProcess);
            sopProcess.setTestTotalCount(entity.getTestTotalCount());
            stockinQaOrderSearchService.buildProcessUnqualifiedReason(entity.getStockinQaOrderId(), sopProcess);
            sopProcess.setQualifiedCount(entity.getBoxQty() - sopProcess.getUnqualifiedCount());
            entity.setSopProcessResult(sopProcess);

            StockinQaOrderProcessEntity firstAuditProcessEntity = collect.get(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());
            if (Objects.nonNull(firstAuditProcessEntity)) {
                StockinQaOrderProcess firstAuditProcess = new StockinQaOrderProcess();
                BeanUtils.copyProperties(firstAuditProcessEntity, firstAuditProcess);
                stockinQaOrderSearchService.buildProcessUnqualifiedReason(entity.getStockinQaOrderId(), firstAuditProcess);
                entity.setFirstAuditResult(firstAuditProcess);
            }
        });
        return skcList;
    }

    @Transactional
    public void batchInspectCommit(StockinQaBatchInspectRequest request) {
        StockinQaInspectService bean = context.getBean(this.getClass());
        request.getList().forEach(item -> bean.inspectCommitResult(item));
    }

    /**
     * 流程状态转换成稽查报告状态
     *
     * @param entity
     * @param statusMap
     * @return
     */
    public String convertInspectStatus(StockinQaInspectPageResponse entity, Map<String, String> statusMap) {
        if (!StringUtils.hasText(entity.getResult())) {
            return "";
        }
        switch (entity.getResult()) {
            case "BACK_QA":
                return statusMap.get(entity.getResult());
            case "QUALIFIED":
                return statusMap.get(StockinQaOrderInspectResultEnum.PUT_ON.name());
            case "UNQUALIFIED":
                if (Objects.nonNull(entity.getBoxQty()) && Objects.nonNull(entity.getReturnCount()) && entity.getBoxQty().equals(entity.getReturnCount())) {
                    return statusMap.get(StockinQaOrderInspectResultEnum.BATCH_RETURN.name());
                } else {
                    return statusMap.get(StockinQaOrderInspectResultEnum.SOME_RETURN.name());
                }
            default:
                return "";
        }
    }

    public void buildInspectStatusQueryCondition(StockinQaInspectPageRequest request) {
        if (CollectionUtils.isEmpty(request.getResultList())) {
            return;
        }
        List<String> resultList = new ArrayList<>();
        for (String result : request.getResultList()) {
            switch (result) {
                case "BACK_QA":
                    resultList.add(QaProcessEnum.BACK_QA.name());
                    break;
                case "PUT_ON":
                    resultList.add(QaProcessEnum.QUALIFIED.name());
                    break;
                case "SOME_RETURN":
                case "BATCH_RETURN":
                    resultList.add(QaProcessEnum.UNQUALIFIED.name());
                    break;
                default:
                    break;
            }
        }
        if (!CollectionUtils.isEmpty(resultList)) {
            request.setResultList(resultList);
        }
        //如果为批退，则需要查询出所有状态为"不合格"的记录
        if (request.getResultList().contains(StockinQaOrderInspectResultEnum.BATCH_RETURN.name()) && !request.getResultList().contains(StockinQaOrderInspectResultEnum.SOME_RETURN.name())) {
            request.setBatchReturnFlag(Boolean.TRUE);
        }
        if (request.getResultList().contains(StockinQaOrderInspectResultEnum.SOME_RETURN.name()) && !request.getResultList().contains(StockinQaOrderInspectResultEnum.BATCH_RETURN.name())) {
            request.setSomeReturnFlag(Boolean.TRUE);
        }
    }
}
