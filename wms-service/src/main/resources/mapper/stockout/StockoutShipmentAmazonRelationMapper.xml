<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentAmazonRelationMapper">

    <select id="findShipmentIdByFbaShipmentId" resultType="java.lang.Integer">
        select DISTINCT shipment_id
        from stockout_shipment_amazon_relation
        where fba_shipment_id in
        <foreach collection="fbaShipmentIds" item="fbaShipmentId"  open="(" close=")" separator=",">
            #{fbaShipmentId}
        </foreach>
    </select>

    <select id="findOrderNosByFbaShipmentId" resultType="java.lang.String">
        select DISTINCT stockout_shipment_item.order_no
        from stockout_shipment_amazon_relation stockout_shipment_amazon_relation
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment_amazon_relation.shipment_id
        left join stockout_shipment stockout_shipment on stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        where stockout_shipment_amazon_relation.fba_shipment_id in
        <foreach collection="fbaShipmentIds" item="fbaShipmentId"  open="(" close=")" separator=",">
            #{fbaShipmentId}
        </foreach>
        <if test="shipmentIdList!=null and shipmentIdList.size() > 0 ">
            and stockout_shipment_item.shipment_id in
            <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                #{shipmentId}
            </foreach>
        </if>
        and stockout_shipment_item.is_deleted = 0
        group by stockout_shipment_item.order_no
        order by max(stockout_shipment.box_index) desc
    </select>
</mapper>
