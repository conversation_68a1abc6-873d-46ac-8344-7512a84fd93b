package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/27 10:31
 */
@ApiModel(value = "StockoutShipmentChangePackageTaskPageRequest", description = "打包任务分页查询请求")
public class StockoutShipmentChangePackageTaskPageRequest extends PageRequest {

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", name = "operator")
    private String operator;

    /**
     * 工作区
     */
    @ApiModelProperty(value = "工作区", name = "workspace")
    private String workspace;

    /**
     * 是否模糊查询
     */
    @ApiModelProperty(value = "是否模糊查询", name = "isFuzzy")
    private Integer isFuzzy = 0;

    /**
     * 操作时间开始
     */
    @ApiModelProperty(value = "操作时间开始", name = "operateDateStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDateStart;

    /**
     * 操作时间结束
     */
    @ApiModelProperty(value = "操作时间结束", name = "operateDateEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateDateEnd;

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public Integer getIsFuzzy() {
        return isFuzzy;
    }

    public void setIsFuzzy(Integer isFuzzy) {
        this.isFuzzy = isFuzzy;
    }

    public Date getOperateDateStart() {
        return operateDateStart;
    }

    public void setOperateDateStart(Date operateDateStart) {
        this.operateDateStart = operateDateStart;
    }

    public Date getOperateDateEnd() {
        return operateDateEnd;
    }

    public void setOperateDateEnd(Date operateDateEnd) {
        this.operateDateEnd = operateDateEnd;
    }
} 