<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderScanTaskItemMapper">
    <select id="pageSearchTaskItem" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItemList">
        SELECT
        s.image_url AS imageUrl,
        s.thumbnail_image_url AS thumbnailImageUrl,
        s.preview_image_url AS previewImageUrl,
        s.color AS color,
        s.size AS size,
        i.sku AS sku,
        i.barcode AS barcode,
        i.order_qty AS orderQty,
        i.expected_qty AS expectedQty,
        i.scan_qty AS scanQty,
        i.lack_qty AS lackQty,
        i.audit_date as auditDate,
        i.audit_by as auditBy
        FROM
        stockout_order_scan_task_item i
        LEFT JOIN product_spec_info s ON s.spec_id = i.spec_id
        <where>
            <if test="taskId!=null">
                and i.task_id = #{taskId}
            </if>
            <if test="sku!=null and sku!= '' ">
                and i.sku  like concat(#{sku}, '%')
            </if>
            <if test="barcode!=null and barcode!= '' ">
                and i.barcode = #{barcode}
            </if>
        </where>
    </select>
    <select id="searchTaskItem" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItem">
        SELECT
        i.task_item_id AS taskItemId,
        i.task_id AS taskId,
        s.product_id AS productId,
        s.spec_id AS specId,
        s.sku AS sku,
        s.barcode AS barcode,
        s.image_url AS imageUrl,
        s.thumbnail_image_url AS thumbnailImageUrl,
        s.preview_image_url AS previewImageUrl,
        i.order_qty AS orderQty,
        i.expected_qty AS expectedQty,
        i.scan_qty AS scanQty,
        i.lack_qty AS lackQty,
        i.shipment_qty AS shipmentQty,
        s.weight AS weight,
        i.order_item_id AS orderItemId,
        i.stockout_order_item_id as stockoutOrderItemId,
        (select seller_barcode from stockout_order_item oi where oi.order_item_id = i.order_item_id and oi.sku = i.sku
        limit 1) sellerBarcode,
        i.is_need_process,
        i.process_qualified_qty,
        i.is_first_order_by_store as isFirstOrderByStore,
        i.is_lead_generation,
        pi.package_vacuum as vacuumCn,
        i.change_type
        FROM
        stockout_order_scan_task_item i
        LEFT JOIN product_spec_info s ON s.spec_id = i.spec_id
        LEFT JOIN product_info pi ON pi.product_id = s.product_id
        <where>
            <if test="taskId!=null">
                and i.task_id = #{taskId}
            </if>
        </where>
        order by i.sku,i.process_qualified_qty desc
    </select>

    <select id="searchShipmentItemSku" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItemSku">
        SELECT
        item.order_no AS orderNo,
        item.order_item_id AS orderItemId,
        t.stockout_order_no AS stockoutOrderNo,
        p.product_name AS productName,
        item.sku AS sku,
        item.barcode AS barcode,
        sm.store_barcode AS storeBarcode,
        (item.scan_qty-item.shipment_qty) AS qty
        FROM stockout_order_scan_task_item item
        LEFT JOIN stockout_order_scan_task t ON t.task_id = item.task_id
        LEFT JOIN product_spec_info s ON s.spec_id = item.spec_id
        LEFT JOIN product_info p ON p.product_id = item.product_id
        LEFT JOIN product_store_sku_mapping sm ON sm.spec_id = item.spec_id
        <where>
            <if test="stockoutOrderNo!=null and stockoutOrderNo!='' ">
                and t.stockout_order_no = #{stockoutOrderNo} and item.scan_qty &gt; item.shipment_qty
            </if>
            <if test="shipmentBoxCode!=null and shipmentBoxCode!='' ">
                and t.shipmentBoxCode = #{shipmentBoxCode}
            </if>
        </where>
    </select>
    <select id="sumAllScanQtyByBatchId" resultType="java.lang.Integer">
        select ifnull(sum(osti.order_qty -osti.scan_qty -osti.lack_qty),0)
        from stockout_batch_order bo
            INNER JOIN stockout_order o on bo.stockout_order_id = o.stockout_order_id
            INNER JOIN stockout_order_scan_task ost on o.stockout_order_no = ost.stockout_order_no
             INNER JOIN stockout_order_scan_task_item osti on osti.task_id = ost.task_id
        where bo.batch_id = #{batchId}
          and sku = #{sku}
          and ost.task_id != #{taskId}

    </select>
    <select id="sumAllScanQtyByMergeBatchId" resultType="java.lang.Integer">
        select ifnull(sum(osti.order_qty -osti.scan_qty -osti.lack_qty),0) from
            stockout_batch b
                INNER JOIN stockout_batch_order bo on b.batch_id = bo.batch_id
                INNER JOIN stockout_order o on bo.stockout_order_id = o.stockout_order_id
                INNER JOIN stockout_order_scan_task ost on o.stockout_order_no = ost.stockout_order_no
                INNER JOIN stockout_order_scan_task_item osti on osti.task_id = ost.task_id
        where b.merge_batch_id = #{batchId}
          and sku = #{sku}
          and ost.task_id != #{taskId}

    </select>
    <select id="getPdaItem" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskPdaItem">
        select
            si.product_id,
            ti.spec_id,
            ti.sku,
            ti.barcode,
            oi.seller_barcode,
            oi.seller_sku,
            si.image_url,
            si.thumbnail_image_url,
            si.preview_image_url,
            sum(ti.order_qty) as orderQty,
            sum(ti.scan_qty) as shipmentQty,
            sum((select ifnull(shipment_item.qty,0)
                 from stockout_shipment_item shipment_item
                          INNER JOIN stockout_shipment shipment
                                     on shipment.shipment_id = shipment_item.shipment_id
                 where shipment.is_deleted = 0
                   and shipment_item.is_deleted = 0
                   and shipment_item.stockout_order_no = #{stockoutOrderNo}
                   and shipment.shipment_box_code = #{shipmentBoxCode}
                   and shipment_item.stockout_order_item_id = ti.stockout_order_item_id)) as currentShipmentQty
        from stockout_order_scan_task_item ti
                 INNER JOIN stockout_order_scan_task t
                            on t.task_id = ti.task_id
                 INNER JOIN stockout_order_item oi
                            on ti.stockout_order_item_id = oi.stockout_order_item_id
                 INNER JOIN product_spec_info si
                            on si.spec_id = ti.spec_id
        where t.stockout_order_no = #{stockoutOrderNo}
        GROUP BY si.sku;
    </select>

</mapper>
