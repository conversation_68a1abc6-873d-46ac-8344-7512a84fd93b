<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutLogisticsBatchMapper">
    <sql id="pageSearchWhere">
        <if test="query.logisticsBatch != null and query.logisticsBatch != ''">
            and stockout_logistics_batch.logistics_batch = #{query.logisticsBatch}
        </if>
        <if test="query.logisticsCompany != null and query.logisticsCompany != ''">
            and stockout_logistics_batch.logistics_company = #{query.logisticsCompany}
        </if>
        <if test="query.fbaShipmentId != null and query.fbaShipmentId != ''">
            and stockout_logistics_batch_shipment.fba_shipment_id = #{query.fbaShipmentId}
        </if>
        <if test="query.orderNo != null and query.orderNo != ''">
            and stockout_logistics_batch_shipment.order_no = #{query.orderNo}
        </if>
        <if test="query.platformName != null and query.platformName != ''">
            and stockout_logistics_batch_shipment.platform_name = #{query.platformName}
        </if>
        <if test="query.businessType != null and query.businessType != ''">
            and stockout_logistics_batch_shipment.business_type = #{query.businessType}
        </if>
        <if test="query!=null and query.statusList != null and query.statusList.size() > 0">
            and stockout_logistics_batch.status in
            <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                     close=")">
                #{status}
            </foreach>
        </if>
        <if test="query!=null and query.storeIdList != null and query.storeIdList.size() > 0">
            and stockout_logistics_batch_shipment.store_id in
            <foreach collection="query.storeIdList" separator="," index="index" item="storeIdOne" open="("
                     close=")">
                #{storeIdOne}
            </foreach>
        </if>
        <if test="query.differenceType != null">
            and stockout_logistics_batch.difference_type = #{query.differenceType}
        </if>
        <if test="query.exactDay != null">
            and stockout_logistics_batch.exact_day = #{query.exactDay}
        </if>
        <if test="query.checkDocumentName != null and query.checkDocumentName != ''">
            and stockout_logistics_batch_fee_document.document_name = #{query.checkDocumentName}
        </if>
        <if test="query.createDateBegin != null">
            and stockout_logistics_batch.create_date &gt;= #{query.createDateBegin}
        </if>
        <if test="query.createDateEnd != null">
            and stockout_logistics_batch.create_date &lt;= #{query.createDateEnd}
        </if>
        <if test="query.reviewResult != null">
            and stockout_logistics_batch.review_result = #{query.reviewResult}
        </if>
        <if test="query.financialPushStatus != null">
            and stockout_logistics_batch.financial_push_status = #{query.financialPushStatus}
        </if>
        <if test="query.deliveryDateBegin != null">
            and stockout_logistics_batch_shipment.delivery_date &gt;= #{query.deliveryDateBegin}
        </if>
        <if test="query.deliveryDateEnd != null">
            and stockout_logistics_batch_shipment.delivery_date &lt;= #{query.deliveryDateEnd}
        </if>
        <if test="query!=null and query.ids != null and query.ids.size() > 0">
            and stockout_logistics_batch.id in
            <foreach collection="query.ids" separator="," index="index" item="id" open="("
                     close=")">
                #{id}
            </foreach>
        </if>
    </sql>
    <select id="pageSearchList" resultType="java.lang.String">
        SELECT
            distinct stockout_logistics_batch.logistics_batch
        FROM
            stockout_logistics_batch stockout_logistics_batch
        LEFT JOIN stockout_logistics_batch_shipment stockout_logistics_batch_shipment
        ON stockout_logistics_batch_shipment.logistics_batch_id = stockout_logistics_batch.id
        <if test="query.checkDocumentName != null and query.checkDocumentName != ''">
            LEFT JOIN stockout_logistics_batch_fee_document stockout_logistics_batch_fee_document
            ON stockout_logistics_batch_fee_document.document_id = stockout_logistics_batch.check_document_id
        </if>
        <where>
            <include refid="pageSearchWhere"></include>
        </where>
        order by stockout_logistics_batch.id desc
    </select>
    <select id="countSearchList"
            resultType="com.nsy.api.wms.response.stockout.StockoutLogisticsBatchSumResponse">
        SELECT
        IFNULL(SUM(IFNULL(predict_weight, 0)), 0) as predictWeight,
        IFNULL(SUM(IFNULL(predict_total_price, 0)), 0) as predictTotalPrice,
        IFNULL(SUM(IFNULL(real_weight, 0)), 0) as realWeight,
        IFNULL(SUM(IFNULL(real_price, 0)), 0) as realPrice,
        IFNULL(SUM(IFNULL(real_tax, 0)), 0) as realTax,
        IFNULL(SUM(IFNULL(real_declare_price, 0)), 0) as realDeclarePrice
        FROM
        stockout_logistics_batch
        WHERE
        logistics_batch IN (
        SELECT
        distinct stockout_logistics_batch.logistics_batch
        FROM
        stockout_logistics_batch stockout_logistics_batch
        LEFT JOIN stockout_logistics_batch_shipment stockout_logistics_batch_shipment
        ON stockout_logistics_batch_shipment.logistics_batch_id = stockout_logistics_batch.id
        <if test="query.checkDocumentName != null and query.checkDocumentName != ''">
            LEFT JOIN stockout_logistics_batch_fee_document stockout_logistics_batch_fee_document
            ON stockout_logistics_batch_fee_document.document_id = stockout_logistics_batch.check_document_id
        </if>
        <where>
            <include refid="pageSearchWhere"></include>
        </where>
        )
    </select>
</mapper>
