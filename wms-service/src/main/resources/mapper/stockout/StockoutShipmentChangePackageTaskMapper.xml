<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentChangePackageTaskMapper">

    <select id="searchPage" resultType="com.nsy.api.wms.response.stockout.StockoutShipmentChangePackageTaskPageResponse">
        SELECT
            t.*
        FROM
            stockout_shipment_change_package_task t
        <include refid="whereCondition"></include>
        ORDER BY t.create_date DESC
    </select>

    <sql id="whereCondition">
        <where>
            <if test="query.shipmentId != null">
                AND t.shipment_id = #{query.shipmentId}
            </if>
            <if test="query.isFuzzy == 1 and query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
                AND t.shipment_box_code LIKE CONCAT('%', #{query.shipmentBoxCode}, '%')
            </if>
            <if test="query.isFuzzy == 0 and query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
                AND t.shipment_box_code = #{query.shipmentBoxCode}
            </if>
            <if test="query.isFuzzy == 1 and query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                AND t.stockout_order_no LIKE CONCAT('%', #{query.stockoutOrderNo}, '%')
            </if>
            <if test="query.isFuzzy == 0 and query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                AND t.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query.isFuzzy == 1 and query.orderNo != null and query.orderNo != ''">
                AND t.order_no LIKE CONCAT('%', #{query.orderNo}, '%')
            </if>
            <if test="query.isFuzzy == 0 and query.orderNo != null and query.orderNo != ''">
                AND t.order_no = #{query.orderNo}
            </if>
            <if test="query.isFuzzy == 0 and query.operator != null and query.operator != ''">
                AND t.operator = #{query.operator}
            </if>
            <if test="query.workspace != null and query.workspace != ''">
                AND t.workspace = #{query.workspace}
            </if>
            <if test="query.operateDateStart != null">
                AND t.operate_date &gt;= #{query.operateDateStart}
            </if>
            <if test="query.operateDateEnd != null">
                AND t.operate_date &lt;= #{query.operateDateEnd}
            </if>
        </where>
    </sql>

</mapper> 