package com.nsy.wms.business.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.request.bd.BdAqlRuleListRequest;
import com.nsy.api.wms.request.bd.BdAqlRuleRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdAqlRuleListResponse;
import com.nsy.wms.business.domain.dto.stockin.StockinQaAqlValidDTO;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.bd.BdAqlRuleEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdAqlRuleMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * AQL规则服务实现类
 */
@Service
public class BdAqlRuleService extends ServiceImpl<BdAqlRuleMapper, BdAqlRuleEntity> {

    @Autowired
    private LoginInfoService loginInfoService;

    /**
     * 分页查询AQL规则列表
     */
    public PageResponse<BdAqlRuleListResponse> pageList(BdAqlRuleListRequest request) {
        Page page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<BdAqlRuleListResponse> pageInfo = this.baseMapper.getRuleList(page, request);

        PageResponse<BdAqlRuleListResponse> pageResponse = new PageResponse<>();
        if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
            pageResponse.setTotalCount(0L);
            return pageResponse;
        }
        pageResponse.setTotalCount(pageInfo.getTotal());
        pageResponse.setContent(pageInfo.getRecords());
        return pageResponse;
    }

    /**
     * 新增AQL规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheKeyConstant.AQL_RULE_ID, allEntries = true)
    public void add(BdAqlRuleRequest request) {
        //校验是否区间重合
        checkRangeOverlap(request, null);
        BdAqlRuleEntity entity = new BdAqlRuleEntity();
        BeanUtils.copyProperties(request, entity);
        // 设置仓库ID默认为0
        if (entity.getSpaceId() == null) {
            entity.setSpaceId(0);
        }
        entity.setIsDeleted(Boolean.FALSE);
        entity.setLocation(TenantContext.getTenant());
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        this.save(entity);
    }

    /**
     * 编辑AQL规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheKeyConstant.AQL_RULE_ID, allEntries = true)
    public void edit(BdAqlRuleRequest request) {
        if (request.getAqlRuleId() == null) {
            throw new BusinessServiceException("请选择要修改的AQL规则");
        }
        BdAqlRuleEntity oldEntity = this.getById(request.getAqlRuleId());
        if (oldEntity == null) {
            throw new BusinessServiceException("AQL规则不存在");
        }
        //校验是否区间重合
        checkRangeOverlap(request, request.getAqlRuleId());
        BeanUtils.copyProperties(request, oldEntity);
        // 设置仓库ID默认为0
        if (oldEntity.getSpaceId() == null) {
            oldEntity.setSpaceId(0);
        }
        oldEntity.setUpdateBy(loginInfoService.getName());
        oldEntity.setUpdateDate(new Date());
        this.updateById(oldEntity);
    }

    /**
     * 检查区间是否重叠
     *
     * @param request   请求对象
     * @param excludeId 需要排除的ID（编辑时使用）
     */
    private void checkRangeOverlap(BdAqlRuleRequest request, Integer excludeId) {
        if (request.getRangeFrom() > request.getRangeTo()) {
            throw new BusinessServiceException("区间从 不能大于 区间到");
        }

        LambdaQueryWrapper<BdAqlRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(wp -> wp.ge(BdAqlRuleEntity::getRangeFrom, request.getRangeFrom())
                        .le(BdAqlRuleEntity::getRangeFrom, request.getRangeTo())
                        .or()
                        .ge(BdAqlRuleEntity::getRangeTo, request.getRangeFrom())
                        .le(BdAqlRuleEntity::getRangeTo, request.getRangeTo()))
                .and(wp -> {
                    if (!Objects.isNull(request.getSpaceId())) {
                        wp.eq(BdAqlRuleEntity::getSpaceId, request.getSpaceId());
                    } else {
                        wp.eq(BdAqlRuleEntity::getSpaceId, 0);
                    }
                })
                .eq(BdAqlRuleEntity::getIsDeleted, Boolean.FALSE);

        if (excludeId != null) {
            wrapper.ne(BdAqlRuleEntity::getAqlRuleId, excludeId);
        }

        List<BdAqlRuleEntity> notPermitList = list(wrapper);
        if (!notPermitList.isEmpty()) {
            throw new BusinessServiceException("该区间已经存在，请重新设置");
        }
    }

    /**
     * 删除AQL规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheKeyConstant.AQL_RULE_ID, allEntries = true)
    public void delete(List<Integer> aqlRuleIdList) {
        List<BdAqlRuleEntity> ruleEntityList = this.listByIds(aqlRuleIdList);
        if (CollectionUtils.isEmpty(ruleEntityList)) {
            throw new BusinessServiceException("AQL规则不存在");
        }
        for (BdAqlRuleEntity bdAqlRule : ruleEntityList) {
            bdAqlRule.setIsDeleted(Boolean.TRUE);
            bdAqlRule.setUpdateBy(loginInfoService.getName());
            bdAqlRule.setUpdateDate(new Date());
        }
        this.updateBatchById(ruleEntityList);
    }

    /**
     * 验证AQL规则
     * 验证逻辑：
     * 1. 检查致命缺陷数量，如果大于0直接返回true（不通过）
     * 2. 检查轻微缺陷数量比例，如果大于0.4返回true（不通过）
     * 3. 将轻微缺陷数量转换为严重缺陷数量（每3件轻微缺陷转换为1件严重缺陷）
     * 4. 检查总严重缺陷数量是否大于规则的最小接受数量
     *
     * @param dto 验证参数，包含：
     *            - minorDefectQty: 轻微缺陷数量
     *            - majorDefectQty: 严重缺陷数量
     *            - criticalDefectQty: 致命缺陷数量
     *            - spaceId: 仓库ID
     *            - qaQty: 质检数量
     * @return true-需要初审 false-不需要初审
     */
    public boolean validateAqlRule(StockinQaAqlValidDTO dto, BdAqlRuleEntity matchedRules) {
        // 1. 检查致命缺陷数量，如果大于0直接返回false（需要初审）
        if (dto.getCriticalDefectCount() > 0) {
            return true;
        }
        // 2. 检查轻微缺陷数量比例
        if (dto.getMinorDefectCount() > 0 && dto.getQaQty() != null && dto.getQaQty() > 0) {
            double minorRatio = (double) dto.getMinorDefectCount() / dto.getQaQty();
            if (minorRatio > 0.4) {
                return true;
            }
        }
        //没有符合条件的aql规则则不需要初审
        if (Objects.isNull(matchedRules)) {
            return false;
        }
        // 3. 转换轻微缺陷数量为严重缺陷数量
        int convertedMajorQty = 0;
        if (dto.getMinorDefectCount() > 0) {
            // 每3件轻微缺陷转换为1件严重缺陷
            convertedMajorQty = dto.getMinorDefectCount() / 3;
        }
        //总的严重数量
        int totalMajorQty = convertedMajorQty + (dto.getMajorDefectCount() != null ? dto.getMajorDefectCount() : 0);

        //5.最少可接受数量为0的特殊处理,配置的最少可接受数量为0则有不合格数都要审核
        if (matchedRules.getMinAcceptQty() == 0) {
            return dto.getMinorDefectCount() > 0 || dto.getMajorDefectCount() > 0 || dto.getCriticalDefectCount() > 0;
        }

        // 6. 检查是否超过最小接受数量
        return totalMajorQty >= matchedRules.getMinAcceptQty();
    }

    /**
     * 查询对应质检数的aql规则
     *
     * @param spaceId
     * @param qaQty
     * @return
     */
    public BdAqlRuleEntity getAqlRuleInfo(Integer spaceId, Integer qaQty) {
        //没有质检数直接返回
        if (qaQty == null) {
            return null;
        }
        List<BdAqlRuleEntity> bdQaRuleBySupplierId = this.getAalRuleBySpaceId(spaceId);

        Optional<BdAqlRuleEntity> spaceAqlRuleAny = bdQaRuleBySupplierId.stream().filter(item -> qaQty >= item.getRangeFrom() && qaQty <= item.getRangeTo()).findAny();
        if (spaceAqlRuleAny.isPresent()) {
            return spaceAqlRuleAny.get();
        }
        //获取兜底方案
        List<BdAqlRuleEntity> defaultRules = this.getAqlDefaultRule();
        if (defaultRules != null) {
            return defaultRules.stream().filter(item -> item.getRangeFrom() <= qaQty && item.getRangeTo() >= qaQty)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    @Cacheable(value = CacheKeyConstant.AQL_RULE_ID, key = "'default'")
    public List<BdAqlRuleEntity> getAqlDefaultRule() {
        LambdaQueryWrapper<BdAqlRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdAqlRuleEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        wrapper.eq(BdAqlRuleEntity::getSpaceId, 0);
        return this.list(wrapper);
    }

    @Cacheable(value = CacheKeyConstant.AQL_RULE_ID, key = "#spaceId")
    public List<BdAqlRuleEntity> getAalRuleBySpaceId(Integer spaceId) {
        LambdaQueryWrapper<BdAqlRuleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdAqlRuleEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        wrapper.eq(BdAqlRuleEntity::getSpaceId, spaceId);
        return this.list(wrapper);
    }
} 