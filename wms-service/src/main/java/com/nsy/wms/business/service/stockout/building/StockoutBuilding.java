package com.nsy.wms.business.service.stockout.building;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Sets;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.SpringContextHolder;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsShipmentInfo;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stockout.CustomerSkuPrintDTO;
import com.nsy.api.wms.domain.stockout.ShipmentOrderVO;
import com.nsy.api.wms.domain.stockout.StockoutBatchListExport;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitPrintSort;
import com.nsy.api.wms.domain.stockout.StockoutDeliverProductPrint;
import com.nsy.api.wms.domain.stockout.StockoutOrderItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTask;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItem;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTaskPrintInfo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItem;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.domain.stockout.StockoutStatusCount;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.common.IsEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferTrackingStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.InvoiceTemplateEnum;
import com.nsy.api.wms.enumeration.stockout.ReplenishOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderScanTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPackingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSewTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentPackStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stock.stocklog.StockChangeLogAddRequest;
import com.nsy.api.wms.request.stock.stocklog.StockTakeStockAddRequest;
import com.nsy.api.wms.request.stock.stocklog.StockinAddRequest;
import com.nsy.api.wms.request.stock.stocklog.StockoutAddRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderItemAddRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskPrintResponse;
import com.nsy.api.wms.response.stockout.StockoutConfirmShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderScanTaskValidateResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderStatusResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentCountResponse;
import com.nsy.wms.business.domain.dto.stockout.StockoutScanDetailMessage;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockTransferTrackingEntity;
import com.nsy.wms.repository.entity.stock.StockTransferTrackingItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLackItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentPackEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentPackItemEntity;
import com.nsy.wms.utils.BarCodeUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * HXD
 * 2021/10/22
 **/

public class StockoutBuilding {

    private static Set<String> misiPrSpaceArea = Sets.newHashSet("PR-A区", "PR-B区", "PR-C区", "PR-D区");

    public static StockoutOrderItemEntity buildOrderItem(StockoutOrderItemInfo optionalOutOrderItem, String userName, Integer qty) {
        StockoutOrderItemEntity stockoutOrderItemEntity = new StockoutOrderItemEntity();
        stockoutOrderItemEntity.setStockoutOrderItemId(optionalOutOrderItem.getStockoutOrderItemId());
        stockoutOrderItemEntity.setUpdateBy(userName);
        stockoutOrderItemEntity.setShipmentQty(qty);
        if (stockoutOrderItemEntity.getShipmentQty() < 0) {
            stockoutOrderItemEntity.setShipmentQty(0);
        }
        return stockoutOrderItemEntity;
    }

    public static StockoutShipmentItemEntity buildByStockoutOrderItemAndQty(StockoutOrderItemInfo stockoutOrderItemEntity, Integer qty, String stockoutOrderNo, Integer shipmentId, StockoutBatchOrderEntity batchOrderEntity) {
        StockoutShipmentItemEntity result = new StockoutShipmentItemEntity();
        result.setLocation(stockoutOrderItemEntity.getLocation());
        result.setStockoutOrderNo(stockoutOrderNo);
        result.setStockoutOrderItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
        result.setSellerBarcode(stockoutOrderItemEntity.getSellerBarcode());
        result.setSellerSku(stockoutOrderItemEntity.getSellerSku());
        result.setOrderNo(stockoutOrderItemEntity.getOrderNo());
        result.setOrderItemId(stockoutOrderItemEntity.getOrderItemId());
        result.setSpecId(stockoutOrderItemEntity.getSpecId());
        result.setSku(stockoutOrderItemEntity.getSku());
        result.setQty(qty);
        result.setShipmentId(shipmentId);
        result.setBatchId(batchOrderEntity.getBatchId());
        return result;
    }

    public static StockoutShipmentEntity buildUpdateShipment(Integer shipmentId, String userName) {
        StockoutShipmentEntity updateEntity = new StockoutShipmentEntity();
        updateEntity.setShipmentId(shipmentId);
        updateEntity.setStatus(StockoutShipmentStatusEnum.SHIPPED.name());
        updateEntity.setUpdateBy(userName);
        return updateEntity;
    }

    public static StockoutShipmentEntity buildSortShipment(StockoutOrderEntity stockoutOrderEntity, String name, String shipmentBoxCode) {
        StockoutShipmentEntity stockoutShipmentEntity = new StockoutShipmentEntity(); // 生成装箱清单
        BeanUtilsEx.copyProperties(stockoutOrderEntity, stockoutShipmentEntity);
        stockoutShipmentEntity.setShipmentBoxCode(shipmentBoxCode);
        stockoutShipmentEntity.setCreateBy(name);
        stockoutShipmentEntity.setUpdateBy(name);
        stockoutShipmentEntity.setShipmentDate(new Date());
        stockoutShipmentEntity.setBoxIndex(1);
        stockoutShipmentEntity.setPackType(StockoutPackingTypeEnum.CARTON.name());
        stockoutShipmentEntity.setIsDeleted(IsEnum.IS_NOT.getCode());
        stockoutShipmentEntity.setStatus(StockoutShipmentStatusEnum.PACKING.name());
        //宓思新补货流程
        if (StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(stockoutOrderEntity.getWorkspace())
                && FbaReplenishTypeEnum.REPLENISH.getName().equalsIgnoreCase(stockoutOrderEntity.getFbaReplenishType())) {
            stockoutShipmentEntity.setReplenishOrderStatus(ReplenishOrderStatusEnum.WAIT_DEAL.name());
            stockoutShipmentEntity.setFbaLabelStatus(FbaLabelStatusEnum.INIT.name());
        }
        return stockoutShipmentEntity;
    }

    public static StockoutShipmentPackEntity buildSortShipmentPack(StockoutOrderEntity stockoutOrderEntity, String name, String shipmentBoxCode) {
        StockoutShipmentPackEntity stockoutShipmentEntity = new StockoutShipmentPackEntity(); // 生成装箱清单
        BeanUtilsEx.copyProperties(stockoutOrderEntity, stockoutShipmentEntity);
        stockoutShipmentEntity.setShipmentBoxCode(shipmentBoxCode);
        stockoutShipmentEntity.setCreateBy(name);
        stockoutShipmentEntity.setUpdateBy(name);
        stockoutShipmentEntity.setShipmentDate(new Date());
        stockoutShipmentEntity.setBoxIndex(1);
        stockoutShipmentEntity.setPackType(StockoutPackingTypeEnum.CARTON.name());
        stockoutShipmentEntity.setIsDeleted(IsEnum.IS_NOT.getCode());
        stockoutShipmentEntity.setStatus(StockoutShipmentStatusEnum.PACKING.name());
        return stockoutShipmentEntity;
    }

    public static StockInternalBoxItemEntity buildBoxItem(Integer spaceId, StockInternalBoxEntity stockInternalBoxEntity, ProductSpecInfoEntity productSpecInfoEntity, Integer batchId) {
        StockInternalBoxItemEntity stockInternalBoxItemEntity = new StockInternalBoxItemEntity();
        stockInternalBoxItemEntity.setSpaceId(spaceId);
        stockInternalBoxItemEntity.setInternalBoxId(stockInternalBoxEntity.getInternalBoxId());
        stockInternalBoxItemEntity.setInternalBoxCode(stockInternalBoxEntity.getInternalBoxCode());
        stockInternalBoxItemEntity.setProductId(productSpecInfoEntity.getProductId());
        stockInternalBoxItemEntity.setSpecId(productSpecInfoEntity.getSpecId());
        stockInternalBoxItemEntity.setSku(productSpecInfoEntity.getSku());
        stockInternalBoxItemEntity.setBatchId(batchId);
        stockInternalBoxItemEntity.setQty(0);
        return stockInternalBoxItemEntity;
    }

    public static String buildFbaHtml(StockoutOrderItemEntity itemById, PrintTemplateEntity template) {
        return PrintTransferUtils.transfer(template.getContent(), buildSkuPrintInfo(itemById, null));
    }

    public static String buildFbaWithSupplierNameHtml(StockoutOrderItemEntity itemById, String supplierName, PrintTemplateEntity template) {
        return PrintTransferUtils.transfer(template.getContent(), buildSkuPrintInfo(itemById, supplierName));
    }

    public static CustomerSkuPrintDTO buildSkuPrintInfo(StockoutOrderItemEntity itemById, String supplierName) {
        CustomerSkuPrintDTO dto = new CustomerSkuPrintDTO();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        dto.setPrintDate(simpleDateFormat.format(new Date()));
        dto.setSku(itemById.getSku());
        dto.setBarcode(itemById.getBarcode());
        dto.setSellerSku(itemById.getSellerSku());
        dto.setSellerBarcode(itemById.getSellerBarcode());
        String sellerTitle = itemById.getSellerTitle();
        if (StringUtils.hasText(sellerTitle) && sellerTitle.length() > 30) {
            dto.setSellerTitle(sellerTitle.substring(0, 15) + "..." + sellerTitle.substring(sellerTitle.length() - 15));
        } else {
            dto.setSellerTitle(sellerTitle);
        }
        ProductSpecInfoService productSpecInfoService = SpringContextHolder.getBean(ProductSpecInfoService.class);
        ProductSpecInfo specInfo = productSpecInfoService.getBySku(itemById.getSku());
        if (Objects.nonNull(specInfo)) {
            dto.setColor(specInfo.getColor());
            dto.setSize(specInfo.getSize());
        }
        dto.setSupplierName(supplierName);
        dto.setSellerTitleOrigin(itemById.getSellerTitle());
        dto.setSellerText(itemById.getSellerText());
        return dto;
    }

    public static StockoutOrderScanTaskEntity scanTaskEntity(StockoutOrderScanTask scanTask, String name, int lackSum) {
        StockoutOrderScanTaskEntity scanTaskEntity = new StockoutOrderScanTaskEntity();
        scanTaskEntity.setTaskId(scanTask.getTaskId());
        scanTaskEntity.setIsLack(lackSum > 0 ? 1 : 0);
        scanTaskEntity.setOperateEndDate(new Date());
        scanTaskEntity.setStatus(StockoutOrderScanTaskStatusEnum.REVIEWED.name());
        scanTaskEntity.setUpdateBy(name);
        return scanTaskEntity;
    }

    public static StockoutOrderScanTaskItemEntity scanTaskItemEntity(StockoutOrderScanTaskItem o, String name) {
        StockoutOrderScanTaskItemEntity scanTaskItemEntity = new StockoutOrderScanTaskItemEntity();
        scanTaskItemEntity.setTaskItemId(o.getTaskItemId());
        scanTaskItemEntity.setUpdateBy(name);
        scanTaskItemEntity.setIsLack(1);
        scanTaskItemEntity.setLackQty(o.getExpectedQty());
        return scanTaskItemEntity;
    }

    // 复核扫描完成返回的response
    public static StockoutConfirmShipmentResponse stockoutConfirmShipmentResponse(StockoutOrderScanTaskValidateResponse validateResponse) {
        StockoutConfirmShipmentResponse response = new StockoutConfirmShipmentResponse();
        response.setCompleteScan(Boolean.TRUE);
        if (StockoutOrderTypeEnum.PROCESS_DELIVERY.name().equals(validateResponse.getStockoutOrderInfo().getStockoutType())) {
            response.setProcessWork(Boolean.TRUE);
        }
        response.setStockoutType(validateResponse.getStockoutOrderInfo().getStockoutType());
        response.setStockoutOrderInfo(validateResponse.getStockoutOrderInfo());
        return response;
    }

    public static List<String> stockoutOrderStatus() {
        List<String> statusList = new LinkedList<>();
        statusList.add(StockoutOrderStatusEnum.WAIT_PRE_MATCH.name());
        statusList.add(StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.name());
        statusList.add(StockoutOrderStatusEnum.READY.name());
        statusList.add(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name());
        statusList.add(StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
        statusList.add(StockoutOrderStatusEnum.READY_PICK.name());
        statusList.add(StockoutOrderStatusEnum.PICKING.name());
        statusList.add(StockoutOrderStatusEnum.READY_OUTBOUND.name());
        statusList.add(StockoutOrderStatusEnum.OUTBOUNDING.name());
        statusList.add(StockoutOrderStatusEnum.READY_DELIVERY.name());
        return statusList;
    }

    public static StockoutOrderStatusResponse stockoutOrderStatusResponse(List<StockoutStatusCount> statusCountList) {
        StockoutOrderStatusResponse response = new StockoutOrderStatusResponse();
        for (StockoutStatusCount statusCount : statusCountList) {
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.READY.name()))
                response.setReadyNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name()))
                response.setLogisticsGetFailNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.WAIT_PRE_MATCH.name()))
                response.setUnFullPreMatchNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.UN_FULL_PRE_MATCH.name()))
                response.setUnFullPreMatchNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.READY_WAVE_GENERATED.name()))
                response.setReadyWaveNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.READY_PICK.name()))
                response.setReadyPickNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.PICKING.name()))
                response.setPickingNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.READY_OUTBOUND.name()))
                response.setReadyOutBoundNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.OUTBOUNDING.name()))
                response.setOutBoundNum(statusCount.getCountNum());
            if (statusCount.getStatus().equals(StockoutOrderStatusEnum.READY_DELIVERY.name()))
                response.setReadyDeliveryNum(statusCount.getCountNum());
        }
        return response;
    }

    public static void stockoutOrderScanTaskPrintInfo(StockoutOrderScanTaskPrintInfo item, Map<String, StockoutOrderScanTaskOrderInfo> map) {
        item.setTaskId(map.get(item.getStockoutOrderNo()).getTaskId());
        item.setQty(map.get(item.getStockoutOrderNo()).getQty());
        item.setProductWeight(map.get(item.getStockoutOrderNo()).getProductWeight());
        item.setWeight(map.get(item.getStockoutOrderNo()).getProductWeight());
        if (!Objects.isNull(map.get(item.getStockoutOrderNo()).getProductWeight()))
            item.setProductWeight(map.get(item.getStockoutOrderNo()).getProductWeight().divide(BigDecimal.valueOf(1000), BigDecimal.ROUND_HALF_UP));
        if (!Objects.isNull(item.getEstimateWeight()))
            item.setEstimateWeight(item.getEstimateWeight().divide(BigDecimal.valueOf(1000), BigDecimal.ROUND_HALF_UP));
        item.setPrintDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
    }

    public static void buildRequest(StockoutShipmentSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getOrderNo())
                && request.getOrderNo().startsWith(FormNoTypeEnum.FBA_REPLENISH_ORDER.getPrefix())) {
            request.setReplenishOrder(request.getOrderNo());
            request.setOrderNo(null);
        }

        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getOrderNo())) {
            if (CollectionUtils.isEmpty(request.getOrderNos())) {
                request.setOrderNos(new ArrayList<>(Collections.singletonList(request.getOrderNo())));
            } else {
                request.getOrderNos().add(request.getOrderNo());
            }
        }
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getStatusSingle())) {
            if (CollectionUtils.isEmpty(request.getStatus())) {
                request.setStatus(new ArrayList<>(Collections.singletonList(request.getStatusSingle())));
            } else {
                request.getStatus().add(request.getStatusSingle());
            }
        }
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getLogisticNo())) {
            if (CollectionUtils.isEmpty(request.getLogisticNos())) {
                request.setLogisticNos(new ArrayList<>(Collections.singletonList(request.getLogisticNo())));
            } else {
                request.getLogisticNos().add(request.getLogisticNo());
            }
        }
    }

    public static String getInvoiceTitle(String invoiceTitle, String countryCode) {
        if (Objects.equals(invoiceTitle, InvoiceTemplateEnum.SHIYING_INVOICE_COMMERCIAL.name())
                || Objects.equals(invoiceTitle, InvoiceTemplateEnum.DETAIL_INVOICE_COMMERCIAL.name())) {
            return "Commercial   invoice";
        }
        if ("BR".equalsIgnoreCase(countryCode) || "BS".equalsIgnoreCase(countryCode)
                || "AG".equalsIgnoreCase(countryCode) || "ZA".equalsIgnoreCase(countryCode)
                || "AT".equalsIgnoreCase(countryCode) || "PT".equalsIgnoreCase(countryCode)
                || "PE".equalsIgnoreCase(countryCode) || "GT".equalsIgnoreCase(countryCode)) {
            return "Commercial   invoice"; //Commercial Invoice
        }
        if ("IN".equalsIgnoreCase(countryCode) || "CR".equalsIgnoreCase(countryCode))
            return "invoice"; //Invoice
        return "PROFORMA   INVOICE"; //形式发票
    }

    // 单双奇偶打印
    public static List<StockoutBatchSplitTaskPrintResponse> buildPrintListInOrderSplit(List<StockoutBatchSplitTaskPrintResponse> responseList) {
        List<StockoutBatchSplitTaskPrintResponse> sortList = responseList.stream().sorted(Comparator.comparing(StockoutBatchSplitTaskPrintResponse::getOutlet)).collect(Collectors.toList());
        List<StockoutBatchSplitTaskPrintResponse> oddPrintList = new ArrayList<>();
        List<StockoutBatchSplitTaskPrintResponse> evenPrintList = new ArrayList<>();
        sortList.forEach(print -> {
            if (print.getOutlet() != null && (print.getOutlet() & 1) == 1) {
                oddPrintList.add(print);
            } else
                evenPrintList.add(print);
        });
        oddPrintList.addAll(evenPrintList);
        return oddPrintList;
    }

    public static void buildPrintListInOrder(List<StockoutBatchSplitPrintSort> printSortList, List<String> htmlList) {
        // 单双打印，且一式两份  奇数偶数 先打印分拣口为1、3、5、7，后打印2、4、6、8
        List<StockoutBatchSplitPrintSort> sortList = printSortList.stream().sorted(Comparator.comparingInt(StockoutBatchSplitPrintSort::getBoxNoIndex)).collect(Collectors.toList());
        List<StockoutBatchSplitPrintSort> oddPrintList = new ArrayList<>();
        List<StockoutBatchSplitPrintSort> evenPrintList = new ArrayList<>();
        sortList.forEach(print -> {
            if (StringUtils.hasText(print.getBoxNo()) && (print.getBoxNoIndex() & 1) == 1) {
                oddPrintList.add(print);
            } else
                evenPrintList.add(print);
        });
        oddPrintList.addAll(evenPrintList);
        oddPrintList.forEach(printModel -> {
            htmlList.add(printModel.getHtml());
            if (StockoutOrderWorkSpaceEnum.B2B_AREA.getName().equals(printModel.getWorkspace()))
                htmlList.add(printModel.getHtml());
        });
    }

    public static List<Map<String, Object>> getOrderNoImgList(List<String> orderNoList) {
        List<Map<String, Object>> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderNoList)) {
            orderNoList.forEach(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("orderNo", item);
                map.put("orderNoImg", BarCodeUtils.getBarCodeBase64(item));
                list.add(map);
            });
        }
        return list;
    }

    public static StockoutOrderScanTaskItemDetail retryScanItem(List<StockoutOrderScanTaskItem> scanTaskItemList, StockoutShipmentItemEntity itemEntity) {
        Optional<StockoutOrderScanTaskItem> any = scanTaskItemList.stream().filter(item -> item.getOrderItemId().equals(itemEntity.getOrderItemId())
                && item.getSku().equals(itemEntity.getSku())).findAny();
        StockoutOrderScanTaskItemDetail detail = new StockoutOrderScanTaskItemDetail();
        detail.setExpectedQty(any.get().getExpectedQty());
        detail.setOrderItemId(itemEntity.getOrderItemId());
        detail.setScanQty(any.get().getShipmentQty() - itemEntity.getQty());
        detail.setTaskItemId(any.get().getTaskItemId());
        detail.setSku(itemEntity.getSku());
        return detail;
    }

    public static BigDecimal getPayWeight(List<StockoutOrderItemEntity> orderItemEntityList, List<ProductSpecInfoEntity> allBySkuIn) {
        BigDecimal result = BigDecimal.ZERO;
        Map<String, List<ProductSpecInfoEntity>> collect = allBySkuIn.stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku));
        for (StockoutOrderItemEntity item : orderItemEntityList) {
            ProductSpecInfoEntity specInfoEntity = collect.get(item.getSku()).get(0);
            result = result.add(ProductSpecInfoService.getSkuWeightDefault(specInfoEntity).multiply(new BigDecimal(item.getQty())));
        }
        return result.divide(new BigDecimal(1000));
    }

    public static BigDecimal getPayWeightByShipment(List<StockoutShipmentItemEntity> shipmentItemEntityList, List<ProductSpecInfoEntity> allBySkuIn) {
        BigDecimal result = BigDecimal.ZERO;
        Map<String, List<ProductSpecInfoEntity>> collect = allBySkuIn.stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku));
        for (StockoutShipmentItemEntity item : shipmentItemEntityList) {
            ProductSpecInfoEntity specInfoEntity = collect.get(item.getSku()).get(0);
            result = result.add(ProductSpecInfoService.getSkuWeightDefault(specInfoEntity).multiply(new BigDecimal(item.getQty())));
        }
        return result.divide(new BigDecimal(1000));
    }

    public static void getItemList(Map<String, Object> mapRes, List<StockoutPickingTaskItemInfo> subList,
                                   Map<String, Integer> storeFirstOrderMap, Map<String, List<String>> productTagMap,
                                   Set<String> transparencySet, Map<String, String> productVersionMap,
                                   Map<Integer, String> productMap, Map<String, Boolean> isLeadGenerationMap,
                                   Map<String, String> vasTypeMap) {
        if (CollectionUtils.isEmpty(subList))
            return;
        List<StockoutPickingTaskItemInfo> mergeList = new ArrayList<>(subList.stream().collect(Collectors
                .toMap(it -> it.getPositionCode() + "#" + it.getSku(), a -> a, (o1, o2) -> {
                    StockoutPickingTaskItemInfo req = new StockoutPickingTaskItemInfo();
                    o1.setExpectedQty(o1.getExpectedQty() + o2.getExpectedQty());
                    BeanUtilsEx.copyProperties(o1, req);
                    return req;
                })).values());
        if (LocationEnum.MISI.name().equals(TenantContext.getTenant())) {
            //宓思 PR仓库区创建错误 ，需要特殊排序逻辑
            mergeList.sort(Comparator.comparing(StockoutPickingTaskItemInfo::getSpaceAreaName, StockoutBuilding::sortCompare)
                    .thenComparing(Function.identity(), StockoutBuilding::misiPositionCompareSort)
                    .thenComparing(StockoutPickingTaskItemInfo::getPositionCode)
                    .thenComparing(StockoutPickingTaskItemInfo::getSku));
        } else
            mergeList.sort(Comparator.comparing(StockoutPickingTaskItemInfo::getSpaceAreaName, StockoutBuilding::sortCompare)
                    .thenComparing(StockoutPickingTaskItemInfo::getPositionCode)
                    .thenComparing(StockoutPickingTaskItemInfo::getSku));
        // 真空需要额外提醒
        String workspace = (String) mapRes.get("workspace");
        List<Map<String, Object>> itemList = mergeList.stream().map(entity -> {
            Map<String, Object> map = new HashMap<>(4);
            map.put("positionCode", entity.getPositionCode());
            map.put("sku", entity.getSku());
            String productVersion = productVersionMap.get(entity.getSku());
            map.put("productVersion", productVersion);
            map.put("qty", String.valueOf(entity.getExpectedQty()));
            map.put("vasType", !StringUtils.hasText(vasTypeMap.get(entity.getSku())) ? "" : "(" + vasTypeMap.get(entity.getSku()) + ")");
            if (StringUtils.hasText(workspace) && workspace.contains("FBA")) {
                List<String> productTag = productTagMap.getOrDefault(entity.getSku(), Collections.emptyList());
                // 首单和商品标签需要展示
                Integer orderFirstIn = storeFirstOrderMap.getOrDefault(entity.getSku(), 0);
                map.put("storeOrderFirst", orderFirstIn == 1 ? "(首单)" : "");
                map.put("productTag", CollectionUtils.isEmpty(productTag) ? "" : "(" + String.join(",", productTag) + ")");
                map.put("transparency", transparencySet.contains(entity.getSku()) ? "(T)" : "");
            }
            map.put("isLeadGeneration", isLeadGenerationMap.getOrDefault(entity.getSku(), Boolean.FALSE) ? "引流款" : null);
            if (isLeadGenerationMap.getOrDefault(entity.getSku(), Boolean.FALSE) || storeFirstOrderMap.getOrDefault(entity.getSku(), 0) == 1) {
                String packageVacuum = productMap.getOrDefault(entity.getProductId(), "");
                map.put("packageVacuum", String.format("%s%s", StringUtils.hasText(packageVacuum) ? ";" : "", packageVacuum));
            }
            return map;
        }).collect(Collectors.toList());
        mapRes.put("itemListAll", itemList);
        if (itemList.size() > 1) {
            mapRes.put("itemList1", itemList.subList(0, (itemList.size() + 1) / 2));
            mapRes.put("itemList2", itemList.subList((itemList.size() + 1) / 2, itemList.size()));
        } else {
            mapRes.put("itemList1", itemList);
            mapRes.put("itemList2", new ArrayList<>());
        }
    }

    private static int sortCompare(String x, String y) {
        if (x == null && y != null) {
            return 1;
        } else if (x != null && y == null) {
            return -1;
        } else if (x == null) {
            return 0;
        } else if (("G".equals(x) || x.contains("ZS")) && ("G".equals(y) || y.contains("ZS"))) {
            return 0; // 两个都是G/ZS开头
        } else if (!"G".equals(x) && !x.contains("ZS") && !"G".equals(y) && !y.contains("ZS")) {
            return 0; // 两个都不是G/ZS开头
        } else if ("G".equals(x) || x.contains("ZS")) {
            return -1; // x是G/ZS开头
        } else {
            return 1;
        }
    }

    private static int misiPositionCompareSort(StockoutPickingTaskItemInfo item1, StockoutPickingTaskItemInfo item2) {

        if (!item1.getSpaceAreaName().equals(item2.getSpaceAreaName())
                || !misiPrSpaceArea.contains(item1.getSpaceAreaName())
                || !misiPrSpaceArea.contains(item2.getSpaceAreaName()))
            return item1.getPositionCode().compareTo(item2.getPositionCode());


        return item1.getSort().compareTo(item2.getSort());
    }

    public static DownloadResponse buildBatchExportData(PageResponse<StockoutBatchResponse> pageResponse) {
        DownloadResponse response = new DownloadResponse();
        List<StockoutBatchListExport> resultList = pageResponse.getContent().stream().map(item -> {
            StockoutBatchListExport export = new StockoutBatchListExport();
            BeanUtilsEx.copyProperties(item, export);
            return export;
        }).collect(Collectors.toList());
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }

    public static void stockLogSetBasicInfo(StockChangeLogAddRequest logAddRequest, StockUpdateRequest stockUpdateRequest, Integer oldStock, Integer newStock) {
        logAddRequest.setStockChangeLogType(stockUpdateRequest.getChangeLogType().name());
        logAddRequest.setTypeModule(stockUpdateRequest.getTypeModule().name());
        logAddRequest.setQty(stockUpdateRequest.getQty());
        logAddRequest.setOldStock(oldStock);
        logAddRequest.setNewStock(newStock);
        // 出库相关
        if (stockUpdateRequest.getBatchId() != null || com.nsy.api.core.apicore.util.StringUtils.hasText(stockUpdateRequest.getStockoutOrderNo())) {
            StockoutAddRequest stockoutInfo = new StockoutAddRequest();
            stockoutInfo.setStockoutOrderNo(stockUpdateRequest.getStockoutOrderNo());
            stockoutInfo.setBatchId(stockUpdateRequest.getBatchId());
            stockoutInfo.setPickingTaskId(stockUpdateRequest.getPickingTaskId());
            stockoutInfo.setSplitTaskId(stockUpdateRequest.getSplitTaskId());
            logAddRequest.setStockoutInfo(stockoutInfo);
        }
        // 入库相关
        if (stockUpdateRequest.getStockInOrderNo() != null) {
            StockinAddRequest stockinInfo = new StockinAddRequest();
            stockinInfo.setStockinOrderNo(stockUpdateRequest.getStockInOrderNo());
            stockinInfo.setPurchasePlanNo(stockUpdateRequest.getPurchasePlanNo());
            logAddRequest.setStockinInfo(stockinInfo);
        }
        // 盘点相关
        if (stockUpdateRequest.getTakeTaskId() != null) {
            StockTakeStockAddRequest takeStockInfo = new StockTakeStockAddRequest();
            takeStockInfo.setTakeTaskId(stockUpdateRequest.getTakeTaskId());
            logAddRequest.setTakeStockInfo(takeStockInfo);
        }
    }

    public static LambdaQueryWrapper<StockoutOrderLackItemEntity> buildByOrderLackIdAndSpecIdAndStatusList(Integer stockoutOrderLackId, Integer specId, List<String> statusList) {
        LambdaQueryWrapper<StockoutOrderLackItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(stockoutOrderLackId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getStockoutOrderLackId, stockoutOrderLackId);
        if (Objects.nonNull(specId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getSpecId, specId);
        if (!CollectionUtils.isEmpty(statusList))
            queryWrapper.in(StockoutOrderLackItemEntity::getStatus, statusList);
        return queryWrapper;
    }

    public static List<StockoutShipmentCountResponse> stockoutShipmentTabCount() {
        List<StockoutShipmentCountResponse> list = Arrays.stream(StockoutShipmentStatusEnum.values()).map(statusEnum -> {
            StockoutShipmentCountResponse response = new StockoutShipmentCountResponse();
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getName());
            return response;
        }).collect(Collectors.toList());
        StockoutShipmentCountResponse response = new StockoutShipmentCountResponse();
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    public static List<ShipmentOrderVO> shipmentOrderList(List<StockoutShipmentItemEntity> itemEntityList) {
        Map<String, List<StockoutShipmentItemEntity>> collect = itemEntityList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getOrderNo));
        List<ShipmentOrderVO> list = new ArrayList<>();
        collect.forEach((key, value) -> {
            ShipmentOrderVO vo = new ShipmentOrderVO();
            vo.setOrderNo(key);
            vo.setQty(value.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum());
            list.add(vo);
        });
        return list;
    }

    public static List<StockoutShipmentCountResponse> stockoutShipmentPackTab() {
        List<StockoutShipmentCountResponse> list = Arrays.stream(StockoutShipmentPackStatusEnum.values()).map(statusEnum -> {
            StockoutShipmentCountResponse response = new StockoutShipmentCountResponse();
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getName());
            return response;
        }).collect(Collectors.toList());
        StockoutShipmentCountResponse response = new StockoutShipmentCountResponse();
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    public static List<ShipmentOrderVO> shipmentPackOrderList(List<StockoutShipmentPackItemEntity> itemEntityList) {
        Map<String, List<StockoutShipmentPackItemEntity>> collect = itemEntityList.stream().collect(Collectors.groupingBy(StockoutShipmentPackItemEntity::getOrderNo));
        List<ShipmentOrderVO> list = new ArrayList<>();
        collect.forEach((key, value) -> {
            ShipmentOrderVO vo = new ShipmentOrderVO();
            vo.setOrderNo(key);
            vo.setQty(value.stream().mapToInt(StockoutShipmentPackItemEntity::getQty).sum());
            list.add(vo);
        });
        return list;
    }

    public static String setSpaceMemo(String spaceMemo, String sku, String replaceSku, String changeType) {
        // 仓库最大长度备注
        int maxLength = 300;
        // 若存在换码，则追加换码备注
        String result = com.nsy.api.core.apicore.util.StringUtils.hasText(replaceSku) ? spaceMemo + String.format("规格编码 %s 改码 %s;", sku, replaceSku) : spaceMemo;
        // 若存在增加吊牌，则追加增加吊牌备注
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(changeType) && !StockoutSewTaskTypeEnum.CHANGE.name().equals(changeType)) {
            result = result + "增加吊牌;";
        }
        result = result.replaceAll("<br>", "");
        if (result.length() > maxLength) {
            result = result.substring(0, maxLength - 30) + "...(请到erp系统查看详细备注！)";
        }
        return result;
    }

    public static List<StockoutDeliverProductPrint> buildDeliverProductPrintList(List<ProductSpecInfoEntity> list, List<StockoutShipmentItemEntity> groupList) {
        List<StockoutDeliverProductPrint> deliverProductPrintList = new ArrayList<>();
        for (ProductSpecInfoEntity specInfo : list) {
            for (StockoutShipmentItemEntity shipmentItemEntity : groupList) {
                if (specInfo.getSpecId().equals(shipmentItemEntity.getSpecId())) {
                    StockoutDeliverProductPrint deliverProductPrint = new StockoutDeliverProductPrint();
                    BeanUtilsEx.copyProperties(specInfo, deliverProductPrint);
                    deliverProductPrint.setQty(shipmentItemEntity.getQty());
                    deliverProductPrintList.add(deliverProductPrint);
                }
            }
        }
        return deliverProductPrintList;
    }

    public static List<String> resetStockoutOrderNos(List<String> stockoutOrderNos) {
        List<String> resultList = new ArrayList<>();
        stockoutOrderNos.forEach(s -> resultList.addAll(Arrays.asList(s.split(","))));
        return resultList.stream().distinct().collect(Collectors.toList());
    }

    public static List<StockoutShipmentCountResponse> stockTransferTrackStatus() {
        List<StockoutShipmentCountResponse> list = Arrays.stream(StockTransferTrackingStatusEnum.values()).map(statusEnum -> {
            StockoutShipmentCountResponse response = new StockoutShipmentCountResponse();
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getTransferStatus());
            return response;
        }).collect(Collectors.toList());
        StockoutShipmentCountResponse response = new StockoutShipmentCountResponse();
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    public static StockoutPickingTaskEntity buildByStockoutBatch(StockoutBatchEntity stockoutBatchEntity) {
        StockoutPickingTaskEntity taskEntity = new StockoutPickingTaskEntity();
        taskEntity.setLocation(stockoutBatchEntity.getLocation());
        taskEntity.setBatchId(stockoutBatchEntity.getBatchId());
        taskEntity.setSpaceId(stockoutBatchEntity.getSpaceId());
        taskEntity.setScanType(stockoutBatchEntity.getScanType());
        taskEntity.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
        return taskEntity;
    }

    public static StockoutScanDetailMessage detailMessage(StockoutShipmentItemEntity itemEntity, int i, String shipmentBox, String cause) {
        StockoutScanDetailMessage message = new StockoutScanDetailMessage();
        message.setShipmentItem(itemEntity);
        message.setQty(i);
        message.setShipmentBoxCode(shipmentBox);
        message.setCause(cause);
        return message;
    }

    public static StockoutPickingTaskItemEntity buildByPickingTaskItemInfo(StockoutPickingTaskEntity pickingTaskEntity, StockoutPickingTaskItemInfo taskItemInfo) {
        StockoutPickingTaskItemEntity taskItemEntity = new StockoutPickingTaskItemEntity();
        BeanUtils.copyProperties(taskItemInfo, taskItemEntity);
        taskItemEntity.setTaskId(pickingTaskEntity.getTaskId());
        taskItemEntity.setLocation(pickingTaskEntity.getLocation());
        taskItemEntity.setPickedQty(0);
        taskItemEntity.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
        taskItemEntity.setIsLack(0);
        return taskItemEntity;
    }

    // 创建谷仓/左海跟踪明细
    public static StockTransferTrackingItemEntity buildTrackingItem(String sku, Integer qty, StockTransferTrackingEntity trackingEntity, Integer spaceId, String positionCode) {
        StockTransferTrackingItemEntity itemEntity = new StockTransferTrackingItemEntity();
        itemEntity.setTrackingId(trackingEntity.getId());
        itemEntity.setSku(sku);
        // 谷仓库位
        itemEntity.setSpaceId(spaceId);
        itemEntity.setShelvedPositionCode(positionCode);
        itemEntity.setStockOutQty(qty);
        itemEntity.setCreateBy(trackingEntity.getCreateBy());
        return itemEntity;
    }

    public static StockoutShipmentSearchResult shipmentSearchResult(StockoutShipmentEntity shipment) {
        StockoutShipmentSearchResult result = new StockoutShipmentSearchResult();
        result.setReplenishOrder(shipment.getReplenishOrder());
        result.setFbaReplenishType(shipment.getFbaReplenishType());
        result.setShipmentId(shipment.getShipmentId());
        result.setShipmentBoxCode(shipment.getShipmentBoxCode());
        result.setBoxIndex(shipment.getBoxIndex());
        result.setBoxSize(shipment.getBoxSize());
        result.setLogisticsCompany(shipment.getLogisticsCompany());
        result.setLogisticsNo(shipment.getLogisticsNo());
        result.setForwarderChannel(shipment.getForwarderChannel());
        result.setTransferLogisticsCompany(shipment.getTransferLogisticsCompany());
        result.setTransferLogisticsNo(shipment.getTransferLogisticsNo());
        result.setStatus(shipment.getStatus());
        result.setDeliveryDate(shipment.getDeliveryDate());
        result.setWeight(shipment.getWeight());
        result.setVolumeWeight(shipment.getVolumeWeight());
        result.setRemark(shipment.getRemark());
        result.setIsDeleted(shipment.getIsDeleted());
        result.setCreateBy(shipment.getCreateBy());
        result.setCreateDate(shipment.getCreateDate());
        result.setUpdateBy(shipment.getUpdateBy());
        result.setUpdateDate(shipment.getUpdateDate());
        return result;
    }

    public static void buildAmazonListRequest(StockoutShipmentSearchRequest request) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(request.getOrderNo()) && request.getOrderNo().startsWith(FormNoTypeEnum.FBA_REPLENISH_ORDER.getPrefix())) {
            request.setReplenishOrder(request.getOrderNo());
            request.setOrderNo(null);
        }
        if (CollectionUtils.isEmpty(request.getStatus())) {
            List<String> arrayList = new ArrayList<>();
            arrayList.add(StockoutShipmentStatusEnum.PACKING_END.name());
            arrayList.add(StockoutShipmentStatusEnum.SHIPPED.name());
            request.setStatus(arrayList);
        }
        buildRequest(request);
    }

    public static LogisticsDocumentsShipmentInfo logisticsShipmentInfo(StockoutShipmentItem item) {
        LogisticsDocumentsShipmentInfo shipmentInfo = new LogisticsDocumentsShipmentInfo();
        shipmentInfo.setShipmentId(item.getShipmentId());
        shipmentInfo.setShipmentBoxCode(item.getShipmentBoxCode());
        shipmentInfo.setBoxIndex(item.getBoxIndex());
        shipmentInfo.setOrderNo(item.getOrderNo());
        shipmentInfo.setWeight(item.getWeight());
        shipmentInfo.setBoxSize(item.getBoxSize());
        return shipmentInfo;
    }

    public static Map<String, String> getDeclareUnitMap() {
        Map<String, String> map = new HashMap<>(22);
        map.put("001", "台");
        map.put("006", "套");
        map.put("007", "个");
        map.put("008", "只");
        map.put("010", "张");
        map.put("011", "件");
        map.put("012", "支");
        map.put("013", "枝");
        map.put("014", "根");
        map.put("015", "条");
        map.put("016", "把");
        map.put("017", "块");
        map.put("018", "卷");
        map.put("019", "副");
        map.put("020", "片");
        map.put("021", "组");
        map.put("022", "份");
        map.put("023", "幅");
        map.put("025", "双");
        map.put("026", "对");
        map.put("035", "千克");
        map.put("036", "克");
        return map;
    }

    public static void setBaseItem(ProductSpecInfoEntity specInfoEntity, StockoutOrderItemAddRequest itemAddRequest, StockoutOrderItemEntity itemEntity) {
        itemEntity.setProductId(specInfoEntity.getProductId());
        itemEntity.setSpecId(specInfoEntity.getSpecId());
        itemEntity.setSku(specInfoEntity.getSku());
        itemEntity.setBarcode(specInfoEntity.getBarcode());
        itemEntity.setScanQty(0);
        itemEntity.setShipmentQty(0);
        itemEntity.setLack(Boolean.FALSE);
        itemEntity.setShipmentQty(0);
        itemEntity.setOrderPayTime(itemAddRequest.getPayTime());
        itemEntity.setIsNeedProcess(itemAddRequest.getIsProcess());
        itemEntity.setIsFirstOrderByStore(itemAddRequest.getIsFirstOrderByStore());
    }
}
