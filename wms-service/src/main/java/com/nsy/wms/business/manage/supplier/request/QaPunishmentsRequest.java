package com.nsy.wms.business.manage.supplier.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-05-20 9:14
 */
public class QaPunishmentsRequest {

    /**
     * 商品编码
     */
    private String spu;

    /**
     * 供应商ID
     */
    private Integer supplierId;

    /**
     * 奖惩日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date rewardAndPunishmentsDate;

    /**
     * 奖惩类型：重大品质问题、批量条码错、虚假发货、未返工直接发货、退货运费补偿、特批入库扣罚、超期罚款、准交奖励、货款补偿/退回
     */
    private String rewardAndPunishmentsType;

    /**
     * 奖惩原因
     */
    private String rewardAndPunishmentsReason;
    /**
     * 附件list
     */
    private List<QaPunishmentsAttachRequest> rewardsAndPunishmentsAttaches;
    /**
     * 数量(仅作为数据备注，不参与任何计算)
     */
    private Integer rewardAndPunishmentsCount;
    /**
     * 结算金额
     */
    private BigDecimal confirmTotalMoney;

    /**
     * 备注
     */
    private String remark;

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Date getRewardAndPunishmentsDate() {
        return rewardAndPunishmentsDate;
    }

    public void setRewardAndPunishmentsDate(Date rewardAndPunishmentsDate) {
        this.rewardAndPunishmentsDate = rewardAndPunishmentsDate;
    }

    public String getRewardAndPunishmentsType() {
        return rewardAndPunishmentsType;
    }

    public void setRewardAndPunishmentsType(String rewardAndPunishmentsType) {
        this.rewardAndPunishmentsType = rewardAndPunishmentsType;
    }

    public String getRewardAndPunishmentsReason() {
        return rewardAndPunishmentsReason;
    }

    public void setRewardAndPunishmentsReason(String rewardAndPunishmentsReason) {
        this.rewardAndPunishmentsReason = rewardAndPunishmentsReason;
    }

    public List<QaPunishmentsAttachRequest> getRewardsAndPunishmentsAttaches() {
        return rewardsAndPunishmentsAttaches;
    }

    public void setRewardsAndPunishmentsAttaches(List<QaPunishmentsAttachRequest> rewardsAndPunishmentsAttaches) {
        this.rewardsAndPunishmentsAttaches = rewardsAndPunishmentsAttaches;
    }

    public Integer getRewardAndPunishmentsCount() {
        return rewardAndPunishmentsCount;
    }

    public void setRewardAndPunishmentsCount(Integer rewardAndPunishmentsCount) {
        this.rewardAndPunishmentsCount = rewardAndPunishmentsCount;
    }

    public BigDecimal getConfirmTotalMoney() {
        return confirmTotalMoney;
    }

    public void setConfirmTotalMoney(BigDecimal confirmTotalMoney) {
        this.confirmTotalMoney = confirmTotalMoney;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
