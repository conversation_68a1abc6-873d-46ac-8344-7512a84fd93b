package com.nsy.wms.business.service.stockin;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockin.ShelveTaskItemInfo;
import com.nsy.api.wms.domain.stockin.StockInPageItemInfo;
import com.nsy.api.wms.domain.stockin.StockinOrderItemShelvedQty;
import com.nsy.api.wms.domain.stockin.StockinOrderQtyInfo;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItem;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItemDetail;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.VacuumEnum;
import com.nsy.api.wms.request.stockin.CheckOrShelveRequest;
import com.nsy.api.wms.request.stockin.QueryStockinOrderItemReceiveInfoRequest;
import com.nsy.api.wms.request.stockin.QueryStockinOrderItemSummaryRequest;
import com.nsy.api.wms.request.stockin.QueryStockinReturnProductQtyRequest;
import com.nsy.api.wms.request.stockin.StockInOrderArrivalCountRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemListRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingValidRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.CheckOrShelveResponse;
import com.nsy.api.wms.response.stockin.QueryStockinOrderItemReceiveInfoResponse;
import com.nsy.api.wms.response.stockin.QueryStockinOrderItemSummaryResponse;
import com.nsy.api.wms.response.stockin.QueryStockinReturnProductQtyResponse;
import com.nsy.api.wms.response.stockin.StockInOrderArrivalCountListResponse;
import com.nsy.api.wms.response.stockin.StockInOrderArrivalCountResponse;
import com.nsy.api.wms.response.stockin.StockinOrderBoxListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderItemSkuListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderTimeQueryResponse;
import com.nsy.api.wms.response.stockin.StockinPackageNameResponse;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.internal.common.PurchaseModuleService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinQcInboundsEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinOrderItemService extends ServiceImpl<StockinOrderItemMapper, StockinOrderItemEntity> {

    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinShelveTaskMapper stockinShelveTaskMapper;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockinOrderItemMapper stockinOrderItemMapper;
    @Autowired
    StockInternalBoxService boxService;
    @Autowired
    StockInternalBoxItemService boxItemService;
    @Autowired
    StockinShelveTaskItemService shelveTaskItemService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private StockinShelveTaskItemMapper stockinShelveTaskItemMapper;
    @Autowired
    private StockinReturnProductService stockinReturnProductService;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    StockinQcInboundsService stockinQcInboundsService;
    @Autowired
    StockinReturnProductTaskItemService returnProductTaskItemService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    OmsApiService omsApiService;
    @Autowired
    PurchaseModuleService purchaseModuleService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    BdPositionService bdPositionService;

    public List<StockinOrderItemEntity> findAllByTaskItemId(Integer taskItemId) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda().eq(StockinOrderItemEntity::getTaskItemId, taskItemId));
    }

    public List<StockinOrderItemEntity> findAllByStockinOrderId(Integer stockinOrderId) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda().eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId));
    }

    public List<StockinOrderItemEntity> findAllByPurchasePlanNo(List<String> purchasePlanNo) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda().in(StockinOrderItemEntity::getPurchasePlanNo, purchasePlanNo));
    }

    public List<StockinOrderItemEntity> findAllByStockinOrderIdList(List<Integer> stockinOrderIdList) {
        if (CollectionUtils.isEmpty(stockinOrderIdList)) {
            return Lists.newArrayList();
        }
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda().in(StockinOrderItemEntity::getStockinOrderId, stockinOrderIdList));
    }

    public StockinOrderItemEntity findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndTaskItemIdIsNull(Integer stockinOrderId, String sku, String internalBoxCode) {
        return this.getOne(new QueryWrapper<StockinOrderItemEntity>().lambda()
                .eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSku, sku)
                .eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode)
                .isNull(StockinOrderItemEntity::getTaskItemId)
                .last("limit 1"));
    }

    public StockinOrderItemEntity findTopByStockinOrderId(Integer stockinOrderId) {
        return this.getOne(new QueryWrapper<StockinOrderItemEntity>().lambda()
                .eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .last("limit 1"));
    }

    public StockinOrderItemEntity findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(Integer stockinOrderId, String sku, String internalBoxCode, String purchasePlanNo, String eliminateStatus) {
        LambdaQueryWrapper<StockinOrderItemEntity> lambda = new QueryWrapper<StockinOrderItemEntity>().lambda();
        lambda.eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSku, sku)
                .eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode);
        if (StringUtils.hasText(purchasePlanNo))
            lambda.eq(StockinOrderItemEntity::getPurchasePlanNo, purchasePlanNo);
        if (StringUtils.hasText(eliminateStatus)) {
            lambda.ne(StockinOrderItemEntity::getStatus, eliminateStatus);
        }
        lambda.last("limit 1");
        return this.getOne(lambda);
    }

    public StockinOrderItemEntity findTopByStockinOrderIdAndPurchasePlanNo(Integer stockinOrderId, String sku, String purchasePlanNo, String internalBoxCode) {
        LambdaQueryWrapper<StockinOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSku, sku)
                .eq(StockinOrderItemEntity::getPurchasePlanNo, purchasePlanNo);
        if (StringUtils.hasText(internalBoxCode))
            queryWrapper.eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    public List<StockinOrderItemEntity> findAllByTaskItemIdIn(List<Integer> taskItemIdList) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda().in(StockinOrderItemEntity::getTaskItemId, taskItemIdList));
    }

    public StockinOrderItemEntity findTopByTaskItemIdAndInternalBoxCode(Integer taskItemId, String internalBoxCode) {
        return this.getOne(new QueryWrapper<StockinOrderItemEntity>().lambda().eq(StockinOrderItemEntity::getTaskItemId, taskItemId)
                .eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode)
                .last("limit 1"));
    }


    public StockinOrderItemEntity createItem(String internalBoxCodeIn, StockinOrderItemEntity stockinOrderItemEntity) {
        StockinOrderItemEntity entity = new StockinOrderItemEntity();
        BeanUtilsEx.copyProperties(stockinOrderItemEntity, entity, "returnQty", "stockinReturnQty", "concessionsCount");
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        entity.setInternalBoxCode(internalBoxCodeIn);
        entity.setPushedQty(0);
        entity.setQty(0);
        this.save(entity);
        return entity;
    }

    public StockinOrderItemEntity createItem(String internalBoxCode, StockinOrderTaskItemEntity taskItemEntity, StockinOrderEntity orderEntity) {
        StockinOrderItemEntity entity = new StockinOrderItemEntity();
        entity.setBatchCode(taskItemEntity.getBatchCode());
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        entity.setLocation(TenantContext.getTenant());
        entity.setInternalBoxCode(internalBoxCode);
        entity.setQty(0);
        entity.setProductId(taskItemEntity.getProductId());
        entity.setPurchasePlanNo(taskItemEntity.getPurchasePlanNo());
        if (orderEntity.getOrderType().equals(1)) {
            entity.setBranchPurchasePlanNo("V" + taskItemEntity.getPurchasePlanNo());
        }
        entity.setSku(taskItemEntity.getSku());
        entity.setSellerSku(taskItemEntity.getSellerSku());
        entity.setSellerBarcode(taskItemEntity.getSellerBarcode());
        entity.setTaskItemId(taskItemEntity.getTaskItemId());
        entity.setStockinOrderId(orderEntity.getStockinOrderId());
        entity.setSpecId(taskItemEntity.getSpecId());
        entity.setStatus(stockinOrderTaskItemService.setSkuStatus(taskItemEntity));
        entity.setPushedQty(0);
        this.save(entity);
        return entity;
    }

    public List<StockinOrderItemEntity> findAllByStockinOrderIdAndSkuAndInternalBoxCode(Integer stockinOrderId, String sku, String internalBoxCode) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda()
                .eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSku, sku)
                .eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode));
    }

    public List<StockinOrderItemEntity> findAllByStockinOrderIdAndSku(Integer stockinOrderId, String sku) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda()
                .eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSku, sku));
    }

    public List<StockinOrderItemEntity> findAllByStockinOrderIdAndSpecId(Integer stockinOrderId, Integer specId) {
        return this.list(new QueryWrapper<StockinOrderItemEntity>().lambda()
                .eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSpecId, specId));
    }


    public List<StockinOrderTaskItem> addItemList(StockinOrderEntity orderEntity) {
        List<StockinOrderItemEntity> itemEntityList = this.list(new QueryWrapper<StockinOrderItemEntity>().lambda()
                .eq(StockinOrderItemEntity::getStockinOrderId, orderEntity.getStockinOrderId())
                .isNull(StockinOrderItemEntity::getTaskItemId));
        if (!CollectionUtils.isEmpty(itemEntityList)) {
            Map<String, List<StockinOrderItemEntity>> collect1 = itemEntityList.stream().collect(Collectors.groupingBy(StockinOrderItemEntity::getSku));
            return collect1.entrySet().stream().map(map -> {
                StockinOrderTaskItem stockinOrderTaskItem = new StockinOrderTaskItem();
                List<StockinOrderTaskItemDetail> collect2 = map.getValue().stream().map(orderItemEntity -> {
                    StockinOrderTaskItemDetail detail = new StockinOrderTaskItemDetail();
                    detail.setSku(orderItemEntity.getSku());
                    detail.setInternalBoxCode(orderItemEntity.getInternalBoxCode());
                    detail.setQty(orderItemEntity.getQty());
                    return detail;
                }).collect(Collectors.toList());
                stockinOrderTaskItem.setStockinOrderTaskItemDetailList(collect2);
                ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(map.getKey());
                stockinOrderTaskItem.setBarcode(productSpecInfoEntity.getBarcode());
                stockinOrderTaskItem.setProductId(productSpecInfoEntity.getProductId());
                stockinOrderTaskItem.setSku(productSpecInfoEntity.getSku());
                stockinOrderTaskItem.setSpecId(productSpecInfoEntity.getSpecId());
                stockinOrderTaskItem.setImageUrl(productSpecInfoEntity.getImageUrl());
                stockinOrderTaskItem.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
                stockinOrderTaskItem.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
                stockinOrderTaskItem.setExpectedQty(Integer.valueOf(0));
                stockinOrderTaskItem.setIsNeedQa(Integer.valueOf(0));
                stockinOrderTaskItem.setQaQty(Integer.valueOf(0));
                stockinOrderTaskItem.setScanQty(collect2.stream().mapToInt(StockinOrderTaskItemDetail::getQty).sum());
                return stockinOrderTaskItem;
            }).collect(Collectors.toList());

        }
        return new ArrayList<>();
    }

    /**
     * 入库单明细sku信息列表
     */
    public PageResponse<StockinOrderItemSkuListResponse> getStockinStockinOrderItemSkuList(StockinOrderItemListRequest request) {
        if (StringUtils.hasText(request.getBarcode())) {
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarcode());
            if (productSpecInfoEntity == null)
                throw new BusinessServiceException("不存在该条码的商品信息");
            request.setSku(productSpecInfoEntity.getSku());
        }
        IPage<StockinOrderItemSkuListResponse> page = stockinOrderItemMapper.pageSearchStockinOrderItemList(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        PageResponse<StockinOrderItemSkuListResponse> pageResponse = PageResponse.of(page.getTotal());
        List<StockinOrderItemSkuListResponse> list = page.getRecords();
        List<StockinOrderItemSkuListResponse> responseList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            responseList = list.stream().map(item -> {
                StockinOrderItemSkuListResponse response = new StockinOrderItemSkuListResponse();
                BeanUtilsEx.copyProperties(item, response);
                response.setStatusName(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_SKU_STATUS.getName(), response.getStatus()));
                //如果一件未收，改成未收货
                if (item.getStockinQty().equals(0))
                    response.setStatusName("未收货");
                response.setVacuumCn(StockConstant.ENABLE.equals(response.getVacuumFlag()) ? VacuumEnum.getPcNoticeByKey(response.getPackageVacuum()) : "");
                StockinQcInboundsEntity qcResult = stockinQcInboundsService.getBaseMapper().findResultFromStockInOrderItem(response.getInternalBoxCode(), response.getSku(), response.getPurchasePlanNo(), response.getSupplierDeliveryBoxCode());
                if (!ObjectUtils.isEmpty(qcResult)) {
                    response.setQcInboundsId(qcResult.getQcInboundsId());
                    response.setProductId(qcResult.getProductId());
                    response.setInspectionWay(qcResult.getInspectionWay());
                }
                if (response.getWaitReturnQty() > 0) {
                    response.setWaitReturnQty(response.getWaitReturnQty() - response.getReturnQty());
                }
                return response;
            }).collect(Collectors.toList());
            buildShelveData(responseList);
            buildStorePreFix(responseList);
        }
        pageResponse.setContent(responseList);
        return pageResponse;
    }

    // 根据入库单明细查上架任务的上架数，退货数
    private void buildShelveData(List<StockinOrderItemSkuListResponse> list) {
        // 分公司申请的入库单，查询上架数、退货数 以分公司入库单id查询
        Integer searchStockinOrderId = list.get(0).getRealStockinOrderId() != null && list.get(0).getRealStockinOrderId() > 0 ? list.get(0).getRealStockinOrderId() : list.get(0).getStockinOrderId();
        List<StockinShelveTaskItemEntity> itemList = stockinShelveTaskItemService.listBySourceIdListIgnoreTenant(Collections.singletonList(searchStockinOrderId));
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<String> positionCodeList = itemList.stream().map(StockinShelveTaskItemEntity::getPositionCode).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        List<BdPositionEntity> bdPositionEntities = CollectionUtils.isEmpty(positionCodeList) ? Collections.emptyList() : bdPositionService.bdPositionByPositionCodeList(positionCodeList);
        Map<String, String> positionTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_POSITION_TYPE.getName());
        Map<String, List<BdPositionEntity>> positionMap = bdPositionEntities.stream().collect(Collectors.groupingBy(BdPositionEntity::getPositionCode));
        Map<Integer, StockinShelveTaskEntity> collect = stockinShelveTaskMapper.findAllByTaskIdListIgnoreTenant(itemList.stream().map(StockinShelveTaskItemEntity::getShelveTaskId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(StockinShelveTaskEntity::getShelveTaskId, Function.identity()));
        list.forEach(item -> {
            int totalShelveQty = item.getShelvedQty() != null ? item.getShelvedQty() : 0;
            Map<String, Integer> positionCodeMap = new HashMap<>();
            for (StockinShelveTaskItemEntity shelveItem : itemList) {
                if (StringUtils.hasText(item.getPurchasePlanNo())) {
                    StockinShelveTaskEntity stockinShelveTaskEntity = collect.get(shelveItem.getShelveTaskId());
                    if ((item.getStockinOrderId().equals(shelveItem.getSourceId()) || item.getRealStockinOrderId().equals(shelveItem.getSourceId()))
                            && stockinShelveTaskEntity.getInternalBoxCode().equals(item.getInternalBoxCode())
                            && item.getSku().equals(shelveItem.getSku())
                            && item.getPurchasePlanNo().equals(shelveItem.getPurchasePlanNo())) {
                        totalShelveQty = totalShelveQty + shelveItem.getShelvedQty();
                        positionCodeMap.put(shelveItem.getPositionCode(), positionCodeMap.getOrDefault(shelveItem.getPositionCode(), 0) + shelveItem.getShelvedQty());
                    }
                } else {
                    if ((item.getStockinOrderId().equals(shelveItem.getSourceId()) || item.getRealStockinOrderId().equals(shelveItem.getSourceId()))
                            && item.getSku().equals(shelveItem.getSku())) {
                        totalShelveQty = totalShelveQty + shelveItem.getShelvedQty();
                        positionCodeMap.put(shelveItem.getPositionCode(), positionCodeMap.getOrDefault(shelveItem.getPositionCode(), 0) + shelveItem.getShelvedQty());
                    }
                }
            }
            if (!CollectionUtils.isEmpty(positionCodeMap)) {
                List<String> positionCodeInfoList = new ArrayList<>();
                positionCodeMap.entrySet().stream().forEach(entry -> {
                    if (ObjectUtils.isEmpty(positionMap.get(entry.getKey()))) {
                        return;
                    }
                    BdPositionEntity positionEntity = positionMap.get(entry.getKey()).get(0);
                    String positionType = positionTypeEnumMap.get(positionEntity.getPositionType());
                    positionCodeInfoList.add(String.format("%s(%s%s%s)", entry.getKey(), positionEntity.getAreaName(), positionType, entry.getValue()));
                });
                if (!CollectionUtils.isEmpty(positionCodeInfoList)) {
                    item.setPositionCode(String.join(",", positionCodeInfoList));
                }
            }
            if (StringUtils.hasText(item.getBranchPurchasePlanNo())) {
                item.setPurchasePlanNo(item.getBranchPurchasePlanNo());
            }
        });

    }

    private void buildStorePreFix(List<StockinOrderItemSkuListResponse> pageList) {
        List<SaStorePageInfoResponse> storeItems = omsApiService.getAllStoreInfo();
        pageList.forEach(o -> {
            if (o.getStoreId() != null) {
                storeItems.stream().filter(s -> s.getId().equals(o.getStoreId())).findFirst().ifPresent(curItem -> {
                    o.setStorePreFix(curItem.getExtendValue());
                    o.setStoreName(curItem.getStoreName());
                });
            }
        });
    }

    public LambdaQueryWrapper<StockinOrderItemEntity> buildQueryByStockinOrderId(Integer stockinOrderId) {
        LambdaQueryWrapper<StockinOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(stockinOrderId)) {
            queryWrapper.eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId);
        }
        /*if (StringUtils.hasText(sku)) {
            queryWrapper.eq(StockinOrderItemEntity::getSku, sku);
        }*/
        return queryWrapper;
    }

    public PageResponse<StockinOrderBoxListResponse> getBoxList(StockinOrderItemListRequest request) {
        StockinOrderEntity orderEntity = stockinOrderService.getById(request.getStockinOrderId());
        request.setStockinOrderNo(orderEntity.getStockinOrderNo());
        Page<StockinOrderBoxListResponse> page = boxItemService.getBaseMapper().stockinOrderDetailList(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        PageResponse<StockinOrderBoxListResponse> response = new PageResponse<>();
        page.getRecords().forEach(item -> item.setStatusLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_STATUS.getName(), item.getStatus())));
        response.setTotalCount(page.getTotal());
        response.setContent(page.getRecords());
        return response;
    }

    /**
     * 根据采购单号查询入库明细信息 （需处理分公司场景）
     */
    public List<QueryStockinOrderItemSummaryResponse> queryStockinOrderItemSummary(QueryStockinOrderItemSummaryRequest request) {
        request.setLocation(TenantContext.getTenant());
        List<StockinOrderItemEntity> stockinOrderItemEntities = this.getBaseMapper().findAllByPurchasePlanNosAndSkusIgnoreTenant(request);
        if (CollectionUtils.isEmpty(stockinOrderItemEntities)) return new ArrayList<>();
        // 分公司申请，泉州入库单明细 -> 分公司入库单明细
        if (stockinOrderItemEntities.get(0).getRealStockinOrderItemId() != null && stockinOrderItemEntities.get(0).getRealStockinOrderItemId() > 0) {
            stockinOrderItemEntities = this.getBaseMapper().findAllByStockinOrderItemIdIgnoreTenant(stockinOrderItemEntities.stream().map(StockinOrderItemEntity::getRealStockinOrderItemId).collect(Collectors.toList()));
        }
        // 入库 + 退货 数据根据 真实采购单号 分组
        List<String> realPurchasePlanNos = stockinOrderItemEntities.stream().map(StockinOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.toList());
        Map<String, List<StockinReturnProductTaskItemEntity>> returnTaskItemMap = returnProductTaskItemService.getBaseMapper().findAllByPurchaseOrderNoListIgnoreTenant(realPurchasePlanNos, request.getLocation())
                .stream().collect(Collectors.groupingBy(t -> String.format("%s##%s", t.getPurchasePlanNo(), t.getSku())));
        Map<String, Map<String, List<StockinOrderItemEntity>>> stockinOrderItemMap = stockinOrderItemEntities.stream()
                .collect(Collectors.groupingBy(StockinOrderItemEntity::getPurchasePlanNo, Collectors.groupingBy(StockinOrderItemEntity::getSku)));
        List<QueryStockinOrderItemSummaryResponse> resultList = new ArrayList<>();
        stockinOrderItemMap.forEach((purchasePlanNo, itemMap) -> {
            QueryStockinReturnProductQtyRequest queryStockinReturnProductQtyRequest = new QueryStockinReturnProductQtyRequest();
            queryStockinReturnProductQtyRequest.setPurchasePlanNos(Sets.newHashSet(purchasePlanNo));
            List<QueryStockinReturnProductQtyResponse> returnQtyLs = stockinReturnProductService.queryReturnQtyByPurchasePlanNo(queryStockinReturnProductQtyRequest);
            itemMap.forEach((sku, items) -> {
                QueryStockinOrderItemSummaryResponse response = new QueryStockinOrderItemSummaryResponse();
                Set<Integer> stockinOrderIdSet = items.stream().map(StockinOrderItemEntity::getStockinOrderId).collect(Collectors.toSet());
                List<StockinOrderQtyInfo> stockinOrderQtyLs = queryShelveQtyAndReturnQty(stockinOrderIdSet, sku);
                Integer totalQty = items.stream().mapToInt(StockinOrderItemEntity::getQty).sum();
                Integer shelvedQty = stockinOrderQtyLs.stream().mapToInt(StockinOrderQtyInfo::getShelvedQty).sum();
                Integer returnQty = stockinOrderQtyLs.stream().mapToInt(StockinOrderQtyInfo::getReturnQty).sum();
                response.setOrderNo(request.getOrderNoList().get(0).startsWith("V") ? "V" + purchasePlanNo : purchasePlanNo);
                response.setSku(sku);
                response.setQty(totalQty);
                response.setVarianceQty(totalQty - shelvedQty - returnQty);
                response.setWaitReturnQty(CollectionUtils.isEmpty(returnQtyLs) ? 0 : returnQtyLs.stream().filter(f -> Objects.equals(f.getSku(), sku) && Objects.nonNull(f.getReturnQty())).mapToInt(QueryStockinReturnProductQtyResponse::getReturnQty).sum());
                int returnedQty = returnTaskItemMap.getOrDefault(String.format("%s##%s", purchasePlanNo, sku), Lists.newArrayList()).stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum();
                response.setReturnedQty(returnedQty);
                resultList.add(response);
            });
        });
        return resultList;
    }


    private List<StockinOrderQtyInfo> queryShelveQtyAndReturnQty(Set<Integer> stockinOrderIdSet, String sku) {
        if (CollectionUtils.isEmpty(stockinOrderIdSet)) {
            return Lists.newArrayList();
        }
        List<StockinOrderQtyInfo> ls = Lists.newArrayListWithExpectedSize(stockinOrderIdSet.size());
        List<ShelveTaskItemInfo> stockinOrderInfoList = stockinShelveTaskItemMapper.searchShelveTaskItemList(Lists.newArrayList(stockinOrderIdSet), StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name());
        if (!CollectionUtils.isEmpty(stockinOrderInfoList)) {
            for (Integer stockinOrderId : stockinOrderIdSet) {
                StockinOrderQtyInfo stockinOrderQtyInfo = new StockinOrderQtyInfo();
                stockinOrderQtyInfo.setStockinOrderId(stockinOrderId);
                stockinOrderQtyInfo.setShelvedQty(stockinOrderInfoList.stream().filter(m -> Objects.equals(m.getSourceId(), stockinOrderId) && Objects.equals(sku, m.getSku())).mapToInt(ShelveTaskItemInfo::getShelvedQty).sum());
                stockinOrderQtyInfo.setReturnQty(stockinOrderInfoList.stream().filter(m -> Objects.equals(m.getSourceId(), stockinOrderId) && Objects.equals(sku, m.getSku())).mapToInt(ShelveTaskItemInfo::getReturnedQty).sum());
                stockinOrderQtyInfo.setSku(sku);
                ls.add(stockinOrderQtyInfo);
            }
        }
        return ls;
    }

    public List<StockinOrderItemEntity> listFromStockin(Boolean isQa, List<Integer> taskItemList) {
        return isQa ? this.baseMapper.listQaInternalBox(taskItemList) : this.findAllByTaskItemIdIn(taskItemList);
    }

    public void buildUpdateShelveTaskItemStatus(StockinShelveTaskItemEntity entity, Integer qty) {
        StockinShelveTaskEntity shelveTaskEntity = stockinShelveTaskService.getById(entity.getShelveTaskId());
        StockinOrderItemEntity stockinOrderItemEntity = this.findTopByStockinOrderIdAndSkuAndInternalBoxCodeAndPurchasePlanNo(entity.getSourceId(), entity.getSku(), shelveTaskEntity.getInternalBoxCode(), entity.getPurchasePlanNo(), StockinOrderItemStatusEnum.RETURNED.name());
        if (Objects.isNull(stockinOrderItemEntity))
            return;
        StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
        orderItemEntity.setStockinOrderItemId(stockinOrderItemEntity.getStockinOrderItemId());
        orderItemEntity.setUpdateBy(loginInfoService.getName());
        boolean shelved = entity.getShelvedQty() + qty > 0;
        orderItemEntity.setStatus(shelved ? StockinOrderItemStatusEnum.SHELVED.name() : StockinOrderItemStatusEnum.RETURNED.name());
        this.updateById(orderItemEntity);
        //整箱上架
        if (qty == 0) {
            purchaseModuleService.pushStockinToEtl(null, orderItemEntity.getStockinOrderItemId(), null);
        }
    }

    public StockInOrderArrivalCountListResponse getStockInOrderItemArrivalCount(StockInOrderArrivalCountRequest request) {
        List<String> supplierDeliveryNoList = stockinOrderTaskService.getSupplierDeliveryNoByBoxCode(request.getSupplierDeliveryBoxCode());
        if (CollectionUtils.isEmpty(supplierDeliveryNoList)) {
            throw new BusinessServiceException("当前出库箱码无对应信息!");
        }
        List<StockInOrderArrivalCountResponse> stockInOrderArrivalCount = new ArrayList<>();
        CollectionUtil.split(supplierDeliveryNoList.stream().distinct().collect(Collectors.toList()), 150).stream().forEach(
                detail -> {
                    stockInOrderArrivalCount.addAll(this.baseMapper.getStockInOrderItemArrivalCount(detail));
                }
        );
        StockInOrderArrivalCountListResponse stockInOrderArrivalCountList = new StockInOrderArrivalCountListResponse();
        stockInOrderArrivalCountList.setStockInOrderItem(stockInOrderArrivalCount);
        return stockInOrderArrivalCountList;
    }

    public List<QueryStockinOrderItemReceiveInfoResponse> queryStockinOrderItemReceiveInfo(QueryStockinOrderItemReceiveInfoRequest request) {
        return this.getBaseMapper().queryStockinOrderItemReceiveInfoIgnoreTenant(request);
    }

    //上架回填入库单明细
    @Transactional
    @JLock(keyConstant = "shelvedUpdateOrderItem", lockKey = "#content.stockinOrderId")
    public void shelvedUpdateOrderItem(StockinOrderItemShelvedQty content) {
        List<StockinOrderItemShelvedQty.ShelveInfoItem> shelveInfoItemList = content.getShelveInfoItemList();
        List<StockinOrderItemEntity> stockinOrderItemEntities = this.listByIds(shelveInfoItemList.stream().map(StockinOrderItemShelvedQty.ShelveInfoItem::getStockinOrderItemId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(stockinOrderItemEntities))
            return;
        Map<Integer, List<StockinOrderItemShelvedQty.ShelveInfoItem>> collect = shelveInfoItemList.stream().collect(Collectors.groupingBy(StockinOrderItemShelvedQty.ShelveInfoItem::getStockinOrderItemId));
        List<StockinOrderItemEntity> itemList = stockinOrderItemEntities.stream().map(item -> {
            StockinOrderItemEntity stockinOrderItemEntity = new StockinOrderItemEntity();
            stockinOrderItemEntity.setStockinOrderItemId(item.getStockinOrderItemId());
            List<StockinOrderItemShelvedQty.ShelveInfoItem> shelveInfoItems = collect.get(item.getStockinOrderItemId());
            int sum = shelveInfoItems.stream().mapToInt(StockinOrderItemShelvedQty.ShelveInfoItem::getShelvedQty).sum();
            stockinOrderItemEntity.setShelvedQty(item.getShelvedQty() + sum);
            stockinOrderItemEntity.setUpdateBy(loginInfoService.getName());
            return stockinOrderItemEntity;
        }).collect(Collectors.toList());
        this.updateBatchById(itemList);
    }

    public List<StockInPageItemInfo> getStockInPageItemInfo(List<Integer> stockInOrderIdList) {
        if (CollectionUtils.isEmpty(stockInOrderIdList)) {
            return null;
        }
        List<StockInPageItemInfo> stockInPageItemInfosList = new ArrayList<>();
        CollectionUtil.split(stockInOrderIdList, 10).forEach(detail -> {
            stockInPageItemInfosList.addAll(this.baseMapper.getStockInPageItemInfo(detail));
        });
        return stockInPageItemInfosList;
    }

    public List<StockinOrderTimeQueryResponse> productStockinOrderTimeQuery(List<Integer> productIdList) {
        return this.baseMapper.productStockinOrderTimeQuery(productIdList);
    }


    /**
     * 根据采购单号和sku查询是否质检或上架
     * 如果未质检并且未上架就返回true
     * 已经质检返回false。
     *
     * @param request
     * @return
     */
    public List<CheckOrShelveResponse> queryCheckOrShelve(CheckOrShelveRequest request) {
        return this.baseMapper.queryCheckOrShelve(request);
    }

    public List<StockinOrderItemEntity> listByStockinOrderIdAndSkuAndBoxCode(Integer stockinOrderId, String sku, String internalBoxCode) {
        return this.list(new LambdaQueryWrapper<StockinOrderItemEntity>()
                .eq(StockinOrderItemEntity::getStockinOrderId, stockinOrderId)
                .eq(StockinOrderItemEntity::getSku, sku)
                .eq(StockinOrderItemEntity::getInternalBoxCode, internalBoxCode));
    }

    public Integer getSpaceIdByCondition(String stockinOrderNo, String purchasePlanNo, String sku) {
        return this.baseMapper.getSpaceIdByCondition(stockinOrderNo, purchasePlanNo, sku);
    }

    /**
     * 计算当前sku的退货率
     *
     * @param sku
     * @return
     */
    public Double calculateReturnRange(String sku) {
        Double returnRange = this.baseMapper.calculateReturnRange(sku);
        if (ObjectUtils.isEmpty(returnRange)) {
            return 0.0;
        }
        return returnRange * 100;
    }

    /**
     * 获取包装信息
     *
     * @param supplierDeliveryNo
     * @param internalBoxCode
     * @param sku
     * @return
     */
    public List<StockinPackageNameResponse> getPackageNameInfo(String supplierDeliveryNo, String internalBoxCode, String sku) {
        return this.baseMapper.getPackageNameInfo(supplierDeliveryNo, internalBoxCode, sku);
    }

    public String getPurchaseNoByInfo(StockinVolumeWeightRecordMappingValidRequest request) {
        LambdaQueryWrapper<StockinOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinOrderItemEntity::getSku, request.getSku());
        queryWrapper.eq(StockinOrderItemEntity::getInternalBoxCode, request.getInternalBoxCode());
        List<StockinOrderItemEntity> orderItemList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(orderItemList)) {
            return null;
        }
        return orderItemList.get(0).getPurchasePlanNo();
    }
}
