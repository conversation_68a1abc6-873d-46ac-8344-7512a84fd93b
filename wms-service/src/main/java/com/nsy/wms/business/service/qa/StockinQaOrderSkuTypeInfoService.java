package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.qa.StockinQaBoxItemInfo;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderSkuTypeInfoMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检任务标签内容业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderSkuTypeInfoService extends ServiceImpl<StockinQaOrderSkuTypeInfoMapper, StockinQaOrderSkuTypeInfoEntity> {

    /**
     * 质检任务生成标签明细
     *
     * @param qaTaskEntity
     * @param stockinQaBoxItemInfos
     */
    public void taskBuildSkuType(StockinQaTaskEntity qaTaskEntity, List<StockinQaBoxItemInfo> stockinQaBoxItemInfos) {
        String skuType = stockinQaBoxItemInfos.stream().map(StockinQaBoxItemInfo::getLabelAttributeNames)
                .filter(StringUtils::hasText)
                .collect(Collectors.joining(","));
        //如果任务下没有挂标签信息进行新增
        if (!StringUtils.hasText(skuType))
            return;

        List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntityList = Arrays.stream(skuType.split(",")).distinct()
                .filter(StringUtils::hasText).map(item -> {
                    StockinQaOrderSkuTypeInfoEntity skuTypeInfoEntity = new StockinQaOrderSkuTypeInfoEntity();
                    skuTypeInfoEntity.setTaskId(qaTaskEntity.getTaskId());
                    skuTypeInfoEntity.setSkuType(item);
                    skuTypeInfoEntity.setLocation(qaTaskEntity.getLocation());
                    return skuTypeInfoEntity;
                }).collect(Collectors.toList());

        this.saveBatch(skuTypeInfoEntityList);
    }


    /**
     * 质检单生成标签明细
     *
     * @param qaOrderEntity
     * @param stockinQaBoxItemInfos
     */
    public void buildSkuType(StockinQaOrderEntity qaOrderEntity, List<StockinQaBoxItemInfo> stockinQaBoxItemInfos) {
        //如果对应任务下有标签信息赋值质检单id
        LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity> skuTypeQueryWrapper = new LambdaQueryWrapper<>();
        skuTypeQueryWrapper.eq(StockinQaOrderSkuTypeInfoEntity::getTaskId, qaOrderEntity.getTaskId());
        List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoList = this.list(skuTypeQueryWrapper);
        if (!CollectionUtils.isEmpty(skuTypeInfoList)) {
            skuTypeInfoList.forEach(detail -> detail.setStockinQaOrderId(qaOrderEntity.getStockinQaOrderId()));
            this.updateBatchById(skuTypeInfoList);
            return;
        }
        String skuType = stockinQaBoxItemInfos.stream().map(StockinQaBoxItemInfo::getLabelAttributeNames)
                .filter(StringUtils::hasText)
                .collect(Collectors.joining(","));
        //如果任务下没有挂标签信息进行新增
        if (!StringUtils.hasText(skuType))
            return;

        List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntityList = Arrays.stream(skuType.split(",")).distinct()
                .filter(StringUtils::hasText).map(item -> {
                    StockinQaOrderSkuTypeInfoEntity skuTypeInfoEntity = new StockinQaOrderSkuTypeInfoEntity();
                    skuTypeInfoEntity.setStockinQaOrderId(qaOrderEntity.getStockinQaOrderId());
                    skuTypeInfoEntity.setTaskId(qaOrderEntity.getTaskId());
                    skuTypeInfoEntity.setSkuType(item);
                    skuTypeInfoEntity.setLocation(qaOrderEntity.getLocation());
                    return skuTypeInfoEntity;
                }).collect(Collectors.toList());

        this.saveBatch(skuTypeInfoEntityList);
    }

    public List<StockinQaOrderSkuTypeInfoEntity> getByStockinQaOrderId(Integer stockinQaOrderId) {
        return this.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>()
                .eq(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderId));
    }

    /**
     * 获取标签信息
     *
     * @param stockinQaOrderId
     * @return
     */
    public List<String> getByStockinSkuTypeByQaOrderId(Integer stockinQaOrderId) {
        List<StockinQaOrderSkuTypeInfoEntity> skuTypeList = this.getByStockinQaOrderId(stockinQaOrderId);
        if (CollectionUtils.isEmpty(skuTypeList)) {
            return Collections.emptyList();
        }
        return skuTypeList.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList());
    }

    public PageResponse<StockinQaOrderSkuTypeInfoEntity> pageList(Page<StockinQaOrderSkuTypeInfoEntity> page) {
        PageResponse<StockinQaOrderSkuTypeInfoEntity> pageResponse = new PageResponse<>();
        IPage<StockinQaOrderSkuTypeInfoEntity> pageResult = this.getBaseMapper().pageList(page);
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return PageResponse.of(Collections.emptyList(), 0L);
        }
        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }
}
