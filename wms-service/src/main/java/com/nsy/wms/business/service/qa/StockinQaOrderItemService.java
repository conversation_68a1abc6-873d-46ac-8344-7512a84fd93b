package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.domain.qa.StockinQaBoxItemInfo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderItemEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderItemMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检单明细表业务实现
 * @date: 2024-11-18 15:58
 */
@Service
public class StockinQaOrderItemService extends ServiceImpl<StockinQaOrderItemMapper, StockinQaOrderItemEntity> {

    @Autowired
    LoginInfoService loginInfoService;


    public Set<String> findStockinOrderNoByOrderId(Integer stockinQaOrderId) {
        return this.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>()
                .select(StockinQaOrderItemEntity::getStockinOrderNo)
                .eq(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderId))
            .stream().map(StockinQaOrderItemEntity::getStockinOrderNo).distinct().collect(Collectors.toSet());
    }


    public void buildOrderItem(StockinQaOrderEntity qaOrderEntity, List<StockinQaBoxItemInfo> stockinQaBoxItemInfos) {
        List<StockinQaOrderItemEntity> stockinQaOrderItemEntityList = stockinQaBoxItemInfos.stream().map(item -> {
            StockinQaOrderItemEntity itemEntity = new StockinQaOrderItemEntity();
            BeanUtils.copyProperties(item, itemEntity);
            itemEntity.setLocation(TenantContext.getTenant());
            itemEntity.setStockinQaOrderId(qaOrderEntity.getStockinQaOrderId());
            itemEntity.setCreateBy(loginInfoService.getName());
            return itemEntity;
        }).collect(Collectors.toList());

        this.saveBatch(stockinQaOrderItemEntityList);

    }

    public List<StockinQaOrderItemEntity> findByStockinQaOrderId(Integer stockinQaOrderId) {
        return this.list(new LambdaUpdateWrapper<StockinQaOrderItemEntity>()
            .eq(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderId));
    }

    /**
     * 判断是否为返工退货
     *
     * @param stockinQaOrderId
     * @return
     */
    public boolean validReturnApply(Integer stockinQaOrderId) {
        LambdaQueryWrapper<StockinQaOrderItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderId);
        queryWrapper.eq(StockinQaOrderItemEntity::getPurchasingApplyType, 6);
        if (this.count(queryWrapper) > 0)
            return true;

        return this.getBaseMapper().isReturnDeliveryType(stockinQaOrderId);

    }
}
