package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: ca<PERSON><PERSON><PERSON>
 * @time: 2024/12/2 14:19
 */
public class StockinQaOperateRequest {

    @ApiModelProperty("质检单ID")
    @NotNull
    private Integer stockinQaOrderId;

    @ApiModelProperty("质检流程名")
    @NotEmpty
    private String processName;

    @ApiModelProperty("结果")
    @NotNull
    private String status;

    @ApiModelProperty("质检数量")
    private Integer testTotalCount;

    @ApiModelProperty("质检不合格件数")
    private Integer unqualifiedCount = 0;
    /**
     * 直接退货件数
     */
    @ApiModelProperty("直接退货件数")
    private Integer directReturnCount = 0;
    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    private Integer returnCount = 0;
    /**
     * 让步接收数
     */
    @ApiModelProperty("让步接收数")
    private Integer concessionsCount = 0;

    @ApiModelProperty("轻微缺陷数量")
    private Integer minorDefectCount = 0;


    @ApiModelProperty("严重缺陷数量")
    private Integer majorDefectCount = 0;

    @ApiModelProperty("致命缺陷数量")
    private Integer criticalDefectCount = 0;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 当前步骤检测图片
     */
    @ApiModelProperty("当前步骤检测图片")
    public List<String> imgUrl;

    @ApiModelProperty("附件地址")
    private String attachmentUrl;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("不合格的问题描述")
    private String unqualifiedQuestion;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("次要不合格的问题描述")
    private String unqualifiedQuestionSecondary;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("次要不合格原因归类")
    private String unqualifiedCategorySecondary;

    @ApiModelProperty("次要不合格原因")
    private String unqualifiedReasonSecondary;

    public Integer getTestTotalCount() {
        return testTotalCount;
    }

    public void setTestTotalCount(Integer testTotalCount) {
        this.testTotalCount = testTotalCount;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public List<String> getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(List<String> imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getDirectReturnCount() {
        return directReturnCount;
    }

    public void setDirectReturnCount(Integer directReturnCount) {
        this.directReturnCount = directReturnCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUnqualifiedQuestionSecondary() {
        return unqualifiedQuestionSecondary;
    }

    public void setUnqualifiedQuestionSecondary(String unqualifiedQuestionSecondary) {
        this.unqualifiedQuestionSecondary = unqualifiedQuestionSecondary;
    }

    public String getUnqualifiedCategorySecondary() {
        return unqualifiedCategorySecondary;
    }

    public void setUnqualifiedCategorySecondary(String unqualifiedCategorySecondary) {
        this.unqualifiedCategorySecondary = unqualifiedCategorySecondary;
    }

    public String getUnqualifiedReasonSecondary() {
        return unqualifiedReasonSecondary;
    }

    public void setUnqualifiedReasonSecondary(String unqualifiedReasonSecondary) {
        this.unqualifiedReasonSecondary = unqualifiedReasonSecondary;
    }

    public Integer getMinorDefectCount() {
        return minorDefectCount == null ? Integer.valueOf(0) : minorDefectCount;
    }

    public void setMinorDefectCount(Integer minorDefectCount) {
        this.minorDefectCount = minorDefectCount;
    }

    public Integer getMajorDefectCount() {
        return majorDefectCount == null ? Integer.valueOf(0) : majorDefectCount;
    }

    public void setMajorDefectCount(Integer majorDefectCount) {
        this.majorDefectCount = majorDefectCount;
    }

    public Integer getCriticalDefectCount() {
        return criticalDefectCount == null ? Integer.valueOf(0) : criticalDefectCount;
    }

    public void setCriticalDefectCount(Integer criticalDefectCount) {
        this.criticalDefectCount = criticalDefectCount;
    }
}

