package com.nsy.wms.elasticjob.stockin;

import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.request.bd.BdStockinAutoShelveRuleListRequest;
import com.nsy.api.wms.request.stockin.UpShelveRequest;
import com.nsy.api.wms.response.bd.BdStockinAutoShelveRuleListResponse;
import com.nsy.api.wms.response.stockin.UpShelvesResponse;
import com.nsy.wms.business.service.bd.BdStockinAutoShelveRuleService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stockin.ShelvePdaService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-04-28 17:18
 */
@Component
public class StockinAutoShelveJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinAutoShelveJob.class);

    @Autowired
    private BdStockinAutoShelveRuleService bdStockinAutoShelveRuleService;
    @Autowired
    private StockInternalBoxItemService internalBoxItemService;
    @Autowired
    private ShelvePdaService shelvePdaService;
    @Autowired
    private StockinShelveTaskService shelveTaskService;

    /**
     * 符合条件数据将自动上架
     *
     * @param jobDataMap
     * @throws Exception
     */
    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockinAutoShelveJob start");
        LoginInfoService.setName("系统自动上架");
        int pageNumber = 1;
        boolean hasMoreData = true;
        BdStockinAutoShelveRuleListRequest request = new BdStockinAutoShelveRuleListRequest();
        request.setPageSize(100);
        request.setPageIndex(pageNumber);
        // 获取当前时间并计算30分钟前的时间
        LocalDateTime thirtyMinutesAgo = LocalDateTime.now().minus(30, ChronoUnit.MINUTES);

        while (hasMoreData) {
            PageResponse<BdStockinAutoShelveRuleListResponse> ruleList = bdStockinAutoShelveRuleService.getRuleList(request);
            if (CollectionUtils.isEmpty(ruleList.getContent())) {
                break;
            }
            this.autoShelve(ruleList.getContent().stream().map(BdStockinAutoShelveRuleListResponse::getInternalBoxCode).collect(Collectors.toList()), thirtyMinutesAgo);
            // 判断是否还有更多数据
            if (ruleList.getContent().size() < request.getPageSize()) {
                break;
            }
            // 增加页码，准备下次查询
            request.setPageIndex(pageNumber++);
        }
        LoginInfoService.removeName();
        LOGGER.info("StockinAutoShelveJob end");
    }

    private void autoShelve(List<String> internalBoxCodeList, LocalDateTime thirtyMinutesAgo) {
        //自动上架数据
        List<StockInternalBoxAutoShelveResponse> autoShelveList = internalBoxItemService.getStockInternalBoxAutoShelveList(internalBoxCodeList, thirtyMinutesAgo);
        if (CollectionUtils.isEmpty(autoShelveList)) {
            return;
        }
        autoShelveList.forEach(detail -> {
            try {
                UpShelveRequest upShelveRequest = new UpShelveRequest();
                upShelveRequest.setInternalBoxCode(detail.getInternalBoxCode());
                upShelveRequest.setPosition(detail.getPositionCode());
                upShelveRequest.setQty(detail.getStockinQty());
                upShelveRequest.setSku(detail.getSku());
                UpShelvesResponse upShelvesResponse = shelvePdaService.upShelve(upShelveRequest);
                if (!"上架成功".equals(upShelvesResponse.getMessage())) {
                    LOGGER.info("内部箱【 {} 】规格编码【 {} 】自动上架失败,失败原因为:  {} ", detail.getInternalBoxCode(), detail.getSku(), upShelvesResponse.getMessage());
                }
                //如果没有上架开始时间则赋值上架开始时间
                shelveTaskService.updateShelveDate(detail.getShelveTaskId());
            } catch (Exception e) {
                LOGGER.error(String.format("内部箱【%s】规格编码【%s】自动上架失败,失败原因为:%s", detail.getInternalBoxCode(), detail.getSku(), e.getMessage()), e);
            }
        });

    }
}