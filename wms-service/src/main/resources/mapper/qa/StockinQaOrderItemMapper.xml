<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderItemMapper">
    <select id="getPurchasePlanNoByStockinQaOrderId" resultType="java.lang.String">
        select distinct purchase_plan_no from stockin_qa_order_item
        where stockin_qa_order_id = #{stockinQaOrderId}
    </select>
    <select id="listSupplierDeliveryNo" resultType="com.nsy.api.wms.response.qa.StockinQaOrderPageExport">
        select oi.stockin_qa_order_id,
               o.sku,
               group_concat(distinct oi.supplier_delivery_no) as supplierDeliveryNo
                from stockin_qa_order_item oi
        inner join stockin_qa_order o on oi.stockin_qa_order_id =o.stockin_qa_order_id
        where oi.stockin_qa_order_id in
        <foreach collection="stockinQaOrderIds" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        group by oi.stockin_qa_order_id
    </select>
    <select id="isReturnDeliveryType" resultType="java.lang.Boolean">
        select count(1) > 0
        from stockin_qa_order_item sqoi
                 inner join stockin_order_item soi
                           on sqoi.stockin_order_item_id = soi.stockin_order_item_id
                 inner join stockin_order_task_item soti
                           on soi.task_item_id = soti.task_item_id
                 inner join stockin_order_task sot
                   on soti.task_id = sot.task_id
                 inner join stock_platform_schedule sps
        on sot.platform_schedule_id = sps.platform_schedule_id
        where sqoi.stockin_qa_order_id = #{stockinQaOrderId}
        and sps.delivery_type = 2
    </select>
</mapper>