package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CountryCodeConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.bd.StockoutNoticeLackResponse;
import com.nsy.api.wms.domain.stockout.DomesticSkuDTO;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitPrint;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitPrintSort;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskItemSowWall;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskPickingBoxInfo;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutSplitBatchType;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderLackSkuStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderLackSourceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSplitServiceTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitScanBarcodeRequest;
import com.nsy.api.wms.request.stockout.StockoutPrintOrderRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskPrintOrderInfo;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskPrintResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskSkuResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskSowWallResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutBatchSplitTaskItemPrintBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutNoticeLackBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutNoticeLackItemBo;
import com.nsy.wms.business.service.bd.BdCountryService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.query.StockoutBatchSplitTaskQueryWrapper;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLackEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLackItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.encryption.AesEncryptUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 二次分拣
 */
@Service
public class StockoutBatchSplitSecondaryService extends ServiceImpl<StockoutBatchSplitTaskMapper, StockoutBatchSplitTaskEntity> implements IBatchSplitService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutBatchSplitSecondaryService.class);

    @Autowired
    StockoutBatchSplitTaskMapper taskMapper;
    @Autowired
    StockoutBatchSplitTaskItemMapper taskItemMapper;
    @Autowired
    StockoutBatchSplitTaskItemService splitItemService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockoutBatchMapper batchMapper;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockoutBatchSplitTaskService stockoutBatchSplitTaskService;
    @Autowired
    StockInternalBoxItemMapper stockInternalBoxItemMapper;
    @Autowired
    StockoutOrderLackItemService stockoutOrderLackItemService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockoutBatchService batchService;
    @Autowired
    StockoutBatchOrderItemService batchOrderItemService;
    @Autowired
    StockoutOrderScanTaskItemService stockoutOrderScanTaskItemService;
    @Autowired
    StockoutOrderScanTaskService stockoutOrderScanTaskService;
    @Autowired
    BdCountryService countryService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutOrderNoticeLackService stockoutOrderNoticeLackService;
    @Autowired
    StockoutBatchSplitCommonBuildService sortCommonBuildService;
    @Autowired
    StockoutBatchSplitService stockoutBatchSplitCommonService;
    @Autowired
    StockoutEasyScanTaskService stockoutEasyScanTaskService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;

    // 更新波次状态
    @Override
    public void updateBatchStatusToSorting(Integer batchId) {
        StockoutBatchEntity batchEntity = batchMapper.selectById(batchId);
        if (Objects.isNull(batchEntity)) {
            throw new BusinessServiceException("找不到波次");
        }
        if (!(StockoutWaveTaskStatusEnum.PICKING.name().equals(batchEntity.getStatus())
                || StockoutWaveTaskStatusEnum.WAIT_SORT.name().equals(batchEntity.getStatus()) || StockoutWaveTaskStatusEnum.SORTING.name().equals(batchEntity.getStatus()))) {
            throw new BusinessServiceException(String.format("波次：%s 不是拣货中,待分拣或分拣中", batchId));
        }
        if (batchEntity.getStatus() != null && !batchEntity.getStatus().equals(StockoutWaveTaskStatusEnum.SORTING.name())) {
            stockoutBatchService.updateBatchStatus(batchEntity, StockoutWaveTaskStatusEnum.SORTING);
        }
    }

    @Override
    public StockoutBatchSplitTaskItemSowWall getStockoutBatchSplitTaskItemSowWall(StockoutBatchSplitScanBarcodeRequest request, StockoutBatchSplitTaskSkuResponse response) {
        StockoutBatchSplitTaskItemSowWall stockoutBatchSplitTaskItemSowWall = new StockoutBatchSplitTaskItemSowWall();
        stockoutBatchSplitTaskItemSowWall.setTaskItemId(request.getItemId());
        return stockoutBatchSplitTaskItemSowWall;
    }

    @Override
    public void createEasyScanTask(StockoutBatchSplitScanBarcodeRequest request, boolean isStockoutOrderScanComplete) {
        //生成简易复核任务
        if (isStockoutOrderScanComplete) {
            stockoutEasyScanTaskService.createEasyScanTask(request.getStockoutOrderNo());
        }
    }

    // 拣货箱号查询
    public StockoutBatchSplitTaskSowWallResponse getBatchIdByInternalBoxCode(String internalBoxCode) {
        List<StockoutBatchSplitTaskPickingBoxInfo> stockoutBatchSplitTaskPickingBoxInfo = taskItemMapper.searchTaskItemByInternalBoxCode(internalBoxCode);
        if (CollectionUtils.isEmpty(stockoutBatchSplitTaskPickingBoxInfo)) {
            throw new BusinessServiceException("拣货箱无分拣数据");
        }
        if (stockoutBatchSplitTaskPickingBoxInfo.get(0).getStatus().equals(StockoutBatchSplitTaskStatus.SORTED.name())) {
            throw new BusinessServiceException("当前波次已经分拣完成");
        }
        // 拣货箱状态修改: 分拣中
        stockInternalBoxService.changeStockInternalBoxStatus(internalBoxCode, StockoutBatchSplitTaskStatus.SORTING.name());
        return stockoutBatchSplitCommonService.batchScanByBatchId(stockoutBatchSplitTaskPickingBoxInfo.get(0).getBatchId(), StockoutSplitServiceTypeEnum.SECOND_SORT);
    }

    @Transactional // 暂停扫描
    public void pauseScan(Integer batchId) {
        StockoutBatchSplitTaskEntity splitTaskEntity = taskMapper.selectOne(StockoutBatchSplitTaskQueryWrapper.buildStockoutBatchSplitTask(batchId));
        if (Objects.isNull(splitTaskEntity)) {
            throw new BusinessServiceException("找不到分拣任务信息");
        }
        stockoutBatchSplitTaskService.updateStockoutBatchSplitTaskStatus(splitTaskEntity.getTaskId(), StockoutBatchSplitTaskStatus.SUSPEND_SORT.name());
        List<StockoutBatchSplitTaskItemEntity> itemEntityList = splitItemService.getByTaskIdOrderByTaskId(splitTaskEntity.getTaskId());
        if (CollectionUtils.isEmpty(itemEntityList)) {
            return;
        }
        int batchQty = itemEntityList.stream().mapToInt(StockoutBatchSplitTaskItemEntity::getBatchQty).sum();
        int scanQty = itemEntityList.stream().mapToInt(StockoutBatchSplitTaskItemEntity::getScanQty).sum();
        String content = String.format("暂停分拣，当前需分拣%s件，已分拣%s件，未分拣%s件", batchQty, scanQty, batchQty - scanQty);
        stockoutBatchSplitTaskService.addSplitTaskLog(splitTaskEntity.getTaskId(), StockoutBatchSplitTaskTypeEnum.SUSPEND_SORT.getName(), content);
    }

    // 刷新sku列表,按缺货数由大到小排序
    public List<StockoutBatchSplitTaskItemSowWall> refreshSkuList(Integer batchId) {
        List<StockoutBatchSplitTaskItemSowWall> splitTaskItemSowWallList = taskItemMapper.searchTaskItemByBatchId(batchId);
        List<StockoutBatchSplitTaskItemSowWall> filterList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(splitTaskItemSowWallList)) {
            filterList = splitTaskItemSowWallList.stream().sorted((a, b) ->
                    Integer.compare(b.getBatchQty() - (b.getScanQty() != null ? b.getScanQty() : 0), a.getBatchQty() - (a.getScanQty() != null ? a.getScanQty() : 0))).collect(Collectors.toList());
            filterList = filterList.stream().map(item -> {
                StockoutBatchSplitTaskItemSowWall sowWall = new StockoutBatchSplitTaskItemSowWall();
                BeanUtilsEx.copyProperties(item, sowWall);
                sowWall.setStatusStr(!item.getStatus().equals(StockoutOrderStatusEnum.CANCELLING.name()) && !item.getStatus().equals(StockoutOrderStatusEnum.CANCELLED.name()) ? "未取消" : enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_DOC_STATUS.getName(), item.getStatus()));
                return sowWall;
            }).collect(Collectors.toList());
        }
        return filterList;
    }

    // 未扫描的置顶
    public List<StockoutBatchSplitTaskItemSowWall> sortList(List<StockoutBatchSplitTaskItemSowWall> splitTaskItemSowWallList) {
        List<StockoutBatchSplitTaskItemSowWall> filterList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(splitTaskItemSowWallList)) {
            // 先按加工完成数降序
            filterList = splitTaskItemSowWallList.stream().sorted(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getProcessQualifiedQty).reversed()).collect(Collectors.toList());
            filterList = filterList.stream().sorted((a, b) ->
                    Integer.compare(b.getBatchQty() - (b.getScanQty() != null ? b.getScanQty() : 0), a.getBatchQty() - (a.getScanQty() != null ? a.getScanQty() : 0))).collect(Collectors.toList());
        }
        return filterList;
    }

    /**
     * 通知缺货
     * 1. 若拣货箱内数量多余，归还原库位
     * 2. 修改分拣任务缺货数
     * 3. 更新波次明细 分拣任务 出库单明细 缺货数
     * -- 4. 更新复核任务待分拣数   删除
     * 5. 记录缺货日志
     * 6.生成缺货单
     * 7.对比库存预配 生成预配
     * 8.有库存 生成拣货任务
     * 9.无库存 确认缺货
     *
     * @param batchId
     */
    @Transactional
    public StockoutNoticeLackResponse noticeLack(Integer batchId) {
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(batchId);
        if (Objects.nonNull(batchEntity) && StockoutWavePlanTypeEnum.LACK_WAVE.name().equals(batchEntity.getBatchType()))
            throw new BusinessServiceException("缺货波次无法通知缺货！");
        StockoutBatchSplitTaskEntity batchSplitTask = stockoutBatchSplitTaskService.getBatchSplitTaskByBatchId(batchId);
        List<StockoutBatchSplitTaskItemEntity> batchSplitTaskItemList = splitItemService.getByTaskIdOrderByTaskId(batchSplitTask.getTaskId());
        //查找出 出库单件数 > 扫描数
        batchSplitTaskItemList = batchSplitTaskItemList.stream().filter(item -> item.getBatchQty() > item.getScanQty()).collect(Collectors.toList());

        List<StockoutNoticeLackItemBo> stockoutNoticeLackItemBoList = new ArrayList<>(batchSplitTaskItemList.size());
        BdSystemParameterEntity systemParameter = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_NOTICE_LACK_CLEAR_BOX.getKey());
        for (StockoutBatchSplitTaskItemEntity splitTaskItem : batchSplitTaskItemList) {
            //1.若拣货箱内数量多余，归还原库位   需配置参数 通知缺货是否自动归还拣货箱库存  true or false  未配置默认为false  modify by caish 2024-04-03
            if (Objects.nonNull(systemParameter) && StockConstant.TRUE.equalsIgnoreCase(systemParameter.getConfigValue()))
                splitItemService.lackUpdatePickingBoxQtyAndPositionQty(batchId, splitTaskItem);

            //2. 修改分拣任务缺货数
            Integer lackQty = splitTaskItem.getBatchQty() - splitTaskItem.getScanQty();
            splitTaskItem.setLackQty(lackQty);
            splitTaskItem.setIsLack(lackQty > 0 ? 1 : 0);
            splitItemService.updateStockoutBatchSplitTaskItem(splitTaskItem);

            //3. 更新缺货数
            if (splitTaskItem.getStockoutOrderNo() != null) {
                batchOrderItemService.updateStockoutBatchOrderItemSpecIdLackQty(batchId, splitTaskItem, lackQty);
                stockoutBatchSplitTaskService.updateStockoutBatchOrder(batchId, splitTaskItem.getStockoutOrderNo(), 1);
                stockoutOrderItemService.updateStockoutItemSkuLackQty(splitTaskItem.getStockoutOrderItemId(), splitTaskItem.getScanQty(), lackQty);
                stockoutBatchSplitTaskService.updateStockoutOrder(splitTaskItem.getStockoutOrderNo(), 1, StockoutOrderStatusEnum.OUTBOUNDING.name());
            }


            StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(splitTaskItem.getStockoutOrderItemId());
            StockoutNoticeLackItemBo stockoutNoticeLackItemBo = getStockoutNoticeLackItemBo(splitTaskItem, stockoutOrderItemEntity);
            stockoutNoticeLackItemBoList.add(stockoutNoticeLackItemBo);
        }

        List<StockoutOrderEntity> stockoutOrderList = getStockoutOrderEntityByBatchId(batchId);
        //生成noticeLack请求对象
        Map<Integer, StockoutOrderEntity> stockoutOrderMap = stockoutOrderList.stream().collect(Collectors.toMap(StockoutOrderEntity::getStockoutOrderId, Function.identity()));
        Map<Integer, List<StockoutNoticeLackItemBo>> stockoutNoticeLackItemMap = stockoutNoticeLackItemBoList.stream().collect(Collectors.groupingBy(temp -> temp.getStockoutOrderItem().getStockoutOrderId()));
        List<StockoutNoticeLackBo> noticeLackBoList = new ArrayList<>(stockoutNoticeLackItemMap.size());
        stockoutNoticeLackItemMap.forEach((stockoutOrderId, stockoutNoticeLackItemList) -> {
            StockoutOrderEntity stockoutOrder = stockoutOrderMap.get(stockoutOrderId);
            if (Objects.isNull(stockoutOrder)) return;
            if (StockoutOrderStatusEnum.CANCELLING.name().equals(stockoutOrder.getStatus())
                    || StockoutOrderStatusEnum.CANCELLED.name().equals(stockoutOrder.getStatus())) return;

            StockoutNoticeLackBo stockoutNoticeLackBo = new StockoutNoticeLackBo();
            stockoutNoticeLackBo.setStockoutOrder(stockoutOrder);
            stockoutNoticeLackBo.setStockoutNoticeLackItemList(stockoutNoticeLackItemList);
            stockoutNoticeLackBo.setLackSource(StockoutOrderLackSourceEnum.SECOND_SORT.name());
            stockoutNoticeLackBo.setTaskId(batchSplitTask.getTaskId());
            noticeLackBoList.add(stockoutNoticeLackBo);
        });

        if (noticeLackBoList.isEmpty()) throw new BusinessServiceException("所有出库单为取消中 或者 已取消");

        //6.生成缺货单
        //7.对比库存预配 生成预配
        //8.有库存 生成拣货任务
        //9.无库存 确认缺货
        return stockoutOrderNoticeLackService.noticeLack(noticeLackBoList, StockoutOrderLackSourceEnum.SECOND_SORT.name());
    }

    @NotNull
    private StockoutNoticeLackItemBo getStockoutNoticeLackItemBo(StockoutBatchSplitTaskItemEntity splitTaskItem, StockoutOrderItemEntity stockoutOrderItemEntity) {
        StockoutNoticeLackItemBo stockoutNoticeLackItemBo = new StockoutNoticeLackItemBo();
        stockoutNoticeLackItemBo.setStockoutOrderItem(stockoutOrderItemEntity);
        stockoutNoticeLackItemBo.setExpectedQty(splitTaskItem.getBatchQty());
        stockoutNoticeLackItemBo.setPickQty(splitTaskItem.getExpectedQty());
        stockoutNoticeLackItemBo.setScanQty(splitTaskItem.getScanQty());
        stockoutNoticeLackItemBo.setLackQty(splitTaskItem.getLackQty());
        return stockoutNoticeLackItemBo;
    }

    List<StockoutOrderEntity> getStockoutOrderEntityByBatchId(Integer batchId) {
        StockoutBatchEntity batchEntity = batchService.getStockoutBatchById(batchId);
        if (Objects.isNull(batchEntity)) {
            throw new BusinessServiceException("找不到该波次");
        }

        List<StockoutBatchOrderEntity> batchOrderList = stockoutBatchOrderService.findAllByBatchId(batchEntity.getBatchId());
        List<Integer> stockoutOrderIdList = batchOrderList.stream().map(StockoutBatchOrderEntity::getStockoutOrderId).collect(Collectors.toList());
        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .in(StockoutOrderEntity::getStockoutOrderId, stockoutOrderIdList));
        if (stockoutOrderList.isEmpty()) throw new BusinessServiceException("找不到对应出库单");

        return stockoutOrderList;
    }


    // 新增或更新缺货列表明细
    public void updateOrSaveOrderLackItem(StockoutOrderLackEntity stockoutOrderLackEntity, StockoutBatchSplitTaskItemEntity splitTaskItemEntity) {
        List<String> statusList = new ArrayList<>();
        statusList.add(StockoutOrderLackSkuStatusEnum.OUT_STOCK_PROCESSED.name());
        statusList.add(StockoutOrderLackSkuStatusEnum.OUT_STOCK_PICKING.name());
        statusList.add(StockoutOrderLackSkuStatusEnum.OUT_STOCK_CHECKING.name());
        StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(splitTaskItemEntity.getStockoutOrderItemId());
        StockoutOrderLackItemEntity orderLackItemEntity = stockoutOrderLackItemService.getOne(buildByOrderLackIdAndSpecIdAndOrderItemIdAndStatusList(
                stockoutOrderLackEntity.getStockoutOrderLackId(), splitTaskItemEntity.getSpecId(), stockoutOrderItemEntity.getOrderItemId(), statusList));
        if (Objects.isNull(orderLackItemEntity)) {
            orderLackItemEntity = new StockoutOrderLackItemEntity();
            BeanUtilsEx.copyProperties(stockoutOrderItemEntity, orderLackItemEntity);
            orderLackItemEntity.setCreateBy(loginInfoService.getName());
            orderLackItemEntity.setExpectedQty(splitTaskItemEntity.getBatchQty());
        }
        orderLackItemEntity.setPickQty(splitTaskItemEntity.getExpectedQty());
        orderLackItemEntity.setScanQty(splitTaskItemEntity.getScanQty());
        orderLackItemEntity.setLackQty(splitTaskItemEntity.getLackQty());
        orderLackItemEntity.setStockoutOrderItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
        orderLackItemEntity.setStockoutOrderLackId(stockoutOrderLackEntity.getStockoutOrderLackId());
        orderLackItemEntity.setLackSpaceAreaName(stockoutOrderItemEntity.getSpaceAreaName() != null ? stockoutOrderItemEntity.getSpaceAreaName() : "");
        orderLackItemEntity.setStatus(StockoutOrderLackSkuStatusEnum.OUT_STOCK_PROCESSED.name());
        orderLackItemEntity.setUpdateBy(loginInfoService.getName());
        stockoutOrderLackItemService.saveOrUpdate(orderLackItemEntity);
    }

    public List<StockInternalBoxItemEntity> getByBatchIdAndSku(Integer batchId, String sku) {
        return stockInternalBoxItemMapper.searchInternalBoxItemList(batchId, sku, StockInternalBoxTypeEnum.PICKING_BOX.name());
    }

    LambdaQueryWrapper<StockoutOrderLackItemEntity> buildByOrderLackIdAndSpecIdAndOrderItemIdAndStatusList(Integer stockoutOrderLackId, Integer specId, String orderItemId, List<String> statusList) {
        LambdaQueryWrapper<StockoutOrderLackItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(stockoutOrderLackId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getStockoutOrderLackId, stockoutOrderLackId);
        if (Objects.nonNull(specId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getSpecId, specId);
        if (StringUtils.hasText(orderItemId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getOrderItemId, orderItemId);
        if (!CollectionUtils.isEmpty(statusList))
            queryWrapper.in(StockoutOrderLackItemEntity::getStatus, statusList);
        return queryWrapper;
    }


    public PrintListResponse print(List<StockoutBatchSplitTaskPrintOrderInfo> orderInfoList, Boolean sort, List<String> stockoutOrderNoList, List<StockoutBatchSplitTaskItemPrintBo> splitTaskItemList) {
        PrintListResponse printListResponse = new PrintListResponse();
        Map<String, List<StockoutBatchSplitTaskItemPrintBo>> splitTaskItemMap = splitTaskItemList.stream()
                .collect(Collectors.groupingBy(StockoutBatchSplitTaskItemPrintBo::getStockoutOrderNo));
        List<StockoutBatchSplitTaskPrintResponse> responseList = this.getBaseMapper().queryPrintData(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(responseList))
            return printListResponse;

        StockoutBatchSplitTaskPrintResponse firstResponse = responseList.get(0);

        PrintTemplateEntity templateEntity = StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name().equals(firstResponse.getStockoutType())
                ? printService.getByName(PrintTemplateNameEnum.STOCKOUT_BATCH_SPLIT_TASK_INTERNAL_PURCHASE.getTemplateName())
                : printService.getByName(PrintTemplateNameEnum.STOCKOUT_BATCH_SPLIT_TASK.getTemplateName());


        Map<String, StockoutBatchSplitTaskPrintOrderInfo> map = orderInfoList.stream().collect(Collectors.toMap(StockoutBatchSplitTaskPrintOrderInfo::getStockoutOrderNo, item -> item));
        List<String> result = new ArrayList<>();
        // 构建打印数据
        responseList.forEach(item -> buildPrintList(map, item));
        if (sort) {
            // 单双排序
            responseList = StockoutBuilding.buildPrintListInOrderSplit(responseList);
        }
        String value = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKOUT_B2B_SELF_LOGISTICS.getKey());
        // 开始打印
        responseList.forEach(item -> {
            // 判断是否内贸/天纵的单，需展示sku
            if (item.getWorkspace().contains(StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.getName())
                || StringUtils.hasText(value) && StrUtil.equalsAnyIgnoreCase(item.getLogisticsCompany(), value.split(","))
                    || StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name().equals(firstResponse.getStockoutType())) { //内购出库
                result.add(printDomestic(splitTaskItemMap.get(item.getStockoutOrderNo()), item, templateEntity.getContent()));
            } else {
                if (item.getWorkspace().contains(StockoutOrderWorkSpaceEnum.B2B_AREA.getName()) && ("Puerto Rico".equalsIgnoreCase(item.getReceiverState()) || "PR".equalsIgnoreCase(item.getReceiverState()))
                        && ("波多黎各".equalsIgnoreCase(item.getCountryCode()) || "美国".equalsIgnoreCase(item.getCountryCode())))
                    item.setSpaceMemo("(注意！波多黎各)" + item.getSpaceMemo());
                String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), item);
                result.add(transfer);
                //B2B多打一张
                if (item.getWorkspace().contains(StockoutOrderWorkSpaceEnum.B2B_AREA.getName()))
                    result.add(transfer);
            }
        });
        // 过滤富文本html标签
        printListResponse.setHtmlList(result.stream().map(item -> item.replaceAll("<br>", "")).collect(Collectors.toList()));
        printListResponse.setSpec(templateEntity.getSpec());
        printListResponse.setTemplateName(templateEntity.getName());
        return printListResponse;
    }

    public PrintListResponse printByOrderNo(StockoutPrintOrderRequest request) {
        PrintListResponse printListResponse = new PrintListResponse();
        List<StockoutBatchSplitTaskPrintOrderInfo> orderInfoList = this.getBaseMapper().queryOrderInfoByOrderNo(request);
        if (CollectionUtils.isEmpty(orderInfoList))
            throw new BusinessServiceException("无法找到出库单的分拣任务！请核对");
        batchOrderService.buildOrderQty(orderInfoList);
        List<String> stockoutOrderNoList = orderInfoList.stream().map(StockoutBatchSplitTaskPrintOrderInfo::getStockoutOrderNo).collect(Collectors.toList());
        //如果同时存在正常波次和缺货波次，打印缺货波次的配货单
        if (orderInfoList.stream().map(StockoutBatchSplitTaskPrintOrderInfo::getBatchId).distinct().count() > 1
                && orderInfoList.stream().anyMatch(info -> StockoutWavePlanTypeEnum.LACK_WAVE.name().equals(info.getBatchType()))) {
            stockoutOrderNoList = stockoutOrderNoList.stream().distinct().collect(Collectors.toList());
            orderInfoList = orderInfoList.stream().filter(info -> StockoutWavePlanTypeEnum.LACK_WAVE.name().equals(info.getBatchType())).collect(Collectors.toList());
        }
        List<StockoutBatchSplitTaskPrintResponse> responseList = this.getBaseMapper().queryPrintData(stockoutOrderNoList);
        if (CollectionUtils.isEmpty(responseList))
            return printListResponse;

        StockoutBatchSplitTaskPrintResponse firstResponse = responseList.get(0);

        PrintTemplateEntity templateEntity = StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name().equals(firstResponse.getStockoutType())
                ? printService.getByName(PrintTemplateNameEnum.STOCKOUT_BATCH_SPLIT_TASK_INTERNAL_PURCHASE.getTemplateName())
                : printService.getByName(PrintTemplateNameEnum.STOCKOUT_BATCH_SPLIT_TASK.getTemplateName());

        Map<String, StockoutBatchSplitTaskPrintOrderInfo> map = orderInfoList.stream().collect(Collectors.toMap(StockoutBatchSplitTaskPrintOrderInfo::getStockoutOrderNo, item -> item));
        List<String> list = new ArrayList<>();
        Map<String, List<StockoutBatchSplitTaskItemPrintBo>> splitTaskItemMap = splitItemService.list(new LambdaQueryWrapper<StockoutBatchSplitTaskItemEntity>()
                        .in(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo, stockoutOrderNoList))
                .stream().map(item -> new StockoutBatchSplitTaskItemPrintBo(item.getStockoutOrderNo(), item.getSku(), item.getBatchQty()))
                .collect(Collectors.groupingBy(StockoutBatchSplitTaskItemPrintBo::getStockoutOrderNo));
        String value = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKOUT_B2B_SELF_LOGISTICS.getKey());
        responseList.forEach(item -> {
            buildPrintList(map, item);
            // 判断是否内贸/伟跃天纵的单，需展示前十个sku
            if (item.getWorkspace().contains(StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.getName())
                || StringUtils.hasText(value) && StrUtil.equalsAnyIgnoreCase(item.getLogisticsCompany(), value.split(","))
                    || StockoutOrderTypeEnum.INTERNAL_PURCHASE_DELIVERY.name().equals(firstResponse.getStockoutType())) {
                list.add(printDomestic(splitTaskItemMap.get(item.getStockoutOrderNo()), item, templateEntity.getContent()));
            } else {
                if (item.getWorkspace().contains(StockoutOrderWorkSpaceEnum.B2B_AREA.getName()) && ("Puerto Rico".equalsIgnoreCase(item.getReceiverState()) || "PR".equalsIgnoreCase(item.getReceiverState()))
                        && ("波多黎各".equalsIgnoreCase(item.getCountryCode()) || "美国".equalsIgnoreCase(item.getCountryCode())))
                    item.setSpaceMemo("(注意！波多黎各)" + item.getSpaceMemo());
                String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), item);
                list.add(transfer);
                if (item.getWorkspace().contains(StockoutOrderWorkSpaceEnum.B2B_AREA.getName())) {
                    list.add(transfer);
                }
            }
        });
        // 过滤富文本html标签
        printListResponse.setHtmlList(list.stream().map(item -> item.replaceAll("<br>", "")).collect(Collectors.toList()));
        printListResponse.setSpec(templateEntity.getSpec());
        printListResponse.setTemplateName(templateEntity.getName());
        LOGGER.info("printListResponse: {}", JsonMapper.toJson(printListResponse));
        return printListResponse;
    }

    private void buildPrintList(Map<String, StockoutBatchSplitTaskPrintOrderInfo> map, StockoutBatchSplitTaskPrintResponse item) {
        item.setBatchId(map.get(item.getStockoutOrderNo()).getBatchId());
        if (!Objects.isNull(map.get(item.getStockoutOrderNo()).getWeight()))
            item.setWeight(map.get(item.getStockoutOrderNo()).getWeight().divide(BigDecimal.valueOf(1000), BigDecimal.ROUND_HALF_UP));
        if (!Objects.isNull(item.getEstimateWeight()))
            item.setEstimateWeight(item.getEstimateWeight().divide(BigDecimal.valueOf(1000), BigDecimal.ROUND_HALF_UP));
        item.setOutlet(map.get(item.getStockoutOrderNo()).getOutlet());
        item.setQty(map.get(item.getStockoutOrderNo()).getQty());
        item.setLackQty(map.get(item.getStockoutOrderNo()).getLackQty());
        if (StringUtils.hasText(item.getReceiverInfo())) {
            String receiverInfoOriginalText = Objects.requireNonNull(AesEncryptUtil.desEncrypt(item.getReceiverInfo())).trim();
            StockoutReceiverInfo receiverInfo = JsonMapper.fromJson(receiverInfoOriginalText, StockoutReceiverInfo.class);
            if (!Objects.isNull(receiverInfo)) {
                item.setCountryCode(countryService.getByCountryEnNameOrCode(receiverInfo));
                item.setReceiverCountry(receiverInfo.getReceiverCountry());
                item.setReceiverName(receiverInfo.getReceiverName());
                item.setReceiverMobile(StringUtils.hasText(receiverInfo.getReceiverMobile()) ? receiverInfo.getReceiverMobile() : receiverInfo.getReceiverPhone());
                item.setPostCode(receiverInfo.getReceiverZip());
                item.setReceiverState(receiverInfo.getReceiverState());
                buildAddress(item, receiverInfo);
            }
        }
        item.setPrintDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        item.setWorkspace(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), item.getWorkspace()) + "配货单");
        item.setNotifyShipStatus(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_DELIVERY_NOTICE.getName(), item.getNotifyShipStatus()));
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(item.getStockoutOrderId());
        item.setOrderNo(orderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.joining(",")));
        item.setSpaceMemo(orderItemEntityList.stream().map(StockoutOrderItemEntity::getSpaceMemo).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        if (StringUtils.hasText(item.getCountryCode())) {
            item.setCountryCode(CountryCodeConstant.getCountryCodeMap().get(item.getCountryCode()));
        }
        StockoutOrderEntity stockoutOrderNo = stockoutOrderService.getByStockoutOrderNo(item.getStockoutOrderNo());
        item.setProcessOrder(stockoutOrderNo.getNeedProcess() ? "加工" : "");
    }

    // 中文正序，英文倒叙
    private void buildAddress(StockoutBatchSplitTaskPrintResponse item, StockoutReceiverInfo receiverInfo) {
        if (PrintTransferUtils.hasChineseByRange(receiverInfo.getReceiverAddress()) || PrintTransferUtils.hasChineseByRange(receiverInfo.getReceiverDistrict())) {
            item.setReceiverAddress(String.format("%s %s %s %s %s", receiverInfo.getReceiverCountry(), receiverInfo.getReceiverState(),
                    receiverInfo.getReceiverCity(), receiverInfo.getReceiverDistrict(), receiverInfo.getReceiverAddress()));
        } else {
            item.setReceiverAddress(String.format("%s %s %s %s %s", receiverInfo.getReceiverAddress(), receiverInfo.getReceiverDistrict(),
                    receiverInfo.getReceiverCity(), receiverInfo.getReceiverState(), receiverInfo.getReceiverCountry()));
        }
    }

    private String printDomestic(List<StockoutBatchSplitTaskItemPrintBo> itemList, StockoutBatchSplitTaskPrintResponse item, String template) {
        if (CollectionUtils.isEmpty(itemList))
            return template;
        Map<String, Integer> collect = itemList.stream().collect(Collectors.toMap(StockoutBatchSplitTaskItemPrintBo::getSku, StockoutBatchSplitTaskItemPrintBo::getQty, Integer::sum));
        int i = 1;
        List<DomesticSkuDTO> printSkuDTOS = new ArrayList<>();
        List<DomesticSkuDTO> printSkuDTOS2 = new ArrayList<>();
        for (Map.Entry<String, Integer> m : collect.entrySet()) {
            DomesticSkuDTO dto = new DomesticSkuDTO();
            dto.setSku(m.getKey());
            dto.setNum(m.getValue());
            if ((i & 1) == 1) {
                printSkuDTOS.add(dto);
            } else
                printSkuDTOS2.add(dto);
            i++;
        }
        item.setSkuList1(JsonMapper.toJson(printSkuDTOS));
        item.setSkuList2(JsonMapper.toJson(printSkuDTOS2));
        return PrintTransferUtils.transfer(template, item);
    }

    public PrintListResponse printMergeBatch(IdListRequest request) {
        PrintListResponse printListResponse = new PrintListResponse();
        List<StockoutSplitBatchType> typeList = this.batchMapper.getSplitBatchType(request.getIdList());
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_BATCH_SPLIT_TASK_MERGE.getTemplateName());
        //合并波次打印子波次
        List<String> htmlList = new ArrayList<>(request.getIdList().size());
        List<StockoutSplitBatchType> mergeBatchTypes = typeList.stream().filter(type -> type.getIsMergeBatch().equals(1)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(mergeBatchTypes)) {
            Map<Integer, List<StockoutSplitBatchType>> mergeBatchTypesMap = mergeBatchTypes.stream().collect(Collectors.groupingBy(StockoutSplitBatchType::getBatchId));
            mergeBatchTypesMap.forEach((batchId, batchTypes) -> printSubBatch(batchId, batchTypes, htmlList, templateEntity));
        }
        //正常波次打印出库单配货单
        List<StockoutSplitBatchType> noMergeBatchTypes = typeList.stream().filter(type -> type.getIsMergeBatch().equals(0)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noMergeBatchTypes)) {
            printNormalBatch(noMergeBatchTypes, htmlList);
        }
        printListResponse.setHtmlList(htmlList);
        printListResponse.setSpec(templateEntity.getSpec());
        printListResponse.setTemplateName(templateEntity.getName());
        return printListResponse;
    }

    /**
     * 打印所有子波次
     *
     * @param batchId
     * @param batchTypes
     * @param htmlList
     * @param templateEntity
     */
    private void printSubBatch(Integer batchId, List<StockoutSplitBatchType> batchTypes, List<String> htmlList, PrintTemplateEntity templateEntity) {
        Map<Integer, List<StockoutBatchSplitTaskItemEntity>> splitTaskItemMap = taskItemMapper.selectList(new LambdaQueryWrapper<StockoutBatchSplitTaskItemEntity>().in(
                        StockoutBatchSplitTaskItemEntity::getTaskId, batchTypes.stream().map(StockoutSplitBatchType::getTaskId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(StockoutBatchSplitTaskItemEntity::getSubBatchId));
        //用于排序
        List<StockoutBatchSplitPrintSort> printSortList = new ArrayList<>();
        splitTaskItemMap.forEach((subBatchId, splitTaskItemList) -> {
            StockoutBatchEntity subBatch = batchService.getStockoutBatchById(subBatchId);
            if (StockoutPickingTypeEnum.WHOLE_PICK.name().equals(subBatch.getPickingType())) {  //按单拣货找出所有子波次打印
                printSortList.add(printWholePickBatch(subBatch, splitTaskItemList));
            } else { //非按单拣货
                StockoutBatchSplitPrint splitPrint = printOneSubBatch(batchId, subBatch, splitTaskItemList);
                StockoutBatchSplitPrintSort printSort = new StockoutBatchSplitPrintSort();
                printSort.setBoxNo(splitPrint.getBoxNo());
                printSort.setBoxNoIndex(splitPrint.getBoxNoIndex());
                printSort.setWorkspace(splitPrint.getWorkspace());
                printSort.setHtml(PrintTransferUtils.transfer(templateEntity.getContent(), splitPrint));
                printSortList.add(printSort);
            }
        });
        // 奇偶排序打印
        StockoutBuilding.buildPrintListInOrder(printSortList, htmlList);
    }

    /**
     * 按单拣货子波次打印
     *
     * @param subBatch
     * @param splitTaskItemList
     * @return
     */
    private StockoutBatchSplitPrintSort printWholePickBatch(StockoutBatchEntity subBatch, List<StockoutBatchSplitTaskItemEntity> splitTaskItemList) {
        List<StockoutBatchOrderEntity> batchOrderList = stockoutBatchOrderService.findAllByBatchId(subBatch.getBatchId());
        if (batchOrderList.isEmpty()) {
            LOGGER.error("找不到波次 {} 下的出库单", subBatch.getBatchId());
            return null;
        }
        if (CollectionUtils.isEmpty(splitTaskItemList))
            throw new BusinessServiceException(String.format("波次 %s 的合并波次分拣明细不存在", subBatch.getBatchId()));

        StockoutBatchOrderEntity firstBatchOrder = batchOrderList.get(0);
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(firstBatchOrder.getStockoutOrderId());

        StockoutBatchSplitTaskPrintOrderInfo orderInfo = new StockoutBatchSplitTaskPrintOrderInfo();
        orderInfo.setBatchId(subBatch.getBatchId());
        orderInfo.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        orderInfo.setOutlet(splitTaskItemList.get(0).getOutlet());
        List<StockoutBatchSplitTaskItemPrintBo> splitTaskItemPrintBoList = splitTaskItemList.stream()
                .map(item -> new StockoutBatchSplitTaskItemPrintBo(stockoutOrderEntity.getStockoutOrderNo(), item.getSku(), item.getBatchQty()))
                .collect(Collectors.toList());

        List<StockoutBatchSplitTaskPrintOrderInfo> orderInfoList = Collections.singletonList(orderInfo);
        batchOrderService.buildOrderQtyByBatch(orderInfoList);
        List<String> htmlList = print(orderInfoList, Boolean.FALSE, Collections.singletonList(stockoutOrderEntity.getStockoutOrderNo()), splitTaskItemPrintBoList).getHtmlList();
        if (CollectionUtils.isEmpty(htmlList)) return null;
        StockoutBatchSplitPrintSort printSort = new StockoutBatchSplitPrintSort();
        printSort.setBoxNo(StringUtils.join(splitTaskItemList.stream().map(item -> item.getOutlet().toString()).distinct().collect(Collectors.toList()), ','));
        printSort.setBoxNoIndex(Integer.parseInt(printSort.getBoxNo()));
        printSort.setWorkspace(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), subBatch.getWorkspace()));
        printSort.setHtml(htmlList.get(0));
        return printSort;
    }

    /**
     * 打印一个子波次
     *
     * @param batchId
     * @param subBatch
     * @param splitTaskItemList
     * @return
     */
    private StockoutBatchSplitPrint printOneSubBatch(Integer batchId, StockoutBatchEntity subBatch, List<StockoutBatchSplitTaskItemEntity> splitTaskItemList) {
        StockoutBatchSplitPrint print = new StockoutBatchSplitPrint();
        print.setDateTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        print.setBatchId(subBatch.getBatchId());
        print.setMergeBatchId(batchId);
        print.setBoxNo(StringUtils.join(splitTaskItemList.stream().map(item -> item.getOutlet().toString()).distinct().collect(Collectors.toList()), ','));
        StockoutBatchEntity byId = stockoutBatchService.getById(subBatch.getBatchId());
        print.setWorkspace(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), byId.getWorkspace()));
        print.setPickingType(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), byId.getPickingType()));
        print.setLogisticsCompany(byId.getLogisticsCompany());
        print.setWeight(getPayWeight(splitTaskItemList));
        if (!StringUtils.hasText(print.getBoxNo()) || print.getBoxNo().contains(",")) {
            throw new BusinessServiceException(print.getBatchId() + "在合并波次下，该波次应有且只有一个分拣口");
        }
        print.setBoxNoIndex(Integer.parseInt(print.getBoxNo()));
        return print;
    }

    private void printNormalBatch(List<StockoutSplitBatchType> batchTypes, List<String> htmlList) {
        List<Integer> taskIdList = batchTypes.stream().map(StockoutSplitBatchType::getTaskId).collect(Collectors.toList());
        List<StockoutBatchSplitTaskPrintOrderInfo> orderInfoList = this.getBaseMapper().queryOrderInfo(taskIdList);
        List<String> stockoutOrderNoList = orderInfoList.stream().map(StockoutBatchSplitTaskPrintOrderInfo::getStockoutOrderNo).distinct().collect(Collectors.toList());
        List<StockoutBatchSplitTaskItemPrintBo> splitTaskItemList = splitItemService.list(new LambdaQueryWrapper<StockoutBatchSplitTaskItemEntity>()
                        .in(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo, stockoutOrderNoList))
                .stream().map(item -> new StockoutBatchSplitTaskItemPrintBo(item.getStockoutOrderNo(), item.getSku(), item.getBatchQty()))
                .collect(Collectors.toList());
        batchOrderService.buildOrderQty(orderInfoList);
        htmlList.addAll(print(orderInfoList, Boolean.TRUE, stockoutOrderNoList, splitTaskItemList).getHtmlList());
    }

    private BigDecimal getPayWeight(List<StockoutBatchSplitTaskItemEntity> orderItemEntityList) {
        BigDecimal result = BigDecimal.ZERO;
        for (StockoutBatchSplitTaskItemEntity item : orderItemEntityList) {
            ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopBySku(item.getSku());
            result = result.add(ProductSpecInfoService.getSkuWeightDefault(specInfoEntity).multiply(new BigDecimal(item.getExpectedQty())));
        }
        return result.divide(new BigDecimal(1000));
    }

    @Override
    public void checkBatch(StockoutBatchEntity batchEntity) {
        if (batchEntity.getIsMergeBatch() != null && batchEntity.getIsMergeBatch() == 1) {
            throw new BusinessServiceException("请选择正常波次分拣");
        }
        if (!batchEntity.getPickingType().equals(StockoutPickingTypeEnum.SECOND_SORT.name())) {
            throw new BusinessServiceException("请选择二次分拣的波次");
        }
    }

    @Override
    public void checkSplitTask(StockoutBatchEntity batchEntity, StockoutBatchSplitTaskEntity stockoutBatchSplitTaskEntity) {
        if (Objects.isNull(stockoutBatchSplitTaskEntity)) {
            throw new BusinessServiceException("分拣任务不存在");
        }
        if (stockoutBatchSplitTaskEntity.getStatus() != null && stockoutBatchSplitTaskEntity.getStatus().equals(StockoutBatchSplitTaskStatus.SORTED.name())) {
            throw new BusinessServiceException("此波次已分拣完成");
        }
        if (!StockoutSortingTypeEnum.MANUAL_SORT.name().equals(stockoutBatchSplitTaskEntity.getBatchSplitType())
                && !StockoutSortingTypeEnum.SOW_SEED_SORT.name().equals(stockoutBatchSplitTaskEntity.getBatchSplitType())) {
            throw new BusinessServiceException("该分拣任务非 人工分拣 或 播种墙分拣");
        }
    }
}
