package com.nsy.wms.controller.stockout;


import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareConstractCompanyAuditNoPassRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareConstractCompanyAuditPassRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareConstractPageRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareContractGenerateRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareContractSetUrlRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareContractStartRescindedRequest;
import com.nsy.api.wms.response.base.NoticeMsgResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomsDeclareConstractPageResponse;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareAEOService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractCancelService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractOpService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 合同表控制层
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
@RestController
@Api(tags = "关单合同列表相关接口")
public class StockoutCustomsDeclareContractController extends BaseController {
    @Resource
    StockoutCustomsDeclareContractService contractService;
    @Resource
    StockoutCustomsDeclareContractOpService contractOpService;
    @Resource
    StockoutCustomsDeclareAEOService declareAEOService;
    @Resource
    StockoutCustomsDeclareContractCancelService declareContractCancelService;

    @ApiOperation(value = "分页", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/page-list")
    public PageResponse<StockoutCustomsDeclareConstractPageResponse> pageList(@RequestBody StockoutCustomsDeclareConstractPageRequest request) {
        return contractService.pageList(request);
    }

    @ApiOperation(value = "状态统计", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/status-count")
    public List<StatusCountResponse> statusCount() {
        return contractService.tabCount();
    }


    @ApiOperation(value = "公司审核通过", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/company-audit-pass")
    public void companyAuditPass(@RequestBody StockoutCustomsDeclareConstractCompanyAuditPassRequest request) {
        contractService.companyAuditPass(request);
    }

    @ApiOperation(value = "公司审核不通过", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/company-audit-no-pass")
    public void companyAuditNoPass(@RequestBody StockoutCustomsDeclareConstractCompanyAuditNoPassRequest request) {
        contractService.companyAuditNoPass(request);
    }

    @ApiOperation(value = "获取预览文件", produces = "application/json")
    @GetMapping("/stockout-customs-declare-contract/getFileUrl")
    public String getFileUrl(@RequestParam Integer contractId) {
        return contractService.getFileUrl(contractId);
    }


    @ApiOperation(value = "检查撤销合同", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/check-revoke/{contractId}")
    public NoticeMsgResponse checkRevoke(@PathVariable Integer contractId) {
        return declareContractCancelService.checkRevoke(contractId);
    }

    @ApiOperation(value = "撤销合同", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/revoke/{contractId}")
    public void revoke(@PathVariable Integer contractId) {
        declareContractCancelService.revoke(contractId);
    }

    @ApiOperation(value = "生成AEO", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/generate-aeo")
    public void generateAEO(@RequestBody IdListRequest request) {
        declareAEOService.generateFromContract(request.getIdList());
    }

    @ApiOperation(value = "冲库存手动生成合同", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/manual-generate-contract")
    public void manualGenerateContract(@RequestBody StockoutCustomsDeclareContractGenerateRequest request) {
        contractOpService.manualGenerateContract(request);
    }

    @ApiOperation(value = "合同设置地址", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/contract-set-url")
    public void contractSetUrl(@RequestBody StockoutCustomsDeclareContractSetUrlRequest request) {
        contractOpService.contractSetUrl(request);
    }

    @ApiOperation(value = "开启解约流程", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/start-rescinded")
    public void startRescinded(@RequestBody StockoutCustomsDeclareContractStartRescindedRequest request) {
        declareContractCancelService.startRescinded(request);
    }

    @ApiOperation(value = "批量冲库存删除合同", notes = "批量删除冲库存生成的合同，删除后关联关单状态变为待处理", produces = "application/json")
    @PostMapping("/stockout-customs-declare-contract/cancel-inventory-contract")
    public void cancelInventoryContract(@RequestBody IdListRequest request) {
        declareContractCancelService.batchCancelInventoryContract(request.getIdList());
    }
}

