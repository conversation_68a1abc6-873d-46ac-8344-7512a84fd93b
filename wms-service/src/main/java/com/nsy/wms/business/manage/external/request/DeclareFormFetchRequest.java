package com.nsy.wms.business.manage.external.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXD
 * 2022/12/14
 **/

public class DeclareFormFetchRequest {
    // 报关单号
    @JsonProperty("EntryId")
    private String entryId;

    @JsonProperty("IEFlag")
    private String iEFlag;

    @JsonProperty("TrafMode")
    private String trafMode;

    @JsonProperty("EntryType")
    private String entryType;

    // 申报日期
    @JsonProperty("DDate")
    private String dDate;

    // 进出口日期
    @JsonProperty("IEDate")
    private String iEDate;

    @JsonProperty("AgentCode")
    private String agentCode;

    @JsonProperty("AgentName")
    private String agentName;

    //只打印不新增
    @JsonProperty("JustPrint")
    private Boolean justPrint = Boolean.FALSE;

    // 申报日期 - 开始
    @JsonProperty("DDateBegin")
    private String dDateBegin;

    // 申报日期 - 结束
    @JsonProperty("DDateEnd")
    private String dDateEnd;

    private Integer dayNum;

    public String getEntryId() {
        return entryId;
    }

    public void setEntryId(String entryId) {
        this.entryId = entryId;
    }

    public String getiEFlag() {
        return iEFlag;
    }

    public void setiEFlag(String iEFlag) {
        this.iEFlag = iEFlag;
    }

    public String getTrafMode() {
        return trafMode;
    }

    public void setTrafMode(String trafMode) {
        this.trafMode = trafMode;
    }

    public String getEntryType() {
        return entryType;
    }

    public void setEntryType(String entryType) {
        this.entryType = entryType;
    }

    public String getdDate() {
        return dDate;
    }

    public void setdDate(String dDate) {
        this.dDate = dDate;
    }

    public String getiEDate() {
        return iEDate;
    }

    public void setiEDate(String iEDate) {
        this.iEDate = iEDate;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public Boolean getJustPrint() {
        return justPrint;
    }

    public void setJustPrint(Boolean justPrint) {
        this.justPrint = justPrint;
    }

    public String getdDateBegin() {
        return dDateBegin;
    }

    public void setdDateBegin(String dDateBegin) {
        this.dDateBegin = dDateBegin;
    }

    public String getdDateEnd() {
        return dDateEnd;
    }

    public void setdDateEnd(String dDateEnd) {
        this.dDateEnd = dDateEnd;
    }

    public Integer getDayNum() {
        return dayNum;
    }

    public void setDayNum(Integer dayNum) {
        this.dayNum = dayNum;
    }
}
