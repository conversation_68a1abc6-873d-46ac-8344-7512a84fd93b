package com.nsy.wms.business.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.bd.BdSpace;
import com.nsy.api.wms.domain.bd.BdSpaceArea;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.enumeration.bd.BdChangLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.request.bd.BdSpaceAreaAddRequest;
import com.nsy.api.wms.request.bd.BdSpaceAreaListRequest;
import com.nsy.api.wms.request.bd.BdSpaceAreaSelectRequest;
import com.nsy.api.wms.request.bd.BdSpaceAreaUpdateDeletedRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.config.RedisConfig;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceAreaEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdAreaMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdSpaceAreaMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.RedisClient;
import com.nsy.wms.utils.Validator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.Table;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class BdSpaceAreaService extends ServiceImpl<BdSpaceAreaMapper, BdSpaceAreaEntity> {

    @Autowired
    BdSpaceService spaceService;
    @Autowired
    BdAreaMapper bdAreaMapper;
    @Autowired
    BdPositionService positionService;
    @Autowired
    BdChangeLogService changeLogService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    RedisClient redisClient;
    @Autowired
    BdAreaService areaService;
    @Autowired
    StockInternalBoxService internalBoxService;
    @Autowired
    private StockService stockService;

    /**
     * 通过区域id获取
     *
     * @param areaId
     * @return
     */
    public List<BdSpaceAreaEntity> getListByAreaId(Integer areaId) {
        return list(new LambdaQueryWrapper<BdSpaceAreaEntity>().eq(BdSpaceAreaEntity::getAreaId, areaId).eq(BdSpaceAreaEntity::getIsDeleted, Boolean.FALSE));
    }

    /**
     * 获取库区详情
     */
    public BdSpaceArea getSpaceAreaById(Integer spaceAreaId) {
        BdSpaceAreaEntity spaceAreaEntity = getById(spaceAreaId);
        return buildSpaceArea(spaceAreaEntity);
    }

    private BdSpaceArea buildSpaceArea(BdSpaceAreaEntity spaceAreaEntity) {
        BdSpaceArea spaceArea = new BdSpaceArea();
        BeanUtilsEx.copyProperties(spaceAreaEntity, spaceArea);
        spaceArea.setSpaceAreaTypeStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_SPACE_AREA_TYPE.getName(), spaceAreaEntity.getSpaceAreaType()));
        return spaceArea;
    }

    /**
     * 保存库区
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.SPACE_AREA_NAME_SELECT, allEntries = true)
    public void saveSpaceArea(BdSpaceAreaAddRequest request, Integer id) {
        validateSpaceArea(request, id);
        //校验库区下的库位库存必须为0，才允许修改
        this.validateEditSpaceArea(id);
        String content;
        String detail = "";
        BdSpaceAreaEntity spaceAreaEntity = new BdSpaceAreaEntity();
        if (Objects.isNull(id)) {
            // 校验库区名称是否已存在
            BdSpaceAreaEntity tempSpaceAreaEntity = getBySpaceAreaName(request.getSpaceAreaName());
            if (Objects.nonNull(tempSpaceAreaEntity)) {
                throw new BusinessServiceException(String.format("库区名称【%s】已使用, 请更换库区名称", request.getSpaceAreaName()));
            }
            // 校验库区编号是否已存在
            tempSpaceAreaEntity = getBySpaceAreaCode(request.getSpaceAreaCode());
            if (Objects.nonNull(tempSpaceAreaEntity)) {
                throw new BusinessServiceException(String.format("库区编号【%s】已使用, 请更换库区编号", request.getSpaceAreaCode()));
            }
            BeanUtils.copyProperties(request, spaceAreaEntity);
            spaceAreaEntity.setIsDeleted(Boolean.FALSE);
            spaceAreaEntity.setCreateBy(loginInfoService.getName());
            spaceAreaEntity.setCreateDate(new Date());
            spaceAreaEntity.setOutlet(Objects.isNull(request.getOutlet()) ? Integer.valueOf(0) : request.getOutlet());
            content = String.format("%s 新增库区:%s", loginInfoService.getName(), spaceAreaEntity.getSpaceAreaName());
        } else {
            spaceAreaEntity = getById(id);
            if (spaceAreaEntity == null) {
                throw new BusinessServiceException("库区不存在");
            }
            detail = JsonMapper.toJson(spaceAreaEntity);
            spaceAreaEntity.setSpaceId(request.getSpaceId());
            spaceAreaEntity.setAreaId(request.getAreaId());
            spaceAreaEntity.setSpaceAreaType(request.getSpaceAreaType());
            spaceAreaEntity.setDescription(request.getDescription());
            spaceAreaEntity.setUpdateBy(loginInfoService.getName());
            spaceAreaEntity.setUpdateDate(new Date());
            spaceAreaEntity.setTransferBoxId(null);
            spaceAreaEntity.setTransferBoxCode(null);
            spaceAreaEntity.setSort(request.getSort());
            spaceAreaEntity.setOutlet(request.getOutlet());
            content = String.format("%s 修改库区:%s", loginInfoService.getName(), spaceAreaEntity.getSpaceAreaName());

            BdSpace spaceById = spaceService.getSpaceById(spaceAreaEntity.getSpaceId());

            //库区下的库位也需要修改
            positionService.update(new LambdaUpdateWrapper<BdPositionEntity>()
                    .set(BdPositionEntity::getSpaceAreaName, spaceAreaEntity.getSpaceAreaName())
                    .set(BdPositionEntity::getSpaceId, spaceById.getSpaceId())
                    .set(BdPositionEntity::getSpaceName, spaceById.getSpaceName())
                    .eq(BdPositionEntity::getSpaceAreaId, id));
        }
        if (StringUtils.hasText(request.getTransferBoxCode())) {
            StockInternalBox internalBox = getTransferBox(request.getTransferBoxCode());
            spaceAreaEntity.setTransferBoxCode(internalBox.getInternalBoxCode());
            spaceAreaEntity.setTransferBoxId(internalBox.getInternalBoxId());
        }
        saveOrUpdate(spaceAreaEntity);
        Boolean hasKey = redisClient.hasKey(RedisConfig.MODULE_NAME + ":S:SPACE_AREA_NAME_SELECT_BY_SPACE_ID:" + spaceAreaEntity.getSpaceId());
        if (hasKey) {
            redisClient.del(RedisConfig.MODULE_NAME + ":S:SPACE_AREA_NAME_SELECT_BY_SPACE_ID:" + spaceAreaEntity.getSpaceId());
        }
        changeLogService.addChangeLog(BdChangLogTypeEnum.SPACE_AREA, content, BdSpaceAreaEntity.class.getAnnotation(Table.class).name(), detail);
    }

    private void validateEditSpaceArea(Integer id) {
        if (Objects.isNull(id)) {
            return;
        }
        BdSpaceAreaEntity bdSpaceAreaEntity = getById(id);
        if (Objects.isNull(bdSpaceAreaEntity)) {
            return;
        }
        List<BdPositionEntity> positionList = positionService.list(new LambdaQueryWrapper<BdPositionEntity>()
                .select(BdPositionEntity::getPositionCode)
                .eq(BdPositionEntity::getSpaceAreaId, bdSpaceAreaEntity.getSpaceAreaId()));
        if (!CollectionUtils.isEmpty(positionList)) {
            List<String> positionCodeList = positionList.stream().map(BdPositionEntity::getPositionCode).collect(Collectors.toList());
            List<StockEntity> stockList = stockService.list(new LambdaQueryWrapper<StockEntity>()
                    .in(StockEntity::getPositionCode, positionCodeList));
            if (!CollectionUtils.isEmpty(stockList) && stockList.stream().mapToInt(StockEntity::getStock).sum() > 0) {
                throw new BusinessServiceException("库区下存在库存，无法修改！");
            }
        }
    }

    public StockInternalBox getTransferBox(String transferBoxCode) {
        StockInternalBox internalBox = internalBoxService.getInternalBox(transferBoxCode);
        if (!StockInternalBoxTypeEnum.TRANSFER_BOX.name().equals(internalBox.getInternalBoxType())) {
            throw new BusinessServiceException("内部箱类型非调拨箱");
        }
        return internalBox;
    }

    /**
     * 根据库存名称获取唯一
     */
    public BdSpaceAreaEntity getBySpaceAreaName(String spaceAreaName) {
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("space_area_name", spaceAreaName).eq("is_deleted", Boolean.FALSE);
        return getOne(queryWrapper);
    }

    /**
     * 根据库区编码获取唯一
     */
    private BdSpaceAreaEntity getBySpaceAreaCode(String spaceAreaCode) {
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("space_area_code", spaceAreaCode).eq("is_deleted", Boolean.FALSE);
        return getOne(queryWrapper);
    }

    private void validateSpaceArea(BdSpaceAreaAddRequest request, Integer id) {
        Validator.isValid(request.getSpaceAreaName(), Objects::nonNull, "请填写库区名称");
        Validator.isValid(request.getSpaceId(), Objects::nonNull, "请选择所属仓库");
        Validator.isValid(request.getAreaId(), Objects::nonNull, "请选择所属区域");
        Validator.isValid(request.getSpaceAreaType(), Objects::nonNull, "请选择库区类型");
        Validator.isValid(request.getSpaceAreaCode(), Objects::nonNull, "请选择库区编号");

        if (!Objects.isNull(request.getOutlet()) && request.getOutlet() <= 0) {
            throw new BusinessServiceException("分拣口设置必须大于0");
        }

        //调拨箱号和分拣口必须同时填或不填
        if (!Objects.isNull(request.getOutlet()) && !StringUtils.hasText(request.getTransferBoxCode()) || Objects.isNull(request.getOutlet()) && StringUtils.hasText(request.getTransferBoxCode())) {
            throw new BusinessServiceException("调拨箱号和分拣口必须同时填或不填");
        }

        //校验分拣口与之前绑定的，对比调拨箱号是否一致
        if (!Objects.isNull(request.getOutlet())) {
            List<BdSpaceAreaEntity> spaceAreaList = this.getByOutlet(request.getOutlet());
            if (!Objects.isNull(id)) {
                spaceAreaList = spaceAreaList.stream().filter(spaceArea -> !spaceArea.getSpaceAreaId().equals(id) && StringUtils.hasText(spaceArea.getTransferBoxCode())).collect(Collectors.toList());
            }
            BdSpaceAreaEntity firstSpaceArea = spaceAreaList.isEmpty() ? null : spaceAreaList.get(0);
            if (!Objects.isNull(firstSpaceArea) && !firstSpaceArea.getTransferBoxCode().equals(request.getTransferBoxCode())) {
                throw new BusinessServiceException(String.format("该库区与【%s】，分拣口相同，调拨箱不同", firstSpaceArea.getSpaceAreaName()));
            }
        }

        //校验调拨箱号与之前绑定的，对比分拣口是否一致
        if (StringUtils.hasText(request.getTransferBoxCode())) {
            List<BdSpaceAreaEntity> spaceAreaList = this.getByTransferBoxCode(request.getTransferBoxCode());
            if (!Objects.isNull(id)) {
                spaceAreaList = spaceAreaList.stream().filter(spaceArea -> !spaceArea.getSpaceAreaId().equals(id) && !Objects.isNull(spaceArea.getOutlet()) && spaceArea.getOutlet() != 0).collect(Collectors.toList());
            }
            BdSpaceAreaEntity firstSpaceArea = spaceAreaList.isEmpty() ? null : spaceAreaList.get(0);
            if (!Objects.isNull(firstSpaceArea) && !firstSpaceArea.getOutlet().equals(request.getOutlet())) {
                throw new BusinessServiceException(String.format("该库区与【%s】，调拨箱相同，分拣口不同", firstSpaceArea.getSpaceAreaName()));
            }
        }
    }

    /**
     * 根据分拣口获取
     *
     * @param outlet
     * @return
     */
    private List<BdSpaceAreaEntity> getByOutlet(Integer outlet) {
        return this.list(new LambdaQueryWrapper<BdSpaceAreaEntity>().eq(BdSpaceAreaEntity::getOutlet, outlet).eq(BdSpaceAreaEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED));
    }

    /**
     * 根据调拨箱号获取
     *
     * @param transferBoxCode
     * @return
     */
    private List<BdSpaceAreaEntity> getByTransferBoxCode(String transferBoxCode) {
        return this.list(new LambdaQueryWrapper<BdSpaceAreaEntity>().eq(BdSpaceAreaEntity::getTransferBoxCode, transferBoxCode).eq(BdSpaceAreaEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED));
    }

    /**
     * 库区列表
     */
    public PageResponse<BdSpaceArea> spaceAreaList(BdSpaceAreaListRequest request) {
        PageResponse<BdSpaceArea> pageResponse = new PageResponse<>();
        Page<BdSpaceAreaEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<BdSpaceAreaEntity> pageResult = page(page, buildSpaceAreaQuery(request));
        pageResponse.setTotalCount(pageResult.getTotal());
        List<BdSpaceAreaEntity> spaceAreaEntityList = pageResult.getRecords();
        Map<String, String> spaceAreaTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_SPACE_AREA_TYPE.getName());
        List<BdSpaceArea> list = spaceAreaEntityList.stream().map(entity -> {
            BdSpaceArea spaceArea = new BdSpaceArea();
            BeanUtilsEx.copyProperties(entity, spaceArea);
            if (Objects.isNull(spaceArea.getDescription())) {
                spaceArea.setDescription("");
            }
            BdSpaceEntity spaceEntity = spaceService.getById(entity.getSpaceId());
            if (Objects.nonNull(spaceEntity)) {
                spaceArea.setSpaceName(spaceEntity.getSpaceName());
            }
            BdAreaEntity bdAreaEntity = bdAreaMapper.selectById(entity.getAreaId());
            if (Objects.nonNull(bdAreaEntity)) {
                spaceArea.setAreaName(bdAreaEntity.getAreaName());
            }
            spaceArea.setSpaceAreaTypeStr(spaceAreaTypeEnumMap.get(entity.getSpaceAreaType()));
            return spaceArea;
        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        return pageResponse;
    }

    /**
     * 启用、禁用 库区
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.SPACE_AREA_NAME_SELECT, allEntries = true)
    public void updateSpaceAreaDeleted(Integer id, BdSpaceAreaUpdateDeletedRequest request) {
        BdSpaceAreaEntity spaceAreaEntity = getById(id);
        if (!Objects.isNull(spaceAreaEntity)) {
            String status = request.getDeleted() ? "停用" : "启用";
            spaceAreaEntity.setIsDeleted(request.getDeleted());
            spaceAreaEntity.setUpdateBy(loginInfoService.getName());
            updateById(spaceAreaEntity);
            if (request.getDeleted()) {
                positionService.deletePositionBySpaceAreaId(spaceAreaEntity.getSpaceAreaId());
            }
            Boolean hasKey = redisClient.hasKey(RedisConfig.MODULE_NAME + ":" + CacheKeyConstant.SPACE_AREA_NAME_SELECT_BY_SPACE_ID + ":" + spaceAreaEntity.getSpaceId());
            if (hasKey) {
                redisClient.del(RedisConfig.MODULE_NAME + ":" + CacheKeyConstant.SPACE_AREA_NAME_SELECT_BY_SPACE_ID + ":" + spaceAreaEntity.getSpaceId());
            }
            String content = String.format("%s %s%s", loginInfoService.getName(), status, spaceAreaEntity.getSpaceAreaName());
            changeLogService.addChangeLog(BdChangLogTypeEnum.SPACE_AREA, content, BdSpaceAreaEntity.class.getAnnotation(Table.class).name());
        }
    }

    /**
     * 停用 区域下级库区
     */
    @Transactional
    @CacheEvict(value = CacheKeyConstant.SPACE_AREA_NAME_SELECT, allEntries = true)
    public void deleteSpaceAreaByAreaId(Integer areaId) {
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", Boolean.FALSE).eq("area_id", areaId);
        List<BdSpaceAreaEntity> spaceAreaEntityList = list(queryWrapper);
        for (BdSpaceAreaEntity spaceAreaEntity : spaceAreaEntityList) {
            spaceAreaEntity.setIsDeleted(Boolean.TRUE);
            spaceAreaEntity.setUpdateBy(loginInfoService.getName());
            updateById(spaceAreaEntity);
            String content = String.format("%s 停用 %s", loginInfoService.getName(), spaceAreaEntity.getSpaceAreaName());
            changeLogService.addChangeLog(BdChangLogTypeEnum.SPACE_AREA, content, BdSpaceAreaEntity.class.getAnnotation(Table.class).name());
            positionService.deletePositionBySpaceAreaId(spaceAreaEntity.getSpaceAreaId());
        }
    }

    /**
     * 下拉数据
     */
    @Cacheable(value = CacheKeyConstant.SPACE_AREA_NAME_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public List<SelectIntegerModel> getNameSelect() {
        List<SelectIntegerModel> result = new LinkedList<>();
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", Boolean.FALSE);
        List<BdSpaceAreaEntity> spaceAreaEntityList = list(queryWrapper);
        List<Integer> areaIds = spaceAreaEntityList.stream().map(BdSpaceAreaEntity::getAreaId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaIds)) {
            return result;
        }
        List<BdAreaEntity> areaEntityList = areaService.list(new QueryWrapper<BdAreaEntity>().lambda().in(BdAreaEntity::getAreaId, areaIds));
        for (BdSpaceAreaEntity spaceAreaEntity : spaceAreaEntityList) {
            BdAreaEntity areaEntity = areaEntityList.stream().filter(o -> o.getAreaId().equals(spaceAreaEntity.getAreaId())).findFirst().orElse(null);
            if (areaEntity == null)
                throw new BusinessServiceException("未找到区域");
            result.add(new SelectIntegerModel(spaceAreaEntity.getSpaceAreaId(), areaEntity.getAreaName() + "-" + spaceAreaEntity.getSpaceAreaName()));
        }
        return result;
    }

    /**
     * 下拉数据(根据区域id)
     */
    @Cacheable(value = CacheKeyConstant.SPACE_AREA_NAME_SELECT, key = "#areaId")
    public List<SelectIntegerModel> getNameSelectByAreaId(Integer areaId) {
        List<SelectIntegerModel> result = new LinkedList<>();
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", Boolean.FALSE).eq("area_id", areaId);
        List<BdSpaceAreaEntity> spaceAreaEntityList = list(queryWrapper);
        if (spaceAreaEntityList.isEmpty())
            return new ArrayList<>();
        List<Integer> areaIds = spaceAreaEntityList.stream().map(BdSpaceAreaEntity::getAreaId).distinct().collect(Collectors.toList());
        if (areaIds.isEmpty())
            return new ArrayList<>();
        List<BdAreaEntity> areaEntityList = areaService.list(new QueryWrapper<BdAreaEntity>().lambda().in(BdAreaEntity::getAreaId, areaIds));
        for (BdSpaceAreaEntity spaceAreaEntity : spaceAreaEntityList) {
            BdAreaEntity areaEntity = areaEntityList.stream().filter(o -> o.getAreaId().equals(spaceAreaEntity.getAreaId())).findFirst().orElse(null);
            if (areaEntity == null)
                throw new BusinessServiceException("未找到区域");
            result.add(new SelectIntegerModel(spaceAreaEntity.getSpaceAreaId(), spaceAreaEntity.getSpaceAreaName()));
        }
        return result;
    }

    /**
     * 下拉数据(根据仓库id)
     */
    @Cacheable(value = CacheKeyConstant.SPACE_AREA_NAME_SELECT_BY_SPACE_ID, key = "#spaceId")
    public List<SelectIntegerModel> getNameSelectBySpaceId(Integer spaceId) {
        List<SelectIntegerModel> result = new LinkedList<>();
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", Boolean.FALSE).eq("space_id", spaceId);
        List<BdSpaceAreaEntity> spaceAreaEntityList = list(queryWrapper);
        //建了仓库还未建库区，返回空
        if (CollectionUtils.isEmpty(spaceAreaEntityList))
            return result;
        List<Integer> areaIds = spaceAreaEntityList.stream().map(BdSpaceAreaEntity::getAreaId).distinct().collect(Collectors.toList());
        List<BdAreaEntity> areaEntityList = areaService.list(new QueryWrapper<BdAreaEntity>().lambda().in(BdAreaEntity::getAreaId, areaIds));
        for (BdSpaceAreaEntity spaceAreaEntity : spaceAreaEntityList) {
            BdAreaEntity areaEntity = areaEntityList.stream().filter(o -> o.getAreaId().equals(spaceAreaEntity.getAreaId())).findFirst().orElse(null);
            if (areaEntity == null)
                throw new BusinessServiceException("未找到区域");
            result.add(new SelectIntegerModel(spaceAreaEntity.getSpaceAreaId(), areaEntity.getAreaName() + "-" + spaceAreaEntity.getSpaceAreaName()));
        }
        return result;
    }

    private QueryWrapper<BdSpaceAreaEntity> buildSpaceAreaQuery(BdSpaceAreaListRequest request) {
        QueryWrapper<BdSpaceAreaEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasText(request.getSpaceAreaName())) {
            queryWrapper.lambda().likeRight(BdSpaceAreaEntity::getSpaceAreaName, request.getSpaceAreaName());
        }
        if (StringUtils.hasText(request.getSpaceAreaType())) {
            queryWrapper.lambda().likeRight(BdSpaceAreaEntity::getSpaceAreaType, request.getSpaceAreaType());
        }
        if (Objects.nonNull(request.getAreaId()) && request.getAreaId() > 0) {
            queryWrapper.lambda().eq(BdSpaceAreaEntity::getAreaId, request.getAreaId());
        }
        if (Objects.nonNull(request.getSpaceId()) && request.getSpaceId() > 0) {
            queryWrapper.lambda().eq(BdSpaceAreaEntity::getSpaceId, request.getSpaceId());
        }
        if (Objects.nonNull(request.getOutlet())) {
            queryWrapper.lambda().eq(BdSpaceAreaEntity::getOutlet, request.getOutlet());
        }
        queryWrapper.lambda().orderByDesc(BdSpaceAreaEntity::getSpaceAreaId);
        return queryWrapper;
    }

    public Integer getSpaceAreaCount(Integer spaceId) {
        return this.count(new QueryWrapper<BdSpaceAreaEntity>().eq("space_id", spaceId).eq("is_deleted", Boolean.FALSE));
    }

    public List<SelectIntegerModel> getSpaceAreaByAreaIdList(BdSpaceAreaSelectRequest request) {
        List<SelectIntegerModel> result = new LinkedList<>();
        LambdaQueryWrapper<BdSpaceAreaEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BdSpaceAreaEntity::getSpaceAreaId, BdSpaceAreaEntity::getSpaceAreaName);
        queryWrapper.eq(BdSpaceAreaEntity::getIsDeleted, 0);

        if (!CollectionUtils.isEmpty(request.getSpaceIdList()))
            queryWrapper.in(BdSpaceAreaEntity::getSpaceId, request.getSpaceIdList());
        if (!CollectionUtils.isEmpty(request.getAreaIdList()))
            queryWrapper.in(BdSpaceAreaEntity::getAreaId, request.getAreaIdList());

        List<BdSpaceAreaEntity> spaceAreaEntityList = list(queryWrapper);

        for (BdSpaceAreaEntity spaceAreaEntity : spaceAreaEntityList) {
            result.add(new SelectIntegerModel(spaceAreaEntity.getSpaceAreaId(), spaceAreaEntity.getSpaceAreaName()));
        }
        return result;
    }
}
