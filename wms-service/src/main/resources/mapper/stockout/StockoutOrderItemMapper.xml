<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderItemMapper">
    <select id="pageSearchOutorderItem" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderItemList">
        SELECT
        outOrderItem.stockout_order_item_id as orderItemId,
        specInfo.image_url as imageUrl,
        specInfo.thumbnail_image_url as thumbnailImageUrl,
        specInfo.preview_image_url as previewImageUrl,
        specInfo.color as color,
        specInfo.size as size,
        outOrderItem.sku as sku,
        outOrderItem.barcode as barcode,
        outOrderItem.space_area_name as spaceAreaName,
        specInfo.actual_weight as actualWeight,
        outOrderItem.qty as qty,
        outOrderItem.scan_qty as scanQty,
        outOrderItem.shipment_qty as shipmentQty,
        outOrderItem.is_transparency as isTransparency,
        outOrderItem.sku_memo as skuMemo,
        outOrderItem.product_id,
        outOrderItem.is_need_process,
        outOrderItem.change_type,
        outOrderItem.cancel_qty
        FROM stockout_order_item outOrderItem
        LEFT JOIN product_spec_info specInfo on specInfo.spec_id = outOrderItem.spec_id
        <where>
            <if test="query!=null and query.stockoutOrderId != null and query.stockoutOrderId !=''">
                and outOrderItem.stockout_order_id = #{query.stockoutOrderId}
            </if>
            <if test="query!=null and query.sku != null and query.sku !=''">
                and outOrderItem.sku like concat(#{query.sku}, '%')
            </if>
            <if test="query.barcode != null and query.barcode !=''">
                and outOrderItem.barcode = #{query.barcode}
            </if>
        </where>
        order by outOrderItem.sku desc
    </select>

    <select id="searchOutorderItemSku" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderItemSkuInfo">
        SELECT
        p.sku_type as skuType,
        p.sku_num as skuNum,
        p.is_urgent as isUrgent
        FROM
        (
        SELECT
        count( DISTINCT item.sku ) AS sku_type,
        sum( item.qty ) AS sku_num,
        o.is_urgent AS is_urgent
        FROM
        stockout_order_item item
        LEFT JOIN stockout_order o ON o.stockout_order_id = item.stockout_order_id
        <where>
            <if test="stockoutOrderId!=null and stockoutOrderId !=''">
                and item.stockout_order_id = #{stockoutOrderId}
            </if>
        </where>
        ) p
        <where>
            <if test="query!=null and query !=''">
                ${query}
            </if>
        </where>
    </select>

    <select id="searchOrderNo" resultType="java.lang.String">
        SELECT item.order_no FROM
        stockout_order_item item
        LEFT JOIN stockout_order o ON o.stockout_order_id = item.stockout_order_id
        <where>
            <if test="stockoutOrderNo!=null and stockoutOrderNo !=''">
                and o.stockout_order_no = #{stockoutOrderNo}
            </if>
        </where>
        GROUP BY item.order_no
    </select>

    <select id="getPickingDescription" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderSkuDescription">
        select max(oi.sku_memo)                           description,
               oi.sku,
               max(IFNULL(oi.is_first_order_by_store, 0)) isFirstOrderByStore,
               max(IFNULL(oi.is_lead_generation, 0))     isLeadGeneration
        from stockout_order o
                 left JOIN
             stockout_order_item oi
             on o.stockout_order_id = oi.stockout_order_id
        where o.stockout_order_id in (select stockout_order_id
                                      from stockout_batch_order
                                      where batch_id in (SELECT batch_id
                                                         FROM stockout_batch
                                                         where batch_id = #{batchId}
                                                            or merge_batch_id = #{batchId}))
        GROUP BY oi.sku

    </select>
    <select id="getSpaceAreaQtyMax" resultType="java.lang.Integer">
        SELECT sum(qty) AS qty
        FROM stockout_order_item stockout_order_item
        WHERE stockout_order_item.stockout_order_id = #{stockoutOrderId}
        GROUP BY stockout_order_item.space_area_name
    </select>
    <select id="calQtyAndWeight" resultType="com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskPrintOrderInfo">
        SELECT sum(oi.qty) AS                qty,
               SUM(oi.qty * p.weight)        weight,
               SUM(oi.qty * p.actual_weight) actualWeight
        FROM stockout_order_item oi
                 LEFT join product_spec_info p ON oi.spec_id = p.spec_id
                 LEFT join stockout_order so ON so.stockout_order_id = oi.stockout_order_id
        WHERE so.stockout_order_no = #{stockoutOrderNo}
        group by oi.stockout_order_id
    </select>
    <select id="getItemWeight" resultType="java.math.BigDecimal">
        SELECT SUM(IFNULL(product_spec_info.actual_weight, 0) * stockout_order_item.qty)
        FROM stockout_order_item stockout_order_item
                 LEFT JOIN stockout_order stockout_order
                           ON stockout_order.stockout_order_id = stockout_order_item.stockout_order_id
                 LEFT JOIN product_spec_info product_spec_info
                           ON product_spec_info.spec_id = stockout_order_item.spec_id
        WHERE stockout_order.stockout_order_no = #{stockoutOrderNo}
        GROUP BY stockout_order.stockout_order_no
    </select>
    <select id="getOrderInfoByBatch" resultType="com.nsy.api.wms.domain.stockout.StockoutOrderSkuDescription">
        select oi.sku,
               max(IFNULL(oi.is_first_order_by_store, 0)) isFirstOrderByStore,
               max(IFNULL(oi.is_transparency, 0))         isTransparency,
               max(IFNULL(oi.is_lead_generation, 0)) isLeadGeneration,
               GROUP_CONCAT(DISTINCT oi.vas_type) as vasType
        from stockout_order o
        left JOIN stockout_order_item oi on o.stockout_order_id = oi.stockout_order_id
        left join stockout_value_add_service_task_item vas on vas.order_no = oi.order_no and oi.sku = vas.sku
        where o.stockout_order_id in (select stockout_order_id
                                      from stockout_batch_order
                                      where batch_id in (SELECT batch_id
                                                         FROM stockout_batch
                                                         where batch_id = #{batchId}
                                                            or merge_batch_id = #{batchId}))
        GROUP BY oi.sku
    </select>
    <select id="findStockoutOrderItemInfo" resultType="com.nsy.wms.business.domain.bo.stockout.StockoutOrderItemInfoBo">
        SELECT
        oi.stockout_order_item_id AS stockoutItemId,
        oi.change_type AS changeType,
        oi.is_need_process AS isNeedProcess,
        od.stockout_order_no AS stockoutOrderNo,
        od.create_date AS stockoutOrderCreateDate,
        oi.product_id AS productId,
        oi.spec_id AS specId,
        oi.sku AS sku,
        oi.barcode AS barcode,
        oi.qty AS pendingQty,
        oi.location AS location,
        ifnull(ifnull(p.area_id,cp.area_id),0) as areaId,
        oi.position_code
        FROM
        stockout_order_item oi
        INNER JOIN stockout_order od ON oi.stockout_order_id = od.stockout_order_id
        LEFT JOIN bd_position p on p.position_code = oi.position_code
        and od.space_id = p.space_id and p.is_deleted = 0
        LEFT JOIN bd_area_common_position cp on cp.position_code = oi.position_code
        and od.space_id = cp.space_id
        <where>
            <if test="stockoutOrderId!=null and stockoutOrderId !=''">
                and oi.stockout_order_id = #{stockoutOrderId}
            </if>
        </where>
    </select>
    <select id="countQty" resultType="java.lang.Integer">
        SELECT sum(qty)
        FROM stockout_order_item stockout_order_item
        WHERE stockout_order_item.stockout_order_id = #{stockoutOrderId}
    </select>

    <select id="getStockoutIdByOrderNo" resultType="java.lang.Integer">
        select distinct item.stockout_order_id
        from stockout_order_item item
        where item.order_no = #{orderNo}
    </select>
    <select id="getQcinboundsList" resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity">
        select oi.location,
               ifnull(si.product_id, oi.product_id) as productId,
               ifnull(si.spec_id, oi.spec_id)       as specId,
               ifnull(si.sku, oi.sku)               as sku,
               ifnull(si.barcode, oi.barcode)       as barcode,
               oi.qty
        from stockout_order_item oi
                 left join stockout_order_item_process_info pi
                           on oi.stockout_order_item_id = pi.stockout_order_item_id
                 left join product_spec_info si
                           on si.sku = pi.base_sku
        where oi.stockout_order_id = #{stockoutOrderId}

    </select>
    <select id="getOrderItemByStockoutOrderIdAndBaseSku"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity">
        select oi.*
        from stockout_order_item oi
                 inner join stockout_order_item_process_info pi
                            on oi.stockout_order_item_id = pi.stockout_order_item_id
        where oi.stockout_order_id = #{stockoutOrderId}
          and pi.base_sku = #{sku}
        limit 1
    </select>
    <select id="findQcReplenishmentItem" resultType="com.nsy.wms.business.domain.bo.stock.StockPrematchItemBo">
        select oi.stockout_order_item_id as stockoutOrderItemId,
               oi.sku                    as sku,
               oi.spec_id                as specId,
               oi.product_id             as productId,
               oi.qty                    as qty,
               oi.is_need_process        as isNeedProcess,
               p.area_id                 as areaId
        from stockout_order_item oi
                 inner join bd_position p
                            on p.position_code = oi.position_code
        where oi.stockout_order_id = #{stockoutOrderId}
          and oi.sku = #{sku}
          and oi.is_need_process = 0
        union all
        select oi.stockout_order_item_id as stockoutOrderItemId,
               pi.base_sku               as sku,
               psi.spec_id               as specId,
               psi.product_id            as productId,
               oi.qty                    as qty,
               oi.is_need_process        as isNeedProcess,
               p.area_id                 as areaId
        from stockout_order_item oi
                 inner join stockout_order_item_process_info pi
                            on oi.stockout_order_item_id = pi.stockout_order_item_id
                 inner join bd_position p
                            on p.position_code = oi.position_code
                 inner join product_spec_info psi
                            on psi.sku = pi.base_sku
        where oi.stockout_order_id = #{stockoutOrderId}
          and pi.base_sku = #{sku}
          and oi.is_need_process = 1
    </select>
    <select id="listAreaInfoByItemId" resultType="com.nsy.api.wms.response.stockout.StockoutOrderAreaResponse">
        select oi.stockout_order_id,
        oi.stockout_order_item_id,
        o.store_id,
        o.store_name,
        m.area_id,
        m.area_name,
        m.erp_space_id,
        m.erp_space_name
        from stockout_order_item oi
        inner join stockout_order o
        on o.stockout_order_id = oi.stockout_order_id
        inner join bd_position bp
        on oi.position_code = bp.position_code
        inner join bd_erp_space_mapping m
        on m.area_id = bp.area_id
        where oi.stockout_order_item_id in
        <foreach collection="stockoutOrdeItemIdList" separator="," index="index" item="stockoutOrdeItemId" open="("
                 close=")">
            #{stockoutOrdeItemId}
        </foreach>
    </select>
    <select id="getOrderNumByBatch" resultType="java.lang.Integer">
        select count(distinct(oi.order_no))
        from stockout_order o
                 left JOIN
             stockout_order_item oi
             on o.stockout_order_id = oi.stockout_order_id
        where o.stockout_order_id in (select stockout_order_id
                                      from stockout_batch_order
                                      where batch_id in (SELECT batch_id
                                                         FROM stockout_batch
                                                         where batch_id = #{batchId}
                                                            or merge_batch_id = #{batchId}))
    </select>

    <select id="getStoreBoListByOrderNoList" resultType="com.nsy.wms.business.domain.bo.base.StoreBo">
        SELECT
            so.store_id,
            so.store_name
        FROM
            stockout_order_item soi
                LEFT JOIN stockout_order so ON soi.stockout_order_id = so.stockout_order_id
        WHERE
            soi.order_no IN
            <foreach collection="orderNoList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
    </select>

    <select id="getStockoutOrderByOrderNo" resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        select distinct t.* from stockout_order t
        inner join stockout_order_item ti on ti.stockout_order_id = t.stockout_order_id
        where  ti.order_no = #{orderNo}  and t.status not in ('CANCELLING','CANCELLED')
        limit 1
    </select>
    <select id="getOrderNoByReplenishOrder" resultType="java.lang.String">
        select distinct ti.order_no from stockout_order t
          inner join stockout_order_item ti on ti.stockout_order_id = t.stockout_order_id
        where  t.replenish_order = #{replenishOrder}

    </select>
    <select id="sumQtyByStockoutOrderId" resultType="java.lang.Integer">
        select ifnull(sum(qty), 0)
        from stockout_order_item
        where stockout_order_id = #{stockoutOrderId}
    </select>
    <select id="getOrderNoByStockoutOrderId" resultType="java.lang.String">
        select distinct ti.order_no from stockout_order_item ti
        where  ti.stockout_order_id = #{stockoutOrderId}
    </select>
</mapper>
