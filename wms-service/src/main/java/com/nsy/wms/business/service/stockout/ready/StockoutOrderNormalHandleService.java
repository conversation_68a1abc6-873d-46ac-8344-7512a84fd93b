package com.nsy.wms.business.service.stockout.ready;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderReadyStatusHandleEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.wms.business.service.stockout.StockoutOrderGetLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2024/5/4 18:16
 */
@Service
public class StockoutOrderNormalHandleService implements IStockoutOrderReadyStatusHandleService {

    @Autowired
    StockoutOrderGetLabelService orderGetLabelService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderTransparencyHandleService transparencyHandleService;

    @Override
    public boolean isSupport(StockoutOrderReadyStatusHandleEnum handleEnum) {
        return StockoutOrderReadyStatusHandleEnum.NORMAL == handleEnum;
    }


    /**
     * 步骤一：获取面单
     * 成功 状态 ==>  Tcode申请中/待生成波次
     * 失败 状态 ==>  获取异常列表
     *
     * @param stockoutOrder
     */
    @Override
    public void handleOrder(StockoutOrderEntity stockoutOrder) {
        // 获取面单
        orderGetLabelService.startGetLabel(stockoutOrder);
        if (StrUtil.isNotBlank(stockoutOrder.getLogisticsNo()) || StrUtil.equalsAnyIgnoreCase(stockoutOrder.getLogisticsCompany(), LogisticsCompanyConstant.TIANZONG_FEDEX)) {
            // 成功获取
            if (!StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(stockoutOrder.getStatus()) && !StockoutOrderStatusEnum.READY.name().equals(stockoutOrder.getStatus())) {
                return;
            }
            List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
            if (stockoutOrderItemService.validTransparency(stockoutOrder.getStockoutOrderNo(), stockoutOrderItemEntities)) {
                //是透明计划开始进行透明计划预占
                transparencyHandleService.handleOrder(stockoutOrder);
            } else {
                stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrder, StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
            }
        } else {
            // 更新失败状态
            orderGetLabelService.buildErrorStatus(stockoutOrder);
        }
    }

}
