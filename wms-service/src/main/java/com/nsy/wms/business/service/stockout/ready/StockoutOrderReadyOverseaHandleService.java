package com.nsy.wms.business.service.stockout.ready;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.constants.StockoutSpaceTrackConstant;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderReadyStatusHandleEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.stockout.StockoutOrderGetLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.handle.OverseaStockoutService;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/5 9:55
 */
@Service
public class StockoutOrderReadyOverseaHandleService implements IStockoutOrderReadyStatusHandleService {

    @Autowired
    private StockoutOrderGetLabelService orderGetLabelService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    OverseaStockoutService overseaStockoutService;
    @Autowired
    BdSpaceService spaceService;


    @Override
    public boolean isSupport(StockoutOrderReadyStatusHandleEnum handleEnum) {
        return StockoutOrderReadyStatusHandleEnum.OVERSEA == handleEnum;
    }


    @Override
    public void handleOrder(StockoutOrderEntity stockoutOrder) {
        try {
            StockoutOrderEntity byId = stockoutOrderService.getById(stockoutOrder.getStockoutOrderId());
            if (StrUtil.isBlank(byId.getLogisticsCompany()) || !byId.getStatus().equals(StockoutOrderStatusEnum.READY.name())
                    && !byId.getStatus().equals(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name())) return;
            if (StrUtil.isNotBlank(stockoutOrder.getLogisticsNo())) {
                Integer spaceId = stockoutOrder.getSpaceId();
                BdSpaceEntity space = spaceService.getSpaceByIdValid(spaceId);
                if (!StrUtil.equals(space.getSpaceName(), SpaceAreaMapConstant.WmsSpace.ZUOHAI_SPACE)
                        && !StrUtil.containsIgnoreCase(space.getDescription(), StockoutSpaceTrackConstant.SPACE_DESC_ZHIYUN)
                        && !StrUtil.containsIgnoreCase(space.getDescription(), StockoutSpaceTrackConstant.SPACE_DESC_GUCANG)) {
                    String logisticsNo = stockoutOrder.getLogisticsNo();
                    LambdaUpdateWrapper<StockoutOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(StockoutOrderEntity::getStockoutOrderId, byId.getStockoutOrderId()).set(StockoutOrderEntity::getLogisticsNo, "");
                    stockoutOrderService.update(updateWrapper);
                    stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CLEAR_LOGISTICS_NO, "删除订单携带的物流单号：" + logisticsNo);
                }
            }
            // 先获取面单
            boolean successful = orderGetLabelService.startGetLabel(byId);
            if (successful) {
                overseaStockoutService.addLogisticsNoLog(stockoutOrder, new BaseGetLogisticsNoResponse());
            } else {
                orderGetLabelService.buildErrorStatus(stockoutOrder);
            }
            // 更新出库单失败
        } catch (Exception e) {
            orderGetLabelService.doException(stockoutOrder, e);
            // 更新出库单失败
            orderGetLabelService.buildErrorStatus(stockoutOrder);
        }
    }


}
