package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPrematchInfoTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.wms.business.manage.erp.ErpTransferApiService;
import com.nsy.wms.business.manage.erp.request.ErpShipDiffStockPrematchInfo;
import com.nsy.wms.business.manage.erp.request.ErpShipDiffStockRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPrematchInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderPrematchInfoMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StockoutOrderPrematchInfoService extends ServiceImpl<StockoutOrderPrematchInfoMapper, StockoutOrderPrematchInfoEntity> {

    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderLackItemService stockoutOrderLackItemService;
    @Autowired
    ErpTransferApiService erpTransferApiService;


    /**
     * 根据出库单号，同步erp发货差异库存
     * case1、不缺货+不重新预占+不缺货预占 = 没有差异
     * case2、不重新预占
     * case3、重新预占(wms不会扣除erp预占库存)
     *
     * @param stockoutOrderEntity 出库单
     */
    public void syncShipDiffStockBy(StockoutOrderEntity stockoutOrderEntity) {
        ErpShipDiffStockRequest request = new ErpShipDiffStockRequest();
        request.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        request.setErpPickId(stockoutOrderEntity.getErpPickId());
        request.setUserName(loginInfoService.getName());
        request.setLocation(TenantContext.getTenant());
        // 出库单预占信息
        List<StockoutOrderPrematchInfoEntity> prematchInfoEntityList = this.list(new QueryWrapper<StockoutOrderPrematchInfoEntity>().lambda()
                .eq(StockoutOrderPrematchInfoEntity::getStockoutOrderNo, stockoutOrderEntity.getStockoutOrderNo()));
        if (prematchInfoEntityList.isEmpty())
            return;
        // 出库单明细
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(prematchInfoEntityList.get(0).getStockoutOrderId());
        // 是否缺货，发货数！=订单数
        boolean shipLack = orderItemEntityList.stream().anyMatch(entity -> !entity.getShipmentQty().equals(entity.getQty()));
        // 是否重新预占
        boolean rePrematch = prematchInfoEntityList.stream().anyMatch(entity -> entity.getType().equals(StockoutOrderPrematchInfoTypeEnum.WMS.name()));
        // 是否缺货预占
        boolean lackPrematch = prematchInfoEntityList.stream().anyMatch(entity -> entity.getType().equals(StockoutOrderPrematchInfoTypeEnum.WMS_LACK.name()));

        // case1、不缺货+不重新预占+不缺货预占 = 没有差异
        if (!shipLack && !rePrematch && !lackPrematch) {
            return;
        }

        // erp 预占库位信息
        List<StockoutOrderPrematchInfoEntity> erpPrematchInfoEntityList = prematchInfoEntityList.stream().filter(o -> o.getType().equals(StockoutOrderPrematchInfoTypeEnum.ERP.name())).collect(Collectors.toList());
        request.setErpPrematchShipInfos(buildErpPrematchShipInfos(erpPrematchInfoEntityList, orderItemEntityList, stockoutOrderEntity));

        List<ErpShipDiffStockPrematchInfo> wmsPrematchShipInfos = new LinkedList<>();
        // wms 缺货 预占库位信息
        List<StockoutOrderPrematchInfoEntity> wmsLackPrematchInfoEntityList = prematchInfoEntityList.stream().filter(o -> o.getType().equals(StockoutOrderPrematchInfoTypeEnum.WMS_LACK.name())).collect(Collectors.toList());
        if (!wmsLackPrematchInfoEntityList.isEmpty()) {
            wmsPrematchShipInfos.addAll(buildWmsPrematchShipInfos(wmsLackPrematchInfoEntityList, StockoutOrderPrematchInfoTypeEnum.WMS_LACK.toString()));
        }

        // case2、不重新预占
        if (!rePrematch) {
            wmsPrematchShipInfos.addAll(buildWmsPrematchShipInfos(erpPrematchInfoEntityList, StockoutOrderPrematchInfoTypeEnum.ERP.toString()));
        } else {
            // case3、重新预占
            // wms 重新 预占库位信息
            List<StockoutOrderPrematchInfoEntity> wmsRePrematchInfoEntityList = prematchInfoEntityList.stream().filter(o -> o.getType().equals(StockoutOrderPrematchInfoTypeEnum.WMS.name())).collect(Collectors.toList());
            if (!wmsRePrematchInfoEntityList.isEmpty()) {
                wmsPrematchShipInfos.addAll(buildWmsPrematchShipInfos(wmsRePrematchInfoEntityList, StockoutOrderPrematchInfoTypeEnum.WMS.toString()));
            }
        }
        request.setWmsPrematchShipInfos(wmsPrematchShipInfos);

        // 同步erp发货差异库存
        erpTransferApiService.syncShipDiffStock(request);
    }

    public void syncShipDiffStockBy(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        syncShipDiffStockBy(stockoutOrderEntity);
    }

    /**
     * 构造 erp 预占库位+  erp扣减的库存数 = 实际发货数
     *
     * @param erpPrematchInfoEntityList erp 预占库位信息
     * @param orderItemEntityList       出库单明细
     * @param stockoutOrderEntity       出库单
     * @return ErpShipDiffStockPrematchInfo list
     */
    private List<ErpShipDiffStockPrematchInfo> buildErpPrematchShipInfos(List<StockoutOrderPrematchInfoEntity> erpPrematchInfoEntityList, List<StockoutOrderItemEntity> orderItemEntityList, StockoutOrderEntity stockoutOrderEntity) {
        List<ErpShipDiffStockPrematchInfo> result = new LinkedList<>();
        for (StockoutOrderPrematchInfoEntity erpPrematchInfoEntity : erpPrematchInfoEntityList) {
            StockoutOrderItemEntity orderItemEntity = orderItemEntityList.stream().filter(o -> o.getOrderItemId().equals(erpPrematchInfoEntity.getOrderItemId())).findFirst().orElse(null);
            if (orderItemEntity == null) {
                continue;
            }
            Integer addQty = orderItemEntity.getShipmentQty() > erpPrematchInfoEntity.getQty() || stockoutOrderEntity.getStockoutType().equals(StockoutOrderTypeEnum.INNER_BORROW_DELIVERY.name())
                    ? erpPrematchInfoEntity.getQty()
                    : orderItemEntity.getShipmentQty();
            ErpShipDiffStockPrematchInfo info = result.stream().filter(o -> o.getOrderItemId().equals(erpPrematchInfoEntity.getOrderItemId()) && o.getPositionCode().equals(erpPrematchInfoEntity.getPositionCode()))
                    .findFirst().orElse(null);
            if (info == null) {
                info = new ErpShipDiffStockPrematchInfo();
                info.setOrderItemId(erpPrematchInfoEntity.getOrderItemId());
                info.setPositionCode(erpPrematchInfoEntity.getPositionCode());
                info.setQty(addQty);
                info.setUnProcessQty(info.getQty());
                info.setPrematchInfoType(StockoutOrderPrematchInfoTypeEnum.ERP.toString());
                info.setSku(erpPrematchInfoEntity.getSku());
                result.add(info);
            } else {
                info.setQty(info.getQty() + addQty);
                info.setUnProcessQty(info.getQty());
            }

        }
        return result;
    }

    /**
     * 构造 wms 预占库位+  wms扣减的库存数
     *
     * @param wmsPrematchInfoEntityList wms 预占库位信息
     * @param prematchType              预占类型
     * @return ErpShipDiffStockPrematchInfo list
     */
    private List<ErpShipDiffStockPrematchInfo> buildWmsPrematchShipInfos(List<StockoutOrderPrematchInfoEntity> wmsPrematchInfoEntityList, String prematchType) {
        List<ErpShipDiffStockPrematchInfo> result = new LinkedList<>();
        for (StockoutOrderPrematchInfoEntity wmsPrematchInfoEntity : wmsPrematchInfoEntityList) {
            ErpShipDiffStockPrematchInfo info = result.stream().filter(o -> o.getOrderItemId().equals(wmsPrematchInfoEntity.getOrderItemId()) && o.getPositionCode().equals(wmsPrematchInfoEntity.getPositionCode()))
                    .findFirst().orElse(null);
            if (info == null) {
                info = new ErpShipDiffStockPrematchInfo();
                info.setOrderItemId(wmsPrematchInfoEntity.getOrderItemId());
                info.setPositionCode(wmsPrematchInfoEntity.getPositionCode());
                info.setQty(wmsPrematchInfoEntity.getQty());
                info.setUnProcessQty(info.getQty());
                info.setPrematchInfoType(prematchType);
                info.setSku(wmsPrematchInfoEntity.getSku());
                result.add(info);
            } else {
                info.setQty(info.getQty() + wmsPrematchInfoEntity.getQty());
                info.setUnProcessQty(info.getQty());
            }
        }
        return result;
    }

    /**
     * 获取出库单下面的预配信息
     *
     * @param stockoutOrderId
     * @return
     */
    public List<StockoutOrderPrematchInfoEntity> getByStockoutOrderId(Integer stockoutOrderId) {
        LambdaQueryWrapper<StockoutOrderPrematchInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderPrematchInfoEntity::getStockoutOrderId, stockoutOrderId);
        return baseMapper.selectList(queryWrapper);
    }
}
