package com.nsy.wms.business.service.logistics.documents;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CountryCodeConstant;
import com.nsy.api.wms.constants.DhlCategoryReplaceConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.logistics.documents.AddressInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsBaseInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintItemInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsReloadShipmentInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsShipmentInfo;
import com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsShipmentSummary;
import com.nsy.api.wms.domain.logistics.documents.request.LogisticsDocumentsBaseRequestInfo;
import com.nsy.api.wms.domain.logistics.documents.request.UpsRequestInfo;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItem;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.InvoiceTemplateEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutFaireShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.UpsUploadStatusEnum;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsPrintInfoRequest;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsSaveShipmentRequest;
import com.nsy.api.wms.request.logistics.doucments.LogisticsDocumentsShipmentInfoListRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemRequest;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.manage.tms.response.LogisticsAccountInfo;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCountryProvinceMapping;
import com.nsy.wms.business.service.async.AsyncProcessFlowService;
import com.nsy.wms.business.service.bd.BdHsCodeConfigService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.logistics.DhlService;
import com.nsy.wms.business.service.logistics.FedexService;
import com.nsy.wms.business.service.logistics.UpsHZService;
import com.nsy.wms.business.service.logistics.UpsService;
import com.nsy.wms.business.service.product.ProductCustomsDeclareService;
import com.nsy.wms.business.service.product.ProductWmsCategoryService;
import com.nsy.wms.business.service.stockout.StockoutFaireShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutFaireShipmentService;
import com.nsy.wms.business.service.stockout.StockoutInvoiceInfoService;
import com.nsy.wms.business.service.stockout.StockoutLogQueueService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderScanCheckService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderTrackingService;
import com.nsy.wms.business.service.stockout.StockoutReceiverInfoService;
import com.nsy.wms.business.service.stockout.StockoutShipmentConfirmService;
import com.nsy.wms.business.service.stockout.StockoutShipmentErpPickingBoxService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentLogisticsInfoService;
import com.nsy.wms.business.service.stockout.StockoutShipmentPackService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.building.StockoutShipmentBuilding;
import com.nsy.wms.business.service.stockout.ship.StockoutShipService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdHsCodeConfigEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.product.ProductCustomsDeclareEntity;
import com.nsy.wms.repository.entity.product.ProductWmsCategoryEntity;
import com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutInvoiceInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutReceiverInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentLogisticsInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class LogisticsDocumentsService {
    @Autowired
    TmsCacheService tmsCacheService;
    @Resource
    BdSystemParameterService parameterService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    StockoutShipmentConfirmService shipmentConfirmService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutInvoiceInfoService invoiceInfoService;
    @Autowired
    FedexService fedexService;
    @Autowired
    DhlService dhlService;
    @Autowired
    UpsService upsService;
    @Autowired
    UpsHZService upsHZService;
    @Autowired
    StockoutShipmentLogisticsInfoService shipmentLogisticsInfoService;
    @Autowired
    LogisticsDocumentsInvoiceService logisticsDocumentsInvoiceService;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    StockoutOrderLabelService labelService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    ProductWmsCategoryService productWmsCategoryService;
    @Autowired
    ProductCustomsDeclareService productCustomsDeclareService;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    BdHsCodeConfigService hsCodeConfigService;
    @Autowired
    StockoutShipService stockoutShipService;
    @Autowired
    StockoutOrderTrackingService stockoutOrderTrackingService;
    @Autowired
    StockoutShipmentPackService packService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    AsyncProcessFlowService asyncProcessFlowService;
    @Autowired
    LogisticsDocumentsPrintService logisticsDocumentsPrintService;
    @Autowired
    StockoutFaireShipmentItemService stockoutFaireShipmentItemService;
    @Autowired
    StockoutFaireShipmentService faireShipmentService;


    /**
     * 获取单证页面基本信息：收件人、账号信息、州二字码
     *
     * @param documentsPage 哪个物流单证页面标识，值为：FedEx，UPS，DHL,
     */
    public LogisticsDocumentsBaseInfo getLogisticsDocumentsBaseInfo(String documentsPage, String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        shipmentConfirmService.validValueAddDeliveryOrder(Collections.singletonList(stockoutOrderNo));
        StockoutShipmentBuilding.validBaseInfo(documentsPage, stockoutOrderNo, stockoutOrderEntity);
        // 校验是否装pack
        if (stockoutOrderEntity.getHasPack())
            packService.validDeliveryBox(stockoutOrderEntity);
        if (!StockoutOrderStatusEnum.COMPLETE.name().equals(stockoutOrderEntity.getStatus()) && !StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())
                && !StockoutOrderStatusEnum.READY_DELIVERY.name().equals(stockoutOrderEntity.getStatus()))
            throw new BusinessServiceException("该出库单不是待发货/已发货状态，无法做单证");
        LogisticsDocumentsBaseInfo baseInfo = new LogisticsDocumentsBaseInfo();
        baseInfo.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
        baseInfo.setStateMap(buildStateMap(documentsPage));
        StockoutOrderInfo stockoutOrderInfo = new StockoutOrderInfo();
        BeanUtilsEx.copyProperties(stockoutOrderEntity, stockoutOrderInfo);
        baseInfo.setStockoutOrderInfo(stockoutOrderInfo);
        baseInfo.setReceiver(buildReceiver(stockoutOrderEntity, baseInfo));
        baseInfo.setSenderMap(buildSenderMap(stockoutOrderEntity));
        baseInfo.setCountryList(tmsCacheService.getLogisticsCountrySelect(documentsPage));
        baseInfo.setLogisticsAccountList(tmsCacheService.getLogisticsAccountSelect(stockoutOrderEntity.getLogisticsCompany()));
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        baseInfo.setOrderNos(stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getOrderNo).distinct().collect(Collectors.joining(",")));
        // 回填箱规和重量
        List<StockoutShipmentEntity> shipments = shipmentItemService.getShipmentByStockoutOrderNo(stockoutOrderNo);
        baseInfo.setBoxWeight(BigDecimal.ZERO);
        shipments.forEach(shipment -> {
            if (StringUtils.hasText(shipment.getBoxSize()) && Objects.equals(shipment.getBoxIndex(), 1))
                baseInfo.setBoxSize(shipment.getBoxSize());
            if (!StringUtils.hasText(baseInfo.getBoxSize()))
                baseInfo.setBoxSize(shipment.getBoxSize());
            if (shipment.getWeight() != null)
                baseInfo.setBoxWeight(baseInfo.getBoxWeight().add(shipment.getWeight()));
        });
        return baseInfo;
    }

    /**
     * 获取单证页面装箱清单信息,报关分类分组，用于无纸化发票和时颖发票
     */
    public LogisticsDocumentsPrintInfo getPrintInfoByCategory(LogisticsDocumentsPrintInfoRequest request) {
        LogisticsDocumentsPrintInfo printInfo = getBaseShipmentInfo(request);
        if (CollectionUtils.isEmpty(printInfo.getShipmentIdList()))
            return printInfo;
        List<LogisticsDocumentsPrintItemInfo> shipmentItemInfoList = stockoutShipmentMapper.logisticsDocumentsCategoryItem(printInfo.getShipmentIdList());
        // 构建申报要素
        buildElementValue(shipmentItemInfoList);
        // 构建重量、中英文
        buildCategoryName(shipmentItemInfoList, request.getLogisticsCompany());
        // 对价格、重量、描述进行重新设置
        printInfo.setShipmentItemInfoList(buildLogisticsDocumentsPrintItemInfo(request, shipmentItemInfoList));
        List<String> collect = shipmentItemInfoList.stream().map(LogisticsDocumentsPrintItemInfo::getCustomsDeclareEn).distinct().collect(Collectors.toList());
        printInfo.setMainProductName(String.join(",", collect));
        if (StrUtil.containsIgnoreCase(request.getLogisticsCompany(), "UPS")) {
            if (collect.size() > 1) {
                printInfo.setMainProductName(collect.get(0) + "," + collect.get(1));
            } else {
                printInfo.setMainProductName(collect.get(0));
            }
        }
        return printInfo;
    }

    private void buildCategoryName(List<LogisticsDocumentsPrintItemInfo> shipmentItemInfoList, String logisticsCompany) {
        for (LogisticsDocumentsPrintItemInfo item : shipmentItemInfoList) {
            if (item.getActualWeight() != null && item.getActualWeight().compareTo(BigDecimal.ZERO) != 0)
                item.setWeight(item.getActualWeight());
            if (StringUtils.hasText(item.getCategoryId())) {
                ProductWmsCategoryEntity productWmsCategoryEntity = productWmsCategoryService.getById(item.getCategoryId());
                if (productWmsCategoryEntity != null && StringUtils.hasText(productWmsCategoryEntity.getName()))
                    item.setCustomsDeclareCn(productWmsCategoryEntity.getName());
                if (productWmsCategoryEntity != null && StringUtils.hasText(productWmsCategoryEntity.getEnglishName()))
                    item.setCustomsDeclareEn(productWmsCategoryEntity.getEnglishName());
            }
            if ("DHL".equalsIgnoreCase(logisticsCompany) && StringUtils.hasText(item.getCustomsDeclareEn()) && DhlCategoryReplaceConstant.getReplaceMap().containsKey(item.getCustomsDeclareEn().toUpperCase(Locale.ROOT))) {
                item.setCustomsDeclareCn(DhlCategoryReplaceConstant.getReplaceMap().get(item.getCustomsDeclareEn().toUpperCase(Locale.ROOT)).get(0));
                item.setCustomsDeclareEn(DhlCategoryReplaceConstant.getReplaceMap().get(item.getCustomsDeclareEn().toUpperCase(Locale.ROOT)).get(1));
            }
            if ("DHL".equalsIgnoreCase(logisticsCompany) && StrUtil.containsAny(item.getCustomsDeclareCn(), "包", "杯", "袋")) {
                if (StringUtils.hasText(item.getFabricTypeEn()))
                    item.setCustomsDeclareEn(item.getFabricTypeEn() + " " + item.getCustomsDeclareEn());
                if (StringUtils.hasText(item.getFabricTypeCn()))
                    item.setCustomsDeclareCn(item.getFabricTypeCn() + " " + item.getCustomsDeclareCn());
            }
        }
    }

    private void buildElementValue(List<LogisticsDocumentsPrintItemInfo> shipmentItemInfoList) {
        for (LogisticsDocumentsPrintItemInfo item : shipmentItemInfoList) {
            if (!StringUtils.hasText(item.getElementValue()))
                continue;
            // 先取spu的申报，如果申报要素不完整，取分类的
            List<BdHsCodeConfigEntity> bdHsCodeConfigEntities = hsCodeConfigService.getBaseMapper().selectElementByHsCode(item.getHsCode());
            ProductCustomsDeclareEntity spuDeclare = productCustomsDeclareService.findBySpu(item.getSpu());
            if (spuDeclare != null && StringUtils.hasText(spuDeclare.getElementValue()) && StrUtil.count(spuDeclare.getElementValue(), "|") == bdHsCodeConfigEntities.size() - 1
                    && Objects.equals(spuDeclare.getHsCode(), item.getHsCode()) && !spuDeclare.getElementValue().contains("||")
                    && !spuDeclare.getElementValue().endsWith("|")) {
                item.setElementValue(spuDeclare.getElementValue());
                continue;
            }
            if (!item.getElementValue().contains("商品档案"))
                continue;
            if (!StringUtils.hasText(item.getHsCode()))
                continue;
            if (CollectionUtils.isEmpty(bdHsCodeConfigEntities))
                continue;
            logisticsDocumentsPrintService.buildProductInfoValue(item, bdHsCodeConfigEntities);
            if (!StringUtils.hasText(item.getElementValue()) || item.getElementValue().contains("||"))
                throw new BusinessServiceException(item.getSpu() + "此款spu对应的 分类报关 要素不完整，请联系报关员补充");
        }
    }

    /**
     * 获取单证页面装箱清单信息,sku级别，用于详细发票
     */
    public LogisticsDocumentsPrintInfo getPrintInfoBySku(LogisticsDocumentsPrintInfoRequest request) {
        LogisticsDocumentsPrintInfo printInfo = getBaseShipmentInfo(request);
        if (CollectionUtils.isEmpty(printInfo.getShipmentIdList()))
            return printInfo;
        List<LogisticsDocumentsPrintItemInfo> shipmentItemInfoList = stockoutShipmentMapper.logisticsDocumentsSkuItem(printInfo.getShipmentIdList());
        // 对价格、重量、描述进行重新设置
        printInfo.setShipmentItemInfoList(buildLogisticsDocumentsPrintItemInfo(request, shipmentItemInfoList));
        buildElementValue(printInfo.getShipmentItemInfoList());
        // 构建重量、中英文
        buildCategoryName(shipmentItemInfoList, request.getLogisticsCompany());
        List<String> collect = shipmentItemInfoList.stream().map(LogisticsDocumentsPrintItemInfo::getCustomsDeclareEn).distinct().collect(Collectors.toList());
        printInfo.setMainProductName(String.join(",", collect));
        if (StrUtil.containsIgnoreCase(request.getLogisticsCompany(), "UPS")) {
            if (collect.size() > 1) {
                printInfo.setMainProductName(collect.get(0) + "," + collect.get(1));
            } else {
                printInfo.setMainProductName(collect.get(0));
            }
        }
        return printInfo;
    }

    /**
     * 对价格、重量、描述进行重新设置
     * 以下调整仅针对收件国家是 美国 或者 波多黎各，其他国家保持不变
     * ①当订单的的商品付款总金额≤100 USD，申报价值就是每件发票价格*0.22
     * ②100 USD＜当订单的的商品付款总金额≤300 USD，申报价值就是每件发票价格*0.16
     * ③300 USD＜当订单的的商品付款总金额≤500 USD，申报价值就是每件发票价格*0.14
     * ④500 USD＜当订单的的商品付款总金额，申报价值就是每件发票价格*0.13
     */
    private List<LogisticsDocumentsPrintItemInfo> buildLogisticsDocumentsPrintItemInfo(LogisticsDocumentsPrintInfoRequest request, List<LogisticsDocumentsPrintItemInfo> shipmentItemInfoList) {
        List<LogisticsDocumentsPrintItemInfo> printItemInfoList = shipmentItemInfoList.stream().peek(itemInfo -> {
            itemInfo.setDescription(String.format("%s(MADE IN CHINA)%s", itemInfo.getCustomsDeclareEn(), itemInfo.getFabricTypeEn()));
            if (StrUtil.containsIgnoreCase(request.getLogisticsCompany(), "fedex")) {
                itemInfo.setDescription(itemInfo.getDescription() + "(MID:CNXIN61QUA)");
            }
            itemInfo.setUnit("PCS");
            itemInfo.setCountry(LogisticsCompanyConstant.DHL.equalsIgnoreCase(request.getLogisticsCompany()) ? "CHINA,PEOPLES REPUBLIC" : "CN");
            if (itemInfo.getActualWeight() != null && itemInfo.getActualWeight().compareTo(BigDecimal.ZERO) != 0)
                itemInfo.setWeight(itemInfo.getActualWeight());
        }).collect(Collectors.toList());
        if (request.getRealPrice()) {
            return printItemInfoList;
        }
        int totalQty = printItemInfoList.stream().mapToInt(LogisticsDocumentsPrintItemInfo::getQty).sum();
        boolean changeInvoicePrice = changeInvoicePrice(request.getLogisticsCompany(), request.getStockoutOrderNoList().get(0));
        if (!changeInvoicePrice) return printItemInfoList;
        List<StockoutReceiverInfoEntity> receiverInfoEntityList = stockoutReceiverInfoService.getByStockoutOrderNos(request.getStockoutOrderNoList());
        if (receiverInfoEntityList.stream().anyMatch(it -> StrUtil.equalsAnyIgnoreCase(it.getCountryCode(), CountryCodeConstant.USA, CountryCodeConstant.PR))) {
            // 计算订单总金额
            double totalAmount = printItemInfoList.stream().mapToDouble(item -> item.getUnitPrice().doubleValue() * item.getQty()).sum();
            return printItemInfoList.stream().peek(itemInfo -> {
                BigDecimal multiplier;
                if (totalAmount <= 100) {
                    multiplier = BigDecimal.valueOf(0.22);
                } else if (totalAmount <= 300) {
                    multiplier = BigDecimal.valueOf(0.16);
                } else if (totalAmount <= 500) {
                    multiplier = BigDecimal.valueOf(0.14);
                } else {
                    multiplier = BigDecimal.valueOf(0.13);
                }

                BigDecimal newUnitPrice = itemInfo.getUnitPrice().multiply(multiplier).setScale(2, RoundingMode.HALF_UP);
                itemInfo.setUnitPrice(newUnitPrice);
                itemInfo.setGoodsPrice(newUnitPrice.multiply(BigDecimal.valueOf(itemInfo.getQty())).doubleValue());
            }).collect(Collectors.toList());
        }
        if (totalQty <= 10) {
            return printItemInfoList.stream().peek(itemInfo -> {
                itemInfo.setUnitPrice(BigDecimal.valueOf(5.0));
                itemInfo.setGoodsPrice(5.0 * itemInfo.getQty());
            }).collect(Collectors.toList());
        } else {
            return printItemInfoList.stream().peek(itemInfo -> {
                BigDecimal invoicePrice = itemInfo.getUnitPrice().divide(BigDecimal.valueOf(2.0), 2).setScale(2, RoundingMode.HALF_UP);
                itemInfo.setUnitPrice(invoicePrice);
                itemInfo.setGoodsPrice(invoicePrice.doubleValue() * itemInfo.getQty());
            }).collect(Collectors.toList());
        }
    }

    private boolean changeInvoicePrice(String logisticsCompany, String stockoutOrderNo) {
        if (!LogisticsCompanyConstant.DHL.equalsIgnoreCase(logisticsCompany)) return true;
        boolean isChangePrice = true;
        StockoutReceiverInfoEntity receiverInfoEntity = stockoutReceiverInfoService.getByStockoutOrderNo(stockoutOrderNo);
        if (Objects.isNull(receiverInfoEntity))
            throw new BusinessServiceException(String.format("出库单【%s】找不到收货人", stockoutOrderNo));
        if (CountryCodeConstant.EUROPEAN_UNION_COUNTRY_CODE.contains(receiverInfoEntity.getCountryCode()))
            isChangePrice = false;
        return isChangePrice;
    }

    /**
     * 单证页面装箱清单信息重载
     *
     * @param request
     * @return
     */
    public List<LogisticsDocumentsReloadShipmentInfo> getShipmentInfoReloadList(LogisticsDocumentsShipmentInfoListRequest request) {
        request.setStatus(request.getRePrint() ? StockoutShipmentStatusEnum.SHIPPED.name() : StockoutShipmentStatusEnum.PACKING_END.name());
        if (CollectionUtils.isEmpty(request.getStockoutOrderNoList()) && !StringUtils.hasText(request.getReceiveInfoMd5())
                && !StringUtils.hasText(request.getShipmentBoxCode()) && request.getBatchId() == null)
            throw new BusinessServiceException("出库单号、地址、箱号、波次号必须要填写其中一个");
        List<LogisticsDocumentsReloadShipmentInfo> shipmentInfoList = stockoutShipmentMapper.logisticsDocumentsShipmentInfo(request);
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKING_LIST_STATUS.getName());
        return shipmentInfoList.stream().peek(info -> info.setStatusStr(statusMap.get(info.getStatus()))).collect(Collectors.toList());
    }

    /**
     * 当天发货信息汇总
     */
    public LogisticsDocumentsShipmentSummary getShipmentSummary(String logisticsCompany, String logisticsAccount) {
        LambdaQueryWrapper<StockoutInvoiceInfoEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(StockoutInvoiceInfoEntity::getLogisticsCompany, logisticsCompany)
                .eq(StockoutInvoiceInfoEntity::getLogisticsAccount, logisticsAccount)
                .ge(StockoutInvoiceInfoEntity::getCreateDate, DateUtils.format(new Date(), "yyyy-MM-dd"));
        List<StockoutInvoiceInfoEntity> invoiceInfoEntityList = invoiceInfoService.list(qw);
        LogisticsDocumentsShipmentSummary summary = new LogisticsDocumentsShipmentSummary();
        summary.setPackageCount(invoiceInfoEntityList.size());
        summary.setGoodsCount(invoiceInfoEntityList.stream().mapToInt(StockoutInvoiceInfoEntity::getGoodsQty).sum());
        summary.setTotalWeight(invoiceInfoEntityList.stream().map(StockoutInvoiceInfoEntity::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add).doubleValue());
        return summary;
    }

    /**
     * 装箱清单列表展示，用来回填箱规和重量
     */
    public List<LogisticsDocumentsShipmentInfo> getShipmentInfoList(LogisticsDocumentsPrintInfoRequest request) {
        StockoutShipmentItemRequest query = new StockoutShipmentItemRequest();
        query.setStatus(request.getRePrint() ? StockoutShipmentStatusEnum.SHIPPED.name() : StockoutShipmentStatusEnum.PACKING_END.name());
        query.setLogisticsCompany(request.getLogisticsCompany());
        query.setStockoutOrderNoList(request.getStockoutOrderNoList());
        List<StockoutShipmentItem> queryList = stockoutShipmentMapper.getShipmentItemByQuery(query);
        return queryList.stream().map(StockoutBuilding::logisticsShipmentInfo).collect(Collectors.toList());
    }

    /**
     * 装箱清单列表保存回填箱规和重量
     */
    public void saveShipmentBoxSizeAndWeight(LogisticsDocumentsSaveShipmentRequest request) {
        List<LogisticsDocumentsSaveShipmentRequest.SaveShipmentItemRequest> shipmentItemRequestList = request.getShipmentItemRequestList();
        shipmentItemRequestList.forEach(item -> {
            StockoutShipmentEntity stockoutShipmentEntity = shipmentService.getById(item.getShipmentId());
            stockoutShipmentEntity.setWeight(item.getWeight());
            stockoutShipmentEntity.setBoxSize(item.getBoxSize());
            stockoutShipmentEntity.setUpdateBy(loginInfoService.getName());
            shipmentService.updateById(stockoutShipmentEntity);
        });
    }

    public void saveShipmentVolumeWeight(LogisticsDocumentsSaveShipmentRequest request) {
        List<LogisticsDocumentsSaveShipmentRequest.SaveShipmentItemRequest> shipmentItemRequestList = request.getShipmentItemRequestList();
        shipmentItemRequestList.forEach(item -> {
            StockoutShipmentEntity stockoutShipmentEntity = shipmentService.getById(item.getShipmentId());
            stockoutShipmentEntity.setVolumeWeight(item.getVolumeWeight());
            stockoutShipmentEntity.setUpdateBy(loginInfoService.getName());
            shipmentService.updateById(stockoutShipmentEntity);
        });
    }


    /**
     * ups打印
     * 包含四步操作
     * 1.获取单号，面单
     * 2.发货操作
     * 3.保存发票信息
     */
    @Transactional
    @JLock(keyConstant = "printUps", lockKey = "#upsInfo.stockoutOrderNo")
    public BaseGetLogisticsNoResponse printUps(UpsRequestInfo upsInfo) {
        // 1.获取单号，面单
        BaseGetLogisticsNoResponse response;
        if (LogisticsCompanyConstant.UPS_HZ.equals(upsInfo.getLogisticsCompany())) {
            response = upsHZService.print(upsInfo);
        } else {
            response = upsService.print(upsInfo);
        }
        // 2.发货操作
        LogisticsDocumentsBaseRequestInfo baseRequestInfo = new LogisticsDocumentsBaseRequestInfo();
        BeanUtils.copyProperties(upsInfo, baseRequestInfo);
        shipmentSaveLogisticsNo(upsInfo.getLogisticsCompany(), response.getLogisticsNo(), baseRequestInfo);
        //3.保存发票信息
        if (!InvoiceTemplateEnum.PAPERLESS_INVOICE.name().equals(upsInfo.getInvoiceTemplate()))
            upsInfo.setInvoiceTitle(getInvoiceTitle(upsInfo.getInvoiceTitle(), upsInfo.getReceiver().getCountryCode()));
        upsInfo.setLogisticsNos(response.getLogisticsNos());
        upsInfo.setServiceTypeName("65".equals(upsInfo.getServiceType()) ? "EXPRESS SAVER" : "EXPEDITED");
        upsInfo.setServiceTypeCode("65".equals(upsInfo.getServiceType()) ? "2" : "1P");
        invoiceInfoService.addStockoutInvoiceInfo(upsInfo.getLogisticsCompany(), response.getLogisticsNo(), JsonMapper.toJson(upsInfo), baseRequestInfo);
        // 非ups杭州，都需要上传发联、不支持无纸化的国家才上传发票
        if (!LogisticsCompanyConstant.UPS_HZ.equals(upsInfo.getLogisticsCompany())) {
            StockoutShipmentLogisticsInfoEntity logisticsInfoEntity = new StockoutShipmentLogisticsInfoEntity();
            logisticsInfoEntity.setLogisticsCompany(upsInfo.getLogisticsCompany());
            logisticsInfoEntity.setLogisticsNo(response.getLogisticsNo());
            logisticsInfoEntity.setUploadFalian(UpsUploadStatusEnum.NEW.name());
            logisticsInfoEntity.setCreateBy(loginInfoService.getName());
            if (Objects.nonNull(response.getSupportPaperless()) && response.getSupportPaperless()) {
                logisticsInfoEntity.setUploadInvoice(UpsUploadStatusEnum.NO_NEED.name());
            } else {
                logisticsInfoEntity.setUploadInvoice(UpsUploadStatusEnum.NEW.name());
            }
            shipmentLogisticsInfoService.save(logisticsInfoEntity);
        }
        labelService.updatePrintStatus(response.getLogisticsNo());
        // 日志
        addStockoutOrderLog(baseRequestInfo, response.getLogisticsNo());
        return response;
    }

    // 添加日志
    @Transactional
    public void addStockoutOrderLog(LogisticsDocumentsBaseRequestInfo baseRequestInfo, String logisticsNo) {
        String content = String.format("出库单【%s】完成物流报关单证，物流【%s】运单号%s", baseRequestInfo.getStockoutOrderNo(), baseRequestInfo.getLogisticsCompany(), logisticsNo);
        stockoutOrderLogService.addLog(baseRequestInfo.getStockoutOrderNo(), StockoutOrderLogTypeEnum.DOCUMENTS_CONFIRM, content);
        stockoutLogQueueService.addLogQueueOfOrderNo(Arrays.asList(baseRequestInfo.getOrderNos().split(StringConstant.COMMA)), "发货确定");
    }

    public LogisticsDocumentsPrintInfo getBaseShipmentInfo(LogisticsDocumentsPrintInfoRequest request) {
        List<Integer> shipmentIdList = request.getShipmentIdList();
        String boxNoStr;
        String orderNoStr;
        String boxSizeStr;
        if (CollectionUtils.isEmpty(request.getShipmentIdList())) {
            if (CollectionUtils.isEmpty(request.getStockoutOrderNoList()))
                throw new BusinessServiceException("请选择出库单进行报关sku查询");
            StockoutShipmentItemRequest query = new StockoutShipmentItemRequest();
            query.setStatus(request.getRePrint() ? StockoutShipmentStatusEnum.SHIPPED.name() : StockoutShipmentStatusEnum.PACKING_END.name());
            query.setLogisticsCompany(request.getLogisticsCompany());
            query.setStockoutOrderNoList(request.getStockoutOrderNoList());
            List<StockoutShipmentItem> itemList = stockoutShipmentMapper.getShipmentItemByQuery(query);
            shipmentIdList = itemList.stream().map(StockoutShipmentItem::getShipmentId).distinct().collect(Collectors.toList());
            boxNoStr = itemList.stream().map(entity -> String.valueOf(entity.getBoxIndex())).distinct().collect(Collectors.joining(","));
            long count = itemList.stream().map(StockoutShipmentItem::getBoxSize).distinct().count();
            boxSizeStr = count > 1 ? "存在多个规格箱子" : "";
            orderNoStr = itemList.stream().map(StockoutShipmentItem::getOrderNo).distinct().collect(Collectors.joining(","));
        } else {
            List<StockoutShipmentEntity> shipmentEntities = shipmentService.listByIds(shipmentIdList);
            boxNoStr = shipmentEntities.stream().map(entity -> String.valueOf(entity.getBoxIndex())).collect(Collectors.joining(","));
            long count = shipmentEntities.stream().map(StockoutShipmentEntity::getBoxSize).distinct().count();
            boxSizeStr = count > 1 ? "存在多个规格箱子" : "";
            orderNoStr = shipmentItemService.findByShipmentIdList(shipmentIdList).stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.joining(","));
        }
        LogisticsDocumentsPrintInfo shipmentInfo = new LogisticsDocumentsPrintInfo();
        shipmentInfo.setShipmentIdList(shipmentIdList);
        shipmentInfo.setBoxSizeNotice(boxSizeStr);
        shipmentInfo.setBoxNoStr(boxNoStr);
        shipmentInfo.setOrderNoStr(orderNoStr);
        return shipmentInfo;
    }

    private Map<String, List<SelectModel>> buildStateMap(String logisticsCompany) {
        List<TmsLogisticsCountryProvinceMapping> logisticsCountryProvinceList = tmsCacheService.getLogisticsCountryProvinceList(logisticsCompany);
        Map<String, List<TmsLogisticsCountryProvinceMapping>> map = logisticsCountryProvinceList.stream().collect(Collectors.groupingBy(TmsLogisticsCountryProvinceMapping::getCountryCode));
        Map<String, List<SelectModel>> stateMap = new HashMap<>();
        map.forEach((countryCode, countryProvinceMappingList) ->
                stateMap.put(countryCode, countryProvinceMappingList.stream().map(mapping -> new SelectModel(mapping.getProvinceCode(), mapping.getProvinceName())).collect(Collectors.toList()))
        );
        return stateMap;
    }

    private Map<String, AddressInfo> buildSenderMap(StockoutOrderEntity stockoutOrderEntity) {
        List<LogisticsAccountInfo> logisticsAccountInfoList = tmsCacheService.getLogisticsAccountInfoList(stockoutOrderEntity.getLogisticsCompany());
        Map<String, AddressInfo> senderMap = new HashMap<>();
        String storeCompany = buildStoreCompany(stockoutOrderEntity);
        logisticsAccountInfoList.forEach(accountInfo -> {
            AddressInfo address = StockoutShipmentBuilding.buildAddressInfo(accountInfo, storeCompany);
            senderMap.put(accountInfo.getLogisticsAccount(), address);
        });
        return senderMap;
    }

    private String buildStoreCompany(StockoutOrderEntity stockoutOrderEntity) {
        if (!stockoutOrderEntity.getLocation().equalsIgnoreCase(LocationEnum.QUANZHOU.getLocation())) {
            return "";
        }
        BdSystemParameterEntity bdSystemParameterEntity = parameterService.getByKey(BdSystemParameterEnum.WMS_STORE_DELIVERY_COMPANY.getKey());
        if (bdSystemParameterEntity != null && StrUtil.isNotBlank(bdSystemParameterEntity.getConfigValue())) {
            Map<String, List<Integer>> map = JsonMapper.fromJson(bdSystemParameterEntity.getConfigValue(), Map.class);
            for (Map.Entry<String, List<Integer>> entry : map.entrySet()) {
                if (entry.getValue().contains(stockoutOrderEntity.getStoreId())) {
                    return entry.getKey();
                }
            }
        }
        return "";
    }

    private AddressInfo buildReceiver(StockoutOrderEntity stockoutOrderEntity, LogisticsDocumentsBaseInfo baseInfo) {
        StockoutReceiverInfo receiverInfo = stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        AddressInfo address = new AddressInfo();
        Boolean faireOrder = stockoutFaireShipmentItemService.existStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        StockoutShipmentBuilding.buildReceiverInfo(receiverInfo, address, faireOrder);
        baseInfo.setBillingAccountNumber(receiverInfo.getBillingAccountNumber());
        baseInfo.setReceiveInfoMd5(receiverInfo.getReceiveInfoMd5());
        if (!faireOrder) {
            List<SelectModel> stateList = baseInfo.getStateMap().get(receiverInfo.getCountryCode());
            if (!CollectionUtils.isEmpty(stateList)) {
                Optional<SelectModel> optional = stateList.stream().filter(t -> t.getLabel().equalsIgnoreCase(receiverInfo.getReceiverState())).findFirst();
                optional.ifPresent(selectModel -> address.setStateOrProvinceCode(selectModel.getValue()));
            }
        }
        return address;
    }

    /**
     * 根据接收国家获取发票标题
     */
    public String getInvoiceTitle(String invoiceTitle, String countryCode) {
        return StockoutBuilding.getInvoiceTitle(invoiceTitle, countryCode);
    }

    /**
     * 装箱清单回填物流单号
     */
    public void shipmentSaveLogisticsNo(String logisticsCompany, String logisticsNo, LogisticsDocumentsBaseRequestInfo baseRequestInfo) {
        List<StockoutShipmentEntity> shipmentEntityList = shipmentService.listByIds(baseRequestInfo.getShipmentIdList());
        if (CollectionUtils.isEmpty(shipmentEntityList))
            throw new BusinessServiceException("装箱清单回填物流单号失败，找不到对应装箱清单");
        List<Integer> shipmentIds = new ArrayList<>();
        List<StockoutShipmentEntity> sortList = shipmentEntityList.stream().sorted(Comparator.comparing(StockoutShipmentEntity::getBoxIndex)).collect(Collectors.toList());
        Set<Integer> waitShipShipment = new HashSet<>(16);
        sortList.forEach(shipmentEntity -> {
            // todo 物流重新打印记录 EgSys_LogisticsReplace
            if (!Objects.equals(shipmentEntity.getStatus(), StockoutShipmentStatusEnum.SHIPPED.name()))
                waitShipShipment.add(shipmentEntity.getShipmentId());
            updateShipmentInfo(logisticsCompany, logisticsNo, baseRequestInfo, sortList, shipmentEntity);
            // 同步.net装箱清单，已发货
            shipmentIds.add(shipmentEntity.getShipmentId());
        });
        // 找所选箱子的全部出库单
        List<StockoutShipmentSearchResult> shipmentConfirmList = stockoutShipmentMapper.findShipmentItemList(shipmentIds, null, 0);
        List<String> stockoutOrderNos = shipmentConfirmList.stream().map(StockoutShipmentSearchResult::getStockoutOrderNo).distinct().filter(StringUtils::hasText).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderNos))
            throw new BusinessServiceException("无法找到出库单！");
        // 箱子发货
        if (!baseRequestInfo.getRePrint() && !CollectionUtils.isEmpty(waitShipShipment)) {
            List<StockoutShipmentItemEntity> items = shipmentItemService.findByShipmentIdList(baseRequestInfo.getShipmentIdList());
            if (!CollectionUtils.isEmpty(items)) {
                StockoutOrderEntity byStockoutOrderNo = stockoutOrderService.getByStockoutOrderNo(items.get(0).getStockoutOrderNo());
                stockoutShipService.shippingUpdateStock(shipmentConfirmList.stream()
                        .filter(it -> waitShipShipment.contains(it.getShipmentId())).collect(Collectors.toList()), byStockoutOrderNo.getSpaceId());
            }
        }
        // 出库单发货
        List<StockoutShipmentSearchResult> allShipments = stockoutShipmentMapper.findShipmentConfirmList(stockoutOrderNos, null, null);
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.getByStockoutOrderNoList(shipmentConfirmList.stream().map(StockoutShipmentSearchResult::getStockoutOrderNo).collect(Collectors.toList()));
        allShipments.stream().collect(Collectors.groupingBy(StockoutShipmentSearchResult::getStockoutOrderNo))
                .forEach((stockoutOrderNo, itemList) -> {
                    boolean result = itemList.stream().allMatch(s -> StockoutShipmentStatusEnum.SHIPPED.name().equals(s.getStatus()));
                    StockoutOrderEntity stockoutOrderEntity = stockoutOrderEntityList.stream().filter(t -> stockoutOrderNo.equals(t.getStockoutOrderNo())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("找不到出库单【%s】", stockoutOrderNo)));
                    if (result && !StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())) {
                        applicationContext.getBean(StockoutOrderScanCheckService.class).finishScanTask(stockoutOrderEntity);
                        stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.DELIVERED.name());
                        // 仓间调拨出库需要生成在途跟踪数据
                        stockoutOrderTrackingService.doShip(stockoutOrderEntity);
//                        if (Objects.equals(stockoutOrderEntity.getPlatformName(), StockoutOrderPlatformEnum.FAIRE.getName()))
//                            stockoutOrderEntityDelivery.add(stockoutOrderEntity);
                    }
                });
        deliveryFaireShipment(shipmentIds, logisticsNo);
        // 同步装箱清单
        erpPickingBoxService.sendErpPickingBoxShippedSyncByShipmentIdList(shipmentIds);
    }

    private void deliveryFaireShipment(List<Integer> shipmentEntityList, String logisticsNo) {
        deliveryFaireShipmentByShipmentId(shipmentEntityList, logisticsNo);
//        if (CollectionUtils.isEmpty(stockoutOrderEntityDelivery))
//            return;
//        for (StockoutOrderEntity entity : stockoutOrderEntityDelivery) {
//            List<Integer> faireShipmentIds = stockoutFaireShipmentItemService.getFaireShipmentIdsByStockoutOrderNo(entity.getStockoutOrderNo());
//            if (CollectionUtils.isEmpty(faireShipmentIds))
//                continue;
//            List<StockoutFaireShipmentEntity> faireShipment = faireShipmentService.listByIds(faireShipmentIds);
//            if (CollectionUtils.isEmpty(faireShipment))
//                continue;
//            List<Integer> mergeFaireId = new ArrayList<>();
//            faireShipment.forEach(item -> {
//                if (!Objects.equals(StockoutFaireShipmentStatusEnum.SHIPPED.name(), item.getStatus())) {
//                    item.setDeliveryDate(new Date());
//                    item.setStatus(StockoutFaireShipmentStatusEnum.SHIPPED.name());
//                    faireShipmentService.updateById(item);
//                }
//                if (item.getMergeFaireShipmentId() != null && item.getMergeFaireShipmentId() != 0) {
//                    StockoutFaireShipmentEntity byId = faireShipmentService.getById(item.getMergeFaireShipmentId());
//                    if (byId != null && !StockoutFaireShipmentStatusEnum.SHIPPED.name().equals(byId.getStatus())) {
//                        mergeFaireId.add(byId.getId());
//                    }
//                }
//            });
//            if (CollectionUtils.isEmpty(mergeFaireId))
//                return;
//            mergeFaireId.stream().distinct().forEach(item -> {
//                List<StockoutFaireShipmentEntity> byMergeFaireShipmentIds = faireShipmentService.getByMergeFaireShipmentIds(Collections.singletonList(item));
//                StockoutFaireShipmentEntity byId = faireShipmentService.getById(item);
//                if (byId == null || StockoutFaireShipmentStatusEnum.SHIPPED.name().equals(byId.getStatus()))
//                    return;
//                if (byMergeFaireShipmentIds.stream().allMatch(it -> StockoutFaireShipmentStatusEnum.SHIPPED.name().equals(it.getStatus()))) {
//                    byId.setDeliveryDate(new Date());
//                    byId.setStatus(StockoutFaireShipmentStatusEnum.SHIPPED.name());
//                    faireShipmentService.updateById(byId);
//                }
//            });
//        }

    }

    private void deliveryFaireShipmentByShipmentId(List<Integer> shipmentEntityList, String logisticsNo) {
        List<StockoutFaireShipmentEntity> byShipmentIds = faireShipmentService.getByShipmentIds(shipmentEntityList);
        if (CollectionUtils.isEmpty(byShipmentIds))
            return;
        List<Integer> mergeFaireId = new ArrayList<>();
        byShipmentIds.forEach(faire -> {
            if (!Objects.equals(StockoutFaireShipmentStatusEnum.SHIPPED.name(), faire.getStatus())) {
                faire.setDeliveryDate(new Date());
                faire.setStatus(StockoutFaireShipmentStatusEnum.SHIPPED.name());
            }
            faire.setLogisticsNo(logisticsNo);
            faireShipmentService.updateById(faire);
            if (faire.getMergeFaireShipmentId() != null && faire.getMergeFaireShipmentId() != 0) {
                mergeFaireId.add(faire.getMergeFaireShipmentId());
            }
        });
        if (CollectionUtils.isEmpty(mergeFaireId))
            return;
        mergeFaireId.stream().distinct().forEach(item -> {
            List<StockoutFaireShipmentEntity> byMergeFaireShipmentIds = faireShipmentService.getByMergeFaireShipmentIdsWithNoDelete(Collections.singletonList(item));
            StockoutFaireShipmentEntity byId = faireShipmentService.getById(item);
            if (byId == null)
                return;
            if (StockoutFaireShipmentStatusEnum.SHIPPED.name().equals(byId.getStatus())) {
                byId.setLogisticsNo(logisticsNo);
                faireShipmentService.updateById(byId);
                return;
            }
            if (byMergeFaireShipmentIds.stream().allMatch(it -> StockoutFaireShipmentStatusEnum.SHIPPED.name().equals(it.getStatus()))) {
                byId.setDeliveryDate(new Date());
                byId.setStatus(StockoutFaireShipmentStatusEnum.SHIPPED.name());
            }
            byId.setLogisticsNo(logisticsNo);
            faireShipmentService.updateById(byId);
        });
    }

    private void updateShipmentInfo(String logisticsCompany, String logisticsNo, LogisticsDocumentsBaseRequestInfo baseRequestInfo, List<StockoutShipmentEntity> shipmentEntityList, StockoutShipmentEntity shipmentEntity) {
        if (shipmentEntityList.indexOf(shipmentEntity) == 0) {
            shipmentEntity.setBoxSize(baseRequestInfo.getBoxSize());
            shipmentEntity.setVolumeWeight(baseRequestInfo.getVolumeWeightValue());
        }
        shipmentEntity.setLogisticsNo(logisticsNo);
        shipmentEntity.setLogisticsCompany(logisticsCompany);
        shipmentEntity.setDeliveryDate(baseRequestInfo.getRePrint() || shipmentEntity.getDeliveryDate() != null ? shipmentEntity.getDeliveryDate() : new Date());
        shipmentEntity.setOperateDeliveryDate(new Date());
        shipmentEntity.setStatus(StockoutShipmentStatusEnum.SHIPPED.name());
        shipmentEntity.setUpdateBy(loginInfoService.getName());
        // todo 发货人字段是否加入
        if (baseRequestInfo.getPackageCount() == 1 && shipmentEntityList.indexOf(shipmentEntity) == 0 && baseRequestInfo.getPackageWeight().doubleValue() > 0)
            shipmentEntity.setWeight(baseRequestInfo.getPackageWeight());
        shipmentService.updateById(shipmentEntity);
    }

    /**
     * 发票打印
     */
    public Map<String, List<String>> printInvoice(List<String> logisticsNoList) {
        Map<String, List<String>> map = new HashMap<>();
        for (String logisticsNo : logisticsNoList) {
            StockoutInvoiceInfoEntity invoiceInfoEntity = invoiceInfoService.getByLogisticsNo(logisticsNo);
            if (Objects.isNull(invoiceInfoEntity)) continue;
            String logisticsCompany = invoiceInfoEntity.getLogisticsCompany();
            if (logisticsCompany.contains(LogisticsCompanyConstant.DHL)) {
                map.put(LogisticsCompanyConstant.DHL, logisticsDocumentsInvoiceService.printInvoiceByDhl(invoiceInfoEntity));
            } else if (logisticsCompany.contains(LogisticsCompanyConstant.FEDEX)) {
                map.put(LogisticsCompanyConstant.FEDEX, logisticsDocumentsInvoiceService.printInvoiceByFedEx(invoiceInfoEntity));
            } else if (logisticsCompany.contains(LogisticsCompanyConstant.UPS)) {
                map.put(LogisticsCompanyConstant.UPS, logisticsDocumentsInvoiceService.printInvoiceByUps(invoiceInfoEntity));
            }
        }
        if (CollectionUtils.isEmpty(map))
            throw new BusinessServiceException("没有发票可打印!");
        return map;
    }

    public List<String> printFaLianByUps(List<String> logisticsNoList) {
        List<String> htmlList = new ArrayList<>();
        for (String logisticsNo : logisticsNoList) {
            StockoutInvoiceInfoEntity invoiceInfoEntity = invoiceInfoService.getByLogisticsNo(logisticsNo);
            if (Objects.isNull(invoiceInfoEntity)) continue;
            String logisticsCompany = invoiceInfoEntity.getLogisticsCompany();
            if (logisticsCompany.contains(LogisticsCompanyConstant.UPS)) {
                UpsRequestInfo requestInfo = JsonMapper.fromJson(invoiceInfoEntity.getInvoiceContent(), UpsRequestInfo.class);
                htmlList.add(logisticsDocumentsInvoiceService.getUpsFaLianHtml(logisticsNo, requestInfo));
            }
        }
        if (CollectionUtils.isEmpty(htmlList))
            throw new BusinessServiceException("没有发联可打印!");
        return htmlList;
    }

    @Transactional
    public void uploadFaLianAndInvoiceByUps(String logisticsNo) {
        QueryWrapper<StockoutShipmentLogisticsInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutShipmentLogisticsInfoEntity::getLogisticsNo, logisticsNo)
                .and(qw -> qw.eq(StockoutShipmentLogisticsInfoEntity::getUploadInvoice, UpsUploadStatusEnum.NEW.name())
                        .or().eq(StockoutShipmentLogisticsInfoEntity::getUploadFalian, UpsUploadStatusEnum.NEW.name()));
        List<StockoutShipmentLogisticsInfoEntity> logisticsInfoEntityList = shipmentLogisticsInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(logisticsInfoEntityList))
            throw new BusinessServiceException("所选运单,发票,发联都已上传无需上传");
        for (StockoutShipmentLogisticsInfoEntity logisticsInfoEntity : logisticsInfoEntityList) {
            StockoutInvoiceInfoEntity invoiceInfoEntity = invoiceInfoService.getByLogisticsNo(logisticsInfoEntity.getLogisticsNo());
            if (Objects.isNull(invoiceInfoEntity))
                throw new BusinessServiceException(String.format("物流单号【%s】在发票信息表中不存在", logisticsInfoEntity.getLogisticsNo()));
            UpsRequestInfo requestInfo = JsonMapper.fromJson(invoiceInfoEntity.getInvoiceContent(), UpsRequestInfo.class);
            if (UpsUploadStatusEnum.NEW.name().equals(logisticsInfoEntity.getUploadInvoice())) {
                logisticsDocumentsInvoiceService.uploadInvoiceByUps(logisticsInfoEntity.getLogisticsNo(), requestInfo);
                logisticsInfoEntity.setUploadInvoice(UpsUploadStatusEnum.SUCCESS.name());
            }
            if (UpsUploadStatusEnum.NEW.name().equals(logisticsInfoEntity.getUploadFalian())) {
                logisticsDocumentsInvoiceService.uploadFaLianByUps(logisticsInfoEntity.getLogisticsNo(), requestInfo);
                logisticsInfoEntity.setUploadFalian(UpsUploadStatusEnum.SUCCESS.name());
            }
            logisticsInfoEntity.setUpdateBy(loginInfoService.getName());
            shipmentLogisticsInfoService.updateById(logisticsInfoEntity);
        }
    }

    @Transactional
    public void uploadFaLianByUps(List<String> logisticsNoList) {
        QueryWrapper<StockoutShipmentLogisticsInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutShipmentLogisticsInfoEntity::getLogisticsNo, logisticsNoList)
                .eq(StockoutShipmentLogisticsInfoEntity::getUploadFalian, UpsUploadStatusEnum.NEW.name());
        List<StockoutShipmentLogisticsInfoEntity> logisticsInfoEntityList = shipmentLogisticsInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(logisticsInfoEntityList))
            throw new BusinessServiceException("所选运单,发联都已上传无需上传");
        for (StockoutShipmentLogisticsInfoEntity logisticsInfoEntity : logisticsInfoEntityList) {
            StockoutInvoiceInfoEntity invoiceInfoEntity = invoiceInfoService.getByLogisticsNo(logisticsInfoEntity.getLogisticsNo());
            if (Objects.isNull(invoiceInfoEntity))
                throw new BusinessServiceException(String.format("物流单号【%s】在发票信息表中不存在", logisticsInfoEntity.getLogisticsNo()));
            UpsRequestInfo requestInfo = JsonMapper.fromJson(invoiceInfoEntity.getInvoiceContent(), UpsRequestInfo.class);
            logisticsDocumentsInvoiceService.uploadFaLianByUps(logisticsInfoEntity.getLogisticsNo(), requestInfo);
            logisticsInfoEntity.setUploadFalian(UpsUploadStatusEnum.SUCCESS.name());
            logisticsInfoEntity.setUpdateBy(loginInfoService.getName());
            shipmentLogisticsInfoService.updateById(logisticsInfoEntity);
        }
    }

    @Transactional
    public void uploadInvoiceByUps(List<String> logisticsNoList) {
        QueryWrapper<StockoutShipmentLogisticsInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutShipmentLogisticsInfoEntity::getLogisticsNo, logisticsNoList)
                .eq(StockoutShipmentLogisticsInfoEntity::getUploadInvoice, UpsUploadStatusEnum.NEW.name());
        List<StockoutShipmentLogisticsInfoEntity> logisticsInfoEntityList = shipmentLogisticsInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(logisticsInfoEntityList))
            throw new BusinessServiceException("所选运单,发票都已上传无需上传");
        for (StockoutShipmentLogisticsInfoEntity logisticsInfoEntity : logisticsInfoEntityList) {
            StockoutInvoiceInfoEntity invoiceInfoEntity = invoiceInfoService.getByLogisticsNo(logisticsInfoEntity.getLogisticsNo());
            if (Objects.isNull(invoiceInfoEntity))
                throw new BusinessServiceException(String.format("物流单号【%s】在发票信息表中不存在", logisticsInfoEntity.getLogisticsNo()));
            UpsRequestInfo requestInfo = JsonMapper.fromJson(invoiceInfoEntity.getInvoiceContent(), UpsRequestInfo.class);
            logisticsDocumentsInvoiceService.uploadInvoiceByUps(logisticsInfoEntity.getLogisticsNo(), requestInfo);
            logisticsInfoEntity.setUploadInvoice(UpsUploadStatusEnum.SUCCESS.name());
            logisticsInfoEntity.setUpdateBy(loginInfoService.getName());
            shipmentLogisticsInfoService.updateById(logisticsInfoEntity);
        }
    }

    @org.springframework.transaction.annotation.Transactional
    @JLock(keyConstant = "uploadUpsDocument", lockKey = "#logisticsNo")
    public AsyncProcessFlowResult asyncPrintUps(String logisticsNo) {
        LocationWrapperMessage<String> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), logisticsNo);
        return asyncProcessFlowService.createFlow(KafkaConstant.STOCKOUT_UPLOAD_FALIAN_INVOICE_TOPIC, KafkaConstant.STOCKOUT_UPLOAD_FALIAN_INVOICE_MARK, message, logisticsNo);
    }
}
