package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.domain.stock.StockSkuPositionInfo;
import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductAddRequest;
import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryOrderDirectShelveEditRequest;
import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryOrderDirectShelveItemRequest;
import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryOrderDirectShelveRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderItemResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryOrderItemSkuListResponse;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.ErpStockinApiService;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackReceivedNewRequest;
import com.nsy.wms.business.manage.erp.request.SyncSupplierDeliveryDetailRequest;
import com.nsy.wms.business.manage.gc.GcApiService;
import com.nsy.wms.business.manage.supplier.request.DeliveryOverShipmentRequest;
import com.nsy.wms.business.manage.supplier.response.DeliveryOverShipmentResponse;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockCenterCommonService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinSupplierDeliveryOrderMapper;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 工厂出库单明细编辑服务
 * @author: caishaohui
 * @time: 2024/5/29 11:05
 */
@Service
public class StockinSupplierDeliveryOrderEditService {

    @Autowired
    private StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockinReturnProductService stockinReturnProductService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockService stockService;
    @Autowired
    private StockinSupplierDeliveryOrderMapper stockinSupplierDeliveryOrderMapper;
    @Autowired
    private StockinSupplierDeliveryOrderService stockinSupplierDeliveryOrderService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private StockCenterCommonService stockCenterCommonService;
    @Autowired
    private StockinOrderLogService stockinOrderLogService;
    @Autowired
    private StockinDeliveryPdaService stockinDeliveryPdaService;
    @Autowired
    private StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    private StockPlatformScheduleItemService stockPlatformScheduleItemService;
    @Autowired
    private StockinScanLogService stockinScanLogService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ErpStockinApiService erpStockinApiService;
    @Autowired
    private BdErpSpaceMappingService bdErpSpaceMappingService;
    @Autowired
    private StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    private GcApiService gcApiService;

    /**
     * 工厂出库单详情-明细编辑功能（已退货数和已上架数）
     * 1.校验入参、入库任务状态、入库单状态、已上架数量和已退货数量
     * <p>
     * 当已上架数+已退货数大于计划数，只允许增加上架数，不允许增加退货数
     * 如果上架数+已退货数大于计划数，且上架数>最大上架数，调用scm系统，判断增加计划数还是新增明细
     * <p>
     * 2.数量处理逻辑
     * 2-1 已上架数<当前上架数，扣减库位库存
     * 2-2 已上架数>当前上架数，增加库位库存
     * 2-3 已退货数<当前退货数，扣减退货库位库存
     * 2-4 已退货数>当前退货数，新增退货库位库存
     *
     * @param request
     */
    @Transactional
    @JLock(keyConstant = "shelveItemEdit", lockKey = "#request.supplierDeliveryBoxCode + '-' + #request.sku")
    public void shelveItemEdit(StockinSupplierDeliveryOrderDirectShelveEditRequest request) {
        // 1.校验入参、入库任务状态、入库单状态、已上架数量和已退货数量
        StockinOrderTaskEntity stockinOrderTaskEntity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(request.getSupplierDeliveryBoxCode());
        if (StockinOrderTaskStatusEnum.RECEIVED.name().equals(stockinOrderTaskEntity.getStatus()))
            throw new BusinessServiceException("入库任务已收货完成，不可编辑");
        StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(stockinOrderTaskEntity.getTaskId());
        if (StockinOrderStatusEnum.isShelved(stockinOrderEntity.getStatus()))
            throw new BusinessServiceException("入库单已经上架完成，不可编辑");
        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(stockinOrderTaskEntity.getTaskId(), request.getSku());
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            if (request.getShelvedQty() + request.getReturnedQty() <= 0)
                throw new BusinessServiceException("已上架数+已退货数不能为0");
            overShipment(stockinOrderTaskEntity, null, 0, request);
            if (request.getShelvedQty() == 0)
                return;
            taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(stockinOrderTaskEntity.getTaskId(), request.getSku());
        }

        //查询是否存在[多发类型]的多发数
/*        String supplierDeliveryBoxCode = stockinOrderTaskEntity.getSupplierDeliveryNo() + "-" + StockConstant.STOCKIN_ORDER_NO_DFRK;
        int otherQty = stockinOrderTaskItemService.getBaseMapper().findQtyBySupplierDeliveryBoxCodeAndSku(supplierDeliveryBoxCode, request.getSku());
        //校验，存在多发数时，不允许再编辑数量
        if (otherQty > 0)
            throw new BusinessServiceException("存在多发数,不允许再编辑数量，若需要退货，请走架上退货流程");*/

        // 计划入库数
        int expectedQty = taskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
        // 修改后的入库数
        int editStockinQty = request.getReturnedQty() + request.getShelvedQty();

        List<StockinOrderItemEntity> orderItemEntityList = stockinOrderItemService.findAllByStockinOrderIdAndSku(stockinOrderEntity.getStockinOrderId(), request.getSku());
        // 2.数量处理逻辑
        int shelvedQty = orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getShelvedQty).sum();
        int returnQty = orderItemEntityList.stream().mapToInt(StockinOrderItemEntity::getReturnQty).sum();


        //当已上架数+已退货数大于计划数，只允许增加上架数，不允许增加退货数
        if (expectedQty < editStockinQty) {
            if (request.getReturnedQty() > returnQty)
                throw new BusinessServiceException("已上架数+已退货数大于计划数时，不允许增加退货数");
            if (request.getShelvedQty() > shelvedQty)
                //调用scm系统，判断增加计划数还是新增明细
                overShipment(stockinOrderTaskEntity, taskItemEntityList, editStockinQty, request);
        }
        //必须做到先减后增，否则可能因为超出入库数而无法增加 上架数/退货数

        //2-1 已上架数<当前上架数，扣减库位库存
        if (request.getShelvedQty() < shelvedQty)
            reduceShelvedQty(stockinOrderTaskEntity, stockinOrderEntity, orderItemEntityList, Math.abs(request.getShelvedQty() - shelvedQty));

        //2-3 已退货数<当前退货数，扣减退货库位库存
        if (request.getReturnedQty() < returnQty)
            reduceReuturnQty(stockinOrderTaskEntity, stockinOrderEntity, orderItemEntityList, Math.abs(request.getReturnedQty() - returnQty));

        //2-2 已上架数>当前上架数，增加库位库存
        if (request.getShelvedQty() > shelvedQty)
            addShelvedQty(request.getPositionCode(), request.getSku(), stockinOrderTaskEntity, Math.abs(request.getShelvedQty() - shelvedQty));

        //2-4 已退货数>当前退货数，新增退货库位库存
        if (request.getReturnedQty() > returnQty)
            addReturnQty(stockinOrderTaskEntity, stockinOrderEntity, request.getSku(), Math.abs(request.getReturnedQty() - returnQty));

        //记录入库单日志
        String content = String.format("工厂出库单详情编辑【%s】已上架数 %s 件,已退货数 %s 件", request.getSku(), request.getShelvedQty(), request.getReturnedQty());
        stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.SHELVE_EDIT.getName(), content);
    }

    /**
     * 工厂多发处理 调用supplier成功即时修改入库数，提交事务，防止报错
     * case1：回填【出库数】    采购单待发货 大于 该工厂出库单计划数， 供应链系统扣减待发货数，wms系统增加计划入库数
     * case2：增加出库明细记录  采购单存在sku，但不在当前工厂出库单下  供应链新增一条出库明细，wms新增出库明细
     * case3： 收货数超出采购单待发货总数，判定为多收，生成【工厂多发入库类型】的入库单，补月台任务明细、入库任务、调用商通补接收单
     *
     * @param stockinOrderTaskEntity
     * @param taskItemEntityList
     * @param stockinQty
     * @param request
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void overShipment(StockinOrderTaskEntity stockinOrderTaskEntity, List<StockinOrderTaskItemEntity> taskItemEntityList, int stockinQty, StockinSupplierDeliveryOrderDirectShelveEditRequest request) {

        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            if (request.getReturnedQty() != 0)
                throw new BusinessServiceException("新增sku不允许增加退货数！");
            // case2：增加出库明细记录  采购单存在sku，但不在当前工厂出库单下  供应链新增一条出库明细，wms新增出库明细
            StockinOrderTaskItemEntity taskItemEntity = stockinOrderTaskItemService.findTopByTaskId(stockinOrderTaskEntity.getTaskId());
            DeliveryOverShipmentRequest shipmentRequest = new DeliveryOverShipmentRequest();
            shipmentRequest.setOrderNo(taskItemEntity.getPurchasePlanNo());
            shipmentRequest.setSku(request.getSku());
            shipmentRequest.setSupplierDeliveryBoxNo(stockinOrderTaskEntity.getSupplierDeliveryBoxCode());
            shipmentRequest.setSupplierDeliveryNo(stockinOrderTaskEntity.getSupplierDeliveryNo());
            shipmentRequest.setTotalReceivedQty(request.getShelvedQty());
            DeliveryOverShipmentResponse deliveryOverShipmentResponse = gcApiService.deliveryOverShipment(shipmentRequest);
            if (deliveryOverShipmentResponse.getSyncType() == 3)
                throw new BusinessServiceException(String.format("sku【%s】不存在待收货数", request.getSku()));
            if (deliveryOverShipmentResponse.getQty() <= 0) {
                createOtherOrderByNewSku(stockinOrderTaskEntity, deliveryOverShipmentResponse, request.getShelvedQty(), request.getPositionCode());
                request.setShelvedQty(0);
            } else {
                createNewTaskItem(stockinOrderTaskEntity, request, deliveryOverShipmentResponse, taskItemEntity);
            }

            return;
        }

        if (taskItemEntityList.size() > 1)
            throw new BusinessServiceException("sku【%s】入库任务明细超过一条，不允许多收操作");

        StockinOrderTaskItemEntity taskItemEntity = taskItemEntityList.get(0);
        DeliveryOverShipmentRequest shipmentRequest = new DeliveryOverShipmentRequest();
        shipmentRequest.setOrderNo(taskItemEntity.getPurchasePlanNo());
        shipmentRequest.setSku(taskItemEntity.getSku());
        shipmentRequest.setSupplierDeliveryBoxNo(stockinOrderTaskEntity.getSupplierDeliveryBoxCode());
        shipmentRequest.setSupplierDeliveryNo(stockinOrderTaskEntity.getSupplierDeliveryNo());
        shipmentRequest.setTotalReceivedQty(stockinQty);
        DeliveryOverShipmentResponse deliveryOverShipmentResponse = gcApiService.deliveryOverShipment(shipmentRequest);

        // case1：回填【出库数】    采购单待发货 大于 该工厂出库单计划数， 供应链系统扣减待发货数，wms系统增加计划入库数
        Integer expectedQty = taskItemEntity.getExpectedQty();
        if (deliveryOverShipmentResponse.getSyncType().equals(1)
            && expectedQty < deliveryOverShipmentResponse.getQty()
            && taskItemEntity.getPurchasePlanNo().equals(deliveryOverShipmentResponse.getPurchasePlanNo())) {

            taskItemEntity.setExpectedQty(deliveryOverShipmentResponse.getQty());
            StockinOrderTaskItemEntity itemEntity = new StockinOrderTaskItemEntity();
            itemEntity.setExpectedQty(deliveryOverShipmentResponse.getQty());
            itemEntity.setTaskItemId(taskItemEntity.getTaskItemId());
            itemEntity.setUpdateBy(loginInfoService.getName());
            stockinOrderTaskItemService.saveOrUpdate(taskItemEntity);

            stockinOrderTaskEntity.setExpectedQty(stockinOrderTaskService.getBaseMapper().sumExpectedQty(stockinOrderTaskEntity.getTaskId()));
            stockinOrderTaskService.updateById(stockinOrderTaskEntity);

            //记录入库单日志
            String content = String.format("工厂出库单详情编辑【%s】已上架数 %s 件,修改计划收货数 %s -> %s", request.getSku(), request.getShelvedQty(), expectedQty, deliveryOverShipmentResponse.getQty());
            stockinScanLogService.addScanLog(content, stockinOrderTaskEntity, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_RECEIVING_EDIT);
            //同步商通
            StockPlatformScheduleItemEntity stockPlatformScheduleItemEntity = stockPlatformScheduleItemService.findBySupplierDeliveryBoxCodeAndPurchasePlanNoAndSku(stockinOrderTaskEntity.getSupplierDeliveryBoxCode(), taskItemEntity.getPurchasePlanNo(), taskItemEntity.getSku());
            stockPlatformScheduleItemEntity.setQty(deliveryOverShipmentResponse.getQty());
            stockPlatformScheduleItemService.updateById(stockPlatformScheduleItemEntity);
            syncErp(stockinOrderTaskEntity, deliveryOverShipmentResponse.getSyncType(), stockPlatformScheduleItemEntity);
        }

        //  case3： 收货数超出采购单待发货总数，判定为多收，生成【其他入库类型】的入库单，补月台任务明细、入库任务、调用商通补接收单
        int overQty = stockinQty - deliveryOverShipmentResponse.getQty();
        if (overQty > 0) {

            // 生成新的月台任务明细、入库任务、入库单
            createOtherOrder(stockinOrderTaskEntity, taskItemEntity, overQty, request.getPositionCode());
            request.setShelvedQty(request.getShelvedQty() - overQty);
        }


    }

    private void createNewTaskItem(StockinOrderTaskEntity stockinOrderTaskEntity, StockinSupplierDeliveryOrderDirectShelveEditRequest request, DeliveryOverShipmentResponse deliveryOverShipmentResponse, StockinOrderTaskItemEntity taskItemEntity) {
        //生成月台任务明细
        StockPlatformScheduleItemEntity platformScheduleItemEntity = stockPlatformScheduleItemService.findBySupplierDeliveryBoxCodeAndPurchasePlanNoAndSku(stockinOrderTaskEntity.getSupplierDeliveryBoxCode(), taskItemEntity.getPurchasePlanNo(), taskItemEntity.getSku());
        StockPlatformScheduleItemEntity newScheduleItemEntity = new StockPlatformScheduleItemEntity();
        BeanUtilsEx.copyProperties(platformScheduleItemEntity, newScheduleItemEntity, "platformScheduleItemId", "createDate", "updateBy", "updateDate", "version");
        BeanUtilsEx.copyProperties(deliveryOverShipmentResponse, newScheduleItemEntity);

        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(request.getSku());
        newScheduleItemEntity.setSku(productSpecInfoEntity.getSku());
        newScheduleItemEntity.setSpecId(productSpecInfoEntity.getSpecId());
        newScheduleItemEntity.setCreateBy(loginInfoService.getName());
        stockPlatformScheduleItemService.save(newScheduleItemEntity);

        //生成入库任务明细
        createTaskItemEntity(stockinOrderTaskEntity, newScheduleItemEntity);

        //同步商通
        syncErp(stockinOrderTaskEntity, deliveryOverShipmentResponse.getSyncType(), newScheduleItemEntity);
    }

    private void syncErp(StockinOrderTaskEntity stockinOrderTaskEntity, Integer syncType, StockPlatformScheduleItemEntity newScheduleItemEntity) {
        SyncSupplierDeliveryDetailRequest syncSupplierDeliveryDetailRequest = new SyncSupplierDeliveryDetailRequest();
        syncSupplierDeliveryDetailRequest.setSupplierDeliveryNo(stockinOrderTaskEntity.getSupplierDeliveryNo());
        syncSupplierDeliveryDetailRequest.setSupplierDeliveryBoxCode(newScheduleItemEntity.getSupplierDeliveryBoxCode());
        syncSupplierDeliveryDetailRequest.setDeliveryQty(newScheduleItemEntity.getQty());
        syncSupplierDeliveryDetailRequest.setSpaceName(newScheduleItemEntity.getSpaceName());
        syncSupplierDeliveryDetailRequest.setOperator(loginInfoService.getName());
        syncSupplierDeliveryDetailRequest.setPurchaseOrderPlanNo(newScheduleItemEntity.getPurchasePlanNo());
        syncSupplierDeliveryDetailRequest.setSku(newScheduleItemEntity.getSku());
        syncSupplierDeliveryDetailRequest.setSyncType(syncType);
        erpStockinApiService.syncSupplierDeliveryDetail(syncSupplierDeliveryDetailRequest);
    }


    // 生成新的月台任务明细、入库任务、入库单
    private void createOtherOrderByNewSku(StockinOrderTaskEntity stockinOrderTaskEntity, DeliveryOverShipmentResponse response, int overQty, String positionCode) {
        String supplierDeliveryBoxCode = stockinOrderTaskEntity.getSupplierDeliveryNo() + "-" + StockConstant.STOCKIN_ORDER_NO_DFRK;
        //生成月台任务明细
        StockPlatformScheduleItemEntity newScheduleItemEntity = new StockPlatformScheduleItemEntity();
        BeanUtilsEx.copyProperties(response, newScheduleItemEntity);
        newScheduleItemEntity.setLocation(TenantContext.getTenant());
        newScheduleItemEntity.setPlatformScheduleId(stockinOrderTaskEntity.getPlatformScheduleId());
        newScheduleItemEntity.setSupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        newScheduleItemEntity.setSupplierDeliveryBarcode(supplierDeliveryBoxCode);
        newScheduleItemEntity.setCreateBy(loginInfoService.getName());
        newScheduleItemEntity.setUpdateBy(loginInfoService.getName());

        newScheduleItemEntity.setQty(overQty);


        BdErpSpaceMappingEntity entityByErpSpaceId = bdErpSpaceMappingService.getEntityByErpSpaceId(response.getSpaceId());

        newScheduleItemEntity.setSpaceId(entityByErpSpaceId.getSpaceId());
        newScheduleItemEntity.setSpaceName(entityByErpSpaceId.getSpaceName());
        newScheduleItemEntity.setAreaName(entityByErpSpaceId.getAreaName());

        ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(response.getSku());
        newScheduleItemEntity.setProductId(productSpecInfo.getProductId());
        newScheduleItemEntity.setSpecId(productSpecInfo.getSpecId());
        newScheduleItemEntity.setBarcode(productSpecInfo.getBarcode());
        stockPlatformScheduleItemService.save(newScheduleItemEntity);

        //生成入库任务
        StockinOrderTaskEntity orderTaskEntity = createStockinOrderTask(stockinOrderTaskEntity, supplierDeliveryBoxCode);
        //生成入库任务明细
        createTaskItemEntity(orderTaskEntity, newScheduleItemEntity);

        syncErp(orderTaskEntity, 2, newScheduleItemEntity);

        //生成入库单
        stockinOrderService.create(orderTaskEntity, FormNoTypeEnum.STOCKIN_ORDER_NO_DFRK);


        //增加上架数
        addShelvedQty(positionCode, response.getSku(), orderTaskEntity, overQty);
    }

    // 生成新的月台任务明细、入库任务、入库单
    private void createOtherOrder(StockinOrderTaskEntity stockinOrderTaskEntity, StockinOrderTaskItemEntity taskItemEntity, int overQty, String positionCode) {
        String supplierDeliveryBoxCode = stockinOrderTaskEntity.getSupplierDeliveryNo() + "-" + StockConstant.STOCKIN_ORDER_NO_DFRK;
        //查询明细是否已经生成
        StockPlatformScheduleItemEntity newScheduleItemEntity = stockPlatformScheduleItemService.findBySupplierDeliveryBoxCodeAndPurchasePlanNoAndSku(supplierDeliveryBoxCode, taskItemEntity.getPurchasePlanNo(), taskItemEntity.getSku());
        //是否首次新增明细
        boolean isFirst = false;
        if (Objects.isNull(newScheduleItemEntity)) {
            //生成月台任务明细
            StockPlatformScheduleItemEntity platformScheduleItemEntity = stockPlatformScheduleItemService.findBySupplierDeliveryBoxCodeAndPurchasePlanNoAndSku(stockinOrderTaskEntity.getSupplierDeliveryBoxCode(), taskItemEntity.getPurchasePlanNo(), taskItemEntity.getSku());
            newScheduleItemEntity = new StockPlatformScheduleItemEntity();
            BeanUtilsEx.copyProperties(platformScheduleItemEntity, newScheduleItemEntity, "platformScheduleItemId", "createDate", "updateBy", "updateDate", "version");
            newScheduleItemEntity.setSupplierDeliveryBoxCode(supplierDeliveryBoxCode);
            newScheduleItemEntity.setSupplierDeliveryBarcode(supplierDeliveryBoxCode);
            newScheduleItemEntity.setCreateBy(loginInfoService.getName());
            newScheduleItemEntity.setQty(overQty);
            StockPlatformScheduleItemEntity stockPlatformScheduleItemEntity = stockPlatformScheduleItemService.findBySupplierDeliveryBoxCodeAndPurchasePlanNoAndSku(stockinOrderTaskEntity.getSupplierDeliveryBoxCode(), taskItemEntity.getPurchasePlanNo(), taskItemEntity.getSku());

            newScheduleItemEntity.setSpaceId(stockPlatformScheduleItemEntity.getSpaceId());
            newScheduleItemEntity.setSpaceName(stockPlatformScheduleItemEntity.getSpaceName());
            newScheduleItemEntity.setAreaName(stockPlatformScheduleItemEntity.getAreaName());
            stockPlatformScheduleItemService.save(newScheduleItemEntity);
            isFirst = true;
        } else {
            newScheduleItemEntity.setQty(newScheduleItemEntity.getQty() + overQty);
            newScheduleItemEntity.setUpdateBy(loginInfoService.getName());
            stockPlatformScheduleItemService.updateById(newScheduleItemEntity);
        }

        //生成入库任务
        StockinOrderTaskEntity orderTaskEntity = createStockinOrderTask(stockinOrderTaskEntity, supplierDeliveryBoxCode);
        //生成入库任务明细
        createTaskItemEntity(orderTaskEntity, newScheduleItemEntity);

        //操作类型：1：回填【出库数】，2：增加出库明细记录
        //每个sku的首次调用为2   第二次调用为1
        int syncType = isFirst ? 2 : 1;
        syncErp(orderTaskEntity, syncType, newScheduleItemEntity);

        //生成入库单
        stockinOrderService.create(orderTaskEntity, FormNoTypeEnum.STOCKIN_ORDER_NO_DFRK);


        //增加上架数
        addShelvedQty(positionCode, taskItemEntity.getSku(), orderTaskEntity, overQty);
    }

    private StockinOrderTaskEntity createStockinOrderTask(StockinOrderTaskEntity stockinOrderTaskEntity, String supplierDeliveryBoxCode) {
        StockinOrderTaskEntity orderTaskEntity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (Objects.nonNull(orderTaskEntity))
            return orderTaskEntity;
        StockinOrderTaskEntity newOrderTaskEntity = new StockinOrderTaskEntity();
        BeanUtilsEx.copyProperties(stockinOrderTaskEntity, newOrderTaskEntity, "taskId", "createDate", "updateBy", "updateDate", "version");
        newOrderTaskEntity.setCreateBy(loginInfoService.getName());
        newOrderTaskEntity.setSupplierDeliveryBarcode(supplierDeliveryBoxCode);
        newOrderTaskEntity.setSupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        newOrderTaskEntity.setStockinType(StockinTypeEnum.OVER.name());
        stockinOrderTaskService.save(newOrderTaskEntity);
        //记录日志
        String content = String.format("工厂出库单【%s】多收，创建其他类型的入库任务", stockinOrderTaskEntity.getSupplierDeliveryNo());
        stockinScanLogService.addScanLog(content, newOrderTaskEntity, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_CREATE);
        return newOrderTaskEntity;
    }

    private StockinOrderTaskItemEntity createTaskItemEntity(StockinOrderTaskEntity stockinOrderTaskEntity, StockPlatformScheduleItemEntity newScheduleItemEntity) {
        StockinOrderTaskItemEntity stockinOrderTaskItemEntity = stockinOrderTaskItemService.findTopOneByTaskIdAndSpecIdAndPurchasePlanNo(stockinOrderTaskEntity.getTaskId(), newScheduleItemEntity.getSpecId(), newScheduleItemEntity.getPurchasePlanNo());
        if (Objects.isNull(stockinOrderTaskItemEntity)) {
            stockinOrderTaskItemEntity = new StockinOrderTaskItemEntity();
            BeanUtilsEx.copyProperties(newScheduleItemEntity, stockinOrderTaskItemEntity, "taskId", "createDate", "updateBy", "updateDate", "version");
            stockinOrderTaskItemEntity.setIsNeedQa(0);
            stockinOrderTaskItemEntity.setQaQty(0);
            stockinOrderTaskItemEntity.setCreateBy(loginInfoService.getName());
        }

        stockinOrderTaskItemEntity.setExpectedQty(newScheduleItemEntity.getQty());
        stockinOrderTaskItemEntity.setTaskId(stockinOrderTaskEntity.getTaskId());
        stockinOrderTaskItemService.saveOrUpdate(stockinOrderTaskItemEntity);

        StockinOrderTaskEntity orderTaskEntity = new StockinOrderTaskEntity();
        orderTaskEntity.setTaskId(stockinOrderTaskEntity.getTaskId());
        orderTaskEntity.setExpectedQty(stockinOrderTaskService.getBaseMapper().sumExpectedQty(stockinOrderTaskEntity.getTaskId()));
        stockinOrderTaskService.updateById(orderTaskEntity);

        return stockinOrderTaskItemEntity;
    }

    //2-1 已上架数<当前上架数，扣减库位库存
    private void reduceShelvedQty(StockinOrderTaskEntity stockinOrderTaskEntity, StockinOrderEntity stockinOrderEntity, List<StockinOrderItemEntity> orderItemEntityList, int qty) {
        int reduceQty = qty;
        String sku = orderItemEntityList.get(0).getSku();
        // 查询入库单上架库位
        List<String> positionCodeList = stockinSupplierDeliveryOrderMapper.findShelvePositionByStockOrderIdAndSku(stockinOrderEntity.getStockinOrderId(), sku);
        if (CollectionUtils.isEmpty(positionCodeList))
            throw new BusinessServiceException("未找到上架库位!");
        List<StockSkuPositionInfo> infoList = stockService.stockSkuPositionInfo(Lists.newArrayList(orderItemEntityList.get(0).getSpecId()), null, null, positionCodeList)
            .stream().filter(item -> item.getStock() > 0).collect(Collectors.toList());
        int stock = infoList.stream().mapToInt(StockSkuPositionInfo::getStock).sum();
        if (reduceQty > stock)
            throw new BusinessServiceException("可扣减的库存不足!");

        //判断预占，若有预占不允许扣减
        Integer skuPreQty = stockPrematchInfoService.getSkuPreQty(sku, positionCodeList);
        if (reduceQty > (stock - skuPreQty))
            throw new BusinessServiceException("库存已预配，剩余可扣减的库存不足!");

        LinkedList<ErpFeedbackReceivedNewRequest> receivedNewRequests = new LinkedList<>();
        List<StockinOrderItemEntity> orderItemEntities = new LinkedList<>();
        for (StockinOrderItemEntity orderItem : orderItemEntityList) {
            int minusQty = Math.min(reduceQty, orderItem.getShelvedQty());
            if (minusQty == 0)
                continue;
            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
            orderItemEntity.setStockinOrderItemId(orderItem.getStockinOrderItemId());
            orderItemEntity.setQty(orderItem.getQty() - minusQty);
            orderItem.setQty(orderItem.getQty() - minusQty);
            orderItemEntity.setShelvedQty(orderItem.getShelvedQty() - minusQty);
            orderItem.setShelvedQty(orderItem.getShelvedQty() - minusQty);
            orderItemEntity.setUpdateBy(loginInfoService.getName());
            orderItemEntities.add(orderItemEntity);

            receivedNewRequests.add(buildReceivedNewRequest(stockinOrderTaskEntity, orderItem, minusQty, infoList));

            StockinOrderTaskItemEntity stockinOrderTaskItemEntity = stockinOrderTaskItemService.getById(orderItem.getTaskItemId());
            StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
            taskItemEntity.setTaskItemId(stockinOrderTaskItemEntity.getTaskItemId());
            taskItemEntity.setStockinQty(Math.max(stockinOrderTaskItemEntity.getStockinQty() - minusQty, 0));
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            stockinOrderTaskItemService.updateById(taskItemEntity);

            reduceQty -= minusQty;
            if (reduceQty <= 0)
                break;
        }
        if (!CollectionUtils.isEmpty(orderItemEntities))
            stockinOrderItemService.updateBatchById(orderItemEntities);

        int reduceStockQty = qty;
        List<StockUpdateRequest> stockUpdateRequestList = new LinkedList<>();
        for (StockSkuPositionInfo skuPositionInfo : infoList) {
            int minusQty = Math.min(reduceStockQty, skuPositionInfo.getStock());
            if (minusQty == 0)
                continue;
            StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
            stockUpdateRequest.setQty(-minusQty);
            stockUpdateRequest.setContent(String.format("工厂出库单【%s】编辑商品【%s】，库位【%s】减少%s件",
                stockinOrderTaskEntity.getSupplierDeliveryNo(), skuPositionInfo.getSku(), skuPositionInfo.getPositionCode(), minusQty));
            stockUpdateRequest.setSku(sku);
            stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.SHELVE_ADJUST);
            stockUpdateRequest.setPositionCode(skuPositionInfo.getPositionCode());
            stockUpdateRequest.setStockInOrderNo(stockinOrderEntity.getStockinOrderNo());
            stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
            stockUpdateRequestList.add(stockUpdateRequest);

            reduceStockQty -= minusQty;
            if (reduceStockQty <= 0)
                break;
        }
        if (!CollectionUtils.isEmpty(stockUpdateRequestList))
            stockService.updateStockBatch(stockUpdateRequestList);

        //上架反馈
        receivedNewRequests.forEach(requestItem -> erpApiService.newFeedbackReceived(requestItem));

    }

    private ErpFeedbackReceivedNewRequest buildReceivedNewRequest(StockinOrderTaskEntity stockinOrderTaskEntity, StockinOrderItemEntity orderItem, int minusQty, List<StockSkuPositionInfo> infoList) {
        ErpFeedbackReceivedNewRequest request = new ErpFeedbackReceivedNewRequest();
        request.setIsAllot(0);
        request.setIsNegativeUpShelf(1);
        request.setLocation(stockinOrderTaskEntity.getLocation());
        request.setOperator(loginInfoService.getName());
        // 如果上架到同一个区域，这里会被转成统一库位
        // todo 但如果是活动仓，上架多个库位的情况， 这里会有问题  ， 暂不处理，要求操作人员上架到一个库位上
        request.setPositionCode(stockCenterCommonService.changePositionCode(infoList.get(0).getPositionCode()));
        request.setPurchasePlanNo(orderItem.getPurchasePlanNo());
        request.setReceiveDate(orderItem.getCreateDate());
        request.setShelvedQty(-minusQty);
        request.setShelfTime(new Date());
        request.setStockinOrderItemId(orderItem.getStockinOrderItemId());
        request.setSupplierDeliveryBoxCode(stockinOrderTaskEntity.getSupplierDeliveryBoxCode());
        request.setSku(orderItem.getSku());
        return request;
    }

    //2-2 已上架数>当前上架数，增加库位库存
    private void addShelvedQty(String positionCode, String sku, StockinOrderTaskEntity stockinOrderTaskEntity, int qty) {

        if (!StringUtils.hasText(positionCode))
            throw new BusinessServiceException("增加已上架数请填写上架库位！");
        StockinSupplierDeliveryOrderDirectShelveRequest shelveRequest = new StockinSupplierDeliveryOrderDirectShelveRequest();
        shelveRequest.setSupplierDeliveryNo(stockinOrderTaskEntity.getSupplierDeliveryNo());
        StockinSupplierDeliveryOrderDirectShelveItemRequest itemRequest = new StockinSupplierDeliveryOrderDirectShelveItemRequest();
        itemRequest.setPositionCode(positionCode);
        itemRequest.setQty(qty);
        itemRequest.setReturnQty(0);
        itemRequest.setSku(sku);
        itemRequest.setSupplierDeliveryBoxCode(stockinOrderTaskEntity.getSupplierDeliveryBoxCode());
        shelveRequest.setItemList(Lists.newArrayList(itemRequest));
        stockinSupplierDeliveryOrderService.directShelve(shelveRequest);

    }


    //2-3 已退货数<当前退货数，扣减退货库位库存
    private void reduceReuturnQty(StockinOrderTaskEntity stockinOrderTaskEntity, StockinOrderEntity stockinOrderEntity, List<StockinOrderItemEntity> orderItemEntityList, int qty) {
        int reduceQty = qty;
        String sku = orderItemEntityList.get(0).getSku();

        //校验退货库位库存
        List<StockinReturnProductEntity> returnProductEntityList = stockinReturnProductService.list(new LambdaQueryWrapper<StockinReturnProductEntity>()
            .eq(StockinReturnProductEntity::getStockinOrderNo, stockinOrderEntity.getStockinOrderNo())
            .eq(StockinReturnProductEntity::getSupplierId, stockinOrderEntity.getSupplierId())
            .eq(StockinReturnProductEntity::getSku, sku));
        int returnStock = returnProductEntityList.stream().mapToInt(StockinReturnProductEntity::getReturnQty).sum();
        if (reduceQty > returnStock)
            throw new BusinessServiceException("退货库位库存不足，请确认！");

        Map<String, Integer> purchasePlanNoMap = new HashMap<>(8);
        List<StockinOrderItemEntity> orderItemEntities = new LinkedList<>();
        for (StockinOrderItemEntity orderItem : orderItemEntityList) {
            int minusQty = Math.min(reduceQty, orderItem.getReturnQty());
            if (minusQty == 0)
                continue;
            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
            orderItemEntity.setStockinOrderItemId(orderItem.getStockinOrderItemId());
            orderItemEntity.setQty(orderItem.getQty() - minusQty);
            orderItem.setQty(orderItem.getQty() - minusQty);
            orderItemEntity.setReturnQty(orderItem.getReturnQty() - minusQty);
            orderItem.setReturnQty(orderItem.getReturnQty() - minusQty);
            orderItemEntity.setUpdateBy(loginInfoService.getName());
            orderItemEntities.add(orderItemEntity);

            StockinOrderTaskItemEntity stockinOrderTaskItemEntity = stockinOrderTaskItemService.getById(orderItem.getTaskItemId());
            StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
            taskItemEntity.setTaskItemId(stockinOrderTaskItemEntity.getTaskItemId());
            taskItemEntity.setStockinQty(Math.max(stockinOrderTaskItemEntity.getStockinQty() - minusQty, 0));
            taskItemEntity.setStockinReturnQty(Math.max(stockinOrderTaskItemEntity.getStockinReturnQty() - minusQty, 0));
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            stockinOrderTaskItemService.updateById(taskItemEntity);


            Integer purchasePlanNoQty = purchasePlanNoMap.getOrDefault(orderItem.getPurchasePlanNo(), 0);
            purchasePlanNoMap.put(orderItem.getPurchasePlanNo(), purchasePlanNoQty + minusQty);

            reduceQty -= minusQty;
            if (reduceQty <= 0)
                break;
        }
        if (!CollectionUtils.isEmpty(orderItemEntities))
            stockinOrderItemService.updateBatchById(orderItemEntities);
        if (!CollectionUtils.isEmpty(purchasePlanNoMap)) {
            // 扣除退货库位库存
            purchasePlanNoMap.forEach((purchasePlanNo, value) -> {
                int leftQty = value;
                List<StockinReturnProductEntity> returnProductEntities = returnProductEntityList.stream().filter(returnEntity -> purchasePlanNo.equals(returnEntity.getPurchasePlanNo())).collect(Collectors.toList());
                for (StockinReturnProductEntity returnProductEntity : returnProductEntities) {
                    if (leftQty >= returnProductEntity.getReturnQty()) {
                        leftQty = leftQty - returnProductEntity.getReturnQty();
                        stockinReturnProductService.removeById(returnProductEntity.getReturnProductId());
                    } else {
                        returnProductEntity.setReturnQty(returnProductEntity.getReturnQty() - leftQty);
                        stockinReturnProductService.updateById(returnProductEntity);
                        leftQty = 0;
                    }
                    if (leftQty <= 0)
                        break;
                }
                StockinReturnProductAddRequest returnProductAddRequest = new StockinReturnProductAddRequest();
                returnProductAddRequest.setQaType(StockChangeLogTypeEnum.SHELVE_ADJUST);
                returnProductAddRequest.setSupplierDeliveryNo(stockinOrderTaskEntity.getSupplierDeliveryNo());
                returnProductAddRequest.setReceiveOrderNo(stockinOrderEntity.getSupplierDeliveryBoxCode());
                returnProductAddRequest.setOperator(loginInfoService.getName());
                returnProductAddRequest.setPurchasePlanNo(purchasePlanNo);
                returnProductAddRequest.setSku(sku);
                returnProductAddRequest.setStockinOrderNo(stockinOrderEntity.getStockinOrderNo());
                returnProductAddRequest.setSupplierId(stockinOrderEntity.getSupplierId());
                returnProductAddRequest.setSupplierName(stockinOrderEntity.getSupplierName());
                returnProductAddRequest.setReturnQty(-value);
                stockinReturnProductService.alterReturnProduct(returnProductAddRequest, Boolean.FALSE);

            });
        }

    }


    //2-4 已退货数>当前退货数，新增退货库位库存
    private void addReturnQty(StockinOrderTaskEntity stockinOrderTaskEntity, StockinOrderEntity stockinOrderEntity, String sku, int qty) {
        int editQty = qty;

        List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(stockinOrderTaskEntity.getTaskId(), sku);
        List<StockinOrderItemEntity> orderItemEntityList = stockinOrderItemService.findAllByStockinOrderIdAndSku(stockinOrderEntity.getStockinOrderId(), sku);
        // 拿出一个已使用的箱子
        String internalBoxCode = orderItemEntityList.stream().filter(item -> StringUtils.hasText(item.getInternalBoxCode()))
            .map(StockinOrderItemEntity::getInternalBoxCode).findAny().orElseThrow(() -> new BusinessServiceException("请先点击上架"));

        Map<String, Integer> purchasePlanNoMap = new HashMap<>(8);
        List<StockinOrderTaskItemEntity> taskItemEntityLinkedList = new LinkedList<>();
        List<StockinOrderItemEntity> orderItemEntities = new LinkedList<>();
        for (StockinOrderTaskItemEntity stockinOrderTaskItemEntity : taskItemEntityList) {
            int canAddQty = stockinOrderTaskItemEntity.getExpectedQty() - stockinOrderTaskItemEntity.getStockinQty();
            int addQty = Math.min(editQty, canAddQty);
            if (addQty == 0)
                continue;
            StockinOrderItemEntity orderItem = orderItemEntityList.stream().filter(item -> item.getTaskItemId().equals(stockinOrderTaskItemEntity.getTaskItemId())).findAny()
                .orElse(this.createReturnOrderItem(stockinOrderTaskItemEntity, stockinOrderEntity, internalBoxCode));
            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
            orderItemEntity.setStockinOrderItemId(orderItem.getStockinOrderItemId());
            orderItemEntity.setQty(orderItem.getQty() + addQty);
            orderItemEntity.setReturnQty(orderItem.getReturnQty() + addQty);
            orderItemEntity.setUpdateBy(loginInfoService.getName());
            orderItemEntities.add(orderItemEntity);

            StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
            taskItemEntity.setTaskItemId(stockinOrderTaskItemEntity.getTaskItemId());
            taskItemEntity.setStockinQty(stockinOrderTaskItemEntity.getStockinQty() + addQty);
            taskItemEntity.setStockinReturnQty(stockinOrderTaskItemEntity.getStockinReturnQty() + addQty);
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            taskItemEntityLinkedList.add(taskItemEntity);

            Integer purchasePlanNoQty = purchasePlanNoMap.getOrDefault(stockinOrderTaskItemEntity.getPurchasePlanNo(), 0);
            purchasePlanNoMap.put(stockinOrderTaskItemEntity.getPurchasePlanNo(), purchasePlanNoQty + addQty);

            editQty -= addQty;
            if (editQty <= 0)
                break;
        }
        if (!CollectionUtils.isEmpty(taskItemEntityLinkedList))
            stockinOrderTaskItemService.updateBatchById(taskItemEntityLinkedList);
        if (!CollectionUtils.isEmpty(orderItemEntities))
            stockinOrderItemService.updateBatchById(orderItemEntities);
        if (!CollectionUtils.isEmpty(purchasePlanNoMap)) {
            purchasePlanNoMap.forEach((purchasePlanNo, value) -> {
                StockinReturnProductAddRequest returnProductAddRequest = new StockinReturnProductAddRequest();
                returnProductAddRequest.setQaType(StockChangeLogTypeEnum.SHELVE_ADJUST);
                returnProductAddRequest.setSupplierDeliveryNo(stockinOrderTaskEntity.getSupplierDeliveryNo());
                returnProductAddRequest.setReceiveOrderNo(stockinOrderEntity.getSupplierDeliveryBoxCode());
                returnProductAddRequest.setOperator(loginInfoService.getName());
                returnProductAddRequest.setPurchasePlanNo(purchasePlanNo);
                returnProductAddRequest.setSku(sku);
                returnProductAddRequest.setStockinOrderNo(stockinOrderEntity.getStockinOrderNo());
                returnProductAddRequest.setSupplierId(stockinOrderEntity.getSupplierId());
                returnProductAddRequest.setSupplierName(stockinOrderEntity.getSupplierName());
                returnProductAddRequest.setReturnQty(value);
                stockinReturnProductService.alterReturnProduct(returnProductAddRequest, Boolean.FALSE);
            });
        }
    }

    public StockinOrderItemEntity createReturnOrderItem(StockinOrderTaskItemEntity taskItemEntity, StockinOrderEntity orderEntity, String internalBoxCode) {
        StockinOrderItemEntity entity = new StockinOrderItemEntity();
        BeanUtilsEx.copyProperties(taskItemEntity, entity, "createDate", "createBy", "updateDate", "updateBy", "version");
        entity.setStockinOrderId(orderEntity.getStockinOrderId());
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        //创建新明细数据时已推送数量为0
        entity.setPushedQty(0);
        entity.setLocation(TenantContext.getTenant());

        entity.setInternalBoxCode(internalBoxCode);
        entity.setQty(0);
        if (orderEntity.getOrderType().equals(1)) {
            entity.setBranchPurchasePlanNo("V" + taskItemEntity.getPurchasePlanNo());
        }
        entity.setReturnQty(0);
        entity.setShelvedQty(0);
        entity.setBrandName(taskItemEntity.getBrandName());
        entity.setStatus(StockinOrderItemStatusEnum.WAIT_SHELVE.name());
        stockinOrderItemService.save(entity);
        return entity;
    }


    /**
     * 确认完结工厂出库单
     * 1.完结入库任务、月台任务
     * 2.完结入库单（需判断是否进入待核对）
     * 3.上架完成反馈商通
     *
     * @param request
     */
    @Transactional
    public void complete(StringListRequest request) {

        for (String supplierDeliveryNo : request.getStringList()) {
            //1.完结入库任务、月台任务
            stockinDeliveryPdaService.modifyReceiveInfo(supplierDeliveryNo, Boolean.TRUE);
            //删除入库数为0的入库单明细
            List<Integer> integerList = stockinOrderItemService.getBaseMapper().selectZeroQtyItemIdBySupplierDeliveryNo(supplierDeliveryNo);
            stockinOrderItemService.removeByIds(integerList);

            //2.完结入库单（需判断是否进入待核对）  3.上架完成反馈商通
            List<Integer> stockinOrderIds = stockinOrderService.getBaseMapper().findIdBySupplierDeliveryNo(supplierDeliveryNo);
            stockinShelveTaskService.updateStockinOrder(stockinOrderIds, true);
        }

    }

    /**
     * 获取可增加的数量
     *
     * @param supplierDeliveryNo
     * @return
     */
    public List<StockinSupplierDeliveryOrderItemResponse> getAddSku(String supplierDeliveryNo) {
        StockinOrderTaskEntity stockinOrderTaskEntity = stockinOrderTaskService.getOne(new LambdaQueryWrapper<StockinOrderTaskEntity>()
            .eq(StockinOrderTaskEntity::getSupplierDeliveryNo, supplierDeliveryNo)
            .last("limit 1"));
        StockinOrderTaskItemEntity taskItemEntity = stockinOrderTaskItemService.findTopByTaskId(stockinOrderTaskEntity.getTaskId());
        List<String> skuList = gcApiService.getDeliverySkuList(taskItemEntity.getPurchasePlanNo());
        if (CollectionUtils.isEmpty(skuList))
            return Collections.emptyList();
        List<String> existsSkuList = stockinOrderTaskItemService.getBaseMapper().findSkuListByPurchasePlanNo(taskItemEntity.getPurchasePlanNo());
        List<String> collect = skuList.stream().filter(sku -> !existsSkuList.contains(sku)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect))
            return Collections.emptyList();
        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(collect).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));

        List<StockinSupplierDeliveryOrderItemSkuListResponse> itemSkuListResponseList = collect.stream().map(sku -> {
            StockinSupplierDeliveryOrderItemSkuListResponse skuListResponse = new StockinSupplierDeliveryOrderItemSkuListResponse();
            skuListResponse.setCanEdit(Boolean.TRUE);
            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(sku);
            skuListResponse.setExpectedQty(0);
            skuListResponse.setShelvedQty(0);
            skuListResponse.setReturnQty(0);
            skuListResponse.setSpecId(productSpecInfoEntity.getSpecId());
            skuListResponse.setSku(productSpecInfoEntity.getSku());
            skuListResponse.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
            skuListResponse.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            skuListResponse.setColor(productSpecInfoEntity.getColor());
            skuListResponse.setBarcode(productSpecInfoEntity.getBarcode());
            skuListResponse.setSupplierDeliveryBoxCode(stockinOrderTaskEntity.getSupplierDeliveryBoxCode());
            skuListResponse.setSize(productSpecInfoEntity.getSize());
            skuListResponse.setSkc(productSpecInfoEntity.getSkc());
            return skuListResponse;
        }).collect(Collectors.toList());

        return itemSkuListResponseList.stream().collect(Collectors.groupingBy(StockinSupplierDeliveryOrderItemSkuListResponse::getSkc))
            .entrySet().stream().map(entry -> {
                StockinSupplierDeliveryOrderItemResponse orderItemResponse = new StockinSupplierDeliveryOrderItemResponse();
                orderItemResponse.setItemList(entry.getValue());
                orderItemResponse.setSkc(entry.getKey());
                return orderItemResponse;
            }).collect(Collectors.toList());

    }
}
