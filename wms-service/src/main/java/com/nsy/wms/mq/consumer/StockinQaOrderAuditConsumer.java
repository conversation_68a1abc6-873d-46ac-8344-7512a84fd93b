package com.nsy.wms.mq.consumer;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.dto.stockin.StockinQaOrderAuditDTO;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.qa.StockinQaOrderAuditService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.CommonConsumer;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:27
 */
@Component
public class StockinQaOrderAuditConsumer extends CommonConsumer<LocationWrapperMessage> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaOrderAuditConsumer.class);

    @Autowired
    StockinQaOrderAuditService qaOrderAuditService;

    @KafkaListener(topics = KafkaConstant.STOCK_IN_QA_ORDER_AUDIT_TOPIC)
    public void newConsumer(ConsumerRecord<?, ?> record) {
        QMessage<LocationWrapperMessage> receiveMessage = JsonMapper.fromJson(record.value().toString(), QMessage.class);
        processMessage(receiveMessage);
    }

    @Override
    protected void doProcessMessage(QMessage<LocationWrapperMessage> message) {
        LocationWrapperMessage messageContent = JsonMapper.fromJson(JsonMapper.toJson(message.getMessageContent()), LocationWrapperMessage.class);
        TenantContext.setTenant(messageContent.getLocation());
        LoginInfoService.setName(messageContent.getOperator());
        StockinQaOrderAuditDTO qaOrderAuditDTO = JsonMapper.fromJson(JsonMapper.toJson(messageContent.getContent()), StockinQaOrderAuditDTO.class);
        LOGGER.info("StockinQaOrderAuditConsumer receive message: {}, transfer message: {}", JsonMapper.toJson(messageContent.getContent()), JsonMapper.toJson(qaOrderAuditDTO));
        switch (qaOrderAuditDTO.getOperationEnum()) {
            case SUBMIT:
                qaOrderAuditService.startQaOrderWorkFlow(qaOrderAuditDTO.getQaOrderId(), qaOrderAuditDTO.getUserAccount(), qaOrderAuditDTO.getContent());
                break;
            case SUBMIT_TO_END:
                qaOrderAuditService.submitToEnd(qaOrderAuditDTO.getQaOrderId(), qaOrderAuditDTO.isAdmin());
                break;
            case SUBMIT_TO_AUDIT:
                qaOrderAuditService.submitToAudit(qaOrderAuditDTO.getQaOrderId(), qaOrderAuditDTO.isAdmin(), qaOrderAuditDTO.getUserAccount());
                break;
            case AUDIT_TO_END:
                qaOrderAuditService.auditToEnd(qaOrderAuditDTO.getQaOrderId(), qaOrderAuditDTO.isAdmin());
                break;
            case AUDIT_TO_SUBMIT:
                qaOrderAuditService.auditToSubmit(qaOrderAuditDTO.getQaOrderId(), qaOrderAuditDTO.isAdmin());
                break;
            case DELETE:
                qaOrderAuditService.deleteTask(qaOrderAuditDTO.getQaOrderId());
                break;
            default:
                throw new BusinessServiceException("查询不到对应的流程！");

        }
        LoginInfoService.removeName();
    }

}