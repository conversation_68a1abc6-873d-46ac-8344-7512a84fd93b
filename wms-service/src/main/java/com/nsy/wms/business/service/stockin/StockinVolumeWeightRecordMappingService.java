package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockout.VolumeWeightStandardTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.BdVolumeWeightRecordMappingInfo;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingDetailRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSaveRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSkuPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingValidRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinPackageNameResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingExportResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordWaitMeasureResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderWorkmanshipInfoResponse;
import com.nsy.wms.business.service.bd.BdTagMappingService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductStoreSkuMappingEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinVolumeWeightRecordMappingEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinVolumeWeightRecordMappingMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.UnitUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 9:55
 */
@Service
public class StockinVolumeWeightRecordMappingService extends ServiceImpl<StockinVolumeWeightRecordMappingMapper, StockinVolumeWeightRecordMappingEntity> implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinVolumeWeightRecordMappingService.class);

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private StockInternalBoxService stockInternalBoxService;
    @Autowired
    private BdTagMappingService bdTagMappingService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private StockinVolumeWeightRecordMappingLogService logService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ProductStoreSkuMappingService storeSkuMappingService;


    /**
     * 保存信息 --如果存在记录则覆盖
     *
     * @param saveRequest
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveMappingInfo(StockinVolumeWeightRecordMappingSaveRequest saveRequest) {
        if (CollectionUtils.isEmpty(saveRequest.getRecordMappingInfoList())) {
            throw new BusinessServiceException("所选的记录为空!");
        }
        if (!StringUtils.hasText(saveRequest.getSupplierDeliveryNos())) {
            throw new BusinessServiceException("查询不到对应的工厂出库单请检查!");
        }
        List<String> list = Arrays.asList(saveRequest.getSupplierDeliveryNos().split(","));
        saveRequest.setSupplierDeliveryNoList(list.stream().distinct().collect(Collectors.toList()));
        List<Integer> existIdList = this.getBaseMapper().getExistRecordInfo(list, saveRequest.getSku(), saveRequest.getInternalBoxCode());
        if (!CollectionUtils.isEmpty(existIdList)) {
            //重新测量测覆盖历史数据
            this.baseMapper.deleteBatchIds(existIdList);
        }
        List<StockinPackageNameResponse> packageNameList = stockInternalBoxService.getBaseMapper().getPackageNameList(saveRequest.getInternalBoxCode(), saveRequest.getSku());
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(saveRequest.getSku());
        Integer boxQty = stockInternalBoxItemService.getBoxQtyByInternalBoxCodeAndSku(saveRequest.getInternalBoxCode(), saveRequest.getSku());
        List<StockinVolumeWeightRecordMappingEntity> recordMappingEntityList = new ArrayList<>();
        for (BdVolumeWeightRecordMappingInfo recordMappingInfo : saveRequest.getRecordMappingInfoList()) {
            for (String supplierDeliveryNo : saveRequest.getSupplierDeliveryNoList()) {
                StockinVolumeWeightRecordMappingEntity recordMappingEntity = new StockinVolumeWeightRecordMappingEntity();
                recordMappingEntity.setRecordId(recordMappingInfo.getId());
                recordMappingEntity.setSupplierDeliveryNo(supplierDeliveryNo);
                recordMappingEntity.setBarcode(productSpecInfo.getBarcode());
                recordMappingEntity.setSpecId(productSpecInfo.getSpecId());
                recordMappingEntity.setProductId(productSpecInfo.getProductId());
                recordMappingEntity.setSku(productSpecInfo.getSku());
                recordMappingEntity.setInternalBoxCode(saveRequest.getInternalBoxCode());
                recordMappingEntity.setBoxQty(boxQty);
                recordMappingEntity.setIsQualified(saveRequest.getIsQualified());
                recordMappingEntity.setPackageName(packageNameList.get(0).getPackageName());
                //判断是否跨档
                recordMappingEntity.setIsOverStandard(this.validIsOverStandard(recordMappingInfo.getFbaCost(), saveRequest.getFbaCost()));
                recordMappingEntity.setCreateBy(loginInfoService.getName());
                recordMappingEntity.setUpdateBy(loginInfoService.getName());
                recordMappingEntityList.add(recordMappingEntity);
            }
        }
        //保存日志
        for (String supplierDeliveryNo : saveRequest.getSupplierDeliveryNoList()) {
            logService.addLog(supplierDeliveryNo, saveRequest.getInternalBoxCode(), saveRequest.getSku(), "测量完成", "提交测量数据");
        }
        if (CollectionUtils.isEmpty(recordMappingEntityList)) {
            return;
        }
        this.saveBatch(recordMappingEntityList);
    }

    /**
     * 由于开始测量的接口都是复用的接口，不能保存日志，测量与提交需要分开保存日志，故抽出来单独写
     *
     * @param request
     */
    public void saveScanLog(StockinVolumeWeightRecordMappingRequest request) {
        if (!StringUtils.hasText(request.getSupplierDeliveryNo())) {
            return;
        }
        List<String> list = Arrays.asList(request.getSupplierDeliveryNo().split(","));
        request.setSupplierDeliveryNoList(list.stream().distinct().collect(Collectors.toList()));
        if (this.getBaseMapper().countScanInfo(request) == 0) {
            for (String supplierDeliveryNo : request.getSupplierDeliveryNoList()) {
                logService.addLog(supplierDeliveryNo, request.getInternalBoxCode(), request.getSku(), "扫描测量", "扫描商品条码开始测量");
            }
        } else {
            for (String supplierDeliveryNo : request.getSupplierDeliveryNoList()) {
                logService.addLog(supplierDeliveryNo, request.getInternalBoxCode(), request.getSku(), "重新测量", "重新扫描商品条码开始测量");
            }
        }
    }

    /**
     * 校验改工厂出库单是否有测量过
     *
     * @param request
     * @return
     */
    public Boolean validExistRecordMapping(StockinVolumeWeightRecordMappingValidRequest request) {
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(request.getSku());
        if (Objects.isNull(productSpecInfo)) {
            throw new BusinessServiceException("查询不到sku信息!");
        }
        request.setSku(productSpecInfo.getSku());
        return this.baseMapper.countRecordMapping(request) > 0;
    }

    /**
     * 查询测量记录
     *
     * @param request
     * @return
     */
    public List<StockinVolumeWeightRecordMappingResponse> getVolumeWeightRecordMappingList(StockinVolumeWeightRecordMappingRequest request) {
        if (!StringUtils.hasText(request.getSku()) || !StringUtils.hasText(request.getSupplierDeliveryNo())) {
            return Collections.emptyList();
        }
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(request.getSku());
        if (Objects.isNull(productSpecInfo)) {
            throw new BusinessServiceException("查询不到sku信息!");
        }
        request.setSku(productSpecInfo.getSku());
        request.setSupplierDeliveryNoList(Arrays.asList(request.getSupplierDeliveryNo().split(",")));
        List<StockinVolumeWeightRecordMappingResponse> recordMappingEntityList = this.baseMapper.getVolumeWeightRecordMappingList(request);
        if (CollectionUtils.isEmpty(recordMappingEntityList)) {
            return Collections.emptyList();
        }
        for (StockinVolumeWeightRecordMappingResponse detail : recordMappingEntityList) {
            detail.setStandardType(VolumeWeightStandardTypeEnum.of(detail.getStandardType()));
            detail.setChargedWeightKg(UnitUtils.poundsToKilograms(detail.getChargedWeight()));
            detail.setVolumeWeightKg(UnitUtils.poundsToKilograms(detail.getVolumeWeightPound()));
        }
        return recordMappingEntityList;
    }

    /**
     * @param fabCost
     * @param standardCost
     * @return
     */

    private Integer validIsOverStandard(BigDecimal fabCost, BigDecimal standardCost) {
        if (Objects.isNull(fabCost) || Objects.isNull(standardCost)) {
            return 0;
        }
        return fabCost.compareTo(standardCost) > 0 ? 1 : 0;
    }

    public List<StockinVolumeWeightRecordWaitMeasureResponse> getWaitMeasureList(String internalBoxCode) {
        StockInternalBoxEntity boxEntity = stockInternalBoxService.findByInternalBoxCode(internalBoxCode);
        if (Objects.isNull(boxEntity)) {
            throw new BusinessServiceException("找不到内部箱: " + internalBoxCode);
        }
        if (IsDeletedConstant.DELETED.equals(boxEntity.getIsDeleted()))
            throw new BusinessServiceException("箱子已删除");

        List<StockinVolumeWeightRecordWaitMeasureResponse> waitMeasureList = stockInternalBoxService.getWaitMeasureList(internalBoxCode);
        if (CollectionUtils.isEmpty(waitMeasureList))
            return Collections.emptyList();
        Map<String, List<StockinPackageNameResponse>> packageNameMap = new HashMap<>();
        List<StockinPackageNameResponse> getPackageNameList = stockInternalBoxService.getBaseMapper().getPackageNameList(internalBoxCode, null);
        if (!CollectionUtils.isEmpty(getPackageNameList)) {
            packageNameMap = getPackageNameList.stream().collect(Collectors.groupingBy(StockinPackageNameResponse::getSku));
        }
        List<String> skuList = waitMeasureList.stream().map(StockinVolumeWeightRecordWaitMeasureResponse::getSku).collect(Collectors.toList());
        Map<String, List<String>> skuTagMap = bdTagMappingService.getProductTagBySkus(skuList);
        for (StockinVolumeWeightRecordWaitMeasureResponse detail : waitMeasureList) {
            detail.setProductTag(skuTagMap.get(detail.getSku()));
            if (Objects.isNull(packageNameMap.get(detail.getSku()))) {
                continue;
            }
            detail.setPackageName(packageNameMap.get(detail.getSku()).stream()
                    .map(packageName -> packageName.getPackageName() + "(" + packageName.getQty() + ")")
                    .collect(Collectors.joining(",")));
        }
        return waitMeasureList;
    }

    /**
     * 根据sku和包装方式统计信息
     *
     * @param request
     * @return
     */
    public PageResponse<StockinVolumeWeightRecordMappingPageResponse> getPageList(StockinVolumeWeightRecordMappingPageRequest request) {
        Page<StockinVolumeWeightRecordMappingPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<StockinVolumeWeightRecordMappingPageResponse> response = this.getBaseMapper().pageList(page, request);
        PageResponse<StockinVolumeWeightRecordMappingPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        pageResponse.setTotalCount(this.getBaseMapper().getPageCount(request));
        return pageResponse;
    }

    /**
     * 在sku和包装方式条件下分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> getPageSkuList(StockinVolumeWeightRecordMappingSkuPageRequest request) {
        Page<StockinVolumeWeightRecordMappingSkuPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<StockinVolumeWeightRecordMappingSkuPageResponse> response = this.getBaseMapper().pageSkuList(page, request);
        PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        pageResponse.setTotalCount(this.getBaseMapper().getPageSkuCount(request));
        return pageResponse;
    }

    public PageResponse<StockinVolumeWeightRecordMappingPageDetailResponse> getDetailPage(StockinVolumeWeightRecordMappingDetailRequest request) {
        if (!StringUtils.hasText(request.getSku())) throw new BusinessServiceException("传入参数不正确请检查!");
        Page<StockinVolumeWeightRecordMappingPageDetailResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinVolumeWeightRecordMappingPageDetailResponse> response = this.getBaseMapper().pageDetailList(page, request);
        PageResponse<StockinVolumeWeightRecordMappingPageDetailResponse> pageResponse = new PageResponse<>();
        if (CollectionUtils.isEmpty(response.getRecords())) {
            pageResponse.setTotalCount(0L);
            return pageResponse;
        }
        Map<String, StockinVolumeWeightRecordMappingResponse> standardMap = new HashMap<>();
        //赋值标准信息
        response.getRecords().forEach(detail -> {
            //不跳档则自行查询标准信息
            if (Objects.nonNull(detail.getIsOverStandard()) && detail.getIsOverStandard() == 0) {
                return;
            }
            detail.setStandardType(VolumeWeightStandardTypeEnum.of(detail.getStandardType()));
            detail.setChargedWeightKg(UnitUtils.poundsToKilograms(detail.getChargedWeight()));
            detail.setVolumeWeightKg(UnitUtils.poundsToKilograms(detail.getVolumeWeightPound()));
            String standardMapKey = String.format("%s_%s", detail.getSku(), detail.getInternalBoxCode());
            StockinVolumeWeightRecordMappingResponse standardInfo = standardMap.get(standardMapKey);
            if (standardInfo != null) {
                detail.setMeasureStandardInfo(standardInfo);
                return;
            }
            StockinVolumeWeightRecordMappingValidRequest measureStandardRequest = new StockinVolumeWeightRecordMappingValidRequest();
            measureStandardRequest.setSku(detail.getSku());
            measureStandardRequest.setInternalBoxCode(detail.getInternalBoxCode());
            standardMap.put(standardMapKey, this.getMeasureStandard(measureStandardRequest));
            detail.setMeasureStandardInfo(standardMap.get(standardMapKey));
        });
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        return pageResponse;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_VOLUME_WEIGHT_RECORD_MAPPING_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        StockinVolumeWeightRecordMappingPageRequest dateRequest = JsonMapper.fromJson(request.getRequestContent(), StockinVolumeWeightRecordMappingPageRequest.class);
        dateRequest.setPageIndex(request.getPageIndex());
        dateRequest.setPageSize(request.getPageSize());
        PageResponse<StockinVolumeWeightRecordMappingPageResponse> pageResult = this.getPageList(dateRequest);
        DownloadResponse response = new DownloadResponse();
        response.setDataJsonStr(JsonMapper.toJson(pageResult.getContent()));
        response.setTotalCount(pageResult.getTotalCount());
        return response;
    }

    public void buildMeasureStandardInfo(StockinVolumeWeightRecordMappingExportResponse response, Map<String, PurchaseOrderWorkmanshipInfoResponse> measureStandardMap) {
        if (!StringUtils.hasText(response.getPurchasePlanNo()) || !StringUtils.hasText(response.getSku())) {
            return;
        }
        PurchaseOrderWorkmanshipInfoResponse measureStandardInfo = measureStandardMap.get(String.format("%s_%s", response.getPurchasePlanNo(), response.getSku()));
        if (Objects.isNull(measureStandardInfo)) {
            measureStandardInfo = scmApiService.purchaseOrderWorkmanshipInfoForQa(response.getPurchasePlanNo(), response.getSku());
            if (Objects.isNull(measureStandardInfo)) {
                return;
            }
            measureStandardMap.put(String.format("%s_%s", response.getPurchasePlanNo(), response.getSku()), measureStandardInfo);
        }
        //赋值工艺标准信息
        response.setLengthStandard(measureStandardInfo.getPackageLong());
        response.setWidthStandard(measureStandardInfo.getPackageWidth());
        response.setHeightStandard(measureStandardInfo.getPackageHeight());
        response.setWeightStandard(measureStandardInfo.getWeight());
        response.setVolumeWeightKgStandard(UnitUtils.poundsToKilograms(measureStandardInfo.getVolumeWeightPound()));
        response.setLengthInchStandard(measureStandardInfo.getLengthInch());
        response.setWidthInchStandard(measureStandardInfo.getWidthInch());
        response.setHeightInchStandard(measureStandardInfo.getHeightInch());
        response.setWeightPoundStandard(measureStandardInfo.getWeightPound());
        response.setVolumeWeightPoundStandard(measureStandardInfo.getVolumeWeightPound());
        response.setChargedWeightKgStandard(UnitUtils.poundsToKilograms(measureStandardInfo.getChargedWeight()));
        response.setChargedWeightStandard(measureStandardInfo.getChargedWeight());
        response.setFbaCostStandard(measureStandardInfo.getFbaCost());
    }

    public StockinVolumeWeightRecordMappingResponse getMeasureStandard(StockinVolumeWeightRecordMappingValidRequest request) {
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(request.getSku());
        if (Objects.isNull(productSpecInfo)) {
            throw new BusinessServiceException("查询不到sku信息!");
        }
        StockinVolumeWeightRecordMappingResponse response = new StockinVolumeWeightRecordMappingResponse();
        String purchaseNo = stockinOrderItemService.getPurchaseNoByInfo(request);
        response.setSku(productSpecInfo.getSku());
        response.setInternalBoxCode(request.getInternalBoxCode());
        response.setSupplierDeliveryNos(stockInternalBoxService.getWaitMeasureListSupplierDeliveryNoList(request.getInternalBoxCode(), request.getSku()));
        if (!StringUtils.hasText(purchaseNo)) {
            return response;
        }
        response.setPurchaseOrderNo(purchaseNo);
        PurchaseOrderWorkmanshipInfoResponse purchaseOrderWorkmanshipInfoResponse = scmApiService.purchaseOrderWorkmanshipInfoForQa(purchaseNo, productSpecInfo.getSku());
        if (Objects.isNull(purchaseOrderWorkmanshipInfoResponse)) {
            return response;
        }
        BeanUtils.copyProperties(purchaseOrderWorkmanshipInfoResponse, response);
        response.setChargedWeightKg(UnitUtils.poundsToKilograms(purchaseOrderWorkmanshipInfoResponse.getChargedWeight()));
        response.setVolumeWeightKg(UnitUtils.poundsToKilograms(purchaseOrderWorkmanshipInfoResponse.getVolumeWeightPound()));
        response.setStandardType(VolumeWeightStandardTypeEnum.of(purchaseOrderWorkmanshipInfoResponse.getStandardType()));
        response.setLength(purchaseOrderWorkmanshipInfoResponse.getPackageLong());
        response.setWidth(purchaseOrderWorkmanshipInfoResponse.getPackageWidth());
        response.setHeight(purchaseOrderWorkmanshipInfoResponse.getPackageHeight());
        List<StockinPackageNameResponse> packageNameList = stockInternalBoxService.getBaseMapper().getPackageNameList(request.getInternalBoxCode(), null);
        if (CollectionUtils.isEmpty(packageNameList)) {
            return response;
        }
        response.setPackageName(packageNameList.stream().filter(detail -> request.getSku().equalsIgnoreCase(detail.getSku()))
                .map(packageName -> packageName.getPackageName() + "(" + packageName.getQty() + ")")
                .collect(Collectors.joining(",")));
        return response;
    }

    /**
     * 获取详情信息
     *
     * @param request
     * @return
     */
    public StockinVolumeWeightRecordMappingDetailResponse getDetail(StockinVolumeWeightRecordMappingDetailRequest request) {
        StockinVolumeWeightRecordMappingDetailResponse response = this.getBaseMapper().getdetail(request);
        if (Objects.isNull(response)) {
            throw new BusinessServiceException("查询不到详情信息!");
        }
        response.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(Collections.singletonList(response.getSupplierDeliveryNo()), response.getSku()));
        List<StockinPackageNameResponse> packageNameList = stockinOrderItemService.getPackageNameInfo(request.getSupplierDeliveryNo(), request.getInternalBoxCode(), null);
        if (!CollectionUtils.isEmpty(packageNameList)) {
            response.setPackageName(packageNameList.stream().filter(detail -> request.getSku().equalsIgnoreCase(detail.getSku()))
                    .map(packageName -> packageName.getPackageName() + "(" + packageName.getQty() + ")")
                    .collect(Collectors.joining(",")));
        }
        //工艺标准信息
        StockinVolumeWeightRecordMappingValidRequest validRequest = new StockinVolumeWeightRecordMappingValidRequest();
        validRequest.setSku(response.getSku());
        validRequest.setInternalBoxCode(response.getInternalBoxCode());
        response.setMeasureStandardInfo(this.getMeasureStandard(validRequest));
        if (Objects.nonNull(response.getMeasureStandardInfo())) {
            response.getMeasureStandardInfo().setPackageName(response.getPackageName());
        }
        //质检包装测量信息
        StockinVolumeWeightRecordMappingRequest recordMappingRequest = new StockinVolumeWeightRecordMappingRequest();
        recordMappingRequest.setSku(response.getSku());
        recordMappingRequest.setSupplierDeliveryNo(response.getSupplierDeliveryNo());
        response.setMeasureInfoList(this.getVolumeWeightRecordMappingList(recordMappingRequest));
        return response;
    }


    @Transactional(rollbackFor = Exception.class)
    public void initPackageName() {
        int pageSize = 200;
        int currentPage = 1;
        while (true) {
            // 分页查询需要处理的数据
            Page<StockinVolumeWeightRecordMappingEntity> page = new Page<>(currentPage, pageSize);
            IPage<StockinVolumeWeightRecordMappingEntity> recordPage = this.page(page);
            List<StockinVolumeWeightRecordMappingEntity> records = recordPage.getRecords();

            if (CollectionUtils.isEmpty(records)) {
                break;
            }

            // 按supplierDeliveryNo和internalBoxCode分组，并过滤掉packageName不为空的记录
            Map<String, List<StockinVolumeWeightRecordMappingEntity>> groupMap = records.stream()
                    .filter(record -> !StringUtils.hasText(record.getPackageName()))
                    .collect(Collectors.groupingBy(
                            record -> record.getSupplierDeliveryNo() + "_" + record.getInternalBoxCode()
                    ));

            // 批量处理数据
            List<StockinVolumeWeightRecordMappingEntity> updateList = new ArrayList<>();
            for (Map.Entry<String, List<StockinVolumeWeightRecordMappingEntity>> entry : groupMap.entrySet()) {
                try {
                    String[] keys = entry.getKey().split("_");
                    String supplierDeliveryNo = keys[0];
                    String internalBoxCode = keys[1];

                    // 获取包装方式信息
                    List<StockinPackageNameResponse> packageNameList = stockinOrderItemService.getPackageNameInfo(supplierDeliveryNo, internalBoxCode, null);

                    if (!CollectionUtils.isEmpty(packageNameList)) {
                        // 按SKU分组包装方式信息，只取第一个元素的packageName
                        Map<String, String> skuPackageMap = packageNameList.stream()
                                .collect(Collectors.toMap(StockinPackageNameResponse::getSku, StockinPackageNameResponse::getPackageName, (existing, replacement) -> existing));

                        // 收集需要更新的记录
                        for (StockinVolumeWeightRecordMappingEntity record : entry.getValue()) {
                            String packageName = skuPackageMap.get(record.getSku());
                            if (StringUtils.hasText(packageName)) {
                                record.setPackageName(packageName);
                                updateList.add(record);
                            }
                        }
                    }
                } catch (Exception e) {
                    // 记录分组处理异常，但不影响其他分组的处理
                    LOGGER.error("处理分组数据异常，supplierDeliveryNo: {}, internalBoxCode: {}, 异常信息: {}", entry.getKey().split("_")[0], entry.getKey().split("_")[1], e.getMessage(), e);
                }
            }

            // 批量更新当前批次的数据
            if (!CollectionUtils.isEmpty(updateList)) {
                try {
                    this.updateBatchById(updateList);
                } catch (Exception e) {
                    // 记录批量更新异常，但不影响其他批次的处理
                    LOGGER.error("批量更新数据异常，批次: {}, 异常信息: {}", currentPage, e.getMessage(), e);
                }
            }

            // 如果当前页的数据量小于页大小，说明已经处理完所有数据
            if (records.size() < pageSize) {
                break;
            }

            currentPage++;
        }
    }

    public String convertSku(StockinVolumeWeightRecordMappingValidRequest request) {
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(request.getSku());
        if (Objects.nonNull(productSpecInfo)) {
            return productSpecInfo.getSku();
        }
        List<StockInternalBoxItemEntity> stockInternalBoxItemList = stockInternalBoxItemService.getByInternalBoxCode(request.getInternalBoxCode());
        if (CollectionUtils.isEmpty(stockInternalBoxItemList)) {
            throw new BusinessServiceException("查询不到内部箱明细信息!");
        }
        List<ProductStoreSkuMappingEntity> skuMappingEntityList = storeSkuMappingService.list(new QueryWrapper<ProductStoreSkuMappingEntity>().lambda()
                .eq(ProductStoreSkuMappingEntity::getStoreBarcode, request.getSku()));
        if (CollectionUtils.isEmpty(skuMappingEntityList)) {
            throw new BusinessServiceException("查询不到条码信息");
        }
        String sku = skuMappingEntityList.stream().filter(skuMapping -> stockInternalBoxItemList.stream().anyMatch(boxItem -> boxItem.getSku().equals(skuMapping.getSku()))).map(ProductStoreSkuMappingEntity::getSku).findFirst().orElse(null);
        if (!StringUtils.hasText(sku)) {
            throw new BusinessServiceException("查询不到条码信息");
        }
        return sku;
    }
}
