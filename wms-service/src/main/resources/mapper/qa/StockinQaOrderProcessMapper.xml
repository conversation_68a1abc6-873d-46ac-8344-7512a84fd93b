<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderProcessMapper">
    
    <select id="getProcessInfoByInfo" resultType="com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity">
        select  stockin_qa_order_id as stockinQaOrderId,max(create_date) as createDate  from stockin_qa_order_process
        where stockin_qa_order_id in 
        <foreach collection="qaOrderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and process_name = #{processName}
        GROUP BY stockin_qa_order_id
    </select>
</mapper>