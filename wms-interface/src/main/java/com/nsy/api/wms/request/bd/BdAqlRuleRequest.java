package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * AQL规则请求对象
 */
@ApiModel("AQL规则请求")
public class BdAqlRuleRequest {
    /**
     * AQL规则ID（编辑时必填）
     */
    @ApiModelProperty("AQL规则ID（编辑时必填）")
    private Integer aqlRuleId;

    /**
     * 区间从
     */
    @ApiModelProperty("区间从")
    @NotNull(message = "区间从不能为空")
    private Integer rangeFrom;

    /**
     * 区间到
     */
    @ApiModelProperty("区间到")
    @NotNull(message = "区间到不能为空")
    private Integer rangeTo;

    /**
     * 最少可接受严重件数
     */
    @ApiModelProperty("最少可接受严重件数")
    @NotNull(message = "最少可接受严重件数不能为空")
    private Integer minAcceptQty;

    /**
     * 仓库id
     */
    @ApiModelProperty("仓库id")
    private Integer spaceId;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    public Integer getAqlRuleId() {
        return aqlRuleId;
    }

    public void setAqlRuleId(Integer aqlRuleId) {
        this.aqlRuleId = aqlRuleId;
    }

    public Integer getRangeFrom() {
        return rangeFrom;
    }

    public void setRangeFrom(Integer rangeFrom) {
        this.rangeFrom = rangeFrom;
    }

    public Integer getRangeTo() {
        return rangeTo;
    }

    public void setRangeTo(Integer rangeTo) {
        this.rangeTo = rangeTo;
    }

    public Integer getMinAcceptQty() {
        return minAcceptQty;
    }

    public void setMinAcceptQty(Integer minAcceptQty) {
        this.minAcceptQty = minAcceptQty;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
} 