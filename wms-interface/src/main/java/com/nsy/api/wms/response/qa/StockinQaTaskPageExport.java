
package com.nsy.api.wms.response.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: caishao<PERSON>
 * @time: 2024/11/18 11:42
 */
public class StockinQaTaskPageExport {

    /**
     * 内部箱号
     */
    @ApiModelProperty("内部箱号")
    private String internalBoxCode;

    @ApiModelProperty(value = "内部箱类型", name = "internalBoxType")
    private String internalBoxType;

    @ApiModelProperty("商品id")
    private Integer productId;
    /**
     * spu
     */
    @ApiModelProperty("商品编码")
    private String spu;
    /**
     * skc
     */
    @ApiModelProperty("颜色编码")
    private String skc;
    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String sku;
    /**
     * 入库数量
     */
    @ApiModelProperty("到货数量")
    private Integer arrivalCount;

    @ApiModelProperty("箱内数")
    private Integer boxQty;

    /**
     * 推荐质检数量
     */
    @ApiModelProperty("要求质检数量")
    private Integer qaQty;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;
    /**
     * 是否新款
     */
    @ApiModelProperty("是否新款")
    private String isNewStr;

    private String checkStatus;

    /**
     * 质检状态：待质检、质检中、已质检、漏检上架
     */
    @ApiModelProperty("质检状态：待质检、质检中、已质检、漏检上架")
    private String checkStatusStr;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间", name = "stockinDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stockinDate;

    @ApiModelProperty(value = "品牌名称", name = "brandName")
    private String brandName;

    /**
     * 包装方式
     */
    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;

    @ApiModelProperty(value = "质检完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeDate;

    /**
     * 工厂出库单号
     */
    @ApiModelProperty(value = "采购单号", name = "purchasePlanNos")
    private String purchasePlanNos;

    /**
     * 出库箱码
     */
    @ApiModelProperty(value = "出库箱码", name = "supplierDeliveryBoxCodes")
    private String supplierDeliveryBoxCodes;

    @ApiModelProperty(value = "品类1", name = "categoryOne")
    private String categoryOne;
    @ApiModelProperty(value = "品类2", name = "categoryTwo")
    private String categoryTwo;
    @ApiModelProperty(value = "品类3", name = "categoryThree")
    private String categoryThree;

    @ApiModelProperty(value = "质检员名字", name = "qcUserName")
    private String qcUserName;

    @ApiModelProperty(value = "初审完成时间", name = "completeDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstAuditEndDate;

    @ApiModelProperty(value = "标签", name = "skuType")
    private String skuType;

    public Integer getBoxQty() {
        return boxQty;
    }

    public void setBoxQty(Integer boxQty) {
        this.boxQty = boxQty;
    }

    public String getInternalBoxType() {
        return internalBoxType;
    }

    public void setInternalBoxType(String internalBoxType) {
        this.internalBoxType = internalBoxType;
    }

    public String getPurchasePlanNos() {
        return purchasePlanNos;
    }

    public void setPurchasePlanNos(String purchasePlanNos) {
        this.purchasePlanNos = purchasePlanNos;
    }

    public String getSupplierDeliveryBoxCodes() {
        return supplierDeliveryBoxCodes;
    }

    public void setSupplierDeliveryBoxCodes(String supplierDeliveryBoxCodes) {
        this.supplierDeliveryBoxCodes = supplierDeliveryBoxCodes;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSkc() {
        return skc;
    }

    public void setSkc(String skc) {
        this.skc = skc;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getQaQty() {
        return qaQty;
    }

    public void setQaQty(Integer qaQty) {
        this.qaQty = qaQty;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getIsNewStr() {
        return isNewStr;
    }

    public void setIsNewStr(String isNewStr) {
        this.isNewStr = isNewStr;
    }

    public String getCheckStatusStr() {
        return checkStatusStr;
    }

    public void setCheckStatusStr(String checkStatusStr) {
        this.checkStatusStr = checkStatusStr;
    }

    public Date getStockinDate() {
        return stockinDate;
    }

    public void setStockinDate(Date stockinDate) {
        this.stockinDate = stockinDate;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public String getCategoryOne() {
        return categoryOne;
    }

    public void setCategoryOne(String categoryOne) {
        this.categoryOne = categoryOne;
    }

    public String getCategoryTwo() {
        return categoryTwo;
    }

    public void setCategoryTwo(String categoryTwo) {
        this.categoryTwo = categoryTwo;
    }

    public String getCategoryThree() {
        return categoryThree;
    }

    public void setCategoryThree(String categoryThree) {
        this.categoryThree = categoryThree;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public Date getFirstAuditEndDate() {
        return firstAuditEndDate;
    }

    public void setFirstAuditEndDate(Date firstAuditEndDate) {
        this.firstAuditEndDate = firstAuditEndDate;
    }

    public String getSkuType() {
        return skuType;
    }

    public void setSkuType(String skuType) {
        this.skuType = skuType;
    }
}
