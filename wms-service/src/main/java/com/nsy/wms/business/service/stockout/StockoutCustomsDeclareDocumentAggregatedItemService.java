package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareDocumentAggregatedItemResult;
import com.nsy.api.wms.request.base.PageRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareDocumentAggregatedSkuQtyPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutCustomDeclareDocumentItemSkuQtyResponse;
import com.nsy.wms.business.service.product.ProductCategoryCustomsDeclareService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentAggregatedItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareDocumentAggregatedItemMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
public class StockoutCustomsDeclareDocumentAggregatedItemService extends ServiceImpl<StockoutCustomsDeclareDocumentAggregatedItemMapper, StockoutCustomsDeclareDocumentAggregatedItemEntity> {
    @Resource
    StockoutCustomsDeclareDocumentItemService documentItemService;
    @Resource
    StockoutCustomsDeclareDocumentService declareDocumentService;
    @Resource
    ProductCategoryCustomsDeclareService productCategoryCustomsDeclareService;

    private static final BigDecimal QTY_COEFFICIENT = new BigDecimal("0.01");

    private static final BigDecimal WEIGHT_COEFFICIENT = new BigDecimal("1.6");

    /**
     * 通过 declareDocumentId 查找
     *
     * @param declareDocumentId
     * @return
     */
    public List<StockoutCustomsDeclareDocumentAggregatedItemEntity> listByDeclareDocumentId(Integer declareDocumentId) {
        return list(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentAggregatedItemEntity>().eq(StockoutCustomsDeclareDocumentAggregatedItemEntity::getDeclareDocumentId, declareDocumentId));
    }


    /**
     * 获取聚合明细分页
     *
     * @param declareDocumentId
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomsDeclareDocumentAggregatedItemResult> getAggregatedItemResultPage(Integer declareDocumentId, PageRequest request) {
        StockoutCustomsDeclareDocumentEntity entity = declareDocumentService.getById(declareDocumentId);
        if (Objects.isNull(entity) || IsDeletedConstant.DELETED.equals(entity.getIsDeleted()))
            throw new BusinessServiceException("所选单据不存在");
        Page<StockoutCustomsDeclareDocumentAggregatedItemEntity> aggregatedItemPage = page(new Page<>(request.getPageIndex(), request.getPageSize()),
                new LambdaQueryWrapper<StockoutCustomsDeclareDocumentAggregatedItemEntity>().eq(StockoutCustomsDeclareDocumentAggregatedItemEntity::getDeclareDocumentId, declareDocumentId));

        PageResponse<StockoutCustomsDeclareDocumentAggregatedItemResult> response = new PageResponse<>();
        //构建返回result
        response.setContent(buildResultList(entity, aggregatedItemPage.getRecords()));
        response.setTotalCount(aggregatedItemPage.getTotal());
        return response;
    }

    /**
     * 处理有差值的运费
     *
     * @param entity
     * @param itemResultList
     */
    public void handelDiffApportionFreight(StockoutCustomsDeclareDocumentEntity entity, List<StockoutCustomsDeclareDocumentAggregatedItemResult> itemResultList) {
        if (itemResultList.isEmpty()) return;
        BigDecimal exchangeRate = entity.getExchangeRate();
        exchangeRate = exchangeRate.compareTo(BigDecimal.ZERO) == 0 ? productCategoryCustomsDeclareService.getExchangeRate() : exchangeRate;
        //真实的运费
        BigDecimal realFreight = StockoutCustomsDeclareDocumentItemFetchService.fetchFreight(entity.getFreight(), entity.getCurrency(), exchangeRate);
        //明细的运费
        BigDecimal originFreight = itemResultList.stream().map(StockoutCustomsDeclareDocumentAggregatedItemResult::getApportionFreight).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //差值
        BigDecimal diffValue = realFreight.subtract(originFreight);
        //差值加到第一条
        StockoutCustomsDeclareDocumentAggregatedItemResult firstItem = itemResultList.get(0);
        firstItem.setApportionFreight(firstItem.getApportionFreight().add(diffValue));
    }

    /**
     * 构建 result list
     *
     * @param entity
     * @return
     */
    public List<StockoutCustomsDeclareDocumentAggregatedItemResult> buildResultList(StockoutCustomsDeclareDocumentEntity entity) {
        //构建返回体
        List<StockoutCustomsDeclareDocumentAggregatedItemEntity> aggregatedItemList = listByDeclareDocumentId(entity.getDeclareDocumentId());
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = aggregatedItemList.stream().map(item -> buildResult(entity, item)).collect(Collectors.toList());
        //处理有差值的运费
        handelDiffApportionFreight(entity, aggregatedItemResultList);
        return aggregatedItemResultList;
    }

    /**
     * 构建 result list
     *
     * @param entity
     * @param aggregatedItemList
     * @return
     */
    public List<StockoutCustomsDeclareDocumentAggregatedItemResult> buildResultList(StockoutCustomsDeclareDocumentEntity entity, List<StockoutCustomsDeclareDocumentAggregatedItemEntity> aggregatedItemList) {
        //构建返回体
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = aggregatedItemList.stream().map(item -> buildResult(entity, item)).collect(Collectors.toList());
        //处理有差值的运费
        handelDiffApportionFreight(entity, aggregatedItemResultList);
        return aggregatedItemResultList;
    }

    /**
     * 构建result对象
     *
     * @param entity
     * @param itemEntity
     * @return
     */
    private StockoutCustomsDeclareDocumentAggregatedItemResult buildResult(StockoutCustomsDeclareDocumentEntity entity, StockoutCustomsDeclareDocumentAggregatedItemEntity itemEntity) {
        StockoutCustomsDeclareDocumentAggregatedItemResult itemResult = BeanUtil.toBean(itemEntity, StockoutCustomsDeclareDocumentAggregatedItemResult.class);
        itemResult.setSigningDate(entity.getSigningDate());

        //设置ActualBoxQty
        itemResult.setWeight(itemResult.getWeight().setScale(3, RoundingMode.HALF_UP));
        itemResult.setActualBoxQty(Optional.ofNullable(itemResult.getActualBoxQty()).orElse(0));
        //设置 ActualRoughWeight
        if (Objects.nonNull(itemResult.getPlanWeight()) && Objects.nonNull(entity.getTotalPlanWeight())
                && BigDecimal.ZERO.compareTo(entity.getTotalPlanWeight()) != 0 && Objects.nonNull(itemResult.getBoxWeight())
                && BigDecimal.ZERO.compareTo(itemResult.getRoughWeight()) == 0) {
            itemResult.setActualRoughWeight(itemResult.getPlanWeight().multiply(entity.getWeight().divide(entity.getTotalPlanWeight(), 9, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP));
        } else {
            itemResult.setActualRoughWeight(itemResult.getRoughWeight().setScale(2, RoundingMode.HALF_UP));
        }
        //设置 ActualNetWeight
        if (itemResult.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal netWeight;
            if (itemResult.getActualBoxQty() == 0)
                netWeight = itemResult.getActualRoughWeight().subtract(BigDecimal.valueOf(itemResult.getQty()).multiply(QTY_COEFFICIENT));
            else
                netWeight = itemResult.getActualRoughWeight().subtract(BigDecimal.valueOf(itemResult.getActualBoxQty()).multiply(WEIGHT_COEFFICIENT));
            itemResult.setActualNetWeight(netWeight.setScale(2, RoundingMode.HALF_UP));
        } else {
            itemResult.setActualNetWeight(itemResult.getNetWeight().setScale(2, RoundingMode.HALF_UP));
        }
        //设置 Volume
        if (entity.getBoxNum() != 0) {
            itemResult.setVolume(entity.getVolume().multiply(BigDecimal.valueOf(itemResult.getActualBoxQty()).divide(BigDecimal.valueOf(entity.getBoxNum()), 2, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP));
        }
        //设置 SpecType
        if (StringUtils.isNotBlank(itemResult.getPolymerizedDeclareElement())) {
            itemResult.setSpecType(itemResult.getPolymerizedDeclareElement());
        } else {
            getMaxQtySellerSku(itemResult);
        }

        return itemResult;
    }


    private void getMaxQtySellerSku(StockoutCustomsDeclareDocumentAggregatedItemResult itemEntity) {
        //货号要取数量最多的seller_sku
        List<StockoutCustomsDeclareDocumentItemEntity> list = documentItemService.getBaseMapper().getSellerSkuOrderByQty(itemEntity.getDeclareDocumentAggregatedItemId());
        if (!list.isEmpty()) {
            itemEntity.setSpecType(list.get(0).getDeclareElement());
        }
    }

    /**
     * 重置聚合明细
     */
    public void resetAggregatedItem(Integer newDeclareDocumentId, String declareDocumentNo) {
        //删除旧的
        removeByDeclareDocumentNo(declareDocumentNo);
        //存新的
        List<StockoutCustomsDeclareDocumentAggregatedItemResult> aggregatedItemResultList = documentItemService.getBaseMapper().aggregatedItemList(newDeclareDocumentId);
        final int[] gNo = {1};
//        StockoutCustomsDeclareDocumentEntity declareDocumentEntity = declareDocumentService.getEnableByDeclareDocumentId(newDeclareDocumentId);
        aggregatedItemResultList.forEach(item -> {
            StockoutCustomsDeclareDocumentAggregatedItemEntity entity = BeanUtil.toBean(item, StockoutCustomsDeclareDocumentAggregatedItemEntity.class);
//            BigDecimal.valueOf(item.getPlanBoxQty()).multiply(BigDecimal.valueOf(declareDocumentEntity.getBoxNum()))
//                    .divide(BigDecimal.valueOf(declareDocumentEntity.getTotalPlanBoxNum()), 0, RoundingMode.HALF_UP).intValue();
            entity.setgNo(gNo[0]);
            entity.setDeclareDocumentId(newDeclareDocumentId);
            entity.setDeclareDocumentNo(declareDocumentNo);
            gNo[0] += 1;
            save(entity);
            //更新单据明细
            documentItemService.update(new LambdaUpdateWrapper<StockoutCustomsDeclareDocumentItemEntity>()
                    .set(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentAggregatedItemId, entity.getDeclareDocumentAggregatedItemId())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getDeclareDocumentId, entity.getDeclareDocumentId())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getCustomsDeclareCn, entity.getCustomsDeclareCn())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getSpinType, entity.getSpinType())
                    .eq(StockoutCustomsDeclareDocumentItemEntity::getFabricType, entity.getFabricType()));
        });
    }

    /**
     * 初始化所有的聚合明细
     */
    @Transactional
    public void init() {
        List<StockoutCustomsDeclareDocumentEntity> declareDocumentList = declareDocumentService.list();
        declareDocumentList.forEach(entity -> resetAggregatedItem(entity.getDeclareDocumentId(), entity.getDeclareDocumentNo()));
    }

    /**
     * 通过单据号 和 项号查找
     *
     * @param declareDocumentNo
     * @param gNo
     * @return
     */
    public StockoutCustomsDeclareDocumentAggregatedItemEntity findByDeclareDocumentNoAndGNo(String declareDocumentNo, Integer gNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentAggregatedItemEntity>()
                .eq(StockoutCustomsDeclareDocumentAggregatedItemEntity::getDeclareDocumentNo, declareDocumentNo)
                .eq(StockoutCustomsDeclareDocumentAggregatedItemEntity::getgNo, gNo)
                .last("limit 1"));
    }

    /**
     * 通过单据号删除
     *
     * @param declareDocumentNo
     * @return
     */
    public void removeByDeclareDocumentNo(String declareDocumentNo) {
        this.remove(new LambdaQueryWrapper<StockoutCustomsDeclareDocumentAggregatedItemEntity>()
                .eq(StockoutCustomsDeclareDocumentAggregatedItemEntity::getDeclareDocumentNo, declareDocumentNo));
    }

    /**
     * 获取result
     *
     * @param declareDocumentNo
     * @param gNo
     * @return
     */
    public StockoutCustomsDeclareDocumentAggregatedItemResult getAggregatedItemResult(String declareDocumentNo, Integer gNo) {
        StockoutCustomsDeclareDocumentEntity declareDocumentEntity = declareDocumentService.findByDeclareDocumentNo(declareDocumentNo);
        if (Objects.isNull(declareDocumentEntity))
            return null;

        StockoutCustomsDeclareDocumentAggregatedItemEntity aggregatedItemEntity = findByDeclareDocumentNoAndGNo(declareDocumentNo, gNo);
        if (Objects.isNull(aggregatedItemEntity))
            return null;
        return buildResult(declareDocumentEntity, aggregatedItemEntity);
    }

    /**
     * 报关单据聚合明细skuQty分页展示 -- 点击报关单据项进入明细
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutCustomDeclareDocumentItemSkuQtyResponse> getDeclareDocumentAggregatedSkuQtyPage(StockoutCustomsDeclareDocumentAggregatedSkuQtyPageRequest request) {
        IPage<StockoutCustomDeclareDocumentItemSkuQtyResponse> page = documentItemService.getBaseMapper().getSkuQtyPage(new Page<>(request.getPageIndex(), request.getPageSize()),
                request.getDeclareDocumentAggregatedItemId());

        PageResponse<StockoutCustomDeclareDocumentItemSkuQtyResponse> response = new PageResponse();
        response.setContent(page.getRecords());
        response.setTotalCount(page.getTotal());
        return response;
    }
}
