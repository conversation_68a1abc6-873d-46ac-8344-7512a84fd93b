package com.nsy.wms.repository.jpa.mapper.qa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.api.wms.response.qa.StockinQaOrderPageExport;
import com.nsy.wms.repository.entity.qa.StockinQaOrderItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: caish<PERSON><PERSON>
 * @version: v1.0
 * @description: 质检单明细表Mapper
 * @date: 2024-11-18 15:58
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinQaOrderItemMapper extends BaseMapper<StockinQaOrderItemEntity> {

    List<String> getPurchasePlanNoByStockinQaOrderId(@Param("stockinQaOrderId") Integer stockinQaOrderId);

    List<StockinQaOrderPageExport> listSupplierDeliveryNo(List<Integer> stockinQaOrderIds);

    boolean isReturnDeliveryType(Integer stockinQaOrderId);
}
