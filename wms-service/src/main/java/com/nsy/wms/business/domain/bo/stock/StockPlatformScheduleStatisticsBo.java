package com.nsy.wms.business.domain.bo.stock;

/**
 * 工厂发货列表统计BO
 */
public class StockPlatformScheduleStatisticsBo {

    /**
     * 发货总箱数
     */
    private Long boxQtyTotal;

    /**
     * 发货总件数
     */
    private Integer shipmentQtyTotal;

    /**
     * 收货总件数
     */
    private Integer receiveQtyTotal;

    /**
     * 需退货总件数
     */
    private Integer waitReturnQtyTotal;

    /**
     * 上架总件数
     */
    private Integer shelvedQtyTotal;

    public Long getBoxQtyTotal() {
        return boxQtyTotal;
    }

    public void setBoxQtyTotal(Long boxQtyTotal) {
        this.boxQtyTotal = boxQtyTotal;
    }

    public Integer getShipmentQtyTotal() {
        return shipmentQtyTotal;
    }

    public void setShipmentQtyTotal(Integer shipmentQtyTotal) {
        this.shipmentQtyTotal = shipmentQtyTotal;
    }

    public Integer getReceiveQtyTotal() {
        return receiveQtyTotal;
    }

    public void setReceiveQtyTotal(Integer receiveQtyTotal) {
        this.receiveQtyTotal = receiveQtyTotal;
    }

    public Integer getWaitReturnQtyTotal() {
        return waitReturnQtyTotal;
    }

    public void setWaitReturnQtyTotal(Integer waitReturnQtyTotal) {
        this.waitReturnQtyTotal = waitReturnQtyTotal;
    }

    public Integer getShelvedQtyTotal() {
        return shelvedQtyTotal;
    }

    public void setShelvedQtyTotal(Integer shelvedQtyTotal) {
        this.shelvedQtyTotal = shelvedQtyTotal;
    }
}
