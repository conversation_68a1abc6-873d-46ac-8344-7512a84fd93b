package com.nsy.api.wms.domain.stockin;

/**
 * 工厂退货库位信息界面增加导出明细
 */
public class StockinReturnProductPositionDetailExport {
    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 采购员
     */
    private String purchaserRealName;

    /**
     * 库位编码
     */
    private String positionCode;

    /**
     * 退货性质
     */
    private String returnNature;

    /**
     * 规格编码
     */
    private String sku;

    /**
     * 数量
     */
    private Integer returnQty;

    /**
     * 操作人
     */
    private String createBy;

    /**
     * 操作时间
     */
    private String createDate;

    /**
     * 采购单号
     */
    private String purchasePlanNo;


    /**
     * 质检员
     */
    private String qcUserName;

    /**
     * 内部箱号
     */
    private String internalBoxCode;

    /**
     * 不合格原因归类
     */
    private String unqualifiedCategory;

    /**
     * 来源区域
     */
    private String sourceArea;

    /**
     * 包装方式
     */
    private String packingMethod;

    /**
     * 版本号
     */
    private String versionNo;


    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPurchaserRealName() {
        return purchaserRealName;
    }

    public void setPurchaserRealName(String purchaserRealName) {
        this.purchaserRealName = purchaserRealName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getReturnNature() {
        return returnNature;
    }

    public void setReturnNature(String returnNature) {
        this.returnNature = returnNature;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getSourceArea() {
        return sourceArea;
    }

    public void setSourceArea(String sourceArea) {
        this.sourceArea = sourceArea;
    }

    public String getPackingMethod() {
        return packingMethod;
    }

    public void setPackingMethod(String packingMethod) {
        this.packingMethod = packingMethod;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }
}
