package com.nsy.wms.business.service.stockout;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.domain.product.SendDingDingMessage;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.AmazonBuyShippingInfoEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderHandleFailTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpSyncPrintExceptionRequest;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.logistics.base.PrintService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;

import cn.hutool.core.util.StrUtil;

@Service
public class StockoutOrderGetLabelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderGetLabelService.class);

    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private OmsApiService omsApiService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private StockoutOrderLogService logService;
    @Autowired
    private PrintService printService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutOrderItemService orderItemService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private StockoutOrderHandleFailRecordService handleFailRecordService;

    /**
     * 仅获取面单，如果需要预占tcode则修改状态为 透明计划预占中
     * true 成功  false失败
     * @param stockoutOrderEntity
     * @return
     */
    public boolean startGetLabel(StockoutOrderEntity stockoutOrderEntity) {
        StockoutOrderEntity byId = stockoutOrderService.getById(stockoutOrderEntity.getStockoutOrderId());
        if (StrUtil.isBlank(stockoutOrderEntity.getLogisticsCompany()) || !byId.getStatus().equals(StockoutOrderStatusEnum.READY.name())
                && !byId.getStatus().equals(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name())) return true;
        try {
            BaseGetLogisticsNoResponse response = printService.generateLabelByStockoutOrder(stockoutOrderEntity, loginInfoService.getName(), "自动");
            // 出库单的物流单号赋值
            if (!StringUtils.hasText(response.getLogisticsNo()) && !StrUtil.equalsAnyIgnoreCase(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.TIANZONG_FEDEX)) {
                throw new BusinessServiceException("TMS返回空物流单号");
            }
            stockoutOrderEntity.setLogisticsNo(response.getLogisticsNo());
            stockoutOrderEntity.setSecondaryNumber(response.getSecondaryNumber());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderEntity.setReadyDate(new Date());
            stockoutOrderEntity.setAmazonBuyShippingInfo(1 == response.getAmazonBuyShippingFlag() ? AmazonBuyShippingInfoEnum.ENABLE.getValue() : AmazonBuyShippingInfoEnum.DISABLE.getValue());
            stockoutOrderService.updateById(stockoutOrderEntity);
            stockoutOrderService.syncErpLogistics(stockoutOrderEntity);
            return true;
        } catch (Exception e) {
            // 尝试重试
            // 目前只有顺丰/万邑通进行重试
            if (LogisticsCompanyConstant.SF_EUROPE.equalsIgnoreCase(stockoutOrderEntity.getLogisticsCompany()) && StrUtil.contains(e.getMessage(), "9744")
                    || StrUtil.contains(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.MS_WINIT_QRD) && StrUtil.containsAnyIgnoreCase(e.getMessage(), "此产品不支持POBOX地址派送", "大于最大限制值33", "订单商品尺寸不符合产品限制")
                    || StrUtil.equalsIgnoreCase(LogisticsCompanyConstant.YUNTU_SHANGPAI, stockoutOrderEntity.getLogisticsCompany()) && StrUtil.containsAny(e.getMessage(), "收件人邮编不可达", "本线路不服务于PO", "无可用渠道")
                    || StrUtil.equalsIgnoreCase(LogisticsCompanyConstant.YUNTU_SHANGPAI_XIAMEN, stockoutOrderEntity.getLogisticsCompany()) && StrUtil.containsAny(e.getMessage(), "收件人邮编不可达", "本线路不服务于PO", "无可用渠道")) {
                return replaceLogisticsCompanyAndRetry(stockoutOrderEntity);
            }
            // 获取面单失败， 通知erp, 发送钉钉消息
            doException(stockoutOrderEntity, e);
            return false;
        }
    }

    // 失败时做法
    public void doException(StockoutOrderEntity stockoutOrderEntity, Exception e) {
        LOGGER.error(e.getMessage(), e);
        // 错误信息
        String msg = StrUtil.maxLength(StrUtil.removeAny(e.getMessage() == null ? "null" : e.getMessage(), "com.nsy.api.core.apicore.exception.InvalidRequestException:", "TMS物流错误：", "【wms】"), 295);
        logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LOGISTICS_FAIL_RECORD, String.format("job获取物流失败：%s，更改出库单状态:获取面单失败", msg));
        //保存获取面单失败记录表
        handleFailRecordService.saveOrUpdateRecords(stockoutOrderEntity.getStockoutOrderId(), stockoutOrderEntity.getStockoutOrderNo(),
                StockoutOrderHandleFailTypeEnum.LOGISTICS_GET_FAIL, String.format("job获取物流失败：%s，更改出库单状态:获取面单失败", msg));
        // 无物流单号，推送erp获取面单失败
        List<String> orderNos = orderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId()).stream()
                .map(StockoutOrderItemEntity::getOrderNo).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        String tid = CollectionUtils.isEmpty(orderNos) ? "" : orderNos.get(0);
        // 推送erp面单失败列表
        erpApiService.syncPrintException(new ErpSyncPrintExceptionRequest(tid, stockoutOrderEntity.getErpPickId(), String.format("【wms】%s", msg), loginInfoService.getName()));
        // 推送钉钉
        sendDingDingMsg(stockoutOrderEntity, tid, msg);

    }

    public void buildErrorStatus(StockoutOrderEntity stockoutOrderEntity) {
        if (StockoutOrderStatusEnum.checkUpdateStatus(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name())) {
            stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name());
        } else {
            LOGGER.info("出库单:{}无法回退状态:{}到:{}", stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name());
        }
        stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
        stockoutOrderService.updateById(stockoutOrderEntity);
    }

    private void sendDingDingMsg(StockoutOrderEntity stockoutOrderEntity, String tid, String msg) {
        try {
            List<Integer> staffingList = omsApiService.getStoreStaffingById(stockoutOrderEntity.getStoreId());
            List<String> notifyUserAccounts = new ArrayList<>();
            staffingList.forEach(staff -> {
                SysUserInfo userInfoByUserId = userApiService.getUserInfoByUserId(staff);
                if (userInfoByUserId != null) {
                    notifyUserAccounts.add(userInfoByUserId.getUserAccount());
                }
            });
            if (!CollectionUtils.isEmpty(notifyUserAccounts)) {
                SendDingDingMessage sendDingDingMessage = new SendDingDingMessage();
                String notifyContent = StrUtil.maxLength(msg, 130);
                sendDingDingMessage.setText(String.format("订单号【%s】，获取物流单号失败: %s", tid, notifyContent));
                sendDingDingMessage.setUserNameList(notifyUserAccounts);
                messageProducer.sendMessage(KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
            }
        } catch (Exception e) {
            LOGGER.error("获取面单失败，钉钉通知错误" + e.getMessage(), e);
        }

    }

    private boolean replaceLogisticsCompanyAndRetry(StockoutOrderEntity stockoutOrderEntity) {
        if (StrUtil.equalsIgnoreCase(LogisticsCompanyConstant.SF_EUROPE, stockoutOrderEntity.getLogisticsCompany())) {
            stockoutOrderEntity.setLogisticsCompany(LogisticsCompanyConstant.YUN_TU);
        } else if (StrUtil.contains(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.MS_WINIT_QRD)) {
            stockoutOrderEntity.setLogisticsCompany(LogisticsCompanyConstant.MS_WINIT_WRD);
        } else if (StrUtil.equalsIgnoreCase(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.YUNTU_SHANGPAI)) {
            stockoutOrderEntity.setLogisticsCompany(LogisticsCompanyConstant.YUNTU_FZZXR);
        } else if (StrUtil.equalsIgnoreCase(stockoutOrderEntity.getLogisticsCompany(), LogisticsCompanyConstant.YUNTU_SHANGPAI_XIAMEN)) {
            stockoutOrderEntity.setLogisticsCompany(LogisticsCompanyConstant.YUNTU_FZZXR_XIAMEN);
        } else {
            throw new BusinessServiceException("暂不支持物流公司失败重试：" + stockoutOrderEntity.getLogisticsCompany());
        }
        stockoutOrderEntity.setLogisticsNo("");
        stockoutOrderService.updateById(stockoutOrderEntity);
        logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LOGISTICS_FAIL_RECORD, "job获取物流失败，系统自动设置新的物流：" + stockoutOrderEntity.getLogisticsCompany());
        stockoutOrderService.syncErpLogistics(stockoutOrderEntity);
        return startGetLabel(stockoutOrderEntity);
    }
}
