<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper">
    <sql id="shipmentPageWhere">
        <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
            and stockout_shipment.shipment_box_code = #{query.shipmentBoxCode}
        </if>
        <if test="query.shipmentIdList != null and query.shipmentIdList.size() > 0">
            and stockout_shipment.shipment_id in
            <foreach collection="query.shipmentIdList" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.spaceAreaIds != null and query.spaceAreaIds.size() > 0">
            and stockout_shipment_item.space_area_Id in
            <foreach collection="query.spaceAreaIds" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.sku != null and query.sku != ''">
            and stockout_shipment_item.sku like  concat(#{query.sku}, '%')
        </if>
        <if test="query.createBy != null and query.createBy != ''">
            and stockout_shipment.create_by like  concat(#{query.createBy}, '%')
        </if>
        <if test="query.replenishOrder != null and query.replenishOrder != ''">
            and stockout_shipment.replenish_order = #{query.replenishOrder}
        </if>

        <if test="query.shipperName != null and query.shipperName != ''">
            and shipper_info.shipper_name = #{query.shipperName}
        </if>
        <if test="query.receiverName != null and query.receiverName != ''">
            and stockout_receiver_info.receiver_name = #{query.receiverName}
        </if>
        <if test="query.orderNos != null and query.orderNos.size() > 0">
            and stockout_shipment_item.order_no in
            <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
            and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
        </if>
        <if test="query.stockoutNos != null and query.stockoutNos.size() > 0">
            and stockout_shipment_item.stockout_order_no in
            <foreach collection="query.stockoutNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.workspace != null and query.workspace != ''">
            and stockout_order.workspace = #{query.workspace}
        </if>
        <if test="query.stockoutBatchId != null">
            and stockout_shipment_item.batch_id = #{query.stockoutBatchId}
        </if>
        <if test="query.status != null and query.status.size() > 0">
            and stockout_shipment.status in
            <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.storeName != null and query.storeName != ''">
            and stockout_order.store_id = #{query.storeName}
        </if>
        <if test="query.logisticNos != null and query.logisticNos.size() > 0">
            and stockout_shipment.logistics_no in
            <foreach collection="query.logisticNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.transferLogisticNo != null and query.transferLogisticNo != ''">
            and stockout_shipment.transfer_logistics_no = #{query.transferLogisticNo}
        </if>
        <if test="query.spaceIds != null and query.spaceIds.size() > 0">
            and stockout_order.space_id in
            <foreach collection="query.spaceIds" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.source != null and query.source != ''">
            and stockout_order_label.source = #{query.source}
        </if>
        <if test="query.stockoutType != null and query.stockoutType != ''">
            and stockout_shipment.stockout_type = #{query.stockoutType}
        </if>
        <if test="query.logisticsCompanyList != null and query.logisticsCompanyList.size() > 0">
            and stockout_shipment.logistics_company in
            <foreach collection="query.logisticsCompanyList" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.forwarderChannel != null and query.forwarderChannel != ''">
            and stockout_shipment.forwarder_channel = #{query.forwarderChannel}
        </if>
        <if test="query.uploadFalian != null and query.uploadFalian == 'NO_NEED'">
            and shipment_logistics_info.upload_falian = 'NO_NEED'
        </if>
        <if test="query.uploadFalian != null and query.uploadFalian != '' and query.uploadFalian != 'NO_NEED'">
            and (shipment_logistics_info.upload_falian = 'NEW' or shipment_logistics_info.upload_falian = 'SUCCESS')
        </if>
        <if test="query.uploadInvoice != null and query.uploadInvoice == 'NO_NEED'">
            and shipment_logistics_info.upload_invoice = 'NO_NEED'
        </if>
        <if test="query.uploadInvoice != null and query.uploadInvoice != '' and query.uploadInvoice != 'NO_NEED'">
            and (shipment_logistics_info.upload_invoice = 'NEW' or shipment_logistics_info.upload_invoice = 'SUCCESS')
        </if>
        <if test="query.isPrint != null">
            and stockout_order_label.is_print = #{query.isPrint}
        </if>
        <if test="query.businessType != null and query.businessType != ''">
            and stockout_order.business_type = #{query.businessType}
        </if>
        <if test="query.deliveryDate != null">
            and stockout_shipment.delivery_date &gt;= #{query.deliveryDate}
        </if>
        <if test="query.deliveryDateEnd != null">
            and stockout_shipment.delivery_date &lt; #{query.deliveryDateEnd}
        </if>
        <if test="query.shipmentDateBegin!=null">
            and stockout_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
        </if>
        <if test="query.shipmentDateEnd!=null">
            and stockout_shipment.shipment_date &lt;= #{query.shipmentDateEnd}
        </if>
        and stockout_shipment_item.is_deleted = 0
    </sql>

    <sql id="shipmentFBAPageWhere">
        <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
            and stockout_shipment.shipment_box_code = #{query.shipmentBoxCode}
        </if>
        <if test="query.shipmentIdList != null and query.shipmentIdList.size() > 0">
            and stockout_shipment.shipment_id in
            <foreach collection="query.shipmentIdList" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.orderNos != null and query.orderNos.size() > 0">
            and stockout_shipment_item.order_no in
            <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
            and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
        </if>
        <if test="query.sku != null and query.sku != ''">
            and stockout_shipment_item.sku = #{query.sku}
        </if>
        <if test="query.createBy != null and query.createBy != ''">
            and stockout_shipment.create_by like  concat(#{query.createBy}, '%')
        </if>
        <if test="query.replenishOrder != null and query.replenishOrder != ''">
            and stockout_shipment.replenish_order = #{query.replenishOrder}
        </if>
        <if test="query.status != null and query.status.size() > 0">
            and stockout_shipment.status in
            <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.storeName != null and query.storeName != ''">
            and stockout_order.store_id = #{query.storeName}
        </if>
        <if test="query.fbaLabelStatus != null and query.fbaLabelStatus != ''">
            and stockout_shipment.fba_label_status = #{query.fbaLabelStatus}
        </if>
        <if test="query.fbaReplenishType != null and query.fbaReplenishType != ''">
            and stockout_shipment.fba_replenish_type = #{query.fbaReplenishType}
        </if>
        <if test="query.logisticNos != null and query.logisticNos.size() > 0">
            and stockout_shipment.logistics_no in
            <foreach collection="query.logisticNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.forwarderChannel != null and query.forwarderChannel != ''">
            and stockout_shipment.forwarder_channel = #{query.forwarderChannel}
        </if>
        <if test="query.deliveryDate != null">
            and stockout_shipment.delivery_date &gt;= #{query.deliveryDate}
        </if>
        <if test="query.deliveryDateEnd != null">
            and stockout_shipment.delivery_date &lt; #{query.deliveryDateEnd}
        </if>
        <if test="query.shipmentDateBegin!=null">
            and stockout_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
        </if>
        <if test="query.shipmentDateEnd!=null">
            and stockout_shipment.shipment_date &lt; #{query.shipmentDateEnd}
        </if>
        <if test="query.fbaShipmentIdList != null and query.fbaShipmentIdList.size() > 0">
            and stockout_shipment_amazon_relation.fba_shipment_id in
            <foreach collection="query.fbaShipmentIdList" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.stockoutType != null and query.stockoutType != ''">
            and stockout_shipment.stockout_type = #{query.stockoutType}
        </if>
        <if test="query.stockoutType == null or query.stockoutType == ''">
            and stockout_shipment.stockout_type in ('FIRST_LEG_DELIVERY', 'FBA_FACTORY_DELIVERY')
        </if>
        <if test="query.platformName != null and query.platformName != ''">
            and stockout_shipment.platform_name = #{query.platformName}
        </if>
        and stockout_shipment.is_deleted = 0
    </sql>

    <update id="cancelReplenishOrder">

        update stockout_shipment
        set replenish_order_status = 'WAIT_DEAL',replenish_order = null,fba_label_status = 'INIT'
        where replenish_order = #{replenishOrder};

    </update>
    <update id="updateFbaLabelStatusByStockoutOrderNo">
        update stockout_shipment s
        inner join stockout_shipment_item si
        on s.shipment_id = si.shipment_id
        set s.fba_label_status = #{fbaLabelStatus}
        where si.stockout_order_no = #{stockoutOrderNo}
        and s.is_deleted = 0
        and si.is_deleted = 0
    </update>

    <select id="pageSearchShipmentIds" resultType="java.lang.Integer">
        select
        DISTINCT (stockout_shipment.shipment_id) as shipmentId
        from stockout_shipment stockout_shipment
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        <if test="(query.source != null and query.source != '') or (query.isPrint != null) or (query.shipperName != null and query.shipperName != '') or (query.workspace != null and query.workspace != '') or (query.storeName != null and query.storeName != '') or (query.spaceIds != null and query.spaceIds.size() > 0) or (query.businessType != null and query.businessType != '')">
            left join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <if test="(query.source != null and query.source != '') or query.isPrint != null">
            left join stockout_order_label stockout_order_label on stockout_order_label.stockout_order_id = stockout_order.stockout_order_id
        </if>
        <if test="(query.uploadFalian != null and query.uploadFalian != '') or (query.uploadInvoice != null and query.uploadInvoice != '')">
            left join stockout_shipment_logistics_info shipment_logistics_info ON shipment_logistics_info.logistics_no = stockout_shipment.logistics_no
        </if>
        <if test="query.shipperName != null and query.shipperName != ''">
            left join stockout_shipper_info shipper_info on stockout_order.stockout_order_id = shipper_info.stockout_order_id
        </if>
        <if test="query.receiverName != null and query.receiverName != ''">
            left join stockout_receiver_info stockout_receiver_info on stockout_shipment_item.stockout_order_no = stockout_receiver_info.stockout_order_no
        </if>
        <where>
            <include refid="shipmentPageWhere"></include>
        </where>
        order by stockout_shipment.shipment_date
    </select>


    <select id="pageSearchShipmentIdsCount" resultType="java.lang.Integer">
        select
            count(DISTINCT stockout_shipment.shipment_id)
        from stockout_shipment stockout_shipment
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        <if test="(query.source != null and query.source != '') or (query.isPrint != null) or (query.shipperName != null and query.shipperName != '') or (query.workspace != null and query.workspace != '') or (query.storeName != null and query.storeName != '') or (query.spaceIds != null and query.spaceIds.size() > 0) or (query.businessType != null and query.businessType != '')">
            left join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <if test="(query.source != null and query.source != '') or query.isPrint != null">
            left join stockout_order_label stockout_order_label on stockout_order_label.stockout_order_id = stockout_order.stockout_order_id
        </if>
        <if test="(query.uploadFalian != null and query.uploadFalian != '') or (query.uploadInvoice != null and query.uploadInvoice != '')">
            left join stockout_shipment_logistics_info shipment_logistics_info ON shipment_logistics_info.logistics_no = stockout_shipment.logistics_no
        </if>
        <if test="query.shipperName != null and query.shipperName != ''">
            left join stockout_shipper_info shipper_info on stockout_order.stockout_order_id = shipper_info.stockout_order_id
        </if>
        <if test="query.receiverName != null and query.receiverName != ''">
            left join stockout_receiver_info stockout_receiver_info on stockout_shipment_item.stockout_order_no = stockout_receiver_info.stockout_order_no
        </if>
        <where>
            <include refid="shipmentPageWhere"></include>
        </where>
    </select>


    <select id="pageSearchCount" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchCount">
        SELECT
            ifnull(sum( boxSkuAmount ), 0) AS boxSkuAmount,
            ifnull(sum( weight ), 0) AS weight
        from(
            select
                stockout_shipment.shipment_id as shipmentId,
                sum(stockout_shipment_item.qty) as boxSkuAmount,
                stockout_shipment.weight as weight
            from stockout_shipment stockout_shipment
            left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
            <if test="query.workspace != null and query.workspace != ''
            or query.storeName != null and query.storeName != ''
            or query.spaceIds != null and query.spaceIds.size() > 0
            or query.businessType != null and query.businessType != ''
            or query.source != null and query.source != ''
            or query.isPrint != null or (query.receiverName != null and query.receiverName != '')
            or query.shipperName != null and query.shipperName != ''">
                left join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
            </if>

            <if test="query.source != null and query.source != ''
            or query.isPrint != null">
                left join stockout_order_label stockout_order_label on stockout_order_label.stockout_order_id = stockout_order.stockout_order_id
            </if>

            <if test="query.shipperName != null and query.shipperName != ''">
                left join stockout_shipper_info shipper_info on stockout_order.stockout_order_id = shipper_info.stockout_order_id
            </if>

            <if test="query.uploadFalian != null and query.uploadFalian != ''
                or query.uploadInvoice != null and query.uploadInvoice != ''">
                left join stockout_shipment_logistics_info shipment_logistics_info ON shipment_logistics_info.logistics_no = stockout_shipment.logistics_no
            </if>
            <if test="query.receiverName != null and query.receiverName != ''">
                left join stockout_receiver_info stockout_receiver_info on stockout_shipment_item.stockout_order_no = stockout_receiver_info.stockout_order_no
            </if>

            <where>
                <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
                    and stockout_shipment.shipment_box_code = #{query.shipmentBoxCode}
                </if>
                <if test="query.replenishOrder != null and query.replenishOrder != ''">
                    and stockout_shipment.replenish_order = #{query.replenishOrder}
                </if>
                <if test="query.orderNos != null and query.orderNos.size() > 0">
                    and stockout_shipment_item.order_no in
                    <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                             close=")">
                        #{eachOne}
                    </foreach>
                </if>
                <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                    and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
                </if>
                <if test="query.workspace != null and query.workspace != ''">
                    and stockout_order.workspace = #{query.workspace}
                </if>
                <if test="query.stockoutBatchId != null">
                    and stockout_shipment_item.batch_id = #{query.stockoutBatchId}
                </if>
                <if test="query.receiverName != null and query.receiverName != ''">
                    and stockout_receiver_info.receiver_name = #{query.receiverName}
                </if>
                <if test="query.status != null and query.status.size() > 0">
                    and stockout_shipment.status in
                    <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                             close=")">
                        #{eachOne}
                    </foreach>
                </if>
                <if test="query.storeName != null and query.storeName != ''">
                    and stockout_order.store_id = #{query.storeName}
                </if>
                <if test="query.logisticNos != null and query.logisticNos.size() > 0">
                    and stockout_shipment.logistics_no in
                    <foreach collection="query.logisticNos" separator="," index="index" item="eachOne" open="("
                             close=")">
                        #{eachOne}
                    </foreach>
                </if>
                <if test="query.transferLogisticNo != null and query.transferLogisticNo != ''">
                    and stockout_shipment.transfer_logistics_no = #{query.transferLogisticNo}
                </if>
                <if test="query.spaceIds != null and query.spaceIds.size() > 0">
                    and stockout_order.space_id in
                    <foreach collection="query.spaceIds" separator="," index="index" item="eachOne" open="("
                             close=")">
                        #{eachOne}
                    </foreach>
                </if>
                <if test="query.logisticsCompanyList != null and query.logisticsCompanyList.size() > 0">
                    and stockout_shipment.logistics_company in
                    <foreach collection="query.logisticsCompanyList" separator="," index="index" item="eachOne" open="("
                             close=")">
                        #{eachOne}
                    </foreach>
                </if>
                <if test="query.forwarderChannel != null and query.forwarderChannel != ''">
                    and stockout_shipment.forwarder_channel = #{query.forwarderChannel}
                </if>
                <if test="query.businessType != null and query.businessType != ''">
                    and stockout_order.business_type = #{query.businessType}
                </if>
                <if test="query.deliveryDate != null">
                    and stockout_shipment.delivery_date &gt;= #{query.deliveryDate}
                </if>
                <if test="query.deliveryDateEnd != null">
                    and stockout_shipment.delivery_date &lt; #{query.deliveryDateEnd}
                </if>
                <if test="query.shipmentDateBegin!=null">
                    and stockout_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
                </if>
                <if test="query.shipmentDateEnd!=null">
                    and stockout_shipment.shipment_date &lt; #{query.shipmentDateEnd}
                </if>
                <if test="query.sku != null and query.sku != ''">
                    and stockout_shipment.shipment_id in (
                        select ss1.shipment_id FROM stockout_shipment ss1 LEFT JOIN stockout_shipment_item ssi1 ON ss1.shipment_id = ssi1.shipment_id where ssi1.sku like  concat(#{query.sku}, '%'))
                </if>
                <if test="query.isPrint != null">
                    and stockout_order_label.is_print = #{query.isPrint}
                </if>
                <if test="query.source != null and query.source != ''">
                    and stockout_order_label.source = #{query.source}
                </if>
                <if test="query.shipperName != null and query.shipperName != ''">
                    and shipper_info.shipper_name = #{query.shipperName}
                </if>
                <if test="query.uploadFalian != null and query.uploadFalian == 'NO_NEED'">
                    and shipment_logistics_info.upload_falian = 'NO_NEED'
                </if>
                <if test="query.uploadFalian != null and query.uploadFalian != '' and query.uploadFalian != 'NO_NEED'">
                    and (shipment_logistics_info.upload_falian = 'NEW' or shipment_logistics_info.upload_falian = 'SUCCESS')
                </if>
                <if test="query.uploadInvoice != null and query.uploadInvoice == 'NO_NEED'">
                    and shipment_logistics_info.upload_invoice = 'NO_NEED'
                </if>
                <if test="query.uploadInvoice != null and query.uploadInvoice != '' and query.uploadInvoice != 'NO_NEED'">
                    and (shipment_logistics_info.upload_invoice = 'NEW' or shipment_logistics_info.upload_invoice = 'SUCCESS')
                </if>
                and (stockout_shipment_item.is_deleted = 0 OR stockout_shipment_item.is_deleted is null) AND stockout_shipment_item.is_deleted != 1
            </where>
            group by shipmentId
        ) t

    </select>
    <select id="shipmentConfirmListBySameReceiverInfo" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipmentItem.order_no as orderNo,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.stockout_type as stockoutType,
        stockoutShipment.box_index as boxIndex,
        'piece',
        stockoutShipmentItem.qty as Qty,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutBatchOrder.batch_id as batchId,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.create_by,
        stockoutShipmentItem.create_date
        from stockout_shipment_item stockoutShipmentItem
        left join stockout_shipment stockoutShipment on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        left join stockout_order stockoutOrder on stockoutOrder.stockout_order_no COLLATE utf8mb4_general_ci=
stockoutShipmentItem.stockout_order_no
        left join stockout_batch_order stockoutBatchOrder on stockoutBatchOrder.stockout_order_id =
        stockoutOrder.stockout_order_id
        WHERE
        stockoutShipmentItem.stockout_order_no in (
        select stockoutReceiverInfo.stockout_order_id from stockout_receiver_info stockoutReceiverInfo WHERE
        stockoutReceiverInfo.receiver_info_md5 IN (

        select stockoutReceiverInfo.receiver_info_md5 from stockout_receiver_info stockoutReceiverInfo WHERE
        stockoutReceiverInfo.stockout_order_id IN
        (

        select stockoutOrder.stockout_order_id from stockout_shipment_item stockoutShipmentItem
        left join stockout_shipment stockoutShipment on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        left join stockout_order stockoutOrder on stockoutOrder.stockout_order_no COLLATE utf8mb4_general_ci=
        stockoutShipmentItem.stockout_order_no
        left join stockout_batch_order stockoutBatchOrder on stockoutBatchOrder.stockout_order_id =
        stockoutOrder.stockout_order_id
        <where>
            <if test="query.no != null and query.no != ''">
                (stockoutShipmentItem.stockout_order_no = #{query.no}
                or stockoutBatchOrder.batch_id = #{query.no}
                or stockoutShipmentItem.order_no= #{query.no})
                <if test="query.logisticsCompany !=null and query.logisticsCompany !='' ">
                    and stockoutShipment.logistics_company = #{query.logisticsCompany}
                </if>
            </if>
            and stockoutShipmentItem.is_deleted = 0
        </where>
            )
            )
        )
        and stockoutShipmentItem.is_deleted = 0
    </select>

    <select id="shipmentConfirmList" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipmentItem.order_no as orderNo,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.stockout_type as stockoutType,
        stockoutShipment.box_index as boxIndex,
        'piece',
        stockoutShipmentItem.qty as Qty,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutBatchOrder.batch_id as batchId,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.create_by,
        stockoutShipmentItem.create_date
        from stockout_shipment_item stockoutShipmentItem
        left join stockout_shipment stockoutShipment on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        left join stockout_order stockoutOrder on stockoutOrder.stockout_order_no = stockoutShipmentItem.stockout_order_no
        left join stockout_batch_order stockoutBatchOrder on stockoutBatchOrder.stockout_order_id = stockoutOrder.stockout_order_id
        <where>
            <if test="query.no != null and query.no != '' ">
                (stockoutShipmentItem.stockout_order_no = #{query.no}
                or stockoutBatchOrder.batch_id = #{query.no}
                or stockoutShipmentItem.order_no= #{query.no})
                <if test="query.logisticsCompany!=null and query.logisticsCompany!=''">
                    and stockoutShipment.logistics_company = #{query.logisticsCompany}
                </if>
            </if>
            and stockoutShipmentItem.is_deleted = 0
        </where>
    </select>
    <select id="findReturnProductList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutOrderReturnProductList">
        select
        o.status as stockoutOrderStatus,
        o.stockout_order_id,
        o.stockout_order_no,
        o.store_id,
        o.store_name,
        o.workspace,
        o.business_type,
        s.logistics_no,
        s.logistics_company,
        spec.product_id,
        spec.spec_id,
        spec.sku,
        spec.image_url,
        spec.thumbnail_image_url,
        spec.preview_image_url,
        spec.color,
        spec.size,
        spec.barcode,
        item.order_no,
        item.qty,
        item.order_item_id
        from
        stockout_shipment s
        inner join stockout_shipment_item item on s.shipment_id = item.shipment_id  and item.is_deleted = 0
        inner join product_spec_info spec on spec.spec_id = item.spec_id
        inner join stockout_order o on o.stockout_order_no = item.stockout_order_no
        <where>
            <if test="scanNumber != null and scanNumber !=''">
                s.logistics_no = #{scanNumber} or item.order_no = #{scanNumber}
            </if>
            and s.is_deleted = 0
        </where>

    </select>
    <select id="findAllShippedExport" resultType="com.nsy.api.wms.domain.stockout.ShipmentAllShippedExport">
        select
        stockout_shipment.delivery_date as deliveryDate,
        stockout_order.store_name as storeName,
        stockout_shipment_item.order_no as orderNo,
        stockout_shipment.logistics_company as logisticsCompany,
        stockout_shipment.logistics_no as logisticsNo,
        stockout_shipment.weight as weight,
        GROUP_CONCAT(distinct stockout_shipment.shipment_id) as shipmentIdConcat,
        stockout_shipment.transfer_logistics_no as transferLogisticsNo,
        stockout_shipment.forwarder_channel as forwarderChannel,
        stockout_receiver_info.receiver_info as receiverInfo
        from stockout_shipment stockout_shipment
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        left join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        left join stockout_receiver_info stockout_receiver_info on stockout_receiver_info.stockout_order_id = stockout_order.stockout_order_id

        <where>
            <if test="shipmentIds != null and shipmentIds.size() > 0">
                and stockout_shipment.shipment_id in
                <foreach collection="shipmentIds" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            and stockout_shipment.status = 'SHIPPED' and stockout_shipment_item.is_deleted = 0 and stockout_shipment.is_deleted = 0
        </where>
        group by stockout_order.stockout_order_no
    </select>
    <select id="getShipmentItemByQuery" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentItem">
        select shipment.*,item.order_no as orderNo, item.stockout_order_item_id as stockoutOrderItemId,
               item.order_item_id as orderItemId, item.spec_id as specId, item.qty, item.sku
        from stockout_shipment shipment
        JOIN stockout_shipment_item item on shipment.shipment_id = item.shipment_id
        <where>
            <if test="query.status != null and query.status !=''">
                and shipment.status = #{query.status}
            </if>
            <if test="query.logisticsCompany != null and query.logisticsCompany !=''">
                and shipment.logistics_company = #{query.logisticsCompany}
            </if>
            <if test="query.stockoutOrderNoList != null and query.stockoutOrderNoList.size() > 0">
                and item.stockout_order_no IN
                <foreach collection="query.stockoutOrderNoList" separator="," index="index" item="orderItem" open="(" close=")">
                    #{orderItem}
                </foreach>
            </if>
            and item.is_deleted = 0
        </where>
    </select>

    <select id="logisticsDocumentsCategoryItem" resultType="com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintItemInfo">
        SELECT ROUND(AVG(soi.invoice_price),2) AS unitPrice,
               SUM(item.qty) AS qty,
               ROUND(AVG(soi.invoice_price) * SUM(item.qty), 2) AS goodsPrice,
               MAX(pccd.hs_code) AS hsCode,
               MAX(pccd.customs_declare_cn) AS customsDeclareCn,
               MAX(pccd.customs_declare_en) AS customsDeclareEn,
               MAX(pi.fabric_type_en) AS fabricTypeEn,
               MAX(pi.fabric_type) AS fabricTypeCn,
               MAX(pi.spu) AS spu,
               MAX(pccd.element_value) AS elementValue,
               CAST(AVG(psi.weight) AS DECIMAL(10,2)) AS weight,
               CAST(AVG(ifnull(psi.actual_weight, 0)) AS DECIMAL(10,2)) AS actualWeight,
               MAX(pwc.english_name) AS categoryEn,
               pccd.wms_category_id As categoryId,
               MAX(bdc.unit) AS declareUnit
        FROM stockout_shipment ss
        LEFT JOIN stockout_shipment_item item ON item.shipment_id = ss.shipment_id
        LEFT JOIN stockout_order_item soi ON soi.stockout_order_item_id = item.stockout_order_item_id
        LEFT JOIN product_spec_info psi ON item.spec_id = psi.spec_id
        LEFT JOIN product_info pi ON psi.product_id = pi.product_id
        left join product_category_mapping pcm on pi.category_id = pcm.category_id and pcm.declaration_type = 'AGGREGATED_DECLARATION'
        LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare pccd ON pccd.wms_category_id = pcm.wms_category_id
        LEFT JOIN bd_hs_code bdc ON bdc.hs_code = pccd.hs_code
        <where>
            <if test="shipmentIdList != null and shipmentIdList.size() > 0">
            ss.shipment_id IN
                <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and item.is_deleted = 0
        </where>
        GROUP BY pccd.wms_category_id
        ORDER BY qty DESC
    </select>

    <select id="upsOtherChannel" resultType="com.nsy.api.wms.domain.logistics.documents.UpsOtherChannelInfo">
        SELECT TRUNCATE(ROUND(AVG(soi.invoice_price),2)/2, 2) AS unitPrice,
        SUM(item.qty) AS qty,
        TRUNCATE(ROUND(AVG(soi.invoice_price) * SUM(item.qty), 2)/2, 2) AS goodsPrice,
        MAX(pccd.hs_code) AS hsCode,
        MAX(pccd.customs_declare_cn) AS customsDeclareCn,
        MAX(pccd.customs_declare_en) AS customsDeclareEn,
        MAX(pi.fabric_type_en) AS fabricTypeEn,
        MAX(pi.fabric_type) AS fabricTypeCn,
        MAX(pi.spu) AS spu,
        TRUNCATE(CAST(AVG(ifnull(psi.weight, 0)) AS DECIMAL(10,3))/1000, 3) AS weight,
        TRUNCATE(CAST(AVG(ifnull(psi.actual_weight, 0)) AS DECIMAL(10,3))/1000, 3) AS actualWeight,
        MAX(pwc.english_name) AS categoryEn,
        MAX(pwc.name) AS name,
        pccd.wms_category_id As categoryId,
        MAX(bdc.unit) AS declareUnit,
        MAX(item.stockout_order_no) AS stockoutOrderNo,
        MAX(item.order_no) AS orderNo,
        ss.shipment_id
        FROM stockout_shipment ss
        LEFT JOIN stockout_shipment_item item ON item.shipment_id = ss.shipment_id
        LEFT JOIN stockout_order_item soi ON soi.stockout_order_item_id = item.stockout_order_item_id
        LEFT JOIN product_spec_info psi ON item.spec_id = psi.spec_id
        LEFT JOIN product_info pi ON psi.product_id = pi.product_id
        left join product_category_mapping pcm on pi.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare pccd ON pccd.wms_category_id = pcm.wms_category_id
        LEFT JOIN bd_hs_code bdc ON bdc.hs_code = pccd.hs_code
        <where>
            <if test="shipmentIdList != null and shipmentIdList.size() > 0">
                ss.shipment_id IN
                <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and item.is_deleted = 0
        </where>
        GROUP BY pwc.english_name, ss.shipment_id
    </select>

    <select id="logisticsDocumentsSkuItem" resultType="com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsPrintItemInfo">
        SELECT soi.invoice_price AS unitPrice,
        item.qty AS qty,
        soi.invoice_price * item.qty AS goodsPrice,
        pccd.hs_code AS hsCode,
        pccd.customs_declare_cn AS customsDeclareCn,
        pccd.customs_declare_en AS customsDeclareEn,
        pi.fabric_type_en AS fabricTypeEn,
        pi.fabric_type AS fabricTypeCn,
        pi.spu AS spu,
        pccd.element_value AS elementValue,
        psi.weight AS weight,
        psi.actual_weight AS actualWeight,
        pwc.english_name AS categoryEn,
        pccd.wms_category_id  As categoryId,
        bdc.unit AS declareUnit,
        psi.sku AS sku,
        CONCAT_WS('-',psi.color,psi.size) as colorSize
        FROM stockout_shipment ss
        LEFT JOIN stockout_shipment_item item ON item.shipment_id = ss.shipment_id
        LEFT JOIN stockout_order_item soi ON soi.stockout_order_item_id = item.stockout_order_item_id
        LEFT JOIN product_spec_info psi ON item.spec_id = psi.spec_id
        LEFT JOIN product_info pi ON psi.product_id = pi.product_id
        left join product_category_mapping pcm on pi.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id
        LEFT JOIN product_category_customs_declare pccd ON pccd.wms_category_id = pcm.wms_category_id
        LEFT JOIN bd_hs_code bdc ON bdc.hs_code = pccd.hs_code
        <where>
            <if test="shipmentIdList != null and shipmentIdList.size() > 0">
                ss.shipment_id IN
                <foreach collection="shipmentIdList" separator="," index="index" item="shipmentId" open="(" close=")">
                    #{shipmentId}
                </foreach>
            </if>
            and item.is_deleted = 0
        </where>
        ORDER BY qty DESC
    </select>

    <select id="logisticsDocumentsShipmentInfo" resultType="com.nsy.api.wms.domain.logistics.documents.LogisticsDocumentsReloadShipmentInfo">
        select ssi.stockout_order_no,
               ss.shipment_id as shipmentId,
               sbo.batch_id as batchId,
               MAX(ss.shipment_box_code) as shipmentBoxCode,
               MAX(ss.box_index) as boxIndex,
               MAX(ss.weight) as weight,
               MAX(ss.box_size) as boxSize,
               MAX(ss.logistics_company) as logisticsCompany,
               MAX(ss.logistics_no) as logisticsNo,
               ssi.order_no as orderNo,
               SUM(ssi.qty) as qty,
               MAX(ss.status) as status,
               MAX(ss.create_by) as createBy,
               MAX(ss.create_date) AS createDate
        from stockout_shipment ss
                 LEFT JOIN stockout_shipment_item ssi on ssi.shipment_id = ss.shipment_id
                 LEFT JOIN stockout_order so on so.stockout_order_no = ssi.stockout_order_no
                 LEFT JOIN stockout_batch_order sbo on sbo.stockout_order_id = so.stockout_order_id
                 LEFT JOIN stockout_receiver_info sri ON sri.stockout_order_id = so.stockout_order_id
        <where>
            <if test="query.logisticsCompany != null and query.logisticsCompany !=''">
                and ss.logistics_company = #{query.logisticsCompany}
            </if>
            <if test="query.receiveInfoMd5 != null and query.receiveInfoMd5 !=''">
                and sri.receiver_info_md5 = #{query.receiveInfoMd5}
            </if>
            <if test="query.batchId != null and query.batchId !=''">
                and sbo.batch_id = #{query.batchId}
            </if>
            <if test="query.status != null and query.status !=''">
                and ss.status = #{query.status}
            </if>
            <if test="query.stockoutOrderNoList != null and query.stockoutOrderNoList.size > 0 ">
                and ssi.stockout_order_no in
                <foreach collection="query.stockoutOrderNoList" item="stockoutOrderNoItem" open="(" close=")" separator=",">
                    #{stockoutOrderNoItem}
                </foreach>
            </if>
            <if test="query.shipmentBoxCode != null and query.shipmentBoxCode !=''">
                and ss.shipment_box_code = #{query.shipmentBoxCode}
            </if>
            and ssi.is_deleted = 0
        </where>
        GROUP BY ssi.stockout_order_no,
                 ss.shipment_id,
                 sbo.batch_id,
                 ssi.order_no
    </select>
    <select id="shipmentBoxSku" resultType="com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport">
        select stockout_shipment.box_index as boxIndex,
               pwc.name as customsDeclareCn,
               pwc.english_name as customsDeclareEn,
               product_customs_declare.hs_code as hsCode,
               stockout_shipment_item.sku as sku,
               product_spec_info.color as color,
               product_spec_info.size as size,
               product_spec_info.image_url as imageUrl,
               stockout_shipment_item.qty as qty,
               stockout_shipment_item.order_no as orderNo,
               stockout_order_item.invoice_price as invoicePrice,
               stockout_shipment.weight as weight,
               stockout_shipment.volume_weight as volumeWeight,
               stockout_shipment.box_size as boxSize,
               product_spec_info.weight/1000 as unitWeight,
               product_info.spin_type as spinType,
               product_info.fabric_type as fabricType,
               product_info.filler as filler,
               stockout_order_item.seller_sku as storeSku,
               stockout_order_item.seller_barcode as storeBarcode,
               product_customs_declare.element_value as elementValue
        FROM
            stockout_shipment_item stockout_shipment_item
            LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
            LEFT JOIN stockout_order_item stockout_order_item ON stockout_order_item.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
            left join product_spec_info product_spec_info on product_spec_info.spec_id = stockout_shipment_item.spec_id
            left join product_info product_info on product_info.product_id = product_spec_info.product_id
            left join product_category_mapping pcm on product_info.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
            LEFT JOIN product_category_customs_declare product_category_customs_declare ON product_category_customs_declare.wms_category_id = pcm.wms_category_id
            LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id
            left join product_customs_declare product_customs_declare on product_customs_declare.product_id = product_spec_info.product_id
        <where>
            <if test="shipmentIds != null and shipmentIds.size > 0 ">
                and stockout_shipment_item.shipment_id in
                <foreach collection="shipmentIds" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            and stockout_shipment_item.is_deleted = 0
        </where>
        order by stockout_shipment.box_index, stockout_shipment_item.order_no asc
    </select>
    <select id="dateShippedInfo" resultType="com.nsy.api.wms.domain.stockout.ShipmentDateShippedInfoDTO">
        select stockout_shipment.logistics_no as LogisticsNo,
               stockout_shipment.logistics_company as logistics_company,
               stockout_order.workspace as workspace,
        stockout_shipment.shipment_box_code as shipmentBoxCode,
               stockout_shipment.delivery_date as deliveryDate,
               stockout_shipment_item.qty as qty
        from stockout_shipment stockout_shipment
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        left join stockout_order stockout_order on stockout_shipment_item.stockout_order_no = stockout_order.stockout_order_no
        <where>
            stockout_shipment.shipment_id in
                <foreach collection="shipmentIds" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            and stockout_shipment.status = 'SHIPPED'
            and stockout_shipment_item.is_deleted = 0
        </where>
        order by stockout_shipment.delivery_date
    </select>

    <select id="findStockOutOrderNoByScanNumber" resultType="java.lang.String">
        select
        stockoutShipmentItem.stockout_order_no
        from
        stockout_shipment stockoutShipment
        inner join
        stockout_shipment_item stockoutShipmentItem
        on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id and stockoutShipmentItem.is_deleted = 0
        <where>
            <if test="no != null and no != '' ">
                (stockoutShipmentItem.stockout_order_no = #{no}
                or stockoutShipment.shipment_box_code = #{no}
                or stockoutShipmentItem.order_no= #{no})
            </if>
            and stockoutShipment.status = 'PACKING_END'
            and stockoutShipment.is_deleted = 0
        </where>
        group by stockoutShipmentItem.stockout_order_no
    </select>
    <select id="findShipmentConfirmList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.forwarder_channel,
        stockoutShipment.shipment_id,
        stockoutShipment.box_size,
        stockoutShipment.stockout_type,
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipmentItem.order_no as orderNo,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.box_index as boxIndex,
        stockoutShipment.logistics_no as logisticsNo,
        stockoutShipmentItem.spec_id as spec_id,
        stockoutShipmentItem.sku as sku,
        stockoutShipmentItem.qty as qty,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutShipmentItem.batch_id as batchId,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.create_by,
        stockoutShipmentItem.create_date,
        o.space_id as spaceId
        from
        stockout_shipment stockoutShipment
        inner join
        stockout_shipment_item stockoutShipmentItem
        on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id and stockoutShipmentItem.is_deleted = 0
        inner join  stockout_order o
        on o.stockout_order_no = stockoutShipmentItem.stockout_order_no
        <where>
            <if test="status != null and status != ''">
                stockoutShipment.status = #{status}
            </if>
            <if test="stockoutOrderNoList != null and stockoutOrderNoList.size > 0 ">
                and stockoutShipmentItem.stockout_order_no in
                <foreach collection="stockoutOrderNoList" item="outOrderNo" open="(" close=")" separator=",">
                    #{outOrderNo}
                </foreach>
            </if>
            <if test="ids != null and ids.size > 0 ">
                and stockoutShipment.shipment_id in
                <foreach collection="ids" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            and stockoutShipment.is_deleted = 0
        </where>
    </select>
    <select id="findShipmentConfirmItemList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.forwarder_channel,
        stockoutShipment.shipment_id,
        stockoutShipment.box_size,
        stockoutShipment.stockout_type,
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.box_index as boxIndex,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipment.create_by,
        stockoutShipment.create_date,
        stockoutShipmentItem.shipment_item_id,
        stockoutShipmentItem.spec_id,
        stockoutShipmentItem.sku,
        stockoutShipmentItem.batch_id as batchId,
        stockoutShipmentItem.qty as qty,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.is_deleted,
        stockoutShipmentItem.stockout_order_item_id as stockoutOrderItemId
        from
        stockout_shipment stockoutShipment
        inner join
        stockout_shipment_item stockoutShipmentItem on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        <if test="isDeleted != null">
         and stockoutShipmentItem.is_deleted = #{isDeleted}
        </if>
        and stockoutShipmentItem.shipment_id in
        ( select
            distinct shipment_id
            from stockout_shipment_item item
            <where>
                <if test="stockoutOrderNoList != null and stockoutOrderNoList.size > 0 ">
                    and item.stockout_order_no in
                    <foreach collection="stockoutOrderNoList" item="outOrderNo" open="(" close=")" separator=",">
                        #{outOrderNo}
                    </foreach>
                </if>
                <if test="isDeleted != null">
                    and item.is_deleted = #{isDeleted}
                </if>
            </where>
        )
        <where>
            <if test="status != null and status != ''">
                stockoutShipment.status = #{status}
            </if>
            <if test="isDeleted != null">
                and stockoutShipment.is_deleted = #{isDeleted}
            </if>
        </where>
    </select>
    <select id="getPrintShipmentList" resultType="com.nsy.api.wms.domain.stockout.ShipmentSkuInfo">
        SELECT
            si.sku,
            s.box_index,
            si.qty,
            p.color,
            p.size,
            s.shipment_box_code,
            si.stockout_order_no,
            r.receiver_info,
            soi.order_no,
            soi.seller_sku,
            o.logistics_company,
            s.remark,
            pi.category_name,
            stt.platform_reference_no
        FROM
            stockout_shipment s
            JOIN stockout_shipment_item si ON si.shipment_id = s.shipment_id
            JOIN product_spec_info p ON si.spec_id = p.spec_id
            left join stockout_receiver_info r on si.stockout_order_no = r.stockout_order_no
            join stockout_order o on si.stockout_order_no = o.stockout_order_no
            join stockout_order_item soi on si.stockout_order_item_id = soi.stockout_order_item_id
            left join product_info pi on p.product_id = pi.product_id
            left join stock_transfer_tracking stt on stt.order_no = soi.order_no
        where s.shipment_id in
        <foreach collection="idList" item="shipmentId" open="(" close=")" separator=",">
            #{shipmentId}
        </foreach>
        and si.is_deleted = 0 order by soi.order_no, s.box_index
    </select>
    <select id="getPrintShipmentOrder" resultType="com.nsy.api.wms.domain.stockout.ShipmentSkuInfo">
        SELECT
            si.sku,
            s.box_index,
            si.qty,
            p.color,
            p.size,
            s.shipment_box_code,
            si.stockout_order_no,
            r.receiver_info,
            s.remark
        FROM
            stockout_shipment s
            JOIN stockout_shipment_item si ON si.shipment_id = s.shipment_id
            JOIN product_spec_info p ON si.spec_id = p.spec_id
            left join stockout_receiver_info r on si.stockout_order_no = r.stockout_order_no
        where si.stockout_order_no in
        <foreach collection="stockoutOrderNoList" item="stockoutOrderNo" open="(" close=")" separator=",">
            #{stockoutOrderNo}
        </foreach>
        and si.is_deleted = 0
    </select>

    <select id="findShipmentItemList" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.forwarder_channel,
        stockoutShipment.shipment_id,
        stockoutShipment.box_size,
        stockoutShipment.stockout_type,
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.box_index as boxIndex,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipment.create_by,
        stockoutShipment.create_date,
        stockoutShipmentItem.shipment_item_id,
        stockoutShipmentItem.spec_id,
        stockoutShipmentItem.sku,
        stockoutShipmentItem.batch_id as batchId,
        stockoutShipmentItem.qty as qty,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.is_deleted
        from
        stockout_shipment stockoutShipment
        inner join
        stockout_shipment_item stockoutShipmentItem on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        <if test="isDeleted != null">
            and stockoutShipmentItem.is_deleted = #{isDeleted}
        </if>
        and stockoutShipmentItem.shipment_id in
        <foreach collection="shipmentIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        <where>
            <if test="status != null and status != ''">
                stockoutShipment.status = #{status}
            </if>
            <if test="isDeleted != null">
                and stockoutShipment.is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <select id="findShipmentIdByShipmentBoxCodeListAndStatusList" resultType="java.lang.Integer">
        select
            stockoutShipment.shipment_id
        from
        stockout_shipment stockoutShipment
        <where>
            <if test="shipmentBoxCodeList != null and shipmentBoxCodeList.size > 0 ">
                and stockoutShipment.shipment_box_code in
                <foreach collection="shipmentBoxCodeList" item="shipmentBoxCode" open="(" close=")" separator=",">
                    #{shipmentBoxCode}
                </foreach>
            </if>
            <if test="statusList != null and statusList.size > 0 ">
                and stockoutShipment.status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            and stockoutShipment.is_deleted = 0
        </where>
        order by stockoutShipment.shipment_id
    </select>

    <select id="findShipmentListByOrderNo"
            resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.forwarder_channel,
        stockoutShipment.shipment_id,
        stockoutShipment.box_size,
        stockoutShipment.stockout_type,
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.box_index as boxIndex,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipment.create_by,
        stockoutShipment.create_date,
        stockoutShipmentItem.shipment_item_id,
        stockoutShipmentItem.spec_id,
        stockoutShipmentItem.sku,
        stockoutShipmentItem.batch_id as batchId,
        stockoutShipmentItem.qty as qty,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.is_deleted
        from
        stockout_shipment stockoutShipment
        inner join
        stockout_shipment_item stockoutShipmentItem on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        <if test="isDeleted != null">
            and stockoutShipmentItem.is_deleted = #{isDeleted}
        </if>
        and stockoutShipmentItem.stockout_order_no in
        <foreach collection="stockoutOrderNoList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <where>
            <if test="status != null and status != ''">
                stockoutShipment.status = #{status}
            </if>
            <if test="isDeleted != null">
                and stockoutShipment.is_deleted = #{isDeleted}
            </if>
        </where>
    </select>
    <select id="countByStatus" resultType="com.nsy.api.wms.response.stockout.StockoutShipmentCountResponse">
        SELECT
            temp_table.STATUS,
            count(*) AS qty
        FROM
            (
                SELECT
                    ANY_VALUE ( stockout_shipment.STATUS ) AS STATUS
                FROM
                    stockout_shipment stockout_shipment
                    LEFT JOIN stockout_shipment_item stockout_shipment_item ON stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
                WHERE
                    ( stockout_shipment_item.is_deleted = 0 OR stockout_shipment_item.is_deleted IS NULL )
                GROUP BY
                    stockout_shipment.shipment_box_code,
                    stockout_shipment_item.stockout_order_no,
                    stockout_shipment_item.order_no
            ) temp_table
        GROUP BY
            temp_table.STATUS
    </select>
    <select id="getLogisticsTrackInfo" resultType="com.nsy.api.wms.domain.stockout.ShipmentLogisticsTrackExport">
        SELECT ss.logistics_company as logisticsCompany,
               ss.logistics_no,
               MIN(ss.delivery_date) as deliveryDate,
               ssi.order_no as orderNo,
               sri.country_code as countryCode,
               so.store_name as storeName,
               ss.forwarder_channel as forwarderChannel,
               sol.source as logisticsChannel
        FROM stockout_shipment ss
        INNER JOIN stockout_shipment_item ssi ON ssi.shipment_id = ss.shipment_id
        INNER JOIN stockout_order so ON so.stockout_order_no = ssi.stockout_order_no
        LEFT JOIN stockout_receiver_info sri ON sri.stockout_order_id = so.stockout_order_id
        LEFT JOIN stockout_order_label sol ON sol.stockout_order_id = so.stockout_order_id
        where ss.status = #{status} and ss.shipment_id IN
        <foreach collection="shipmentIdList" item="shipmentId" open="(" close=")" separator=",">
            #{shipmentId}
        </foreach>
        GROUP BY ss.logistics_company,ssi.order_no,ss.logistics_no,so.store_name,sri.country_code,sol.source
    </select>
    <select id="findShipmentItemListByStatus" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.forwarder_channel,
        stockoutShipment.shipment_id,
        stockoutShipment.box_size,
        stockoutShipment.stockout_type,
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.box_index as boxIndex,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipment.create_by,
        stockoutShipment.create_date,
        GROUP_CONCAT(distinct stockoutShipmentItem.order_no) as orderNo,
        stockoutShipmentItem.shipment_item_id,
        stockoutShipmentItem.spec_id,
        stockoutShipmentItem.sku,
        GROUP_CONCAT(distinct stockoutShipmentItem.batch_id) as batchId,
        sum(stockoutShipmentItem.qty) as qty,
        GROUP_CONCAT(distinct stockoutShipmentItem.stockout_order_no) as stockoutOrderNo,
        stockoutShipmentItem.is_deleted,
        amazonRelation.fba_shipment_id as fbaShipmentId,
        sote.delivery_code as deliveryCode,
        sote.delivery_batch_code as deliveryBatchCode,
        so.store_id,
        so.platform_name,
        so.is_urgent,
        so.store_name
        from
        stockout_shipment stockoutShipment
        left join
        stockout_shipment_item stockoutShipmentItem on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        left join stockout_shipment_amazon_relation amazonRelation on stockoutShipment.shipment_id = amazonRelation.shipment_id
        left join stockout_order so on so.stockout_order_no = stockoutShipmentItem.stockout_order_no
        left join stockout_order_tiktok_extend sote on sote.order_no = stockoutShipmentItem.order_no
        <where>
            stockoutShipment.shipment_id in
                <foreach collection="request.shipmentIds" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            <if test="request.statusList!=null and request.statusList.size() > 0">
                and stockoutShipment.status in
                <foreach collection="request.statusList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.isDeleted != null">
                and stockoutShipment.is_deleted = #{request.isDeleted} and stockoutShipmentItem.is_deleted = #{request.isDeleted}
            </if>
            <if test="request.fbaShipmentId!=null and request.fbaShipmentId != ''">
                and amazonRelation.fba_shipment_id = #{request.fbaShipmentId}
            </if>
            <if test="request.storeId!=null and request.storeId != ''">
                and so.store_id = #{request.storeId}
            </if>
        </where>
        group by stockoutShipment.shipment_id
    </select>

    <select id="getShipmentListToEdlSync" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentModel">
        SELECT
        s.*
        FROM
        stockout_shipment s
        WHERE
        s.`status` = 'SHIPPED'
        AND s.create_date &lt; DATE_FORMAT( NOW(), '%Y-%m-%d' )
        AND s.create_date &gt;= DATE_FORMAT(
        DATE_ADD( now(), INTERVAL #{day} DAY ),
        '%Y-%m-%d'
        )
        AND (s.logistics_no LIKE 'HM%' or s.logistics_no LIKE 'AS%' or s.logistics_no LIKE 'AQ%' or s.logistics_no LIKE
        'WNBAA%')
        AND s.logistics_company IN
        <foreach collection="logisticsCompanyList" item="logisticsCompany" open="(" close=")" separator=",">
            #{logisticsCompany}
        </foreach>
    </select>

    <select id="getShipmentListToYUNTUSync" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentModel">
        SELECT
        DISTINCT s.*
        FROM
        stockout_shipment s
        LEFT JOIN stockout_shipment_item stockoutShipmentItem on stockoutShipmentItem.shipment_id = s.shipment_id
	    LEFT JOIN stockout_order so on so.stockout_order_no = stockoutShipmentItem.stockout_order_no
        WHERE
        s.`status` = 'SHIPPED' and so.business_type = 'B2B'
        AND s.create_date &lt; DATE_FORMAT( NOW(), '%Y-%m-%d' )
        AND s.create_date &gt;= DATE_FORMAT(
        DATE_ADD( now(), INTERVAL #{day} DAY ),
        '%Y-%m-%d'
        )
        AND (s.logistics_no LIKE 'YT%')
        
        AND s.logistics_company IN
        <foreach collection="logisticsCompanyList" item="logisticsCompany" open="(" close=")" separator=",">
            #{logisticsCompany}
        </foreach>
    </select>

    <select id="getShipmentListToZHONGCHENGHANGSync" resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentModel">
        SELECT
        s.*
        FROM
        stockout_shipment s
        WHERE
        s.`status` = 'SHIPPED'
        AND s.create_date &lt; DATE_FORMAT( NOW(), '%Y-%m-%d' )
        AND s.create_date &gt;= DATE_FORMAT(
        DATE_ADD( now(), INTERVAL #{day} DAY ),
        '%Y-%m-%d'
        )
        AND (s.logistics_no LIKE 'GD%')
        AND s.logistics_company IN
        <foreach collection="logisticsCompanyList" item="logisticsCompany" open="(" close=")" separator=",">
            #{logisticsCompany}
        </foreach>
    </select>
    <select id="shipmentBoxSkuList" resultType="com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport">
            select DISTINCT stockout_shipment_item.order_no as orderNo,
            stockout_shipment.shipment_box_code as shipmentBoxCode,
            stockout_shipment_item.shipment_id as shipmentId,
            stockout_shipment_item.stockout_order_no as stockoutOrderNo,
            stockout_shipment_item.stockout_order_item_id as stockoutOrderItemId,
            stockout_shipment.box_index as boxIndex,
            pwc.name as customsDeclareCn,
            pwc.english_name as customsDeclareEn,
            product_customs_declare.hs_code as hsCode,
            stockout_shipment_item.sku as sku,
            product_spec_info.color as color,
            product_spec_info.size as size,
            product_spec_info.image_url as imageUrl,
            stockout_shipment_item.qty as qty,
            stockout_order_item.invoice_price as invoicePrice,
            stockout_shipment.weight as weight,
            stockout_shipment.volume_weight as volumeWeight,
            stockout_shipment.box_size as boxSize,
            product_spec_info.weight as unitWeight,
            product_info.spin_type as spinType,
            product_info.fabric_type as fabricType,
            product_info.fabric_type_en as fabricTypeEn,
            product_info.filler as filler,
            if(stockout_order_item.seller_sku is null or stockout_order_item.seller_sku = '',product_spec_info.sku,stockout_order_item.seller_sku) as storeSku,
            if(stockout_order_item.seller_barcode is null or stockout_order_item.seller_barcode = '', product_spec_info.barcode,stockout_order_item.seller_barcode) as storeBarcode,
            stockout_order_item.order_item_id as orderItemId,
            stockout_order_item_extend.asin as asin,
            product_customs_declare.element_value as elementValue,
            product_info.spu
            FROM
            stockout_shipment_item stockout_shipment_item
            LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
            LEFT JOIN stockout_order_item stockout_order_item ON stockout_order_item.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
            left join product_spec_info product_spec_info on product_spec_info.spec_id = stockout_shipment_item.spec_id
            left join product_info product_info on product_info.product_id = product_spec_info.product_id
            left join product_category_mapping pcm on product_info.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
            LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id and pwc.is_deleted = 0
            left join product_customs_declare product_customs_declare on product_customs_declare.product_id = product_spec_info.product_id
            LEFT JOIN stockout_customs_declare_order d on stockout_shipment_item.order_no = d.order_no and stockout_shipment_item.stockout_order_no = d.stockout_order_no
            LEFT JOIN stockout_customs_declare_document_item di on di.declare_order_id = d.declare_order_id
            LEFT JOIN stockout_customs_declare_document dd on dd.declare_document_id = di.declare_document_id
            LEFT JOIN stockout_order_item_extend stockout_order_item_extend on stockout_order_item_extend.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
        <where>
            <if test="shipmentIds != null and shipmentIds.size > 0 ">
                and stockout_shipment_item.shipment_id in
                <foreach collection="shipmentIds" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            and stockout_shipment_item.is_deleted = 0
            <if test="declareDocumentNo != null and declareDocumentNo.size > 0">
                and dd.declare_document_no in
                <foreach collection="declareDocumentNo" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by stockout_shipment_item.order_no, stockout_shipment.box_index asc
    </select>
    <select id="shipmentBoxSkuListOrderByFba" resultType="com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport">
        select DISTINCT stockout_shipment_item.order_no as orderNo,
        stockout_shipment.shipment_box_code as shipmentBoxCode,
        stockout_shipment_item.shipment_id as shipmentId,
        stockout_shipment_amazon_relation.fba_shipment_id as fbaShipmentId,
        stockout_shipment_item.stockout_order_no as stockoutOrderNo,
        stockout_shipment_item.stockout_order_item_id as stockoutOrderItemId,
        stockout_shipment.box_index as boxIndex,
        pwc.name as customsDeclareCn,
        pwc.english_name as customsDeclareEn,
        product_customs_declare.hs_code as hsCode,
        stockout_shipment_item.sku as sku,
        product_spec_info.color as color,
        product_spec_info.size as size,
        stockout_shipment_item.qty as qty,
        stockout_order_item.invoice_price as invoicePrice,
        stockout_shipment.weight as weight,
        stockout_shipment.volume_weight as volumeWeight,
        stockout_shipment.box_size as boxSize,
        product_spec_info.weight as unitWeight,
        product_info.spin_type as spinType,
        product_info.fabric_type as fabricType,
        product_info.filler as filler,
        if(stockout_order_item.seller_sku is null or stockout_order_item.seller_sku = '',product_spec_info.sku,stockout_order_item.seller_sku) as storeSku,
        if(stockout_order_item.seller_barcode is null or stockout_order_item.seller_barcode = '', product_spec_info.barcode,stockout_order_item.seller_barcode) as storeBarcode,
        stockout_order_item.order_item_id as orderItemId,
        stockout_order_item_extend.asin as asin,
        product_customs_declare.element_value as elementValue,
        product_info.spu
        FROM
        stockout_shipment_item stockout_shipment_item
        LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        LEFT JOIN stockout_order_item stockout_order_item ON stockout_order_item.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
        left join product_spec_info product_spec_info on product_spec_info.spec_id = stockout_shipment_item.spec_id
        left join product_info product_info on product_info.product_id = product_spec_info.product_id
        left join product_category_mapping pcm on product_info.category_id = pcm.category_id and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id and pwc.is_deleted = 0
        left join product_customs_declare product_customs_declare on product_customs_declare.product_id = product_spec_info.product_id
        LEFT JOIN stockout_customs_declare_order d on stockout_shipment_item.order_no = d.order_no and stockout_shipment_item.stockout_order_no = d.stockout_order_no
        LEFT JOIN stockout_customs_declare_document_item di on di.declare_order_id = d.declare_order_id
        LEFT JOIN stockout_customs_declare_document dd on dd.declare_document_id = di.declare_document_id
        LEFT JOIN stockout_order_item_extend stockout_order_item_extend on stockout_order_item_extend.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
        <where>
            <if test="shipmentIds != null and shipmentIds.size > 0 ">
                and stockout_shipment_item.shipment_id in
                <foreach collection="shipmentIds" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            and stockout_shipment_item.is_deleted = 0
            and stockout_shipment_amazon_relation.fba_shipment_id is not null
            <if test="declareDocumentNo != null and declareDocumentNo.size > 0">
                and dd.declare_document_no in
                <foreach collection="declareDocumentNo" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by stockout_shipment_amazon_relation.fba_shipment_id, stockout_shipment.box_index asc
    </select>
    <select id="pageSearchFBAShipmentIds" resultType="java.lang.Integer">
        select
        DISTINCT (stockout_shipment.shipment_id) as shipmentId
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
        </where>
        order by stockout_shipment_amazon_relation.fba_shipment_id, stockout_shipment.box_index
    </select>

    <select id="pageSearchFBAShipmentIdsOrderByShipmentId" resultType="java.lang.Integer">
        select
        DISTINCT (stockout_shipment.shipment_id) as shipmentId
        from stockout_shipment stockout_shipment
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
        </where>
        order by stockout_shipment.shipment_id
    </select>
    <select id="pageSearchFBAShipmentIdsCount" resultType="java.lang.Integer">
        select
        count(DISTINCT stockout_shipment.shipment_id)
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
        </where>
    </select>

    <select id="pageSearchFBAShipmentOrderNos" resultType="java.lang.String">
        select
        stockout_shipment_item.order_no
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
        </where>
        group by stockout_shipment_item.order_no
        order by max(stockout_shipment.box_index) desc
    </select>
    <select id="countQtyByIds" resultType="java.lang.Integer">
        SELECT
            ifnull(SUM(stockout_shipment_item.qty), 0)
        FROM
            stockout_shipment_item
        WHERE
            stockout_shipment_item.shipment_id in
        <foreach collection="records" separator="," index="index" item="eachOne" open="("
                 close=")">
            #{eachOne}
        </foreach>
        and stockout_shipment_item.is_deleted = 0
    </select>
    <select id="listIdsByPage" resultType="java.lang.Integer">
        select
            DISTINCT(stockout_shipment.shipment_id)
        from stockout_shipment stockout_shipment
        INNER JOIN stockout_shipment_item stockout_shipment_item ON stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        AND stockout_shipment_item.is_deleted != 1
        WHERE
        stockout_shipment.`create_date` >= '2022-09-01 00:00:00'
          AND stockout_shipment.`status` != 'PACKING'
          AND stockout_shipment.`is_deleted` = 0
          AND stockout_shipment.`stockout_type` IS NULL
        order by stockout_shipment.shipment_id desc
    </select>
    <select id="pageSearchFBAShipmentReferenceId" resultType="com.nsy.api.wms.domain.stockout.AmazonShipmentReferenceIdExport">
        select
        stockout_shipment_amazon_relation.fba_shipment_id,
        MAX(stockout_shipment.logistics_no) as logisticsNo,
        MAX(stockout_shipment_amazon_relation.amazon_reference_id) as amazonReferenceId,
        MAX(stockout_shipment_item.stockout_order_item_id) as stockoutOrderItemId,
        MAX(stockout_shipment_item.stockout_order_no) as stockoutOrderNo,
        MAX(stockout_shipment_item.order_no) as orderNo,
        MAX(stockout_shipment_amazon_relation.destination_fulfillment_center_id) as destinationFulfillmentCenterId,
        count(distinct stockout_shipment.shipment_id) as totalBox
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
            and stockout_shipment_amazon_relation.fba_shipment_id IS NOT NULL
        </where>
        group by stockout_shipment_amazon_relation.fba_shipment_id
        order by totalBox desc
    </select>
    <select id="pageSearchFBAShipmentReferenceIdCount" resultType="java.lang.Integer">
        select count(*) from (select
        count(stockout_shipment_amazon_relation.fba_shipment_id)
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
            and stockout_shipment_amazon_relation.fba_shipment_id IS NOT NULL
        </where>
        group by stockout_shipment_amazon_relation.fba_shipment_id) as temp
    </select>
    <select id="findTopStockoutOrderByShipmentId"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderEntity">
        select
            stockout_order.*
        from stockout_shipment_item stockout_shipment_item
        left join stockout_order stockout_order on stockout_shipment_item.stockout_order_no = stockout_order.stockout_order_no
        where stockout_shipment_item.shipment_id = #{id} and stockout_shipment_item.is_deleted = 0 limit 1
    </select>
    <select id="pageSearchForwarderChannel"
            resultType="com.nsy.api.wms.response.stockout.ForwarderAccountExportResponse">
        SELECT
        MAX( stockout_shipment.delivery_date ) AS deliveryDate,
        MAX( stockout_shipment_item.order_no ) AS orderNo,
        <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
            stockout_shipment_amazon_relation.fba_shipment_id AS fbaShipmentId,
        </if>
        MAX( stockout_shipment.forwarder_channel ) AS forwarderChannel,
        MAX( stockout_shipment.logistics_company ) AS logisticsCompany,
        MAX( stockout_shipment.logistics_no ) AS logisticsNo,
        COUNT( DISTINCT stockout_shipment.shipment_id ) AS totalBoxAmount,
        SUM( stockout_shipment_item.qty ) AS totalAmount,
        GROUP_CONCAT(DISTINCT stockout_shipment.shipment_id) as shipmentIds,
        MAX( stockout_order.store_name ) AS storeName,
        MAX( stockout_order.stockout_order_no ) AS stockoutOrderNo
        FROM
        stockout_shipment stockout_shipment
        <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
            LEFT JOIN stockout_shipment_amazon_relation stockout_shipment_amazon_relation ON stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        </if>
        LEFT JOIN stockout_shipment_item stockout_shipment_item ON stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        LEFT JOIN stockout_order stockout_order ON stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        <where>
            <include refid="shipmentPageWhere"></include>
            <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
                and stockout_shipment_amazon_relation.fba_shipment_id IS NOT NULL
            </if>
            <if test="query.workspace != null and query.workspace == 'B2C_BAG_AREA'">
                and stockout_shipment_item.order_no IS NOT NULL
            </if>
        </where>
        <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
            GROUP BY
            stockout_shipment_amazon_relation.fba_shipment_id
        </if>
        <if test="query.workspace != null and query.workspace == 'B2C_BAG_AREA'">
            GROUP BY
            stockout_shipment_item.order_no
        </if>
        order by deliveryDate
    </select>
    <select id="pageSearchForwarderChannelCount" resultType="java.lang.Integer">
        SELECT
        <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
            count(DISTINCT stockout_shipment_amazon_relation.fba_shipment_id)
        </if>
        <if test="query.workspace != null and query.workspace == 'B2C_BAG_AREA'">
            count(DISTINCT stockout_shipment_item.order_no)
        </if>
        FROM
        stockout_shipment stockout_shipment
        <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
            LEFT JOIN stockout_shipment_amazon_relation stockout_shipment_amazon_relation ON stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        </if>
        LEFT JOIN stockout_shipment_item stockout_shipment_item ON stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        LEFT JOIN stockout_order stockout_order ON stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        <where>
            <include refid="shipmentPageWhere"></include>
            <if test="query.workspace != null and query.workspace == 'FBA_AREA'">
                and stockout_shipment_amazon_relation.fba_shipment_id IS NOT NULL
            </if>
            <if test="query.workspace != null and query.workspace == 'B2C_BAG_AREA'">
                and stockout_shipment_item.order_no IS NOT NULL
            </if>
        </where>
    </select>


    <select id="pageSearchFBAShipmentSummary" resultType="com.nsy.api.wms.domain.stockout.AmazonShipmentSummaryExport">
        select
        stockout_shipment_amazon_relation.fba_shipment_id as fbaShipmentId,
        max(stockout_shipment.delivery_date) as deliveryDate,
        count(DISTINCT stockout_shipment.shipment_id) as boxNum,
        sum(stockout_shipment_item.qty) as qty,
        GROUP_CONCAT(DISTINCT stockout_shipment.forwarder_channel) as forwarderChannel,
        GROUP_CONCAT(DISTINCT stockout_shipment.logistics_no) as logisticsNo,
        GROUP_CONCAT(DISTINCT stockout_shipment_item.order_no) as orderNo,
        GROUP_CONCAT(DISTINCT stockout_shipment_amazon_relation.destination_fulfillment_center_id) as amazonSpaceName,
        GROUP_CONCAT(DISTINCT stockout_order.store_name) as storeName,
        GROUP_CONCAT(DISTINCT stockout_shipment.create_by) as createBy
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        <where>
            <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
                and stockout_shipment.shipment_box_code = #{query.shipmentBoxCode}
            </if>
            <if test="query.shipmentIdList != null and query.shipmentIdList.size() > 0">
                and stockout_shipment.shipment_id in
                <foreach collection="query.shipmentIdList" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.orderNos != null and query.orderNos.size() > 0">
                and stockout_shipment_item.order_no in
                <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query.status != null and query.status.size() > 0">
                and stockout_shipment.status in
                <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.storeName != null and query.storeName != ''">
                and stockout_order.store_id = #{query.storeName}
            </if>
            <if test="query.logisticNos != null and query.logisticNos.size() > 0">
                and stockout_shipment.logistics_no in
                <foreach collection="query.logisticNos" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.forwarderChannel != null and query.forwarderChannel != ''">
                and stockout_shipment.forwarder_channel = #{query.forwarderChannel}
            </if>
            <if test="query.deliveryDate != null">
                and stockout_shipment.delivery_date &gt;= #{query.deliveryDate}
            </if>
            <if test="query.deliveryDateEnd != null">
                and stockout_shipment.delivery_date &lt; #{query.deliveryDateEnd}
            </if>
            <if test="query.shipmentDateBegin!=null">
                and stockout_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
            </if>
            <if test="query.shipmentDateEnd!=null">
                and stockout_shipment.shipment_date &lt; #{query.shipmentDateEnd}
            </if>
            <if test="query.fbaShipmentIdList != null and query.fbaShipmentIdList.size() > 0">
                and stockout_shipment_amazon_relation.fba_shipment_id in
                <foreach collection="query.fbaShipmentIdList" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            and stockout_shipment.stockout_type = 'FIRST_LEG_DELIVERY'
            and stockout_shipment.is_deleted = 0
            and stockout_shipment_amazon_relation.fba_shipment_id is not null
        </where>
        group by stockout_shipment_amazon_relation.fba_shipment_id
    </select>

    <select id="pageSearchFBAShipmentSummaryWeight" resultType="com.nsy.api.wms.domain.stockout.AmazonShipmentSummaryWeight">
        SELECT
               stockout_shipment_amazon_relation.fba_shipment_id,
               sum(stockout_shipment.weight) as weight
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        where stockout_shipment_amazon_relation.fba_shipment_id in
        <foreach collection="fbaShipmentIds" separator="," index="index" item="eachOne" open="("
                 close=")">
            #{eachOne}
        </foreach>
        group by stockout_shipment_amazon_relation.fba_shipment_id
    </select>


    <select id="countSearchFBAShipmentSummary" resultType="java.lang.Integer">
        select
        count(distinct stockout_shipment_amazon_relation.fba_shipment_id) as fbaShipmentId
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
                and stockout_shipment.shipment_box_code = #{query.shipmentBoxCode}
            </if>
            <if test="query.shipmentIdList != null and query.shipmentIdList.size() > 0">
                and stockout_shipment.shipment_id in
                <foreach collection="query.shipmentIdList" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.orderNos != null and query.orderNos.size() > 0">
                and stockout_shipment_item.order_no in
                <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query.status != null and query.status.size() > 0">
                and stockout_shipment.status in
                <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.storeName != null and query.storeName != ''">
                and stockout_order.store_id = #{query.storeName}
            </if>
            <if test="query.logisticNos != null and query.logisticNos.size() > 0">
                and stockout_shipment.logistics_no in
                <foreach collection="query.logisticNos" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            <if test="query.forwarderChannel != null and query.forwarderChannel != ''">
                and stockout_shipment.forwarder_channel = #{query.forwarderChannel}
            </if>
            <if test="query.deliveryDate != null">
                and stockout_shipment.delivery_date &gt;= #{query.deliveryDate}
            </if>
            <if test="query.deliveryDateEnd != null">
                and stockout_shipment.delivery_date &lt; #{query.deliveryDateEnd}
            </if>
            <if test="query.shipmentDateBegin!=null">
                and stockout_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
            </if>
            <if test="query.shipmentDateEnd!=null">
                and stockout_shipment.shipment_date &lt; #{query.shipmentDateEnd}
            </if>
            <if test="query.fbaShipmentIdList != null and query.fbaShipmentIdList.size() > 0">
                and stockout_shipment_amazon_relation.fba_shipment_id in
                <foreach collection="query.fbaShipmentIdList" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            and stockout_shipment.stockout_type = 'FIRST_LEG_DELIVERY'
            and stockout_shipment.is_deleted = 0
        </where>
    </select>

    <select id="searchShipmentSummaryByFbaShipmentId" resultType="com.nsy.api.wms.domain.stockout.AmazonShipmentSummaryExport">
        select
                stockout_shipment.shipment_id as shipmentId,
                stockout_shipment.box_size as boxSize,
                stockout_shipment.volume_weight as volumeWeight,
                stockout_shipment.logistics_company as logisticsCompany,
                stockout_shipment_amazon_relation.fba_shipment_id as fbaShipmentId
                from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation
        on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        where stockout_shipment_amazon_relation.fba_shipment_id in
        <foreach collection="fbaShipmentIds" separator="," index="index" item="eachOne" open="("
                 close=")">
            #{eachOne}
        </foreach>
        <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
            and stockout_shipment.shipment_box_code = #{query.shipmentBoxCode}
        </if>
        <if test="query.shipmentIdList != null and query.shipmentIdList.size() > 0">
            and stockout_shipment.shipment_id in
            <foreach collection="query.shipmentIdList" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.orderNos != null and query.orderNos.size() > 0">
            and stockout_shipment_item.order_no in
            <foreach collection="query.orderNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
            and stockout_shipment_item.stockout_order_no = #{query.stockoutOrderNo}
        </if>
        <if test="query.status != null and query.status.size() > 0">
            and stockout_shipment.status in
            <foreach collection="query.status" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.storeName != null and query.storeName != ''">
            and stockout_order.store_id = #{query.storeName}
        </if>
        <if test="query.logisticNos != null and query.logisticNos.size() > 0">
            and stockout_shipment.logistics_no in
            <foreach collection="query.logisticNos" separator="," index="index" item="eachOne" open="("
                     close=")">
                #{eachOne}
            </foreach>
        </if>
        <if test="query.forwarderChannel != null and query.forwarderChannel != ''">
            and stockout_shipment.forwarder_channel = #{query.forwarderChannel}
        </if>
        <if test="query.deliveryDate != null">
            and stockout_shipment.delivery_date &gt;= #{query.deliveryDate}
        </if>
        <if test="query.deliveryDateEnd != null">
            and stockout_shipment.delivery_date &lt; #{query.deliveryDateEnd}
        </if>
        <if test="query.shipmentDateBegin!=null">
            and stockout_shipment.shipment_date &gt;= #{query.shipmentDateBegin}
        </if>
        <if test="query.shipmentDateEnd!=null">
            and stockout_shipment.shipment_date &lt; #{query.shipmentDateEnd}
        </if>
        and stockout_shipment.stockout_type = 'FIRST_LEG_DELIVERY'
        and stockout_shipment.is_deleted = 0
        group by stockout_shipment_amazon_relation.fba_shipment_id,stockout_shipment.shipment_id
    </select>

    <select id="getStockOutShipmentBoxNumList" resultType="com.nsy.api.wms.domain.stockout.StockOutShipmentPrintDTO">
        select distinct t.box_index as boxNum,f.fba_shipment_id,ti.order_no from stockout_shipment t
        inner join stockout_shipment_item ti on ti.shipment_id = t.shipment_id
		left join stockout_shipment_amazon_relation f on f.shipment_id = t.shipment_id
		where t.shipment_id in  (
				select DISTINCT(shipment_id) from stockout_shipment_item where stockout_order_no =#{stockOutOderNo}
				)
		order by box_index
    </select>
    <select id="countWeightByIds" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(weight), 0) AS total_weight
        FROM
        stockout_shipment stockout_shipment
        WHERE
        stockout_shipment.shipment_id in
        <foreach collection="records" separator="," index="index" item="eachOne" open="("
                 close=")">
            #{eachOne}
        </foreach>
    </select>
    <select id="findAllShippedExportForCustomer"
            resultType="com.nsy.api.wms.domain.stockout.ShipmentAllShippedExport">
        select stockout_shipment_item.order_no as orderNo,
        stockout_shipment_item.sku as sku,
        product_spec_info.color as color,
        product_spec_info.size as size,
        product_spec_info.barcode as barcode,
        stockout_order_item.seller_sku as storeSku,
        stockout_order_item.seller_barcode as storeBarcode,
        stockout_shipment_item.qty as qty,
        stockout_order_item.invoice_price as invoicePrice,
        stockout_receiver_info.receiver_info as receiverInfo,
        stockout_shipment.box_index as boxIndex,
        stockout_shipment.logistics_company as logisticsCompany,
        stockout_shipment.logistics_no as logisticsNo,
        stockout_shipment.weight as weight,
        stockout_shipment.volume_weight as volumeWeight,
        stockout_shipment.box_size as boxSize,
        stockout_order_item.seller_memo as sellerMemo,
        stockout_shipment.transfer_logistics_no as transferLogisticsNo,
        stockout_shipment.delivery_date as deliveryDate,
        stockout_order.store_name as storeName
        from stockout_shipment stockout_shipment
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        left join product_spec_info product_spec_info on product_spec_info.spec_id = stockout_shipment_item.spec_id
        left join stockout_order_item stockout_order_item on stockout_order_item.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
        left join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        left join stockout_receiver_info stockout_receiver_info on stockout_receiver_info.stockout_order_id = stockout_order.stockout_order_id
        <where>
            <if test="shipmentIds != null and shipmentIds.size() > 0">
                and stockout_shipment.shipment_id in
                <foreach collection="shipmentIds" separator="," index="index" item="eachOne" open="("
                         close=")">
                    #{eachOne}
                </foreach>
            </if>
            and stockout_shipment.status = 'SHIPPED' and stockout_shipment_item.is_deleted = 0
        </where>
    </select>
    <select id="countWaitToShippedShipment" resultType="java.lang.Integer">
        select count(stockout_shipment.shipment_id)
        from stockout_shipment stockout_shipment
                 left join stockout_shipment_item stockout_shipment_item
                           on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        where stockout_shipment_item.stockout_order_no = #{stockOutOderNo}
          and stockout_shipment.is_deleted = 0
          and stockout_shipment_item.is_deleted = 0
          and stockout_shipment.status != 'SHIPPED'
    </select>
    <select id="getReplenishShipment" resultType="com.nsy.api.wms.response.stockout.StockoutReplenishResponse">
        select
        s.shipment_id,
        s.shipment_box_code,
        s.logistics_company,
        s.create_by,
        any_value(si.space_area_name) as spaceAreaName,
        s.box_index as boxIndex,
        sum(si.qty) as qty,
        o.store_name,
        s.product_size_segment
        from stockout_shipment s
        inner join stockout_shipment_item si
        on s.shipment_id = si.shipment_id
        inner join stockout_order o
        on o.stockout_order_no = si.stockout_order_no
        <if test="query.spaceIds != null and query.spaceIds.size() > 0">
        inner join bd_space_area sa
        on sa.space_area_id = si.space_area_id
        </if>
        where s.replenish_order_status = 'WAIT_DEAL'
        and s.is_deleted = 0
        and si.is_deleted = 0
        <if test="query.logisticsCompany != null and query.logisticsCompany!='' ">
            and s.logistics_company = #{query.logisticsCompany}
        </if>
        <if test="query.productSizeSegment != null and query.productSizeSegment!='' ">
            and s.product_size_segment = #{query.productSizeSegment}
        </if>
        <if test="query.createBy != null and query.createBy!='' ">
            and s.create_by = #{query.createBy}
        </if>
        <if test="query.spaceAreaIds != null and query.spaceAreaIds.size() > 0">
            and si.space_area_id in
            <foreach collection="query.spaceAreaIds" separator="," index="index" item="spaceAreaId" open="("
                     close=")">
                #{spaceAreaId}
            </foreach>
        </if>
        <if test="query.spaceIds != null and query.spaceIds.size() > 0">
            and sa.space_id in
            <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="("
                     close=")">
                #{spaceId}
            </foreach>
        </if>
        group by s.shipment_id
        order by si.space_area_id,s.logistics_company,s.box_index
    </select>
    <select id="getReplenishShipmentCreateBySelect" resultType="java.lang.String">
        select
        distinct s.create_by
        from stockout_shipment s
        where s.replenish_order_status = 'WAIT_DEAL'
        and s.is_deleted = 0

    </select>

    <select id="searchShipmentItemsCount" resultType="java.lang.Integer">
        select
        count(*)
        from stockout_shipment stockout_shipment
        left join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        <if test="(query.source != null and query.source != '') or (query.isPrint != null) or (query.shipperName != null and query.shipperName != '') or (query.workspace != null and query.workspace != '') or (query.storeName != null and query.storeName != '') or (query.spaceIds != null and query.spaceIds.size() > 0) or (query.businessType != null and query.businessType != '')">
            left join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <if test="(query.source != null and query.source != '') or query.isPrint != null">
            left join stockout_order_label stockout_order_label on stockout_order_label.stockout_order_id = stockout_order.stockout_order_id
        </if>
        <if test="(query.uploadFalian != null and query.uploadFalian != '') or (query.uploadInvoice != null and query.uploadInvoice != '')">
            left join stockout_shipment_logistics_info shipment_logistics_info ON shipment_logistics_info.logistics_no = stockout_shipment.logistics_no
        </if>
        <if test="query.shipperName != null and query.shipperName != ''">
            left join stockout_shipper_info shipper_info on stockout_order.stockout_order_id = shipper_info.stockout_order_id
        </if>
        <if test="query.receiverName != null and query.receiverName != ''">
            left join stockout_receiver_info stockout_receiver_info on stockout_shipment_item.stockout_order_no = stockout_receiver_info.stockout_order_no
        </if>
        <where>
            <include refid="shipmentPageWhere"></include>
        </where>
        order by stockout_shipment.shipment_date
    </select>
    <select id="listByStockinOrderNo"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity">
        select distinct s.* from stockout_shipment s
        inner join stockout_shipment_item si
        on s.shipment_id = si.shipment_id
        where s.is_deleted = 0
        and si.is_deleted = 0
        and si.stockout_order_no in
        <foreach collection="stockoutOrderNoList" separator="," index="index" item="stockoutOrderNo" open="("
                 close=")">
            #{stockoutOrderNo}
        </foreach>
    </select>
    <select id="listByOrderNo"
            resultType="com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity">
        select distinct s.* from stockout_shipment s
        inner join stockout_shipment_item si
        on s.shipment_id = si.shipment_id
        where s.is_deleted = 0
        and si.is_deleted = 0
        and si.order_no in
        <foreach collection="orderNoList" separator="," index="index" item="orderNo" open="("
                 close=")">
            #{orderNo}
        </foreach>
        order by s.box_index
    </select>
    <select id="pageSearchFBAShipmentSku" resultType="com.nsy.api.wms.domain.stockout.AmazonShipmentSkuExport">
        SELECT
        stockout_shipment.logistics_company,
        stockout_shipment.forwarder_channel,
        stockout_shipment.replenish_order,
        stockout_order.store_name,
        stockout_shipment_amazon_relation.fba_shipment_id,
        stockout_shipment_item.order_no,
        stockout_shipment_item.sku,
        product_info.package_vacuum as packageName,
        stockout_shipment_item.qty,
        stockout_shipment_item.stockout_order_no,
        stockout_shipment.logistics_no,
        stockout_shipment.create_by,
        stockout_shipment.create_date,
        stockout_shipment.shipment_date,
        stockout_shipment.delivery_date,
        stockout_receiver_info.receiver_name as receiveName,
        '亚马逊仓库' as receiveAddress
        FROM
        stockout_shipment_item stockout_shipment_item
        LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        LEFT JOIN stockout_order stockout_order on stockout_shipment_item.stockout_order_no = stockout_order.stockout_order_no
        LEFT JOIN stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id  = stockout_shipment_item.shipment_id
        LEFT JOIN stockout_receiver_info stockout_receiver_info on stockout_receiver_info.stockout_order_no = stockout_order.stockout_order_no
        LEFT JOIN product_spec_info product_spec_info on product_spec_info.sku = stockout_shipment_item.sku
        LEFT JOIN product_info product_info on product_info.product_id = product_spec_info.product_id
        <where>
            <include refid="shipmentFBAPageWhere"></include>
            and stockout_shipment_item.is_deleted = 0
        </where>
        order by stockout_shipment_item.shipment_item_id
    </select>
    <select id="pageSearchFBAShipmentSkuCount" resultType="java.lang.Integer">
        select
        count(1)
        from nsy_wms.stockout_shipment_item stockout_shipment_item
        left join stockout_shipment stockout_shipment on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
            and stockout_shipment_item.is_deleted = 0
        </where>
    </select>
    <select id="searchAllFBAShipmentIds" resultType="java.lang.String">
        select
        DISTINCT (stockout_shipment_amazon_relation.fba_shipment_id) as fbaShipmentId
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
            and stockout_shipment_amazon_relation.fba_shipment_id is not null
        </where>
    </select>
    <select id="shipmentBoxSkuListOrderByStockoutOrderNo"
            resultType="com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport">
        select DISTINCT stockout_shipment_item.order_no as orderNo,
        stockout_shipment.shipment_box_code as shipmentBoxCode,
        stockout_shipment_item.shipment_id as shipmentId,
        stockout_shipment_amazon_relation.fba_shipment_id as fbaShipmentId,
        stockout_shipment_item.stockout_order_no as stockoutOrderNo,
        stockout_shipment_item.stockout_order_item_id as stockoutOrderItemId,
        stockout_shipment.box_index as boxIndex,
        pwc.name as customsDeclareCn,
        pwc.english_name as customsDeclareEn,
        product_customs_declare.hs_code as hsCode,
        stockout_shipment_item.sku as sku,
        product_spec_info.color as color,
        product_spec_info.size as size,
        product_spec_info.image_url as imageUrl,
        stockout_shipment_item.qty as qty,
        stockout_order_item.invoice_price as invoicePrice,
        stockout_shipment.weight as weight,
        stockout_shipment.volume_weight as volumeWeight,
        stockout_shipment.box_size as boxSize,
        product_spec_info.weight as unitWeight,
        product_info.spin_type as spinType,
        product_info.fabric_type as fabricType,
        product_info.filler as filler,
        if(stockout_order_item.seller_sku is null or stockout_order_item.seller_sku = '',product_spec_info.sku,stockout_order_item.seller_sku) as storeSku,
        if(stockout_order_item.seller_barcode is null or stockout_order_item.seller_barcode = '', product_spec_info.barcode,stockout_order_item.seller_barcode) as storeBarcode,
        stockout_order_item.order_item_id as orderItemId,
        stockout_order_item_extend.asin as asin,
        product_customs_declare.element_value as elementValue,
        product_info.spu
        FROM
        stockout_shipment_item stockout_shipment_item
        LEFT JOIN stockout_shipment stockout_shipment on stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        LEFT JOIN stockout_order_item stockout_order_item ON stockout_order_item.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
        left join product_spec_info product_spec_info on product_spec_info.spec_id = stockout_shipment_item.spec_id
        left join product_info product_info on product_info.product_id = product_spec_info.product_id
        left join product_category_mapping pcm on product_info.category_id = pcm.category_id  and pcm.declaration_type = 'GRANULAR_DECLARATION'
        LEFT JOIN product_wms_category pwc ON pwc.wms_category_id = pcm.wms_category_id and pwc.is_deleted = 0
        left join product_customs_declare product_customs_declare on product_customs_declare.product_id = product_spec_info.product_id
        LEFT JOIN stockout_customs_declare_order d on stockout_shipment_item.order_no = d.order_no and stockout_shipment_item.stockout_order_no = d.stockout_order_no
        LEFT JOIN stockout_customs_declare_document_item di on di.declare_order_id = d.declare_order_id
        LEFT JOIN stockout_customs_declare_document dd on dd.declare_document_id = di.declare_document_id
        LEFT JOIN stockout_order_item_extend stockout_order_item_extend on stockout_order_item_extend.stockout_order_item_id = stockout_shipment_item.stockout_order_item_id
        <where>
            <if test="shipmentIds != null and shipmentIds.size > 0 ">
                and stockout_shipment_item.shipment_id in
                <foreach collection="shipmentIds" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
            and stockout_shipment_item.is_deleted = 0
        </where>
        order by stockout_shipment_item.stockout_order_no, stockout_shipment.box_index asc
    </select>
    <select id="findShipmentConfirmItemListByStockoutOrderNoList"
            resultType="com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult">
        select
        stockoutShipment.forwarder_channel,
        stockoutShipment.shipment_id,
        stockoutShipment.box_size,
        stockoutShipment.stockout_type,
        stockoutShipment.shipment_box_code as shipmentBoxCode,
        stockoutShipment.logistics_company as logisticsCompany,
        stockoutShipment.box_index as boxIndex,
        stockoutShipment.status as status,
        stockoutShipment.weight as weight,
        stockoutShipment.volume_weight as volumeWeight,
        stockoutShipment.logistics_label_size as logisticsLabelSize,
        stockoutShipment.remark as remark,
        stockoutShipment.create_by,
        stockoutShipment.create_date,
        stockoutShipmentItem.shipment_item_id,
        stockoutShipmentItem.spec_id,
        stockoutShipmentItem.sku,
        stockoutShipmentItem.batch_id as batchId,
        stockoutShipmentItem.qty as qty,
        stockoutShipmentItem.stockout_order_no as stockoutOrderNo,
        stockoutShipmentItem.is_deleted,
        stockoutShipmentItem.stockout_order_item_id as stockoutOrderItemId
        from
        stockout_shipment stockoutShipment
        inner join
        stockout_shipment_item stockoutShipmentItem on stockoutShipmentItem.shipment_id = stockoutShipment.shipment_id
        where stockoutShipment.is_deleted = 0
          and stockoutShipmentItem.is_deleted = 0
        <if test="stockoutOrderNoList != null and stockoutOrderNoList.size > 0 ">
            and stockoutShipmentItem.stockout_order_no in
            <foreach collection="stockoutOrderNoList" item="outOrderNo" open="(" close=")" separator=",">
                #{outOrderNo}
            </foreach>
        </if>
    </select>

    <select id="pageSearchAmazonShipmentIdInfo" resultType="com.nsy.api.wms.domain.stockout.AmazonShipmentIdInfoExport">
        select
        stockout_shipment_amazon_relation.fba_shipment_id,
		group_concat(distinct stockout_shipment.logistics_company) as logisticsCompany,
        group_concat(distinct stockout_shipment_amazon_relation.destination_fulfillment_center_id) as destinationFulfillmentCenterId,
		group_concat(distinct stockout_shipment.logistics_no) as logisticsNo,
        sum(stockout_shipment_item.qty) as totalQty,
        count(distinct stockout_shipment_item.shipment_id) as totalBox,
        group_concat(distinct stockout_shipment_item.stockout_order_no) as stockoutOrderNos,
        group_concat(distinct stockout_shipment.shipment_id) as shipmentIds
        from stockout_shipment stockout_shipment
        left join stockout_shipment_amazon_relation stockout_shipment_amazon_relation on stockout_shipment_amazon_relation.shipment_id = stockout_shipment.shipment_id
        inner join stockout_shipment_item stockout_shipment_item on stockout_shipment_item.shipment_id = stockout_shipment.shipment_id AND stockout_shipment_item.is_deleted = 0
        <if test="query.storeName != null and query.storeName != ''">
            inner join stockout_order stockout_order on stockout_order.stockout_order_no = stockout_shipment_item.stockout_order_no
        </if>
        <where>
            <include refid="shipmentFBAPageWhere"></include>
            and stockout_shipment_amazon_relation.fba_shipment_id IS NOT NULL
        </where>
        group by stockout_shipment_amazon_relation.fba_shipment_id
        order by totalBox desc
    </select>

    <select id="countSkuWeightByShipmentBoxCode"  resultType="java.math.BigDecimal">
        SELECT
        sum(Ifnull(s.actual_weight,0) * qty /1000) AS weight
        FROM stockout_shipment_item item
        LEFT JOIN stockout_shipment t ON t.shipment_id = item.shipment_id
        LEFT JOIN product_spec_info s ON s.spec_id = item.spec_id
        <where>
            t.shipment_box_code = #{shipmentBoxCode}
            and item.is_deleted = 0 and t.is_deleted = 0
        </where>
    </select>
</mapper>
