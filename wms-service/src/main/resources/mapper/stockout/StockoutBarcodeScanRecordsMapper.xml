<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutBarcodeScanRecordsMapper">

    <select id="searchPage" resultType="com.nsy.api.wms.response.stockout.StockoutBarcodeScanRecordsPageResponse">
        SELECT
        sbsr.*,COALESCE(sum(si.qty), 0) as deliveryQty
        FROM
        stockout_barcode_scan_records sbsr
        left join stockout_order s on sbsr.stockout_order_no = s.stockout_order_no
        left join stockout_order_item si on si.stockout_order_id = s.stockout_order_id and sbsr.order_no = si.order_no
        <include refid="whereCondition"></include>
        group by sbsr.id
        order by sbsr.operate_time desc
    </select>

    <sql  id="whereCondition">
        <where>
            <if test="query!=null and query.isFuzzy == 1 and  query.orderNo != null and query.orderNo != ''">
                AND sbsr.order_no like concat('%',#{query.orderNo},'%')
            </if>
            <if test="query!=null and query.isFuzzy == 0 and query.orderNo != null and query.orderNo != ''">
                AND sbsr.order_no = #{query.orderNo}
            </if>
            <if test="query.businessType != null and query.businessType != ''">
                and sbsr.business_type = #{query.businessType}
            </if>
            <if test="query.shipmentBoxCode != null and query.shipmentBoxCode != ''">
                and sbsr.shipment_box_code = #{query.shipmentBoxCode}
            </if>
            <if test="query!=null and query.isFuzzy == 1 and query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                and sbsr.stockout_order_no like concat('%',#{query.stockoutOrderNo},'%')
            </if>
            <if test="query!=null and query.isFuzzy == 0 and query.stockoutOrderNo != null and query.stockoutOrderNo != ''">
                and sbsr.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query!=null and query.isFuzzy == 1 and  query.logisticsNo != null and query.logisticsNo != ''">
                and sbsr.logistics_no like concat('%',#{query.logisticsNo},'%')
            </if>
            <if test="query!=null and query.isFuzzy == 0 and  query.logisticsNo != null and query.logisticsNo != ''">
                and sbsr.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query.platformName != null and query.platformName != ''">
                and sbsr.platform_name = #{query.platformName}
            </if>
            <if test="query.operateTimeStart != null">
                and sbsr.operate_time &gt;= #{query.operateTimeStart}
            </if>
            <if test="query.operateTimeEnd != null">
                and sbsr.operate_time &lt;= #{query.operateTimeEnd}
            </if>
            <if test="query!=null and query.isFuzzy == 0 and  query.operateName != null and query.operateName != ''">
                and sbsr.operate_name = #{query.operateName}
            </if>
            <if test="query!=null and query.isFuzzy == 1 and  query.operateName != null and query.operateName != ''">
                and sbsr.operate_name  like concat('%',#{query.operateName},'%')
            </if>
        </where>
    </sql>
</mapper>
