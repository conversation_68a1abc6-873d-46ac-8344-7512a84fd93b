package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.domain.stockout.FaireShipmentListExport;
import com.nsy.api.wms.domain.stockout.StockoutFaireShipmentSearchResult;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutFaireShipmentSpecEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutFaireShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentIndex;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentOpClearRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentOpMergeRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentScanRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentShipRequest;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentUpdateRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StockoutFaireMergeShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentScanItemResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentScanResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.user.upload.StockoutShipmentLogisticsNoImport;
import com.nsy.wms.business.service.async.AsyncProcessFlowService;
import com.nsy.wms.business.service.bd.BdBoxSpecificationsService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.building.StockoutShipmentBuilding;
import com.nsy.wms.business.service.stockout.ship.StockoutShipService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutFaireShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutFaireShipmentMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutFaireShipmentService extends ServiceImpl<StockoutFaireShipmentMapper, StockoutFaireShipmentEntity> implements IDownloadService {

    @Autowired
    StockoutFaireShipmentItemService faireShipmentItemService;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    StockoutShipmentService stockoutShipmentService;
    @Autowired
    StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    StockoutShipService stockoutShipService;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    BdSpaceService bdSpaceService;
    @Autowired
    BdBoxSpecificationsService specificationsService;
    @Resource
    AsyncProcessFlowService asyncProcessFlowService;

    /**
     * Faire装箱清单列表
     */
    public PageResponse<StockoutFaireShipmentSearchResult> searchList(StockoutFaireShipmentSearchRequest request) {
        buildSearchRequest(request);
        PageResponse<StockoutFaireShipmentSearchResult> pageResponse = new PageResponse<>();
        pageResponse.setContent(new LinkedList<>());
        Page<StockoutFaireShipmentSearchRequest> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<Integer> pageResult = this.getBaseMapper().pageSearchShipmentIds(page, request);
        pageResponse.setTotalCount(this.getBaseMapper().pageSearchShipmentIdsCount(request));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return pageResponse;
        }
        List<StockoutFaireShipmentItemEntity> faireShipmentItems = faireShipmentItemService.findByFaireShipmentIdList(pageResult.getRecords());
        Map<Integer, String> spaceMap = spaceService.getNameSelect().stream().collect(Collectors.toMap(SelectIntegerModel::getValue, SelectIntegerModel::getLabel));
        // 每个装箱对应的明细
        Map<Integer, List<StockoutFaireShipmentItemEntity>> itemMap = faireShipmentItems.stream().collect(Collectors.groupingBy(StockoutFaireShipmentItemEntity::getFaireShipmentId));

        pageResult.getRecords().forEach(faireShipmentId -> {
            StockoutFaireShipmentEntity faireShipmentEntity = getById(faireShipmentId);
            StockoutFaireShipmentSearchResult item = new StockoutFaireShipmentSearchResult();
            BeanUtilsEx.copyProperties(faireShipmentEntity, item);
            item.setStatus(StockoutFaireShipmentStatusEnum.getDesByName(item.getStatus()));
            item.setBoxSpec(StockoutFaireShipmentSpecEnum.getDesByName(item.getBoxSpec()));
            item.setSpaceName(spaceMap.get(item.getSpaceId()));
            item.setWorkspace(StockoutOrderWorkSpaceEnum.getNameBy(item.getWorkspace()));
            item.setBoxSize(faireShipmentEntity.getBoxSize());
            List<StockoutFaireShipmentItemEntity> itemEntityList;
            if (faireShipmentEntity.getIsMerge().equals(1)) {
                itemEntityList = faireShipmentItemService.getSubItemList(faireShipmentId);
            } else {
                itemEntityList = itemMap.get(faireShipmentId);
            }
            if (!CollectionUtils.isEmpty(itemEntityList)) {
                item.setOrderNoList(itemEntityList.stream().map(StockoutFaireShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList()));
                item.setStockoutOrderNoList(itemEntityList.stream().map(StockoutFaireShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList()));
                pageResponse.getContent().add(item);
            }
        });
        return pageResponse;
    }

    /**
     * 处理请求参数
     */
    private void buildSearchRequest(StockoutFaireShipmentSearchRequest request) {
        if (StringUtils.hasText(request.getBoxCode())) {
            if (CollectionUtils.isEmpty(request.getBoxCodes())) {
                request.setBoxCodes(Collections.singletonList(request.getBoxCode()));
            } else {
                request.getBoxCodes().add(request.getBoxCode());
            }
        }
        if (StringUtils.hasText(request.getOrderNo())) {
            if (CollectionUtils.isEmpty(request.getOrderNos())) {
                request.setOrderNos(Collections.singletonList(request.getOrderNo()));
            } else {
                request.getOrderNos().add(request.getOrderNo());
            }
        }
        if (StringUtils.hasText(request.getStockoutOrderNo())) {
            if (CollectionUtils.isEmpty(request.getStockoutOrderNos())) {
                request.setStockoutOrderNos(Collections.singletonList(request.getStockoutOrderNo()));
            } else {
                request.getStockoutOrderNos().add(request.getStockoutOrderNo());
            }
        }
        if (StringUtils.hasText(request.getStatusSingle())) {
            if (CollectionUtils.isEmpty(request.getStatus())) {
                request.setStatus(Collections.singletonList(request.getStatusSingle()));
            } else {
                request.getStatus().add(request.getStatusSingle());
            }
        }
        // 订单号|出库单号 明细维度的查询，需转换成 主表idList
        if (!CollectionUtils.isEmpty(request.getOrderNos())) {
            List<Integer> faireIdList = getIdListByOrderNoOrStockoutOrderNo(request.getOrderNos(), null);
            if (CollectionUtils.isEmpty(request.getIdList())) {
                request.setIdList(faireIdList);
            } else {
                request.getIdList().addAll(faireIdList);
            }
        }
        if (!CollectionUtils.isEmpty(request.getStockoutOrderNos())) {
            List<Integer> faireIdList = getIdListByOrderNoOrStockoutOrderNo(null, request.getStockoutOrderNos());
            if (CollectionUtils.isEmpty(request.getIdList())) {
                request.setIdList(faireIdList);
            } else {
                request.getIdList().addAll(faireIdList);
            }
        }
        if (!CollectionUtils.isEmpty(request.getIdList()))
            request.setIdList(request.getIdList().stream().distinct().collect(Collectors.toList()));
    }

    private List<Integer> getIdListByOrderNoOrStockoutOrderNo(List<String> orderNos, List<String> stockoutOrderNos) {
        LambdaQueryWrapper<StockoutFaireShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(orderNos)) {
            queryWrapper.in(StockoutFaireShipmentItemEntity::getOrderNo, orderNos);
        }
        if (!CollectionUtils.isEmpty(stockoutOrderNos)) {
            queryWrapper.in(StockoutFaireShipmentItemEntity::getStockoutOrderNo, stockoutOrderNos);
        }
        List<Integer> faireIdList = faireShipmentItemService.list(queryWrapper).stream().map(StockoutFaireShipmentItemEntity::getFaireShipmentId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(faireIdList)) {
            return Collections.singletonList(-1);
        }
        List<StockoutFaireShipmentEntity> faireShipmentEntities = this.listByIds(faireIdList);
        List<Integer> idList = faireShipmentEntities.stream().map(StockoutFaireShipmentEntity::getId).distinct().collect(Collectors.toList());
        List<Integer> mergeFaireShipmentId = faireShipmentEntities.stream().filter(o -> o.getMergeFaireShipmentId() != null && o.getMergeFaireShipmentId() > 0).map(StockoutFaireShipmentEntity::getMergeFaireShipmentId).distinct().collect(Collectors.toList());
        if (!CollUtil.isEmpty(mergeFaireShipmentId)) {
            idList.addAll(mergeFaireShipmentId);
        }
        return idList;
    }

    /**
     * 批次发货
     * 1.状态变化，装箱完成->待发货
     * 2.子箱处理
     * 3.装箱清单处理
     */
    @Transactional
    @JLock(keyConstant = "batchShip", lockKey = "#request.shipBatchId")
    public void batchShipV2(StockoutFaireShipmentShipRequest request) {
        validBatchShip(request);
        //当前批次存在的打印顺序最大值
        StockoutFaireShipmentEntity faireMaxShipmentEntity = this.getBaseMapper().getStockoutFaireShipmentByMax(request.getShipBatchId());
        Integer printBoxIndex = 0;
        if (Objects.nonNull(faireMaxShipmentEntity) && faireMaxShipmentEntity.getPrintBoxIndex() > 0) {
            printBoxIndex = faireMaxShipmentEntity.getPrintBoxIndex() + 1;
        }
        List<Integer> shipmentIds = new LinkedList<>();
        List<Integer> faireIds = request.getBoxItems().stream().map(StockoutFaireShipmentIndex::getId).collect(Collectors.toList());
        List<StockoutFaireShipmentEntity> faireShipmentEntities = this.listByIds(faireIds);
        for (StockoutFaireShipmentEntity o : faireShipmentEntities) {
            StockoutFaireShipmentIndex boxIndex = request.getBoxItems().stream().filter(i -> i.getId().equals(o.getId())).findFirst().orElse(null);
            if (boxIndex != null) {
                o.setStatus(StockoutFaireShipmentStatusEnum.READY_DELIVERY.name());
                o.setPrintBoxCode(FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKOUT_FAIRE_SHIPMENT_PRINT));
                if (printBoxIndex == 0) {
                    o.setPrintBoxIndex(boxIndex.getIndex());
                } else {
                    o.setPrintBoxIndex(printBoxIndex);
                    printBoxIndex = printBoxIndex + 1;
                }
                o.setLogisticsCompany(request.getLogisticsCompany());
                o.setForwarderChannel(request.getForwarderChannel());
                o.setShipBatchId(request.getShipBatchId());
                o.setUpdateBy(loginInfoService.getName());
            }
            if (o.getShipmentId() != null && o.getShipmentId() > 0) {
                shipmentIds.add(o.getShipmentId());
            }
        }
        this.updateBatchById(faireShipmentEntities);
        //子箱处理
        List<StockoutFaireShipmentEntity> subFaireShipmentList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().in(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireIds));
        if (!CollectionUtils.isEmpty(subFaireShipmentList)) {
            subFaireShipmentList.forEach(o -> {
                o.setStatus(StockoutFaireShipmentStatusEnum.READY_DELIVERY.name());
                o.setLogisticsCompany(request.getLogisticsCompany());
                o.setForwarderChannel(request.getForwarderChannel());
                o.setShipBatchId(request.getShipBatchId());
                o.setUpdateBy(loginInfoService.getName());
                shipmentIds.add(o.getShipmentId());
            });
            this.updateBatchById(subFaireShipmentList);
        }
        //装箱清单处理
        batchOpShipmentV2(shipmentIds, request.getLogisticsCompany(), request.getForwarderChannel());
    }

    private void batchOpShipmentV2(List<Integer> shipmentIds, String logisticsCompany, String forwarderChannel) {
        if (CollectionUtils.isEmpty(shipmentIds))
            return;
        List<StockoutShipmentEntity> shipmentEntityList = stockoutShipmentService.listByIds(shipmentIds);
        String name = loginInfoService.getName();
        shipmentEntityList.forEach(o -> {
            o.setLogisticsCompany(logisticsCompany);
            o.setForwarderChannel(forwarderChannel);
            o.setUpdateBy(name);
        });
        stockoutShipmentService.updateBatchById(shipmentEntityList);
        List<StockoutShipmentItemEntity> shipmentItems = shipmentItemService.findByShipmentIdList(shipmentIds);
        List<StockoutOrderEntity> byStockoutOrderNoList = stockoutOrderService.getByStockoutOrderNoList(shipmentItems.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList()));
        byStockoutOrderNoList.forEach(order -> {
            order.setLogisticsCompany(logisticsCompany);
            order.setUpdateBy(name);
        });
        stockoutOrderService.updateBatchById(byStockoutOrderNoList);
    }

    private void validBatchShip(StockoutFaireShipmentShipRequest request) {
        if (!StringUtils.hasText(request.getLogisticsCompany()) || !StringUtils.hasText(request.getShipBatchId()))
            throw new BusinessServiceException("物流公司|批次号 不能为空");
        if (request.getBoxItems().isEmpty())
            throw new BusinessServiceException("箱子信息不能为空");
    }

    /**
     * 批次发货（早期货代发货场景）
     * 1.状态变化，装箱完成->已发货
     * 2.子箱处理
     * 3.装箱清单处理
     */
    @Transactional
    @JLock(keyConstant = "batchShipV1", lockKey = "#request.shipBatchId")
    public void batchShipV1(StockoutFaireShipmentShipRequest request) {
        validBatchShip(request);
        //当前批次存在的打印顺序最大值
        StockoutFaireShipmentEntity faireMaxShipmentEntity = this.getBaseMapper().getStockoutFaireShipmentByMax(request.getShipBatchId());
        Integer printBoxIndex = 0;
        if (Objects.nonNull(faireMaxShipmentEntity) && faireMaxShipmentEntity.getPrintBoxIndex() > 0) {
            printBoxIndex = faireMaxShipmentEntity.getPrintBoxIndex() + 1;
        }
        List<Integer> shipmentIds = new LinkedList<>();
        List<Integer> faireIds = request.getBoxItems().stream().map(StockoutFaireShipmentIndex::getId).collect(Collectors.toList());
        List<StockoutFaireShipmentEntity> faireShipmentEntities = this.listByIds(faireIds);
        for (StockoutFaireShipmentEntity o : faireShipmentEntities) {
            StockoutFaireShipmentIndex boxIndex = request.getBoxItems().stream().filter(i -> i.getId().equals(o.getId())).findFirst().orElse(null);
            if (boxIndex != null) {
                o.setStatus(StockoutFaireShipmentStatusEnum.SHIPPED.name());
                o.setPrintBoxCode(FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKOUT_FAIRE_SHIPMENT_PRINT));
                if (printBoxIndex == 0) {
                    o.setPrintBoxIndex(boxIndex.getIndex());
                } else {
                    o.setPrintBoxIndex(printBoxIndex);
                    printBoxIndex = printBoxIndex + 1;
                }
                o.setDeliveryDate(new Date());
                o.setLogisticsCompany(request.getLogisticsCompany());
                o.setForwarderChannel(request.getForwarderChannel());
                o.setShipBatchId(request.getShipBatchId());
                o.setUpdateBy(loginInfoService.getName());
            }
            if (o.getShipmentId() != null && o.getShipmentId() > 0)
                shipmentIds.add(o.getShipmentId());
        }
        this.updateBatchById(faireShipmentEntities);
        //子箱处理
        List<StockoutFaireShipmentEntity> subFaireShipmentList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().in(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireIds));
        if (!CollectionUtils.isEmpty(subFaireShipmentList)) {
            subFaireShipmentList.forEach(o -> {
                o.setStatus(StockoutFaireShipmentStatusEnum.SHIPPED.name());
                o.setDeliveryDate(new Date());
                o.setLogisticsCompany(request.getLogisticsCompany());
                o.setForwarderChannel(request.getForwarderChannel());
                o.setShipBatchId(request.getShipBatchId());
                o.setUpdateBy(loginInfoService.getName());
                shipmentIds.add(o.getShipmentId());
            });
            this.updateBatchById(subFaireShipmentList);
        }
        //装箱清单处理
        batchOpShipment(shipmentIds, request.getLogisticsCompany(), request.getForwarderChannel());
    }

    public void batchOpShipment(List<Integer> shipmentIds, String logisticsCompany, String forwarderChannel) {
        if (CollectionUtils.isEmpty(shipmentIds))
            return;
        List<StockoutShipmentEntity> shipmentEntityList = stockoutShipmentService.listByIds(shipmentIds);
        shipmentEntityList.forEach(o -> {
            o.setLogisticsCompany(logisticsCompany);
            o.setForwarderChannel(forwarderChannel);
            o.setDeliveryDate(new Date());
            o.setStatus(StockoutShipmentStatusEnum.SHIPPED.name());
            o.setUpdateBy(loginInfoService.getName());
        });
        stockoutShipmentService.updateBatchById(shipmentEntityList);
        // 找所选箱子的全部出库单
        List<StockoutShipmentSearchResult> shipmentConfirmList = stockoutShipmentMapper.findShipmentItemList(shipmentIds, null, 0);
        List<String> stockoutOrderNos = shipmentConfirmList.stream().map(StockoutShipmentSearchResult::getStockoutOrderNo).distinct().filter(StringUtils::hasText).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderNos)) {
            throw new BusinessServiceException("无法找到出库单！");
        }
        stockoutOrderService.checkStockoutCancel(stockoutOrderNos);
        // 箱子发货
        List<StockoutShipmentItemEntity> items = shipmentItemService.findByShipmentIdList(shipmentIds);
        if (!CollectionUtils.isEmpty(items)) {
            StockoutOrderEntity byStockoutOrderNo = stockoutOrderService.getByStockoutOrderNo(items.get(0).getStockoutOrderNo());
            stockoutShipService.shippingUpdateStock(shipmentConfirmList, byStockoutOrderNo.getSpaceId());
        }
        // 出库单发货
        List<StockoutShipmentSearchResult> allShipments = stockoutShipmentMapper.findShipmentConfirmList(stockoutOrderNos, null, null);
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.getByStockoutOrderNoList(shipmentConfirmList.stream().map(StockoutShipmentSearchResult::getStockoutOrderNo).collect(Collectors.toList()));
        allShipments.stream().collect(Collectors.groupingBy(StockoutShipmentSearchResult::getStockoutOrderNo))
                .forEach((stockoutOrderNo, itemList) -> {
                    boolean result = itemList.stream().allMatch(s -> StockoutShipmentStatusEnum.SHIPPED.name().equals(s.getStatus()));
                    StockoutOrderEntity stockoutOrderEntity = stockoutOrderEntityList.stream().filter(t -> stockoutOrderNo.equals(t.getStockoutOrderNo())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("找不到出库单【%s】", stockoutOrderNo)));
                    if (result && !StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())) {
                        applicationContext.getBean(StockoutOrderScanCheckService.class).finishScanTask(stockoutOrderEntity);
                        stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.DELIVERED.name());
                    }
                });
        // 同步装箱清单
        erpPickingBoxService.sendErpPickingBoxShippedSyncByShipmentIdList(shipmentIds);
    }


    /**
     * 生成或更新Faire装箱清单
     */
    public void createOrUpdateByShipment(StockoutShipmentEntity shipmentEntity) {
        // 装箱清单不含规格 则返回
        if (!StringUtils.hasText(shipmentEntity.getBoxSize())) {
            return;
        }
        // 校验是否Faire订单
        List<StockoutShipmentItemEntity> shipmentItemEntityList = shipmentItemService.findByShipmentId(shipmentEntity.getShipmentId());
        if (CollectionUtils.isEmpty(shipmentItemEntityList)) {
            return;
        }
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(shipmentItemEntityList.get(0).getStockoutOrderNo());
        if (!isFaire(stockoutOrderEntity)) return;

        StockoutFaireShipmentEntity faireShipmentEntity = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getShipmentId, shipmentEntity.getShipmentId()));
        if (faireShipmentEntity == null) {
            Map<String, String> boxStandardsEnumMap = specificationsService.getBdBoxSpecificationsMap();
            String boxSpec = boxStandardsEnumMap.get(shipmentEntity.getBoxSize());
            faireShipmentEntity = StockoutShipmentBuilding.createByShipment(shipmentEntity, stockoutOrderEntity, boxSpec);
            this.save(faireShipmentEntity);
            faireShipmentItemService.createByShipmentItems(faireShipmentEntity, shipmentItemEntityList);
        } else {
            faireShipmentEntity.setBoxIndex(shipmentEntity.getBoxIndex());
            faireShipmentEntity.setUpdateBy(shipmentEntity.getUpdateBy());
            this.updateById(faireShipmentEntity);
            // 仅修改数量
            faireShipmentItemService.updateByShipmentItems(faireShipmentEntity, shipmentItemEntityList);
        }
    }

    /**
     * 是否faire装箱清单
     */
    public boolean isFaire(StockoutOrderEntity stockoutOrderEntity) {
        BdSystemParameterEntity systemParamLogisticCompanyList = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_FAIRE_SHIPMENT_LOGISTIC_COMPANY_LIST.getKey());
        if (Objects.isNull(systemParamLogisticCompanyList)) {
            return false;
        }
        String logisticCompany = systemParamLogisticCompanyList.getConfigValue();
        if (!StringUtils.hasText(logisticCompany)) {
            return false;
        }
        List<String> logisticCompanyList = Arrays.asList(logisticCompany.split(","));
        return logisticCompanyList.contains(stockoutOrderEntity.getLogisticsCompany());
    }

    /**
     * Faire装箱详情
     */
    public StockoutFaireShipmentResponse getByBoxCode(String boxCode) {
        StockoutFaireShipmentResponse response = new StockoutFaireShipmentResponse();
        StockoutFaireShipmentEntity faireShipmentEntity = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, boxCode));
        if (faireShipmentEntity == null || faireShipmentEntity.getIsDelete() == 1) {
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        }
        List<Integer> faireShipmentIds = Collections.singletonList(faireShipmentEntity.getId());
        if (faireShipmentEntity.getIsMerge() == 1) {
            faireShipmentIds = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireShipmentEntity.getId()))
                    .stream()
                    .map(StockoutFaireShipmentEntity::getId)
                    .collect(Collectors.toList());
        }
        if (faireShipmentIds.isEmpty()) {
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        }
        BeanUtils.copyProperties(faireShipmentEntity, response);
        response.setFaireShipmentId(faireShipmentEntity.getId());
        response.setPackType(faireShipmentEntity.getBoxSpec());
        response.setPackTypeLabel(StockoutFaireShipmentSpecEnum.getDesByName(faireShipmentEntity.getBoxSpec()));
        // 取订单号
        LambdaQueryWrapper<StockoutFaireShipmentItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutFaireShipmentItemEntity::getFaireShipmentId, faireShipmentIds);
        queryWrapper.eq(StockoutFaireShipmentItemEntity::getIsDelete, 0);
        List<String> orderNoList = faireShipmentItemService.list(queryWrapper)
                .stream()
                .map(StockoutFaireShipmentItemEntity::getOrderNo)
                .distinct().collect(Collectors.toList());
        StringBuilder orderNoStr = new StringBuilder();
        orderNoList.forEach(item -> {
            if (StringUtils.hasText(item)) {
                orderNoStr.append(',').append(item);
            }
        });
        if (StringUtils.hasText(orderNoStr.toString()))
            response.setOrderNoStr(orderNoStr.toString().replaceFirst(",", ""));
        return response;
    }

    /**
     * 更新Faire装箱
     */
    public void updateFaireShipment(StockoutFaireShipmentUpdateRequest request) {
        StockoutFaireShipmentEntity faireShipmentEntity = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, request.getBoxCode()));
        if (faireShipmentEntity == null || faireShipmentEntity.getIsDelete() == 1)
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        BeanUtils.copyProperties(request, faireShipmentEntity);
        faireShipmentEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(faireShipmentEntity);
    }

    public String createShipmentBoxByUser() {
        return FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKOUT_FAIRE_SHIPMENT_HB);
    }


    /**
     * Faire装箱 - 扫描订单
     */
    public StockoutFaireShipmentScanResponse scanOrderNo(StockoutFaireShipmentScanRequest request) {
        StockoutFaireShipmentScanResponse response = new StockoutFaireShipmentScanResponse();
        // 合并箱码赋值
        if (StringUtils.hasText(request.getBoxCode())) {
            response.setBoxCode(request.getBoxCode());
        } else {
            response.setBoxCode(FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKOUT_FAIRE_SHIPMENT_HB));
        }
        // 校验扫描单号
        List<Integer> faireShipmentIds = faireShipmentItemService.getFaireShipmentIdsByOrderNo(request.getOrderNo());
        if (faireShipmentIds.isEmpty()) {
            faireShipmentIds = faireShipmentItemService.getFaireShipmentIdsByStockoutOrderNo(request.getOrderNo());
        }
        if (faireShipmentIds.isEmpty())
            throw new BusinessServiceException("未找到该订单对应Faire装箱，请确认");
        LambdaQueryWrapper<StockoutFaireShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutFaireShipmentEntity::getId, faireShipmentIds);
        queryWrapper.orderByDesc(StockoutFaireShipmentEntity::getId);
        // 当前订单|出库单扫描数据
        List<StockoutFaireShipmentEntity> faireShipmentEntities = this.list(queryWrapper);
        // 已合并的数据
        List<StockoutFaireShipmentEntity> collect = faireShipmentEntities.stream().filter(o -> o.getMergeFaireShipmentId() != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect) || faireShipmentEntities.size() != collect.size()) {
            Map<String, String> boxStandardsEnumMap = specificationsService.getBdBoxSpecificationsMap();
            List<StockoutFaireShipmentEntity> needUpdateList = faireShipmentEntities.stream()
                    .filter(o -> o.getMergeFaireShipmentId() == 0
                            && StockoutFaireShipmentStatusEnum.WAIT_SEC_PACK.name().equals(o.getStatus())
                            && (StockoutFaireShipmentSpecEnum.BAG.name().equals(o.getBoxSpec()) || StringUtils.hasText(boxStandardsEnumMap.get(o.getBoxSize())) && boxStandardsEnumMap.get(o.getBoxSize()).startsWith("A"))
                            && o.getIsDelete() == 0).collect(Collectors.toList());
            if (needUpdateList.isEmpty()) {
                throw new BusinessServiceException("仅支持装箱规格为袋子或A箱规的订单扫描");
            }
            // 新增或更新合并箱
            StockoutFaireShipmentEntity mergeFaireShipment = createMergeBySubFaireShipment(response.getBoxCode(), needUpdateList);
            // 更新明细+构造返回
            for (StockoutFaireShipmentEntity subFaireShipment : needUpdateList) {
                subFaireShipment.setMergeFaireShipmentId(mergeFaireShipment.getId());
                subFaireShipment.setUpdateBy(loginInfoService.getName());
            }
            this.updateBatchById(needUpdateList);
        }
        buildResponses(response, faireShipmentEntities);
        return response;
    }

    private void buildResponses(StockoutFaireShipmentScanResponse
                                        response, List<StockoutFaireShipmentEntity> faireShipmentEntities) {
        List<StockoutFaireShipmentScanItemResponse> itemResponses = new LinkedList<>();

        StockoutFaireShipmentEntity mergeFaireShipment = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, response.getBoxCode()));
        if (mergeFaireShipment == null) {
            Integer mergeFaireId = faireShipmentEntities.stream().filter(o -> StockoutFaireShipmentStatusEnum.WAIT_SEC_PACK.name().equals(o.getStatus()) && o.getMergeFaireShipmentId() != null && o.getMergeFaireShipmentId() > 0).map(StockoutFaireShipmentEntity::getMergeFaireShipmentId).findFirst().orElse(0);
            mergeFaireShipment = this.getById(mergeFaireId);
            if (mergeFaireShipment == null)
                throw new BusinessServiceException("未找到该订单对应Faire装箱，请确认");
            response.setBoxCode(mergeFaireShipment.getBoxCode());
        }

        LambdaQueryWrapper<StockoutFaireShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, mergeFaireShipment.getId());
        queryWrapper.eq(StockoutFaireShipmentEntity::getIsDelete, 0);
        queryWrapper.orderByDesc(StockoutFaireShipmentEntity::getId);
        List<StockoutFaireShipmentEntity> stockoutFaireShipmentEntities = this.list(queryWrapper);
        for (StockoutFaireShipmentEntity subFaireShipment : stockoutFaireShipmentEntities) {
            StockoutFaireShipmentScanItemResponse itemResponse = new StockoutFaireShipmentScanItemResponse();
            BeanUtils.copyProperties(subFaireShipment, itemResponse);
            itemResponse.setFaireShipmentId(subFaireShipment.getId());
            itemResponse.setOrderNoList(faireShipmentItemService.getOrderNoByFaireShipmentId(subFaireShipment.getId()));
            itemResponse.setStockoutOrderNoList(faireShipmentItemService.getStockoutOrderNoByFaireShipmentId(subFaireShipment.getId()));
            itemResponse.setBoxSpec(StockoutFaireShipmentSpecEnum.getDesByName(itemResponse.getBoxSpec()));
            itemResponses.add(itemResponse);
        }
        response.setAddItemList(itemResponses);
    }

    private StockoutFaireShipmentEntity createMergeBySubFaireShipment(String
                                                                              mergeBoxCode, List<StockoutFaireShipmentEntity> faireShipmentEntities) {
        StockoutFaireShipmentEntity mergeFaireShipment = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, mergeBoxCode));
        if (mergeFaireShipment != null) {
            return mergeFaireShipment;
        }
        String name = loginInfoService.getName();
        mergeFaireShipment = new StockoutFaireShipmentEntity();
        StockoutShipmentBuilding.buildMergeFaireShipment(mergeBoxCode, faireShipmentEntities, mergeFaireShipment, name);
        this.save(mergeFaireShipment);
        return mergeFaireShipment;
    }

    /**
     * 获取合并装箱详情
     */
    public StockoutFaireMergeShipmentResponse getByMergeBoxCode(String boxCode) {
        StockoutFaireMergeShipmentResponse response = new StockoutFaireMergeShipmentResponse();
        StockoutFaireShipmentEntity faireShipmentEntity = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, boxCode));
        if (faireShipmentEntity == null || faireShipmentEntity.getIsDelete() == 1) {
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        }
        List<StockoutFaireShipmentEntity> subFaireShipmentList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda()
                .eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireShipmentEntity.getId())
                .eq(StockoutFaireShipmentEntity::getIsDelete, 0));
        response.setFaireShipmentId(faireShipmentEntity.getId());
        response.setBoxCode(faireShipmentEntity.getBoxCode());
        response.setBoxQty(subFaireShipmentList.size());
        response.setBoxSkuQty(subFaireShipmentList.stream().mapToInt(StockoutFaireShipmentEntity::getBoxSkuQty).sum());
        BigDecimal weight = subFaireShipmentList.stream().map(i -> {
            if (i.getWeight() == null) {
                return BigDecimal.ZERO;
            } else {
                return i.getWeight();
            }
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        response.setWeight(weight);
        // 更新合并箱信息
        faireShipmentEntity.setUpdateBy(loginInfoService.getName());
        faireShipmentEntity.setBoxSkuQty(response.getBoxSkuQty());
        faireShipmentEntity.setWeight(response.getWeight());
        this.updateById(faireShipmentEntity);
        return response;
    }

    /**
     * 确认合并
     */
    public void confirmMerge(StockoutFaireShipmentOpMergeRequest request) {
        StockoutFaireShipmentEntity faireShipmentEntity = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, request.getBoxCode()));
        if (faireShipmentEntity == null || faireShipmentEntity.getIsDelete() == 1) {
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        }
        faireShipmentEntity.setBoxSize(request.getBoxSize());
        faireShipmentEntity.setWeight(request.getWeight());
        faireShipmentEntity.setVolumeWeight(request.getVolumeWeight());
        faireShipmentEntity.setStatus(StockoutFaireShipmentStatusEnum.PACKING_END.name());
        faireShipmentEntity.setShipmentDate(new Date());
        faireShipmentEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(faireShipmentEntity);
        // 子箱状态修改为 装箱完成
        List<StockoutFaireShipmentEntity> subFaireShipmentEntityList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda()
                .eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireShipmentEntity.getId())
                .eq(StockoutFaireShipmentEntity::getIsDelete, 0));
        subFaireShipmentEntityList.forEach(o -> {
            o.setStatus(StockoutFaireShipmentStatusEnum.PACKING_END.name());
            o.setUpdateBy(loginInfoService.getName());
            o.setShipmentDate(new Date());
        });
        this.updateBatchById(subFaireShipmentEntityList);
    }

    /**
     * 移除合并
     */
    public void removeMerge(StockoutFaireShipmentOpClearRequest request) {
        StockoutFaireShipmentEntity faireShipmentEntity = this.getOne(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getBoxCode, request.getBoxCode()));
        if (faireShipmentEntity == null || faireShipmentEntity.getIsDelete() == 1) {
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        }
        faireShipmentEntity.setIsDelete(1);
        faireShipmentEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(faireShipmentEntity);
        // 子箱移除合并箱子号
        List<StockoutFaireShipmentEntity> subFaireShipmentEntityList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda()
                .eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireShipmentEntity.getId())
                .eq(StockoutFaireShipmentEntity::getIsDelete, 0));
        subFaireShipmentEntityList.forEach(o -> {
            o.setStatus(StockoutFaireShipmentStatusEnum.WAIT_SEC_PACK.name());
            o.setShipmentDate(null);
            o.setMergeFaireShipmentId(0);
            o.setUpdateBy(loginInfoService.getName());
        });
        this.updateBatchById(subFaireShipmentEntityList);
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_FAIRE_SHIPMENT;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        IdListRequest idListRequest = JSONObject.parseObject(request.getRequestContent(), IdListRequest.class);
        List<Integer> idList = idListRequest.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            StockoutFaireShipmentSearchRequest searchRequest = JSONObject.parseObject(request.getRequestContent(), StockoutFaireShipmentSearchRequest.class);
            idList = this.baseMapper.getSearchShipmentIds(searchRequest);
        }
        if (CollectionUtils.isEmpty(idList))
            throw new BusinessServiceException("idList不可为空");
        List<StockoutFaireShipmentEntity> faireShipmentEntityList = this.list(new LambdaQueryWrapper<StockoutFaireShipmentEntity>()
                .in(StockoutFaireShipmentEntity::getId, idList).orderByAsc(StockoutFaireShipmentEntity::getBoxCode, StockoutFaireShipmentEntity::getBoxIndex));
        if (CollectionUtils.isEmpty(faireShipmentEntityList))
            return response;
        List<FaireShipmentListExport> resultList = faireShipmentEntityList.stream().map(this::buildExport).collect(Collectors.toList());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        response.setTotalCount((long) resultList.size());
        return response;
    }

    private FaireShipmentListExport buildExport(StockoutFaireShipmentEntity faireShipment) {
        FaireShipmentListExport export = new FaireShipmentListExport();
        BeanUtilsEx.copyProperties(faireShipment, export);
        List<Integer> faireIdList;
        if (faireShipment.getIsMerge() != null && faireShipment.getIsMerge() > 0) {
            List<StockoutFaireShipmentEntity> subFarieShipmentList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda().eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, faireShipment.getId()));
            faireIdList = subFarieShipmentList.stream().map(StockoutFaireShipmentEntity::getId).collect(Collectors.toList());
        } else {
            faireIdList = Collections.singletonList(faireShipment.getId());
        }
        List<StockoutFaireShipmentItemEntity> faireShipmentItemEntityList = faireShipmentItemService.findByFaireShipmentIdList(faireIdList);
        String spaceName = bdSpaceService.getSpaceById(faireShipment.getSpaceId()).getSpaceName();
        List<String> orderNos = faireShipmentItemEntityList.stream().map(StockoutFaireShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList());
        List<String> stockoutOrderNos = faireShipmentItemEntityList.stream().map(StockoutFaireShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
        StockoutShipmentBuilding.buildFaireExport(faireShipment, export, spaceName, orderNos, stockoutOrderNos);
        return export;
    }

    /**
     * 导入尾程单号更新faire装箱清单
     */
    public void importShipmentLogisticsNo(StockoutOrderEntity
                                                  stockoutOrderEntity, StockoutShipmentLogisticsNoImport row, List<StockoutShipmentEntity> shipmentList, String
                                                  loginName) {
        if (!isFaire(stockoutOrderEntity)) return;
        List<StockoutFaireShipmentEntity> faireShipmentEntityList = this.getByShipmentIds(shipmentList.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(faireShipmentEntityList)) return;
        List<StockoutFaireShipmentEntity> byMergeFaireShipmentIds = this.getByMergeFaireShipmentIds(faireShipmentEntityList.stream().map(StockoutFaireShipmentEntity::getId).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(byMergeFaireShipmentIds)) {
            faireShipmentEntityList.addAll(byMergeFaireShipmentIds);
        }
        List<StockoutFaireShipmentEntity> collect = faireShipmentEntityList.stream().map(entity -> StockoutShipmentBuilding.convertStockoutFaireShipmentEntity(row, loginName, entity)).collect(Collectors.toList());
        this.updateBatchById(collect);
    }

    public List<StockoutFaireShipmentEntity> getByShipmentIds(List<Integer> shipmentIds) {
        LambdaQueryWrapper<StockoutFaireShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutFaireShipmentEntity::getShipmentId, shipmentIds);
        return this.list(queryWrapper);
    }

    public List<StockoutFaireShipmentEntity> getByMergeFaireShipmentIds(List<Integer> mergeFaireShipmentIds) {
        LambdaQueryWrapper<StockoutFaireShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutFaireShipmentEntity::getMergeFaireShipmentId, mergeFaireShipmentIds);
        return this.list(queryWrapper);
    }

    public List<StockoutFaireShipmentEntity> getByMergeFaireShipmentIdsWithNoDelete(List<Integer> mergeFaireShipmentIds) {
        LambdaQueryWrapper<StockoutFaireShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutFaireShipmentEntity::getMergeFaireShipmentId, mergeFaireShipmentIds);
        queryWrapper.eq(StockoutFaireShipmentEntity::getIsDelete, 0);
        return this.list(queryWrapper);
    }

    public void removeFaire(Integer faireShipmentId) {
        StockoutFaireShipmentEntity faireShipmentEntity = this.getById(faireShipmentId);
        if (faireShipmentEntity == null || faireShipmentEntity.getIsDelete() == 1) {
            throw new BusinessServiceException("请确认当前装箱清单是否可用");
        }
        StockoutFaireShipmentEntity pFaireShipment = this.getById(faireShipmentEntity.getMergeFaireShipmentId());
        if (pFaireShipment == null || pFaireShipment.getIsDelete() == 1) {
            throw new BusinessServiceException("请确认当前合并箱是否可用");
        }
        faireShipmentEntity.setMergeFaireShipmentId(0);
        faireShipmentEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(faireShipmentEntity);
        //更新合并箱 sku数
        List<StockoutFaireShipmentEntity> subFaireShipmentList = this.list(new QueryWrapper<StockoutFaireShipmentEntity>().lambda()
                .eq(StockoutFaireShipmentEntity::getMergeFaireShipmentId, pFaireShipment.getId())
                .eq(StockoutFaireShipmentEntity::getIsDelete, 0));
        pFaireShipment.setBoxSkuQty(subFaireShipmentList.stream().mapToInt(StockoutFaireShipmentEntity::getBoxSkuQty).sum());
        pFaireShipment.setUpdateBy(loginInfoService.getName());
        this.updateById(pFaireShipment);
    }

    @org.springframework.transaction.annotation.Transactional
    @JLock(keyConstant = "asyncBatchShip", lockKey = "#request.shipBatchId")
    public AsyncProcessFlowResult asyncBatchShip(StockoutFaireShipmentShipRequest request) {
        LocationWrapperMessage<StockoutFaireShipmentShipRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request);
        return asyncProcessFlowService.createFlow(KafkaConstant.STOCKOUT_FAIRE_BATCH_SHIPPED_TOPIC, KafkaConstant.STOCKOUT_FAIRE_BATCH_SHIPPED_TOPIC_MARK, message, request.getShipBatchId(), 1);
    }

    /**
     * 装箱清单删除，删除对应的额faire装箱清单
     *
     * @param shipmentId
     */
    public void deleteByShipmentId(Integer shipmentId) {
        this.update(new LambdaUpdateWrapper<StockoutFaireShipmentEntity>().eq(StockoutFaireShipmentEntity::getShipmentId, shipmentId)
                .set(StockoutFaireShipmentEntity::getIsDelete, Boolean.TRUE).set(StockoutFaireShipmentEntity::getUpdateBy, loginInfoService.getName()));
    }

    public void updateFaireShipmentStatus(List<Integer> faireShipmentIds) {
        LambdaQueryWrapper<StockoutFaireShipmentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutFaireShipmentEntity::getId, faireShipmentIds);
        List<StockoutFaireShipmentEntity> faireShipmentList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(faireShipmentList)) {
            throw new BusinessServiceException("找不到对应的Faire装箱清单数据！");
        }
        faireShipmentList.forEach(detail -> {
            if (!StockoutFaireShipmentStatusEnum.PACKING_END.name().equals(detail.getStatus())) {
                throw new BusinessServiceException("只有装箱完成状态的数据才能修改请确认！");
            }
            detail.setStatus(StockoutFaireShipmentStatusEnum.WAIT_SEC_PACK.name());
        });
        this.updateBatchById(faireShipmentList);
    }
}
