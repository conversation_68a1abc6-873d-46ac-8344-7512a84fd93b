package com.nsy.wms.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.nsy.api.core.apicore.util.EncodingUtils;
import com.nsy.api.wms.constants.HeaderConstants;
import com.nsy.api.wms.domain.DemoMessage;
import com.nsy.base.paper.vatinvoice.IVatInvoiceService;
import com.nsy.base.paper.vatinvoice.VatInvoiceVo;
import com.nsy.wms.business.service.DemoService;
import com.nsy.wms.mq.KafkaBusinessMarkConstant;
import com.nsy.wms.mq.producer.MessageProducer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Api(tags = "Demo接口")
@RestController
public class DemoController extends BaseController {

    @Autowired
    MessageProducer messageProducer;

    @Autowired
    DemoService demoService;

    @Autowired
    @Qualifier("demoTopic")
    NewTopic demoTopic;

    @Autowired
    Environment environment;

    @Autowired
    IVatInvoiceService vatInvoiceService;

    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public VatInvoiceVo hello() throws Exception {
        return vatInvoiceService.vatInvoiceReaderFromUrl("https://stage-nsy-wms.oss-cn-hangzhou.aliyuncs.com/vqp/hkj/1651815144335.pdf");
    }

    @SentinelResource("/hello")
    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    public String hello(@RequestParam String name) {
        return demoService.hello(name);
    }

    // todo 临时添加用作调试，正式上线需要剔除
    @RequestMapping(value = "/env", method = RequestMethod.GET)
    public String env(@RequestParam String key) {
        return environment.getProperty(key, "");
    }

    @ApiOperation(value = "kafka测试", notes = "kafka发送测试", produces = "application/json")
    @RequestMapping(value = "/test-kafka-send", method = RequestMethod.GET)
    @Transactional
    public void sendToKafka(@RequestParam String messageContent) {
        DemoMessage message = new DemoMessage();
        message.setMessage(messageContent);
        String businessMark = KafkaBusinessMarkConstant.DEMO;
        messageProducer.sendMessage(businessMark, demoTopic.name(), message);
    }

    @RequestMapping(value = "/test-redis-cache", method = RequestMethod.GET)
    public String redisCache(@RequestParam String username) {
        return demoService.getUserConfig(username);
    }

    @RequestMapping(value = "/test-header", method = RequestMethod.GET)
    public Map<String, String> getHeaderDemo(HttpServletRequest request) throws UnsupportedEncodingException {
        String userName = EncodingUtils.decodeURL(request.getHeader(HeaderConstants.USER_NAME));
        String realUserName = EncodingUtils.decodeURL(request.getHeader(HeaderConstants.REAL_NAME));
        String location = new String(request.getHeader(HeaderConstants.LOCATION).getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        String duty = EncodingUtils.decodeURL(request.getHeader(HeaderConstants.DUTY_NAME));
        String authorization = EncodingUtils.decodeURL(request.getHeader(HeaderConstants.AUTHORIZATION));
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("userName", userName);
        headerMap.put("realUserName", realUserName);
        headerMap.put("location", location);
        headerMap.put("duty", duty);
        headerMap.put("authorization", authorization);
        return headerMap;
    }

    @RequestMapping(value = "/test-transactional/{isThrow}", method = RequestMethod.POST)
    public void transactionalTest(@PathVariable Boolean isThrow) {
        demoService.transactionalTest(isThrow);
    }
}
