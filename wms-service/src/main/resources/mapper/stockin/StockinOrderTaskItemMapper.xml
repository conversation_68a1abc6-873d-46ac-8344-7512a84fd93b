<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderTaskItemMapper">
    <select id="pageList" resultType="com.nsy.api.wms.response.stockin.StockinOrderTaskItemListResponse">
        SELECT
        ps.image_url,
        ps.thumbnail_image_url,
        ps.preview_image_url,
        ps.color,
        ps.size,
        sp.space_name,
        s.area_name,
        s.sku,
        s.barcode,
        s.purchase_plan_no,
        s.order_no,
        s.is_need_qa,
        s.shelve_space_area_name,
        s.expected_qty,
        s.qa_qty,
        s.stockin_qty,
        s.stockin_return_qty,
        s.qa_problem description,
        s.is_fba_quick,
        s.vacuum_flag as vacuumFlag,
        p.package_vacuum as packageVacuum,
        s.brand_name,
        s.store_id
        FROM
        stockin_order_task_item s
        join stockin_order_task t on s.task_id = t.task_id
        LEFT JOIN bd_space sp ON sp.space_id = s.space_id
        LEFT JOIN product_spec_info ps ON s.spec_id = ps.spec_id
        LEFT JOIN product_info p ON p.product_id = ps.product_id
        WHERE
        s.task_id = #{request.taskId}
        <if test="request.sku != null and request.sku !=''">
            AND s.sku like concat(#{request.sku},'%')
        </if>
        <if test="request.brandName != null and request.brandName !=''">
            AND s.brand_name = #{request.brandName}
        </if>
        <if test="request.barcode != null and request.barcode !=''">
            AND s.barcode = #{request.barcode}
        </if>
        order by s.task_item_id
    </select>

    <select id="queryEarliestReceiptDateByPurchasePlanNo"
            resultType="com.nsy.api.wms.response.stockin.QueryPurchaseOrderEarliestReceiptDateResponse">
        SELECT
        ti.purchase_plan_no,
        MIN( ot.operate_start_date ) AS earliestReceiptDate
        FROM nsy_wms.stockin_order_task ot
        INNER JOIN nsy_wms.stockin_order_task_item ti ON ti.task_id = ot.task_id
        <where>
            <if test="request.purchasePlanNoList != null and request.purchasePlanNoList.size() > 0">
                ti.purchase_plan_no in
                <foreach collection="request.purchasePlanNoList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        ti.purchase_plan_no
    </select>

    <select id="queryEarliestReceiptDateByPurchasePlanNoAndSku"
            resultType="com.nsy.api.wms.response.stockin.QueryPurchaseOrderEarliestReceiptDateResponse">
        SELECT
        ti.purchase_plan_no,
        ti.sku,
        MIN( ot.operate_start_date ) AS earliestReceiptDate,
        MAX( ot.operate_start_date ) AS latestReceiptDate
        FROM nsy_wms.stockin_order_task ot
        INNER JOIN nsy_wms.stockin_order_task_item ti ON ti.task_id = ot.task_id
        WHERE (ti.purchase_plan_no, ti.sku) IN
        <foreach collection="request" separator="," item="info" open="(" close=")">
            (#{info.purchasePlanNo},#{info.sku})
        </foreach>
        GROUP BY
        ti.purchase_plan_no, ti.sku
    </select>
    <select id="findBoxQtyBySupplierDeliveryNo" resultType="com.nsy.api.wms.domain.stockin.StockinSupplierBoxSku">
        select ti.sku                                      as sku,
               count(DISTINCT t.supplier_delivery_barcode) as boxQty,
               sum(ti.qa_qty)                              as qaQty
        from stockin_order_task_item ti
                 left join stockin_order_task t on t.task_id = ti.task_id
        where t.supplier_delivery_no = #{supplierDeliveryNo}
        group by ti.sku
    </select>
    <select id="findAllByTaskIdListIgnoreTenant"
            resultType="com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity">
        SELECT *
        FROM
        stockin_order_task_item
        <where>
            <if test="taskIdList != null and taskIdList.size() > 0">
                task_id in
                <foreach collection="taskIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getPackageQty" resultType="com.nsy.api.wms.domain.stockin.StockinPackageQty">
        SELECT oi.stockin_order_item_id,
               oi.sku,
               oi.purchase_plan_no,
               oi.shelved_qty,
               oi.return_qty,
               oi.qty as stockinQty,
               oi.concessions_count,
               oi.unqualified_category,
               ti.package_name
        FROM stockin_order_task_item ti
                 INNER JOIN stockin_order_item oi
                            on ti.task_item_id = oi.task_item_id
        where ti.package_name is not null
          and ti.task_id = #{taskId}
          and oi.shelved_qty > 0
    </select>
    <select id="findQtyBySupplierDeliveryBoxCodeAndSku" resultType="java.lang.Integer">
        select ifnull(sum(ti.expected_qty), 0)
        from stockin_order_task_item ti
                 inner join stockin_order_task t
                            on t.task_id = ti.task_id
        where t.supplier_delivery_box_code = #{supplierDeliveryBoxCode}
          and ti.sku = #{sku}
    </select>
    <select id="findSkuListByPurchasePlanNo" resultType="java.lang.String">
        select distinct ti.sku from stockin_order_task_item ti
        inner join  stockin_order_task t
        on t.task_id = ti.task_id
        where ti.purchase_plan_no = #{purchasePlanNo}
        and t.status != 'RECEIVED'
    </select>
    <select id="queryEarliestReceiptDateByPurchasePlanNoBySku"
            resultType="com.nsy.api.wms.response.stockin.QueryPurchaseOrderEarliestReceiptDateResponse">

    </select>
    <select id="isReturnDeliveryType" resultType="java.lang.Boolean">
            select count(1) > 0 from stockin_order_task t
            inner join stock_platform_schedule p
            on t.platform_schedule_id = p.platform_schedule_id
            where t.task_id in
            <foreach collection="taskIdList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            and p.delivery_type = 2
    </select>
</mapper>
