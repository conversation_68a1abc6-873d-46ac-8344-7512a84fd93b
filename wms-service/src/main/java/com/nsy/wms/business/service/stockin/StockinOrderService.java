package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ExceptionConstants;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.bd.BdSpace;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.StockInPageItemInfo;
import com.nsy.api.wms.domain.stockin.StockinOrderListExport;
import com.nsy.api.wms.domain.stockin.StockinOrderListInfo;
import com.nsy.api.wms.domain.stockin.StockinOrderStatisticsResponse;
import com.nsy.api.wms.domain.stockin.StockinOrderStatusDTO;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItemDetail;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskSkuItem;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderDeliveryConfirmStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.product.ProductTryOnTaskSaveRequest;
import com.nsy.api.wms.request.stock.StockinInternalBoxFullRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSetRequest;
import com.nsy.api.wms.request.stockin.StockinOrderListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinOrderListResponse;
import com.nsy.api.wms.response.stockin.StockinOrderStatusCountResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockin.StockinQaTaskUpdateBo;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackShelvedBoxInfo;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackShelvedInfo;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackShelvedRequest;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.internal.common.SaleModuleService;
import com.nsy.wms.business.service.internal.scm.StockinReceiptService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleItemService;
import com.nsy.wms.business.service.stock.StockPlatformScheduleService;
import com.nsy.wms.business.service.stockin.valid.StockinOrderTaskValid;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskItemMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.ReflectUtils;
import com.nsy.wms.utils.SortListUtils;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinOrderService extends ServiceImpl<StockinOrderMapper, StockinOrderEntity> implements IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockinOrderService.class);
    @Inject
    StockinOrderItemService stockinOrderItemService;
    @Inject
    LoginInfoService loginInfoService;
    @Autowired
    StockinScanLogService stockinScanLogService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    SaleModuleService saleModuleService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Inject
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinOrderMapper stockinOrderMapper;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    StockinShelveTaskItemMapper stockinShelveTaskItemMapper;
    @Autowired
    ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    ReflectUtils reflectUtils;
    @Autowired
    StockInternalBoxItemService internalBoxItemService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockPlatformScheduleService platformScheduleService;
    @Autowired
    StockPlatformScheduleItemService platformScheduleItemService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockinShelveTaskOpService stockinShelveTaskOpService;
    @Autowired
    StockinOrderTaskValid stockinOrderTaskValid;
    @Autowired
    StockinShelveTaskItemService shelveTaskItemService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;
    @Autowired
    BdSpaceService bdSpaceService;
    @Autowired
    StockinShelveSyncErpService stockinShelveSyncErpService;
    @Autowired
    StockinOrderPostProcessingService stockinOrderPostProcessingService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    BrandCommonService brandCommonService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    StockinReceiptService stockinReceiptService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    private StockinReturnProductTaskItemService stockinReturnProductTaskItemService;

    /**
     * 创建入库单
     *
     * @param task
     * @return
     */
    @Transactional
    public StockinOrderEntity create(StockinOrderTaskEntity task, FormNoTypeEnum formNoType) {
        //先查询是否存在
        StockinOrderEntity entity = this.findTopByTaskId(task.getTaskId());
        if (entity != null)
            return entity;
        FormNoTypeEnum formNoTypeEnum = StockinTypeEnum.ALLOT.name().equals(task.getStockinType()) ? FormNoTypeEnum.STOCKIN_ORDER_NO_DBRK : formNoType;


        StockPlatformScheduleEntity stockPlatformScheduleEntity = platformScheduleService.getById(task.getPlatformScheduleId());
        StockinOrderEntity stockinOrderEntity = new StockinOrderEntity();
        stockinOrderEntity.setCreateBy(loginInfoService.getName());
        stockinOrderEntity.setUpdateBy(loginInfoService.getName());
        stockinOrderEntity.setStockinType(task.getStockinType());
        stockinOrderEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVING.name());
        stockinOrderEntity.setStockinOrderNo(FormNoGenerateUtil.generateFormNo(formNoTypeEnum));
        stockinOrderEntity.setSupplierDeliveryBoxCode(task.getSupplierDeliveryBoxCode());
        stockinOrderEntity.setSupplierDeliveryBarcode(task.getSupplierDeliveryBarcode());
        stockinOrderEntity.setTaskId(task.getTaskId());
        stockinOrderEntity.setPurchaseUserId(task.getPurchaseUserId());
        stockinOrderEntity.setSupplierId(stockPlatformScheduleEntity.getSupplierId());
        stockinOrderEntity.setSupplierName(stockPlatformScheduleEntity.getSupplierName());
        stockinOrderEntity.setOrderType(stockPlatformScheduleEntity.getOrderType());
        stockinOrderEntity.setLocation(stockPlatformScheduleEntity.getLocation());
        this.save(stockinOrderEntity);
        // 生成入库单日志记录
        stockinOrderTaskService.createStockinOrderAddStockinOrderLog(stockinOrderEntity, task);
        // erp 生成接收单表头
        //多发入库改为同步
        stockinOrderTaskService.generatePurchaseReceivingOrder(task.getSupplierDeliveryBoxCode(), Boolean.FALSE, new Date(), task.getStockinType());
        return stockinOrderEntity;
    }

    @Transactional
    public void stockinOrderItemSet(Integer taskId, StockinOrderItemSetRequest stockinOrderItemSetRequest, FormNoTypeEnum formNoTypeEnum) {
        StockinOrderTaskValid.validateStockinOrderItemSetRequest(stockinOrderItemSetRequest);
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(stockinOrderItemSetRequest.getBarcode(), Collections.singletonList(taskId));
        StockInternalBox internalBox = stockInternalBoxService.getInternalBox(stockinOrderItemSetRequest.getInternalBoxCode());
        if (internalBox.getIsDeleted().equals(1))
            throw new BusinessServiceException("箱子已删除，无法装箱！");
        //大小写问题处理
        stockinOrderItemSetRequest.setInternalBoxCode(internalBox.getInternalBoxCode());

        Boolean isQa = StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType());
        StockinOrderTaskEntity taskEntity = stockinOrderTaskService.getById(taskId);
        if (Objects.isNull(taskEntity))
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        //校验箱内的商品，不允许调拨入库和正常入库混在一个箱子
        stockinOrderTaskValid.validateInternalBox(taskEntity, stockinOrderItemSetRequest.getInternalBoxCode());
        StockinOrderEntity stockinOrderEntity = this.create(taskEntity, formNoTypeEnum);
        List<StockinOrderTaskItemEntity> stockinOrderTaskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(taskId, productSpecInfoEntity.getSku());
        //判断sku是否在该任务下 为空则不存在
        if (CollectionUtils.isEmpty(stockinOrderTaskItemEntityList))
            throw new BusinessServiceException("商品未在该任务中，不允许收货");
        List<Integer> collect = stockinOrderTaskItemEntityList.stream().map(StockinOrderTaskItemEntity::getSpaceId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect) && !collect.contains(internalBox.getSpaceId())) {
            BdSpace spaceById = bdSpaceService.getSpaceById(collect.get(0));
            throw new BusinessServiceException(String.format("该商品属于%s，请收到对应仓库的收货箱！", spaceById.getSpaceName()));
        }

        //校验箱内的商品，不允许同个sku跨工厂收货
        stockinOrderTaskValid.validateInternalBoxSku(stockinOrderEntity, stockinOrderTaskItemEntityList.get(0).getProductId(), stockinOrderItemSetRequest.getInternalBoxCode());
        productSpecInfoService.validPrice(stockinOrderTaskItemEntityList.get(0).getSku());
        //校验入库数是否会大于计划入库数
        validQty(stockinOrderTaskItemEntityList, stockinOrderItemSetRequest.getQty(), stockinOrderItemSetRequest.getInternalBoxCode());
        Integer qty = stockinOrderItemSetRequest.getQty();

        //遍历taskItem进行数据录入
        StockinOrderTaskItemDetail detail = new StockinOrderTaskItemDetail();
        for (int i = 0; qty > 0 && i < stockinOrderTaskItemEntityList.size(); i++) {
            StockinOrderTaskItemEntity taskItemEntity = stockinOrderTaskItemEntityList.get(i);
            //是否为最后一个
            boolean isLast = i == stockinOrderTaskItemEntityList.size() - 1;
            //还可装入的数量
            int stockinQty = Objects.isNull(taskItemEntity.getStockinQty()) ? 0 : taskItemEntity.getStockinQty();
            int canLoadQty = taskItemEntity.getExpectedQty() - stockinQty;
            //如果这个taskItem已经装满 且不是最后一个taskItem，装下一个
            if (canLoadQty <= 0 && !isLast)
                continue;
            //实际装入的数量
            int loadQty = isLast ? qty : qty > canLoadQty ? canLoadQty : qty;

            StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemService.findTopByTaskItemIdAndInternalBoxCode(taskItemEntity.getTaskItemId(), stockinOrderItemSetRequest.getInternalBoxCode());
            //已经录入过的增加数量
            if (Objects.nonNull(stockinOrderItemEntity)) {
                detail.setInternalBoxCode(stockinOrderItemEntity.getInternalBoxCode());
                detail.setSku(stockinOrderItemEntity.getSku());
                detail.setQty(stockinOrderItemEntity.getQty() + loadQty);
                stockinOrderTaskService.editQty(stockinOrderItemEntity, detail);
            } else {
                detail.setQty(loadQty);
                detail.setInternalBoxCode(stockinOrderItemSetRequest.getInternalBoxCode());
                StockinOrderItemEntity item = this.createItem(taskEntity, taskItemEntity, stockinOrderEntity, detail);

                //商品入箱并更新库存
                stockInternalBoxService.skuToBox(Collections.singletonList(item), stockinOrderEntity.getStockinOrderNo());
            }
            qty = qty - loadQty;
            if (isQa)
                taskItemEntity.setStockinQaQty(taskItemEntity.getStockinQaQty() + loadQty);
            //数据回填给taskItem
            taskItemEntity.setStockinQty(stockinQty + loadQty);
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            stockinOrderTaskItemService.updateById(taskItemEntity);
        }
        //生成日志
        String content = String.format("SKU【%s】预收货%s件，实际收货%s件，质检%s件，绑定内部箱号%s", stockinOrderTaskItemEntityList.get(0).getSku(),
            stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum(), stockinOrderItemSetRequest.getQty(),
            stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getQaQty).sum(), stockinOrderItemSetRequest.getInternalBoxCode());
        stockinScanLogService.addScanLog(content, taskEntity, stockinOrderEntity, stockinOrderItemSetRequest.getQty(), isQa ? StockinScanLogTypeEnum.STOCKIN_QA_SCAN_LOG_TYPE_RECEIVING : StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_RECEIVING);
        //修改内部箱号为待质检或待上架
        modifyBox(stockinOrderItemSetRequest, taskEntity);

        //生成或更新质检任务
        messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_TASK_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_TASK_TOPIC,
            Key.of(internalBox.getInternalBoxCode() + "_" + productSpecInfoEntity.getSku()),
            new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(),
                new StockinQaTaskUpdateBo(internalBox.getInternalBoxCode(), productSpecInfoEntity.getSku())));

        // 生成入库单日志
        receiveAddStockinOrderLog(stockinOrderEntity.getStockinOrderId(), stockinOrderTaskItemEntityList.get(0).getSku(), stockinOrderTaskItemEntityList, stockinOrderItemSetRequest);
        this.sendTryOnTask(taskEntity.getPlatformScheduleId(), stockinOrderTaskItemEntityList.get(0).getSku(),
            stockinOrderTaskItemEntityList.get(0).getProductId(), stockinOrderEntity.getStockinOrderNo());
    }

    public void validQty(List<StockinOrderTaskItemEntity> stockinOrderTaskItemEntityList, Integer qty, String internalBoxCode) {
        int expectedQty = stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
        int stockinQty = stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getStockinQty).sum();
        if (stockinQty + qty > expectedQty)
            throw new BusinessServiceException(String.format("已收货入库%s件，还可入库%s件！", stockinQty, expectedQty - stockinQty));
        //校验箱子内不能放入不同区域的商品
        List<String> areaList = stockInternalBoxItemService.getBaseMapper().findAreaNameByBoxCode(internalBoxCode)
            .stream().filter(areaName -> StringUtils.hasText(areaName)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(areaList))
            return;
        String areaName = areaList.get(0);
        if (areaName.equals(stockinOrderTaskItemEntityList.get(0).getAreaName()))
            return;
        List<String> brandAreaNameList = brandCommonService.getBrandAreaNameList();
        //存在品牌仓的则不允许混装
        if (brandAreaNameList.contains(areaName) || brandAreaNameList.contains(stockinOrderTaskItemEntityList.get(0).getAreaName()))
            throw new BusinessServiceException(String.format("收货箱已存在【%s】商品，不允许装入【%s】的商品！", areaName, stockinOrderTaskItemEntityList.get(0).getAreaName()));
    }

    private void modifyBox(StockinOrderItemSetRequest stockinOrderItemSetRequest, StockinOrderTaskEntity taskEntity) {
        StockinInternalBoxFullRequest request = new StockinInternalBoxFullRequest();
        request.setTaskId(taskEntity.getTaskId());
        //满箱确认
        stockInternalBoxService.fullInternalBox(stockinOrderItemSetRequest.getInternalBoxCode(), request);
    }

    private void receiveAddStockinOrderLog(Integer stockinOrderId, String sku, List<StockinOrderTaskItemEntity> stockinOrderTaskItemEntityList, StockinOrderItemSetRequest request) {
        Integer expectedQty = stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
        Integer qaQty = stockinOrderTaskItemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getQaQty).sum();
        String content = String.format("SKU【%s】,预收货 %s件,现收货 %s 件，需质检 %s 件，装入内部箱%s", sku, expectedQty, request.getQty(), qaQty, request.getInternalBoxCode());
        stockinOrderLogService.addLog(stockinOrderId, StockinOrderLogTypeEnum.RECEIVE_CHECK_NUMBER.getName(), content);
    }

    @Transactional
    public StockinOrderItemEntity createItem(StockinOrderTaskEntity taskEntity, StockinOrderTaskItemEntity taskItemEntity,
                                             StockinOrderEntity orderEntity, StockinOrderTaskItemDetail detail) {
        StockinOrderItemEntity entity = new StockinOrderItemEntity();
        BeanUtilsEx.copyProperties(taskItemEntity, entity, "createDate", "createBy", "updateDate", "updateBy", "version");
        entity.setStockinOrderId(orderEntity.getStockinOrderId());
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        //创建新明细数据时已推送数量为0
        entity.setPushedQty(0);
        entity.setLocation(TenantContext.getTenant());
        entity.setInternalBoxCode(detail.getInternalBoxCode());
        entity.setQty(detail.getQty());
        if (orderEntity.getOrderType().equals(1)) {
            entity.setBranchPurchasePlanNo("V" + taskItemEntity.getPurchasePlanNo());
        }
        entity.setBrandName(taskItemEntity.getBrandName());
        entity.setStatus(stockinOrderTaskItemService.setSkuStatus(taskItemEntity));
        stockinOrderItemService.save(entity);
        if (StringUtils.hasText(taskItemEntity.getBrandName())) {
            orderEntity.setHasBrand(1);
            stockinOrderService.updateById(orderEntity);
        }
        return entity;
    }

    public StockinOrderEntity findTopByTaskId(Integer taskId) {
        return this.getOne(new QueryWrapper<StockinOrderEntity>().lambda().eq(StockinOrderEntity::getTaskId, taskId).last("limit 1"));
    }

    public StockinOrderEntity findTopByLogisticsNo(String logisticsNo) {
        return this.getOne(new QueryWrapper<StockinOrderEntity>().lambda().eq(StockinOrderEntity::getLogisticsNo, logisticsNo).last("limit 1"));
    }

    public StockinOrderTaskSkuItem queryBarcode(String supplierDeliveryBoxCode, String barcode) {
        StockinOrderTaskEntity entity = stockinOrderTaskService.findTopBySupplierDeliveryBoxCode(supplierDeliveryBoxCode);
        if (entity == null) {
            throw new BusinessServiceException(ExceptionConstants.NOT_FOUND_STOCKIN_ORDER_TASK);
        }
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcodeFromStockin(barcode, Collections.singletonList(entity.getTaskId()));
        if (Objects.nonNull(productSpecInfoEntity)) {
            return buildStockinOrderTaskSkuItemBySku(productSpecInfoEntity.getSku(), entity, productSpecInfoEntity);
        }
        List<StockinOrderTaskItemEntity> allByTaskId = stockinOrderTaskItemService.findAllByTaskId(entity.getTaskId());
        return buildStockinOrderTaskSkuItem(barcode, allByTaskId);
    }

    private StockinOrderTaskSkuItem buildStockinOrderTaskSkuItemBySku(String sku, StockinOrderTaskEntity entity, ProductSpecInfoEntity productSpecInfoEntity) {
        List<StockinOrderTaskItemEntity> allByTaskIdAndSku = stockinOrderTaskItemService.findAllByTaskIdAndSku(entity.getTaskId(), sku);
        StockinOrderTaskSkuItem item = new StockinOrderTaskSkuItem();
        StockinOrderTaskItemEntity taskItemEntity = allByTaskIdAndSku.get(0);
        item.setShowNotice(Boolean.FALSE);
        item.setIsNeedQa(taskItemEntity.getIsNeedQa());
        item.setQaQty(taskItemEntity.getQaQty());
        item.setExpectedQty(taskItemEntity.getExpectedQty());
        item.setProductId(productSpecInfoEntity.getProductId());
        item.setSku(productSpecInfoEntity.getSku());
        item.setSpecId(productSpecInfoEntity.getSpecId());
        item.setImageUrl(productSpecInfoEntity.getImageUrl());
        item.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
        item.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
        item.setBarcode(productSpecInfoEntity.getBarcode());
        return item;
    }

    private StockinOrderTaskSkuItem buildStockinOrderTaskSkuItem(String barcode, List<StockinOrderTaskItemEntity> orderTaskItemEntityList) {

        StockinOrderTaskSkuItem item = new StockinOrderTaskSkuItem();
        String finalBarcode = barcode;
        Optional<StockinOrderTaskItemEntity> any = orderTaskItemEntityList.stream().filter(itemEntity -> {
            //如果是伟跃，只判断入库单明细的sellersku
            if (LocationEnum.WEIYUE.name().equals(TenantContext.getTenant())
                && StringUtils.hasText(itemEntity.getSellerBarcode())) {
                return barcode.equals(itemEntity.getSellerBarcode());
            }
            return barcode.equals(itemEntity.getBarcode())
                || barcode.equals(itemEntity.getSellerBarcode());
        }).findAny();
        if (any.isPresent()) {
            StockinOrderTaskItemEntity taskItemEntity = any.get();
            item.setShowNotice(Boolean.FALSE);
            item.setIsNeedQa(taskItemEntity.getIsNeedQa());
            item.setQaQty(taskItemEntity.getQaQty());
            item.setExpectedQty(taskItemEntity.getExpectedQty());
            finalBarcode = taskItemEntity.getBarcode();
        } else {
            item.setShowNotice(Boolean.TRUE);
            item.setNotice("商品条码不在当前工厂到货计划中，请核实。");
            Integer zero = Integer.valueOf(0);
            item.setIsNeedQa(zero);
            item.setQaQty(zero);
            item.setScanQty(zero);
            item.setExpectedQty(zero);
        }
        ProductSpecInfoEntity productSpecInfoEntity = productStoreSkuMappingService.validBarcode(finalBarcode);
        item.setBarcode(productSpecInfoEntity.getBarcode());
        item.setProductId(productSpecInfoEntity.getProductId());
        item.setSku(productSpecInfoEntity.getSku());
        item.setSpecId(productSpecInfoEntity.getSpecId());
        item.setImageUrl(productSpecInfoEntity.getImageUrl());
        item.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
        item.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
        item.setBarcode(productSpecInfoEntity.getBarcode());
        return item;
    }

    @Transactional
    public void changeStatus(StockinOrderEntity orderEntity, String status) {
        LOGGER.info("入库单 {}状态由 {}变更为 {}", orderEntity.getStockinOrderNo(), orderEntity.getStatus(), status);
        orderEntity.setStatus(status);
        orderEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(orderEntity);
    }

    /**
     * 巡检推送上架变更入库单状态
     */
    @Transactional
    public void routingInSpectShelveChangeStatus(String internalBoxCode, String status) {
        LOGGER.info("巡检推送上架准备变更入库单状态, 内部箱号 {}", internalBoxCode);
        StockInternalBoxEntity internalBoxEntity = stockInternalBoxService.getByInternalBoxCodeAndStatus(internalBoxCode, status);
        if (Objects.isNull(internalBoxEntity))
            return;
        List<StockInternalBoxItemEntity> internalBoxItemList = internalBoxItemService.getByInternalBoxId(internalBoxEntity.getInternalBoxId());
        if (CollectionUtils.isEmpty(internalBoxItemList))
            return;
        List<String> stockinOrderNoList = internalBoxItemList.stream().map(StockInternalBoxItemEntity::getStockInOrderNo)
            .filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        List<StockinOrderEntity> stockinOrderList = stockinOrderService.getByStockinOrderNoList(stockinOrderNoList);
        if (CollectionUtils.isEmpty(stockinOrderList))
            return;
        List<Integer> stockinOrderIdList = stockinOrderList.stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());
        List<StockinOrderItemEntity> itemEntityList = stockinOrderItemService.findAllByStockinOrderIdList(stockinOrderIdList);
        if (CollectionUtils.isEmpty(itemEntityList))
            return;
        List<String> receiveBoxList = itemEntityList.stream().map(StockinOrderItemEntity::getInternalBoxCode).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        List<StockInternalBoxEntity> internalBoxEntityList = stockInternalBoxService.getByInternalBoxCodeListAndStatus(receiveBoxList, null);
        if (!CollectionUtils.isEmpty(internalBoxEntityList)) {
            List<StockInternalBoxEntity> filterInternalBoxEntityList = internalBoxEntityList.stream()
                .filter(item -> !StockInternalBoxStatusEnum.WAIT_SHELVE.name().equals(item.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterInternalBoxEntityList)) {
                stockinOrderList.forEach(item -> {
                    item.setStatus(StockinOrderStatusEnum.PENDING_SHELVE.name());
                    item.setUpdateBy(loginInfoService.getName());
                });
                stockinOrderService.updateBatchById(stockinOrderList);
            }
        }
    }

    /**
     * 批量更新入库单
     */
    @Transactional
    public void batchChangeStatus(List<StockinOrderEntity> orderEntityList, String status, String contentStr, String changType) {
        List<StockinOrderLogEntity> logEntityList = new ArrayList<>();
        Map<String, String> stockinOrderStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_ORDER_STATUS.getName());

        orderEntityList.forEach(item -> {
            String oldStatus = item.getStatus();
            item.setStatus(status);
            item.setUpdateBy(loginInfoService.getName());

            String content = String.format(contentStr + " 入库单 【%s】状态由 【%s】变更为 【%s】", item.getStockinOrderNo(),
                stockinOrderStatusEnumMap.get(oldStatus),
                stockinOrderStatusEnumMap.get(status));
            StockinOrderLogEntity log = stockinOrderLogService.buildLog(item.getStockinOrderId(), changType, content);
            logEntityList.add(log);
            LOGGER.info("入库单 {}状态由 {}变更为 {}", item.getStockinOrderNo(), oldStatus, status);
        });
        this.updateBatchById(orderEntityList);
        stockinOrderLogService.saveBatch(logEntityList);
    }

    /**
     * 入库单列表
     */
    public PageResponse<StockinOrderListResponse> getStockinOrderListByRequest(StockinOrderListRequest request) {
        PageResponse<StockinOrderListResponse> pageResponse = new PageResponse<>();
        Page page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        request.setLocation(TenantContext.getTenant());
        if (StringUtils.hasText(request.getSku())) {
            int count = productSpecInfoService.count(new LambdaQueryWrapper<ProductSpecInfoEntity>().likeRight(ProductSpecInfoEntity::getSku, request.getSku()));
            if (count > 1000)
                throw new BusinessServiceException("sku查询范围过大，请重新确认！");

        }
        IPage<StockinOrderListInfo> pageResult = stockinOrderMapper.pageSearchList(page, request);
        List<StockinOrderListInfo> resultList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(resultList)) {
            pageResponse.setTotalCount(0);
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        this.buildStockInPageItemInfo(resultList);
        Map<String, String> stockinTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName());
        Map<String, String> stockinOrderStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_ORDER_STATUS.getName());
        List<String> isPreQaBySupplierDeliveryNo = stockinOrderItemService.getBaseMapper().getIsPreQaBySupplierDeliveryNo(resultList.stream().map(StockinOrderListInfo::getSupplierDeliveryNo).collect(Collectors.toList()));
        Map<Integer, String> spaceMap = bdSpaceService.list(new LambdaQueryWrapper<BdSpaceEntity>().select(BdSpaceEntity::getSpaceId, BdSpaceEntity::getSpaceName).in(BdSpaceEntity::getSpaceId, resultList.stream().map(StockinOrderListInfo::getSpaceId).collect(Collectors.toList())))
            .stream().collect(Collectors.toMap(BdSpaceEntity::getSpaceId, BdSpaceEntity::getSpaceName));
        List<StockinOrderListResponse> responseList = resultList.stream().map(item -> {
            StockinOrderListResponse response = new StockinOrderListResponse();
            BeanUtils.copyProperties(item, response);
            response.setSpaceName(spaceMap.get(item.getSpaceId()));
            response.setStockinTypeStr(StringUtils.hasText(item.getStockinType()) ? stockinTypeEnumMap.get(item.getStockinType()) : null);
            if (StringUtils.hasText(item.getBranchPurchasePlanNo())) {
                response.setPurchasePlanNo(item.getBranchPurchasePlanNo());
            }
            response.setStatusStr(StringUtils.hasText(item.getStatus()) ? stockinOrderStatusEnumMap.get(item.getStatus()) : null);
            if (response.getWaitReturnQty() > 0) {
                response.setWaitReturnQty(response.getWaitReturnQty() - response.getHasReturnQty());
            }
            response.setReceiptPlace(enumConversionChineseUtils.getLocationValue(item.getReceiptPlace()));
            response.setIsPreQa(CollectionUtils.isEmpty(isPreQaBySupplierDeliveryNo) ? 0 : isPreQaBySupplierDeliveryNo.contains(item.getStockinOrderNo()) ? 1 : 0);
            return response;
        }).collect(Collectors.toList());
        if (StringUtils.hasText(request.getSortField())) {
            SortListUtils.sortByMethod(responseList, reflectUtils.getMethodName(request.getSortField()), request.getSortOrder());
        }
        pageResponse.setTotalCount(stockinOrderMapper.pageSearchListCount(request));
        pageResponse.setContent(responseList);
        return pageResponse;
    }

    public StockinOrderStatisticsResponse statisticsStockinOrderItemList(StockinOrderListRequest request) {
        request.setLocation(TenantContext.getTenant());
        StockinOrderStatisticsResponse response = this.baseMapper.statisticsStockinOrderItemList(request);
        response.setVarianceQty(response.getQtyTotal() - response.getShelvedQtyTotal() - response.getReturnQtyTotal());
        return response;
    }

    /**
     * 各个入库单状态对应数量
     */
    public StockinOrderStatusCountResponse getStatusCount() {
        StockinOrderStatusCountResponse response = new StockinOrderStatusCountResponse();
        response.setAllNum(0);
        Map<String, StockinOrderStatusDTO> map = new HashMap<>(32);
        this.getBaseMapper().statusCount().forEach(item -> {
            response.setAllNum(response.getAllNum() + item.getQty());
            map.put(item.getStatus(), item);
        });
        response.setReceivingNum(map.getOrDefault(StockinOrderStatusEnum.RECEIVING.name(), new StockinOrderStatusDTO()).getQty());
        response.setPendingQcNum(map.getOrDefault(StockinOrderStatusEnum.PENDING_QC.name(), new StockinOrderStatusDTO()).getQty());
        response.setQcProcessingNum(map.getOrDefault(StockinOrderStatusEnum.QC_PROCESSING.name(), new StockinOrderStatusDTO()).getQty());
        response.setPendingShelvingNum(map.getOrDefault(StockinOrderStatusEnum.PENDING_SHELVE.name(), new StockinOrderStatusDTO()).getQty());
        response.setShelvingNum(map.getOrDefault(StockinOrderStatusEnum.SHELVING.name(), new StockinOrderStatusDTO()).getQty());
        response.setCheckingNum(map.getOrDefault(StockinOrderStatusEnum.CHECKING.name(), new StockinOrderStatusDTO()).getQty());
        return response;
    }

    /**
     * 根据入库单号获取入库单Entity
     */
    public StockinOrderEntity getByStockinOrderNo(String stockinOrderNo) {
        LambdaQueryWrapper<StockinOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(stockinOrderNo)) {
            queryWrapper.eq(StockinOrderEntity::getStockinOrderNo, stockinOrderNo);
        }
        StockinOrderEntity stockinOrderEntity = this.getOne(queryWrapper);
        if (Objects.isNull(stockinOrderEntity)) {
            throw new BusinessServiceException("找不到入库单");
        }
        return stockinOrderEntity;
    }

    /**
     * 根据入库单号获取入库单Entity
     */
    public StockinOrderEntity findByStockinOrderNo(String stockinOrderNo) {
        LambdaQueryWrapper<StockinOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinOrderEntity::getStockinOrderNo, stockinOrderNo);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据入库任务号获取入库单Entity
     */
    public StockinOrderEntity getByTaskId(Integer taskId) {
        LambdaQueryWrapper<StockinOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinOrderEntity::getTaskId, taskId);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据入库单号list获取入库单列表
     */
    public List<StockinOrderEntity> getByStockinOrderNoList(List<String> stockinOrderNoList) {
        LambdaQueryWrapper<StockinOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockinOrderEntity::getStockinOrderNo, stockinOrderNoList);
        return this.list(queryWrapper);
    }


    /**
     * 导出
     */
    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_ORDER_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockinOrderListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockinOrderListRequest.class);
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        PageResponse<StockinOrderListResponse> pageResponse = getStockinOrderListByRequest(downloadRequest);
        List<StockinOrderListResponse> list = pageResponse.getContent();
        List<StockinOrderListExport> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            resultList = list.stream().map(item -> {
                StockinOrderListExport export = new StockinOrderListExport();
                BeanUtilsEx.copyProperties(item, export);
                return export;
            }).collect(Collectors.toList());
        }
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }

    /**
     * 自动核对确认
     *
     * @param orderEntityList
     */
    public void autoCheckConfirm(List<StockinOrderEntity> orderEntityList) {
        if (CollectionUtils.isEmpty(orderEntityList))
            return;

        List<StockinOrderTaskEntity> orderTaskEntityList = stockinOrderTaskService.getBaseMapper().findAllByIds(orderEntityList.stream().map(StockinOrderEntity::getTaskId).distinct().collect(Collectors.toList()));
        List<StockinOrderTaskItemEntity> taskItemEntities = stockinOrderTaskItemService.findAllByTaskIdList(orderTaskEntityList.stream().map(StockinOrderTaskEntity::getTaskId).collect(Collectors.toList()));
        Map<Integer, List<StockinOrderTaskItemEntity>> taskItemEntitiesMap = taskItemEntities.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getTaskId));
        List<StockinOrderLogEntity> stockinOrderLogEntityList = Lists.newArrayList();
        List<StockinShelveTaskItemEntity> shelveTaskItemEntityList = shelveTaskItemService.list(new LambdaQueryWrapper<StockinShelveTaskItemEntity>()
            .in(StockinShelveTaskItemEntity::getSourceId, orderEntityList.stream().map(StockinOrderEntity::getStockinOrderId).distinct().collect(Collectors.toList())));
        String value = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKIN_DELIVERY_CONFIRM.getKey());
        boolean isDeliveryConfirm = StringUtils.hasText(value) && "true".equals(value);
        for (StockinOrderTaskEntity taskEntity : orderTaskEntityList) {
            // 1、入库任务是否完成
            if (!StockinOrderTaskStatusEnum.RECEIVED.name().equals(taskEntity.getStatus()))
                continue;
            // 2、上架数=入库数（出库箱码维度）
            StockinOrderEntity stockinOrderEntity = orderEntityList.stream().filter(o -> o.getTaskId().equals(taskEntity.getTaskId())).findFirst().orElse(null);
            if (stockinOrderEntity == null) {
                continue;
            }
            List<StockinShelveTaskItemEntity> shelveTaskItemList = shelveTaskItemEntityList.stream().filter(o -> o.getSourceId().equals(stockinOrderEntity.getStockinOrderId())).collect(Collectors.toList());
            int shelveQty = shelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getShelvedQty).sum();
            int returnQty = shelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getReturnedQty).sum();
            int stockinQty = shelveTaskItemList.stream().mapToInt(StockinShelveTaskItemEntity::getStockinQty).sum();
            if (shelveQty + returnQty >= stockinQty) {
                if (isDeliveryConfirm) {
                    int expectedQty = taskItemEntitiesMap.get(taskEntity.getTaskId()).stream().filter(detail -> Objects.nonNull(detail.getExpectedQty())).mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
                    int actualityStockInQty = taskItemEntitiesMap.get(taskEntity.getTaskId()).stream().filter(detail -> Objects.nonNull(detail.getStockinQty())).mapToInt(StockinOrderTaskItemEntity::getStockinQty).sum();
                    stockinOrderEntity.setDeliveryConfirmStatus(expectedQty > actualityStockInQty ? StockinOrderDeliveryConfirmStatusEnum.WAIT_CONFIRM.name() : StockinOrderDeliveryConfirmStatusEnum.IGNORE.name());
                }
                stockinOrderEntity.setStatus(StockinOrderStatusEnum.COMPLETED.name());
                stockinOrderEntity.setUpdateBy(loginInfoService.getName());
                StockinOrderLogEntity logEntity = stockinOrderLogService.buildLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.COMPLETE_STOCKIN_ORDER.getName(), "入库单上架完成，自动核对确认");
                stockinOrderLogEntityList.add(logEntity);
                this.updateById(stockinOrderEntity);
                stockinOrderPostProcessingService.processingOrder(stockinOrderEntity);
            }
        }
        // 记录入库单日志
        if (!CollectionUtils.isEmpty(stockinOrderLogEntityList)) {
            stcokinOrderTimeService.addTimeBatch(stockinOrderLogEntityList.stream().map(StockinOrderLogEntity::getStockinOrderId).collect(Collectors.toList()), StockinOrderLogTypeEnum.COMPLETE_STOCKIN_ORDER);
            stockinOrderLogService.saveBatch(stockinOrderLogEntityList);
        }
    }

    /**
     * 入库单上架完成反馈erp，已入库
     *
     * @param stockinOrderEntityList
     * @param operator
     */
    public void feedbackShelvedToErp(List<StockinOrderEntity> stockinOrderEntityList, String operator) {
        if (CollectionUtils.isEmpty(stockinOrderEntityList)) {
            return;
        }
        List<StockinOrderTaskEntity> orderTaskEntityList = stockinOrderTaskService.listByIds(stockinOrderEntityList.stream().map(StockinOrderEntity::getTaskId).distinct().collect(Collectors.toList()));
        List<ErpFeedbackShelvedInfo> allShelvedInfoList = new LinkedList<>();
        List<String> allPackageInfoList = new LinkedList<>();
        orderTaskEntityList.stream().collect(Collectors.groupingBy(StockinOrderTaskEntity::getSupplierDeliveryNo))
            .forEach((supplierDeliveryNo, taskList) -> {
                ErpFeedbackShelvedInfo feedbackShelvedInfo = stockinShelveTaskOpService.getIsCheckedInfo(supplierDeliveryNo, stockinOrderEntityList, taskList);
                if (feedbackShelvedInfo != null) {
                    if (!StockinTypeEnum.ALLOT.name().equalsIgnoreCase(taskList.get(0).getStockinType())) {
                        allShelvedInfoList.add(feedbackShelvedInfo);
                    }
                    List<String> collect = feedbackShelvedInfo.getSupplierDeliveryBoxList().stream().filter(item -> item.getIsChecked().equals(1))
                        .map(ErpFeedbackShelvedBoxInfo::getSupplierDeliveryBoxNo).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect))
                        allPackageInfoList.addAll(collect);
                }
            });
        if (CollectionUtils.isEmpty(allShelvedInfoList)) {
            LOGGER.error("未生成入库任务 {}", orderTaskEntityList.stream().map(StockinOrderTaskEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(StringConstant.COMMA)));
            return;
        }
/*        messageProducer.sendMessage(KafkaConstant.STOCKIN_FEEDBACK_PACKAGE_QTY_TOPIC_NAME, KafkaConstant.STOCKIN_FEEDBACK_PACKAGE_QTY_TOPIC,
                new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), idListRequest));*/
        if (!CollectionUtils.isEmpty(allPackageInfoList)) {
            List<StockinOrderTaskEntity> stockinOrderTaskEntityList = orderTaskEntityList.stream().filter(item -> allPackageInfoList.contains(item.getSupplierDeliveryBoxCode())).collect(Collectors.toList());
            List<StockinOrderEntity> stockinOrderEntities = stockinOrderEntityList.stream().filter(item -> allPackageInfoList.contains(item.getSupplierDeliveryBoxCode())).collect(Collectors.toList());
            //反馈让步接收数
            stockinShelveSyncErpService.processConcessionSyncErp(stockinOrderEntities);
            //反馈包装数量
            stockinShelveSyncErpService.processFeedbackPackageQty(stockinOrderTaskEntityList);
            //采购接收单完成同步SCM
            stockinShelveSyncErpService.syncPurchaseOrderToScm(stockinOrderEntities);
        }
        if (CollectionUtils.isEmpty(allShelvedInfoList)) {
            return;
        }

        //回填月台任务
        allShelvedInfoList.stream().map(ErpFeedbackShelvedInfo::getSupplierDeliveryNo).distinct()
            .forEach(supplierDeliveryNo -> platformScheduleService.stockinOrderCompleteUpdatePlatformStatus(supplierDeliveryNo));


        ErpFeedbackShelvedRequest request = new ErpFeedbackShelvedRequest();
        request.setLocation(TenantContext.getTenant());
        request.setOperator(operator);
        request.setSupplierDeliveryNoList(allShelvedInfoList);
        erpApiService.feedbackShelvedAsync(request);
    }

    /**
     * 根据入库单id获取月台信息
     *
     * @param stockinOrderId
     * @return
     */
    public StockPlatformScheduleEntity getPlatformScheduleInfoByStockinOrderId(Integer stockinOrderId) {
        return this.baseMapper.getPlatformScheduleInfoByStockinOrderId(stockinOrderId);
    }

    private void buildStockInPageItemInfo(List<StockinOrderListInfo> resultList) {
        List<String> stockinOrderNos = resultList.stream().map(StockinOrderListInfo::getStockinOrderNo).distinct().collect(Collectors.toList());
        //查询actualReturnQty
        Map<String, Integer> actualReturnQtyMap = stockinReturnProductTaskItemService.getBaseMapper().findActualReturnQty(stockinOrderNos)
            .stream().collect(Collectors.toMap(StockinOrderListInfo::getStockinOrderNo, StockinOrderListInfo::getActualReturnQty));
        resultList.forEach(detail -> {
            detail.setActualReturnQty(actualReturnQtyMap.getOrDefault(detail.getStockinOrderNo(), 0));
        });

        List<StockInPageItemInfo> stockInPageItemInfoList = stockinOrderItemService.getStockInPageItemInfo(resultList.stream().map(StockinOrderListInfo::getStockinOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(stockInPageItemInfoList)) {
            return;
        }
        Map<Integer, StockInPageItemInfo> stockInPageItemInfoMap = stockInPageItemInfoList.stream().collect(Collectors.toMap(StockInPageItemInfo::getStockinOrderId, Function.identity()));
        resultList.forEach(detail -> {
            if (!stockInPageItemInfoMap.containsKey(detail.getStockinOrderId())) {
                return;
            }
            detail.setQty(stockInPageItemInfoMap.get(detail.getStockinOrderId()).getQty());
            detail.setBranchPurchasePlanNo(stockInPageItemInfoMap.get(detail.getStockinOrderId()).getBranchPurchasePlanNo());
            detail.setPurchasePlanNo(stockInPageItemInfoMap.get(detail.getStockinOrderId()).getPurchasePlanNo());
        });
    }

    private void sendTryOnTask(Integer platformScheduleId, String sku, Integer productId, String stockinOrderNo) {
        List<StockPlatformScheduleItemEntity> scheduleItemEntities = platformScheduleItemService.listByPlatformScheduleIdAndSku(platformScheduleId, sku);
        if (CollectionUtils.isEmpty(scheduleItemEntities)) {
            return;
        }
        ProductTryOnTaskSaveRequest saveRequest = new ProductTryOnTaskSaveRequest();
        saveRequest.setLabelName(scheduleItemEntities.get(0).getLabelAttributeNames());
        saveRequest.setSku(sku);
        saveRequest.setProductId(productId);
        saveRequest.setStockinOrderNo(stockinOrderNo);
        saveRequest.setStorePlatformIds(scheduleItemEntities.get(0).getStorePlatformIds());
        messageProducer.sendMessage(KafkaConstant.PRODUCT_TRY_ON_TASK_TOPIC_NAME, KafkaConstant.PRODUCT_TRY_ON_TASK_TOPIC,
            new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), saveRequest));
    }
}
