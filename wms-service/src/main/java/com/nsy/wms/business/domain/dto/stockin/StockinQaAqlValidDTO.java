package com.nsy.wms.business.domain.dto.stockin;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-05-14 15:52
 */
public class StockinQaAqlValidDTO {
    /**
     * 轻微缺陷数量
     */
    private Integer minorDefectCount;

    /**
     * 严重缺陷数量
     */
    private Integer majorDefectCount;

    /**
     * 致命缺陷数量
     */
    private Integer criticalDefectCount;

    /**
     * 仓库ID
     */
    private Integer spaceId;

    /**
     * 质检数量
     */
    private Integer qaQty;

    /**
     * 无参构造函数
     */
    public StockinQaAqlValidDTO() {
    }

    /**
     * 满入参构造函数
     *
     * @param minorDefectCount    轻微缺陷数量
     * @param majorDefectCount    严重缺陷数量
     * @param criticalDefectCount 致命缺陷数量
     * @param spaceId             仓库ID
     * @param qaQty               质检数量
     */
    public StockinQaAqlValidDTO(Integer minorDefectCount, Integer majorDefectCount,
                                Integer criticalDefectCount, Integer spaceId, Integer qaQty) {
        this.minorDefectCount = minorDefectCount;
        this.majorDefectCount = majorDefectCount;
        this.criticalDefectCount = criticalDefectCount;
        this.spaceId = spaceId;
        this.qaQty = qaQty;
    }

    public Integer getMinorDefectCount() {
        return minorDefectCount;
    }

    public void setMinorDefectCount(Integer minorDefectCount) {
        this.minorDefectCount = minorDefectCount;
    }

    public Integer getMajorDefectCount() {
        return majorDefectCount;
    }

    public void setMajorDefectCount(Integer majorDefectCount) {
        this.majorDefectCount = majorDefectCount;
    }

    public Integer getCriticalDefectCount() {
        return criticalDefectCount;
    }

    public void setCriticalDefectCount(Integer criticalDefectCount) {
        this.criticalDefectCount = criticalDefectCount;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getQaQty() {
        return qaQty;
    }

    public void setQaQty(Integer qaQty) {
        this.qaQty = qaQty;
    }
}
