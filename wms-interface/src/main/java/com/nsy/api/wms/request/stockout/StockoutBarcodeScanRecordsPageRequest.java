package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/27 10:31
 */
@ApiModel(value = "StockoutBarcodeScanRecordsRequest", description = "贴码扫描请求单号")
public class StockoutBarcodeScanRecordsPageRequest extends PageRequest {

    /**
     * 业务单号
     */
    @ApiModelProperty(value = "业务单号", name = "businessNo")
    private String orderNo;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", name = "businessType")
    private String businessType;


    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    /**
     * 箱子编号
     */
    @ApiModelProperty(value = "箱子编号", name = "shipmentBoxCode")
    private String shipmentBoxCode;

    /**
     * 平台
     */
    @ApiModelProperty(value = "平台", name = "platformName")
    private String platformName;
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", name = "operateName")
    private String operateName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间", name = "operateTimeStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTimeStart;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间", name = "operateTimeEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTimeEnd;

    @ApiModelProperty(value = "是否模糊查询", name = "isFuzzy")
    private Integer isFuzzy = 0;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getOperateName() {
        return operateName;
    }

    public void setOperateName(String operateName) {
        this.operateName = operateName;
    }

    public Date getOperateTimeStart() {
        return operateTimeStart;
    }

    public void setOperateTimeStart(Date operateTimeStart) {
        this.operateTimeStart = operateTimeStart;
    }

    public Date getOperateTimeEnd() {
        return operateTimeEnd;
    }

    public void setOperateTimeEnd(Date operateTimeEnd) {
        this.operateTimeEnd = operateTimeEnd;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public Integer getIsFuzzy() {
        return isFuzzy;
    }

    public void setIsFuzzy(Integer isFuzzy) {
        this.isFuzzy = isFuzzy;
    }

    public String getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }
}
