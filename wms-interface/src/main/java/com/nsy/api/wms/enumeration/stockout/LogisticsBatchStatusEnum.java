package com.nsy.api.wms.enumeration.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 待处理WAIT_DEAL、运价异常EXCEPTION、已核对COMPLETION
 * HXD
 * 2023/3/16
 **/

public enum LogisticsBatchStatusEnum {

    WAIT_DEAL("待核对"),
    EXCEPTION("运价异常"),
    DONE("已核对");

    String desc;

    LogisticsBatchStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }


    public static LogisticsBatchStatusEnum getEnumByDescOrName(String desc) {
        return Arrays.stream(values())
                .filter(s -> s.getDesc().equalsIgnoreCase(desc) || s.name().equalsIgnoreCase(desc))
                .findAny()
                .orElseThrow(() -> new BusinessServiceException(String.format("枚举值%s不存在，必须是[%s]其中一个", desc, Arrays.stream(values()).map(LogisticsBatchStatusEnum::getDesc).collect(Collectors.joining("、")))));
    }

}
