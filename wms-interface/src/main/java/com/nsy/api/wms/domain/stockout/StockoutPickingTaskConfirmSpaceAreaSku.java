package com.nsy.api.wms.domain.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutPickingTaskConfirmSpaceAreaSku", description = "拣货任务库区信息实体")
public class StockoutPickingTaskConfirmSpaceAreaSku {

    @ApiModelProperty(value = "主键id", name = "taskItemId")
    private String taskItemId;
    @ApiModelProperty(value = "拣货库位", name = "positionCode")
    private String positionCode;
    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "条形码", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "待拣件数", name = "expectedQty")
    private Integer expectedQty;

    @ApiModelProperty(value = "已拣件数", name = "pickedQty")
    private Integer pickedQty;

    @ApiModelProperty(value = "是否缺货", name = "isLack")
    private Integer isLack;
    /**
     * 状态
     */
    @ApiModelProperty(value = "拣货状态: PICKED拣货完成，WAIT_PICK待拣货，PICKING拣货中", name = "status")
    private String status;

    @ApiModelProperty(value = "出库单备注", name = "description")
    private String description;

    @ApiModelProperty(value = "真空cn", name = "vacuumCn")
    private String vacuumCn;

    @ApiModelProperty(value = "真空", name = "vacuum")
    private String vacuum;

    @ApiModelProperty(value = "是否店铺首单", name = "isFirstOrderByStore")
    private Integer isFirstOrderByStore;

    @ApiModelProperty(value = "商品标签", name = "productTag")
    private List<String> productTag;

    @ApiModelProperty(value = "是否引流款", name = "isLeadGeneration")
    private Boolean isLeadGeneration;

    @ApiModelProperty("包装方式")
    private String packageVacuum;

    public Boolean getIsLeadGeneration() {
        return isLeadGeneration;
    }

    public void setIsLeadGeneration(Boolean isLeadGeneration) {
        this.isLeadGeneration = isLeadGeneration;
    }

    public List<String> getProductTag() {
        return productTag;
    }

    public void setProductTag(List<String> productTag) {
        this.productTag = productTag;
    }

    public Integer getIsFirstOrderByStore() {
        return isFirstOrderByStore;
    }

    public void setIsFirstOrderByStore(Integer isFirstOrderByStore) {
        this.isFirstOrderByStore = isFirstOrderByStore;
    }

    public String getVacuum() {
        return vacuum;
    }

    public void setVacuum(String vacuum) {
        this.vacuum = vacuum;
    }

    public String getVacuumCn() {
        return vacuumCn;
    }

    public void setVacuumCn(String vacuumCn) {
        this.vacuumCn = vacuumCn;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Integer getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(Integer pickedQty) {
        this.pickedQty = pickedQty;
    }

    public Integer getIsLack() {
        return isLack;
    }

    public void setIsLack(Integer isLack) {
        this.isLack = isLack;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(String taskItemId) {
        this.taskItemId = taskItemId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPackageVacuum() {
        return packageVacuum;
    }

    public void setPackageVacuum(String packageVacuum) {
        this.packageVacuum = packageVacuum;
    }
}
