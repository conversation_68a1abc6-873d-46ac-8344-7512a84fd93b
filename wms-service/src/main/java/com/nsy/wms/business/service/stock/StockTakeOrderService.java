package com.nsy.wms.business.service.stock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.constants.StockTakeOrderConstant;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockTakeOrderAuditStateEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockPlanTypeEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockTaskModeEnum;
import com.nsy.api.wms.enumeration.stockout.StockTakeStockTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockTakeOrderRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockTakeOrderDetailResponse;
import com.nsy.api.wms.response.stock.StockTakeOrderExportResponse;
import com.nsy.api.wms.response.stock.StockTakeOrderResponse;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.domain.bo.stock.StockPDATakeStockTaskSubmitBo;
import com.nsy.wms.business.domain.bo.stock.StockTakeOrderSaveBo;
import com.nsy.wms.business.manage.user.upload.StockTakeOrderImport;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockTakeOrderEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockTakeOrderMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/15 10:28
 */
@Service
public class StockTakeOrderService extends ServiceImpl<StockTakeOrderMapper, StockTakeOrderEntity> implements IProcessUploadDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockTakeOrderService.class);

    @Autowired
    private StockTakeOrderItemService stockTakeOrderItemService;

    @Autowired
    private StockTakeOrderLogService stockTakeOrderLogService;

    @Autowired
    private StockService stockService;

    @Autowired
    private StockTakeStockTaskService stockTakeStockTaskService;

    @Autowired
    private StockTakeStockTaskItemService stockTakeStockTaskItemService;

    @Autowired
    private StockTakeStockLogService stockTakeStockLogService;

    @Autowired
    private StockTakeOrderMapper stockTakeOrderMapper;

    @Autowired
    private BdPositionService positionService;

    @Autowired
    private StockPDATakeStockTaskService stockPDATakeStockTaskService;

    @Autowired
    private ProductSpecInfoService specInfoService;

    @Autowired
    private StockInternalBoxService internalBoxService;

    /**
     * 新增盘点单及明细
     *
     * @param stockTakeOrderSaveBo
     */
    @Transactional(rollbackFor = Exception.class)
    public void buildStockTakeOrder(StockTakeOrderSaveBo stockTakeOrderSaveBo) {
        LambdaQueryWrapper<StockTakeOrderEntity> orderEmptyWrapper = new LambdaQueryWrapper<StockTakeOrderEntity>()
                .eq(StockTakeOrderEntity::getTaskId, stockTakeOrderSaveBo.getTakeEntity().getTaskId());
        StockTakeOrderEntity orderEntity = this.getOne(orderEmptyWrapper);
        if (ObjectUtil.isNull(orderEntity)) {
            orderEntity = new StockTakeOrderEntity();
            orderEntity.setStockTakeOrderNo(FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCK_TAKE_ORDER));
            orderEntity.setLocation(stockTakeOrderSaveBo.getTakeEntity().getLocation());
            orderEntity.setStatus(StockTakeOrderAuditStateEnum.PENDING.getCode());
            orderEntity.setSpaceAreaQty(stockTakeOrderSaveBo.getTakeEntity().getSpaceAreaQty());
            orderEntity.setPositionQty(stockTakeOrderSaveBo.getTakeEntity().getPositionQty());
            orderEntity.setOperator(stockTakeOrderSaveBo.getTakeEntity().getCreateBy());
            orderEntity.setTaskId(stockTakeOrderSaveBo.getTakeEntity().getTaskId());
            orderEntity.setOperateDate(new Date());
            this.save(orderEntity);
            //保存操作日志
            stockTakeOrderLogService.saveStockTakeOrderLog(orderEntity.getTakeOrderId(), "盘点单生成", "盘点任务完成生成盘点单");
            //系统自动审核通过
            this.auditStockTakeOrder(orderEntity);
        }
        stockTakeOrderItemService.createTakeItem(stockTakeOrderSaveBo.getTakeItemEntityList(), orderEntity.getTakeOrderId());
    }

    public void updateSpaceAreaAndPositionQty(Integer spaceAreaQty, Integer positionQty, Integer taskId) {
        this.update(new UpdateWrapper<StockTakeOrderEntity>().lambda()
                .eq(StockTakeOrderEntity::getTaskId, taskId)
                .set(StockTakeOrderEntity::getSpaceAreaQty, spaceAreaQty)
                .set(StockTakeOrderEntity::getPositionQty, positionQty));
    }

    /**
     * 盘点单审核
     *
     * @param orderEntity
     */
    public void auditStockTakeOrder(StockTakeOrderEntity orderEntity) {
        orderEntity.setStatus(StockTakeOrderAuditStateEnum.PASS.getCode());
        this.updateById(orderEntity);
        stockTakeOrderLogService.saveStockTakeOrderLog(orderEntity.getTakeOrderId(), "盘点单审核", "盘点任务系统自动审核完成");
    }

    /**
     * 盘点单分页查询
     *
     * @param request
     * @return
     */
    public PageResponse<StockTakeOrderResponse> pageList(StockTakeOrderRequest request) {
        IPage page = new Page(request.getPageIndex(), request.getPageSize());
        Page<StockTakeOrderResponse> pageList = this.stockTakeOrderMapper.pageList(page, request);
        PageResponse<StockTakeOrderResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }

    /**
     * 盘点单分页查询
     *
     * @param request
     * @return
     */
    public PageResponse<StockTakeOrderExportResponse> exportPageList(IPage page, StockTakeOrderRequest request) {
        Page<StockTakeOrderExportResponse> pageList = this.stockTakeOrderMapper.exportPageList(page, request);
        PageResponse<StockTakeOrderExportResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }

    public StockTakeOrderDetailResponse getStockTakeOrderDetail(Integer takeOrderId) {
        return stockTakeOrderMapper.getStockTakeOrderDetail(takeOrderId);
    }

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_STORE_TAKE_ORDER_IMPORT;
    }

    /**
     * 导入任务，当所有校验都通过才算成功
     *
     * @param request
     * @return
     */
    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<StockTakeOrderImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StockTakeOrderImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        this.importStockTakeOrder(importList, request, response);
        return response;
    }

    /**
     * 为导入文件创建盘点任务
     *
     * @param importList
     * @param response
     */
    public void importStockTakeOrder(List<StockTakeOrderImport> importList, UploadRequest request, UploadResponse response) {
        Map<String, ProductSpecInfoEntity> skuMap = specInfoService.findAllBySkuIn(importList.stream().map(StockTakeOrderImport::getSku).filter(StringUtils::hasText).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));
        response.setDataJsonStr(this.validImportStockTakeOrder(importList, skuMap));
        if (StringUtils.hasText(response.getDataJsonStr())) {
            return;
        }
        //全部校验通过后进行逻辑处理
        LOGGER.info("盘点单导入校验通过开始导入...");
        StockTakeStockTaskEntity taskEntity = new StockTakeStockTaskEntity();
        taskEntity.setLocation(request.getLocation());
        taskEntity.setPlanType(TakeStockPlanTypeEnum.TEMPORARY_INVENTORY.name());
        taskEntity.setTaskGenerateMode(TakeStockTaskModeEnum.BY_POSITION.getCode());
        //由于可能存在新增入库上架盘点（即该库位内无该sku信息）导致不准确，故设置为0由盘点任务赋值该信息
        taskEntity.setPositionQty(0);
        taskEntity.setSpaceAreaQty(0);
        taskEntity.setStatus(StockTakeStockTaskStatusEnum.INVENTORY_COMPLETED.name());
        taskEntity.setCreateBy(request.getCreateBy());
        taskEntity.setOperateDate(new Date());
        stockTakeStockTaskService.save(taskEntity);
        stockTakeStockLogService.addLog(taskEntity.getTaskId(), "盘点任务生成", "盘点任务生成");
        this.buildStockTakeTaskItem(importList, taskEntity, skuMap);
    }

    /**
     * 生成盘点任务明细以及，明细信息进行盘点
     *
     * @param importList
     * @param taskEntity
     */
    private void buildStockTakeTaskItem(List<StockTakeOrderImport> importList, StockTakeStockTaskEntity taskEntity, Map<String, ProductSpecInfoEntity> skuMap) {
        List<StockEntity> allStockEntityList = stockService.list(Wrappers.<StockEntity>lambdaQuery()
                .in(StockEntity::getPositionCode, importList.stream().map(StockTakeOrderImport::getPositionCode).collect(Collectors.toList())));
        Map<String, StockTakeOrderImport> importMap = importList.stream()
                .collect(Collectors.toMap(detail -> detail.getSku().concat(detail.getPositionCode()), Function.identity()));
        Map<String, StockEntity> stockMap = allStockEntityList.stream().collect(Collectors.toMap(detail -> detail.getSku().concat(detail.getPositionCode()), Function.identity()));
        List<StockTakeStockTaskItemEntity> itemEntityList = new ArrayList<>(importList.size());
        for (StockTakeOrderImport item : importList) {
            StockTakeStockTaskItemEntity taskItemEntity = new StockTakeStockTaskItemEntity();
            if (!stockMap.containsKey(item.getSku().concat(item.getPositionCode()))) {
                //当前库存不存在则生成新的数据库存数据
                BdPositionEntity positionEntity = positionService.getPositionByCode(item.getPositionCode());
                taskItemEntity = new StockTakeStockTaskItemEntity();
                taskItemEntity.setLocation(taskEntity.getLocation());
                taskItemEntity.setProductId(skuMap.get(item.getSku()).getProductId());
                taskItemEntity.setSku(item.getSku());
                taskItemEntity.setSpecId(skuMap.get(item.getSku()).getSpecId());
                taskItemEntity.setPositionId(positionEntity.getPositionId());
                taskItemEntity.setPositionCode(positionEntity.getPositionCode());
                taskItemEntity.setSpaceAreaId(positionEntity.getSpaceAreaId());
                taskItemEntity.setSpaceAreaName(positionEntity.getSpaceAreaName());
                taskItemEntity.setAreaId(positionEntity.getAreaId());
                taskItemEntity.setAreaName(positionEntity.getAreaName());
            } else {
                BeanUtils.copyProperties(stockMap.get(item.getSku().concat(item.getPositionCode())), taskItemEntity, "updateBy", "version");
            }
            taskItemEntity.setLocation(taskEntity.getLocation());
            taskItemEntity.setCreateBy(taskEntity.getCreateBy());
            taskItemEntity.setTaskId(taskEntity.getTaskId());
            taskItemEntity.setStatus(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name());
            itemEntityList.add(taskItemEntity);
        }
        stockTakeStockTaskItemService.saveBatch(itemEntityList);
        //生产盘点任务后,进行盘点,生成盘点单逻辑
        itemEntityList.forEach(detail -> {
            StockPDATakeStockTaskSubmitBo submitBo = new StockPDATakeStockTaskSubmitBo();
            BeanUtils.copyProperties(detail, submitBo);
            submitBo.setTargetCode(detail.getPositionCode());
            submitBo.setStock(importMap.get(detail.getSku().concat(detail.getPositionCode())).getStockQty());
            submitBo.setOperateReason(importMap.get(detail.getSku().concat(detail.getPositionCode())).getTakeStockReason());
            submitBo.setClearStock(submitBo.getStock() == 0);
            stockPDATakeStockTaskService.submit(submitBo);
        });
    }

    private String validImportStockTakeOrder(List<StockTakeOrderImport> importList, Map<String, ProductSpecInfoEntity> skuMap) {
        List<StockTakeOrderImport> errorList = new ArrayList<>();
        List<String> errorMsgList;
        List<String> existImportInfoList = new ArrayList<>();
        for (StockTakeOrderImport row : importList) {
            errorMsgList = new ArrayList<>();
            if (!StringUtils.hasText(row.getSku())) {
                errorMsgList.add("规格编码不能为空");
            } else if (!skuMap.containsKey(row.getSku())) {
                errorMsgList.add("sku不存在");
            } else if (StringUtils.hasText(row.getPositionCode()) && existImportInfoList.contains(row.getSku().concat(row.getPositionCode()))) {
                errorMsgList.add("同一文件存在库位和商品规格信息均相同信息");
            } else if (StringUtils.hasText(row.getPositionCode())) {
                existImportInfoList.add(row.getSku().concat(row.getPositionCode()));
            }
            if (!StringUtils.hasText(row.getPositionCode())) {
                errorMsgList.add("库位编码不能为空");
            } else {
                BdPositionEntity positionEntity = positionService.getPositionByCode(row.getPositionCode());
                if (Objects.isNull(positionEntity)) {
                    errorMsgList.add("库位不存在");
                } else if (!StockTakeOrderConstant.STOCK_TAKE_ORDER_POSITION_TYPE.contains(positionEntity.getPositionType())) {
                    errorMsgList.add("盘点仅支持储存库位、零拣库位、退货库位、活动库位、OEM库位、越库库位、店铺库位");
                } else if (Objects.nonNull(internalBoxService.findByInternalBoxCode(row.getPositionCode()))) {
                    errorMsgList.add("内部箱暂不支持盘点");
                } else if (BdPositionTypeEnum.CROSS_POSITION.name().equals(positionEntity.getPositionType())
                        && (SpaceAreaMapConstant.WmsArea.OEM_AREA.equals(positionEntity.getAreaName())
                        || SpaceAreaMapConstant.WmsArea.ACTIVITY_AREA.equals(positionEntity.getAreaName())))
                    errorMsgList.add("活动仓和OEM仓的库位请到旧系统盘点");
            }
            if (Objects.isNull(row.getStockQty())) {
                errorMsgList.add("库存不能为空");
            }
            if (!StringUtils.hasText(row.getTakeStockReason())) {
                errorMsgList.add("盘点原因不能为空");
            }
            if (CollUtil.isNotEmpty(errorMsgList)) {
                row.setErrorMsg(StringUtils.join(errorMsgList, ','));
                errorList.add(row);
            }
        }
        String dataJsonStr = null;
        if (CollUtil.isNotEmpty(errorList)) {
            dataJsonStr = JsonMapper.toJson(errorList);
        }
        return dataJsonStr;
    }
}
