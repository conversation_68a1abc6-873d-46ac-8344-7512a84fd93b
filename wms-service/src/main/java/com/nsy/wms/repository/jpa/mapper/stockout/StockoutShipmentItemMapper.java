package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockout.DeclareItemInfo;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareKaiQiExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareLinChuangExportItem;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemCustoms;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemDeclareInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemInvoicePrice;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemSku;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemSearchByStockoutOrderRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.response.stockout.StockoutOrderBoxPositionResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutShipmentDeclareDownloadItemBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutShipmentItemBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutShipmentSpuQtyBo;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface StockoutShipmentItemMapper extends BaseMapper<StockoutShipmentItemEntity> {
    List<String> searchOrderNo(@Param("shipmentBoxCode") String shipmentBoxCode);

    String getShowOrder(@Param("shipmentId") Integer shipmentId);

    IPage<StockoutShipmentItemSku> searchShipmentItemSku(IPage page, @Param("shipmentBoxCode") String shipmentBoxCode);

    List<StockoutShipmentItemSku> searchShipmentItemDetail(@Param("stockoutOrderNo") String stockoutOrderNo, @Param("shipmentBoxCode") String shipmentBoxCode);

    List<String> searchShipmentBoxCodeList(@Param("stockoutOrderNo") String stockoutOrderNo, @Param("orderNoList") List<String> orderNoList);

    List<String> searchShipmentBoxCodeListByOrderNo(@Param("orderNoList") List<String> orderNoList);

    List<String> searchShipmentBoxCodeListByStockoutOrderNo(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList);

    List<Integer> searchShipmentIdCodeListByOrderNo(@Param("orderNoList") List<String> orderNoList);

    List<Integer> searchShipmentIdByOrderNoAndShipmentId(@Param("orderNoList") List<String> orderNoList, @Param("shipmentIdList") List<Integer> shipmentIdList);

    IPage<StockoutShipmentItemDeclareInfo> searchDeclareInfoListXZ(Page<StockoutShipmentItemEntity> page, @Param("orderNoList") List<String> orderNoList);

    IPage<StockoutShipmentItemDeclareInfo> searchDeclareInfoListJRD(Page<StockoutShipmentItemEntity> page, @Param("shipmentIds") List<Integer> shipmentIds);

    IPage<StockoutShipmentItemDeclareInfo> searchDeclareInfoListYFH(Page<StockoutShipmentItemEntity> page, @Param("orderNoList") List<String> orderNoList);

    IPage<StockoutShipmentItemDeclareInfo> searchDeclareInfoListXZUPS(Page<StockoutShipmentItemEntity> page, @Param("orderNoList") List<String> orderNoList);

    IPage<StockoutShipmentItemInvoicePrice> searchInvoicePriceListXZ(Page<StockoutShipmentItemEntity> page, @Param("orderNoList") List<String> orderNoList);

    List<StockoutShipmentSearchResult> getDeductionStockList(@Param("shipmentIdList") List<Integer> shipmentIdList);

    List<StockoutShipmentItemCustoms> searchFbaItem(@Param("shipmentIdList") List<Integer> shipmentIdList);

    /**
     * 根据出库单号查找装箱清单明细
     *
     * @param request
     * @return
     */
    IPage<StockoutShipmentItemDetail> searchListByStockoutOrder(IPage page, @Param("request") StockoutShipmentItemSearchByStockoutOrderRequest request);

    Integer validSameBoxIndex(@Param("stockoutOrderNo") List<String> stockoutOrderNo, @Param("shipmentId") Integer shipmentId, @Param("boxIndex") Integer boxIndex);

    StockoutShipmentEntity findTopByStockoutOrderNo(@Param("stockoutOrderNo") String stockoutOrderNo);

    IPage<StockoutShipmentItemDeclareInfo> searchDeclareInfoListYH(Page<StockoutShipmentItemEntity> page, @Param("shipmentIdList") List<Integer> shipmentIdList);

    /**
     * 获取该订单装箱清单已发货数量
     *
     * @param stockoutOrderNo
     * @return
     */
    int getShippedCountByStockoutOrderNo(String stockoutOrderNo);

    /**
     * 装箱清单根据区域分组
     *
     * @param shipmentBoxCode
     * @return
     */
    List<String> groupByWithSpace(String shipmentBoxCode);

    /**
     * 获取装箱清单得erpspaceId
     *
     * @param shipmentBoxCode
     * @return
     */
    List<Integer> getShipmentErpSpace(String shipmentBoxCode);

    /**
     * 查找指定区域的装箱清单信息
     *
     * @param shipmentId
     * @param erpSpaceId
     * @return
     */
    List<StockoutShipmentItemEntity> findByShipmentIdAndErpSpaceId(Integer shipmentId, Integer erpSpaceId);

    Date findDeliveryDateByOrderNo(@Param("orderNo") String orderNo);

    List<String> getLogisticsNoByOrderNos(@Param("orderNos") List<String> orderNo);

    /**
     * 查询对应装箱清单的拣货模式
     *
     * @param shipmentId
     * @return
     */
    List<String> getPickTypeByShipmentId(@Param("shipmentId") Integer shipmentId);

    /**
     * 根据shipmentId获取sku
     *
     * @param shipmentId
     * @return
     */
    List<String> getSkuByShipmentId(Integer shipmentId);


    List<DeclareItemInfo> getDeclareInfoOfJiuFang(@Param("shipmentIdList") List<Integer> shipmentIdList);

    List<ShipmentDeclareKaiQiExportItem> getDeclareInfoOfKaiQi(@Param("shipmentIdList") List<Integer> shipmentIdList);

    List<ShipmentDeclareLinChuangExportItem> getDeclareInfoOfLinChuang(@Param("shipmentIdList") List<Integer> shipmentIdList);

    String findBoxCodeByStockoutOrderNoAndStatus(String stockoutOrderNo, String status);

    /**
     * 通过shipmentId去找spu对应的qty
     *
     * @param shipmentId
     * @return
     */
    List<StockoutShipmentSpuQtyBo> sumSpuQtyByShipmentId(Integer shipmentId);

    List<StockoutShipmentSpuQtyBo> sumSpuQtyByShipmentIdList(List<Integer> shipmentIdList);

    List<String> getStocktoutOrderByShipment(@Param("shipmentBoxCodeList") List<String> shipmentBoxCodeList);

    List<StockoutShipmentDeclareDownloadItemBo> getShipmentDeclareDownloadItemBoList(@Param("shipmentIdList") List<Integer> shipmentIdList);

    List<StockoutShipmentDeclareDownloadItemBo> getDepponShipmentDeclareDownloadItemBoList(@Param("shipmentIdList") List<Integer> shipmentIdList);

    Integer getShipmentCountByStockoutOrderNo(@Param("stockoutOrderNo") String stockoutOrderNo);

    List<StockoutShipmentItemBo> listByWorkspaceAndShipmentId(List<String> workspaceList, Integer shipmentId);

    StockoutOrderBoxPositionResponse getOrderInfoByOrderNo(String orderNo);

    List<Integer> queryItemPutShipmentId(@Param("query") StockoutShipmentSearchRequest request);
}
