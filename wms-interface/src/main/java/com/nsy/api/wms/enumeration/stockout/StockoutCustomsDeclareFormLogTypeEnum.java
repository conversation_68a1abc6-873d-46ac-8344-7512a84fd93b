package com.nsy.api.wms.enumeration.stockout;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;

public enum StockoutCustomsDeclareFormLogTypeEnum {

    FETCH_FORM_DATA("拉取关单数据"),
    CONFIRM_ITEM("审核进项明细"),
    INCREASE_ITEM("新增进项明细"),
    UPDATE_ITEM("更新进项明细"),
    UPDATE_INFO("更新出口明细"),
    MATCH_SUPPLIER("匹配供应商"),
    DONE("已完成"),
    SUPPLIER_SIGN("供方已签署"),
    COMPANY_SIGN("需方已签署"),
    GENERATE_CONTRACT("合同生成"),
    REJECT_FORM("关单驳回"),
    AUDIT_CONTRACT_NO_PASS("合同审核不通过"),
    SUPPLIER_DONE("工厂已反馈"),
    REMOVE_ITEM("删除进项明细"),
    CONTRACT_CANCEL("合同取消"),
    AUDIT("关单审核");

    String name;

    StockoutCustomsDeclareFormLogTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getByName(String logType) {
        if (Strings.isBlank(logType)) return null;
        StockoutCustomsDeclareFormLogTypeEnum stockoutCustomsDeclareFormLogTypeEnum = Arrays.stream(values()).filter(instance -> logType.equals(instance.name())).findFirst()
                .orElse(null);
        if (stockoutCustomsDeclareFormLogTypeEnum != null) {
            return stockoutCustomsDeclareFormLogTypeEnum.getName();
        } else {
            return "";
        }
    }
}
