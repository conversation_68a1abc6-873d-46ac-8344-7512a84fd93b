package com.nsy.wms.controller.bd;

import com.nsy.api.wms.request.bd.BdAqlRuleListRequest;
import com.nsy.api.wms.request.bd.BdAqlRuleRequest;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdAqlRuleListResponse;
import com.nsy.wms.business.service.bd.BdAqlRuleService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * AQL规则控制器
 */
@Api(tags = "AQL规则管理")
@RestController
@RequestMapping("/bd/aql-rule")
public class BdAqlRuleController extends BaseController {

    @Autowired
    private BdAqlRuleService bdAqlRuleService;

    @ApiOperation("分页查询AQL规则列表")
    @PostMapping("/page-list")
    public PageResponse<BdAqlRuleListResponse> pageList(@RequestBody @Valid BdAqlRuleListRequest request) {
        return bdAqlRuleService.pageList(request);
    }

    @ApiOperation("新增AQL规则")
    @PostMapping("/add")
    public void add(@RequestBody @Valid BdAqlRuleRequest request) {
        bdAqlRuleService.add(request);
    }

    @ApiOperation("编辑AQL规则")
    @PostMapping("/edit")
    public void edit(@RequestBody @Valid BdAqlRuleRequest request) {
        bdAqlRuleService.edit(request);
    }

    @ApiOperation("删除AQL规则")
    @PostMapping("/batch/delete")
    public void delete(@RequestBody IdListRequest request) {
        bdAqlRuleService.delete(request.getIdList());
    }
} 