package com.nsy.api.wms.enumeration.bd;

import java.util.Arrays;

public enum BdPositionTypeEnum {
    CROSS_POSITION("越库库位"),
    STOCK_POSITION("储存库位"),
    SPARE_POSITION("零拣库位"),
    EXCEPTION_POSITION("异常库位"),
    RETURN_POSITION("退货库位"),
    SHIPPING_POSITION("发货库位"),
    RETURN_DEMAGE_POSITION("退货次品库位"),
    RETURN_EXCEPTION_POSITION("退货异常库位"),
    ACTIVITY_POSITION("活动库位"),
    OEM_POSITION("OEM库位"),
    PROCESS_SHIPPING_POSITION("加工发货库位"),
    PROCESS_DEMAGE_POSITION("加工次品库位"),
    STORE_POSITION("店铺库位");

    BdPositionTypeEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

    public static String resolve(String positionType) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(positionType))
                .findAny()
                .map(BdPositionTypeEnum::getName)
                .orElse("");
    }

}
