package com.nsy.wms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * AQL规则实体类
 */
@Entity
@Table(name = "bd_aql_rule")
@TableName("bd_aql_rule")
public class BdAqlRuleEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer aqlRuleId;

    private String location;

    /**
     * 区间从
     */
    private Integer rangeFrom;

    /**
     * 区间到
     */
    private Integer rangeTo;

    /**
     * 最少可接受严重件数
     */
    private Integer minAcceptQty;

    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 描述
     */
    private String description;

    private Boolean isDeleted;

    public Integer getAqlRuleId() {
        return aqlRuleId;
    }

    public void setAqlRuleId(Integer aqlRuleId) {
        this.aqlRuleId = aqlRuleId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getRangeFrom() {
        return rangeFrom;
    }

    public void setRangeFrom(Integer rangeFrom) {
        this.rangeFrom = rangeFrom;
    }

    public Integer getRangeTo() {
        return rangeTo;
    }

    public void setRangeTo(Integer rangeTo) {
        this.rangeTo = rangeTo;
    }

    public Integer getMinAcceptQty() {
        return minAcceptQty;
    }

    public void setMinAcceptQty(Integer minAcceptQty) {
        this.minAcceptQty = minAcceptQty;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }
} 