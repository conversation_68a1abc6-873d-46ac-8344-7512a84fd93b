package com.nsy.wms.business.service.stockin;

import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockin.ReceiveSkuView;
import com.nsy.api.wms.response.stockin.AddReceiveResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.response.SchedulingOrderPlanBomDetailResponse;
import com.nsy.wms.business.manage.scm.response.StronglyRecommendStyleLabelsDto;
import com.nsy.wms.business.service.bd.BdTagService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinOrderTaskBuildService {

    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockinOrderTaskPDAService stockinOrderTaskPDAService;
    @Autowired
    StockinDeliveryPdaService stockinDeliveryPdaService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    private BdTagService bdTagService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;

    @Transactional
    public void buildOrderItem(List<StockinOrderTaskItemEntity> taskItemEntityList, List<StockinOrderItemEntity> orderItemEntityList, StockinOrderEntity orderEntity) {
        for (StockinOrderTaskItemEntity taskItemEntity : taskItemEntityList) {
            Integer taskItemId = taskItemEntity.getTaskItemId();
            if (orderItemEntityList.stream().anyMatch(item -> item.getTaskItemId().equals(taskItemId)))
                continue;
            stockinOrderItemService.createItem(null, taskItemEntity, orderEntity);
        }
    }

    public void buildAddReceiveResponse(AddReceiveResponse addReceiveResponse, List<StockinOrderTaskItemEntity> itemEntityList) {
        List<StronglyRecommendStyleLabelsDto> labelsDtoList = scmApiService.getAllStronglyRecommendStyleLabelsDtoList();
        List<String> stronglyRecommendList = labelsDtoList.stream().filter(detail -> StringUtils.hasText(detail.getLabel())).map(StronglyRecommendStyleLabelsDto::getLabel).distinct().collect(Collectors.toList());
        List<ProductSpecInfoEntity> productInfoEntityList = productSpecInfoService.getListBySkuList(itemEntityList.stream().map(StockinOrderTaskItemEntity::getSku).collect(Collectors.toList()));
        Map<String, ProductSpecInfoEntity> specMap = productInfoEntityList.stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));
        List<ReceiveSkuView> collect = itemEntityList.stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku)).values().stream().map(value -> {
            ReceiveSkuView receiveSkuView = new ReceiveSkuView();
            int waitReceiveQty = value.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum();
            int receiveQty = value.stream().mapToInt(StockinOrderTaskItemEntity::getStockinQty).sum();
            BeanUtilsEx.copyProperties(value.get(0), receiveSkuView);
            receiveSkuView.setReceiveQty(receiveQty);
            receiveSkuView.setDeliveryQty(waitReceiveQty);
            receiveSkuView.setLabelAttributeNames(stockinOrderTaskPDAService.getLabelAttributeNames(value));
            receiveSkuView.setStatus(stockinDeliveryPdaService.getReceiveStatus(waitReceiveQty, receiveQty));
            receiveSkuView.setIsLeadGeneration(value.stream().anyMatch(temp -> StrUtil.nullToDefault(temp.getLabelAttributeNames(), "").contains("引流款")));
            // 检查是否配置了先进先出
            String sku = value.get(0).getSku();
            receiveSkuView.setIsFifo(bdTagService.checkStockFifo(sku));

            stronglyRecommendList.forEach(label -> {
                if (value.stream().anyMatch(item -> item.getLabelAttributeNames().contains(label)))
                    receiveSkuView.setStronglyRecommendLabel(label);
            });
            ProductSpecInfoEntity specInfoEntity = specMap.get(receiveSkuView.getSku());
            if (Objects.isNull(specInfoEntity)) {
                return receiveSkuView;
            }
            SchedulingOrderPlanBomDetailResponse bomDetail = scmApiService.getSchedulingOrderPlanBomDetail(specInfoEntity.getProductId(), specInfoEntity.getSkc());
            if (Objects.isNull(bomDetail) || CollectionUtils.isEmpty(bomDetail.getProductSkcBomMaterialOfferingDtoList())) {
                return receiveSkuView;
            }
            if (bomDetail.getProductSkcBomMaterialOfferingDtoList().stream().filter(detail -> !CollectionUtils.isEmpty(detail.getRiskDescList()) && detail.getRiskDescList().stream().anyMatch(risk -> risk.contains("二创"))).findAny().isPresent()) {
                receiveSkuView.setSecondCreationLabel("二创");
            }
            return receiveSkuView;
        }).collect(Collectors.toList());
        List<ReceiveSkuView> collect1 = collect.stream().sorted(Comparator.comparing(ReceiveSkuView::getStatus)).collect(Collectors.toList());
        addReceiveResponse.setReceiveSkuViewList(collect1);
        addReceiveResponse.setQty(itemEntityList.stream().mapToInt(StockinOrderTaskItemEntity::getExpectedQty).sum());
        addReceiveResponse.setBoxQty((int) itemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskId).distinct().count());
    }
}
