package com.nsy.wms.business.service.stockout;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockout.FileInfo;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormItemResult;
import com.nsy.api.wms.enumeration.stockout.ExportDrawbackInformationTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormItemUpdateRequest;
import com.nsy.wms.business.domain.bo.esign.ESignGetSignedFilesResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.esign.ESignService;
import com.nsy.wms.business.service.system.AliyunOssService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutExportDrawbackInformationEntity;
import com.nsy.wms.repository.entity.stockout.SupplierTaxInvoiceInfoFeedbackRequest;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareFormItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关单明细(StockoutCustomsDeclareForm)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
@Service
public class StockoutCustomsDeclareFormItemService extends ServiceImpl<StockoutCustomsDeclareFormItemMapper, StockoutCustomsDeclareFormItemEntity> {

    @Autowired
    StockoutCustomsDeclareFormService stockoutCustomsDeclareFormService;
    @Autowired
    StockoutCustomsDeclareFormLogService logService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutExportDrawbackInformationService drawbackInformationService;
    @Autowired
    StockoutCustomsDeclareDocumentItemService declareDocumentItemService;
    @Autowired
    private StockoutCustomsDeclareDocumentService stockoutCustomsDeclareDocumentService;
    @Autowired
    private StockoutCustomsDeclareContractService declareContractService;
    @Autowired
    AliyunOssService aliyunOssService;
    @Autowired
    ESignService eSignService;
    @Resource
    StockoutCustomsDeclareDocumentFileService declareDocumentFileService;
    @Autowired
    private ScmApiService scmApiService;

    /**
     * 查找未删除 非手动拆分
     *
     * @param declareFormId
     * @return
     */
    public List<StockoutCustomsDeclareFormItemEntity> itemListNoManual(Integer declareFormId) {
        LambdaQueryWrapper<StockoutCustomsDeclareFormItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, declareFormId);
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.FALSE);
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getIsManual, Boolean.FALSE);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 查找未删除 手动拆分
     *
     * @param declareFormId
     * @return
     */
    public List<StockoutCustomsDeclareFormItemEntity> itemListManual(Integer declareFormId) {
        LambdaQueryWrapper<StockoutCustomsDeclareFormItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, declareFormId);
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.FALSE);
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getIsManual, Boolean.TRUE);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 查找未删除
     *
     * @param declareFormId
     * @return
     */
    public List<StockoutCustomsDeclareFormItemEntity> itemList(Integer declareFormId) {
        LambdaQueryWrapper<StockoutCustomsDeclareFormItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, declareFormId);
        wrapper.eq(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.FALSE);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 查找未删除
     *
     * @param declareFormId
     * @return
     */
    public List<StockoutCustomsDeclareFormItemResult> itemResultList(Integer declareFormId) {
        List<StockoutCustomsDeclareFormItemEntity> stockoutCustomsDeclareFormItemEntities = itemList(declareFormId);
        if (CollectionUtils.isEmpty(stockoutCustomsDeclareFormItemEntities)) {
            return new ArrayList<>();
        }
        return stockoutCustomsDeclareFormItemEntities.stream().map(itemEntity -> {
            StockoutCustomsDeclareFormItemResult result = new StockoutCustomsDeclareFormItemResult();
            BeanUtilsEx.copyProperties(itemEntity, result);
            result.setInputPrice(itemEntity.getInputPrice().setScale(2, RoundingMode.HALF_UP));
            result.setTaxPrice(itemEntity.getTaxPrice().setScale(2, RoundingMode.HALF_UP));
            result.setTaxInclusiveUnitPrice(itemEntity.getTaxInclusiveUnitPrice().setScale(0, RoundingMode.HALF_UP));
            return result;
        }).collect(Collectors.toList());
    }

    /**
     * 新增 -- 限制【待处理】；计算金额
     * 更新 -- 明细是否手动拆分
     * * * 是 限制【待处理】状态；计算金额
     * * * 否 限制已审核关单；只更新进项发票代码 三个字段
     *
     * @param request
     */
    @Transactional
    public void updateItemList(StockoutCustomsDeclareFormItemUpdateRequest request) {

        request.getUpdateList().stream()
                .sorted(Comparator.comparing(StockoutCustomsDeclareFormItemResult::getDeleted).reversed())
                .forEach(item -> {
                    if (!Objects.isNull(item.getDeleted()) && item.getDeleted()) {
                        delete(item);
                    } else {
                        if (item.getInputQty() <= 0) {
                            throw new BusinessServiceException("进项数量必须大于0");
                        }

                        //id为空表示新增
                        if (Objects.isNull(item.getDeclareFormItemId())) {
                            save(item);
                        } else { //id为空表示更新
                            update(item);
                        }
                    }
                });

        //计算并且更新关单金额
        sumAndUpdateFormPrice(request.getUpdateList().stream().map(StockoutCustomsDeclareFormItemResult::getDeclareFormId).collect(Collectors.toList()));
//        //检查是否所有手动拆满
//        request.getUpdateList().stream().map(StockoutCustomsDeclareFormItemResult::getDeclareFormId)
//                .distinct()
//                .forEach(this::checkManualAll);
    }

    /**
     * 计算并且更新关单金额
     *
     * @param formIdList
     */
    public void sumAndUpdateFormPrice(List<Integer> formIdList) {
        formIdList.forEach(formId -> {
            List<StockoutCustomsDeclareFormItemEntity> itemList = this.itemList(formId);
            if (CollectionUtil.isEmpty(itemList))
                return;
            StockoutCustomsDeclareFormEntity form = stockoutCustomsDeclareFormService.findById(formId);
            BigDecimal taxInclusiveUnitPrice = itemList.get(0).getTaxInclusiveUnitPrice();
            BigDecimal taxInclusivePrice = itemList.stream().map(StockoutCustomsDeclareFormItemEntity::getTaxInclusivePrice).reduce(BigDecimal.ZERO, BigDecimal::add);

            String content = String.format("修改进项明细，计算含税单价【%s】->【%s】，含税金额【%s】->【%s】",
                    form.getTaxInclusiveUnitPrice(), taxInclusiveUnitPrice, form.getTaxInclusivePrice(), taxInclusivePrice);
            logService.addLog(formId, StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, content);

            form.setTaxInclusiveUnitPrice(taxInclusiveUnitPrice);
            form.setTaxInclusivePrice(taxInclusivePrice);
            stockoutCustomsDeclareFormService.updateById(form);
        });
    }

    /**
     * 人工拆分进项明细
     * 限制【待处理】；计算金额
     *
     * @param itemResult
     */
    private void save(StockoutCustomsDeclareFormItemResult itemResult) {
        StockoutCustomsDeclareFormEntity declareForm = stockoutCustomsDeclareFormService.getById(itemResult.getDeclareFormId());
        if (Objects.isNull(declareForm)) {
            throw new BusinessServiceException(String.format("找不到关单【%s】", itemResult.getDeclareFormId()));
        }
        if (!StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(declareForm.getStatus())) {
            throw new BusinessServiceException(String.format("该关单非【待处理】，无法拆分【%s】", declareForm.getDeclareDocumentNo()));
        }
        StockoutCustomsDeclareFormItemEntity itemEntity = new StockoutCustomsDeclareFormItemEntity();
        BeanUtilsEx.copyProperties(itemResult, itemEntity);
        itemEntity.setDeclareFormId(declareForm.getDeclareFormId());
        itemEntity.setgNo(declareForm.getgNo());
        itemEntity.setInputQty(itemResult.getInputQty());
        itemEntity.setInputPrice(StockoutCustomsDeclareFormService.calInputPrice(itemResult.getInputQty(), declareForm.getTaxInclusiveUnitPrice()));
        itemEntity.setTaxInclusivePrice(StockoutCustomsDeclareFormService.calTaxInclusivePrice(itemResult.getInputQty(), declareForm.getTaxInclusiveUnitPrice()));
        itemEntity.setTaxInclusiveUnitPrice(declareForm.getTaxInclusiveUnitPrice());
        itemEntity.setTaxPrice(itemEntity.getTaxInclusivePrice().subtract(itemEntity.getInputPrice()));
        itemEntity.setIsManual(Boolean.TRUE);
        itemEntity.setIsDeleted(Boolean.FALSE);
        this.save(itemEntity);
        //更新手动拆分的进项数量
        updateManualInputQty(declareForm.getDeclareFormId());
        logService.addLog(declareForm.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.INCREASE_ITEM, String.format("人工拆分进项明细, 进项数量 %s", itemResult.getInputQty()));
    }

    /**
     * 更新进项明细
     * 更新 -- 明细是否手动拆分
     * * * 是 限制【待处理】状态；是否删除
     * * * * * 是，减去手动拆分数量
     * * * * * 对比，计算金额
     * * * 否 限制已审核关单；只更新进项发票代码 三个字段
     *
     * @param itemResult
     */
    private void update(StockoutCustomsDeclareFormItemResult itemResult) {
        StockoutCustomsDeclareFormEntity declareForm = stockoutCustomsDeclareFormService.getById(itemResult.getDeclareFormId());
        if (Objects.isNull(declareForm))
            throw new BusinessServiceException(String.format("找不到关单【%s】", itemResult.getDeclareFormId()));
        StockoutCustomsDeclareFormItemEntity oldItemEntity = this.getById(itemResult.getDeclareFormItemId());
        if (Objects.isNull(oldItemEntity))
            throw new BusinessServiceException(String.format("找不到进项明细【%s】", itemResult.getDeclareFormItemId()));
        Date oldInvoiceDate = oldItemEntity.getInvoiceDate();
        validUpdateParam(itemResult, oldInvoiceDate);
        //非手动冲库存也不是非待处理 只更新进项发票代码 三个字段
        if (!StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name().equals(declareForm.getSystemMark())
                && !StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(declareForm.getStatus())) {
            updateLimit(itemResult, declareForm);
        } else {
            updateAll(itemResult, declareForm);
        }

        if (ObjectUtil.isNotNull(itemResult.getInvoiceDate())
                && !StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name().equals(declareForm.getSystemMark())) {
            if (ObjectUtil.isNotNull(oldInvoiceDate) && DateUtil.isSameDay(itemResult.getInvoiceDate(), oldInvoiceDate))
                return;

            //1. 新增 2. 修改不同日 则同步scm
            SupplierTaxInvoiceInfoFeedbackRequest invoiceInfoFeedbackRequest = new SupplierTaxInvoiceInfoFeedbackRequest();
            invoiceInfoFeedbackRequest.setFormId(oldItemEntity.getDeclareFormId());
            invoiceInfoFeedbackRequest.setTaxItemId(oldItemEntity.getTaxItemId());
            invoiceInfoFeedbackRequest.setInvoiceDate(itemResult.getInvoiceDate());
            invoiceInfoFeedbackRequest.setDeclareDocumentNo(declareForm.getDeclareDocumentNo());
            invoiceInfoFeedbackRequest.setProtocolNo(declareForm.getProtocolNo());
            invoiceInfoFeedbackRequest.setgNo(declareForm.getgNo());
            scmApiService.invoiceInfoFeedback(invoiceInfoFeedbackRequest);
        }
    }

    private void validUpdateParam(StockoutCustomsDeclareFormItemResult itemResult, Date oldInvoiceDate) {
        if (ObjectUtil.isNotNull(oldInvoiceDate)
                && ObjectUtil.isNull(itemResult.getInvoiceDate())) {
            throw new BusinessServiceException("已设置开票时间不能清空");
        }

        if (ObjectUtil.isNotNull(itemResult.getInvoiceDate())
                && ObjectUtil.isNotNull(oldInvoiceDate)
                && !DateUtil.isSameMonth(itemResult.getInvoiceDate(), oldInvoiceDate)) {
            throw new BusinessServiceException("开票时间只能修改相同月份");
        }
    }

    private void updateLimit(StockoutCustomsDeclareFormItemResult itemResult, StockoutCustomsDeclareFormEntity declareForm) {
        StockoutCustomsDeclareFormItemEntity itemEntity = this.getById(itemResult.getDeclareFormItemId());
        StringBuilder content = new StringBuilder("更新发票数据：");
        if (StrUtil.isNotEmpty(itemResult.getInputInvoiceCode()) && !itemResult.getInputInvoiceCode().equals(itemEntity.getInputInvoiceCode())) {
            content.append(String.format(" 进项发票号码【%s】->【%s】", itemEntity.getInputInvoiceCode(), itemResult.getInputInvoiceCode()));
            itemEntity.setInputInvoiceCode(itemResult.getInputInvoiceCode());
        }
        if (StrUtil.isNotEmpty(itemResult.getInputInvoiceNo()) && !itemResult.getInputInvoiceNo().equals(itemEntity.getInputInvoiceNo())) {
            content.append(String.format(" 进项发票代码【%s】->【%s】", itemEntity.getInputInvoiceNo(), itemResult.getInputInvoiceNo()));
            itemEntity.setInputInvoiceNo(itemResult.getInputInvoiceNo());
        }
        if (!Objects.isNull(itemResult.getInvoiceDate()) && !itemResult.getInvoiceDate().equals(itemEntity.getInvoiceDate())) {
            content.append(String.format(" 开票时间【%s】->【%s】", itemEntity.getInvoiceDate(), itemResult.getInvoiceDate()));
            itemEntity.setInvoiceDate(itemResult.getInvoiceDate());
        }
        itemEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(itemEntity);
        logService.addLog(declareForm.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, content.toString());
    }

    private void updateAll(StockoutCustomsDeclareFormItemResult itemResult, StockoutCustomsDeclareFormEntity declareForm) {
        StockoutCustomsDeclareFormItemEntity itemEntity = this.getById(itemResult.getDeclareFormItemId());
        StringBuilder content = new StringBuilder("更新进项明细：");
        if (!Objects.isNull(itemResult.getInputQty()) && !itemResult.getInputQty().equals(itemEntity.getInputQty())) {
            content.append(String.format(" 进项数量【%s】->【%s】", itemEntity.getInputQty(), itemResult.getInputQty()));
            itemEntity.setInputQty(itemResult.getInputQty());
        }
        if (!Objects.isNull(itemResult.getInputPrice()) && itemResult.getInputPrice().compareTo(itemEntity.getInputPrice()) != 0) {
            content.append(String.format(" 不含税金额【%s】->【%s】", itemEntity.getInputPrice(), itemResult.getInputPrice()));
            itemEntity.setInputPrice(itemResult.getInputPrice());
        }
        if (!Objects.isNull(itemResult.getTaxInclusiveUnitPrice()) && itemResult.getTaxInclusiveUnitPrice().compareTo(itemEntity.getTaxInclusiveUnitPrice()) != 0) {
            content.append(String.format(" 含税单价【%s】->【%s】", itemEntity.getTaxInclusiveUnitPrice(), itemResult.getTaxInclusiveUnitPrice()));
            itemEntity.setTaxInclusiveUnitPrice(itemResult.getTaxInclusiveUnitPrice());
        }
        if (!Objects.isNull(itemResult.getTaxInclusivePrice()) && itemResult.getTaxInclusivePrice().compareTo(itemEntity.getTaxInclusivePrice()) != 0) {
            content.append(String.format(" 含税金额【%s】->【%s】", itemEntity.getTaxInclusivePrice(), itemResult.getTaxInclusivePrice()));
            itemEntity.setTaxInclusivePrice(itemResult.getTaxInclusivePrice());
        }
        if (!Objects.isNull(itemResult.getTaxPrice()) && itemResult.getTaxPrice().compareTo(itemEntity.getTaxPrice()) != 0) {
            content.append(String.format(" 税额【%s】->【%s】", itemEntity.getTaxPrice(), itemResult.getTaxPrice()));
            itemEntity.setTaxPrice(itemResult.getTaxPrice());
        }
        if (StrUtil.isNotEmpty(itemResult.getInputInvoiceCode()) && !itemResult.getInputInvoiceCode().equals(itemEntity.getInputInvoiceCode())) {
            content.append(String.format(" 进项发票号码【%s】->【%s】", itemEntity.getInputInvoiceCode(), itemResult.getInputInvoiceCode()));
            itemEntity.setInputInvoiceCode(itemResult.getInputInvoiceCode());
        }
        if (StrUtil.isNotEmpty(itemResult.getInputInvoiceNo()) && !itemResult.getInputInvoiceNo().equals(itemEntity.getInputInvoiceNo())) {
            content.append(String.format(" 进项发票代码【%s】->【%s】", itemEntity.getInputInvoiceNo(), itemResult.getInputInvoiceNo()));
            itemEntity.setInputInvoiceNo(itemResult.getInputInvoiceNo());
        }
        if (!Objects.isNull(itemResult.getInvoiceDate()) && !itemResult.getInvoiceDate().equals(itemEntity.getInvoiceDate())) {
            content.append(String.format(" 开票时间【%s】->【%s】", itemEntity.getInvoiceDate(), itemResult.getInvoiceDate()));
            itemEntity.setInvoiceDate(itemResult.getInvoiceDate());
        }
        if (StrUtil.isNotEmpty(itemResult.getDeclareContractNo()) && !itemResult.getDeclareContractNo().equals(itemEntity.getDeclareContractNo())) {
            content.append(String.format(" 采购合同号【%s】->【%s】", itemEntity.getDeclareContractNo(), itemResult.getDeclareContractNo()));
            itemEntity.setDeclareContractNo(itemResult.getDeclareContractNo());
        }
        itemEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(itemEntity);
        logService.addLog(declareForm.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, content.toString());
    }

    private void delete(StockoutCustomsDeclareFormItemResult itemResult) {
        StockoutCustomsDeclareFormEntity declareForm = stockoutCustomsDeclareFormService.getById(itemResult.getDeclareFormId());
        if (Objects.isNull(declareForm))
            throw new BusinessServiceException(String.format("找不到关单【%s】", itemResult.getDeclareFormId()));
        StockoutCustomsDeclareFormItemEntity itemEntity = this.getById(itemResult.getDeclareFormItemId());
        if (Objects.isNull(itemEntity))
            throw new BusinessServiceException(String.format("找不到进项明细【%s】", itemResult.getDeclareFormItemId()));
        itemEntity.setIsDeleted(Boolean.TRUE);
        itemEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(itemEntity);
        logService.addLog(declareForm.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.REMOVE_ITEM,
                String.format("删除进项明细, 进项数量 %s, 含税单价 %s", itemEntity.getInputQty(), itemEntity.getTaxInclusiveUnitPrice()));
    }

//    /**
//     * 更新手动拆分进项明细
//     * 限制【待处理】状态；是否删除
//     * * * * * * 是，减去手动拆分数量
//     * * * * * * 对比，计算金额
//     *
//     * @param itemResult
//     * @param itemEntity
//     * @param declareForm
//     */
//    private void updateManual(StockoutCustomsDeclareFormItemResult itemResult, StockoutCustomsDeclareFormItemEntity itemEntity, StockoutCustomsDeclareFormEntity declareForm) {
//        //限制【待处理】状态
//        if (!StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(declareForm.getStatus()))
//            throw new BusinessServiceException("该关单非【待处理】无法操作手动拆分的进项明细");
//        //是否删除 是，减去手动拆分数量
//        if (!itemEntity.getIsDeleted() && itemResult.getDeleted()) {
//            itemEntity.setIsDeleted(Boolean.TRUE);
//            itemEntity.setUpdateBy(loginInfoService.getName());
//            this.updateById(itemEntity);
//            //更新手动拆分的进项数量
//            updateManualInputQty(declareForm.getDeclareFormId());
//            logService.addLog(declareForm.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, String.format("删除进项明细【%s】", itemEntity.getDeclareFormItemId()));
//        } else if (!itemResult.getDeleted()) {
//            itemEntity.setInputQty(itemResult.getInputQty());
//            itemEntity.setInputPrice(StockoutCustomsDeclareFormService.calInputPrice(itemResult.getInputQty(), declareForm.getTaxInclusiveUnitPrice()));
//            itemEntity.setTaxInclusivePrice(StockoutCustomsDeclareFormService.calTaxInclusivePrice(itemResult.getInputQty(), declareForm.getTaxInclusiveUnitPrice()));
//            itemEntity.setTaxPrice(StockoutCustomsDeclareFormService.calTaxPrice(itemEntity.getInputPrice()));
//            itemEntity.setDeclareContractNo(itemResult.getDeclareContractNo());
//            itemEntity.setInputInvoiceCode(itemResult.getInputInvoiceCode());
//            itemEntity.setInputInvoiceNo(itemResult.getInputInvoiceNo());
//            itemEntity.setInvoiceDate(itemResult.getInvoiceDate());
//            itemEntity.setUpdateBy(loginInfoService.getName());
//            this.updateById(itemEntity);
//            //更新手动拆分的进项数量
//            updateManualInputQty(declareForm.getDeclareFormId());
//            logService.addLog(declareForm.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, String.format("更新进项明细【%s】, 进项数量 %s", itemEntity.getDeclareFormItemId(), itemResult.getInputQty()));
//        }
//    }

//    /**
//     * 检查是否所有手动拆满
//     *
//     * @param formId
//     */
//    private void checkManualAll(Integer formId) {
//        StockoutCustomsDeclareFormEntity form = stockoutCustomsDeclareFormService.findById(formId);
//        if (!StockoutCustomsDeclareFormStatusEnum.COMPLETE.name().equals(form.getStatus())
//                && form.getTotalManualInputQty() >= form.getgQty()) {
//            form.setStatus(StockoutCustomsDeclareFormStatusEnum.COMPLETE.name());
//            form.setSystemMark(StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name());
//            form.setUpdateBy(loginInfoService.getName());
//            stockoutCustomsDeclareFormService.updateById(form);
//            logService.addLog(formId, StockoutCustomsDeclareFormLogTypeEnum.DONE, "手动拆满，状态变更【已完成】");
//        }
//    }

    /**
     * 更新手动拆分的进项数量
     *
     * @param formId
     * @return
     */
    private void updateManualInputQty(Integer formId) {
        List<StockoutCustomsDeclareFormItemEntity> itemList = itemListManual(formId);
        int totalInputQty = itemList.stream().mapToInt(StockoutCustomsDeclareFormItemEntity::getInputQty).sum();
        StockoutCustomsDeclareFormEntity form = stockoutCustomsDeclareFormService.findById(formId);
        if (totalInputQty > form.getgQty()) {
            throw new BusinessServiceException("进项数量之和 大于 出库数量");
        }

        form.setTotalManualInputQty(totalInputQty);
        form.setUpdateBy(loginInfoService.getName());
        stockoutCustomsDeclareFormService.updateById(form);
    }

    /**
     * 删掉自动拆分的明细
     *
     * @param formId
     */
    public void deleteAutoSplitItem(Integer formId) {
        this.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormItemEntity>()
                .eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, formId)
                .eq(StockoutCustomsDeclareFormItemEntity::getIsManual, Boolean.FALSE)
                .eq(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.FALSE)
                .set(StockoutCustomsDeclareFormItemEntity::getUpdateBy, loginInfoService.getName())
                .set(StockoutCustomsDeclareFormItemEntity::getIsDeleted, Boolean.TRUE));
    }

    /**pocker
     * 删除手动冲库存明细
     * 用于冲库存合同删除时物理删除手动导入的明细数据
     *
     * @param formId 关单ID
     */
    public void deleteManualItems(Integer formId) {
        // 物理删除所有手动明细记录（不管isDeleted状态）
        this.remove(new LambdaQueryWrapper<StockoutCustomsDeclareFormItemEntity>()
                .eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, formId)
                .eq(StockoutCustomsDeclareFormItemEntity::getIsManual, Boolean.TRUE));
    }

    @Transactional
    public void updateDocumentData(StockoutCustomsDeclareFormEntity formEntity) {
        List<StockoutCustomsDeclareFormItemEntity> itemList = itemList(formEntity.getDeclareFormId());
        if (CollectionUtils.isEmpty(itemList))
            return;
        StockoutCustomsDeclareDocumentEntity documentEntity = stockoutCustomsDeclareDocumentService.findByDeclareDocumentNo(formEntity.getProtocolNo());
        if (documentEntity == null)
            throw new BusinessServiceException("找不到对应的报关单据：" + formEntity.getProtocolNo());
        itemList.forEach(item -> {
            //回填进项发票
            if (StringUtils.hasText(item.getInputInvoiceNo()) && StringUtils.hasText(item.getInputInvoiceCode())) {
                fillInputInvoice(item, documentEntity);
            }
            // 回填 合同 及其 文件
            if (StringUtils.hasText(formEntity.getDeclareContractNo())) {
                fillContract(documentEntity, formEntity);
            }
        });
    }

    /**
     * 回填进项发票
     *
     * @param item
     * @param documentEntity
     */
    private void fillInputInvoice(StockoutCustomsDeclareFormItemEntity item, StockoutCustomsDeclareDocumentEntity documentEntity) {
        String informationKey = item.getInputInvoiceCode() + item.getInputInvoiceNo();
        List<StockoutExportDrawbackInformationEntity> list = drawbackInformationService.listByInformationKey(informationKey);
        if (!CollectionUtils.isEmpty(list))
            return;
        StockoutCustomsDeclareDocumentFileService.RelateBo relateBo = new StockoutCustomsDeclareDocumentFileService.RelateBo(documentEntity, ExportDrawbackInformationTypeEnum.INPUT_INVOICE, informationKey, item.getInvoiceUrl(), item.getInvoiceFilename(), Boolean.FALSE);
        declareDocumentFileService.relate(relateBo);
    }

    /**
     * 回填 合同 及其 文件
     *
     * @param documentEntity
     * @param formEntity
     */
    private void fillContract(StockoutCustomsDeclareDocumentEntity documentEntity, StockoutCustomsDeclareFormEntity formEntity) {
        List<StockoutCustomsDeclareContractEntity> list = declareContractService.list(new LambdaQueryWrapper<StockoutCustomsDeclareContractEntity>()
                .eq(StockoutCustomsDeclareContractEntity::getDeclareContractNo, formEntity.getDeclareContractNo()));
        if (CollectionUtils.isEmpty(list))
            return;
        FileInfo declareContractOssFile = getDeclareContractOssFile(list.get(0));
        StockoutCustomsDeclareDocumentFileService.RelateBo relateBo = new StockoutCustomsDeclareDocumentFileService.RelateBo(documentEntity, ExportDrawbackInformationTypeEnum.PURCHASE_CONTRACT, formEntity.getDeclareContractNo(), declareContractOssFile.getFileUrl(), declareContractOssFile.getFileName(), Boolean.FALSE);
        declareDocumentFileService.relate(relateBo);
    }

    public FileInfo getDeclareContractOssFile(StockoutCustomsDeclareContractEntity declareContract) {
        ESignGetSignedFilesResponse.File signedFile = declareContractService.getSignedFileUrl(declareContract);
        byte[] bytes = HttpUtil.downloadBytes(signedFile.getDownloadUrl());
        String fileUrl = aliyunOssService.putObject(IoUtil.toStream(bytes), "declareContract", declareContract + signedFile.getFileName());
        FileInfo file = new FileInfo();
        file.setFileName(declareContract.getDeclareContractNo() + signedFile.getFileName());
        file.setFileUrl(fileUrl);
        return file;
    }

    private StockoutCustomsDeclareFormItemEntity getByDeclareFormItemId(Integer declareFormItemId) {
        StockoutCustomsDeclareFormItemEntity declareFormItem = getById(declareFormItemId);
        if (Objects.isNull(declareFormItem))
            throw new BusinessServiceException(String.format("关单明细 %s 不存在", declareFormItemId));
        return declareFormItem;
    }

    public void updateTax(StockoutCustomsDeclareFormItemUpdateRequest request) {
        request.getUpdateList().forEach(itemResult -> {
            StockoutCustomsDeclareFormEntity formEntity = stockoutCustomsDeclareFormService.findById(itemResult.getDeclareFormId());
            if (!StockoutCustomsDeclareFormStatusEnum.COMPLETE.name().equals(formEntity.getStatus()))
                throw new BusinessServiceException(String.format("关单 %s 非已完成，不能修改税额", itemResult.getDeclareFormId()));

            StockoutCustomsDeclareFormItemEntity declareFormItem = getByDeclareFormItemId(itemResult.getDeclareFormItemId());

            if (Objects.isNull(itemResult.getTaxInclusivePrice()))
                throw new BusinessServiceException(String.format("明细 %s 含税金额不能为空", declareFormItem.getDeclareFormItemId()));

            if (Objects.isNull(itemResult.getTaxPrice()))
                throw new BusinessServiceException(String.format("明细 %s 税额不能为空", declareFormItem.getDeclareFormItemId()));

            declareFormItem.setTaxInclusivePrice(itemResult.getTaxInclusivePrice());
            declareFormItem.setTaxPrice(itemResult.getTaxPrice());
            declareFormItem.setUpdateBy(loginInfoService.getName());
            declareFormItem.setUpdateDate(new Date());
            this.updateById(declareFormItem);

            logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, String.format("更新税额, 含税金额 %s, 税额 %s", itemResult.getTaxInclusivePrice(), itemResult.getTaxPrice()));
        });
    }

    /**
     * 统计进项金额
     *
     * @param formId
     * @return
     */
    public BigDecimal totalInputPrice(Integer formId) {
        return list(new LambdaQueryWrapper<StockoutCustomsDeclareFormItemEntity>()
                .eq(StockoutCustomsDeclareFormItemEntity::getDeclareFormId, formId)
                .eq(StockoutCustomsDeclareFormItemEntity::getIsDeleted, 0))
                .stream().map(StockoutCustomsDeclareFormItemEntity::getInputPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }
}

