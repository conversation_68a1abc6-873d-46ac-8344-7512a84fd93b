package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-03-27 09:50:22
 */
@ApiModel(value = "StockoutLogisticsBatchSumResponse", description = "统计对象")
public class StockoutLogisticsBatchSumResponse {

    @ApiModelProperty("仓库称重总重量")
    private BigDecimal predictWeight;

    @ApiModelProperty("预估总价")
    private BigDecimal predictTotalPrice;


    /**
     * 实际重量
     */
    @ApiModelProperty("实际重量")
    private BigDecimal realWeight;

    /**
     * 实际总价
     */
    @ApiModelProperty("实际总价")
    private BigDecimal realPrice;

    /**
     * 税费
     */
    @ApiModelProperty("税费")
    private BigDecimal realTax;

    /**
     * 报关费用
     */
    @ApiModelProperty("报关费用")
    private BigDecimal realDeclarePrice;

    @ApiModelProperty("店铺实际费用")
    private BigDecimal storeRealPrice;

    public BigDecimal getPredictWeight() {
        return predictWeight;
    }

    public void setPredictWeight(BigDecimal predictWeight) {
        this.predictWeight = predictWeight;
    }

    public BigDecimal getPredictTotalPrice() {
        return predictTotalPrice;
    }

    public void setPredictTotalPrice(BigDecimal predictTotalPrice) {
        this.predictTotalPrice = predictTotalPrice;
    }

    public BigDecimal getRealWeight() {
        return realWeight;
    }

    public void setRealWeight(BigDecimal realWeight) {
        this.realWeight = realWeight;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public BigDecimal getRealTax() {
        return realTax;
    }

    public void setRealTax(BigDecimal realTax) {
        this.realTax = realTax;
    }

    public BigDecimal getRealDeclarePrice() {
        return realDeclarePrice;
    }

    public void setRealDeclarePrice(BigDecimal realDeclarePrice) {
        this.realDeclarePrice = realDeclarePrice;
    }

    public BigDecimal getStoreRealPrice() {
        return storeRealPrice;
    }

    public void setStoreRealPrice(BigDecimal storeRealPrice) {
        this.storeRealPrice = storeRealPrice;
    }
}

